<!-- v1 -->
<%- include('header', {}); %>
<%- include('menu', { currentTab: 'Home' }); %>
<div class="container">
    <br>
    <div class="jumbotron">
        <h2 class="display-4">Add/Edit Command</h2>
        <br>
        <div class="row">
            <div class="col">
                <input id="commandName" type="text" class="form-control" placeholder="command Name">
                <input type="hidden" id="commandOID" name="OID" value="" />
            </div>
            <div class="col">
                <button id="loadCommand" type="button" class="btn btn-primary">Load</button>
                <button id="saveCommand" type="button" class="btn btn-success">Save</button>
                <button id="saveCommand" type="button" class="btn btn-secondary" onclick="clearForm()">New</button>
            </div>
        </div>
        <br>
        <fieldset class="form-group">
            <div class="row">
              <legend class="col-form-label col-sm-2 pt-0">Command Type</legend>
              <div class="col-sm-10">
                <div class="form-check-inline">
                  <input class="form-check-input" type="radio" name="commandType" id="commandTypeRestAPI" value="RestAPI" checked>
                  <label class="form-check-label" for="gridRadios1">Rest API</label>
                </div>
                <div class="form-check-inline disabled">
                  <input class="form-check-input" type="radio" name="commandType" id="commandTypeDBSelect" value="DBSelect" disabled>
                  <label class="form-check-label" for="gridRadios3">DB Select</label>
                </div>
                <div class="form-check-inline disabled">
                    <input class="form-check-input" type="radio" name="commandType" id="commandTypePHPScript" value="PHPScript" disabled>
                    <label class="form-check-label" for="gridRadios3">PHP Script</label>
                </div>
                <div class="form-check-inline disabled">
                    <input class="form-check-input" type="radio" name="commandType" id="commandTypeLinuxExe" value="LinuxExe" disabled>
                    <label class="form-check-label" for="gridRadios3">Linux Exe</label>
                </div>
              </div>
            </div>
          </fieldset>
        <input id="commandPermission" type="text" class="form-control" placeholder="AGS Group Can Run (Empty means everyone)">
        <textarea id="commandHelp" rows="2" class="form-control" placeholder="help"></textarea>
        <textarea id="commandConfig" rows="8" class="form-control" placeholder="Command Config"></textarea>
        <textarea id="commandInput" rows="6" class="form-control" placeholder="Command Input Parameters JSON"></textarea>
        <br>
        <br>
        <input id="testCommand" type="text" class="form-control" placeholder="command input paramters">
        <button id="testRun" type="button" class="btn btn-success">Test Run</button>
        <button id="clear" type="button" class="btn btn-secondary" onclick="clearResultInPage()">Clear</button>


    </div>
    <div id="QCResults"></div>
</div>
<script>
    var socket = io();
    var currentCommandOID;

    $(document).ready(function () {

        //Triger when Save button clicked
        $("#saveCommand").click(function () {
            saveCommand();
        });
        //Triger when Load button clicked
        $("#loadCommand").click(function () {
            loadCommand();
        });
    });


    function saveCommand(QCCommand) {
        var commandID = idGen('MQC');
        var QCCommand = {
            id        : commandID,
            name: $("#commandName").val(),
            type: $("#commandType").val(),
            permission: $("#commandPermission").val(),
            help: $("#commandHelp").val(),
            config: $("#commandConfig").val(),
            input: $("#commandInput").val()
        };

        $.post('/commandEdit/save', QCCommand);

    }

    //Set value of Command fields
    function setCommand(command) {
        console.log('#> Set command value to form  for command : ' , command.name);
        $("#commandName").val(command.name);
        $("#commandOID").val(command._id);
        $("#commandType").val(command.type);
        $("#commandPermission").val(command.permission);
        $("#commandHelp").val(command.help);
        $("#commandConfig").val(command.config);
        $("#commandInput").val(command.input);
    }

    //Get a command by its name
    function loadCommand() {
        $.get('/commandEdit/load/' + $("#commandName").val(), (command) => {
            //var command = data.shift();
            if (command != null) {
                currentCommandOID = command._id;
                setCommand(command);
                console.log('#> loaded command is : ' , command);

            } else {
                console.log('#> No command exist with this name!');

            }
        })
    }


    function clearForm() {
        $("#commandName").val('');
        $("#commandOID").val('');
        $("#commandHelp").val('');
        $("#commandPermission").val('');
        $("#commandBody").val('');
        console.log('Cleared Form');
    }


</script>
<%- include('footer', {}); %>
