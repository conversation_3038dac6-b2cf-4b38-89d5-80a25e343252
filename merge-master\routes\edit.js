'use strict';

import express from 'express';
import fs from 'fs';
import _ from 'lodash';

import logger from '../modules/logger.js';
import Api from '../db/model/api.js';
import ApiMetadata from '../db/model/apiMetadata.js';
import MessageBucket from '../db/model/messageBucket.js';
import MessageBucketMetadata from '../db/model/messageBucketMetadata.js';
import OutcomeMetadata from '../db/model/outcomeMetadata.js';
import RuleMetadata from '../db/model/ruleMetadata.js';
import TextTemplateMetadata from '../db/model/textTemplateMetadata.js';
import regex from '../modules/helpers/regex.js';
import auth from '../modules/auth.js';
import { AuthorizationRoles } from '../modules/enumerations.js';
import apiFunctions from '../modules/apiFunctions.js';


const apisSchema = JSON.parse(await fs.promises.readFile('./config/editorAPISchema.json'));
const rulesSchema = JSON.parse(await fs.promises.readFile('./config/editorRuleSchema.json'));
const messageBucketsSchema = JSON.parse(await fs.promises.readFile('./config/editorMessageBucketSchema.json'));
const outcomesSchema = JSON.parse(await fs.promises.readFile('./config/editorOutcomeSchema.json'));
const sourcesSchema = JSON.parse(await fs.promises.readFile('./config/editorSourceSchema.json'));
const textTemplatesSchema = JSON.parse(await fs.promises.readFile('./config/editorTextTemplateSchema.json'));

const router = express.Router();


// Loads URLs for API into the api schema from config
let apiBaseUrlConfig = _.get(global, ['gConfig', 'APIUriConfig'], {});
let baseUrls = {};
extractUrlKeys(baseUrls, apiBaseUrlConfig, 'config');
for (let key of Object.keys(baseUrls).sort()) {
    apisSchema.properties.baseUrl.anyOf[0].enum.push(key);
    apisSchema.properties.baseUrl.anyOf[0].options.enum_titles.push(`${baseUrls[key]} (${key})`);
}


// Note: API function names are currently stored in the "baseUrl" field of an API object
for (let key of Object.keys(apiFunctions).sort()) {
    apisSchema.properties.baseUrl.anyOf[1].enum.push(key);
    apisSchema.properties.baseUrl.anyOf[1].options.enum_titles.push(key);
}
Object.freeze(apisSchema);


router.get('/apis', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], true), async function (req, res) {
    try {
        let apiMetadata = await ApiMetadata.findOne({}).select(['-__v', '-_id']);
        res.render('listApis', { user: req.user, metadata: apiMetadata });
    } catch(error) {
        logger.error(`Error obtaining metadata for rules page, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.get('/rules', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), async function(req, res) {
    try {
        let ruleSourceMetadata = await RuleMetadata.findOne({}).select(['-__v', '-_id']);
        res.render('listRules', { user: req.user, metadata: ruleSourceMetadata });
    } catch(error) {
        logger.error(`Error obtaining metadata for rules page, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.get('/messageBuckets', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), async function(req, res) {
    try {
        let messageBucketMetadata = await MessageBucketMetadata.findOne({}).select(['-__v', '-_id']);
        res.render('listMessageBuckets', { user: req.user, metadata: messageBucketMetadata });
    } catch(error) {
        logger.error(`Error obtaining metadata for outcomes page, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.get('/outcomes', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), async function(req, res) {
    try {
        let outcomeMetadata = await OutcomeMetadata.findOne({}).select(['-__v', '-_id']);
        res.render('listOutcomes', { user: req.user, metadata: outcomeMetadata });
    } catch(error) {
        logger.error(`Error obtaining metadata for outcomes page, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.get('/sources', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), async function(req, res) {
    try {
        let ruleSourceMetadata = await RuleMetadata.findOne({}).select(['-__v', '-_id']);
        res.render('listSources', { user: req.user, metadata: ruleSourceMetadata });
    } catch(error) {
        logger.error(`Error obtaining metadata for sources page, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.get('/templates', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), async function(req, res) {
    try {
        let textTemplateMetadata = await TextTemplateMetadata.findOne({}).select(['-__v', '-_id']);
        res.render('listTemplates', { user: req.user, metadata: textTemplateMetadata });
    } catch(error) {
        logger.error(`Error obtaining metadata for templates page, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.get('/create/api', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], true), function (req, res) {
    try {
        // Read the JSON schema for editing rules from file, set properties if required and convert to base 64
        let disableRuleSourceEditing = req.user && req.user.isAdmin ? false : global.gConfig.disableRuleSourceEditing;
        let apisSchemaBase64 = Buffer.from(JSON.stringify(setAPIsSchemaReadOnly(apisSchema, true, disableRuleSourceEditing))).toString('base64');

        res.render('editApis', { user: req.user, apiName: null, createApi: true, schema: apisSchemaBase64, title: "Create API" });
    } catch (error) {
        logger.error(`Error in reading APIs schema file, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.get('/create/messageBucket', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), function(req, res) {
    try {
        // Read the JSON schema for editing message buckets from file, set properties if required and convert to base 64
        let disableRuleSourceEditing = req.user && req.user.isAdmin ? false : global.gConfig.disableRuleSourceEditing;
        let messageBucketsSchemaBase64 = Buffer.from(JSON.stringify(setMessageBucketsSchemaReadOnly(messageBucketsSchema, true, disableRuleSourceEditing))).toString('base64');
        res.render('editMessageBuckets', { user: req.user, messageBucketName: null, createMessageBucket: true, schema: messageBucketsSchemaBase64, title: "Create Message Bucket" });
    } catch (error) {
        logger.error(`Error in reading templates schema file, ${error.toString()}`);
        res.sendStatus(500);
    }
});



router.get('/create/outcome', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), async function(req, res) {
    try {
        // Read the JSON schema for editing outcomes from file, set properties if required and convert to base 64
        let disableRuleSourceEditing = req.user && req.user.isAdmin ? false : global.gConfig.disableRuleSourceEditing;
        let outcomesSchemaBase64 = Buffer.from(JSON.stringify(await setOutcomesSchemaReadOnly(outcomesSchema, true, disableRuleSourceEditing))).toString('base64');
        res.render('editOutcomes', { user: req.user, outcomeName: null, createOutcome: true, schema: outcomesSchemaBase64, title: "Create Outcome" });
    } catch (error) {
        logger.error(`Error in reading templates schema file, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.get('/create/rule', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), async function(req, res) {
    try {
        // Read the JSON schema for editing rules from file, set properties if required and convert to base 64
        let disableRuleSourceEditing = req.user && req.user.isAdmin ? false : global.gConfig.disableRuleSourceEditing;
        let rulesSchemaBase64 = Buffer.from(JSON.stringify(await setRulesSchemaReadOnly(rulesSchema, true, disableRuleSourceEditing))).toString('base64');

        res.render('editRules', { user: req.user, ruleName: null, createRule: true, schema: rulesSchemaBase64, title: "Create Rule" });
    } catch (error) {
        logger.error(`Error in reading rules schema file, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.get('/create/source', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), async function(req, res) {
    try {
        // Read the JSON schema for editing sources from file, set properties if required and convert to base 64
        let disableRuleSourceEditing = req.user && req.user.isAdmin ? false : global.gConfig.disableRuleSourceEditing;
        let sourcesSchemaBase64 = Buffer.from(JSON.stringify(await setSourcesSchemaReadOnly(sourcesSchema, true, disableRuleSourceEditing))).toString('base64');

        res.render('editSources', { user: req.user, sourceName: null, createSource: true, schema: sourcesSchemaBase64, title: "Create Source" });
    } catch (error) {
        logger.error(`Error in reading sources schema file, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.get('/create/template', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), function(req, res) {
    try {
        // Read the JSON schema for editing sources from file, set properties if required and convert to base 64
        let disableRuleSourceEditing = req.user && req.user.isAdmin ? false : global.gConfig.disableRuleSourceEditing;
        let textTemplatesSchemaBase64 = Buffer.from(JSON.stringify(setTemplatesSchemaReadOnly(textTemplatesSchema, true, disableRuleSourceEditing))).toString('base64');

        res.render('editTemplates', { user: req.user, templateName: null, createTemplate: true, schema: textTemplatesSchemaBase64, title: "Create Template" });
    } catch (error) {
        logger.error(`Error in reading templates schema file, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.get('/apis/:name', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], true), async function (req, res) {
    try {
        let apiNameBase64 = Buffer.from(req.params.name).toString('base64');

        // Read the JSON schema for editing rules from file, set properties if required and convert to base 64
        let disableRuleSourceEditing = req.user && req.user.isAdmin ? false : global.gConfig.disableRuleSourceEditing;
        let apisSchemaBase64 = Buffer.from(JSON.stringify(setAPIsSchemaReadOnly(apisSchema, false, disableRuleSourceEditing))).toString('base64');

        res.render('editApis', { user: req.user, apiName: apiNameBase64, createApi: false, schema: apisSchemaBase64, title: req.params.name });
    } catch (error) {
        logger.error(`Error in reading API schema file, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.get('/rules/:name', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), async function(req, res) {
    try {
        let ruleNameBase64 = Buffer.from(req.params.name).toString('base64');

        // Read the JSON schema for editing rules from file, set properties if required and convert to base 64
        let disableRuleSourceEditing = req.user && req.user.isAdmin ? false : global.gConfig.disableRuleSourceEditing;
        let rulesSchemaBase64 = Buffer.from(JSON.stringify(await setRulesSchemaReadOnly(rulesSchema, false, disableRuleSourceEditing))).toString('base64');

        res.render('editRules', { user: req.user, ruleName: ruleNameBase64, createRule: false, schema: rulesSchemaBase64, title: req.params.name });
    } catch (error) {
        logger.error(`Error in reading rules schema file, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.get('/messageBuckets/:name', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), async function(req, res) {
    try {
        let messageBucketNameBase64 = Buffer.from(req.params.name).toString('base64');

        // Read the JSON schema for editing outcomes from file, set properties if required and convert to base 64
        let disableRuleSourceEditing = req.user && req.user.isAdmin ? false : global.gConfig.disableRuleSourceEditing;
        let messageBucketsSchemaBase64 = Buffer.from(JSON.stringify(setMessageBucketsSchemaReadOnly(messageBucketsSchema, false, disableRuleSourceEditing))).toString('base64');

        res.render('editMessageBuckets', { user: req.user, messageBucketName: messageBucketNameBase64, createMessageBucket: false, schema: messageBucketsSchemaBase64, title: req.params.name });
    } catch (error) {
        logger.error(`Error in reading message buckets schema file, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.get('/outcomes/:name', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), async function(req, res) {
    try {
        let outcomeNameBase64 = Buffer.from(req.params.name).toString('base64');

        // Read the JSON schema for editing outcomes from file, set properties if required and convert to base 64
        let disableRuleSourceEditing = req.user && req.user.isAdmin ? false : global.gConfig.disableRuleSourceEditing;
        let outcomesSchemaBase64 = Buffer.from(JSON.stringify(await setOutcomesSchemaReadOnly(outcomesSchema, false, disableRuleSourceEditing))).toString('base64');

        res.render('editOutcomes', { user: req.user, outcomeName: outcomeNameBase64, createOutcome: false, schema: outcomesSchemaBase64, title: req.params.name });
    } catch (error) {
        logger.error(`Error in reading outcomes schema file, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.get('/sources/:name', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), async function(req, res) {
    try {
        let sourceNameBase64 = Buffer.from(req.params.name).toString('base64');

        // Read the JSON schema for editing sources from file, set properties if required and convert to base 64
        let disableRuleSourceEditing = req.user && req.user.isAdmin ? false : global.gConfig.disableRuleSourceEditing;
        let sourcesSchemaBase64 = Buffer.from(JSON.stringify(await setSourcesSchemaReadOnly(sourcesSchema, false, disableRuleSourceEditing))).toString('base64');

        res.render('editSources', { user: req.user, sourceName: sourceNameBase64, createSource: false, schema: sourcesSchemaBase64, title: req.params.name });
    } catch (error) {
        logger.error(`Error in reading sources schema file, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.get('/templates/:name', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), async function(req, res) {
    try {
        let templateNameBase64 = Buffer.from(req.params.name).toString('base64');

        // Read the JSON schema for editing sources from file, set properties if required and convert to base 64
        let disableRuleSourceEditing = req.user && req.user.isAdmin ? false : global.gConfig.disableRuleSourceEditing;
        let textTemplatesSchemaBase64 = Buffer.from(JSON.stringify(setTemplatesSchemaReadOnly(textTemplatesSchema, false, disableRuleSourceEditing))).toString('base64');

        res.render('editTemplates', { user: req.user, templateName: templateNameBase64, createTemplate: false, schema: textTemplatesSchemaBase64, title: req.params.name });
    } catch (error) {
        logger.error(`Error in reading templates schema file, ${error.toString()}`);
        res.sendStatus(500);
    }
});


// Sets properties in rules schema to readonly if config editing is disabled
async function setRulesSchemaReadOnly(originalSchema, isCreate, readOnly) {
    let schema = _.cloneDeep(originalSchema);

    // Sets the regex patterns for rule name, depended on sources / rules
    let ruleNamePattern = regex.ruleName.toString().replace(/^\/|\/$/g, '');
    let sourceNamePattern = regex.sourceName.toString().replace(/^\/|\/$/g, '');
    let apis = await Api.find({}).select(['name']).sort({ name: 1 }).collation({ locale: 'en' });
    let apiNames = apis.map(api => { return api.name; });

    schema.properties.name.pattern = ruleNamePattern;
    schema.properties.preRules.items.pattern = ruleNamePattern;
    schema.properties.preSources.items.pattern = sourceNamePattern;
    schema.properties.action.properties.api.enum = apiNames;

    schema.properties.name.readonly = !isCreate;
    schema.properties.action.properties.parameterCode.readonly = readOnly;
    schema.properties.action.properties.codeCondition.readonly = readOnly;
    schema.properties.preSources.items.readonly = readOnly;
    schema.properties.preSources.options.disable_array_delete = readOnly;
    schema.properties.preSources.options.disable_array_reorder = readOnly;
    schema.properties.preRules.items.readonly = readOnly;
    schema.properties.preRules.options.disable_array_delete = readOnly;
    schema.properties.preRules.options.disable_array_reorder = readOnly;
    schema.properties.preCondition.readonly = readOnly;
    schema.properties.ruleCode.readonly = readOnly;
    schema.properties.valueMsgStm.readonly = readOnly;
    schema.properties.extraInfo.readonly = readOnly;
    schema.properties.overrideCompare.readonly = readOnly;
    schema.properties.unitTests.items.readonly = readOnly;
    schema.properties.unitTests.items.properties.name.readonly = readOnly;
    schema.properties.unitTests.items.properties.inputRecord.readonly = readOnly;
    schema.properties.unitTests.items.properties.outputRecord.readonly = readOnly;
    schema.properties.unitTests.options.disable_array_delete = readOnly;
    schema.properties.unitTests.options.disable_array_reorder = readOnly;

    return schema;
}


// Sets properties in sources schema to readonly if config editing is disabled
async function setSourcesSchemaReadOnly(originalSchema, isCreate, readOnly) {
    let schema = _.cloneDeep(originalSchema);

    // Sets the regex patterns for source name, depended on sources / rules
    let ruleNamePattern = regex.ruleName.toString().replace(/^\/|\/$/g, '');
    let sourceNamePattern = regex.sourceName.toString().replace(/^\/|\/$/g, '');
    let sourceTitlePattern = regex.sourceTitle.toString().replace(/^\/|\/$/g, '');
    let apis = await Api.find({}).select(['name']).sort({ name: 1 }).collation({ locale: 'en' });
    let apiNames = apis.map(api => { return api.name; });

    schema.properties.name.pattern = sourceNamePattern;
    schema.properties.title.pattern = sourceTitlePattern;
    schema.properties.preRules.items.pattern = ruleNamePattern;
    schema.properties.preSources.items.pattern = sourceNamePattern;
    schema.properties.api.enum = apiNames;

    schema.properties.name.readonly = !isCreate;
    schema.properties.param.readonly = readOnly;
    schema.properties.suite.items.readonly = readOnly;
    schema.properties.suite.options.disable_array_delete = readOnly;
    schema.properties.suite.options.disable_array_reorder = readOnly;
    schema.properties.preSources.items.readonly = readOnly;
    schema.properties.preSources.options.disable_array_delete = readOnly;
    schema.properties.preSources.options.disable_array_reorder = readOnly;
    schema.properties.preRules.items.readonly = readOnly;
    schema.properties.preRules.options.disable_array_delete = readOnly;
    schema.properties.preRules.options.disable_array_reorder = readOnly;
    schema.properties.preCondition.readonly = readOnly;

    return schema;
}


// Sets properties in apis schema to readonly if rules / sources editing is disabled
function setAPIsSchemaReadOnly(originalSchema, isCreate, readOnly) {
    let schema = _.cloneDeep(originalSchema);

    // Sets the regex patterns for rule name, depended on sources / rules
    let apiNamePattern = regex.apiName.toString().replace(/^\/|\/$/g, '');
    schema.properties.name.pattern = apiNamePattern;

    schema.properties.name.readonly = !isCreate;
    schema.properties.parameters.items.readonly = readOnly;
    schema.properties.parameters.options.disable_array_delete = readOnly;
    schema.properties.parameters.options.disable_array_reorder = readOnly;
    schema.properties.baseUrl.readonly = readOnly;
    schema.properties.uri.readonly = readOnly;
    schema.properties.queryParams.readonly = readOnly;
    schema.properties.body.readonly = readOnly;
    schema.properties.header.readonly = readOnly;
    schema.properties.errorCondition.readonly = readOnly;
    schema.properties.parseResponse.readonly = readOnly;
    schema.properties.pollCondition.readonly = readOnly;
    schema.properties.masslCertificateName.enum.push(...Object.keys(global.gConfig.APIMasslCertificates));

    schema.definitions.asyncPoll.properties.uri.readonly = readOnly;
    schema.definitions.asyncPoll.properties.header.readonly = readOnly;
    schema.definitions.asyncPoll.properties.parseResponse.readonly = readOnly;
    schema.definitions.asyncPoll.properties.errorCondition.readonly = readOnly;
    schema.definitions.asyncPoll.properties.doneCondition.readonly = readOnly;

    schema.definitions.resultAPI.properties.uri.readonly = readOnly;
    schema.definitions.resultAPI.properties.header.readonly = readOnly;
    schema.definitions.resultAPI.properties.parseResponse.readonly = readOnly;

    return schema;
}


function setMessageBucketsSchemaReadOnly(originalSchema, isCreate, readOnly) {
    let schema = _.cloneDeep(originalSchema);

    schema.properties.name.readonly = !isCreate;

    return schema;
}


async function setOutcomesSchemaReadOnly(originalSchema, isCreate, readOnly) {
    let schema = _.cloneDeep(originalSchema);

    let messageBuckets = await MessageBucket.find({}).select(['name']).sort({ name: 1 }).collation({ locale: 'en' });
    let messageBucketNames = messageBuckets.map(messageBucket => { return messageBucket.name; });

    schema.properties.name.readonly = !isCreate;
    schema.properties.messageBucketName.enum.push(...messageBucketNames);

    return schema;
}


function setTemplatesSchemaReadOnly(originalSchema, isCreate, readOnly) {
    let schema = _.cloneDeep(originalSchema);

    schema.properties.name.readonly = !isCreate;
    schema.properties.template.readonly = readOnly;

    return schema;
}


/**
 *
 * @param {Objects} urlKeys Object to store nested keys with string values
 * @param {Object} apiConfig Object from main config containing URLs to be used with APIs
 * @param {string} prefix prefix for current object
 */
function extractUrlKeys(urlKeys, apiConfig, prefix) {
    if (_.isPlainObject(apiConfig)) {
        for (let key in apiConfig) {
            if (_.isPlainObject(apiConfig[key])) {
                extractUrlKeys(urlKeys, apiConfig[key], `${prefix}.${key}`);
            } else {
                urlKeys[`${prefix}.${key}`] = apiConfig[key];
            }
        }
    }
}


export default router;
