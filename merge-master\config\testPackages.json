[{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON> and <PERSON><PERSON><PERSON>", "sections": [{"name": "Summary", "title": "<PERSON> and <PERSON><PERSON><PERSON>", "ruleName": "MTR087"}], "productTypes": ["ADSL", "BDSL", "INTERNATIONAL", "IPMAN", "MDN", "MOBILE", "NBN", "NBN EE", "VOIP"], "hasDateRangeFilter": false, "defaultStartOffsetHours": null}, {"name": "BSIP", "title": "BSIP Data", "sections": [{"name": "Summary", "title": "Summary", "ruleName": "MTR086"}], "productTypes": ["VOIP"], "hasDateRangeFilter": false, "defaultStartOffsetHours": null}, {"name": "CMICommands", "title": "CMI Commands", "sections": [{"name": "InterfaceSummary", "title": "Interface Summary", "ruleName": "MDR207"}], "productTypes": ["MDN"], "hasDateRangeFilter": false, "defaultStartOffsetHours": null}, {"name": "COMMPilot", "title": "COMMPilot", "sections": [{"name": "Summary", "title": "Summary", "ruleName": "MTR051"}], "productTypes": ["VOIP"], "hasDateRangeFilter": false, "defaultStartOffsetHours": null}, {"name": "COMMPilotPGN", "title": "COMMPilot Primary Group Number", "sections": [{"name": "Summary", "title": "Summary", "ruleName": "MTR066"}], "productTypes": ["VOIP"], "hasDateRangeFilter": false, "defaultStartOffsetHours": null}, {"name": "Fibre Adaptive Network Summary", "title": "Fibre Adaptive Network Information", "sections": [{"name": "NetworkSummary", "title": "Fibre Adaptive Network Summary", "ruleName": "MDR142"}], "productTypes": ["Telstra Fibre Adapt"], "hasDateRangeFilter": false, "defaultStartOffsetHours": null}, {"name": "GeospatialCoverageMap", "title": "Geospatial Coverage Map", "sections": [{"name": "MapLink", "title": "Map Link", "ruleName": "MDR199"}], "productTypes": ["ADSL", "BDSL", "INTERNATIONAL", "IPMAN", "MOBILE", "VOIP"], "hasDateRangeFilter": false, "defaultStartOffsetHours": null}, {"name": "GMACSTest", "title": "GMACS BDSL Test", "sections": [{"name": "Interface", "title": "Interface Up / Down", "ruleName": "MTR017"}, {"name": "CustomerInterfaceErrorIncreasing", "title": "Customer Interface Error Increasing", "ruleName": "MTR018"}, {"name": "CustomerInterfacePacketsIncreasing", "title": "Customer Interface Inbound / Outbound Packets Increasing", "ruleName": "MTR019"}, {"name": "SHDSLCheck", "title": "SHDSL Check", "ruleName": "MTR020"}, {"name": "EFMCustomerInterfaceSpeed", "title": "EFM Customer Interface Speed", "ruleName": "MTR021"}, {"name": "BearerAndEquipmentInfo", "title": "Bearer And Equipment Info", "ruleName": "MDR206"}], "productTypes": ["BDSL"], "hasDateRangeFilter": false, "defaultStartOffsetHours": null}, {"name": "MANAT", "title": "MANAT", "sections": [{"name": "MonitoringEnabled", "title": "MANAT Monitoring Enabled for CAN service", "ruleName": "MTR085"}], "productTypes": ["MDN"], "hasDateRangeFilter": false, "defaultStartOffsetHours": null}, {"name": "NBNBtdStatus", "title": "NBN BTD Status Diagnostic", "sections": [{"name": "Results", "title": "Results", "ruleName": "MDR149"}], "productTypes": ["NBN EE"], "hasDateRangeFilter": false, "defaultStartOffsetHours": null}, {"name": "NBNUniEStatus", "title": "NBN UNI-E Status Diagnostic", "sections": [{"name": "Results", "title": "Results", "ruleName": "MDR150"}], "productTypes": ["NBN EE"], "hasDateRangeFilter": false, "defaultStartOffsetHours": null}, {"name": "NBNServiceHealthFTTNFTTB", "title": "NBN Service Health Test", "sections": [{"name": "Dropouts", "title": "Dropouts", "ruleName": "MDR204"}, {"name": "SyncRate", "title": "Sync Rate", "ruleName": "MDR135"}, {"name": "OperationalStatus", "title": "Operational Status", "ruleName": "MDR205"}, {"name": "CPEDetails", "title": "CPE Details", "ruleName": "MDR208"}], "productTypes": ["NBN FTTN", "NBN FTTB"], "hasDateRangeFilter": false, "defaultStartOffsetHours": null}, {"name": "NBNServiceHealthFTTC", "title": "NBN Service Health Test", "sections": [{"name": "Dropouts", "title": "Dropouts", "ruleName": "MDR204"}, {"name": "SyncRate", "title": "Sync Rate", "ruleName": "MDR135"}, {"name": "OperationalStatus", "title": "Operational Status", "ruleName": "MDR205"}, {"name": "CPEDetails", "title": "CPE Details", "ruleName": "MDR208"}, {"name": "NCDDetails", "title": "NCD Details", "ruleName": "MDR211"}], "productTypes": ["NBN FTTC"], "hasDateRangeFilter": false, "defaultStartOffsetHours": null}, {"name": "NBNServiceHealthHFC", "title": "NBN Service Health Test", "sections": [{"name": "Dropouts", "title": "Dropouts", "ruleName": "MDR204"}, {"name": "OperationalStatus", "title": "Operational Status", "ruleName": "MDR205"}, {"name": "CPEDetails", "title": "CPE Details", "ruleName": "MDR208"}, {"name": "NTDDetails", "title": "NTD Details", "ruleName": "MDR209"}], "productTypes": ["NBN HFC"], "hasDateRangeFilter": false, "defaultStartOffsetHours": null}, {"name": "NBNServiceHealthFTTP", "title": "NBN Service Health Test", "sections": [{"name": "Dropouts", "title": "Dropouts", "ruleName": "MDR204"}, {"name": "OperationalStatus", "title": "Operational Status", "ruleName": "MDR205"}, {"name": "CPEDetails", "title": "CPE Details", "ruleName": "MDR208"}, {"name": "NTDDetails", "title": "NTD Details", "ruleName": "MDR209"}], "productTypes": ["NBN FTTP"], "hasDateRangeFilter": false, "defaultStartOffsetHours": null}, {"name": "NBNServiceHealthWireless", "title": "NBN Service Health Test", "sections": [{"name": "Network", "title": "Network", "ruleName": "MDR210"}, {"name": "OperationalStatus", "title": "Operational Status", "ruleName": "MDR205"}, {"name": "NTDDetails", "title": "NTD Details", "ruleName": "MDR209"}], "productTypes": ["NBN Wireless"], "hasDateRangeFilter": false, "defaultStartOffsetHours": null}, {"name": "<PERSON>", "title": "Ping Test", "sections": [{"name": "<PERSON>", "title": "<PERSON>", "ruleName": "MTR007"}], "productTypes": ["MDN"], "hasDateRangeFilter": false, "defaultStartOffsetHours": null}, {"name": "RASSServiceConfigInfo", "title": "RASS Service Config Info", "sections": [{"name": "PhoneNumberRanges", "title": "Phone Number Ranges", "ruleName": "MDR202"}, {"name": "PhoneNumberRanges", "title": "Phone Number Ranges", "ruleName": "MDR203"}], "productTypes": [], "hasDateRangeFilter": false, "defaultStartOffsetHours": null}, {"name": "SDWAN", "title": "SDWAN", "sections": [{"name": "BackupInterface", "title": "Backup Interface", "ruleName": "MDR131"}], "productTypes": ["MOBILE"], "hasDateRangeFilter": false, "defaultStartOffsetHours": null}, {"name": "SHAREDBOSS", "title": "SHAREDBOSS Commands", "sections": [{"name": "InterfaceStatistics", "title": "Interface Statistics", "ruleName": "MDR084"}, {"name": "PacketDropsAndErrors", "title": "Packet Drops And Errors", "ruleName": "MDR189"}], "productTypes": ["NTU Device"], "hasDateRangeFilter": false, "defaultStartOffsetHours": null}, {"name": "TRADBasementSwitchDevice", "title": "TRAD Commands (Basement Switch Device)", "sections": [{"name": "InterfaceStatistics", "title": "Interface Statistics", "ruleName": "MDR187"}, {"name": "PacketDropsAndErrors", "title": "Packet Drops And Errors", "ruleName": "MDR025"}, {"name": "ASICStats", "title": "ASIC Stats for Model 3400", "ruleName": "MDR192"}], "productTypes": ["Basement Switch Device"], "hasDateRangeFilter": false, "defaultStartOffsetHours": null}, {"name": "TRADNextUpstreamOrEdgeDevice", "title": "TRAD Commands (Next Upstream Device / Edge Device)", "sections": [{"name": "InterfaceStatistics", "title": "Interface Statistics", "ruleName": "MDR188"}], "productTypes": ["IPMAN"], "hasDateRangeFilter": false, "defaultStartOffsetHours": null}, {"name": "VPNPING", "title": "VPN PING", "sections": [{"name": "<PERSON>", "title": "<PERSON>", "ruleName": "MTR026"}], "productTypes": ["IPMAN", "NBN", "NBN EE", "Telstra Fibre Adapt"], "hasDateRangeFilter": false, "defaultStartOffsetHours": null}, {"name": "WASOrderDetails", "title": "WAS Order Details", "sections": [{"name": "OrderDetails", "title": "Order Details", "ruleName": "MDR186"}], "productTypes": ["IPMAN", "NBN", "NBN EE", "MDN", "MOBILE", "Telstra Fibre Adapt"], "hasDateRangeFilter": false, "defaultStartOffsetHours": null}, {"name": "WirelessExperienceChecker", "title": "Wireless Experience Checker", "sections": [{"name": "WirelessExperienceChecker", "title": "Wireless Experience Checker", "ruleName": "MDR194"}], "productTypes": ["ADSL", "IPMAN", "MDN", "MOBILE", "NBN", "NBN EE", "VOIP"], "hasDateRangeFilter": false, "defaultStartOffsetHours": null}]