.overviewTab {
    display: grid;
    grid-template-columns: repeat(6, 1fr); 
    gap: 1rem; 
    height: 100%; 

    .networkDiagramsPanel {
        grid-column: span 6; 
        grid-row: 1;
    }

    .productDetailsPanel {
        grid-column: span 3; 
        grid-row: 2;
    }

    .deviceDetailsPanel {
        grid-column: span 3; 
        grid-row: 2; 
    }

    .accountStatusPanel {
        grid-column: span 2; 
        grid-row: 3; 
    }

    .outagesPanel {
        grid-column: span 2; 
        grid-row: 3;
    }

    .activeIncidentsPanel {
        grid-column: span 2;
        grid-row: 3;
    }
    
}
