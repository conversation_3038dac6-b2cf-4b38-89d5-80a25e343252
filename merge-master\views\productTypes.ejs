
<%- include('header', { title: title }) %>
<%- include('menu', { currentTab: 'Form' }); %>
<div class="container">
    <br>
    <div class="card">
        <div class="card-body">
            <h3 class="card-title">Product Types</h3>
            <p class="card-text">This is a list of product types that can be identified during a Merge service check, the description states the sources and logic used to identify each product type. More product types may be added over time.</p>
        </div>
    </div>
    <br>
    <div class="input-group">
        <input type="text" class="form-control" id="filter-product-types"></input>
        <div class="input-group-append">
            <button class="btn btn-outline-secondary" type="button" title="Clear filter" id="filter-product-types-clear">&times;</button>
        </div>
    </div>
    <br>
    <hr>
    <div id="product-types-list"></div>
</div>
<script>
const productTypes = JSON.parse(atob("<%- productTypes %>"));

$(document).ready(function() {
    filterProductTypesByName("");

    $("#filter-product-types").on("input", function() {
        filterProductTypesByName($("#filter-product-types").val());
    });

    $("#filter-product-types-clear").click(function() {
        $("#filter-product-types").val("");
        filterProductTypesByName("");
    });

    function filterProductTypesByName(filterText) {
        let filteredProductTypes = [];
        if (filterText) {
            filteredProductTypes = productTypes.filter(productType => {
                return productType.name.match(new RegExp(filterText, "i")) || productType.description.match(new RegExp(filterText, "i"));
            });
        } else {
            filteredProductTypes = productTypes;
        }

        $("#product-types-list").empty();
        for (productType of filteredProductTypes) {
            $("#product-types-list").append(
                $("<div>").addClass("card").append(
                    $("<div>").addClass("card-header").text(productType.name)
                ).append(
                    $("<div>").addClass("card-body").html(
                        $("<p>").text(productType.description)
                    )
                )
            ).append($("<br>"));
        }
    }
});
</script>
</body>
</html>
