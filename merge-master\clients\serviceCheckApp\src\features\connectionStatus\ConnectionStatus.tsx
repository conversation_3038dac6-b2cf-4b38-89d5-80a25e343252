import { TextStyle } from '@able/react'
import { GreenCheckIcon } from '../../components/statusIcon/GreenCheckIcon'
import { OrangeWarningIcon } from '../../components/statusIcon/OrangeWarningIcon'
import { RedCrossIcon } from '../../components/statusIcon/RedCrossIcon'
import { useSocketContext } from '../../context/SocketContext'
import styles from './ConnectionStatus.module.scss'

export const ConnectionStatus = () => {
    const { isConnected, reconnectAttempts } = useSocketContext()

    const renderStatus = () => {
        if (isConnected) {
            return (
                <div className={styles.connectionStatus}>
                    <GreenCheckIcon />
                    <TextStyle alias="FinePrintA">Connected</TextStyle>
                </div>
            )
        } else if (reconnectAttempts > 0) {
            return (
                <div className={styles.connectionStatus}>
                    <OrangeWarningIcon />
                    <TextStyle alias="FinePrintA">
                        {`Reconnecting... (Attempt #${reconnectAttempts})`}
                    </TextStyle>
                </div>
            )
        } else {
            return (
                <div className={styles.connectionStatus}>
                    <RedCrossIcon />
                    <TextStyle alias="FinePrintA">
                        Disconnected. Trying to reconnect...
                    </TextStyle>
                </div>
            )
        }
    }

    return <div>{renderStatus()}</div>
}
