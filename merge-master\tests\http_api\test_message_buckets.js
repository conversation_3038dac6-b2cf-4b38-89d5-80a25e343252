'use strict';

import mongoose from 'mongoose';
import chai from 'chai';
import chaiHttp from 'chai-http';
import chai<PERSON>ike from 'chai-like';
import chaiDateTime from 'chai-datetime';

import superagentDefaults from 'superagent-defaults';
import supertest from 'supertest';
import cookie from 'cookie';
import _ from 'lodash';
import sinon from 'sinon';
import assert from 'assert';

var app;
var request;
import config from '../config.js';
import MessageBucket from '../../db/model/messageBucket.js';
import helpers from '../helpers.js';
import { UNIT_TEST_ALL_ACCESS_USERNAME, UNIT_TEST_USERS } from '../users.js';

const should = chai.should();

chaiLike.extend(helpers.dateFieldCheck);
chaiLike.extend(helpers.regexCheck);
chai.use(chaiLike);
chai.use(chaiHttp);
chai.use(chaiDateTime);


const MESSAGE_BUCKET_KEYS = Object.freeze(Object.keys(MessageBucket.schema.obj));


describe('Merge message buckets REST endpoints', () => {
    // Setup and connect to mongodb memory server, start merge app instance
    // Set request variable with a configured supertest agent with test JWT and app instance
    before(async function() {
        global.gConfig = _.cloneDeep(config.default);
        app = (await import('../../app.js')).default;
        request = superagentDefaults(supertest(app));
    });

    // Drop all contents of database before each test
    beforeEach(async function() {
        global.gConfig = _.cloneDeep(config.default);

        // Uses deleteMany instead of mongoose.connection.db.dropDatabase() because
        // custom indexes appear to be deleted if dropDatabase() is used
        for (let collectionName in mongoose.connection.collections) {
            await mongoose.connection.collections[collectionName].deleteMany({});
        }

        await helpers.authenticateSession(request, UNIT_TEST_ALL_ACCESS_USERNAME);
    });

    afterEach(async function() {
        sinon.restore();
    });

    describe('Read list of message buckets', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/messageBuckets');

            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/messageBuckets');

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Get empty list of message buckets', async() => {
            let res = await request.get('/messageBuckets');

            res.should.have.status(200);
            chai.expect(res.body).to.eql({
                metadata: {
                    pagination: {
                        limit: 100,
                        offset: 0,
                        total: 0,
                        prev: null,
                        next: null
                    }
                },
                results: []
            });
        });

        it('Get list of message buckets with one message bucket (default values)', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'MessageBucketName1',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);

            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(MESSAGE_BUCKET_KEYS);
            });
        });

        it('Get list of message buckets with three message buckets (default values)', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName2'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'MessageBucketName1',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'MessageBucketName2',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'MessageBucketName3',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(MESSAGE_BUCKET_KEYS);
            });
        });

        it('Get list of message buckets with one message bucket', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1',
                description: 'MessageBucket Description 1',
                messageFrontOfHouse: 'FOH Message 1',
                messageCustomer: 'Customer Message 1',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'MessageBucketName1',
                description: 'MessageBucket Description 1',
                messageFrontOfHouse: 'FOH Message 1',
                messageCustomer: 'Customer Message 1',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(MESSAGE_BUCKET_KEYS);
            });
        });

        it('Get list of message buckets with two message buckets', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1',
                description: 'MessageBucket Description 1',
                messageFrontOfHouse: 'FOH Message 1',
                messageCustomer: 'Customer Message 1',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName2',
                description: 'MessageBucket Description 2',
                messageFrontOfHouse: 'FOH Message 2',
                messageCustomer: 'Customer Message 2',
                createdOn: currDate,
                createdBy: 'merge'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets');

            res.should.have.status(200);

            chai.expect(res.body.results).to.like([{
                name: 'MessageBucketName1',
                description: 'MessageBucket Description 1',
                messageFrontOfHouse: 'FOH Message 1',
                messageCustomer: 'Customer Message 1',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            },
            {
                name: 'MessageBucketName2',
                description: 'MessageBucket Description 2',
                messageFrontOfHouse: 'FOH Message 2',
                messageCustomer: 'Customer Message 2',
                createdOn: currDate,
                createdBy: 'merge'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(MESSAGE_BUCKET_KEYS);
            });
        });

        it('Get one message bucket with limit', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName2'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName3'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName4'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName5'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets?limit=1');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'MessageBucketName1',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(MESSAGE_BUCKET_KEYS);
            });
        });

        it('Get one message bucket with limit and offset', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName2'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName3'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName4'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName5'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets?limit=2&offset=2');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'MessageBucketName3',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'MessageBucketName4',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(MESSAGE_BUCKET_KEYS);
            });
        });

        it('Get message bucket list sorted by name ascending', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName2'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets?sort=name&order=asc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'MessageBucketName1',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'MessageBucketName2',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'MessageBucketName3',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(MESSAGE_BUCKET_KEYS);
            });
        });

        it('Get message bucket list sorted by name descending', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName2'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets?sort=name&order=desc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'MessageBucketName3',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'MessageBucketName2',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'MessageBucketName1',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(MESSAGE_BUCKET_KEYS);
            });
        });

        it('Get message bucket list sorted by name ascending case insensitive', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName2'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets?sort=name&order=asc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'MessageBucketName1',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'MessageBucketName2',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'MessageBucketName3',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(MESSAGE_BUCKET_KEYS);
            });
        });

        it('Get message bucket list sorted by name descending case insensitive', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName2'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets?sort=name&order=desc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'MessageBucketName3',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'MessageBucketName2',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'MessageBucketName1',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(MESSAGE_BUCKET_KEYS);
            });
        });

        it('Get message bucket list sorted by description ascending', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1',
                description: 'Bravo'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName2',
                description: 'Charlie'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName3',
                description: 'Alpha'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets?sort=description&order=asc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'MessageBucketName3',
                description: 'Alpha',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'MessageBucketName1',
                description: 'Bravo',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'MessageBucketName2',
                description: 'Charlie',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(MESSAGE_BUCKET_KEYS);
            });
        });

        it('Get message bucket list sorted by description descending', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1',
                description: 'Bravo'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName2',
                description: 'Charlie'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName3',
                description: 'Alpha'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets?sort=description&order=desc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'MessageBucketName2',
                description: 'Charlie',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'MessageBucketName1',
                description: 'Bravo',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'MessageBucketName3',
                description: 'Alpha',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(MESSAGE_BUCKET_KEYS);
            });
        });

        it('Get message bucket list sorted by description ascending case insensitive', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1',
                description: 'Test Description 1'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName2',
                description: 'test description 2'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName3',
                description: 'TEST DESCRIPTION 3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets?sort=description&order=asc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'MessageBucketName1',
                description: 'Test Description 1',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'MessageBucketName2',
                description: 'test description 2',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'MessageBucketName3',
                description: 'TEST DESCRIPTION 3',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(MESSAGE_BUCKET_KEYS);
            });
        });

        it('Get message bucket list sorted by description descending case insensitive', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1',
                description: 'Test Description 1'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName2',
                description: 'test description 2'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName3',
                description: 'TEST DESCRIPTION 3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets?sort=description&order=desc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'MessageBucketName3',
                description: 'TEST DESCRIPTION 3',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'MessageBucketName2',
                description: 'test description 2',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'MessageBucketName1',
                description: 'Test Description 1',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(MESSAGE_BUCKET_KEYS);
            });
        });

        it('Get message bucket list filter by name equality 1', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'TestMessageBucket1'
            }).save());

            promises.push(new MessageBucket({
                name: 'TestMessageBucket2'
            }).save());

            promises.push(new MessageBucket({
                name: 'AnotherMessageBucket1'
            }).save());

            promises.push(new MessageBucket({
                name: 'AnotherMessageBucket2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets?name=TestMessageBucket1');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'TestMessageBucket1',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(MESSAGE_BUCKET_KEYS);
            });
        });

        it('Get message bucket list filter by name equality 2', async() => {
            let promises = [];

            promises.push(new MessageBucket({
                name: 'TestMessageBucket1'
            }).save());

            promises.push(new MessageBucket({
                name: 'TestMessageBucket2'
            }).save());

            promises.push(new MessageBucket({
                name: 'AnotherMessageBucket1'
            }).save());

            promises.push(new MessageBucket({
                name: 'AnotherMessageBucket2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets?name=Test');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([]);
        });

        it('Get message bucket list filter by name equality 3', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'TestMessageBucket1'
            }).save());

            promises.push(new MessageBucket({
                name: 'TestMessageBucket2'
            }).save());

            promises.push(new MessageBucket({
                name: 'AnotherMessageBucket1'
            }).save());

            promises.push(new MessageBucket({
                name: 'AnotherMessageBucket2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets?name[equal]=AnotherMessageBucket2');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'AnotherMessageBucket2',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(MESSAGE_BUCKET_KEYS);
            });
        });

        it('Get message bucket list filter by name equality 4', async() => {
            let promises = [];

            promises.push(new MessageBucket({
                name: 'TestMessageBucket1'
            }).save());

            promises.push(new MessageBucket({
                name: 'TestMessageBucket2'
            }).save());

            promises.push(new MessageBucket({
                name: 'AnotherMessageBucket1'
            }).save());

            promises.push(new MessageBucket({
                name: 'AnotherMessageBucket2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets?name[equal]=MessageBucket');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([]);
        });

        it('Get message bucket list filter by name like 1', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'TestMessageBucket1'
            }).save());

            promises.push(new MessageBucket({
                name: 'TestMessageBucket2'
            }).save());

            promises.push(new MessageBucket({
                name: 'AnotherMessageBucket1'
            }).save());

            promises.push(new MessageBucket({
                name: 'AnotherMessageBucket2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets?name[like]=Test');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'TestMessageBucket1',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'TestMessageBucket2',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(MESSAGE_BUCKET_KEYS);
            });
        });

        it('Get message bucket list filter by name like 2', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'TestMessageBucket1'
            }).save());

            promises.push(new MessageBucket({
                name: 'TestMessageBucket2'
            }).save());

            promises.push(new MessageBucket({
                name: 'AnotherMessageBucket1'
            }).save());

            promises.push(new MessageBucket({
                name: 'AnotherMessageBucket2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets?name[like]=MessageBucket1');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'AnotherMessageBucket1',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'TestMessageBucket1',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(MESSAGE_BUCKET_KEYS);
            });
        });

        it('Get message bucket list filter by name like 3', async() => {
            let promises = [];

            promises.push(new MessageBucket({
                name: 'TestMessageBucket1'
            }).save());

            promises.push(new MessageBucket({
                name: 'TestMessageBucket2'
            }).save());

            promises.push(new MessageBucket({
                name: 'AnotherMessageBucket1'
            }).save());

            promises.push(new MessageBucket({
                name: 'AnotherMessageBucket2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets?name[like]=Text');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([]);
        });

        it('Get message bucket list filter by name like 4 (case insensitive)', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'TestMessageBucket1'
            }).save());

            promises.push(new MessageBucket({
                name: 'TestMessageBucket2'
            }).save());

            promises.push(new MessageBucket({
                name: 'AnotherMessageBucket1'
            }).save());

            promises.push(new MessageBucket({
                name: 'AnotherMessageBucket2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets?name[like]=AnOtHer');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'AnotherMessageBucket1',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'AnotherMessageBucket2',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(MESSAGE_BUCKET_KEYS);
            });
        });

        it('Get message bucket list filter by description equality 1', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1',
                description: 'A message bucket description'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName2',
                description: 'Another message bucket description'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName3',
                description: 'MessageBucket description here'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName4',
                description: 'Yet another message bucket description'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets?description=MessageBucket%20description%20here');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'MessageBucketName3',
                description: 'MessageBucket description here',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(MESSAGE_BUCKET_KEYS);
            });
        });

        it('Get message bucket list filter by description equality 2', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1',
                description: 'A message bucket description'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName2',
                description: 'Another message bucket description'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName3',
                description: 'MessageBucket description here'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName4',
                description: 'Yet another message bucket description'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets?description[equal]=MessageBucket%20description%20here');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'MessageBucketName3',
                description: 'MessageBucket description here',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(MESSAGE_BUCKET_KEYS);
            });
        });

        it('Get message bucket list filter by description like 1', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1',
                description: 'Extract field here'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName2',
                description: 'Extract field there'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName3',
                description: 'Summarise field here'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName4',
                description: 'Summarise field there'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets?description[like]=Extract');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'MessageBucketName1',
                description: 'Extract field here',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'MessageBucketName2',
                description: 'Extract field there',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(MESSAGE_BUCKET_KEYS);
            });
        });

        it('Get message bucket list filter by description like 2 (case insensitive)', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1',
                description: 'Extract field here'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName2',
                description: 'Extract field there'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName3',
                description: 'Summarise field here'
            }).save());

            promises.push(new MessageBucket({
                name: 'MessageBucketName4',
                description: 'Summarise field there'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets?description[like]=THeRE');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'MessageBucketName2',
                description: 'Extract field there',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }, {
                name: 'MessageBucketName4',
                description: 'Summarise field there',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(MESSAGE_BUCKET_KEYS);
            });
        });

        it('Get message bucket list invalid limit (non-integer)', async() => {
            let res = await request.get('/messageBuckets?limit=notalimit');

            res.should.have.status(400);
        });

        it('Get message bucket list invalid limit (integer equals 0)', async() => {
            let res = await request.get('/messageBuckets?limit=0');

            res.should.have.status(400);
        });

        it('Get message bucket list invalid limit (negative integer)', async() => {
            let res = await request.get('/messageBuckets?limit=-1');

            res.should.have.status(400);
        });

        it('Get message bucket list invalid offset (non-integer)', async() => {
            let res = await request.get('/messageBuckets?offset=notanoffset');

            res.should.have.status(400);
        });

        it('Get message bucket list invalid offset (negative integer)', async() => {
            let res = await request.get('/messageBuckets?offset=-1');

            res.should.have.status(400);
        });

        it('Get message bucket list invalid order parameter', async() => {
            let res = await request.get('/messageBuckets?order=ascending');

            res.should.have.status(400);
        });

        it('Get message bucket list invalid name filter operator', async() => {
            let res = await request.get('/messageBuckets?name[ophere]=test');

            res.should.have.status(400);
        });

        it('Get message bucket list empty description filter operator', async() => {
            let res = await request.get('/messageBuckets?description[]=no-op');

            res.should.have.status(400);
        });

        it('Get message bucket list with HTTP GET with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            let res = await request.get('/messageBuckets');

            res.should.have.status(200);
            chai.expect(res.body).to.eql({
                metadata: {
                    pagination: {
                        limit: 100,
                        offset: 0,
                        total: 0,
                        prev: null,
                        next: null
                    }
                },
                results: []
            });
        });
    });

    describe('Read message bucket', () => {
        it('Unauthenticated request', async() => {
            await new MessageBucket({
                name: 'MessageBucketName'
            }).save();

            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/messageBuckets/MessageBucketName');

            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            await new MessageBucket({
                name: 'MessageBucketName'
            }).save();

            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/messageBuckets/MessageBucketName');

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Get non-existant message bucket', async() => {
            let res = await request.get('/messageBuckets/MessageBucketName1');

            res.should.have.status(404);
        });

        it('Get one existing message bucket (default values)', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets/MessageBucketName1');

            res.should.have.status(200);
            chai.expect(res.body).to.like({
                name: 'MessageBucketName1',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(res.body).to.have.all.keys(MESSAGE_BUCKET_KEYS);
        });

        it('Get one existing message bucket', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1',
                description: 'MessageBucket Description 1',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets/MessageBucketName1');

            res.should.have.status(200);

            chai.expect(res.body).to.like({
                name: 'MessageBucketName1',
                description: 'MessageBucket Description 1',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });

            chai.expect(res.body).to.have.all.keys(MESSAGE_BUCKET_KEYS);
        });

        it('Get existing message bucket with HTTP GET with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/messageBuckets/MessageBucketName1');

            res.should.have.status(200);
            chai.expect(res.body).to.like({
                name: 'MessageBucketName1',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(res.body).to.have.all.keys(MESSAGE_BUCKET_KEYS);
        });
    });

    describe('Create message bucket', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.post('/messageBuckets').send({
                name: 'MessageBucketName1'
            });

            res.should.have.status(401);
            chai.expect(await MessageBucket.find({ name: 'MessageBucketName1' }).countDocuments()).to.eql(0);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.post('/messageBuckets').send({
                    name: 'MessageBucketName1'
                });

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(201);
                            chai.expect(await MessageBucket.find({ name: 'MessageBucketName1' }).countDocuments()).to.eql(1);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                            chai.expect(await MessageBucket.find({ name: 'MessageBucketName1' }).countDocuments()).to.eql(0);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }

                await MessageBucket.deleteMany({});
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Create message bucket with HTTP POST (default values)', async() => {
            let currDate = new Date();

            let postRes = await request.post('/messageBuckets').send({
                name: 'MessageBucketName1'
            });

            postRes.should.have.status(201);
            chai.expect(postRes.body).to.like({
                name: 'MessageBucketName1',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });

            let getRes = await request.get('/messageBuckets/MessageBucketName1');

            getRes.should.have.status(200);
            chai.expect(getRes.body).to.like({
                name: 'MessageBucketName1',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(getRes.body).to.have.all.keys(MESSAGE_BUCKET_KEYS);
        });

        it('Create message bucket with HTTP POST', async() => {
            let currDate = new Date();

            let postRes = await request.post('/messageBuckets').send({
                name: 'MessageBucketName1',
                description: 'Description Here',
                messageFrontOfHouse: 'Front of House Message Here',
                messageCustomer: 'Customer Message Here'
            });

            postRes.should.have.status(201);
            chai.expect(postRes.body).to.like({
                name: 'MessageBucketName1',
                description: 'Description Here',
                messageFrontOfHouse: 'Front of House Message Here',
                messageCustomer: 'Customer Message Here',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(postRes.body).to.have.all.keys(MESSAGE_BUCKET_KEYS);

            let getRes = await request.get('/messageBuckets/MessageBucketName1');

            getRes.should.have.status(200);
            chai.expect(getRes.body).to.like({
                name: 'MessageBucketName1',
                description: 'Description Here',
                messageFrontOfHouse: 'Front of House Message Here',
                messageCustomer: 'Customer Message Here',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(getRes.body).to.have.all.keys(MESSAGE_BUCKET_KEYS);
        });

        it('Create duplicate message bucket with HTTP POST', async() => {
            let postRes1 = await request.post('/messageBuckets').send({
                name: 'MessageBucketName1'
            });

            postRes1.should.have.status(201);

            let postRes2 = await request.post('/messageBuckets').send({
                name: 'MessageBucketName1',
            });

            postRes2.should.have.status(409);
        });

        it('Create message bucket with HTTP POST test createdBy immutable', async() => {
            let currDate = new Date();

            let res = await request.post('/messageBuckets').send({
                name: 'MessageBucketName1',
                createdBy: 'anotheruser'
            });

            res.should.have.status(201);
            chai.expect(res.body).to.like({
                name: 'MessageBucketName1',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(res.body).to.have.all.keys(MESSAGE_BUCKET_KEYS);
        });

        it('Create message bucket with HTTP POST test createdOn immutable', async() => {
            let customDate = new Date('2020-12-15T16:00:00.000Z');
            let currDate = new Date();

            let res = await request.post('/messageBuckets').send({
                name: 'MessageBucketName1',
                createdOn: customDate
            });

            res.should.have.status(201);
            chai.expect(res.body).to.like({
                name: 'MessageBucketName1',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(res.body).to.have.all.keys(MESSAGE_BUCKET_KEYS);
        });

        it('Create message bucket with HTTP POST unrecognised fields', async() => {
            let currDate = new Date();

            let postRes = await request.post('/messageBuckets').send({
                name: 'MessageBucketName1',
                customField: 'should not show up',
                unrecognisedField: 550
            });

            postRes.should.have.status(201);
            chai.expect(postRes.body).to.like({
                name: 'MessageBucketName1',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });

            chai.expect(postRes.body).to.have.all.keys(MESSAGE_BUCKET_KEYS);
            chai.expect(postRes.body).to.not.have.any.keys('customField', 'unrecognisedField');

            let getRes = await request.get('/messageBuckets/MessageBucketName1');

            getRes.should.have.status(200);
            chai.expect(getRes.body).to.like({
                name: 'MessageBucketName1',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(getRes.body).to.have.all.keys(MESSAGE_BUCKET_KEYS);
            chai.expect(getRes.body).to.not.have.any.keys('customField', 'unrecognisedField');
        });

        it('Create message bucket with HTTP POST with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            let postRes = await request.post('/messageBuckets').send({
                name: 'MessageBucketName1'
            });

            postRes.should.have.status(405);

            let getRes = await request.get('/messageBuckets/MessageBucketName1');

            getRes.should.have.status(404);
        });

        it('Create message bucket with HTTP POST with disableRuleSourceEditing enabled and admin user', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'admin');

            let postRes = await request.post('/messageBuckets').send({
                name: 'MessageBucketName1'
            });

            postRes.should.have.status(201);

            let getRes = await request.get('/messageBuckets/MessageBucketName1');

            getRes.should.have.status(200);
        });
    });

    describe('Update message bucket', () => {
        it('Unauthenticated request', async() => {
            await new MessageBucket({
                name: 'MessageBucketName1',
                description: 'test description'
            }).save();

            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.put('/messageBuckets/MessageBucketName1').send({
                description: 'new description'
            });

            res.should.have.status(401);
            chai.expect((await MessageBucket.findOne({ name: 'MessageBucketName1' })).description).to.eql('test description');
        });

        it('Authorization tests', async() => {
            await new MessageBucket({
                name: 'MessageBucketName1',
                description: 'test description'
            }).save();

            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.put('/messageBuckets/MessageBucketName1').send({
                    description: 'new description'
                });

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                            chai.expect((await MessageBucket.findOne({ name: 'MessageBucketName1' })).description).to.eql('new description');
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                            chai.expect((await MessageBucket.findOne({ name: 'MessageBucketName1' })).description).to.eql('test description');
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
                await MessageBucket.updateOne({ name: 'MessageBucketName1' }, { $set: { description: 'test description' }});
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Update message bucket with HTTP PUT', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/messageBuckets/MessageBucketName1').send({
                name: 'MessageBucketName1',
                description: 'MessageBucket Description 1',
                messageFrontOfHouse: 'Front of House Message Here',
                messageCustomer: 'Customer Message Here'
            });

            res.should.have.status(200);
            chai.expect(res.body).to.like({
                name: 'MessageBucketName1',
                description: 'MessageBucket Description 1',
                messageFrontOfHouse: 'Front of House Message Here',
                messageCustomer: 'Customer Message Here',
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(res.body).to.have.all.keys(MESSAGE_BUCKET_KEYS);
        });

        it('Update message bucket with HTTP PUT test non-existant message bucket', async() => {
            let res = await request.put('/messageBuckets/MessageBucketName1');

            res.should.have.status(404);
        });

        it('Update message bucket with HTTP PUT test name immutable', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1'
            }).save());

            await Promise.all(promises);
            let putRes = await request.put('/messageBuckets/MessageBucketName1').send({
                name: 'MessageBucketName2'
            });

            putRes.should.have.status(200);

            let getRes1 = await request.get('/messageBuckets/MessageBucketName1');

            getRes1.should.have.status(200);
            chai.expect(getRes1.body).to.like({
                name: 'MessageBucketName1',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(getRes1.body).to.have.all.keys(MESSAGE_BUCKET_KEYS);

            let getRes2 = await request.get('/messageBuckets/MessageBucketName2');

            getRes2.should.have.status(404);
        });

        it('Update message bucket with HTTP PUT test createdBy immutable', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1',
                createdBy: 'testuser'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/messageBuckets/MessageBucketName1').send({
                createdBy: 'anotheruser'
            });

            res.should.have.status(200);
            chai.expect(res.body).to.like({
                name: 'MessageBucketName1',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'testuser'
            });
            chai.expect(res.body).to.have.all.keys(MESSAGE_BUCKET_KEYS);
        });

        it('Update message bucket with HTTP PUT test createdOn immutable', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/messageBuckets/MessageBucketName1').send({
                createdOn: '2010-01-15T16:00:00.000Z'
            });

            res.should.have.status(200);
            chai.expect(res.body).to.like({
                name: 'MessageBucketName1',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(res.body).to.have.all.keys(MESSAGE_BUCKET_KEYS);
        });

        it('Update message bucket with HTTP PUT unrecognised fields', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/messageBuckets/MessageBucketName1').send({
                badField: 'badValue'
            });

            res.should.have.status(200);

            chai.expect(res.body).to.like({
                name: 'MessageBucketName1',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            });

            chai.expect(res.body).to.have.all.keys(MESSAGE_BUCKET_KEYS);
            chai.expect(res.body).to.not.have.any.keys('badField');
        });

        it('Update message bucket with HTTP PUT with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1'
            }).save());

            await Promise.all(promises);
            let putRes = await request.put('/messageBuckets/MessageBucketName1').send({
                description: 'new description'
            });

            putRes.should.have.status(405);

            let getRes = await request.get('/messageBuckets/MessageBucketName1');

            getRes.should.have.status(200);
            getRes.body.should.like({
                name: 'MessageBucketName1',
                description: '',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(getRes.body).to.have.all.keys(MESSAGE_BUCKET_KEYS);
        });

        it('Create message bucket with HTTP PUT with disableRuleSourceEditing enabled and admin user', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'admin');

            let currDate = new Date();
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1'
            }).save());

            await Promise.all(promises);
            let putRes = await request.put('/messageBuckets/MessageBucketName1').send({
                description: 'new description'
            });

            putRes.should.have.status(200);

            let getRes = await request.get('/messageBuckets/MessageBucketName1');

            getRes.should.have.status(200);
            getRes.body.should.like({
                name: 'MessageBucketName1',
                description: 'new description',
                messageFrontOfHouse: '',
                messageCustomer: '',
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(getRes.body).to.have.all.keys(MESSAGE_BUCKET_KEYS);
        });
    });

    describe('Delete message bucket', () => {
        it('Unauthenticated request', async() => {
            await new MessageBucket({
                name: 'MessageBucketName1'
            }).save();

            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.delete('/messageBuckets/MessageBucketName1');

            res.should.have.status(401);
            chai.expect(await MessageBucket.find({ name: 'MessageBucketName1' }).countDocuments()).to.eql(1);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                if (await MessageBucket.find({ name: 'MessageBucketName1' }).countDocuments() === 0) {
                    await new MessageBucket({
                        name: 'MessageBucketName1'
                    }).save();
                }

                await helpers.authenticateSession(request, username);
                let res = await request.delete('/messageBuckets/MessageBucketName1');

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                            chai.expect(await MessageBucket.find({ name: 'MessageBucketName1' }).countDocuments()).to.eql(0);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                            chai.expect(await MessageBucket.find({ name: 'MessageBucketName1' }).countDocuments()).to.eql(1);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Delete message bucket with HTTP DELETE', async() => {
            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1'
            }).save());

            await Promise.all(promises);
            let deleteRes = await request.delete('/messageBuckets/MessageBucketName1');

            deleteRes.should.have.status(200);

            let getRes = await request.get('/messageBuckets/MessageBucketName1');

            getRes.should.have.status(404);
        });

        it('Delete message bucket with HTTP DELETE test non-existant message bucket', async() => {
            let res = await request.delete('/messageBuckets/MessageBucketName1');

            res.should.have.status(404);
        });

        it('Delete message bucket with HTTP DELETE with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1'
            }).save());

            await Promise.all(promises);
            let deleteRes = await request.delete('/messageBuckets/MessageBucketName1');

            deleteRes.should.have.status(405);

            let getRes = await request.get('/messageBuckets/MessageBucketName1');

            getRes.should.have.status(200);
        });

        it('Delete message bucket with HTTP DELETE with disableRuleSourceEditing enabled and admin user', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'admin');

            let promises = [];

            promises.push(new MessageBucket({
                name: 'MessageBucketName1'
            }).save());

            await Promise.all(promises);
            let deleteRes = await request.delete('/messageBuckets/MessageBucketName1');

            deleteRes.should.have.status(200);

            let getRes = await request.get('/messageBuckets/MessageBucketName1');

            getRes.should.have.status(404);
        });
    });
});