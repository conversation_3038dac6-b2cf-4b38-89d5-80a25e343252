
import fs from 'fs';
import path, { dirname } from 'path';
import { fileURLToPath } from 'url';

const __dirname = dirname(fileURLToPath(import.meta.url));

export const UNIT_TEST_ALL_ACCESS_USERNAME = 'allAccess';
export const UNIT_TEST_USERS = JSON.parse(await fs.promises.readFile(path.join(__dirname, './usersTest.json')));
export const UNIT_TEST_API_SERVICE_CHECK_USERS = JSON.parse(await fs.promises.readFile(path.join(__dirname, './usersTestApiServiceCheck.json')));
export const UNIT_TEST_PASSWORD = 'UnitTestAuthentication';

export default {
    UNIT_TEST_ALL_ACCESS_USERNAME: UNIT_TEST_ALL_ACCESS_USERNAME,
    UNIT_TEST_USERS: UNIT_TEST_USERS,
    UNIT_TEST_API_SERVICE_CHECK_USERS: UNIT_TEST_API_SERVICE_CHECK_USERS,
    UNIT_TEST_PASSWORD: UNIT_TEST_PASSWORD
};