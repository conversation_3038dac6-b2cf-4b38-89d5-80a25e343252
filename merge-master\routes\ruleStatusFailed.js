
import express from 'express';

import auth from '../modules/auth.js';
import { AuthorizationRoles } from '../modules/enumerations.js';

const router = express.Router();


router.get('/', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), function (req, res) {
    res.render('ruleStatusFailed', { title: 'Rule Status (Failed)', user: req.user });
});


export default router;
