/**
 * Note: This migration file was created when 'migrate-mongoose' was the npm package
 * used to perform MongoDB migrations, since the 'migrate' npm package is now used
 * the implementation may not be compatible and all functionality is commented out
 */

// const mongoose = require('mongoose');

// const migrate = require('./migrate');

// const serviceCheckModel = new mongoose.Schema({
//   id: { type: String, index: true, immutable: true },
//   fnn: { type: String, index: true },
//   phoneNumber: { type: String, index: true, default: null },
//   billingFNN: String,
//   carriageFNN: String,
//   carriageFNNBackup: String,
//   MDNFNN: String,
//   carriageType: String,
//   nbnServiceType: String,
//   nbnAccessType: String,
//   nbnSubAccessType: String,
//   deviceName: String,
//   CIDN: String,
//   address: String,
//   OffshoreResources: String,
//   serviceType: { type: String, default: null },
//   siiamCases: {
//       type: [{
//           type: String
//       }],
//       default: []
//   },
//   serviceNowIncidents: {
//       type: [{
//           type: String
//       }],
//       default: []
//   },
//   serviceCentralIncidents: {
//       type: [{
//           type: String
//       }],
//       default: []
//   },
//   status: String,
//   createdBy: { type: String, required: true, immutable: true, default: "unknown", index: true },
//   createdOn: { type: Date, required: true, immutable: true, default: Date.now, index: true },
//   endedOn: Date,
//   durationMilliSec: Number,
//   input: {
//       searchFNN: String,
//       carriageType: String,
//       carriageFNN: String,
//       deviceName: String,
//       deviceIP: String,
//       level: Number,
//       process: String,
//       suite : String,
//       idType : String
//   },
//   additionalParameters: Object,
//   rulesData: Object,
//   sourcesData: Object,
//   sourcesMetadata: Object,
//   saveError: String
// }, { minimize: false });


// const ServiceCheckModel = mongoose.model('serviceCheck', serviceCheckModel);

/**
 * Make any changes you need to make to the database here
 */
async function up () {
    // await migrate.connect();

    // for await (const record of ServiceCheckModel.find({})) {
    //     if (record.rulesData && record.rulesData.MSR002 && record.rulesData.MSR002.valueMsg) {
    //         record.rulesData.MSR002.valueMsg = record.rulesData.MSR002.valueMsg.replace(/<table>None<table>/g, "<table>");
    //         record.rulesData.MSR002.valueMsg = record.rulesData.MSR002.valueMsg.replace(/: None<\/table>/g, ": None");
    //         record.rulesData.MSR002.valueMsg = record.rulesData.MSR002.valueMsg.replace(/: <\/table>/g, ": None");
    //         record.rulesData.MSR002.valueMsg = record.rulesData.MSR002.valueMsg.replace(/: $/g, ": None");

    //         record.rulesData.MSR002.serviceCheckLinks = record.rulesData.MSR002.serviceCheckLinks.replace(/<table>None<table>/g, "<table>");
    //         record.rulesData.MSR002.serviceCheckLinks = record.rulesData.MSR002.serviceCheckLinks.replace(/^None<\/table>/g, ": None");
    //         record.rulesData.MSR002.serviceCheckLinks = record.rulesData.MSR002.serviceCheckLinks.replace(/^<\/table>/g, ": None");

    //         if (record.rulesData.MSR002.serviceCheckLinks === "") {
    //             record.rulesData.MSR002.serviceCheckLinks = "None";
    //         }
    //     }

    //     record.markModified('rulesData');
    //     await record.save();
    // }
}

/**
 * Make any changes that UNDO the up function side effects here (if possible)
 */
async function down () {
    // No reverse migration
}

module.exports = { up, down };
