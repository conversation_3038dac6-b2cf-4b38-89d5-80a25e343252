import debug from 'debug';
import moment from 'moment';

import commandRunModel from '../db/model/commandRun.js';
import { runCommand } from '../modules/commandRun.js';
import logger from '../modules/logger.js';
import { generateId } from '../modules/helpers/id.js';


export function socketListen(socket) {
    socket.on('commandRun', async function (command) {
        if (typeof command !== 'string') {
            logger.warn('Non-string was passed into commandRun socket');
            socket.emit('QCError', new TypeError('socket commandRun only accepts string as input'));
            return;
        }

        let startTime = moment();
        let QCr = new commandRunModel({
            id: generateId('MCR'),
            command: command.trim(),
            createdBy: socket.request.user.username
        });

        logger.info(`Command "${QCr.command}" by "${QCr.createdBy}" submited via socket.`);

        try {
            await QCr.save();

            socket.emit('QCStart', QCr);

            runCommand(QCr, socket, function(error, response) {
                // Calculate duration
                QCr.duration = (moment().diff(startTime) / 1000).toFixed(2);

                if (error) {
                    QCr.status = 'Error';
                    QCr.error = error.toString();
                } else {
                    QCr.status = 'Completed';
                }

                if (response) {
                    QCr.statusCode = response.status;

                    if (typeof response.data === 'object') {
                        QCr.result = JSON.stringify(response.data);
                    } else {
                        QCr.result = response.data;
                    }
                }

                QCr.save().catch((error) => {
                    logger.error(`Could not save quick command record ${QCr.id}, ${error.toString()}`);
                });

                socket.emit('QC', QCr);
            });

        } catch(error) {
            logger.error(`Could not save quick command record ${QCr.id}, ${error.toString()}`);
        }
    });
}