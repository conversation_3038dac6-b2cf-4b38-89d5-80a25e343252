
import assert from 'assert';
import _ from 'lodash';

import Api from '../../db/model/api.js';
import mergeConfigList from '../../modules/mergeConfigList.js';
import { MERGE_OBJECT_CONFIG_FILES } from '../../modules/helpers/constants.js';
import helpers from '../helpers.js';


describe('Merge APIs', () => {
    it('Unique names', async () => {
        let apis = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.api)).apis;
        let apiNames = new Set();

        for (let i in apis) {
            if (apiNames.has(apis[i].name)) {
                assert.fail(`Duplicate API name ${apis[i].name} in config`);
            }
            apiNames.add(apis[i].name);
        };
    });

    it('Valid fields', async () => {
        let apis = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.api)).apis;

        for (let i in apis) {
            let api = new Api(apis[i]);
            try {
                await api.validate();
            } catch(error) {
                let newError = new Error(`Error validating API ${api.name}`);
                newError.original_error = error;
                newError.stack = error.stack;
                throw newError;
            }
        }
    });

    it('Ordered by name ascending', async () => {
        let apis = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.api)).apis;

        for (let i = 1; i < apis.length; i++) {
            let prevApi = apis[i - 1];
            let currApi = apis[i];

            if (prevApi.name.localeCompare(currApi.name, 'en') > 0) {
                assert.fail(`API name ${prevApi.name} is out of order, should be after API ${currApi.name}`);
            }
        }
    });

    it('Correct field order', async() => {
        let apis = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.api)).apis;
        let invalidNames = [];

        for (let i in apis) {
            let apiJson = new Api(apis[i]).toJSON();
            delete apiJson.createdBy;
            delete apiJson.createdOn;

            if (!_.isEqualWith(apis[i], apiJson, helpers.compareObjectWithKeyOrder)) {
                invalidNames.push(apis[i].name);
            };
        }

        if (invalidNames.length) {
            assert.fail(`Key order for API(s) ${invalidNames.join(',')} does not match database schema order`);
        }
    });
});

