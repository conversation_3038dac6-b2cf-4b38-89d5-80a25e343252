
import Ajv from 'ajv';
import fs from 'fs';


import logger from '../modules/logger.js';

const ajv = new Ajv({ allErrors: true });
const testPackages = JSON.parse(await fs.promises.readFile('./config/testPackages.json'));
const testPackagesSchema = JSON.parse(await fs.promises.readFile('./config/testPackagesSchema.json'));
const validate = ajv.compile(testPackagesSchema);

if (!validate(testPackages)) {
    logger.error(`Test packages: config file does not match schema, ${JSON.stringify(validate.errors)}`);

    throw new Error(`Test package configuration file does not match ` +
                    `JSON schema\n` +
                    `${JSON.stringify(validate.errors, null, 4)}`);
}


export function getTestPackages() {
    return testPackages;
}


export function getTestPackagesForProductTypes(serviceCheckProductTypes) {
    return testPackages.filter((testPackage) => {
        let includesProductType = false;

        for (let productType of testPackage.productTypes) {
            if (serviceCheckProductTypes.includes(productType)) {
                includesProductType = true;
            }
            // Test package with 'All' in their product types are always included
            if (productType === 'All') {
                includesProductType = true;
            }
        }

        return includesProductType;
    });
}
