/**
 * Note: This migration file was created when 'migrate-mongoose' was the npm package
 * used to perform MongoDB migrations, since the 'migrate' npm package is now used
 * the implementation may not be compatible and all functionality is commented out
 */

// const mongoose = require('mongoose');

// const migrate = require('./migrate');


// const geoliteCallSchema = new mongoose.Schema({
//     createdOn: { type: Date, required: true, immutable: true, default: Date.now },
//     removeOn: { type: Date, required: true, immutable: true }
// },
// {
//     versionKey: false
// });

// const inputSchema = new mongoose.Schema({
//     searchFNN: { type: String, default: null },
//     suite: { type: String, default: "standard" },
//     level: { type: Number, min: 0, max: 6, default: 0 },
//     carriageFNN: { type: String, default: null },
//     carriageType: { type: String, default: null },
//     deviceIP: { type: String, default: null },
//     deviceName: { type: String, default: null },
//     idType: { type: String, default: null },
//     fieldInterfaceIP: { type: String, default: null },
//     ruleNames: { type: [{ type: String }], default: [] },
//     sourceNames: { type: [{ type: String }], default: [] },
// }, { _id: false });

// const serviceCheckSchema = new mongoose.Schema({
//     id: { type: String, default: null, index: true, immutable: true },
//     fnn: { type: String, default: "", index: true },
//     phoneNumber: { type: String, default: null, index: true },
//     billingFNN: { type: String, default: null },
//     carriageFNN: { type: String, default: null, index: true },
//     MDNFNN: { type: String, default: null },
//     carriageType: { type: String, default: null, index: true },
//     nbnServiceType: { type: String, default: null },
//     nbnAccessType: { type: String, default: null },
//     nbnSubAccessType: { type: String, default: null },
//     nbnId: { type: String, default: null },
//     deviceName: { type: String, default: null, index: true },
//     CIDN: { type: String, default: null },
//     address: { type: String, default: null },
//     OffshoreResources: { type: String, default: null },
//     serviceType: { type: String, default: null, index: true },
//     siiamCases: {
//         type: [{
//             type: String
//         }],
//         default: []
//     },
//     siiamCasesLength: { type: Number, default: 0, select: false, index: true },
//     serviceCentralIncidents: {
//         type: [{
//             type: String
//         }],
//         default: []
//     },
//     serviceCentralIncidentsLength: { type: Number, default: 0, select: false, index: true },
//     status: { type: String, default: null, index: true },
//     endedOn: { type: Date, default: null },
//     durationMilliSec: { type: Number, default: 0 },
//     input: {
//         type: inputSchema,
//         default: {}
//     },
//     additionalParameters: { type: Object, default: {} },
//     rulesData: { type: Object, default: {} },
//     sourcesData: { type: Object, default: {} },
//     sourcesMetadata: { type: Object, default: {} },
//     errorMessage: { type: String, default: null },
//     createdBy: { type: String, required: true, immutable: true, default: "unknown", index: true },
//     createdOn: { type: Date, required: true, immutable: true, default: Date.now, index: true }
// }, { minimize: false });


// const GeoliteCall = mongoose.model('geolitecall', geoliteCallSchema);
// const ServiceCheckModel = mongoose.model('servicechecks', serviceCheckSchema);

/**
 * Make any changes you need to make to the database here
 */
async function up () {
    // Goes through any service checks in the last month
    // which has collected the Geolite source and creates
    // an instance of a GeoliteCall model relative from the createdOn
    // field of the service check
    // await migrate.connect();

    // let startDate = new Date();
    // startDate.setMonth(startDate.getMonth() - 1);

    // let serviceCheckRecords = await ServiceCheckModel.find({
    //     createdOn: { $gte: startDate },
    //     "sourcesMetadata.Geolite.status": "Collected"
    // });

    // let savePromises = [];
    // for (let serviceCheckRecord of serviceCheckRecords) {
    //     let removeOnDate = serviceCheckRecord.createdOn;
    //     removeOnDate.setMonth(removeOnDate.getMonth() + 1);

    //     savePromises.push(new GeoliteCall({
    //         removeOn: removeOnDate
    //     }).save());
    // }
    // await savePromises;
}

/**
 * Make any changes that UNDO the up function side effects here (if possible)
 */
async function down () {
    // Removes all documents from GeoliteCall model
    // await migrate.connect();

    // await GeoliteCall.collection.drop();
}

module.exports = { up, down };
