<%- include("header", { title: "Rules / sources dependency graph" }) %>
<link rel="stylesheet" type="text/css" href="/public/stylesheets/select2.min.css">
<script src="/public/javascripts/vis-network.min.js"></script>
<script src="/public/javascripts/select2.min.js"></script>
<%- include("menu", {currentTab: "Form"}); %>

<div class="container">
    <br>
    <div class="jumbotron py-3">
        <p class="h2">Rules and Sources Dependency Graph</p>
    </div>
    <div class="row">
        <div class="col-8">
        </div>
        <div class="col-4">
            <div class="float-right">
                <div class="form-row">
                    <label class="col-form-label">Rule:</label>
                    <span style="background-color:#218838; color:#e0e0e0; width: 48px; height:32px;" class="rounded border p-2"></span>
                    <label class="col-form-label">Source:</label>
                    <span style="background-color:#eb7df4; color:#e0e0e0; width: 48px; height:32px;" class="rounded border p-2"></span>
                </div>
            </div>
        </div>
    </div>
    <br>
    <div class="border rounded" id="ruleSourceGraph"></div>
</div>

<script>

var currRules;
var currSources;

$(document).ready(function() {
    $("#selectSuite").select2({
        minimumResultsForSearch: -1,
        width: "resolve"
    });

    $("#selectSuite").on("select2:select", function(e) {
        // Only draw graph if rule and source data have been loaded
        if (currRules && currSources) {
            drawGraph(currRules, currSources);
        }

        localStorage.setItem("ruleConfigGraphTestSuite", $("#selectSuite").val());
    });

    let ruleConfigGraphTestSuite = localStorage.getItem("ruleConfigGraphTestSuite");
    if (ruleConfigGraphTestSuite) {
        $("#selectSuite").val(ruleConfigGraphTestSuite);
        $("#selectSuite").trigger("change");
    }

    $("#refreshSuite").click(function() {
        reloadAndDrawGraph();
    });

    reloadAndDrawGraph();
});

function reloadAndDrawGraph() {
    setButtonSpinner($("#refreshSuite"), $("<span>").addClass("fas fa-sync"), true);
    $("#selectSuite").attr("disabled", true);

    let rulesPromise = getAllRules();
    let sourcesPromise = getAllSources();

    $.when(rulesPromise, sourcesPromise).done(function(rules, sources) {
        // Stores rules and sources locally so no reload is necessary if the test suite is changed
        currRules = rules;
        currSources = sources;

        drawGraph(rules, sources);
        setButtonSpinner($("#refreshSuite"), $("<span>").addClass("fas fa-sync"), false);

        $("#selectSuite").removeAttr("disabled");
    });
}

function drawGraph(rules, sources) {
    let ruleSourceGraphWidth = $("#ruleSourceGraph").parent().width();
    let ruleSourceGraphHeight = $(window).height() - $("#ruleSourceGraph").position().top;
    $("#ruleSourceGraph").css({
        width: `${ruleSourceGraphWidth}px`,
        height: `${ruleSourceGraphHeight}px`
    });

    // create an array with nodes
    let nodesRaw = [{
        id: "start",
        label: "Start Test",
        shape: "diamond",
        color: "#007bff",
        fixed: true,
        font: { size: 100 }
    }];
    let edgesRaw = [];

    let filteredSources = [];
    let filter = $('#selectSuite').val();

    sources.forEach(function(source) {
        nodesRaw.push({
            id: source.name,
            label: source.name,
            color: "#eb7df4",
            font: { color: "#e0e0e0" },
            shadow: true,
            data: source
        });

        if (source.preSources.length == 0 && source.preRules.length == 0) {
            edgesRaw.push({ from: "start", to: source.name });
        } else {
            source.preSources.forEach(function(presource) {
                edgesRaw.push({ from: presource, to: source.name });
            });

            source.preRules.forEach(function(prerule) {
                edgesRaw.push({ from: prerule, to: source.name });
            });
        }
    });


    rules.forEach(function(rule) {
        if (rule.preSources.length == 0 && rule.preRules.length == 0) {
            edgesRaw.push({ from: "start", to: rule.name });
        } else {
            var keep = false;
            rule.preSources.forEach(function(presource) {
                if (!filteredSources.includes(presource)) {
                    edgesRaw.push({ from: presource, to: rule.name });
                    keep = true;
                }
            });

            if (!keep) {
                return;
            }

            rule.preRules.forEach(function(prerule) {
                edgesRaw.push({ from: prerule, to: rule.name });
            });
        }

        nodesRaw.push({
            id: rule.name,
            label: rule.name + '\n' + rule.description,
            color: "#218838",
            font: { color: "#e0e0e0" },
            shadow: true,
            data: rule
        });
    });


    delete result;
    var nodes = new vis.DataSet(nodesRaw);

    // create an array with edges
    var edges = new vis.DataSet(edgesRaw);

    // create a network
    var container = document.getElementById('ruleSourceGraph');

    // provide the data in the vis format
    var data = {
        nodes: nodes,
        edges: edges
    };

    var options = {
        layout: {
            hierarchical: {
                direction: "LR",
                sortMethod: "directed",
                levelSeparation: 500
            },
        },
        physics: {
            hierarchicalRepulsion: {
                avoidOverlap: 1,
            },
        }
    };

    network = new vis.Network(container, data, options);

    network.on("click", function (params) {
        if (params.nodes[0] === "start" || params.nodes[0] === undefined) {
            return;
        }

        params.event = "[original event]";
        window.open("https://confluence.tools.telstra.com/display/MERGE/"+params.nodes[0], '_blank');
    });
}

function getAllRules() {
    let deferred = $.Deferred();
    let rules = [];

    queryApiWithPagination(deferred, rules, "/rules");

    return deferred.promise();
}

function getAllSources() {
    let deferred = $.Deferred();
    let sources = [];

    queryApiWithPagination(deferred, sources, "/sources");

    return deferred.promise();
}

function queryApiWithPagination(deferred, results, url) {
    $.ajax({
        cache: false,
        type: "GET",
        url: url,
        success: function(response) {
            results.push(...response.results);

            if (response.metadata && response.metadata.pagination && response.metadata.pagination.next) {
                queryApiWithPagination(deferred, results, response.metadata.pagination.next);
            } else {
                deferred.resolve(results);
            }
        },
        error: function(xhr, status, e) {
            // Returns current list of accumulated results on API failure
            console.error(`Error with obtaining data from ${url}`);
            deferred.reject(e);
        }
    });
}
</script>
</div>
</body>
</html>
