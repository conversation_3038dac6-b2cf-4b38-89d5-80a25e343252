import { TextStyle } from '@able/react'
import { faSortDown, faSortUp } from '@fortawesome/free-solid-svg-icons'
import { faCopy, faCheck, faDownload } from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import classNames from 'classnames'
import { useState } from 'react'
import { formatDate } from '../../../../../../helpers/formatDate'
import styles from './PaginatedTable.module.scss'

export interface PaginatedTableProps {
    data: Array<Record<string, unknown>>
    rowsPerPage?: number
}

export const PaginatedTable = ({
    data,
    rowsPerPage = 15,
}: PaginatedTableProps) => {
    const [hasCopied, setHasCopied] = useState<boolean>(false)
    // Pagination state
    const [currentPage, setCurrentPage] = useState<number>(1)
    // Sorting state: which column and whether ascending
    const [sortColumn, setSortColumn] = useState<string | null>(null)
    const [sortAsc, setSortAsc] = useState<boolean>(true)

    // Handler for clicking a header to sort by that column.
    const handleSort = (column: string) => {
        if (sortColumn === column) {
            setSortAsc(!sortAsc)
        } else {
            setSortColumn(column)
            setSortAsc(true)
        }
        setCurrentPage(1)
    }

    // Sort data based on the current sort settings.
    const sortedData = sortColumn
        ? data.slice().sort((a, b) => {
              const aVal = a[sortColumn]
              const bVal = b[sortColumn]
              const aStr = aVal != null ? String(aVal) : ''
              const bStr = bVal != null ? String(bVal) : ''

              // Simple regex to detect a date format like "YYYY-MM-DD" or "YYYY/MM/DD"
              const dateRegex = /^\d{4}[-/]\d{1,2}[-/]\d{1,2}/
              if (dateRegex.test(aStr) && dateRegex.test(bStr)) {
                  const aDate = new Date(aStr).getTime()
                  const bDate = new Date(bStr).getTime()
                  return sortAsc ? aDate - bDate : bDate - aDate
              } else {
                  return sortAsc
                      ? aStr.localeCompare(bStr)
                      : bStr.localeCompare(aStr)
              }
          })
        : data

    const totalPages = Math.ceil(sortedData.length / rowsPerPage)
    const startIndex = (currentPage - 1) * rowsPerPage
    const currentData = sortedData.slice(startIndex, startIndex + rowsPerPage)
    const columns =
        data.length > 0
            ? Object.keys(data[0]).filter((col) => col !== 'rowLink')
            : []

    const handlePrevPage = () => {
        setCurrentPage((prev) => Math.max(prev - 1, 1))
    }

    const handleNextPage = () => {
        setCurrentPage((prev) => Math.min(prev + 1, totalPages))
    }

    const handleCopyToClipboard = async () => {
        try {
            const formattedData = JSON.stringify(data, null, 2)
            await navigator.clipboard.writeText(formattedData)
            setHasCopied(true)
        } catch {
            setHasCopied(false)
        }
    }

    const handleDownload = () => {
        const headers = columns.join(',')

        // Map each rows data to match the columns
        const rows = data.map((row) =>
            columns.map((col) => {
                    const value = row[col]
                    return typeof value === 'string' || typeof value === 'number' ? `"${value}"` : '""'
                }).join(',')
        )

        const csvContent = [headers, ...rows].join('\n')

        const currentDate = new Date().toISOString().split('T')[0]
        const fileName = `${currentDate}_DMSLogData.csv`

        try {
            const element = document.createElement('a')
            const file = new Blob([csvContent], { type: 'text/csv' })
            element.href = URL.createObjectURL(file)
            element.download = fileName
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        } catch (error) {
            console.error('Error creating or downloading the file:', error)
        }
    }

    // Helper to render cell contents.
    const renderCellValue = (value: unknown): React.ReactNode => {
        if (value === null || value === undefined) return ''
        const strValue = String(value)

        // Check if the value looks like a date.
        const dateRegex = /^\d{4}[-/]\d{1,2}[-/]\d{1,2}/
        if (dateRegex.test(strValue)) {
            return formatDate(strValue)
        }

        return strValue
    }

    return (
        <div className={styles.paginatedTable}>
            <div className={styles.buttonGroup}>
                <button
                    className={styles.copyToClipboardButton}
                    onClick={handleCopyToClipboard}
                >
                    {!hasCopied ? (
                        <FontAwesomeIcon icon={faCopy} />
                    ) : (
                        <FontAwesomeIcon icon={faCheck} />
                    )}
                    <span>Copy</span>
                </button>
                <button
                    className={styles.downloadButton}
                    onClick={handleDownload}
                >
                    <FontAwesomeIcon icon={faDownload} />
                    <span>Download to CSV</span>
                </button>
            </div>
            <table>
                <thead>
                    <tr>
                        {columns.map((column) => (
                            <th key={column} onClick={() => handleSort(column)}>
                                <TextStyle alias="LabelA1">
                                    <div className={styles.headerCellContent}>
                                        <span>{column}</span>
                                        {sortColumn === column && (
                                            <FontAwesomeIcon
                                                className={classNames({
                                                    [styles.sortIconUp]: sortAsc,
                                                    [styles.sortIconDown]:
                                                        !sortAsc,
                                                })}
                                                icon={
                                                    sortAsc
                                                        ? faSortUp
                                                        : faSortDown
                                                }
                                            />
                                        )}
                                    </div>
                                </TextStyle>
                            </th>
                        ))}
                    </tr>
                </thead>
                <tbody>
                    {currentData.map((row, rowIndex) => {
                        const rowLink = row['rowLink']
                        return (
                            <tr
                                key={rowIndex}
                                onClick={() =>
                                    rowLink &&
                                    typeof rowLink === 'string' &&
                                    window.open(rowLink, '_blank')
                                }
                                className={classNames({
                                    [styles.clickableRow]: rowLink,
                                })}
                            >
                                {columns.map((col) => (
                                    <td key={col}>
                                        <TextStyle alias="FinePrintA">
                                            <>{renderCellValue(row[col])}</>
                                        </TextStyle>
                                    </td>
                                ))}
                            </tr>
                        )
                    })}
                    {currentData.length === 0 && (
                        <tr>
                            <td colSpan={columns.length}>No data available.</td>
                        </tr>
                    )}
                </tbody>
            </table>
            <div className={styles.pageActions}>
                <button onClick={handlePrevPage} disabled={currentPage === 1}>
                    <TextStyle alias="LabelA1">Prev</TextStyle>
                </button>
                <TextStyle>
                    <>
                        Page {currentPage} of {totalPages}
                    </>
                </TextStyle>
                <button
                    onClick={handleNextPage}
                    disabled={currentPage === totalPages}
                >
                    <TextStyle alias="LabelA1">Next</TextStyle>
                </button>
            </div>
        </div>
    )
}
