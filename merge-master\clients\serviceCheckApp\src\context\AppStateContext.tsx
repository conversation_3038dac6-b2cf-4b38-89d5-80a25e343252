import { createContext, ReactNode, useEffect, useState } from 'react'
import { useSearchParams } from 'react-router'
import { useGetServiceQuery } from '../api/queries/useGetServiceQuery'
import {
    AppState,
    InitialAppState,
    SelectedTestPackage,
    TestResultsModel,
} from '../infrastructure/models'

export interface AppStateContextValue {
    appState: AppState
    setAppState: React.Dispatch<React.SetStateAction<AppState>>
    refreshApp: () => Promise<void>
}

// eslint-disable-next-line react-refresh/only-export-components
export const AppStateContext = createContext<AppStateContextValue | undefined>(
    undefined
)

interface AppStateProviderProps {
    children: ReactNode
}

// TODO 
// Remove this before going production 


//Instructions for backend how to contruct the CDR log rowLink 
/*
1. Define the Input Fields and Constant Values
Each record provides the following input fields:

extTrackingId
_date
_start
_end
host
In addition, use these constant values:

action = "search4Splunk"
index = "tipt*-app-n"
count = "2500"
2. Build the Inner Parameter String

Create a mapping (or dictionary) where:
"regex" is mapped to the record’s extTrackingId
"date" is mapped to a URL‑encoded version of the record’s _date
"starttime" is mapped to a URL‑encoded version of the record’s _start
"stoptime" is mapped to a URL‑encoded version of the record’s _end
"host" is mapped to the record’s host
"action", "index", and "count" are set to their constant values (with "index" also URL‑encoded)
For example, the mapping becomes:

innerParams = {
  "action": "search4Splunk",
  "regex": <extTrackingId>,
  "date": URL_Encode(<_date>),
  "starttime": URL_Encode(<_start>),
  "stoptime": URL_Encode(<_end>),
  "host": <host>,
  "index": URL_Encode("tipt*-app-n"),
  "count": "2500"
}
Note: The first level of URL encoding is applied individually to _date, _start, _end, and "tipt*-app-n".

3. Create a Semicolon-Separated String

Convert the mapping to a string in the format:
key1=value1;key2=value2;...;keyN=valueN
For example, after encoding individual values, you might get:

action=search4Splunk;regex=<extTrackingId>;date=08%2F04%2F2025;starttime=14%3A00%3A00;stoptime=14%3A04%3A00;host=lxbas1004a;index=tipt%2A-app-n;count=2500
4. Perform a Full (Outer) URL Encoding of the Inner String

URL‑encode the entire inner string (this re-encodes any already encoded segments).
This transforms characters such as = to %3D and ; to %3B. For example, the inner string might become:
action%3Dsearch4Splunk%3Bregex%3D<extTrackingId>%3Bdate%3D08%252F04%252F2025%3Bstarttime%3D14%253A00%253A00%3Bstoptime%3D14%253A04%253A00%3Bhost%3Dlxbas1004a%3Bindex%3Dtipt%252A-app-n%3Bcount%3D2500
5. Construct the Final URL

Prepend your fixed base URL (for example, the login endpoint):
base_url = "http://pucq-tipt-01.networks.in.telstra.com.au/cgi-bin/xstools/loginLDAP.pl"
Append a query parameter named url whose value is the fully URL‑encoded inner string:
final_url = base_url + "?url=" + <outer_encoded_inner_string>
By following these language‑agnostic steps, you can construct the final URL that is then stored in the rowLink field of each JSON record. This method ensures that:

Each inner parameter value is encoded once.
The entire inner string is then encoded a second time.
The final URL is built by appending that double‑encoded string to the base URL as the url parameter.
This approach is universal and can be implemented similarly in any programming language that supports URL encoding and string manipulation.
*/

export const AppStateProvider = ({ children }: AppStateProviderProps) => {
    const [searchParams] = useSearchParams()
    const id = searchParams.get('id')
    const { refetch: getService } = useGetServiceQuery(id ?? '')
    const [state, setState] = useState<AppState>(InitialAppState)

    const refreshApp = async () => {
        if (id) {
            console.log('Refreshing app..')
            // Clear out current state and set loading flag
            const clearedOutState = {
                ...InitialAppState,
                id: state.id,
                serviceNumber: state.serviceDetails.productDetails.fnn ?? '',
                isLoadingService: true,
            } as AppState

            setState({ ...clearedOutState })

            const delay = new Promise((resolve) => setTimeout(resolve, 400))

            // Have a minimum delay so screen isn't too jolty
            const [response] = await Promise.all([getService(), delay])

            if (response.error) {
                setState({
                    ...InitialAppState,
                    isLoadingServiceError: true,
                    serviceNumber: 'Invalid',
                    serviceNumberErrorMessage: 'Service record does not exist.',
                })
                return
            }

            const updatedTestResults = response.data?.testResults?.map(
                (result) =>
                    ({
                        ...result,
                        status:
                            response.data.status === 'running'
                                ? 'loading'
                                : 'completed',
                    } as TestResultsModel)
            )

            const selectedTestPackages = response.data?.testPackages
                ? response.data?.testPackages.map(
                      (testPackage) =>
                          ({
                              name: testPackage.name,
                              title: testPackage.title,
                              isSelected: false,
                              hasDateRangeFilter:
                                  testPackage.hasDateRangeFilter,
                              parameters: testPackage.hasDateRangeFilter
                                  ? {
                                        startDate: new Date(
                                            Date.now() -
                                                (testPackage.defaultStartOffsetHours ??
                                                    0.5) *
                                                    60 *
                                                    60 *
                                                    1000
                                        ).toISOString(),
                                        endDate: new Date().toISOString(),
                                    }
                                  : null,
                          } as SelectedTestPackage)
                  )
                : []

            const mockCdrLogs = [
                {
                    rowLink:
                        'http://pucq-tipt-01.networks.in.telstra.com.au/cgi-bin/xstools/loginLDAP.pl?url=action%3Dsearch4Splunk%3Bregex%3Dcb2ec14d-ba9f-4b38-ade7-c1558e99cd6f%3Bdate%3D08%252F04%252F2025%3Bstarttime%3D14%253A00%253A00%3Bstoptime%3D14%253A04%253A00%3Bhost%3Dlxbas1004a%3Bindex%3Dtipt%252A-app-n%3Bcount%3D2500',
                    startTime: '2025-04-08 12:00:08.441',
                    answerTime: '2025-04-08 12:00:16.363',
                    releaseTime: '2025-04-08 12:03:32.764',
                    CLIPIndicr: 'Public',
                    direction: 'Originating',
                    callingNumber: '+61897224800',
                    calledNumber: '97526100',
                    dialedDigits: '97526100',
                    ans: 'Yes',
                    CCode: '016',
                    extTrackingId: 'cb2ec14d-ba9f-4b38-ade7-c1558e99cd6f',
                    callCat: 'private',
                    asCType: 'Network',
                    netCType: null,
                    callSecs: '204.323',
                    ringSecs: '7.922',
                    talkSecs: '196.401',
                    releasingParty: 'local',
                    codec: 'PCMA/8000/1',
                    trunkGroupName: 'N9703919R/N9703927R',
                    trunkGroupInfo: 'Normal',
                    accessCallID: '1859349504-106277253',
                    networkCallID: 'BW140008459080425-565186959@*************',
                    location: '<EMAIL>',
                    locationType: 'Primary Device',
                    userAgent: 'Mitel-3300-ICP 14.0.3.60',
                    enterprise: 'N9002432R',
                    group: 'N9703919R',
                    userId: '<EMAIL>',
                    userNumber: '+61897224800',
                    groupNumber: null,
                    netTransdNum: '+61897526100',
                    redirNumber: null,
                    redirReason: null,
                    configCLID: null,
                    btluExceeded: null,
                    entTrunkCapExceed: null,
                    transfer_invocTime: null,
                    transfer_result: null,
                    host: 'lxbas1004a',
                    _date: '08/04/2025',
                    _start: '14:00:00',
                    _end: '14:04:00',
                },
                {
                    rowLink:
                        'http://pucq-tipt-01.networks.in.telstra.com.au/cgi-bin/xstools/loginLDAP.pl?url=action%3Dsearch4Splunk%3Bregex%3D2a472741-15ec-4d21-be85-d1b57d84e83a%3Bdate%3D08%252F04%252F2025%3Bstarttime%3D14%253A14%253A00%3Bstoptime%3D14%253A17%253A00%3Bhost%3Dlxbas1004a%3Bindex%3Dtipt%252A-app-n%3Bcount%3D2500',
                    startTime: '2025-04-08 12:14:59.847',
                    answerTime: '2025-04-08 12:15:06.509',
                    releaseTime: '2025-04-08 12:16:01.428',
                    CLIPIndicr: 'Public',
                    direction: 'Originating',
                    callingNumber: '+61897224800',
                    calledNumber: '0428466027',
                    dialedDigits: '0428466027',
                    ans: 'Yes',
                    CCode: '016',
                    extTrackingId: '2a472741-15ec-4d21-be85-d1b57d84e83a',
                    callCat: 'national',
                    asCType: 'Network',
                    netCType: null,
                    callSecs: '61.581',
                    ringSecs: '6.662',
                    talkSecs: '54.919',
                    releasingParty: 'local',
                    codec: 'PCMA/8000',
                    trunkGroupName: 'N9703919R/N9703927R',
                    trunkGroupInfo: 'Normal',
                    accessCallID: '2750739504-106277303',
                    networkCallID: 'BW1414598680804251122805527@*************',
                    location: '<EMAIL>',
                    locationType: 'Primary Device',
                    userAgent: 'Mitel-3300-ICP 14.0.3.60',
                    enterprise: 'N9002432R',
                    group: 'N9703919R',
                    userId: '<EMAIL>',
                    userNumber: '+61897224800',
                    groupNumber: null,
                    netTransdNum: '+61428466027',
                    redirNumber: null,
                    redirReason: null,
                    configCLID: null,
                    btluExceeded: null,
                    entTrunkCapExceed: null,
                    transfer_invocTime: null,
                    transfer_result: null,
                    host: 'lxbas1004a',
                    _date: '08/04/2025',
                    _start: '14:14:00',
                    _end: '14:17:00',
                },
            ]

            const updatedState = response
                ? ({
                      ...InitialAppState,
                      ...state,
                      serviceNumber: response.data?.productDetails.fnn ?? '',
                      id: response.data?.id,
                      serviceDetails: response.data,
                      selectedTab: state.selectedTab ?? 'overview',
                      testResults: updatedTestResults,
                      testActions: [
                          ...(response.data?.testActions ?? []),
                          {
                              name: 'MTR104',
                              msg: 'Mock CDR Log',
                              result: 'Actioned',
                              updatedOn: new Date().toISOString(),
                              valueMsg: JSON.stringify({
                                  renderAsTable: true,
                                  data: mockCdrLogs,
                              }),
                              userInputs: [
                                  {
                                      type: 'daterange-locale',
                                      fieldNames: ['dateFrom', 'dateTo'],
                                  },
                              ],
                          },
                      ],
                      testPackages: response.data?.testPackages,
                      nextBestAction: response.data?.nextBestAction,
                      textTemplates: response.data?.textTemplates,
                      createdBy: response.data?.createdBy,
                      createdOn: response.data?.createdOn,
                      selectedTestPackages: selectedTestPackages,
                  } as AppState)
                : InitialAppState

            setState({ ...updatedState })
        }
    }

    useEffect(() => {
        refreshApp()
        // Caching refreshApp causes weird issues
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [id])

    useEffect(() => {
        let intervalId: ReturnType<typeof setInterval> | null = null

        if (state.serviceDetails && state.serviceDetails.status === 'running') {
            intervalId = setInterval(async () => {
                const response = await getService()
                if (response.error) {
                    return
                }

                if (response.data && response.data.status !== 'running') {
                    const updatedTestResults = response.data?.testResults?.map(
                        (result) =>
                            ({
                                ...result,
                                status: 'completed',
                            } as TestResultsModel)
                    )

                    setState(
                        (prevState) =>
                            ({
                                ...InitialAppState,
                                ...state,
                                serviceNumber:
                                    response.data?.productDetails.fnn ?? '',
                                id: response.data?.id,
                                serviceDetails: response.data,
                                testResults: updatedTestResults,
                                selectedTab: prevState.selectedTab,
                                selectedTestPackages:
                                    prevState.selectedTestPackages,
                                testPackages: response.data?.testPackages,
                                textTemplates: response.data?.textTemplates,
                                testActions: response.data?.testActions,
                                nextBestAction: response.data.nextBestAction,
                                createdBy: response.data.createdBy,
                                createdOn: response.data?.createdOn,
                            } as AppState)
                    )

                    if (intervalId) {
                        clearInterval(intervalId)
                    }
                }
            }, 3000)
        }

        return () => {
            if (intervalId) {
                clearInterval(intervalId)
            }
        }
    }, [state, getService])

    return (
        <AppStateContext.Provider
            value={{ appState: state, setAppState: setState, refreshApp }}
        >
            {children}
        </AppStateContext.Provider>
    )
}
