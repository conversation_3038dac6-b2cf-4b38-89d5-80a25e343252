import { ActionButton } from '@able/react'
import { useRetryServiceCheck } from '../../api/mutations/useRetryServiceCheck'
import { InformationPanel } from '../../components/informationPanel/InformationPanel'
import { useAppState } from '../../hooks/useAppState'

interface ServiceCheckStatusInformationPanelProps {
    className?: string
}

export const ServiceCheckStatusInformationPanel = ({
    className,
}: ServiceCheckStatusInformationPanelProps) => {
    const { appState, setAppState } = useAppState()
    const { mutate: retryServiceCheckMutate } = useRetryServiceCheck()

    const serviceCheckStatus = appState.serviceDetails.status

    let infoMessage = ''

    switch (serviceCheckStatus) {
        case 'error':
            infoMessage = 'Service check encountered an error'
            break
        case 'completedWithError':
            infoMessage = 'Service check completed with error'
            break
        case 'completedWithErrorPartial':
            infoMessage = 'Service check completed partially with error'
            break
        case 'abortedInitial':
            infoMessage = 'Service check was aborted initially'
            break
        default:
            infoMessage = ''
            break
    }

    const handleRetry = () => {
        if (!appState.id) return
        retryServiceCheckMutate(appState.id)
        setAppState({
            ...appState,
            serviceDetails: {
                ...appState.serviceDetails,
                status: 'running',
            },
        })
    }

    return infoMessage ? (
        <InformationPanel
            className={className}
            infoMessage={infoMessage}
            actions={
                <>
                    {serviceCheckStatus === 'abortedInitial' && (
                        <ActionButton
                            events={{
                                onClick: handleRetry,
                            }}
                            label="Retry service check"
                            developmentUrl="/public/images/able-sprites.svg"
                            variant={'MediumEmphasis'}
                            element={'button'}
                        />
                    )}
                </>
            }
        />
    ) : null
}
