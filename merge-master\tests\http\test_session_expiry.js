
import mongoose from 'mongoose';
import chai from 'chai';
import chaiHttp from 'chai-http';
import chai<PERSON>ike from 'chai-like';
const should = chai.should();
import superagentDefaults from 'superagent-defaults';
import supertest from 'supertest';
import _ from 'lodash';
import sinon from 'sinon';


var app;
var request;
var clock;
import config from '../config.js';
import helpers from '../helpers.js';
import { UNIT_TEST_ALL_ACCESS_USERNAME } from '../users.js';


chai.use(chaiLike);
chai.use(chaiHttp);


// Only tests that these endpoints can be accessed and return HTTP 200
describe('Merge Session Expiry', () => {
    // Setup and connect to mongodb memory server, start merge app instance
    // Set request variable with a configured supertest agent with test JWT and app instance
    before(async function() {
        global.gConfig = _.cloneDeep(config.default);
        app = (await import('../../app.js')).default;
        request = superagentDefaults(supertest(app));
    });

    // Drop all contents of database before each test
    beforeEach(async function() {
        global.gConfig = _.cloneDeep(config.default);
        // Uses deleteMany instead of mongoose.connection.db.dropDatabase() because
        // custom indexes appear to be deleted if dropDatabase() is used
        for (let collectionName in mongoose.connection.collections) {
            await mongoose.connection.collections[collectionName].deleteMany({});
        }
    });

    after(async function() {
        clock.restore();
    });

    it('Test session expiry (service check page)', async() => {
        await helpers.authenticateSession(request, UNIT_TEST_ALL_ACCESS_USERNAME);
        let res = await request.get('/serviceCheck');
        res.should.have.status(200);

        let pastExpiryDate = new Date();
        pastExpiryDate.setDate(pastExpiryDate.getDate() + 4);
        clock = sinon.useFakeTimers(pastExpiryDate);

        let res2 = await request.get('/serviceCheck');
        res2.should.have.status(302);
        res2.should.redirectTo('/auth/login');
    });
});