
import dedent from 'dedent';
import _ from 'lodash';

import { serviceCheckHistoryByFnn } from './serviceCheck.js';
import Sdwan from '../db/model/sdwan.js';
import { calculateStartDate } from './helpers/date.js';


async function getServiceCheckHistoryByFnn({ fnn, suite, limit, offset, last }) {
    if (typeof fnn !== 'string') {
        throw new TypeError('fnn must be a string');
    }

    // Obtains last 2 months of service checks
    let startDate = calculateStartDate(last);

    let [serviceChecks, count] = await serviceCheckHistoryByFnn(fnn, suite, limit, offset, startDate);

    return {
        metadata: {
            pagination: {
                limit: limit,
                offset: offset,
                total: count
            },
            startDate: startDate
        },
        results: serviceChecks
    };
}


async function getSDWANDocument({ deviceName, fnn }) {
    if (!fnn && !deviceName) {
        return null;
    }

    let sdwanSearchFilter;

    if (fnn) {
        sdwanSearchFilter = { sdwanFnn: fnn };
    } else if (deviceName) {
        sdwanSearchFilter = { deviceName: deviceName };
    }

    let sdwanDocuments = await Sdwan.find(sdwanSearchFilter, { _id: 0, createdAt: 0, updatedAt: 0 }).sort({ deviceTimestamp: -1 }).limit(1);
    if (sdwanDocuments.length) {
        return refineData(sdwanDocuments[0].toJSON());
    } else {
        // Returns object to match format of API response from previous implementation
        return {
            message: `SDWAN document for FNN / device name ${fnn ? fnn : deviceName} not found`
        };
    }
}


// Helper function for getSDWANDocument()
function refineData(sdwanData) {
    // if imsi is empty then delete the row, otherwise print present imsi
    if (sdwanData.imsi == null) {
        delete sdwanData.imsi;
    }

    // Convert every date into AEST timezone
    sdwanData.deviceUptimeDate = new Date(sdwanData.deviceUptimeDate).toLocaleString('en-AU', { timeZone: 'Australia/Sydney' });
    sdwanData.deviceTimestamp = new Date(sdwanData.deviceTimestamp).toLocaleString('en-AU', { timeZone: 'Australia/Sydney' });
    sdwanData.interfaceData.forEach(function(element) {
        element.interfaceTimestamp = new Date(element.interfaceTimestamp).toLocaleString('en-AU', { timeZone: 'Australia/Sydney' });
    }.bind(this));

    // sort all the keys in lexicographical order
    let sortedKeys = Object.keys(sdwanData).sort();
    let sdwanDataSortedKeys = {};
    for (let i = 0; i < sortedKeys.length; i++) {
        sdwanDataSortedKeys[sortedKeys[i]] = sdwanData[sortedKeys[i]];
    }

    return sdwanDataSortedKeys;
}


export default {
    getSDWANDocument,
    getServiceCheckHistoryByFnn
};
