/**
 * Note: This migration file was created when 'migrate-mongoose' was the npm package
 * used to perform MongoDB migrations, since the 'migrate' npm package is now used
 * the implementation may not be compatible and all functionality is commented out
 */

// const mongoose = require('mongoose');

// const migrate = require('./migrate');

// const serviceCheckModel = new mongoose.Schema({
//     id: { type: String, index: true, immutable: true },
//     fnn: { type: String, index: true },
//     phoneNumber: { type: String, index: true, default: null },
//     billingFNN: String,
//     carriageFNN: String,
//     carriageFNNBackup: String,
//     MDNFNN: String,
//     carriageType: String,
//     nbnServiceType: String,
//     nbnAccessType: String,
//     nbnSubAccessType: String,
//     deviceName: String,
//     CIDN: String,
//     address: String,
//     OffshoreResources: String,
//     status: String,
//     createdBy: { type: String, required: true, immutable: true, default: "unknown", index: true },
//     createdOn: { type: Date, required: true, immutable: true, default: Date.now, index: true },
//     endedOn: Date,
//     durationMilliSec: Number,
//     input: {
//         searchFNN: String,
//         carriageType: String,
//         carriageFNN: String,
//         deviceName: String,
//         deviceIP: String,
//         level: Number,
//         process: String,
//         suite : String,
//         idType : String
//     },
//     additionalParameters: Object,
//     rulesData: Object,
//     sourcesData: Object,
//     sourcesMetadata: Object,
//     saveError: String
// }, { minimize: false });

// const ServiceCheckModel = mongoose.model('servicechecks', serviceCheckModel);


/**
 * Make any changes you need to make to the database here
 */
async function up () {
    // Write migration here
    // await migrate.connect();
    // for await (const record of ServiceCheckModel.find({})) {
    //     for (var source in record.sourcesMetadata) {
    //         if (source.indexOf("InRequest-") === 0) {
    //             // Transfers the status from metadata back to sources data if it was not 'Collected'
    //             if (record.sourcesData[source] &&
    //                 record.sourcesData[source].status === undefined &&
    //                 record.sourcesMetadata[source].status !== undefined &&
    //                 record.sourcesMetadata[source].status !== 'Collected') {
    //                 record.sourcesData[source].status = record.sourcesMetadata[source].status;
    //             }
    //             delete record.sourcesMetadata[source];
    //         }
    //     }

    //     record.markModified('sourcesData');
    //     record.markModified('sourcesMetadata');

    //     await record.save();
    // }
}

/**
 * Make any changes that UNDO the up function side effects here (if possible)
 */
async function down () {
    // No reverse migration, as source metadata for sources starting with
    // InRequest- are removed
}

module.exports = { up, down };
