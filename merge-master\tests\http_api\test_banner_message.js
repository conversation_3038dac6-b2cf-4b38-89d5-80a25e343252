'use strict';

import mongoose from 'mongoose';
import chai from 'chai';
import chaiHttp from 'chai-http';
import chaiLike from 'chai-like';
import chaiDateTime from 'chai-datetime';
import superagentDefaults from 'superagent-defaults';
import supertest from 'supertest';
import cookie from 'cookie';
import _ from 'lodash';
import assert from 'assert';

var app;
var request;
import config from '../config.js';
import BannerMessage from '../../db/model/bannerMessage.js';
import helpers from '../helpers.js';
import { UNIT_TEST_ALL_ACCESS_USERNAME, UNIT_TEST_USERS } from '../users.js';

const should = chai.should();

chaiLike.extend(helpers.dateFieldCheck);
chaiLike.extend(helpers.regexCheck);
chai.use(chaiLike);
chai.use(chaiHttp);
chai.use(chaiDateTime);

const BANNER_MESSAGE_KEYS = Object.freeze([
    'message',
    'updatedBy',
    'updatedOn'
]);

describe('Merge banner message endpoints', () => {
    // Setup and connect to mongodb memory server, start merge app instance
    // Set request variable with a configured supertest agent with test JWT and app instance
    before(async function() {
        global.gConfig = _.cloneDeep(config.default);
        app = (await import('../../app.js')).default;
        request = superagentDefaults(supertest(app));
    });

    // Drop all contents of database before each test
    beforeEach(async function() {
        global.gConfig = _.cloneDeep(config.default);

        // Uses deleteMany instead of mongoose.connection.db.dropDatabase() because
        // custom indexes appear to be deleted if dropDatabase() is used
        for (let collectionName in mongoose.connection.collections) {
            await mongoose.connection.collections[collectionName].deleteMany({});
        }

        await helpers.authenticateSession(request, UNIT_TEST_ALL_ACCESS_USERNAME);
    });

    describe('Read banner message', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/bannerMessage');

            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/bannerMessage');

                switch(username) {
                    case 'admin':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('No banner message', async() => {
            let res = await request.get('/bannerMessage');

            res.should.have.status(200);
            chai.expect(res.body).to.be.eql({});
        });

        it('Banner message exists', async() => {
            let currDate = new Date();
            await new BannerMessage({
                message: 'Test message string',
                updatedBy: 'username',
                updatedOn: currDate
            }).save();

            let res = await request.get('/bannerMessage');

            res.should.have.status(200);
            chai.expect(res.body).to.be.like({
                message: 'Test message string',
                updatedBy: 'username',
                updatedOn: currDate
            });

            chai.expect(res.body).to.have.all.keys(BANNER_MESSAGE_KEYS);
        });
    });

    describe('Update banner message', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.put('/bannerMessage');

            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.put('/bannerMessage');

                switch(username) {
                    case 'admin':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Update message with no message field' , async() => {
            let res = await request.put('/bannerMessage');

            res.should.have.status(200);
            chai.expect(res.body).to.be.like({
                message: '',
                updatedBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                updatedOn: new Date()
            });
            chai.expect((await BannerMessage.findOne({})).toJSON()).to.be.like({
                message: '',
                updatedBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                updatedOn: new Date()
            });
        });

        it('Update message with empty message field', async() => {
            let res = await request.put('/bannerMessage').send({
                message: ''
            });

            res.should.have.status(200);
            chai.expect(res.body).to.be.like({
                message: '',
                updatedBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                updatedOn: new Date()
            });
            chai.expect((await BannerMessage.findOne({})).toJSON()).to.be.like({
                message: '',
                updatedBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                updatedOn: new Date()
            });
        });

        it('Update message with non-empty message field', async() => {
            let res = await request.put('/bannerMessage').send({
                message: 'test message edit'
            });

            res.should.have.status(200);
            chai.expect(res.body).to.be.like({
                message: 'test message edit',
                updatedBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                updatedOn: new Date()
            });
            chai.expect((await BannerMessage.findOne({})).toJSON()).to.be.like({
                message: 'test message edit',
                updatedBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                updatedOn: new Date()
            });
        });

        it('Update message with invalid message type (integer)', async() => {
            let res = await request.put('/bannerMessage').send({
                message: 200
            });

            res.should.have.status(400);
            chai.expect(await BannerMessage.findOne({})).to.equal(null);
        });

        it('Update message with invalid banner message type (object)', async() => {
            let res = await request.put('/bannerMessage').send({
                message: { value: 'message' }
            });

            res.should.have.status(400);
            chai.expect(await BannerMessage.findOne({})).to.equal(null);
        });

        it('Update message test updatedBy and updatedOn immutable', async() => {
            let res = await request.put('/bannerMessage').send({
                message: 'test message edit',
                updatedBy: 'anotheruser',
                updatedOn: new Date("2000-01-01T00:00:00Z")
            });

            res.should.have.status(200);
            chai.expect(res.body).to.be.like({
                message: 'test message edit',
                updatedBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                updatedOn: new Date()
            });
            chai.expect((await BannerMessage.findOne({})).toJSON()).to.be.like({
                message: 'test message edit',
                updatedBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                updatedOn: new Date()
            });
        });

        it('Update message test character limit', async() => {
            let longMessage = '';
            for (let i = 0; i < 10000; i++) {
                longMessage += 'a';
            }

            let res = await request.put('/bannerMessage').send({
                message: longMessage,
                updatedBy: 'anotheruser',
                updatedOn: new Date("2000-01-01T00:00:00Z")
            });

            res.should.have.status(400);
            chai.expect(await BannerMessage.findOne({})).to.equal(null);
        });

        it('Update existing message', async() => {
            await new BannerMessage({
                message: 'first message',
                updatedBy: 'first user',
                updatedOn: new Date("2020-01-01T00:00:00Z")
            }).save();

            let res = await request.put('/bannerMessage').send({
                message: 'second message'
            });

            res.should.have.status(200);
            chai.expect(res.body).to.be.like({
                message: 'second message',
                updatedBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                updatedOn: new Date()
            });
            chai.expect((await BannerMessage.findOne({})).toJSON()).to.be.like({
                message: 'second message',
                updatedBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                updatedOn: new Date()
            });
        });

        it('Update existing message, invalid request', async() => {
            await new BannerMessage({
                message: 'first message',
                updatedBy: 'first user',
                updatedOn: new Date("2020-01-01T00:00:00Z")
            }).save();

            let res = await request.put('/bannerMessage').send({
                message: false
            });

            res.should.have.status(400);
            chai.expect((await BannerMessage.findOne({})).toJSON()).to.be.like({
                message: 'first message',
                updatedBy: 'first user',
                updatedOn: new Date("2020-01-01T00:00:00Z")
            });
        });
    });
});