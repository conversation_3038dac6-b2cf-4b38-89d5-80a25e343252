
import fs from 'fs';
import mysql from 'mysql2/promise';

import '../config/config.js';


const poolOptions = {
    host: global.gConfig.hangarDb.hostname,
    user: global.gConfig.hangarDb.username,
    password: global.gConfig.hangarDb.password,
    database: global.gConfig.hangarDb.database,
    waitForConnections: true,
    connectionLimit: 20
};

// Use SSL if caCert is specified in config
if (global.gConfig.hangarDb.caCert) {
    poolOptions.ssl = {
        ca: await fs.promises.readFile(global.gConfig.hangarDb.caCert),
        cert: await fs.promises.readFile(global.gConfig.hangarDb.clientCert),
        key: await fs.promises.readFile(global.gConfig.hangarDb.clientKey)
    };
}

const pool = mysql.createPool(poolOptions);

export default pool;
