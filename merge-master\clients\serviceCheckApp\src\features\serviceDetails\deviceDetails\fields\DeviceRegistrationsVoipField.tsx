import { CollapsablePanel } from '../../../../components/collapsablePanel/CollapsablePanel'
import { DetailField } from '../../../../components/detailsPanel/DetailField'
import { Panel } from '../../../../components/panel/Panel'
import { DeviceRegistration } from '../../../../infrastructure/models'
import styles from './DeviceRegistrationsVoipField.module.scss'

interface DeviceRegistrationFieldProps {
    value: DeviceRegistration[] | null
}

export const DeviceRegistrationsVoipField = ({
    value,
}: DeviceRegistrationFieldProps) => {
    if (value === null || value === undefined) {
        return null
    }

    if (value.length === 0) {
        return (
            <Panel>
                <DetailField label="Registrations" value="None" />
            </Panel>
        )
    }

    return (
        <CollapsablePanel
            headerElement={<DetailField label="Registrations" value="" />}
            canOpen={value.length > 0}
            itemCount={value.length}
        >
            <div className={styles.deviceRegistrationsVoip}>
                <div className={styles.registrationRow}>
                    {value.map((registration, index) => (
                        <div key={`${registration.device}-${index}`}>
                            <DetailField
                                label="User"
                                value={registration.user ?? null}
                                inline={true}
                            />
                            <DetailField
                                label="RegType"
                                value={registration.regType ?? null}
                                inline={true}
                            />
                            <DetailField
                                label="Device"
                                value={registration.device ?? null}
                                inline={true}
                            />
                            <DetailField
                                label="Device"
                                value={registration.deviceBak ?? null}
                                inline={true}
                            />
                            <DetailField
                                label="Line Port"
                                value={registration.linePort ?? null}
                                inline={true}
                            />
                            <DetailField
                                label="Expires"
                                value={registration.expires ?? null}
                                inline={true}
                            />
                            <DetailField
                                label="Expires"
                                value={registration.expiresBak ?? null}
                                inline={true}
                            />
                            <DetailField
                                label="User Agent"
                                value={registration.userAgent ?? null}
                                inline={true}
                            />
                            <DetailField
                                label="SBC"
                                value={registration.SBC ?? null}
                                inline={true}
                            />
                            <DetailField
                                label="Call Info"
                                value={registration.callInfo ?? null}
                                inline={true}
                            />
                            <DetailField
                                label="Trunk FNN"
                                value={registration.trunkFnn ?? null}
                                inline={true}
                            />
                        </div>
                    ))}
                </div>
            </div>
        </CollapsablePanel>
    )
}
