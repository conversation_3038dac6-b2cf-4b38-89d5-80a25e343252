'use strict';

import express from 'express';
import { body, validationResult } from 'express-validator';

import auth from '../modules/auth.js';
import { AuthorizationRoles } from '../modules/enumerations.js';
import logger from '../modules/logger.js';
import BannerMessage from '../db/model/bannerMessage.js';

const router = express.Router();

router.get('/', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], false), async function(req, res) {
    try {
        let bannerMessage = await BannerMessage.findOne({});

        if (bannerMessage) {
            res.send(bannerMessage.toJSON());
        } else {
            res.send({});
        }
    } catch(error) {
        res.status(500).send(`Could not obtain banner message, ${error.toString()}`);
    }
});

router.put('/', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], false), [
    body('message').default('').isString().isLength({ max: 500 })
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    try {
        let message = req.body.message;
        let bannerMessage = await BannerMessage.findOneAndUpdate(
            {},
            { $set: { message: message, updatedBy: req.user.username, updatedOn: new Date() } },
            {
                upsert: true,
                runValidators: true,
                strict: 'throw',
                new: true,
                useFindAndModify: false
            }
        );

        if (bannerMessage) {
            // Updates the bannerMessage variable in templates here, as MongoDB instances
            // that are not replica sets will not have a listener set up on the bannermessage collection

            // If there is a case where Merge is run in an environment which consists of multiple nodes
            // connected to a single non-replicaset MongoDB instance, all nodes except the one the banner update
            // was performed on will not receive the new message (until the application restarts),
            // this will need to be fixed, if this condition occurs
            req.app.locals.bannerMessage = bannerMessage.message;
            res.send(bannerMessage.toJSON());
        } else {
            res.sendStatus(422);
        }
    } catch(error) {
        if (error.name == 'ValidationError') {
            res.status(400).send({
                error: error.toString(),
                validation: error.errors
            });
        } else if (error.name == 'CastError') {
            res.status(400).send({
                error: error.toString()
            });
        } else {
            logger.error(`Update banner message: unexpected error, ${error.toString()}`);
            res.status(500).send({
                error: error.toString()
            });
        }
    }
});


export default router;
