
import { fork } from 'child_process';
import ivm from 'isolated-vm';
import _ from 'lodash';

import { InputTypes, ProductTypes } from './enumerations.js';
import { NotFoundError } from './error.js';
import logger from './logger.js';
import Template from '../db/model/template.js';


// Hardcoded list of templates which can only bet viewed by users who are
// a part of Enterprise Network Management (or dev / admin in Merge)
const ENTERPRISE_NETWORK_MANGEMENT_TEMPLATE_NAMES = Object.freeze([
    'IPMANSummaryText',
    'MDNFieldTask',
    'TelstraFieldTask'
]);


// Hardcoded list of Service Central robot IDs which should be able to view
// the templates in ENTERPRISE_NETWORK_MANGEMENT_TEMPLATE_NAMES
const ROBOT_USER_ACCOUNT_NAME_WHITELIST = Object.freeze([
    'n113080',
    'n113081'
]);


async function renderTemplateByName(serviceCheckRecord, templateName, showRulesData, user) {
    let template = await Template.findOne({
        name: templateName,
        active: true,
        templateType: "Service Check"
    });

    if (!template) {
        throw new NotFoundError(`Template ${templateName} does not exist or is not active or is on the incorrect suite`);
    }

    // Hardcoded condition for the IPMAN summary text template, if the user is not allowed to access the template,
    // treat it as not found
    if (ENTERPRISE_NETWORK_MANGEMENT_TEMPLATE_NAMES.includes(template.name) && !canAccessIpmanTemplate(user)) {
        throw new NotFoundError(`Template ${templateName} does not exist or is not active or is on the incorrect suite`);
    }

    return await renderTemplate(serviceCheckRecord, template.template, template.formatType, showRulesData, true);
}


async function renderTemplate(serviceCheckRecord, templateCode, formatType, showRulesData, enableTemplateModules, templateParameters=null) {
    if (!_.isPlainObject(serviceCheckRecord)) {
        throw new TypeError('Service check record should be a plain object');
    }

    const timeout = 5000;

    let moduleTemplates = await Template.find({
        active: true,
        templateType: "Module"
    },
    {
        name: true,
        template: true
    });

    return new Promise((resolve, reject) => {
        let child = fork('./bin/render_template', null, {
            timeout: timeout
        });

        // Uses prod URL by default if it is not in the config file
        let mergeUrl = _.get(global, ['gConfig', 'APIUriConfig', 'MERGE', 'baseURI'], 'https://merge.in.telstra.com.au');

        child.send({
            templateCode: templateCode,
            moduleTemplates: moduleTemplates,
            serviceCheckRecord: serviceCheckRecord,
            showRulesData: showRulesData,
            mergeUrl: mergeUrl,
            enableTemplateModules: enableTemplateModules,
            templateParameters: templateParameters
        });

        child.on('message', (message) => {
            if (message.error) {
                // Receives error from child process as a string as it is not an instance of Error
                let error = new Error(message.error);
                reject(error);
                return;
            }

            let text = message.text;

            if (typeof text !== 'string') {
                reject(new TypeError('Return value of template rendering was not a string'));
                return;
            }

            resolve(text);
        });

        child.on('exit', (code, signal) => {
            // SIGTERM should only be received if there is a timeout when running the child script
            if (signal === 'SIGTERM') {
                reject(new Error(`Template rendering terminated, timeout of ${timeout}ms exceeded`));
            }
        });
    });
}


async function listActiveTemplatesServiceCheck(record, user) {
    let textTemplates = await Template.find({
        active: true,
        templateType: "Service Check"
    }).sort({ name: 1 }).collation({ locale: 'en' });

    const isolate = new ivm.Isolate({
        memoryLimit: 64
    });

    let textTemplatesMatched = [];
    try {
        for (let template of textTemplates) {
            // Currently a hardcoded condition to ensure the template with the name "IPMANSummaryText" is visible
            // only to users within the Enterprise Managed Networks business unit
            if (ENTERPRISE_NETWORK_MANGEMENT_TEMPLATE_NAMES.includes(template.name) && !canAccessIpmanTemplate(user)) {
                continue;
            }

            if (!template.listCondition) {
                textTemplatesMatched.push(template);
            } else {
                let templateListConditionContext = await isolate.createContext();

                try {
                    await templateListConditionContext.global.set('data', new ivm.ExternalCopy(record.toJSON()).copyInto());
                    await templateListConditionContext.global.set('r', templateListConditionContext.global.getSync('data').getSync('rulesData').derefInto());
                    await templateListConditionContext.global.set('s', templateListConditionContext.global.getSync('data').getSync('sourcesData').derefInto());
                    await templateListConditionContext.global.set('InputTypes', new ivm.ExternalCopy(InputTypes).copyInto());
                    await templateListConditionContext.global.set('ProductTypes', new ivm.ExternalCopy(ProductTypes).copyInto());

                    let listConditionResult = await templateListConditionContext.eval(template.listCondition, { timeout: 1000 });

                    if (listConditionResult) {
                        textTemplatesMatched.push(template);
                    }
                } catch(error) {
                    logger.warn(`Code error for listCondition in ${template.name}, ${error.toString()}`);
                } finally {
                    templateListConditionContext.release();
                }
            }
        }
    } finally {
        if (!isolate.isDisposed) {
            isolate.dispose();
        }
    }

    textTemplatesMatched = textTemplatesMatched.map((template) => {
        return _.pick(template, [
            'name',
            'title',
            'description',
            'defaultPriority',
            'allowedSystemsToAppend'
        ]);
    });

    return textTemplatesMatched;
}


function canAccessIpmanTemplate(user) {
    if (!user) {
        return false;
    }

    const BUSINESS_UNIT_ENTERPRISE_NETWORK_MANGEMENT = 'AU\\Telstra Group\\Telstra\\Global Networks and Technology\\Commercial Engineering\\Enterprise Service Management\\Enterprise Network Management';

    return (
        (user.isDeveloper || user.isAdmin) ||
        (typeof user.businessUnit === 'string' && user.businessUnit.startsWith(BUSINESS_UNIT_ENTERPRISE_NETWORK_MANGEMENT)) ||
        (ROBOT_USER_ACCOUNT_NAME_WHITELIST.includes(user.username))
    );
}


async function listActiveTemplates() {
    let templates = await Template.find({
        active: true,
        templateType: "Service Check"
    },
    {
        name: 1,
        description: 1,
        title: 1,
        allowedSystemsToAppend: 1,
        defaultPriority: 1
    }).sort({ name: 1 }).collation({ locale: 'en' });

    return templates;
}


export default {
    renderTemplateByName: renderTemplateByName,
    renderTemplate: renderTemplate,
    listActiveTemplatesServiceCheck: listActiveTemplatesServiceCheck,
    listActiveTemplates: listActiveTemplates
};
