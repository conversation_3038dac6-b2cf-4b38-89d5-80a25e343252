module.exports = { runDBCronJob : runDBCronJob };

const debugMsg = require('debug')('merge:DBBackUp');
var nodemailer = require('nodemailer');
var backup = require('mongodb-backup');
const compressing = require('compressing');
const splitFile = require('split-file');
 
const fs = require('fs');
const cron = require('cron');

const logger = require('../modules/logger');

var environment = global && global.gConfig ? global.gConfig.env : 'localDev';

let dbReplicaSet = global.gConfig.dbReplicaSet ? `?replicaSet=${global.gConfig.dbReplicaSet}&authSource=${global.gConfig.dbName}` : `?authSource=${global.gConfig.dbName}`;
let dbUseSsl =  global.gConfig.dbUseSsl;
let sslValidate = global.gConfig.dbUseSsl;
let dbCaCert = null;
if (global.gConfig.dbCaCert) {
    try {
        dbCaCert = fs.readFileSync( global.gConfig.dbCaCert);
    } catch (err) {
        console.log(`Could not read CA certificate for mongodb: ${err}`);
        process.exit(1);
    }
}

if (!environment) {
    console.log(`Could not find environment in config: ${environment}`);
    process.exit(1);
}

let dbCredentials = global.gConfig.dbUser ? `${global.gConfig.dbUser}:${global.gConfig.dbPass}@` : ''

function runDBCronJob() {
    debugMsg('DB backup: start backup');
    logger.info('DB Back up cron: start backup');
    
    try {
        let cronJob = cron.job(global.gConfig.backUpMail.cron, async function() {
            try {
                backup({
                    root: '/var/backup/db',
                    options:{useNewUrlParser: true,
                    useCreateIndex: true,
                    useUnifiedTopology: true,
                    ssl: dbUseSsl,
                    sslValidate: true,
                    sslCA: dbCaCert,
                    checkServerIdentity:false},
                    //tar: filename,
                    callback: function(err) {
                        if (err) {
                            console.error(err);
                        } else {
                            console.log('DB Back up file created successfully');
                            try {
                                // compress a file
                                compressing.tgz.compressDir('/var/backup/db/'+global.gConfig.dbName, '/var/backup/db/'+filename+'.tgz')
                                .then((data)=> {
                                    console.log('Compression of DB backup Successful');
                                    sendMail()
                                }).catch((err)=> console.log(err));
                                
                                //send mail
                                async function sendMail() {
                                    if(err) console.log(err);

                                    let transporter = nodemailer.createTransport({
                                        host: global.gConfig.backUpMail.smtpHost, 
                                        port: global.gConfig.backUpMail.port,
                                        use_authentication: false, 
                                        use_tls: true,
                                        user: null, 
                                        pass: null,
                                        secure: false
                                    });

                                    //split into multiple files  to send as email
                                    splitFile.splitFileBySize('/var/backup/db/'+filename+'.tgz', 6500000).then(async (names) => {
                                        //console.log(names);
                                        for(var i=0; i<names.length;i++) {
                                            let info = await transporter.sendMail({       
                                                sender: global.gConfig.backUpMail.sender,
                                                to: global.gConfig.backUpMail.to,
                                                subject: 'Mongo db backup from '+environment,
                                                text: 'Mongo db backup Part '+i+' of '+names.length,
                                                attachments: [{'filename': filename+'-'+i+'.tgz', 'content': names[i]}]
                                            });
                                            console.log("Message sent with DB back up attachment successfully: %s", info.messageId);
                                            fs.unlinkSync(names[i]);
                                        }
                                        fs.unlinkSync('/var/backup/db/'+filename+'.tgz');
                                        fs.rmSync('/var/backup/db/'+global.gConfig.dbName, {force: true, recursive: true});
                                    }).catch((err) => {
                                        console.error('Error: ', err);
                                    });
                                }
                            } catch (err) { console.error(err) };
                        }
                    }
                  });
            } catch(error) {
                debugMsg(`!> Error when trying to back the db, ${error.toString()}`);
                console.error(`Error when trying to back the db, ${error.toString()}`);
            }
	    });
        cronJob.start();
        console.info(`DB cron job started: started cron job for DB back up`);
    } catch(error) {
        console.error(`Error when trying to back the db: error when starting cron job for db back up, ${error.toString()}`);
    }
}
