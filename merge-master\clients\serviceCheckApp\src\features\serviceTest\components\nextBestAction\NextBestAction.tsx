import { TextStyle } from '@able/react'
import { DetailsPanel } from '../../../../components/detailsPanel/DetailsPanel'
import { FunctionNotReady } from '../../../../components/functionNotReady/FunctionNotReady'
import { useAppState } from '../../../../hooks/useAppState'
import { LoadingPanel } from '../serviceCheckLoadingPanel/LoadingPanel'

export const NextBestAction = () => {
    const { appState } = useAppState()
    const serviceTestStatus = appState.serviceDetails.status

    const nextBestActionContent = (() => {
        if (serviceTestStatus === 'running') {
            return <LoadingPanel />
        } else if (
            serviceTestStatus !== 'completedWithError' &&
            serviceTestStatus !== 'done'
        ) {
            return <FunctionNotReady />
        } else {
            return (
                <div>
                    <TextStyle>
                        {appState.nextBestAction ??
                            'Next Best Action is not available.'}
                    </TextStyle>
                </div>
            )
        }
    })()

    return (
        <DetailsPanel label="Next Best Action">
            {nextBestActionContent}
        </DetailsPanel>
    )
}
