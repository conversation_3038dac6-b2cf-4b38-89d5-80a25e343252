import mongoose from 'mongoose';

import MessageBucket from './messageBucket.js';


const outcomeSchema = new mongoose.Schema({
    name: { type: String, minLength: 1, default: null, unique: true, required: true, immutable: true },
    title: { type: String, default: '' },
    description: { type: String, default: '' },
    header: { type: String, default: '' },
    display: { type: Boolean, default: false },
    tier: { type: Number, default: 7, min: 0, max: 7, validate: { validator: Number.isInteger }},
    weight: { type: Number, default: 0, min: 0, validate: { validator: Number.isInteger }},
    messageBucketName: { type: String, default: null },
    createdBy: { type: String, required: true, immutable: true, default: 'unknown' },
    createdOn: { type: Date, required: true, immutable: true, default: Date.now }
}, {
    autoIndex: true
});


outcomeSchema.virtual('messageBucket', {
    ref: MessageBucket,
    localField: 'messageBucketName',
    foreignField: 'name',
    justOne: true
});


outcomeSchema.methods.toJSON = function() {
    let obj = this.toObject();
    delete obj._id;
    delete obj.__v;
    return obj;
}


export default mongoose.model('outcome', outcomeSchema);
