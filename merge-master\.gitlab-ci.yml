variables:
  GIT_DEPTH: 10

stages:
  - build-dependencies
  - test
  - build
  - deploy
  - load-config


# Commits made by the project bot only make configuration changes (APIs / rules / sources / text templates)
# and will not need this stage to build nodejs dependencies
.build_dependencies_template: &build_dependencies_template
  rules:
    - if: $CI_COMMIT_REF_NAME == $REF_NAME
      when: manual
  stage: build-dependencies
  before_script:
    - apk add --update curl jq
  script:
    - |
      CI_LAST_SUCCESSFUL_PIPELINE_SHA=$(curl -H "Authorization: Bearer ${GIT_API_READ_TOKEN}" ${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/pipelines?ref=${REF_NAME} | jq -r -c 'map(select(.status=="success")) | first | .["sha"]')
    - |
      if [ "$CI_LAST_SUCCESSFUL_PIPELINE_SHA" = "null" ]; then
        CI_LAST_SUCCESSFUL_PIPELINE_SHA="@~..@"
      fi;
    - GIT_DIFF_PACKAGE=$(git diff -G '^\s{4}".*":\s?"[\^~]?[0-9]+.*"' ${CI_LAST_SUCCESSFUL_PIPELINE_SHA} -- package.json || true)
    - |
      if [[ ! -z "$GIT_DIFF_PACKAGE" || ! -z "$OVERRIDE_RUN_BUILD_DEPENDENCIES" ]]; then
        docker build --build-arg HTTP_PROXY=${HTTP_PROXY} --build-arg HTTPS_PROXY=${HTTPS_PROXY} -t ${DOCKER_REGISTRY_REPO}/${DOCKER_DEPENDENCIES_IMAGE}:${CI_COMMIT_SHA} -f Dockerfile.node-dependencies .
        docker tag ${DOCKER_REGISTRY_REPO}/${DOCKER_DEPENDENCIES_IMAGE}:${CI_COMMIT_SHA} ${DOCKER_REGISTRY_REPO}/${DOCKER_DEPENDENCIES_IMAGE}:latest
        echo ${DOCKER_BUILD_PASSWORD} | docker login --username ${DOCKER_BUILD_USERNAME} --password-stdin ${DOCKER_REGISTRY_REPO}
        docker push ${DOCKER_REGISTRY_REPO}/${DOCKER_DEPENDENCIES_IMAGE}:${CI_COMMIT_SHA}
        docker push ${DOCKER_REGISTRY_REPO}/${DOCKER_DEPENDENCIES_IMAGE}:latest
        docker rmi ${DOCKER_REGISTRY_REPO}/${DOCKER_DEPENDENCIES_IMAGE}:${CI_COMMIT_SHA}
        docker rmi ${DOCKER_REGISTRY_REPO}/${DOCKER_DEPENDENCIES_IMAGE}:latest
      else
        echo "Skipping build no changes found in package.json file";
      fi;

.test_template: &test_template
  rules:
    - if: $CI_COMMIT_REF_NAME == $REF_NAME
      when: on_success
  image: "${DOCKER_REGISTRY_REPO}/${DOCKER_DEPENDENCIES_IMAGE}:latest"
  stage: test
  script:
    - ln -s /usr/src/app/node_modules node_modules
    - npm install --prefix /usr/src/app --save-dev
    - node tests/init_memory_server.js
    - npm test -- tests/*/test_*.js
    - npm test -- tests/*/validate_*.js

.build_template: &build_template
  rules:
    - if: $CI_COMMIT_REF_NAME == $REF_NAME
      when: on_success
  stage: build
  script:
    - docker pull ${DOCKER_REGISTRY_DEPENDENCIES_REPO}/${DOCKER_DEPENDENCIES_IMAGE}:latest
    - >
      docker build --build-arg HTTP_PROXY=${HTTP_PROXY} --build-arg HTTPS_PROXY=${HTTPS_PROXY}
      --build-arg DEPENDENCIES_IMAGE="${DOCKER_REGISTRY_DEPENDENCIES_REPO}/${DOCKER_DEPENDENCIES_IMAGE}:latest"
      -t ${DOCKER_REGISTRY_REPO}/${DOCKER_MERGE_IMAGE}:${CI_COMMIT_SHA} -f Dockerfile.merge .
    - docker tag ${DOCKER_REGISTRY_REPO}/${DOCKER_MERGE_IMAGE}:${CI_COMMIT_SHA} ${DOCKER_REGISTRY_REPO}/${DOCKER_MERGE_IMAGE}:latest
    - echo ${DOCKER_REGISTRY_PASSWORD} | docker login --username ${DOCKER_REGISTRY_USERNAME} --password-stdin ${DOCKER_REGISTRY_REPO}
    - docker push ${DOCKER_REGISTRY_REPO}/${DOCKER_MERGE_IMAGE}:${CI_COMMIT_SHA}
    - docker push ${DOCKER_REGISTRY_REPO}/${DOCKER_MERGE_IMAGE}:latest
    - docker rmi ${DOCKER_REGISTRY_REPO}/${DOCKER_MERGE_IMAGE}:${CI_COMMIT_SHA}
    - docker rmi ${DOCKER_REGISTRY_REPO}/${DOCKER_MERGE_IMAGE}:latest

.deploy_template: &deploy_template
  rules:
    - if: $CI_COMMIT_REF_NAME == $REF_NAME
      when: on_success
  image: "harbor.tools.telstra.com/merge-ci/deploy:latest"
  before_script:
    - eval $(ssh-agent -s)
    - echo "$MERGE_DEPLOY_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo "$SSH_KNOWN_HOSTS" >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
    - echo "$GIT_CRYPT_KEY" | base64 -d > /tmp/git-crypt.key
    - git-crypt unlock /tmp/git-crypt.key
  script:
    - rsync -a --chown=merge-deploy:merge-admin --chmod=640 config/config.json ${USER}@${HOST}:${CONFIG_PATH}
    - ssh ${USER}@${HOST} "docker pull ${DOCKER_REGISTRY_REPO}/${DOCKER_MERGE_IMAGE}:latest"
    - ssh ${USER}@${HOST} "cd ${CONFIG_PATH} && ./docker-compose-set-id docker compose up -d"
    - UNTAGGED_IMAGES=$(ssh ${USER}@${HOST} "docker image ls --filter reference=${DOCKER_REGISTRY_REPO}/${DOCKER_MERGE_IMAGE} --filter dangling=true --quiet")
    - '[[ ! -z $UNTAGGED_IMAGES ]] && ssh ${USER}@${HOST} docker rmi ${UNTAGGED_IMAGES} || exit 0'

.load_config_template: &load_config_template
  rules:
    - if: $GITLAB_USER_LOGIN == "project_32001_bot"
      when: never
    - if: $CI_COMMIT_REF_NAME == $REF_NAME
      when: on_success
  image: "harbor.tools.telstra.com/merge-ci/deploy:latest"
  stage: load-config
  before_script:
    - eval $(ssh-agent -s)
    - echo "$MERGE_DEPLOY_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo "$SSH_KNOWN_HOSTS" >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - ssh ${USER}@${HOST} "docker run --rm -v /etc/ssl/certs:/etc/ssl/certs:ro -v /etc/ssl/private:/etc/ssl/private:ro -v ${CONFIG_PATH}/config.json:/usr/src/app/config/config.json ${DOCKER_REGISTRY_REPO}/${DOCKER_MERGE_IMAGE}:latest /bin/bash -c \"node bin/config_to_database.js --env ${MERGE_ENV}\""

build-dependencies:testing:
  <<: *build_dependencies_template
  variables:
    DOCKER_REGISTRY_REPO: harbor.tools.telstra.com/merge-ci
    DOCKER_DEPENDENCIES_IMAGE: node-dependencies-testing
    REF_NAME: testing
  allow_failure: false

build-dependencies:staging:
  <<: *build_dependencies_template
  variables:
    DOCKER_REGISTRY_REPO: harbor.tools.telstra.com/merge-ci
    DOCKER_DEPENDENCIES_IMAGE: node-dependencies-staging
    REF_NAME: stage
  allow_failure: false

build-dependencies:production:
  <<: *build_dependencies_template
  variables:
    DOCKER_REGISTRY_REPO: harbor.tools.telstra.com/merge-ci
    DOCKER_DEPENDENCIES_IMAGE: node-dependencies-production
    REF_NAME: master
  allow_failure: false

test:testing:
  <<: *test_template
  variables:
    DOCKER_REGISTRY_REPO: harbor.tools.telstra.com/merge-ci
    DOCKER_DEPENDENCIES_IMAGE: node-dependencies-testing
    REF_NAME: testing
  allow_failure: false
  needs:
    - "build-dependencies:testing"

test:staging:
  <<: *test_template
  variables:
    DOCKER_REGISTRY_REPO: harbor.tools.telstra.com/merge-ci
    DOCKER_DEPENDENCIES_IMAGE: node-dependencies-staging
    REF_NAME: stage
  allow_failure: false
  needs:
    - "build-dependencies:staging"

test:production:
  <<: *test_template
  variables:
    DOCKER_REGISTRY_REPO: harbor.tools.telstra.com/merge-ci
    DOCKER_DEPENDENCIES_IMAGE: node-dependencies-production
    REF_NAME: master
  allow_failure: false
  needs:
    - "build-dependencies:production"

build:testing:
  <<: *build_template
  variables:
    DOCKER_MERGE_IMAGE: merge-testing
    DOCKER_REGISTRY_REPO: harbor.tools.telstra.com/merge
    DOCKER_REGISTRY_DEPENDENCIES_REPO: harbor.tools.telstra.com/merge-ci
    DOCKER_DEPENDENCIES_IMAGE: node-dependencies-testing
    REF_NAME: testing
  allow_failure: false
  needs:
    - "test:testing"

build:staging:
  <<: *build_template
  variables:
    DOCKER_MERGE_IMAGE: merge-staging
    DOCKER_REGISTRY_REPO: harbor.tools.telstra.com/merge
    DOCKER_REGISTRY_DEPENDENCIES_REPO: harbor.tools.telstra.com/merge-ci
    DOCKER_DEPENDENCIES_IMAGE: node-dependencies-staging
    REF_NAME: stage
  allow_failure: false
  needs:
    - "test:staging"

build:production:
  <<: *build_template
  variables:
    DOCKER_MERGE_IMAGE: merge-production
    DOCKER_REGISTRY_REPO: harbor.tools.telstra.com/merge
    DOCKER_REGISTRY_DEPENDENCIES_REPO: harbor.tools.telstra.com/merge-ci
    DOCKER_DEPENDENCIES_IMAGE: node-dependencies-production
    REF_NAME: master
  allow_failure: false
  needs:
    - "test:production"

deploy:testing:pucq-merg-001:
  <<: *deploy_template
  stage: deploy
  variables:
    CONFIG_PATH: /etc/merge/testing
    DOCKER_MERGE_IMAGE: merge-testing
    DOCKER_REGISTRY_REPO: harbor.tools.telstra.com/merge
    HOST: pucq-merg-001.networks.in.telstra.com.au
    REF_NAME: testing
    USER: merge-deploy
  allow_failure: false
  needs:
    - "build:testing"

deploy:staging:puex-merg-001:
  <<: *deploy_template
  stage: deploy
  variables:
    CONFIG_PATH: /etc/merge/staging
    DOCKER_MERGE_IMAGE: merge-staging
    DOCKER_REGISTRY_REPO: harbor.tools.telstra.com/merge
    HOST: puex-merg-001.networks.in.telstra.com.au
    REF_NAME: stage
    USER: merge-deploy
  allow_failure: false
  needs:
    - "build:staging"

deploy:production:pucq-merg-002:
  <<: *deploy_template
  stage: deploy
  variables:
    CONFIG_PATH: /etc/merge/production
    DOCKER_MERGE_IMAGE: merge-production
    DOCKER_REGISTRY_REPO: harbor.tools.telstra.com/merge
    HOST: pucq-merg-002.networks.in.telstra.com.au
    REF_NAME: master
    USER: merge-deploy
  allow_failure: false
  needs:
    - "build:production"

deploy:production:puex-merg-002:
  <<: *deploy_template
  stage: deploy
  variables:
    CONFIG_PATH: /etc/merge/production
    DOCKER_MERGE_IMAGE: merge-production
    DOCKER_REGISTRY_REPO: harbor.tools.telstra.com/merge
    HOST: puex-merg-002.networks.in.telstra.com.au
    REF_NAME: master
    USER: merge-deploy
  allow_failure: false
  needs:
    - "build:production"

deploy:production:pucq-merg-003:
  <<: *deploy_template
  stage: deploy
  variables:
    CONFIG_PATH: /etc/merge/production
    DOCKER_MERGE_IMAGE: merge-production
    DOCKER_REGISTRY_REPO: harbor.tools.telstra.com/merge
    HOST: pucq-merg-003.networks.in.telstra.com.au
    REF_NAME: master
    USER: merge-deploy
  allow_failure: false
  needs:
    - "build:production"

deploy:production:puex-merg-003:
  <<: *deploy_template
  stage: deploy
  variables:
    CONFIG_PATH: /etc/merge/production
    DOCKER_MERGE_IMAGE: merge-production
    DOCKER_REGISTRY_REPO: harbor.tools.telstra.com/merge
    HOST: puex-merg-003.networks.in.telstra.com.au
    REF_NAME: master
    USER: merge-deploy
  allow_failure: false
  needs:
    - "build:production"

load-config:testing:
  <<: *load_config_template
  variables:
    CONFIG_PATH: /etc/merge/testing
    DOCKER_MERGE_IMAGE: merge-testing
    DOCKER_REGISTRY_REPO: harbor.tools.telstra.com/merge
    HOST: pucq-merg-001.networks.in.telstra.com.au
    MERGE_ENV: testing
    REF_NAME: testing
    USER: merge-deploy
  needs:
    - "deploy:testing:pucq-merg-001"
  allow_failure: true

load-config:staging:
  <<: *load_config_template
  variables:
    CONFIG_PATH: /etc/merge/staging
    DOCKER_MERGE_IMAGE: merge-staging
    DOCKER_REGISTRY_REPO: harbor.tools.telstra.com/merge
    HOST: puex-merg-001.networks.in.telstra.com.au
    MERGE_ENV: stage
    REF_NAME: stage
    USER: merge-deploy
  needs:
    - "deploy:staging:puex-merg-001"
  allow_failure: true

load-config:production:
  <<: *load_config_template
  variables:
    CONFIG_PATH: /etc/merge/production
    DOCKER_MERGE_IMAGE: merge-production
    DOCKER_REGISTRY_REPO: harbor.tools.telstra.com/merge
    HOST: pucq-merg-002.networks.in.telstra.com.au
    MERGE_ENV: prd
    REF_NAME: master
    USER: merge-deploy
  needs:
    - "deploy:production:pucq-merg-002"
  allow_failure: true
