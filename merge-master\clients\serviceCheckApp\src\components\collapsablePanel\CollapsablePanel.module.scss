.collapsablePanel {
    display: flex;
    flex-direction: column;

    button {
        background: none;
        color: inherit;
        border: none;
        padding: 0;
        font: inherit;
        cursor: pointer;
        outline: inherit;
    }

    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .headerRight {
        display: flex;
        align-items: center;
        gap: 1rem;
        
    }
    
}