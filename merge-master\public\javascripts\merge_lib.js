/* Merge Javascript Function for ServiceCheck Page*/

$(document).ready(function() {
    $('body').on('click', '.expand', function() {
        if ($(this).attr('data-target')) {
            if(!$($(this).attr('data-target')).hasClass('show')) {
                $(this).children('span').removeClass('fa-plus-square');
                $(this).children('span').addClass('fa-minus-square');
            } else {
                $(this).children('span').removeClass('fa-minus-square');
                $(this).children('span').addClass('fa-plus-square');
            }
        }
    });

    let clipboardLink = new ClipboardJS('.copyToClipboardLink', {
        text: function(trigger) {
            let originalTitle = trigger.getAttribute('title');

            setTooltip(trigger, 'Copied');
            hideTooltip(trigger, originalTitle);

            return trigger.getAttribute('href');
        }
    });

    let clipboardEditor = new ClipboardJS('.copyToClipboardEditor', {
        text: function(trigger) {
            if (!ace) {
                return null;
            }

            let originalTitle = trigger.getAttribute('title');

            setTooltip(trigger, 'Copied');
            hideTooltip(trigger, originalTitle);

            let editor = ace.edit(trigger.attributes["data-editor"].value);
            return editor.getValue();
        }
    });

    let clipboardRecord = new ClipboardJS('.copyToClipboardRecord', {
        text: function(trigger) {
            let id = trigger.attributes["data-id"].value;
            let originalTitle = trigger.getAttribute('title');

            setTooltip(trigger, 'Copied');
            hideTooltip(trigger, originalTitle);

            return $(`#fullRecord-${id} > pre`).text();
        }
    });

    let clipboardTemplate = new ClipboardJS('.copyToClipboardTemplate', {
        text: function(trigger) {
            let originalTitle = trigger.getAttribute('title');

            setTooltip(trigger, 'Copied');
            hideTooltip(trigger, originalTitle);

            return $('#textTemplateModal-text').text();
        }
    });

    // ClipboardJS does not work inside modals bootstrap due an issue with focusing
    // These listeners just change the container of the ClipboardJS instances
    // to the modal element
    $(document).on('shown.bs.modal','.modal', function() {
        let modalDocument = document.getElementById($(this).attr('id'));
        clipboardLink.container = modalDocument;
        clipboardEditor.container = modalDocument;
        clipboardRecord.container = modalDocument;
        clipboardTemplate.container = modalDocument;
    });

    $(document).on('hidden.bs.modal', '.modal', function() {
        clipboardLink.container = document.body;
        clipboardEditor.container = document.body;
        clipboardRecord.container = document.body;
        clipboardTemplate.container = document.body;
    });

    // Listener for dropdown submenu support
    $('.dropdown-menu a.dropdown-toggle').on('click', function(e) {
        if (!$(this).next().hasClass('show')) {
            $(this).parents('.dropdown-menu').first().find('.show').removeClass('show');
        }
        var $subMenu = $(this).next('.dropdown-menu');
        $subMenu.toggleClass('show');

        $(this).parents('li.nav-item.dropdown.show').on('hidden.bs.dropdown', function(e) {
            $('.dropdown-submenu .show').removeClass('show');
        });

        return false;
    });
});


//function Show external image on Messages
function mergeShowLinkPopoverImages(id){
    $(`#${id} a.aShowImgPopover`).popover({
        html: true,
        trigger: 'hover',
        placement: 'bottom',
        //content: function(){return '<img src="'+$(this).data('img') + '" />';}
        content: function(){return '<img src="'+$(this).attr('href')+ '" />';}
    });
}

//Format and Object in printable JSON
function appendObjectTextbox(element, categoryID, data) {
    if (!categoryID) {
        categoryID = idGen('MJT');
    }

    let expandID = `${categoryID}-expand`;
    let checkboxWordWrapID = `${categoryID}-wordwrap`;
    let editorID = `${categoryID}-editor`;

    let title;
    let displayHtml;
    let editorContent;
    let editorMode;

    if (typeof data === 'object') {
        title = "Show data in JSON format";
        displayHtml = "JSON <span style='color:blue' class='fas fa-plus-square'/>";
        editorContent = JSON.stringify(data, null, 4);
        editorMode = "ace/mode/json";
    } else {
        title = "Show data in plaintext format";
        displayHtml = "Text <span style='color:blue' class='fas fa-plus-square'/>";
        editorContent = data ? data : "";
        editorMode = "ace/mode/text";
    }

    let output = $("<div>")
                    .append($("<a>").addClass("expand badge badge-info")
                        .attr("title", title)
                        .attr("data-toggle", "collapse")
                        .attr("data-target", `#${expandID}`).html(displayHtml))
                    .append($("<div>").attr("id", expandID).addClass("collapse")
                        .append($("<div>").addClass("form-row p-2").append(
                            $("<div>").addClass("pl-2 pr-2")
                                .html($("<button>").addClass("btn btn-outline-secondary btn-sm copyToClipboardEditor")
                                    .attr("data-editor", editorID)
                                    .attr("data-toggle", "tooltip")
                                    .attr("data-placement", "top")
                                    .attr("title", "Copy to clipboard").html("<span class=\"fa fa-copy\"></span>")
                                )
                        ).append(
                            $("<div>").addClass("pl-2 pr-2")
                                .append($("<label>").addClass("col-form-label pr-1")
                                    .attr("for", checkboxWordWrapID).text("Word Wrap"))
                                .append($("<input>")
                                    .attr("id", checkboxWordWrapID)
                                    .attr("type", "checkbox")
                                )
                            )
                        )
                        .append($("<div>").addClass("p-2").html(
                            $("<div>").addClass("border border-secondary rounded")
                                .attr("id", editorID)
                            )
                        )
                    );

    element.append(output);

    $(`#${$.escapeSelector(checkboxWordWrapID)}`).change(function() {
        let currEditor = ace.edit(editorID);
        currEditor.setOption("wrap", this.checked);
    });

    let editor = ace.edit(editorID, {
        mode: editorMode,
        minLines: 1,
        maxLines: 100,
        readOnly: true,
        printMargin: false,
        fontSize: '12pt',
        wrap: false
    });

    editor.setValue(editorContent);
    editor.clearSelection();
    editor.setAutoScrollEditorIntoView(true);
}


function appendObjectTable(element, data) {
    if (element) {
        element.append(printObjectTable(data));
    }
}

//Format an Objected in Nested Table
function printObjectTable(obj) {
    //if is not object/array just print string
    //console.log('======Object to print table ========' + typeof obj);
    //console.log(obj);
    if (typeof obj === 'string') {
        return $("<div>").addClass("border border-info").html(DOMPurify.sanitize(obj));
    }

    let output = "<table border='1' style='width: 100%;' cellpadding='5' class='text-left text-monospace table table-striped' >\n<thead class=\"thead-dark\">\n";
    output += "\t<th id=\"key\" style='width: 5%;'>Key</th>\n";
    output += "\t<th id=\"val\" style='width: 95%;'>Content</th>\n";
    output += "</thead>\n<tbody>";
    for (var key in obj) {
        let keyStr;
        if (typeof key === "string") {
            keyStr = key;
        } else {
            keyStr = '>';
        }
        let id = 'key_' + key.replace(/[^A-Za-z0-9]/ig, '_') + '_' + Math.floor(Math.random() * Math.floor(9999999));
        if (typeof obj[key] === "object" && obj[key] == null) {
            output += `\t<tr><td>${keyStr}</td><td>(empty)</td>\n`;
        } else if (typeof obj[key] === "object") {
            output += `\t<tr><td class='expand' data-toggle='collapse' data-target='#${id}'>${key} <span style='color:blue' class='fas fa-plus-square'/></td>\n<td>\n`;
            output += `<div id='${id}' class='collapse in'>`;
            output += printObjectTable(obj[key]);
            output += "</div></td>\n";
        } else if (obj[key].length > 250) {
            output += `\t<tr><td class='expand' data-toggle='collapse' data-target='#${id}'>${keyStr} <span style='color:yellowgreen' class='fas fa-plus-square'/></td>\n<td>\n`;
            output += `<div id='${id}' class='collapse in'><pre>`;
            //output  += obj[key].replace(/\r\n|\n/g, '_' );
            output += obj[key];
            output += "</pre></div></td>\n";
        } else if (typeof obj[key] === "boolean") {
            output += `\t<tr><td>${key}</td><td>` + ((obj[key]) ? 'true' : 'false') + `</td>\n`;
        } else if (typeof obj[key] === "String") {
            output += `\t<tr><td>${keyStr}</td><td><pre>` + obj[key].replace(/\r\n|\n/g, '</br>') + "</pre></td>\n";
        } else {
            output += `\t<tr><td>${keyStr}</td><td>` + obj[key] + "</td>\n";
        }
    }
    output += "</tbody>\n</table>\n";
    return output;
}

//CopyToClipBoard no Library
function copyToClipboardModal(copyID) {
    const el = document.createElement('textarea');
    console.info('copy id = ', copyID);
    el.value = $('#' + copyID).html();
    document.getElementById(copyID).appendChild(el);
    el.select();
    document.execCommand('Copy');
    document.getElementById(copyID).removeChild(el);
}


//Convert an Array of Object to an object (hash with one of attributes)
const arrayToObject = (array, keyField) =>
    array.reduce((obj, item) => {
        obj[item[keyField]] = item
        return obj
    }, {})

// Generate a unique ID for debug components in UI
function idGen(prefix) {
    var S4 = function () {
        return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    };
    return ((prefix ? prefix : '') + S4() + S4() + S4() + S4() + S4() + S4() + S4() + S4());
}


function setTooltip(element, message) {
    $(element).attr('title', message);
    $(element).tooltip({
        trigger: 'hover'
    });
    $(element).tooltip('enable');
    $(element).tooltip('show');
}


function hideTooltip(element, message) {
    $(element).tooltip('disable');
    $(element).attr('title', message);
}


function setButtonSpinner(element, originalContents, enableSpinner) {
    if (enableSpinner) {
        $(element).prop("disabled", true);
        $(element).html($("<span>").addClass("spinner-border spinner-border-sm").attr("role", "status").attr("aria-hidden", true));
    } else {
        $(element).prop("disabled", false);
        $(element).html(originalContents);
    }
}

function downloadToFile(fileName, fileContents) {
    const link = document.createElement('a');
    const file = new Blob([fileContents], { type: 'text/plain' });
    link.href = URL.createObjectURL(file);
    link.download = fileName;
    link.click();
    URL.revokeObjectURL(link.href);
}
