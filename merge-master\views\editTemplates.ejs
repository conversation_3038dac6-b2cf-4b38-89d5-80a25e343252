<%- include('header') %>
<%- include('jsonEditInclude', {}) %>
<%- include('menu', {currentTab: 'Form'}); %>
<link rel="stylesheet" type="text/css" href="/public/stylesheets/select2.min.css">
<script src="/public/javascripts/select2.min.js" crossorigin="anonymous"></script>
<script src="/public/javascripts/merge_SC_lib.js" crossorigin="anonymous"></script>
<br>
<div class="container">
    <div class="row">
        <div class="col-8">
            <a href="/edit/templates">< Back to templates</a>
        </div>
        <div class="col-4">
            <div class="float-right">
                <button class="btn btn-primary" id="createTemplate" data-toggle="modal" data-target="#confirmCreateModal" style="display:none;" title="Create" disabled><span class="fas fa-plus-square"></span></button>
                <button class="btn btn-primary" id="saveTemplate" data-toggle="modal" data-target="#confirmSaveModal" style="display:none;" title="Save" disabled><span class="fas fa-save"></span></button>
                <button class="btn btn-primary" id="refreshTemplate" data-toggle="modal" data-target="#confirmRefreshModal"  style="display:none;" title="Refresh"><span class="fas fa-sync"></span></button>
                <button class="btn btn-danger" id="deleteTemplate" data-toggle="modal" data-target="#confirmDeleteModal" style="display:none;" title="Delete" disabled><span class="fas fa-trash"></span></button>
            </div>
        </div>
    </div>
    <br>
    <div class="row">
        <div class="col-12">
            <div id="readonlyAlert" style="display:none;" class="alert alert-warning">Warning: Template is read only, editing is disabled.</div>
        </div>
    </div>

    <ul class="nav nav-tabs" role="tablist">
        <li role="presentation" class="nav-item active">
            <a class="nav-link active" data-target="#editTemplatesTab" data-toggle="tab" role="tab" href="">Edit Template</a>
        </li>
        <li role="presentation" class="nav-item">
            <a class="nav-link" data-target="#renderTemplatesTab" data-toggle="tab" role="tab" href="">Render Template</a>
        </li>
    </ul>

    <div class="tab-content">
        <div class="tab-pane active border" id="editTemplatesTab" style="height:75vh;overflow-y:auto;padding-bottom:16px;">
            <div style="padding: 2rem;">
                <div id="templatesLoadError" class="alert alert-danger" role="alert" style="display:none;padding-left:16px;"></div>
                <div id="templatesEdit" style="display:none;"></div>
            </div>
        </div>
        <div class="tab-pane border" id="renderTemplatesTab" style="height:75vh;overflow-y:auto;padding-bottom:16px;">
            <div style="padding: 2rem;">
                <div class="row border border-primary rounded bg-light">
                    <div class="card-body">
                        <div id="developDiffStatus" class="alert"></div>
                        <div class="form-row">
                            <button class="btn btn-outline-primary ml-1 mr-1" id="developSaveCode" title="Save code to &quot;Edit Template&quot; tab"><span class="fas fa-save"></span></button>
                            <button class="btn btn-outline-primary ml-1 mr-1" id="developRefreshCode" title="Reset code to values in &quot;Edit Template&quot; tab"><span class="fas fa-undo"></span></button>
                            <label id="developSaveTime" class="col-form-label"></label>
                        </div>
                        <div class="form-group">
                            <label class="col-form-label">Automatic Save</label>
                            <div class="form-check form-check-inline ml-2 mr-2">
                                <input class="form-check-input" type="radio" name="saveOptionRadios" id="saveOptionDisabled" value="disabled">
                                <label class="form-check-label" for="saveOptionDisabled">
                                    Disabled
                                </label>
                            </div>
                            <div class="form-check form-check-inline ml-2 mr-2">
                                <input class="form-check-input" type="radio" name="saveOptionRadios" id="saveOptionInterval" value="interval">
                                <label class="form-check-label mr-1" for="saveOptionInterval">
                                    Save every
                                </label>
                                <select id="saveOptionIntervalLength" style="width: 4em;">
                                    <option value=10 selected>10</option>
                                    <option value=30>30</option>
                                    <option value=60>60</option>
                                    <option value=300>300</option>
                                    <option value=600>600</option>
                                </select>
                                <label class="form-check-label ml-1" for="saveOptionInterval">
                                    seconds
                                </label>
                            </div>
                        </div>
                        <label class="col-form-label">Editor options:</label>
                        <div class="form-row">
                            <div class="ml-2 mr-2">
                                <input id="editorOptionWordWrap" type="checkbox">
                                <label class="col-form-label" for="editorOptionWordWrap">Word Wrap</label>
                            </div>
                            <div class="ml-2 mr-2">
                                <label class="col-form-label" for="editorOptionFontSize">Font Size</label>
                                <select id="editorOptionFontSize">
                                    <option value="8">8</option>
                                    <option value="10" selected>10</option>
                                    <option value="12">12</option>
                                    <option value="14">14</option>
                                    <option value="16">16</option>
                                </select>
                            </div>
                            <div class="ml-2 mr-2">
                                <label class="col-form-label" for="editorOptionTheme">Theme</label>
                                <select id="editorOptionTheme">
                                    <optgroup label="Light">
                                        <option value="ace/theme/textmate" selected>Default (Textmate)</option>
                                        <option value="ace/theme/eclipse">Eclipse</option>
                                        <option value="ace/theme/github">Github</option>
                                        <option value="ace/theme/xcode">Xcode</option>
                                    </optgroup>
                                    <optgroup label="Dark">
                                        <option value="ace/theme/cobalt">Cobalt</option>
                                        <option value="ace/theme/monokai">Monokai</option>
                                        <option value="ace/theme/tomorrow_night">Tomorrow Night</option>
                                        <option value="ace/theme/twilight">Twilight</option>
                                    </optgroup>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <br>
                <div class="row">
                    <div class="col-12 border rounded codeBackground" style="padding:8px;">
                        <label for="developTemplateInputServiceCheckRecord" class="codeLabel">Input Service Check Record</label>
                        <button id="resetTemplateInputServiceCheckRecord" class="btn btn-primary btn-sm" title="Reset"><span class="fas fa-undo"></span></button>
                        <div id="developTemplateInputServiceCheckRecord" style="height: 100%; width: 100%;"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 border rounded codeBackground" style="padding:8px;">
                        <label for="developTemplateInputParameters" class="codeLabel">Template Parameters</label>
                        <br>
                        <small class="codeLabel">Template parameter definitions that <b>only apply</b> for templateType "Module". Should be valid JSON.</small>
                        <button id="resetTemplateInputParameters" class="btn btn-primary btn-sm" title="Reset"><span class="fas fa-undo"></span></button>
                        <div id="developTemplateInputParameters" style="height: 100%; width: 100%;"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 border rounded codeBackground" style="padding:8px;">
                        <label for="developTemplateCode" class="codeLabel">Template Code</label>
                        <button id="renderTemplateCode" class="btn btn-success btn-sm">Render</button>
                        <br>
                        <small class="codeLabel">Template code to be rendered with EJS. If the template has templateType "Service Check", the renderTemplate() method is available to use to render templates with templateType "Module".</small>
                        <div class="alert p-1" role="alert" id="renderTemplateCodeResult" style="display:none;">
                            <small id="renderTemplateCodeText"></small>
                        </div>
                        <div id="developTemplateCode" style="height: 100%; width: 100%;"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 border rounded bg-light" style="padding:8px;">
                        <label for="templateOutput" class="text-dark">Template Output</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showRulesData"><label class="form-check-label" for="showRulesData">Show rules data</label>
                        </div>
                        <small id="templateOutputUpdatedAt" class="text-muted"></small>
                        <div id="textTemplateModal-controls" class="form-row p-2" style="display:none;">
                            <span class="icons">
                                <button
                                    class="btn btn-outline-secondary btn-sm copyToClipboardTemplate"
                                    data-toggle="tooltip"
                                    data-placement="top"
                                    title="Copy to clipboard"
                                >
                                    <span class="fa fa-copy"></span>
                                </button>
                                <button id="textTemplateModal-download" class="btn btn-dark btn-sm" title="Download template to text file">
                                    <span class="fas fa-download"></span> Text
                                </button>
                            </span>
                        </div>
                        <div id="textTemplateModal-alert" style="display:none;" class="alert alert-danger" role="alert">
                            <pre id="textTemplateModal-alertText"></pre>
                        </div>
                        <pre id="textTemplateModal-text" style="display:none;" class="border"></pre>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 border rounded bg-light" style="padding:8px;">
                        <label for="developOutputConsole" class="text-dark">Console</label>
                        <div id="developOutputConsole" class="border" style="height: 100%; width: 100%; max-height: 2160px; resize: vertical; overflow: hidden;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="createTemplateModal" class="modal fade">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Error</h5>
            </div>
            <div class="modal-body">
                <code id="createTemplateModalMessage" style="white-space:pre-wrap;"></code>
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-outline-secondary">Close</button>
            </div>
        </div>
    </div>
</div>

<div id="confirmSaveModal" class="modal fade">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Save</h5>
            </div>
            <div class="modal-body">
                Would you like to save the changes for this template?
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-primary" id="confirmSave">Save</button>
                <button type="button" data-dismiss="modal" class="btn btn-outline-secondary">Cancel</button>
            </div>
        </div>
    </div>
</div>

<div id="confirmRefreshModal" class="modal fade">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Refresh</h5>
            </div>
            <div class="modal-body">
                Would you like to refresh the template contents? Any changes made will be lost.
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-primary" id="confirmRefresh">Refresh</button>
                <button type="button" data-dismiss="modal" class="btn btn-outline-secondary">Cancel</button>
            </div>
        </div>
    </div>
</div>

<div id="confirmDeleteModal" class="modal fade">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
            </div>
            <div class="modal-body">
                Would you like to delete this template? This action cannot be reversed.
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-danger" id="confirmDelete">Delete</button>
                <button type="button" data-dismiss="modal" class="btn btn-outline-secondary">Cancel</button>
            </div>
        </div>
    </div>
</div>

<script>
    const templateName = atob("<%- templateName %>");
    const wikiBaseURL = "<%= global.gConfig.wikiBaseURL %>";
    const createTemplate = <%= createTemplate %>;
    const disableRuleSourceEditing = <%- user.isAdmin ? false : global.gConfig.disableRuleSourceEditing %>;
    const schema = JSON.parse(atob("<%- schema %>"));

    const SERVICE_CHECK_RECORD_DEFAULT = JSON.stringify({
        "input": {
            "searchFNN": "",
            "level": 0,
            "suite": "standard",
            "idType": null,
            "carriageFNN": "",
            "deviceName": "",
            "deviceIP": "",
            "carriageType": ""
        },
        "phoneNumber": null,
        "serviceType": null,
        "siiamCases": [],
        "serviceCentralIncidents": [],
        "createdBy": "",
        "id": "",
        "additionalParameters": {
            "dateFrom": null,
            "dateTo": null
        },
        "createdOn": "",
        "status": "",
        "fnn": "",
        "billingFNN": null,
        "carriageFNN": "",
        "MDNFNN": "",
        "carriageType": "",
        "nbnServiceType": null,
        "nbnAccessType": null,
        "nbnSubAccessType": null,
        "deviceName": null,
        "CIDN": null,
        "address": "",
        "endedOn": "",
        "durationMilliSec": 0,
        "rulesData": {},
        "sourcesData": {},
        "sourcesMetadata": {}
    }, null, 4);

    var dataLoaded = false;
    var dataModified = false;
    var autosaveInterval = null;

    $(document).ready(function() {
        $("#createTemplate").prop("disabled", disableRuleSourceEditing);
        $("#saveTemplate").prop("disabled", disableRuleSourceEditing);
        $("#deleteTemplate").prop("disabled", disableRuleSourceEditing);
        $("#developSaveCode").prop("disabled", disableRuleSourceEditing);

        let jsonEditor = new JSONEditor(document.getElementById('templatesEdit'), {
            ajax: false,
            enable_array_copy: false,
            schema: schema,
            startval: null,
            no_additional_properties: true,
            required_by_default: true,
            show_errors: "always"
        });

        if (disableRuleSourceEditing) {
            jsonEditor.disable();
            $("input[name='saveOptionRadios']").attr("disabled", true);
            $("#readonlyAlert").show();
        }

        if (createTemplate) {
            $("#createTemplate").show();
            $("#templatesEdit").show();
        } else {
            $("#saveTemplate").show();
            $("#refreshTemplate").show();
            $("#deleteTemplate").show();
            getTemplateToForm(jsonEditor);
        }

        const aceEditors = setupDevelopmentEditors();
        const codeEditors = Object.freeze({
            template: aceEditors.template
        });
        var outputVariables;


        $("#createTemplate").click(function() {
            createTemplateFromForm(jsonEditor);
        });

        $("#confirmSave").click(function() {
            writeTemplateFromForm(jsonEditor);
        });

        $("#confirmRefresh").click(function() {
            getTemplateToForm(jsonEditor);
        });

        $("#confirmDelete").click(function() {
            $.ajax({
                type: "DELETE",
                url: `/templates/${encodeURIComponent(templateName)}`,
                success: function (response) {
                    window.location.href = "/edit/templates";
                },
                error: function (error) {
                    alert("Error, could not delete template:" + error.responseText);
                },
            });
        });

        $("#developSaveCode").click(function() {
            jsonEditor.getEditor('root.template').setValue(codeEditors.template.getValue());

            updateCodeDiffStatus(jsonEditor, codeEditors);
            updateSaveTimeLabel();
        });

        $("#developRefreshCode").click(function() {
            let template = jsonEditor.getValue();
            codeEditors.template.setValue(template.template);
            codeEditors.template.clearSelection();

            updateCodeDiffStatus(jsonEditor, codeEditors);
        });

        $("#resetTemplateInputServiceCheckRecord").click(function() {
            aceEditors.inputServiceCheckRecord.setValue(SERVICE_CHECK_RECORD_DEFAULT);
            aceEditors.inputServiceCheckRecord.clearSelection();
        });

        $("#resetTemplateInputParameters").click(function() {
            aceEditors.inputParameters.setValue("{}");
            aceEditors.inputParameters.clearSelection();
        });

        $("#saveOptionIntervalLength").select2({
            minimumResultsForSearch: -1,
            width: "resolve"
        });

        $("#editorOptionFontSize").select2({
            minimumResultsForSearch: -1,
            width: "resolve"
        });

        $("#editorOptionTheme").select2({
            minimumResultsForSearch: -1,
            width: "16em"
        });

        $("input[name='saveOptionRadios']").change(function() {
            localStorage.setItem("templateDevelopAutosaveOption", this.value);
            setupSaveInterval(this.value, $("#saveOptionIntervalLength").val());
        });

        $("#editorOptionWordWrap").change(function() {
            localStorage.setItem("templateDevelopEditorWordWrap", this.checked);

            for (var editorName in aceEditors) {
                aceEditors[editorName].setOption("wrap", this.checked);
            }
        });

        $("#saveOptionIntervalLength").on('select2:select', function (e) {
            localStorage.setItem("templateDevelopAutosaveInterval", this.value);
            setupSaveInterval($("input[name='saveOptionRadios']:checked").val(), this.value);
        });

        $("#editorOptionFontSize").on('select2:select', function (e) {
            let fontSize = $("#editorOptionFontSize").val();
            localStorage.setItem("templateDevelopEditorFontSize", fontSize);

            for (var editorName in aceEditors) {
                if (editorName !== "outputConsole") {
                    aceEditors[editorName].setOption("fontSize", `${fontSize}pt`);
                }
            }
        });

        $("#editorOptionTheme").on('select2:select', function (e) {
            let theme = $("#editorOptionTheme").val();
            let themeType = $("#editorOptionTheme :selected").parent("optgroup").attr("label");

            localStorage.setItem("templateDevelopEditorTheme", theme);

            for (var editorName in aceEditors) {
                if (editorName !== "outputConsole") {
                    aceEditors[editorName].setOption("theme", theme);
                }
            }

            switch (themeType) {
                case "Light":
                    $(".codeBackground").addClass("bg-light");
                    $(".codeLabel").addClass("text-dark");

                    $(".codeBackground").removeClass("bg-dark");
                    $(".codeLabel").removeClass("text-light");
                    break;
                case "Dark":
                    $(".codeBackground").addClass("bg-dark");
                    $(".codeLabel").addClass("text-light");

                    $(".codeBackground").removeClass("bg-light");
                    $(".codeLabel").removeClass("text-dark");
                    break;
            }
        });

        let autosaveOption = localStorage.getItem("templateDevelopAutosaveOption") ? localStorage.getItem("templateDevelopAutosaveOption") : "disabled";
        let autosaveInterval = localStorage.getItem("templateDevelopAutosaveInterval") ? localStorage.getItem("templateDevelopAutosaveInterval") : "10";
        let editorOptionFontSize = localStorage.getItem("templateDevelopEditorFontSize") ?  localStorage.getItem("templateDevelopEditorFontSize") : "10";
        let editorOptionTheme = localStorage.getItem("templateDevelopEditorTheme") ? localStorage.getItem("templateDevelopEditorTheme") : "ace/theme/textmate";
        let editorOptionWordWrap = localStorage.getItem("templateDevelopEditorWordWrap") ? localStorage.getItem("templateDevelopEditorWordWrap") : false;

        $("input[name='saveOptionRadios']").filter(function() {
            return $(this).val() == autosaveOption;
        }).attr("checked", true);

        $("#saveOptionIntervalLength").val(autosaveInterval);
        $("#saveOptionIntervalLength").trigger("change");
        $("#saveOptionIntervalLength").trigger("select2:select");

        $("#editorOptionWordWrap").prop("checked", editorOptionWordWrap);

        $("#editorOptionFontSize").val(editorOptionFontSize);
        $("#editorOptionFontSize").trigger("change");
        $("#editorOptionFontSize").trigger("select2:select");

        $("#editorOptionTheme").val(editorOptionTheme);
        $("#editorOptionTheme").trigger("change");
        $("#editorOptionTheme").trigger("select2:select");

        $('.close').click(function() {
            $(this).parent().hide();
        });

        $("#renderTemplateCode").click(function() {
            renderTemplate();
        });

        $("#showRulesData").on("change", function() {
            renderTemplate();
        });

        $("#textTemplateModal-download").click(function() {
            const templateText = $("#textTemplateModal-text").text();
            const templateName = (jsonEditor.getValue()?.name || '').replace(/[^a-z0-9]/gi, '_');
            const dateString = new Date().toISOString().replace(/[^0-9]/g, '');
            const fileName = `Text_Template_${templateName}_${dateString}.txt`;

            downloadToFile(fileName, templateText);
        });

        jsonEditor.on("change", function() {
            // If the template was loaded recently via AJAX, the change event will still be triggered,
            // do not count this instance as the template being modified by the user
            if (!dataLoaded) {
                dataModified = true;
            } else {
                dataLoaded = false;
            }
        });

        // Sets up listeners on Ace editor instances for source code
        for (const key in codeEditors) {
            codeEditors[key].on("change", function() {
                updateCodeDiffStatus(jsonEditor, codeEditors);
            });
        }

        $(window).on("beforeunload", function(e) {
            let template = jsonEditor.getValue();

            if (dataModified || editorHasDiff(template, codeEditors)) {
                return confirm("");
            } else {
                return;
            }
        });

        function renderTemplate() {
            try {
                let serviceCheckRecord = JSON.parse(aceEditors.inputServiceCheckRecord.getValue());
                let showRulesData = $("#showRulesData").prop("checked");

                let template = jsonEditor.getValue();
                template.template = codeEditors.template.getValue();

                setButtonSpinner($("#renderTemplateCode"), "Render", true);
                editorAppendText(aceEditors.outputConsole, "Template render started");
                $("#renderTemplateCodeResult").hide();

                let renderRequestData = {
                    serviceCheckRecord: serviceCheckRecord,
                    template: template,
                    showRulesData: showRulesData
                };

                if (template.templateType === "Module") {
                    renderRequestData.parameters = JSON.parse(aceEditors.inputParameters.getValue());
                }

                $.ajax({
                    type: "POST",
                    url: "/templates/render",
                    contentType: "application/json",
                    data: JSON.stringify(renderRequestData),
                    success: function(response) {
                        $("#textTemplateModal-alert").hide();

                        $("#textTemplateModal-controls").show();
                        $("#textTemplateModal-text").text(response);
                        $("#textTemplateModal-text").show();

                        let currDate = new Date().toLocaleString('en-GB');
                        $("#templateOutputUpdatedAt").text(`Last updated at: ${currDate}`);

                        editorAppendText(aceEditors.outputConsole, "Template render completed");
                    },
                    error: function(err) {
                        $("#textTemplateModal-alertText").text(err.responseText);
                        $("#textTemplateModal-alert").show();

                        $("#textTemplateModal-controls").hide();
                        $("#textTemplateModal-text").hide();

                        editorAppendText(aceEditors.outputConsole, "Error when rendering template, see \"Template Output\" section");
                    },
                    complete: function(xhr, status) {
                        setButtonSpinner($("#renderTemplateCode"), "Render", false);
                    }
                });
            } catch(error) {
                if (error.name == "SyntaxError" && error.message.startsWith("JSON.parse")) {
                    editorAppendText(aceEditors.outputConsole, `Error when parsing JSON from "Input Service Check Record" or "Input Parameters", ${error.toString()}`);

                    $("#textTemplateModal-alertText").text("Invalid JSON found in \"Input Service Check Record\" section or \"Input Parameters\" section");
                    $("#textTemplateModal-alert").show();

                    $("#textTemplateModal-controls").hide();
                    $("#textTemplateModal-text").hide();
                } else {
                    editorAppendText(aceEditors.outputConsole, `Unexpected error when rendering template, ${error.toString()}`);
                }
            }
        }
    });

    function getTemplateToForm(editor) {
        setButtonSpinner($("#refreshTemplate"), $("<span>").addClass("fas fa-sync"), true);

        $.ajax({
            cache: false,
            type: "GET",
            url: `/templates/${encodeURIComponent(templateName)}`,
            dataType: "json",
            success: function (response) {
                dataLoaded = true;
                dataModified = false;

                editor.setValue(response);
                $("#templatesEdit").show();

                // Load code obtained from request into execute template editors
                $("#developRefreshCode").click();
            },
            error: function (error) {
                let errorMessage = '';
                if (error.status == 404) {
                    errorMessage = `Template ${templateName} does not exist`;
                } else {
                    errorMessage = `Error encountered loading template ${templateName}: HTTP ${error.status} (${error.statusText}): ${error.responseText}`;
                }
                $("#templatesLoadError").text(errorMessage);
                $("#templatesLoadError").show();
            },
            complete: function(xhr, status) {
                setButtonSpinner($("#refreshTemplate"), $("<span>").addClass("fas fa-sync"), false);
            }
        });
    }

    function createTemplateFromForm(editor) {
        let errors = editor.validate();

        if (!errors.length) {
            $.ajax({
                type: "POST",
                url: "/templates/",
                contentType: "application/json",
                dataType: "json",
                data: JSON.stringify(editor.getValue()),
                success: function (response) {
                    dataModified = false;
                    window.location.href = `/edit/templates/${encodeURIComponent(editor.getValue().name)}`;
                },
                error: function (error) {
                    if (error.status == 400) {
                        $("#createTemplateModalMessage").text(`Error in fields for template:\n${JSON.stringify(error.responseJSON, null, 4)}`);
                    } else if (error.status == 409) {
                        $("#createTemplateModalMessage").text(`A template with name ${editor.getValue().name} already exists.`);
                    } else {
                        $("#createTemplateModalMessage").text(`Unknown Error:\n${error.responseJSON}`);
                    }

                    $("#createTemplateModal").modal({ show: true });
                },
            });
        } else {
            $("#createTemplateModalMessage").text("Validation errors in editor, please check fields in form.");
            $("#createTemplateModal").modal({ show: true });
        }
    }

    function writeTemplateFromForm(editor) {
        let errors = editor.validate();

        if (!errors.length) {
            setButtonSpinner($("#saveTemplate"), $("<span>").addClass("fas fa-save"), true);

            $.ajax({
                cache: false,
                type: "PUT",
                url: `/templates/${encodeURIComponent(templateName)}`,
                contentType: "application/json",
                dataType: "json",
                data: JSON.stringify(editor.getValue()),
                success: function (_) {
                    dataModified = false;
                },
                error: function(error) {
                    // just alerts the user for now
                    alert("Error, could not save template:" + error.responseText);
                },
                complete: function(xhr, status) {
                    setButtonSpinner($("#saveTemplate"), $("<span>").addClass("fas fa-save"), false);
                }
            });
        } else {
            $("#createTemplateModalMessage").text("Validation errors in editor, please check fields in form.");
            $("#createTemplateModal").modal({ show: true });
        }
    }

    function setupDevelopmentEditors() {
        let editors = {};

        editors.inputServiceCheckRecord = ace.edit("developTemplateInputServiceCheckRecord", {
            mode: "ace/mode/json",
            minLines: 8,
            maxLines: 16
        });

        editors.inputServiceCheckRecord.setValue(SERVICE_CHECK_RECORD_DEFAULT);
        editors.inputServiceCheckRecord.clearSelection();

        editors.inputParameters = ace.edit("developTemplateInputParameters", {
            mode: "ace/mode/json",
            minLines: 1,
            maxLines: 20
        });

        editors.inputParameters.setValue("{}");
        editors.inputParameters.clearSelection();

        editors.template = ace.edit("developTemplateCode", {
            mode: "ace/mode/ejs",
            minLines: 1,
            maxLines: 50
        });

        editors.outputConsole = ace.edit("developOutputConsole", {
            mode: "ace/mode/text",
            minLines: 10,
            maxLines: 100,
            readOnly: true,
            showLineNumbers: false,
            printMargin: false,
            showGutter: false,
            showFoldWidgets: false
        });

        return Object.freeze(editors);
    }

    function editorAppendText(editor, text) {
        let currDate = new Date().toLocaleString('en-GB');
        editor.session.insert({
            row: editor.session.getLength(),
            column: 0
        }, `[${currDate}] ${text}\n`);
    }

    function updateCodeDiffStatus(templateEditor, codeEditors) {
        let template = templateEditor.getValue();

        if (editorHasDiff(template, codeEditors)) {
            $("#developDiffStatus").text("Unsaved changes");
            $("#developDiffStatus").addClass("alert-warning");
            $("#developDiffStatus").removeClass("alert-success");
        } else {
            $("#developDiffStatus").text("No differences with code fields in \"Edit Template\"");
            $("#developDiffStatus").removeClass("alert-warning");
            $("#developDiffStatus").addClass("alert-success");
        }
    }

    function updateSaveTimeLabel() {
        $("#developSaveTime").text(`Last saved at: ${new Date().toLocaleString("en-GB")}`);
    }

    function setupSaveInterval(value, interval) {
        // Ignores setting interval if readonly is set,
        // but this function usually is not called in this case
        if (disableRuleSourceEditing) {
            return;
        }

        switch (value) {
            case "disabled":
                clearInterval(autosaveInterval);
                break;
            case "interval":
                autosaveInterval = setInterval(function() {
                    $("#developSaveCode").click();
                }, interval * 1000);
                break;
        }
    }

    function editorHasDiff(data, editors) {
        for (const key in editors) {
            if (data[key] != editors[key].getValue()) {
                return true;
            }
        }

        return false;
    }
</script>
</body>
</html>
