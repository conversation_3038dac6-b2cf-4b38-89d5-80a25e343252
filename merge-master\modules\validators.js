'use strict';

import _ from 'lodash';

function isTimePeriod(param) {
    return /^(\d+[d|w|m|y])$/.test(param);
}

function isTimeInterval(param) {
    return /^([h|d|w|m|y])$/.test(param);
}

function isIPv4Address(param) {
    return /^(?:(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])(\.(?!$)|$)){4}$/.test(param);
}

function isAdborId(param) {
    return /^[1-9][0-9]{7,8}$/.test(param);
}

function isValidEndDate(date, { req }) {
    if (!(date instanceof Date)) {
        return true;
    }

    if (req.query.startDate instanceof Date) {
        return req.query.startDate < date;
    } else {
        return false;
    }
}

function isValidSourceData(param) {
    let sourceData;
    if (typeof param === 'object') {
        sourceData = param;
    } else if (typeof param === 'string') {
        try {
            sourceData = JSON.parse(param);
        } catch(error) {
            return false;
        }
    } else {
        return false;
    }

    if (!_.isPlainObject(sourceData) && sourceData !== null) {
        return false;
    }

    for (let key in sourceData) {
        if (typeof sourceData[key] !== 'object' || sourceData[key] === null) {
            return false;
        }
    }

    return true;
}


function isValidAdditionalParameters(param) {
    let additionalParameters;
    if (typeof param === 'object') {
        additionalParameters = param;
    } else if (typeof param === 'string') {
        try {
            additionalParameters = JSON.parse(param);
        } catch(error) {
            return false;
        }
    } else {
        return false;
    }

    if (!_.isPlainObject(additionalParameters) && additionalParameters !== null) {
        return false;
    }

    if (_.isPlainObject(additionalParameters)) {
        let dateFrom;
        let dateTo;

        if (additionalParameters.dateFrom !== undefined) {
            dateFrom = new Date(additionalParameters.dateFrom);

            if (isNaN(dateFrom)) {
                return false;
            }

            if (dateFrom > new Date()) {
                return false;
            }
        }

        if (additionalParameters.dateTo !== undefined) {
            dateTo = new Date(additionalParameters.dateTo);

            if (isNaN(dateTo)) {
                return false;
            }
        }

        if (!isNaN(dateFrom) && !isNaN(dateTo)) {
            // Invalid if dateTo is before dateFrom
            if (dateTo < dateFrom) {
                return false;
            }
        }
    }

    return true;
}


function createFilterQueryValidator(validKeys=[], valueType='string') {
    return function(param) {
        if (typeof param === 'string') {
            return true;
        }

        if (typeof param === 'object' && param !== null) {
            for (const key in param) {
                if (!validKeys.includes(key) || typeof param[key] !== 'string') {

                    return false;
                }

                switch (valueType) {
                    case 'number':
                        if (isNaN(parseFloat(param[key]))) {
                            throw new Error(`Value ${param[key]} could not be parsed as number.`);
                        }
                        break;
                    case 'integer':
                        if (isNaN(parseInt(param[key]))) {
                            throw new Error(`Value ${param[key]} could not be parsed as integer.`);
                        }
                        break;
                }
            }
            return true;
        }

        return false;
    }
}


function createFilterQuerySanitizer({ defaultOperator = 'equal', valueType = 'string' } = {}) {
    return function(param) {
        if (typeof param === 'string') {
            let value = param;
            param = {
                [defaultOperator]: value
            };
        } else if (typeof param === 'object') {
            for (const key in param) {
                switch (valueType) {
                    case 'number':
                        param[key] = parseFloat(param[key]);
                        break;
                    case 'integer':
                        param[key] = parseInt(param[key]);
                        break;
                }
            }
        }

        return param;
    }
}


function startDateSanitizer(date) {
    if (date) {
        // Returns date value if it's set, will be validated by the rest of the chain
        return date;
    }

    // Default date is set to 1 day before current date
    let lastDayDate = new Date();
    lastDayDate.setDate(lastDayDate.getDate() - 1);
    return lastDayDate;
}

export default {
    isTimePeriod: isTimePeriod,
    isTimeInterval: isTimeInterval,
    isIPv4Address: isIPv4Address,
    isAdborId: isAdborId,
    isValidEndDate: isValidEndDate,
    isValidSourceData: isValidSourceData,
    isValidAdditionalParameters: isValidAdditionalParameters,
    createFilterQueryValidator: createFilterQueryValidator,
    createFilterQuerySanitizer: createFilterQuerySanitizer,
    startDateSanitizer: startDateSanitizer
};
