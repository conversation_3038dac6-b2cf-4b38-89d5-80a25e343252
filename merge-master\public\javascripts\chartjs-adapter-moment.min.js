/*!
 * chartjs-adapter-moment v0.1.1
 * https://www.chartjs.org
 * (c) 2020 Chart.js Contributors
 * Released under the MIT license
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("moment"),require("chart.js")):"function"==typeof define&&define.amd?define(["moment","chart.js"],t):t((e=e||self).moment,e.Chart)}(this,(function(e,t){"use strict";e=e&&e.hasOwnProperty("default")?e.default:e;const n={datetime:"MMM D, YYYY, h:mm:ss a",millisecond:"h:mm:ss.SSS a",second:"h:mm:ss a",minute:"h:mm a",hour:"hA",day:"MMM D",week:"ll",month:"MMM YYYY",quarter:"[Q]Q - YYYY",year:"YYYY"};t._adapters._date.override("function"==typeof e?{_id:"moment",formats:function(){return n},parse:function(t,n){return"string"==typeof t&&"string"==typeof n?t=e(t,n):t instanceof e||(t=e(t)),t.isValid()?t.valueOf():null},format:function(t,n){return e(t).format(n)},add:function(t,n,f){return e(t).add(n,f).valueOf()},diff:function(t,n,f){return e(t).diff(e(n),f)},startOf:function(t,n,f){return t=e(t),"isoWeek"===n?t.isoWeekday(f).valueOf():t.startOf(n).valueOf()},endOf:function(t,n){return e(t).endOf(n).valueOf()}}:{})}));