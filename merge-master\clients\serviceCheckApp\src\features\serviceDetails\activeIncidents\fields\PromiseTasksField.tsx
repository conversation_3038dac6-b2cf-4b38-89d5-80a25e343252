import { CollapsablePanel } from '../../../../components/collapsablePanel/CollapsablePanel'
import { DetailField } from '../../../../components/detailsPanel/DetailField'
import { Panel } from '../../../../components/panel/Panel'
import { formatDate } from '../../../../helpers/formatDate'
import { PromiseTask } from '../../../../infrastructure/models'
import styles from './PromiseTasksField.module.scss'
interface PromiseTasksFieldProps {
    tasks: PromiseTask[]
}

export const PromiseTasksField = ({ tasks }: PromiseTasksFieldProps) => {
    if (tasks.length === 0) {
        return (
            <Panel>
                <DetailField label="Field Tasks" value="N/A" />
            </Panel>
        )
    }

    return (
        <CollapsablePanel
            headerElement={<DetailField label="Promise Tasks" value={''} />}
            canOpen={tasks.length > 0}
            itemCount={tasks.length}
        >
            <div className={styles.promiseTasksField}>
                <div className={styles.taskRow}>
                    {tasks.map((task, index) => (
                        <div key={`${task.crn}-${index}`}>
                            <DetailField
                                label="CRN"
                                value={task.crn || 'Unknown'}
                                inline={true}
                            />
                            <DetailField
                                label="SLA Date"
                                value={formatDate(task.slaDate, 'Unknown')}
                                inline={true}
                            />
                            <DetailField
                                label="Status"
                                value={task.status || 'Unknown'}
                                inline={true}
                            />
                        </div>
                    ))}
                </div>
            </div>
        </CollapsablePanel>
    )
}
