import mongoose from 'mongoose';
import ServiceCheckSchema from '../base/serviceCheckSchema.js';

const serviceCheckSchema = new ServiceCheckSchema();
serviceCheckSchema.set('minimize', false);


const BaseGeneratedServiceCheckModel = mongoose.model('generatedServiceCheck', serviceCheckSchema);


// Extends base service check model to set a default on the virtual field 'sourcesData'
export default class GeneratedServiceCheckModel extends BaseGeneratedServiceCheckModel {
    constructor(...args) {
        super(...args);
        // Sets sources data to default if it is selected
        if (!this.sourcesData && this.isSelected('sourcesData')) {
            this.sourcesData = {};
        }
    }
}
