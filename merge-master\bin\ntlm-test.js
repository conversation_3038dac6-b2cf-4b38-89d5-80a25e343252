var express = require('express'),
    ntlm = require('express-ntlm');

var app = express();

app.use(ntlm({
    debug: function() {
        var args = Array.prototype.slice.apply(arguments);
        console.log.apply(null, args);
    },
    domain: 'account-01',
    domaincontroller: 'ldap://ldap-account-01.puma.corp.telstra.com'      
}));

app.all('*', function(request, response) {
    response.end(JSON.stringify(request.ntlm)); // {"DomainName":"MYDOMAIN","UserName":"MYUSER","Workstation":"MYWORKSTATION"}
});

app.listen(3008);