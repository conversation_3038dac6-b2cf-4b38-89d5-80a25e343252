
import mongoose from 'mongoose';

const commandSchema = new mongoose.Schema({
    id: String,
    owner: { type: String, index: true },
    name:  { type: String, unique: true },
    permission: String,
    type: String,
    status: String,
    config: String,
    input: String,
    help: String,
    createdOn: Date,
    createdBy: String,
    updatedOn: Date,
    updatedBy: String
});

commandSchema.methods.toJSON = function() {
    let obj = this.toObject();
    delete obj._id;
    delete obj.__v;
    return obj;
}


export default mongoose.model('command', commandSchema);
