
import assert from 'assert';
import _ from 'lodash';

import Template from '../../db/model/template.js';
import mergeConfigList from '../../modules/mergeConfigList.js';
import { MERGE_OBJECT_CONFIG_FILES } from '../../modules/helpers/constants.js';
import helpers from '../helpers.js';


describe('Merge Templates', () => {
    it('Unique names', async() => {
        let templates = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.textTemplate)).textTemplates;
        let templateNames = new Set();

        for (let i in templates) {
            if (templateNames.has(templates[i].name)) {
                assert.fail(`Duplicate template name ${templates[i].name} in config`);
            }
            templateNames.add(templates[i].name);
        };
    });

    it('Valid fields', async() => {
        let templates = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.textTemplate)).textTemplates;

        for (let i in templates) {
            let template = new Template(templates[i]);
            try {
                await template.validate();
            } catch(error) {
                let newError = new Error(`Error validating template ${template.name}`);
                newError.original_error = error;
                newError.stack = error.stack;
                throw newError;
            }

        }
    });

    it('Ordered by name ascending', async () => {
        let templates = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.textTemplate)).textTemplates;

        for (let i = 1; i < templates.length; i++) {
            let prevTemplate = templates[i - 1];
            let currTemplate = templates[i];

            if (prevTemplate.name.localeCompare(currTemplate.name, 'en') > 0) {
                assert.fail(`Template name ${prevTemplate.name} is out of order, should be after template ${currTemplate.name}`);
            }
        }
    });

    it('Correct field order', async() => {
        let templates = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.textTemplate)).textTemplates;
        let invalidNames = [];

        for (let i in templates) {
            let templateJson = new Template(templates[i]).toJSON();
            delete templateJson.createdBy;
            delete templateJson.createdOn;

            if (!_.isEqualWith(templates[i], templateJson, helpers.compareObjectWithKeyOrder)) {
                invalidNames.push(templates[i].name);

            };
        }

        if (invalidNames.length) {
            assert.fail(`Key order for template(s) ${invalidNames.join(',')} does not match database schema order`);
        }
    });
});

