<%- include('header', {}); %>
<div class="container">
    <br>
    <div class="jumbotron">
        <span class="float-right"><%= global.gConfig.env %> System</span>
        <h2 class="display-4"><%= title  %></h2>
        <% if (typeof errorMessage === 'string') { %>
        <br>
        <div class="alert alert-danger"><%= errorMessage %></div>
        <br><br>
        <% } %>
        <div id="login" class="">
            <form action="/auth/login" method="post">
                <div class="form-group">
                  <label for="inputUserName">User Name</label>
                  <input type="text" class="form-control" name="username" id="inputUserName" aria-describedby="emailHelp" placeholder="User Name">
                  <small id="UserNameHelp" class="form-text text-muted">Use your account-01 username (d or c number)</small>
                </div>
                <div class="form-group">
                  <label for="exampleInputPassword1">Password</label>
                  <input type="password" class="form-control" name="password" id="exampleInputPassword1" placeholder="Password">
                  <small id="UserNameHelp" class="form-text text-muted">Your Telstra Account-01 password</small>
                </div>
                <!--
                <div class="form-group form-check">
                  <input type="checkbox" class="form-check-input" id="RememberMe" name="rememberMe">
                  <label class="form-check-label" for="RememberMe">Remember Me</label>
                </div>
                -->
                <button type="submit" class="btn btn-primary">Login</button>
                <small class="form-text text-muted">Merge access can be requested in AGS. To find how to apply for access, please refer to <a href="https://confluence.tools.telstra.com/display/MERGE/Merge+Access">here</a>. For more information about Merge, refer to Merge Wiki <a href="https://confluence.tools.telstra.com/display/MERGE">here</a>.</small>
            </form>
        </div>
    </div>
</div>
<%- include('footer', {}); %>