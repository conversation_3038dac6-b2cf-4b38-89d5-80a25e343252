import { ActionButtonValidation, TextStyle } from '@able/react'
import { useFullServiceCheckRun } from '../../api/sockets/useFullServiceCheckRun'
import { useUser } from '../../context/UserContext'
import { useAppState } from '../../hooks/useAppState'
import { Panel } from '../panel/Panel'
import styles from './FunctionNotReady.module.scss'

export const FunctionNotReady = () => {
    const { runFullServiceCheck } = useFullServiceCheckRun()
    const { appState } = useAppState()
    const { canModify } = useUser()
    return (
        <div className={styles.functionNotReady}>
            <Panel>
                <div className={styles.details}>
                    <TextStyle>
                        Function is not ready to be used. A full service check
                        must be performed to determine details.
                    </TextStyle>
                    <ActionButtonValidation
                        actionButtonEvents={{ onClick: runFullServiceCheck }}
                        className={styles.runButton}
                        label={'Run full check'}
                        state={
                            canModify(appState.createdBy)
                                ? 'Normal'
                                : 'Attention'
                        }
                        validationMessage={
                            canModify(appState.createdBy)
                                ? ''
                                : 'You must be create user of the service check to perform this action.'
                        }
                        developmentUrl="/public/images/able-sprites.svg"
                    />
                </div>
            </Panel>
        </div>
    )
}
