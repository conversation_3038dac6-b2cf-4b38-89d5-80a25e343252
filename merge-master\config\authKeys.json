{"NBNOKAPI": {"url": "`${global.gConfig.APIUriConfig.NBNOKAPI.baseURI}/v2/oauth/token`", "body": "`grant_type=client_credentials&client_id=${global.gConfig.NBNOKAPIClientKey}&client_secret=${global.gConfig.NBNOKAPIClientSecret}&scope=DIAGNOSTICS`", "header": "{\"content-type\":\"application/x-www-form-urlencoded\"}", "timeout": 60000, "apiAuthKey": "response.data.access_token", "authType": "Bearer", "cronTime": "0 */25 * * * *", "masslCertificateName": "OKAPIClient", "active": true, "requiredTokens": []}, "CDRLive": {"url": "`${global.gConfig.APIUriConfig.CDRLive.baseURI}/BDlive.API/api/v1/users/token`", "body": "`username=${global.gConfig.CDRLiveUsername}&password=${global.gConfig.CDRLivePassword}`", "timeout": 60000, "header": "{\"content-type\":\"application/x-www-form-urlencoded\"}", "apiAuthKey": "typeof response.data === 'string' ? JSON.parse(response.data).Data.Token : response.data.Data.Token", "authType": "Bearer", "cronTime": "0 */25 * * * *", "active": true, "requiredTokens": []}, "ServiceHealth": {"url": "`${global.gConfig.APIUriConfig.ServiceHealth.baseURI}/v2/oauth/token`", "body": "`grant_type=client_credentials&client_id=${global.gConfig.ServiceHealthClientKey}&client_secret=${global.gConfig.ServiceHealthClientSecret}`", "header": "{\"content-type\":\"application/x-www-form-urlencoded\"}", "timeout": 60000, "apiAuthKey": "response.data.access_token", "authType": "Bearer", "cronTime": "0 */25 * * * *", "active": true, "masslCertificateName": "OKAPIClient", "proxyRequired": false, "requiredTokens": []}, "PromiseAPI": {"url": "`${global.gConfig.APIUriConfig.PromiseAPI.baseURI}/v2/oauth/token`", "body": "`client_id=${global.gConfig.PromiseClientKey}&client_secret=${global.gConfig.PromiseClientSecret}&grant_type=client_credentials`", "header": "{\"content-type\":\"application/x-www-form-urlencoded\"}", "timeout": 60000, "apiAuthKey": "response.data.access_token", "authType": "Bearer", "cronTime": "0 */25 * * * *", "active": true, "masslCertificateName": "OKAPIClient", "proxyRequired": false, "requiredTokens": []}, "BSIPAPI": {"url": "`${global.gConfig.APIUriConfig.BSIPAPI.baseURI}/v2/oauth/token`", "body": "`client_id=${global.gConfig.BSIPClientKey}&client_secret=${global.gConfig.BSIPClientSecret}&grant_type=client_credentials`", "header": "{\"content-type\":\"application/x-www-form-urlencoded\"}", "timeout": 60000, "apiAuthKey": "response.data.access_token", "authType": "Bearer", "cronTime": "0 */25 * * * *", "active": true, "masslCertificateName": "OKAPIClient", "proxyRequired": false, "requiredTokens": []}, "B2BAPI": {"url": "`${global.gConfig.APIUriConfig.B2BAPI.baseURI}/v2/oauth/token`", "body": "`client_id=${global.gConfig.B2BClientKey}&client_secret=${global.gConfig.B2BClientSecret}&grant_type=client_credentials&scope=Inventory-Merge`", "header": "{\"content-type\":\"application/x-www-form-urlencoded\"}", "timeout": 60000, "apiAuthKey": "response.data.access_token", "authType": "Bearer", "cronTime": "0 */25 * * * *", "active": true, "masslCertificateName": "OKAPIClient", "proxyRequired": false, "requiredTokens": []}, "SMDMAPI": {"url": "`${global.gConfig.APIUriConfig.SMDMAPI.baseURI}/v2/oauth/token`", "body": "`client_id=${global.gConfig.SMDMClientKey}&client_secret=${global.gConfig.SMDMClientSecret}&grant_type=client_credentials&scope=SERVICEMANAGEMENT`", "header": "{\"content-type\":\"application/x-www-form-urlencoded\"}", "timeout": 60000, "apiAuthKey": "response.data.access_token", "authType": "Bearer", "cronTime": "0 */25 * * * *", "active": true, "masslCertificateName": "OKAPIClient", "proxyRequired": false, "requiredTokens": []}, "NAASAPI": {"url": "`${global.gConfig.APIUriConfig.NAASAPI.baseURI}/token`", "body": "`client_id=${global.gConfig.NAASClientKey}&client_secret=${global.gConfig.NAASClientSecret}&grant_type=client_credentials`", "header": "{\"content-type\":\"application/x-www-form-urlencoded\"}", "timeout": 60000, "apiAuthKey": "response.data.access_token", "authType": "Bearer", "cronTime": "0 */25 * * * *", "active": true, "masslCertificateName": "OKAPIClient", "proxyRequired": false, "requiredTokens": []}, "NBNDiagnostics": {"url": "`${global.gConfig.APIUriConfig.NBNDiagnostics.baseURI}/v2/oauth/token`", "body": "`grant_type=client_credentials&client_id=${global.gConfig.NBNDiagnosticsClientKey}&client_secret=${global.gConfig.NBNDiagnosticsClientSecret}`", "header": "{\"content-type\":\"application/x-www-form-urlencoded\"}", "timeout": 60000, "apiAuthKey": "response.data.access_token", "authType": "Bearer", "cronTime": "0 */25 * * * *", "active": true, "masslCertificateName": "OKAPIClient", "proxyRequired": false, "requiredTokens": []}, "InternetDirect": {"url": "`${global.gConfig.APIUriConfig.InternetDirect.authURI}`", "body": "`grant_type=client_credentials&client_id=${global.gConfig.InternetDirectClientKey}&client_secret=${global.gConfig.InternetDirectClientSecret}`", "header": "{\"content-type\":\"application/x-www-form-urlencoded\"}", "timeout": 60000, "apiAuthKey": "response.data.access_token", "authType": "Bearer", "cronTime": "0 */25 * * * *", "active": true, "masslCertificateName": "OKAPIClient", "proxyRequired": false, "requiredTokens": []}, "MagpieAPI": {"url": "`${global.gConfig.APIUriConfig.MagpieAPI.authURI}`", "body": "`grant_type=client_credentials&client_id=${global.gConfig.MagpieAPIClientKey}&client_secret=${global.gConfig.MagpieAPIClientSecret}&scope=READFNN`", "header": "{\"content-type\":\"application/x-www-form-urlencoded\"}", "timeout": 60000, "apiAuthKey": "response.data.access_token", "authType": "Bearer", "cronTime": "0 */25 * * * *", "active": true, "masslCertificateName": "OKAPIClient", "proxyRequired": true, "requiredTokens": []}, "ScienceLogicOKAPI": {"url": "`${global.gConfig.APIUriConfig.PatTsoApi.okapiAuthURI}`", "body": "`grant_type=client_credentials&client_id=${global.gConfig.PatTsoClientKey}&client_secret=${global.gConfig.PatTsoClientSecret}`", "header": "{\"content-type\":\"application/x-www-form-urlencoded\"}", "timeout": 60000, "apiAuthKey": "response.data.access_token", "authType": "Bearer", "cronTime": "0 */25 * * * *", "active": true, "masslCertificateName": "OKAPIClient", "proxyRequired": false, "requiredTokens": []}, "ScienceLogicTsoAPI": {"url": "`${global.gConfig.APIUriConfig.PatTsoApi.patBaseURI}/login`", "body": "new Object({ \"username\": global.gConfig.PatTsoUsername, \"password\": global.gConfig.PatTsoPassword })", "header": "{ \"content-type\": \"application/json\" }", "timeout": 60000, "apiAuthKey": "response.headers['authentication-token']", "authType": null, "cronTime": "0 1,26,51 * * * *", "active": true, "masslCertificateName": "OKAPIClient", "proxyRequired": false, "requiredTokens": [{"name": "ScienceLogicOKAPI", "header": "Authorization"}]}}