'use strict';

import _ from 'lodash';
import ivm from 'isolated-vm';

import Api from '../db/model/api.js';
import Source from '../db/model/source.js';
import ServiceCheckModel from '../db/model/serviceCheck.js';
import mergeCollect from '../modules/mergeCollect.js';
import authEmitter from '../modules/authEmitter.js';
import constants from '../modules/helpers/constants.js';
import { ApiUriError } from '../modules/error.js';
import { InputTypes, ProductTypes, SourceStatus } from '../modules/enumerations.js';
import { disableDisplayCondition } from '../modules/serviceCheck.js';
import mergeLib from '../modules/mergeLib.js';


export function socketListen(socket) {
    socket.on('sourceDev:preCondition', async function (data) {
        if (!_.isPlainObject(data)) {
            socket.emit('sourceDev:preConditionResult', {
                error: new TypeError("Input data from socket was not a plain object").toString(),
                message: 'Error with input data'
            });
            return;
        }

        let source = data.source;
        let variablesCode = data.variables;
        let SCr = new ServiceCheckModel();

        try {
            await new Source(source).validate();
        } catch(error) {
            socket.emit('sourceDev:preConditionResult', {
                error: error.toString(),
                message: 'Error encountered while validating input source'
            });
            return;
        }

        const isolate = new ivm.Isolate({
            memoryLimit: 32
        });
        let sourceVariableContext = await isolate.createContext();
        await sourceVariableContext.global.set('InputTypes', new ivm.ExternalCopy(InputTypes).copyInto());
        await sourceVariableContext.global.set('ProductTypes', new ivm.ExternalCopy(ProductTypes).copyInto());

        try {
            try {
                await sourceVariableContext.eval(variablesCode, { timeout: 2000 });

                let data = await sourceVariableContext.global.get('data', { copy: true });
                let r = await sourceVariableContext.global.get('r', { copy: true });
                let s = await sourceVariableContext.global.get('s', { copy: true });

                // Currently only sets specific functions from mergeLib
                // TODO: implement dynamic loading of all functions from merge lib into the isolate context
                await sourceVariableContext.global.set('checkIsValidPhoneNumber', function(...args) {
                    return mergeLib.checkIsValidPhoneNumber(...args);
                });
                await sourceVariableContext.global.set('checkIsMobilePhoneNumber', function(...args) {
                    return mergeLib.checkIsMobilePhoneNumber(...args);
                });
                await sourceVariableContext.global.set('formatE164PhoneNumber', function (...args) {
                    return mergeLib.formatE164PhoneNumber(...args);
                });
                await sourceVariableContext.global.set('formatNationalPhoneNumber', function (...args) {
                    return mergeLib.formatNationalPhoneNumber(...args);
                });

                if (data === undefined) {
                    throw ReferenceError("data is undefined");
                }
                if (r === undefined) {
                    throw ReferenceError("r is undefined");
                }
                if (s === undefined) {
                    throw ReferenceError("s is undefined");
                }

                if (!_.isPlainObject(data)) {
                    throw ReferenceError("data is not an object");
                }
                if (!_.isPlainObject(r)) {
                    throw ReferenceError("r is not an object");
                }
                if (!_.isPlainObject(s)) {
                    throw ReferenceError("s is not an object");
                }

                if (!_.isPlainObject(data.rulesData)) {
                    data.rulesData = {};
                }
                if (!_.isPlainObject(data.sourcesData)) {
                    data.sourcesData = {};
                }
                if (!_.isPlainObject(data.sourcesMetadata)) {
                    data.sourcesMetadata = {};
                }

                Object.assign(SCr, data);
                // Set rules data in variables sandbox only if the specified r, s values are not
                // empty objects or null / undefined
                if (_.isEmpty(r)) {
                    r = SCr.rulesData ? SCr.rulesData : {};
                } else {
                    SCr.rulesData = r;
                }
                if (_.isEmpty(s)) {
                    s = SCr.sourcesData ? SCr.sourcesData : {};
                } else {
                    SCr.sourcesData = s;
                }

                // Copies processed rulesData / sourcesData / service check record data
                // back into isolate context
                await sourceVariableContext.global.set('data', new ivm.ExternalCopy(SCr.toJSON()).copyInto());
                await sourceVariableContext.global.set('r', sourceVariableContext.global.getSync('data').getSync('rulesData').derefInto());
                await sourceVariableContext.global.set('s', sourceVariableContext.global.getSync('data').getSync('sourcesData').derefInto());
            } catch(error) {
                socket.emit('sourceDev:preConditionResult', {
                    error: error.toString(),
                    message: 'Error encountered while attempting to evaluate variables'
                });
                return;
            }

            try {
                let preCondResult;

                if (source.preCondition) {
                    preCondResult = await sourceVariableContext.eval(source.preCondition, { copy: true, timeout: 2000 });
                } else {
                    preCondResult = true;
                }

                let data = await sourceVariableContext.global.get('data', { copy: true });

                socket.emit('sourceDev:preConditionResult', {
                    error: null,
                    message: 'Condition to collect run successful',
                    preConditionResult: preCondResult,
                    preConditionIsUndefined: preCondResult === undefined,
                    variables: {
                        data: data
                    }
                });
            } catch(error) {
                socket.emit('sourceDev:preConditionResult', {
                    error: error.toString(),
                    message: 'Error encountered while running condition to collect code'
                });
            }
        } finally {
            sourceVariableContext.release();
            if (!isolate.isDisposed) {
                isolate.dispose();
            }
        }
    });

    socket.on('sourceDev:run', async function (data) {
        if (!_.isPlainObject(data)) {
            socket.emit('sourceDev:runResult', {
                error: new TypeError("Input data from socket was not a plain object").toString(),
                message: 'Error with input data'
            });
            return;
        }

        let source = data.source;
        let variablesCode = data.variables;
        let SCr = new ServiceCheckModel();

        try {
            await new Source(source).validate();
        } catch(error) {
            socket.emit('sourceDev:runResult', {
                error: error.toString(),
                message: 'Error encountered while validating input source'
            });
            return;
        }

        const isolate = new ivm.Isolate({
            memoryLimit: 32
        });
        let sourceVariableContext = await isolate.createContext();
        await sourceVariableContext.global.set('InputTypes', new ivm.ExternalCopy(InputTypes).copyInto());
        await sourceVariableContext.global.set('ProductTypes', new ivm.ExternalCopy(ProductTypes).copyInto());

        let sourceErrorMessageContext = await isolate.createContext();


        try {
            try {
                await sourceVariableContext.eval(variablesCode, { timeout: 2000 });

                let data = await sourceVariableContext.global.get('data', { copy: true });
                let r = await sourceVariableContext.global.get('r', { copy: true });
                let s = await sourceVariableContext.global.get('s', { copy: true });

                // Currently only sets specific functions from mergeLib
                // TODO: implement dynamic loading of all functions from merge lib into the isolate context
                await sourceVariableContext.global.set('checkIsValidPhoneNumber', function(...args) {
                    return mergeLib.checkIsValidPhoneNumber(...args);
                });
                await sourceVariableContext.global.set('checkIsMobilePhoneNumber', function(...args) {
                    return mergeLib.checkIsMobilePhoneNumber(...args);
                });
                await sourceVariableContext.global.set('formatE164PhoneNumber', function (...args) {
                    return mergeLib.formatE164PhoneNumber(...args);
                });
                await sourceVariableContext.global.set('formatNationalPhoneNumber', function (...args) {
                    return mergeLib.formatNationalPhoneNumber(...args);
                });

                if (data === undefined) {
                    throw ReferenceError("data is undefined");
                }
                if (r === undefined) {
                    throw ReferenceError("r is undefined");
                }
                if (s === undefined) {
                    throw ReferenceError("s is undefined");
                }

                if (!_.isPlainObject(data)) {
                    throw ReferenceError("data is not an object");
                }
                if (!_.isPlainObject(r)) {
                    throw ReferenceError("r is not an object");
                }
                if (!_.isPlainObject(s)) {
                    throw ReferenceError("s is not an object");
                }

                if (!_.isPlainObject(data.rulesData)) {
                    data.rulesData = {};
                }
                if (!_.isPlainObject(data.sourcesData)) {
                    data.sourcesData = {};
                }
                if (!_.isPlainObject(data.sourcesMetadata)) {
                    data.sourcesMetadata = {};
                }

                Object.assign(SCr, data);
                // Set rules data in variables sandbox only if the specified r, s values are not
                // empty objects or null / undefined
                if (_.isEmpty(r)) {
                    r = SCr.rulesData ? SCr.rulesData : {};
                } else {
                    SCr.rulesData = r;
                }
                if (_.isEmpty(s)) {
                    s = SCr.sourcesData ? SCr.sourcesData : {};
                } else {
                    SCr.sourcesData = s;
                }

                SCr.sourcesMetadata = {
                    [source.name]: {}
                };

                await sourceVariableContext.global.set('data', new ivm.ExternalCopy(SCr.toJSON()).copyInto());
                await sourceVariableContext.global.set('r', sourceVariableContext.global.getSync('data').getSync('rulesData').derefInto());
                await sourceVariableContext.global.set('s', sourceVariableContext.global.getSync('data').getSync('sourcesData').derefInto());
            } catch(error) {
                socket.emit('sourceDev:runResult', {
                    error: error.toString(),
                    message: 'Error encountered while attempting to evaluate variables',
                    variables: {
                        data: SCr.toJSON(),
                        parameters: null
                    },
                });
                return;
            }

            if (source.preCondition) {
                try {
                    SCr.sourcesMetadata[source.name].preCondResult = await sourceVariableContext.eval(source.preCondition, { copy: true, timeout: 2000 });
                } catch(error) {
                    SCr.sourcesMetadata[source.name].preCondResult = false;
                    SCr.sourcesMetadata[source.name].status = SourceStatus.preConditionError;

                    // Note: Any service check fields modified inside the precondition code won't apply
                    // here as they aren't copied from the sourceVariableContext, this is intentional

                    socket.emit('sourceDev:runResult', {
                        error: error.toString(),
                        message: 'Error encountered while running preCondition code',
                        variables: {
                            data: SCr.toJSON(),
                            parameters: null
                        }
                    });

                    return;
                }
            } else {
                SCr.sourcesMetadata[source.name].preCondResult = true;
            }

            let api = null;

            if (SCr.sourcesMetadata[source.name].preCondResult) {
                let parameters = {};

                try {
                    // Note: Retrieval of API could be moved to a $lookup with the source
                    // Also if this fails it will be counted as a parameter error
                    api = await Api.findOne({ name: source.api });

                    await sourceVariableContext.eval(source.param, { timeout: 2000 });

                    if (api) {
                        for (let parameterName of api.parameters) {
                            let parameterValue = await sourceVariableContext.global.get(parameterName, { copy: true });
                            if (parameterValue !== undefined) {
                                parameters[parameterName] = parameterValue;
                            }
                        }
                    }

                    SCr.sourcesMetadata[source.name].parameters = parameters;
                } catch(error) {
                    SCr.sourcesMetadata[source.name].preCondResult = false;
                    SCr.sourcesMetadata[source.name].status = SourceStatus.parameterError;

                    socket.emit('sourceDev:runResult', {
                        error: error.toString(),
                        message: 'Error encountered while running API parameters code',
                        variables: {
                            data: SCr.toJSON(),
                            parameters: parameters
                        }
                    });

                    return;
                }

                try {
                    if (api === null) {
                        throw new Error(`API ${source.api} does not exist`);
                    }

                    SCr.sourcesMetadata[source.name].status = SourceStatus.running;

                    let result = await mergeCollect.collectApi(api, parameters);

                    SCr.sourcesMetadata[source.name].status = SourceStatus.collected;
                    SCr.sourcesMetadata[source.name].collectionDuration = result.collectionDuration;
                    SCr.sourcesMetadata[source.name].statusCode = result.status;
                    SCr.sourcesMetadata[source.name].updatedOn = new Date().toISOString();
                    SCr.sourcesData[source.name] = result.data;

                    let disableDisplayMessage = disableDisplayCondition(SCr, socket.request.user);
                    if (disableDisplayMessage) {
                        socket.emit('sourceDev:runResult', {
                            error: [disableDisplayMessage.message, disableDisplayMessage.link].join(' '),
                            message: 'Disable display condition error'
                        });
                        return;
                    }

                    socket.emit('sourceDev:runResult', {
                        message: 'Source collection complete',
                        variables: {
                            data: SCr.toJSON(),
                            parameters: parameters
                        }
                    });
                } catch(error) {
                    if (error.message == constants.PROXY_AUTH_ERROR_MESSAGE) {
                        authEmitter.emit('AuthError');
                    }

                    let overrideErrorResult = false;

                    if (error instanceof ApiUriError) {
                        SCr.sourcesMetadata[source.name].status = SourceStatus.apiUriError;
                    } else {
                        SCr.sourcesMetadata[source.name].status = SourceStatus.error;
                    }

                    if (error.result) {
                        SCr.sourcesMetadata[source.name].collectionDuration = error.result.collectionDuration;
                        SCr.sourcesMetadata[source.name].statusCode = error.result.status;
                        SCr.sourcesMetadata[source.name].updatedOn = new Date().toISOString();
                        SCr.sourcesData[source.name] = error.result.data;

                        let disableDisplayMessage = disableDisplayCondition(SCr, socket.request.user);
                        if (disableDisplayMessage) {
                            socket.emit('sourceDev:runResult', {
                                error: [disableDisplayMessage.message, disableDisplayMessage.link].join(' '),
                                message: 'Disable display condition error'
                            });
                            return;
                        }

                        // Adds the result from error as 'response' in the vm sandbox
                        await sourceErrorMessageContext.global.set('response', new ivm.ExternalCopy(error.result).copyInto());

                        if (source.overrideErrorCondition) {
                            try {
                                overrideErrorResult = await sourceErrorMessageContext.eval(source.overrideErrorCondition, { copy: true, timeout: 2000 });
                            } catch(error) {
                                socket.emit('sourceDev:runResultCodeError', {
                                    error: error.toString(),
                                    message: 'Error encountered while running override error condition code'
                                });
                                return;
                            }
                        }
                    } else {
                        await sourceErrorMessageContext.global.set('response', new ivm.ExternalCopy({}).copyInto());
                    }

                    if (source.errorMessage) {
                        try {
                            let errorMessageSummary = await sourceErrorMessageContext.eval(source.errorMessage, { copy: true, timeout: 2000 });

                            if (!_.isNil(errorMessageSummary)) {
                                SCr.sourcesMetadata[source.name].errorMessageSummary = _.toString(errorMessageSummary);
                            }
                        } catch(error) {
                            socket.emit('sourceDev:runResultCodeError', {
                                error: error.toString(),
                                message: 'Error encountered while running error message code'
                            });
                            return;
                        }
                    }

                    // If the evaluation of the overrideErrorCondition expression is true
                    // in a boolean context, change the status of the source to Collected
                    if (overrideErrorResult) {
                        SCr.sourcesMetadata[source.name].status = SourceStatus.collected;

                        socket.emit('sourceDev:runResult', {
                            message: 'Source collection complete',
                            variables: {
                                data: SCr.toJSON(),
                                parameters: parameters
                            }
                        });
                    } else {
                        SCr.sourcesMetadata[source.name].error = error.toString();

                        socket.emit('sourceDev:runResult', {
                            error: error.toString(),
                            message: 'Error encountered while collecting source',
                            variables: {
                                data: SCr.toJSON(),
                                parameters: parameters
                            }
                        });
                    }
                }
            } else {
                SCr.sourcesMetadata[source.name].status = SourceStatus.preConditionFalse;

                socket.emit('sourceDev:runResult', {
                    message: 'Source collection skipped: preCondition was not fulfilled',
                    variables: {
                        data: SCr.toJSON(),
                        parameters: null
                    }
                });
            }
        } finally {
            sourceVariableContext.release();
            sourceErrorMessageContext.release();
            if (!isolate.isDisposed) {
                isolate.dispose();
            }
        }
    });
}