'use strict';

import mongoose from 'mongoose';
import chai from 'chai';
import chaiHttp from 'chai-http';
import chaiLike from 'chai-like';
import chaiDateTime from 'chai-datetime';
import superagentDefaults from 'superagent-defaults';
import supertest from 'supertest';
import cookie from 'cookie';
import _ from 'lodash';
import assert from 'assert';

var app;
var request;
import config from '../config.js';
import Rule from '../../db/model/rule.js';
import helpers from '../helpers.js';
import { UNIT_TEST_ALL_ACCESS_USERNAME, UNIT_TEST_USERS } from '../users.js';

const should = chai.should();

chaiLike.extend(helpers.dateFieldCheck);
chaiLike.extend(helpers.regexCheck);
chai.use(chaiLike);
chai.use(chaiHttp);
chai.use(chaiDateTime);


const RULE_KEYS = Object.freeze(Object.keys(Rule.schema.obj));


describe('Merge Rules REST endpoints', () => {
    // Setup and connect to mongodb memory server, start merge app instance
    // Set request variable with a configured supertest agent with test JWT and app instance
    before(async function() {
        global.gConfig = _.cloneDeep(config.default);
        app = (await import('../../app.js')).default;
        request = superagentDefaults(supertest(app));
    });

    // Drop all contents of database before each test
    beforeEach(async function() {
        global.gConfig = _.cloneDeep(config.default);

        // Uses deleteMany instead of mongoose.connection.db.dropDatabase() because
        // custom indexes appear to be deleted if dropDatabase() is used
        for (let collectionName in mongoose.connection.collections) {
            await mongoose.connection.collections[collectionName].deleteMany({});
        }

        await helpers.authenticateSession(request, UNIT_TEST_ALL_ACCESS_USERNAME);
    });

    describe('Read list of rules', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/rules');

            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/rules');

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Get empty list of rules', (done) => {
            request.get('/rules')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(200);

                chai.expect(res.body).to.eql({
                    metadata: {
                        pagination: {
                            limit: 100,
                            offset: 0,
                            total: 0,
                            prev: null,
                            next: null
                        }
                    },
                    results: []
                });

                done();
            });
        });

        it('Get list of rules with one rule (default values)', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/rules')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR001'
                    }]);

                    res.body.results.forEach((rule) => {
                        chai.expect(rule).to.have.all.keys(RULE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get list of rules with three rules (default values)', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001'
            }).save());

            promises.push(new Rule({
                name: 'MDR002'
            }).save());

            promises.push(new Rule({
                name: 'MDR003'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/rules')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR001'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR002'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR003'
                    }]);

                    res.body.results.forEach((rule) => {
                        chai.expect(rule).to.have.all.keys(RULE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get list of rules with one rule', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001',
                active: true,
                hideInResult: false,
                showReject: false,
                requiresReview: false,
                includedInitialServiceCheck: false,
                title: 'Test Rule',
                rootCauseCategory: 'Data Collection',
                level: 2,
                description: 'Rule for unit tests',
                wikiPage: 'N/A',
                ruleType: 'Extract',
                isWarning: false,
                displayInSummary: false,
                failedInSummary: true,
                preSources: [
                    'MAGPIE'
                ],
                preSourcesRunOnFail: true,
                preRules: [],
                preRulesRunOnFail: false,
                runWhenDependenciesResolved: false,
                depedenciesFromRelatedServiceChecks: false,
                runForEachPreSource: false,
                preCondition: 's.MAGPIE',
                preConditionMsg: '',
                trueMsg: 'This is true',
                falseMsg: 'This is false',
                errorMsg: 'This is an error message',
                ruleCode: 'data.success = s.MAGPIE.success',
                valueMsgStm: 'message',
                extraInfo: 'No extra info',
                unitTests: [],
                action: {
                    api: 'TestApi',
                    parameterCode: '',
                    codeCondition: 'true;',
                    autoExecution: true,
                },
                ignoreCompare: false,
                overrideCompare: '',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            }).save());

            Promise.all(promises).then(() => {
                request.get('/rules')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: true,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: 'Test Rule',
                        rootCauseCategory: 'Data Collection',
                        level: 2,
                        description: 'Rule for unit tests',
                        wikiPage: 'N/A',
                        ruleType: 'Extract',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [
                            'MAGPIE'
                        ],
                        preSourcesRunOnFail: true,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: 's.MAGPIE',
                        preConditionMsg: '',
                        trueMsg: 'This is true',
                        falseMsg: 'This is false',
                        errorMsg: 'This is an error message',
                        ruleCode: 'data.success = s.MAGPIE.success',
                        valueMsgStm: 'message',
                        extraInfo: 'No extra info',
                        unitTests: [],
                        action: {
                            api: 'TestApi',
                            parameterCode: '',
                            codeCondition: 'true;',
                            autoExecution: true,
                        },
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                        name: 'MDR001'
                    }]);

                    res.body.results.forEach((rule) => {
                        chai.expect(rule).to.have.all.keys(RULE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get list of rules with two rules', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001',
                active: true,
                hideInResult: false,
                showReject: false,
                requiresReview: false,
                includedInitialServiceCheck: false,
                title: 'Test Rule',
                rootCauseCategory: 'Data Collection',
                level: 2,
                description: 'Rule for unit tests',
                wikiPage: 'N/A',
                ruleType: 'Extract',
                isWarning: false,
                displayInSummary: false,
                failedInSummary: true,
                preSources: [
                    'MAGPIE'
                ],
                preSourcesRunOnFail: true,
                preRules: [],
                preRulesRunOnFail: false,
                runWhenDependenciesResolved: false,
                depedenciesFromRelatedServiceChecks: false,
                runForEachPreSource: false,
                preCondition: 's.MAGPIE',
                preConditionMsg: '',
                trueMsg: 'This is true',
                falseMsg: 'This is false',
                errorMsg: 'This is an error message',
                ruleCode: 'data.success = s.MAGPIE.success',
                valueMsgStm: 'message',
                extraInfo: 'No extra info',
                unitTests: [],
                action: {
                    api: 'Api1',
                    parameterCode: 'fnn = `${data.fnn}`;',
                    codeCondition: '"valid";',
                    autoExecution: true,
                },
                ignoreCompare: false,
                overrideCompare: '',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            }).save());

            promises.push(new Rule({
                name: 'MDR002',
                active: false,
                hideInResult: true,
                showReject: true,
                requiresReview: false,
                includedInitialServiceCheck: false,
                title: 'Test Rule 2',
                rootCauseCategory: 'Ticket',
                level: 6,
                description: 'A second rule for unit tests',
                wikiPage: 'N/A',
                ruleType: 'Logical',
                isWarning: true,
                displayInSummary: false,
                failedInSummary: false,
                preSources: [
                    'ODIN',
                    'RASSP'
                ],
                preSourcesRunOnFail: false,
                preRules: [
                    'MDR000',
                    'MDR001',
                    'MTR001'
                ],
                preRulesRunOnFail: true,
                runWhenDependenciesResolved: false,
                depedenciesFromRelatedServiceChecks: false,
                runForEachPreSource: false,
                preCondition: 's.ODIN && s.RASSP',
                preConditionMsg: 'Failed because sources had no data',
                trueMsg: 'True message here',
                falseMsg: 'False message here',
                errorMsg: '"Could not obtain data: " + s.ODIN',
                ruleCode: 'data.field = s.ODIN.field;',
                valueMsgStm: 'test message',
                extraInfo: '',
                unitTests: [],
                action: {
                    api: 'Api2',
                    parameterCode: 'uri = `test/path/to/poll`;',
                    codeCondition: '60;',
                    autoExecution: false,
                },
                ignoreCompare: false,
                overrideCompare: '',
                createdOn: currDate,
                createdBy: 'merge'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/rules')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: true,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: 'Test Rule',
                        rootCauseCategory: 'Data Collection',
                        level: 2,
                        description: 'Rule for unit tests',
                        wikiPage: 'N/A',
                        ruleType: 'Extract',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [
                            'MAGPIE'
                        ],
                        preSourcesRunOnFail: true,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: 's.MAGPIE',
                        preConditionMsg: '',
                        trueMsg: 'This is true',
                        falseMsg: 'This is false',
                        errorMsg: 'This is an error message',
                        ruleCode: 'data.success = s.MAGPIE.success',
                        valueMsgStm: 'message',
                        extraInfo: 'No extra info',
                        unitTests: [],
                        action: {
                            api: 'Api1',
                            parameterCode: 'fnn = `${data.fnn}`;',
                            codeCondition: '"valid";',
                            autoExecution: true,
                        },
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                        name: 'MDR001'
                    },
                    {
                        name: 'MDR002',
                        active: false,
                        hideInResult: true,
                        showReject: true,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: 'Test Rule 2',
                        rootCauseCategory: 'Ticket',
                        level: 6,
                        description: 'A second rule for unit tests',
                        wikiPage: 'N/A',
                        ruleType: 'Logical',
                        isWarning: true,
                        displayInSummary: false,
                        failedInSummary: false,
                        preSources: [
                            'ODIN',
                            'RASSP'
                        ],
                        preSourcesRunOnFail: false,
                        preRules: [
                            'MDR000',
                            'MDR001',
                            'MTR001'
                        ],
                        preRulesRunOnFail: true,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: 's.ODIN && s.RASSP',
                        preConditionMsg: 'Failed because sources had no data',
                        trueMsg: 'True message here',
                        falseMsg: 'False message here',
                        errorMsg: '"Could not obtain data: " + s.ODIN',
                        ruleCode: 'data.field = s.ODIN.field;',
                        valueMsgStm: 'test message',
                        extraInfo: '',
                        unitTests: [],
                        action: {
                            api: 'Api2',
                            parameterCode: 'uri = `test/path/to/poll`;',
                            codeCondition: '60;',
                            autoExecution: false,
                        },
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'merge'
                    }]);

                    res.body.results.forEach((rule) => {
                        chai.expect(rule).to.have.all.keys(RULE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get one rule with limit, five rules existing', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001'
            }).save());

            promises.push(new Rule({
                name: 'MDR002'
            }).save());

            promises.push(new Rule({
                name: 'MDR003'
            }).save());

            promises.push(new Rule({
                name: 'MDR004'
            }).save());

            promises.push(new Rule({
                name: 'MDR005'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/rules?limit=1')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR001'
                    }]);

                    res.body.results.forEach((rule) => {
                        chai.expect(rule).to.have.all.keys(RULE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get one rule with limit and offset, five rules existing', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001'
            }).save());

            promises.push(new Rule({
                name: 'MDR002'
            }).save());

            promises.push(new Rule({
                name: 'MDR003'
            }).save());

            promises.push(new Rule({
                name: 'MDR004'
            }).save());

            promises.push(new Rule({
                name: 'MDR005'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/rules?limit=2&offset=2')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR003'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR004'
                    }]);

                    res.body.results.forEach((rule) => {
                        chai.expect(rule).to.have.all.keys(RULE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get rule list sorted by name ascending', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001'
            }).save());

            promises.push(new Rule({
                name: 'MDR002'
            }).save());

            promises.push(new Rule({
                name: 'MDR003'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/rules?sort=name&order=asc')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR001'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR002'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR003'
                    }]);

                    res.body.results.forEach((rule) => {
                        chai.expect(rule).to.have.all.keys(RULE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get rule list sorted by name descending', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001'
            }).save());

            promises.push(new Rule({
                name: 'MDR002'
            }).save());

            promises.push(new Rule({
                name: 'MDR003'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/rules?sort=name&order=desc')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR003'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR002'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR001'
                    }]);

                    res.body.results.forEach((rule) => {
                        chai.expect(rule).to.have.all.keys(RULE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get rule list sorted by title ascending', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001',
                title: 'Bravo'
            }).save());

            promises.push(new Rule({
                name: 'MDR002',
                title: 'Charlie'
            }).save());

            promises.push(new Rule({
                name: 'MDR003',
                title: 'Alpha'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/rules?sort=title&order=asc')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: 'Alpha',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR003'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: 'Bravo',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR001'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: 'Charlie',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR002'
                    }]);

                    res.body.results.forEach((rule) => {
                        chai.expect(rule).to.have.all.keys(RULE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get rule list sorted by title descending', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001',
                title: 'Bravo'
            }).save());

            promises.push(new Rule({
                name: 'MDR002',
                title: 'Charlie'
            }).save());

            promises.push(new Rule({
                name: 'MDR003',
                title: 'Alpha'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/rules?sort=title&order=desc')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: 'Charlie',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR002'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: 'Bravo',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR001'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: 'Alpha',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR003'
                    }]);

                    res.body.results.forEach((rule) => {
                        chai.expect(rule).to.have.all.keys(RULE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get rule list sorted by title ascending case insensitive', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001',
                title: 'title C'
            }).save());

            promises.push(new Rule({
                name: 'MDR002',
                title: 'TITLE b'
            }).save());

            promises.push(new Rule({
                name: 'MDR003',
                title: "Title A"
            }).save());

            Promise.all(promises).then(() => {
                request.get('/rules?sort=title&order=asc')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: 'Title A',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR003'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: 'TITLE b',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR002'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: 'title C',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR001'
                    }]);

                    res.body.results.forEach((rule) => {
                        chai.expect(rule).to.have.all.keys(RULE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get rule list sorted by title descending case insensitive', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001',
                title: 'title C'
            }).save());

            promises.push(new Rule({
                name: 'MDR002',
                title: 'TITLE b'
            }).save());

            promises.push(new Rule({
                name: 'MDR003',
                title: "Title A"
            }).save());

            Promise.all(promises).then(() => {
                request.get('/rules?sort=title&order=desc')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: 'title C',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR001'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: 'TITLE b',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR002'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: 'Title A',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR003'
                    }]);

                    res.body.results.forEach((rule) => {
                        chai.expect(rule).to.have.all.keys(RULE_KEYS);
                    });

                    done();
                });
            });
        });
        it('Get rule list filter by name equality 1', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001'
            }).save());

            promises.push(new Rule({
                name: 'MDR002'
            }).save());

            promises.push(new Rule({
                name: 'MTR001'
            }).save());

            promises.push(new Rule({
                name: 'MTR002'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/rules?name=MTR001')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MTR001'
                    }]);

                    res.body.results.forEach((rule) => {
                        chai.expect(rule).to.have.all.keys(RULE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get rule list filter by name equality 2', (done) => {
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001'
            }).save());

            promises.push(new Rule({
                name: 'MDR002'
            }).save());

            promises.push(new Rule({
                name: 'MTR001'
            }).save());

            promises.push(new Rule({
                name: 'MTR002'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/rules?name=MTR')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([]);

                    done();
                });
            });
        });

        it('Get rule list filter by name equality 3', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001'
            }).save());

            promises.push(new Rule({
                name: 'MDR002'
            }).save());

            promises.push(new Rule({
                name: 'MTR001'
            }).save());

            promises.push(new Rule({
                name: 'MTR002'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/rules?name[equal]=MDR002')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR002'
                    }]);

                    res.body.results.forEach((rule) => {
                        chai.expect(rule).to.have.all.keys(RULE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get rule list filter by name equality 4', (done) => {
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001'
            }).save());

            promises.push(new Rule({
                name: 'MDR002'
            }).save());

            promises.push(new Rule({
                name: 'MTR001'
            }).save());

            promises.push(new Rule({
                name: 'MTR002'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/rules?name[equal]=MDR')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([]);

                    done();
                });
            });
        });

        it('Get rule list filter by name like 1', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001'
            }).save());

            promises.push(new Rule({
                name: 'MDR002'
            }).save());

            promises.push(new Rule({
                name: 'MTR001'
            }).save());

            promises.push(new Rule({
                name: 'MTR002'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/rules?name[like]=MDR')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR001'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR002'
                    }]);

                    res.body.results.forEach((rule) => {
                        chai.expect(rule).to.have.all.keys(RULE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get rule list filter by name like 2', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001'
            }).save());

            promises.push(new Rule({
                name: 'MDR002'
            }).save());

            promises.push(new Rule({
                name: 'MTR001'
            }).save());

            promises.push(new Rule({
                name: 'MTR002'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/rules?name[like]=01')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR001'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MTR001'
                    }]);

                    res.body.results.forEach((rule) => {
                        chai.expect(rule).to.have.all.keys(RULE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get rule list filter by name like 3', (done) => {
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001'
            }).save());

            promises.push(new Rule({
                name: 'MDR002'
            }).save());

            promises.push(new Rule({
                name: 'MTR001'
            }).save());

            promises.push(new Rule({
                name: 'MTR002'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/rules?name[like]=MSR')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([]);

                    done();
                });
            });
        });

        it('Get rule list filter by name like 4 (case insensitive)', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001'
            }).save());

            promises.push(new Rule({
                name: 'MDR002'
            }).save());

            promises.push(new Rule({
                name: 'MTR001'
            }).save());

            promises.push(new Rule({
                name: 'MTR002'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/rules?name[like]=mtr')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MTR001'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MTR002'
                    }]);

                    res.body.results.forEach((rule) => {
                        chai.expect(rule).to.have.all.keys(RULE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get rule list filter by title equality 1', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001',
                title: 'A rule title'
            }).save());

            promises.push(new Rule({
                name: 'MDR002',
                title: 'Another rule title'
            }).save());

            promises.push(new Rule({
                name: 'MTR001',
                title: 'Rule title here'
            }).save());

            promises.push(new Rule({
                name: 'MTR002',
                title: 'Yet another rule title'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/rules?title=Rule%20title%20here')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: 'Rule title here',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MTR001'
                    }]);

                    res.body.results.forEach((rule) => {
                        chai.expect(rule).to.have.all.keys(RULE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get rule list filter by title equality 2', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001',
                title: 'A rule title'
            }).save());

            promises.push(new Rule({
                name: 'MDR002',
                title: 'Another rule title'
            }).save());

            promises.push(new Rule({
                name: 'MTR001',
                title: 'Rule title here'
            }).save());

            promises.push(new Rule({
                name: 'MTR002',
                title: 'Yet another rule title'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/rules?title[equal]=Rule%20title%20here')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: 'Rule title here',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MTR001'
                    }]);

                    res.body.results.forEach((rule) => {
                        chai.expect(rule).to.have.all.keys(RULE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get rule list filter by title like 1', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001',
                title: 'Extract FNN from MAGPIE'
            }).save());

            promises.push(new Rule({
                name: 'MDR002',
                title: 'Extract service number from source'
            }).save());

            promises.push(new Rule({
                name: 'MTR001',
                title: 'Append things'
            }).save());

            promises.push(new Rule({
                name: 'MTR002',
                title: 'Do something else'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/rules?title[like]=Extract')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: 'Extract FNN from MAGPIE',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR001'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: 'Extract service number from source',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR002'
                    }]);

                    res.body.results.forEach((rule) => {
                        chai.expect(rule).to.have.all.keys(RULE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get rule list filter by title like 2 (case insensitive)', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001',
                title: 'Extract FNN from MAGPIE'
            }).save());

            promises.push(new Rule({
                name: 'MDR002',
                title: 'Extract service number from TID data source'
            }).save());

            promises.push(new Rule({
                name: 'MTR001',
                title: 'Check TID router shaper'
            }).save());

            promises.push(new Rule({
                name: 'MTR002',
                title: 'Verify TID service'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/rules?title[like]=tId')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: 'Extract service number from TID data source',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR002'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: 'Check TID router shaper',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MTR001'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: 'Verify TID service',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MTR002'
                    }]);

                    res.body.results.forEach((rule) => {
                        chai.expect(rule).to.have.all.keys(RULE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get rule list invalid limit (non-integer)', (done) => {
            request.get('/rules?limit=notalimit')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get rule list invalid limit (integer equals 0)', (done) => {
            request.get('/rules?limit=0')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get rule list invalid limit (negative integer)', (done) => {
            request.get('/rules?limit=-1')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get rule list invalid offset (non-integer)', (done) => {
            request.get('/rules?offset=notanoffset')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get rule list invalid offset (negative integer)', (done) => {
            request.get('/rules?offset=-1')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get rule list invalid order parameter', (done) => {
            request.get('/rules?order=ascending')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get rule list invalid name filter operator', (done) => {
            request.get('/rules?name[ophere]=test')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get rule list empty title filter operator', (done) => {
            request.get('/rules?title[]=no-op')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get rule list with HTTP GET with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            let res = await request.get('/rules');

            res.should.have.status(200);

            chai.expect(res.body).to.eql({
                metadata: {
                    pagination: {
                        limit: 100,
                        offset: 0,
                        total: 0,
                        prev: null,
                        next: null
                    }
                },
                results: []
            });
        });
    });

    describe('Read rule', () => {
        it('Unauthenticated request', async() => {
            await new Rule({
                name: 'MDR001'
            }).save();

            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/rules/MDR001');

            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            await new Rule({
                name: 'MDR001'
            }).save();

            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/rules/MDR001');

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Get non-existant rule', (done) => {
            request.get('/rules/MDR001')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(404);

                done();
            });
        });

        it('Get one existing rule (default values)', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/rules/MDR001')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body).to.like({
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR001'
                    });

                    chai.expect(res.body).to.have.all.keys(RULE_KEYS);

                    done();
                });
            });
        });

        it('Get one existing rule', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001',
                active: true,
                hideInResult: false,
                showReject: false,
                requiresReview: false,
                includedInitialServiceCheck: false,
                title: 'Test Rule',
                rootCauseCategory: 'Data Collection',
                level: 2,
                description: 'Rule for unit tests',
                wikiPage: 'N/A',
                ruleType: 'Extract',
                isWarning: false,
                displayInSummary: false,
                failedInSummary: true,
                preSources: [
                    'MAGPIE'
                ],
                preSourcesRunOnFail: true,
                preRules: [],
                preRulesRunOnFail: false,
                runWhenDependenciesResolved: false,
                depedenciesFromRelatedServiceChecks: false,
                runForEachPreSource: false,
                preCondition: 's.MAGPIE',
                preConditionMsg: '',
                trueMsg: 'This is true',
                falseMsg: 'This is false',
                errorMsg: 'This is an error message',
                ruleCode: 'data.success = s.MAGPIE.success',
                valueMsgStm: 'message',
                extraInfo: 'No extra info',
                unitTests: [],
                action: {
                    api: 'ApiName',
                    parameterCode: '',
                    codeCondition: 'true;',
                    autoExecution: true,
                },
                ignoreCompare: false,
                overrideCompare: '',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            }).save());

            Promise.all(promises).then(() => {
                request.get('/rules/MDR001')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body).to.like({
                        active: true,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: 'Test Rule',
                        rootCauseCategory: 'Data Collection',
                        level: 2,
                        description: 'Rule for unit tests',
                        wikiPage: 'N/A',
                        ruleType: 'Extract',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [
                            'MAGPIE'
                        ],
                        preSourcesRunOnFail: true,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: 's.MAGPIE',
                        preConditionMsg: '',
                        trueMsg: 'This is true',
                        falseMsg: 'This is false',
                        errorMsg: 'This is an error message',
                        ruleCode: 'data.success = s.MAGPIE.success',
                        valueMsgStm: 'message',
                        extraInfo: 'No extra info',
                        unitTests: [],
                        action: {
                            api: 'ApiName',
                            parameterCode: '',
                            codeCondition: 'true;',
                            autoExecution: true,
                        },
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                        name: 'MDR001'
                    });

                    chai.expect(res.body).to.have.all.keys(RULE_KEYS);

                    done();
                });
            });
        });

        it('Get existing rule with HTTP GET with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            let currDate = new Date();

            await new Rule({
                name: 'MDR001'
            }).save();

            let res = await request.get('/rules/MDR001');

            res.should.have.status(200);
            res.body.should.like({
                active: false,
                hideInResult: false,
                showReject: false,
                requiresReview: false,
                includedInitialServiceCheck: false,
                title: '',
                rootCauseCategory: 'Administration',
                level: 0,
                description: '',
                wikiPage: '',
                ruleType: 'Logical',
                isWarning: false,
                displayInSummary: false,
                failedInSummary: true,
                preSources: [],
                preSourcesRunOnFail: false,
                preRules: [],
                preRulesRunOnFail: false,
                runWhenDependenciesResolved: false,
                depedenciesFromRelatedServiceChecks: false,
                runForEachPreSource: false,
                preCondition: '',
                preConditionMsg: '',
                trueMsg: '',
                falseMsg: '',
                errorMsg: '',
                ruleCode: '',
                valueMsgStm: '',
                extraInfo: '',
                unitTests: [],
                action: null,
                ignoreCompare: false,
                overrideCompare: '',
                createdOn: currDate,
                createdBy: 'unknown',
                name: 'MDR001'
            });

            chai.expect(res.body).to.have.all.keys(RULE_KEYS);
        });
    });

    describe('Create rule', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.post('/rules').send({
                name: 'MDR001'
            });

            res.should.have.status(401);
            chai.expect(await Rule.find({ name: 'MDR001' }).countDocuments()).to.eql(0);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.post('/rules').send({
                    name: 'MDR001'
                });

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(201);
                            chai.expect(await Rule.find({ name: 'MDR001' }).countDocuments()).to.eql(1);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                            chai.expect(await Rule.find({ name: 'MDR001' }).countDocuments()).to.eql(0);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }

                await Rule.deleteMany({});
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Create rule with HTTP POST (default values)', (done) => {
            let currDate = new Date();

            request.post('/rules')
            .send({
                name: 'MDR001'
            })
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(201);

                chai.expect(res.body).to.like({
                    active: false,
                    hideInResult: false,
                    showReject: false,
                    requiresReview: false,
                    includedInitialServiceCheck: false,
                    title: '',
                    rootCauseCategory: 'Administration',
                    level: 0,
                    description: '',
                    wikiPage: '',
                    ruleType: 'Logical',
                    isWarning: false,
                    displayInSummary: false,
                    failedInSummary: true,
                    preSources: [],
                    preSourcesRunOnFail: false,
                    preRules: [],
                    preRulesRunOnFail: false,
                    runWhenDependenciesResolved: false,
                    depedenciesFromRelatedServiceChecks: false,
                    runForEachPreSource: false,
                    preCondition: '',
                    preConditionMsg: '',
                    trueMsg: '',
                    falseMsg: '',
                    errorMsg: '',
                    ruleCode: '',
                    valueMsgStm: '',
                    extraInfo: '',
                    unitTests: [],
                    action: null,
                    ignoreCompare: false,
                    overrideCompare: '',
                    createdOn: currDate,
                    createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                    name: 'MDR001'
                });

                request.get('/rules/MDR001')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body).to.like({
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                        name: 'MDR001'
                    });

                    chai.expect(res.body).to.have.all.keys(RULE_KEYS);

                    done();
                });
            });
        });

        it('Create rule with HTTP POST test invalid level field', (done) => {
            request.post('/rules')
            .send({
                name: 'MDR001',
                level: 'not a valid integer'
            })
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);

                done();
            });
        });

        it('Create rule with HTTP POST', (done) => {
            let currDate = new Date();

            request.post('/rules')
            .send({
                name: 'MDR001',
                active: true,
                hideInResult: true,
                showReject: true,
                requiresReview: false,
                includedInitialServiceCheck: false,
                title: 'Test Rule POST',
                rootCauseCategory: '3rd Party Network',
                level: 0,
                description: 'Rule for unit tests',
                wikiPage: 'N/A',
                ruleType: 'Extract',
                isWarning: false,
                displayInSummary: false,
                failedInSummary: true,
                preSources: [
                    'MAGPIE'
                ],
                preSourcesRunOnFail: false,
                preRules: [
                    'MTR001'
                ],
                preRulesRunOnFail: true,
                runWhenDependenciesResolved: false,
                depedenciesFromRelatedServiceChecks: false,
                runForEachPreSource: false,
                preCondition: 's.MAGPIE',
                preConditionMsg: '',
                trueMsg: 'This is true',
                falseMsg: 'This is false',
                errorMsg: 'This is an error message',
                ruleCode: 'data.success = s.MAGPIE.success',
                valueMsgStm: 'message',
                extraInfo: 'Test extra info',
                unitTests: [],
                action: {
                    api: 'ThisIsAnApi',
                    parameterCode: 'carriageType = data.carriageType;',
                    codeCondition: '"ADSL";',
                    autoExecution: true,
                },
                ignoreCompare: true,
                overrideCompare: '(function (ruleData1, ruleData2) { return true; });',
            })
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(201);

                chai.expect(res.body).to.like({
                    active: true,
                    hideInResult: true,
                    showReject: true,
                    requiresReview: false,
                    includedInitialServiceCheck: false,
                    title: 'Test Rule POST',
                    rootCauseCategory: '3rd Party Network',
                    level: 0,
                    description: 'Rule for unit tests',
                    wikiPage: 'N/A',
                    ruleType: 'Extract',
                    isWarning: false,
                    displayInSummary: false,
                    failedInSummary: true,
                    preSources: [
                        'MAGPIE'
                    ],
                    preSourcesRunOnFail: false,
                    preRules: [
                        'MTR001'
                    ],
                    preRulesRunOnFail: true,
                    runWhenDependenciesResolved: false,
                    depedenciesFromRelatedServiceChecks: false,
                    runForEachPreSource: false,
                    preCondition: 's.MAGPIE',
                    preConditionMsg: '',
                    trueMsg: 'This is true',
                    falseMsg: 'This is false',
                    errorMsg: 'This is an error message',
                    ruleCode: 'data.success = s.MAGPIE.success',
                    valueMsgStm: 'message',
                    extraInfo: 'Test extra info',
                    unitTests: [],
                    createdOn: currDate,
                    createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                    name: 'MDR001',
                    action: {
                        api: 'ThisIsAnApi',
                        parameterCode: 'carriageType = data.carriageType;',
                        codeCondition: '"ADSL";',
                        autoExecution: true,
                    },
                    ignoreCompare: true,
                    overrideCompare: '(function (ruleData1, ruleData2) { return true; });',
                });

                chai.expect(res.body).to.have.all.keys(RULE_KEYS);

                request.get('/rules/MDR001')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body).to.like({
                        active: true,
                        hideInResult: true,
                        showReject: true,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: 'Test Rule POST',
                        rootCauseCategory: '3rd Party Network',
                        level: 0,
                        description: 'Rule for unit tests',
                        wikiPage: 'N/A',
                        ruleType: 'Extract',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [
                            'MAGPIE'
                        ],
                        preSourcesRunOnFail: false,
                        preRules: [
                            'MTR001'
                        ],
                        preRulesRunOnFail: true,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: 's.MAGPIE',
                        preConditionMsg: '',
                        trueMsg: 'This is true',
                        falseMsg: 'This is false',
                        errorMsg: 'This is an error message',
                        ruleCode: 'data.success = s.MAGPIE.success',
                        valueMsgStm: 'message',
                        extraInfo: 'Test extra info',
                        unitTests: [],
                        action: {
                            api: 'ThisIsAnApi',
                            parameterCode: 'carriageType = data.carriageType;',
                            codeCondition: '"ADSL";',
                            autoExecution: true,
                        },
                        ignoreCompare: true,
                        overrideCompare: '(function (ruleData1, ruleData2) { return true; });',
                        createdOn: currDate,
                        createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                        name: 'MDR001'
                    });

                    chai.expect(res.body).to.have.all.keys(RULE_KEYS);

                    done();
                });
            });
        });

        it('Create rule with HTTP POST test duplicate preSources', async() => {
            let res = await request.post('/rules').send({
                name: 'MDR001',
                preSources: [
                    'SourceName',
                    'SourceName'
                ]
            });

            res.should.have.status(400);

            chai.expect(await Rule.find({ name: 'MDR001' }).countDocuments()).to.eql(0);
        });

        it('Create rule with HTTP POST test duplicate preRules', async() => {
            let res = await request.post('/rules').send({
                name: 'MDR001',
                preRules: [
                    'MTR001',
                    'MTR001'
                ]
            });

            res.should.have.status(400);

            chai.expect(await Rule.find({ name: 'MDR001' }).countDocuments()).to.eql(0);
        });

        it('Create rule with empty action with HTTP POST', (done) => {
            request.post('/rules')
            .send({
                name: 'MDR001',
                action: {}
            })
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);

                done();
            });
        });

        it('Create rule with default action fields with HTTP POST', (done) => {
            let currDate = new Date();

            request.post('/rules')
            .send({
                name: 'MDR001',
                action: {
                    api: 'ApiName'
                }
            })
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(201);

                chai.expect(res.body).to.like({
                    active: false,
                    hideInResult: false,
                    showReject: false,
                    requiresReview: false,
                    includedInitialServiceCheck: false,
                    title: '',
                    rootCauseCategory: 'Administration',
                    level: 0,
                    description: '',
                    wikiPage: '',
                    ruleType: 'Logical',
                    isWarning: false,
                    displayInSummary: false,
                    failedInSummary: true,
                    preSources: [],
                    preSourcesRunOnFail: false,
                    preRules: [],
                    preRulesRunOnFail: false,
                    runWhenDependenciesResolved: false,
                    depedenciesFromRelatedServiceChecks: false,
                    runForEachPreSource: false,
                    preCondition: '',
                    preConditionMsg: '',
                    trueMsg: '',
                    falseMsg: '',
                    errorMsg: '',
                    ruleCode: '',
                    valueMsgStm: '',
                    extraInfo: '',
                    unitTests: [],
                    action: {
                        api: 'ApiName',
                        parameterCode: '',
                        codeCondition: '',
                        autoExecution: false
                    },
                    ignoreCompare: false,
                    overrideCompare: '',
                    createdOn: currDate,
                    createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                    name: 'MDR001'
                });

                chai.expect(res.body).to.have.all.keys(RULE_KEYS);

                request.get('/rules/MDR001')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body).to.like({
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: {
                            api: 'ApiName',
                            parameterCode: '',
                            codeCondition: '',
                            autoExecution: false
                        },
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                        name: 'MDR001'
                    });

                    chai.expect(res.body).to.have.all.keys(RULE_KEYS);
                    done();
                });
            });
        });

        it('Create rule with invalid action fields with HTTP POST', (done) => {
            request.post('/rules')
            .send({
                name: 'MDR001',
                action: {
                    api: 'TestApi',
                    parameterCode: '',
                    codeCondition: '',
                    autoExecution: 'string here'
                }
            })
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);

                done();
            });
        });

        it('Create duplicate rule with HTTP POST', (done) => {
            request.post('/rules')
            .send({
                name: 'MDR001'
            })
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(201);

                request.post('/rules')
                .send({
                    name: 'MDR001',
                })
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(409);
                    done();
                });
            });
        });

        it('Create rule with HTTP POST test createdBy immutable', (done) => {
            let currDate = new Date();

            request.post('/rules')
            .send({
                name: 'MDR001',
                createdBy: 'anotheruser'
            })
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(201);

                chai.expect(res.body).to.like({
                    active: false,
                    hideInResult: false,
                    showReject: false,
                    requiresReview: false,
                    includedInitialServiceCheck: false,
                    title: '',
                    rootCauseCategory: 'Administration',
                    level: 0,
                    description: '',
                    wikiPage: '',
                    ruleType: 'Logical',
                    isWarning: false,
                    displayInSummary: false,
                    failedInSummary: true,
                    preSources: [],
                    preSourcesRunOnFail: false,
                    preRules: [],
                    preRulesRunOnFail: false,
                    runWhenDependenciesResolved: false,
                    depedenciesFromRelatedServiceChecks: false,
                    runForEachPreSource: false,
                    preCondition: '',
                    preConditionMsg: '',
                    trueMsg: '',
                    falseMsg: '',
                    errorMsg: '',
                    ruleCode: '',
                    valueMsgStm: '',
                    extraInfo: '',
                    unitTests: [],
                    action: null,
                    ignoreCompare: false,
                    overrideCompare: '',
                    createdOn: currDate,
                    createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                    name: 'MDR001'
                });

                chai.expect(res.body).to.have.all.keys(RULE_KEYS);

                done();
            });
        });

        it('Create rule with HTTP POST test createdOn immutable', (done) => {
            let customDate = new Date('2020-12-15T16:00:00.000Z');
            let currDate = new Date();

            request.post('/rules')
            .send({
                name: 'MDR001',
                createdOn: customDate
            })
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(201);

                chai.expect(res.body).to.like({
                    active: false,
                    hideInResult: false,
                    showReject: false,
                    requiresReview: false,
                    includedInitialServiceCheck: false,
                    title: '',
                    rootCauseCategory: 'Administration',
                    level: 0,
                    description: '',
                    wikiPage: '',
                    ruleType: 'Logical',
                    isWarning: false,
                    displayInSummary: false,
                    failedInSummary: true,
                    preSources: [],
                    preSourcesRunOnFail: false,
                    preRules: [],
                    preRulesRunOnFail: false,
                    runWhenDependenciesResolved: false,
                    depedenciesFromRelatedServiceChecks: false,
                    runForEachPreSource: false,
                    preCondition: '',
                    preConditionMsg: '',
                    trueMsg: '',
                    falseMsg: '',
                    errorMsg: '',
                    ruleCode: '',
                    valueMsgStm: '',
                    extraInfo: '',
                    unitTests: [],
                    action: null,
                    ignoreCompare: false,
                    overrideCompare: '',
                    createdOn: currDate,
                    createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                    name: 'MDR001'
                });

                chai.expect(res.body).to.have.all.keys(RULE_KEYS);

                done();
            });
        });

        it('Create rule with HTTP POST unrecognised fields', (done) => {
            let currDate = new Date();

            request.post('/rules')
            .send({
                name: 'MDR001',
                customField: 'should not show up',
                unrecognisedField: 550
            })
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(201);

                chai.expect(res.body).to.like({
                    active: false,
                    hideInResult: false,
                    showReject: false,
                    requiresReview: false,
                    includedInitialServiceCheck: false,
                    title: '',
                    rootCauseCategory: 'Administration',
                    level: 0,
                    description: '',
                    wikiPage: '',
                    ruleType: 'Logical',
                    isWarning: false,
                    displayInSummary: false,
                    failedInSummary: true,
                    preSources: [],
                    preSourcesRunOnFail: false,
                    preRules: [],
                    preRulesRunOnFail: false,
                    runWhenDependenciesResolved: false,
                    depedenciesFromRelatedServiceChecks: false,
                    runForEachPreSource: false,
                    preCondition: '',
                    preConditionMsg: '',
                    trueMsg: '',
                    falseMsg: '',
                    errorMsg: '',
                    ruleCode: '',
                    valueMsgStm: '',
                    extraInfo: '',
                    unitTests: [],
                    action: null,
                    ignoreCompare: false,
                    overrideCompare: '',
                    createdOn: currDate,
                    createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                    name: 'MDR001'
                });

                chai.expect(res.body).to.have.all.keys(RULE_KEYS);
                chai.expect(res.body).to.not.have.any.keys('customField', 'unrecognisedField');

                request.get('/rules/MDR001')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body).to.like({
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                        name: 'MDR001'
                    });

                    chai.expect(res.body).to.have.all.keys(RULE_KEYS);
                    chai.expect(res.body).to.not.have.any.keys('customField', 'unrecognisedField');

                    done();
                });
            });
        });

        it('Create rule with HTTP POST with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            let postRes = await request.post('/rules').send({
                name: 'MDR001'
            });

            postRes.should.have.status(405);

            let getRes = await request.get('/rules/MDR001');

            getRes.should.have.status(404);
        });

        it('Create rule with HTTP POST with disableRuleSourceEditing enabled and admin user', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'admin');

            let postRes = await request.post('/rules').send({
                name: 'MDR001'
            });

            postRes.should.have.status(201);

            let getRes = await request.get('/rules/MDR001');

            getRes.should.have.status(200);
        });
    });

    describe('Update rule', () => {
        it('Unauthenticated request', async() => {
            await new Rule({
                name: 'MDR001',
                active: false
            }).save();

            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.put('/rules/MDR001').send({
                active: true
            });

            res.should.have.status(401);
            chai.expect((await Rule.findOne({ name: 'MDR001' })).active).to.eql(false);
        });

        it('Authorization tests', async() => {
            await new Rule({
                name: 'MDR001',
                active: false
            }).save();

            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.put('/rules/MDR001').send({
                    active: true
                });

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                            chai.expect((await Rule.findOne({ name: 'MDR001' })).active).to.eql(true);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                            chai.expect((await Rule.findOne({ name: 'MDR001' })).active).to.eql(false);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
                await Rule.updateOne({ name: 'MDR001' }, { $set: { active: false }});
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Update rule with HTTP PUT', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001'
            }).save());

            Promise.all(promises).then(() => {
                request.put('/rules/MDR001')
                .send({
                    name: 'MDR001',
                    active: true,
                    hideInResult: true,
                    showReject: false,
                    requiresReview: false,
                    includedInitialServiceCheck: false,
                    title: 'Test Rule PUT',
                    rootCauseCategory: '3rd Party Network',
                    level: 0,
                    description: 'Rule for unit tests',
                    wikiPage: 'N/A',
                    ruleType: 'Extract',
                    isWarning: false,
                    displayInSummary: false,
                    failedInSummary: true,
                    preSources: [
                        'MAGPIE'
                    ],
                    preSourcesRunOnFail: false,
                    preRules: [
                        'MTR001'
                    ],
                    preRulesRunOnFail: true,
                    runWhenDependenciesResolved: false,
                    depedenciesFromRelatedServiceChecks: false,
                    runForEachPreSource: false,
                    preCondition: 's.MAGPIE',
                    preConditionMsg: '',
                    trueMsg: 'This is true',
                    falseMsg: 'This is false',
                    errorMsg: 'This is an error message',
                    ruleCode: 'data.success = s.MAGPIE.success',
                    valueMsgStm: 'message',
                    extraInfo: 'Test extra info',
                    unitTests: [],
                    action: {
                        api: 'API',
                        parameterCode: 'carriageFnn = data.fnn + data.carriageType;',
                        codeCondition: 'false;',
                        autoExecution: true,
                    },
                    ignoreCompare: true,
                    overrideCompare: 'notafunction;',
                }).end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body).to.like({
                        active: true,
                        hideInResult: true,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: 'Test Rule PUT',
                        rootCauseCategory: '3rd Party Network',
                        level: 0,
                        description: 'Rule for unit tests',
                        wikiPage: 'N/A',
                        ruleType: 'Extract',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [
                            'MAGPIE'
                        ],
                        preSourcesRunOnFail: false,
                        preRules: [
                            'MTR001'
                        ],
                        preRulesRunOnFail: true,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: 's.MAGPIE',
                        preConditionMsg: '',
                        trueMsg: 'This is true',
                        falseMsg: 'This is false',
                        errorMsg: 'This is an error message',
                        ruleCode: 'data.success = s.MAGPIE.success',
                        valueMsgStm: 'message',
                        extraInfo: 'Test extra info',
                        unitTests: [],
                        action: {
                            api: 'API',
                            parameterCode: 'carriageFnn = data.fnn + data.carriageType;',
                            codeCondition: 'false;',
                            autoExecution: true,
                        },
                        ignoreCompare: true,
                        overrideCompare: 'notafunction;',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR001'
                    });

                    chai.expect(res.body).to.have.all.keys(RULE_KEYS);

                    done();
                });
            });
        });

        it('Update rule with HTTP PUT test non-existant rule', (done) => {
            request.put('/rules/MDR001')
            .send({}).end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(404);
                done();
            });
        });

        it('Update rule with HTTP PUT test invalid level field', (done) => {
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001'
            }).save());

            Promise.all(promises).then(() => {
                request.put('/rules/MDR001')
                .send({
                    level: 'not a number'
                }).end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(400);

                    done();
                });
            });
        });

        it('Update rule with HTTP PUT test duplicate elements in preSources', async() => {
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001'
            }).save());

            await Promise.all(promises);

            let res = await request.put('/rules/MDR001').send({
                preSources: [
                    'SourceName',
                    'SourceName'
                ]
            });

            res.should.have.status(400);
        });

        it('Update rule with HTTP PUT test duplicate elements in preRules', async() => {
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001'
            }).save());

            await Promise.all(promises);

            let res = await request.put('/rules/MDR001').send({
                preRules: [
                    'MTR001',
                    'MTR001'
                ]
            });

            res.should.have.status(400);
        });

        it('Update rule with HTTP PUT test name immutable', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001'
            }).save());

            Promise.all(promises).then(() => {
                request.put('/rules/MDR001')
                .send({
                    name: 'MDR002'
                }).end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    request.get('/rules/MDR001')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }

                        res.should.have.status(200);

                        chai.expect(res.body).to.like({
                            active: false,
                            hideInResult: false,
                            showReject: false,
                            requiresReview: false,
                            includedInitialServiceCheck: false,
                            title: '',
                            rootCauseCategory: 'Administration',
                            level: 0,
                            description: '',
                            wikiPage: '',
                            ruleType: 'Logical',
                            isWarning: false,
                            displayInSummary: false,
                            failedInSummary: true,
                            preSources: [],
                            preSourcesRunOnFail: false,
                            preRules: [],
                            preRulesRunOnFail: false,
                            runWhenDependenciesResolved: false,
                            depedenciesFromRelatedServiceChecks: false,
                            runForEachPreSource: false,
                            preCondition: '',
                            preConditionMsg: '',
                            trueMsg: '',
                            falseMsg: '',
                            errorMsg: '',
                            ruleCode: '',
                            valueMsgStm: '',
                            extraInfo: '',
                            unitTests: [],
                            action: null,
                            ignoreCompare: false,
                            overrideCompare: '',
                            createdOn: currDate,
                            createdBy: 'unknown',
                            name: 'MDR001'
                        });

                        chai.expect(res.body).to.have.all.keys(RULE_KEYS);

                        request.get('/rules/MDR002')
                        .end((err, res) => {
                            if (err) {
                                chai.assert.fail('expected', 'actual', err);
                            }

                            res.should.have.status(404);
                            done();
                        });
                    });
                });
            });
        });

        it('Update rule with HTTP PUT test createdBy immutable', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001',
                createdBy: 'testuser'
            }).save());

            Promise.all(promises).then(() => {
                request.put('/rules/MDR001')
                .send({
                    createdBy: 'anotheruser'
                }).end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body).to.like({
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'testuser',
                        name: 'MDR001'
                    });

                    chai.expect(res.body).to.have.all.keys(RULE_KEYS);

                    done();
                });
            });
        });

        it('Update rule with HTTP PUT test createdOn immutable', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001'
            }).save());

            Promise.all(promises).then(() => {
                request.put('/rules/MDR001')
                .send({
                    createdOn: '2010-01-15T16:00:00.000Z'
                }).end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body).to.like({
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR001'
                    });

                    chai.expect(res.body).to.have.all.keys(RULE_KEYS);

                    done();
                });
            });
        });

        it('Update rule with HTTP PUT test action valid fields', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001',
                action: {
                    api: 'API',
                    parameterCode: '',
                    codeCondition: '',
                    autoExecution: true
                },
            }).save());

            Promise.all(promises).then(() => {
                request.put('/rules/MDR001')
                .send({
                    action: {
                        api: 'NewAPI',
                        parameterCode: 'true;',
                        codeCondition: '5000;',
                        autoExecution: false
                    },
                }).end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body).to.like({
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: {
                            api: 'NewAPI',
                            parameterCode: 'true;',
                            codeCondition: '5000;',
                            autoExecution: false
                        },
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR001'
                    });

                    chai.expect(res.body).to.have.all.keys(RULE_KEYS);

                    done();
                });
            });
        });

        it('Update rule with HTTP PUT test action invalid fields', (done) => {
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001',
                action: {
                    api: 'API',
                    parameterCode: '',
                    codeCondition: '',
                    autoExecution: true,
                },
            }).save());

            Promise.all(promises).then(() => {
                request.put('/rules/MDR001')
                .send({
                    action: {
                        timeout: -10000
                    },
                }).end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(400);

                    done();
                });
            });
        });

        it('Update rule with HTTP PUT unrecognised fields', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001'
            }).save());

            Promise.all(promises).then(() => {
                request.put('/rules/MDR001')
                .send({
                    badField: 'badValue'
                }).end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body).to.like({
                        active: false,
                        hideInResult: false,
                        showReject: false,
                        requiresReview: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        rootCauseCategory: 'Administration',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        ruleType: 'Logical',
                        isWarning: false,
                        displayInSummary: false,
                        failedInSummary: true,
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        depedenciesFromRelatedServiceChecks: false,
                        runForEachPreSource: false,
                        preCondition: '',
                        preConditionMsg: '',
                        trueMsg: '',
                        falseMsg: '',
                        errorMsg: '',
                        ruleCode: '',
                        valueMsgStm: '',
                        extraInfo: '',
                        unitTests: [],
                        action: null,
                        ignoreCompare: false,
                        overrideCompare: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MDR001'
                    });

                    chai.expect(res.body).to.have.all.keys(RULE_KEYS);
                    chai.expect(res.body).to.not.have.any.keys('badField');

                    done();
                });
            });
        });

        it('Update rule with HTTP PUT with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            let currDate = new Date();

            await new Rule({
                name: 'MDR001'
            }).save();

            let putRes = await request.put('/rules/MDR001').send({
                active: true
            });

            putRes.should.have.status(405);

            let getRes = await request.get('/rules/MDR001');

            getRes.should.have.status(200);
            getRes.body.should.like({
                active: false,
                hideInResult: false,
                showReject: false,
                requiresReview: false,
                includedInitialServiceCheck: false,
                title: '',
                rootCauseCategory: 'Administration',
                level: 0,
                description: '',
                wikiPage: '',
                ruleType: 'Logical',
                isWarning: false,
                displayInSummary: false,
                failedInSummary: true,
                preSources: [],
                preSourcesRunOnFail: false,
                preRules: [],
                preRulesRunOnFail: false,
                runWhenDependenciesResolved: false,
                depedenciesFromRelatedServiceChecks: false,
                runForEachPreSource: false,
                preCondition: '',
                preConditionMsg: '',
                trueMsg: '',
                falseMsg: '',
                errorMsg: '',
                ruleCode: '',
                valueMsgStm: '',
                extraInfo: '',
                action: null,
                ignoreCompare: false,
                overrideCompare: '',
                unitTests: [],
                createdOn: currDate,
                createdBy: 'unknown',
                name: 'MDR001'
            });

            chai.expect(getRes.body).to.have.all.keys(RULE_KEYS);
        });

        it('Update rule with HTTP POST with disableRuleSourceEditing enabled and admin user', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'admin');

            let currDate = new Date();

            await new Rule({
                name: 'MDR001'
            }).save();

            let putRes = await request.put('/rules/MDR001').send({
                active: true
            });

            putRes.should.have.status(200);

            let getRes = await request.get('/rules/MDR001');

            getRes.should.have.status(200);
            getRes.body.should.like({
                active: true,
                hideInResult: false,
                showReject: false,
                requiresReview: false,
                includedInitialServiceCheck: false,
                title: '',
                rootCauseCategory: 'Administration',
                level: 0,
                description: '',
                wikiPage: '',
                ruleType: 'Logical',
                isWarning: false,
                displayInSummary: false,
                failedInSummary: true,
                preSources: [],
                preSourcesRunOnFail: false,
                preRules: [],
                preRulesRunOnFail: false,
                runWhenDependenciesResolved: false,
                depedenciesFromRelatedServiceChecks: false,
                runForEachPreSource: false,
                preCondition: '',
                preConditionMsg: '',
                trueMsg: '',
                falseMsg: '',
                errorMsg: '',
                ruleCode: '',
                valueMsgStm: '',
                extraInfo: '',
                action: null,
                ignoreCompare: false,
                overrideCompare: '',
                unitTests: [],
                createdOn: currDate,
                createdBy: 'unknown',
                name: 'MDR001'
            });

            chai.expect(getRes.body).to.have.all.keys(RULE_KEYS);
        });
    });

    describe('Delete rule', () => {
        it('Unauthenticated request', async() => {
            await new Rule({
                name: 'MDR001'
            }).save();

            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.delete('/rules/MDR001');

            res.should.have.status(401);
            chai.expect(await Rule.find({ name: 'MDR001' }).countDocuments()).to.eql(1);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                if (await Rule.find({ name: 'MDR001' }).countDocuments() === 0) {
                    await new Rule({
                        name: 'MDR001'
                    }).save();
                }

                await helpers.authenticateSession(request, username);
                let res = await request.delete('/rules/MDR001');

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                            chai.expect(await Rule.find({ name: 'MDR001' }).countDocuments()).to.eql(0);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                            chai.expect(await Rule.find({ name: 'MDR001' }).countDocuments()).to.eql(1);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Delete rule with HTTP DELETE', (done) => {
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001'
            }).save());

            Promise.all(promises).then(() => {
                request.delete('/rules/MDR001')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    request.get('/rules/MDR001')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }

                        res.should.have.status(404);

                        done();
                    });
                });
            });
        });

        it('Delete rule with HTTP DELETE test non-existant rule', (done) => {
            request.delete('/rules/MDR001')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(404);
                done();
            });
        });

        it('Delete rule with HTTP DELETE with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            await new Rule({
                name: 'MDR001'
            }).save();

            let delRes = await request.delete('/rules/MDR001');

            delRes.should.have.status(405);

            let getRes = await request.get('/rules/MDR001');

            getRes.should.have.status(200);
        });

        it('Delete rule with HTTP DELETE with disableRuleSourceEditing enabled and admin user', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'admin');

            await new Rule({
                name: 'MDR001'
            }).save();

            let delRes = await request.delete('/rules/MDR001');

            delRes.should.have.status(200);

            let getRes = await request.get('/rules/MDR001');

            getRes.should.have.status(404);
        });
    });

    describe('Unit test rule', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.post('/rules/unittest').send({
                rule: {
                    name: 'MDR001'
                }
            });

            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.post('/rules/unittest').send({
                    rule: {
                        name: 'MDR001'
                    }
                });

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Unit test rule with undefined rule', (done) => {
            request.post('/rules/unittest')
            .send({})
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Unit test rule with undefined unit tests', (done) => {
            request.post('/rules/unittest')
            .send({
                rule: {
                    name: 'MDR001'
                }
            })
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(200);
                chai.expect(res.body).to.eql([]);
                done();
            });
        });

        it('Unit test rule with empty unit tests', (done) => {
            request.post('/rules/unittest')
            .send({
                rule: {
                    name: 'MDR001',
                    unitTests: []
                }
            })
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(200);
                chai.expect(res.body).to.eql([]);
                done();
            });
        });

        it('Unit test rule with one valid unit test', (done) => {
            request.post('/rules/unittest')
            .send({
                rule: {
                    name: 'MDR001',
                    active: false,
                    hideInResult: false,
                    showReject: false,
                    requiresReview: false,
                    includedInitialServiceCheck: false,
                    title: '',
                    rootCauseCategory: 'Administration',
                    level: 0,
                    description: '',
                    wikiPage: '',
                    ruleType: 'Logical',
                    isWarning: false,
                    displayInSummary: false,
                    failedInSummary: true,
                    preSources: [],
                    preSourcesRunOnFail: false,
                    preRules: [],
                    preRulesRunOnFail: false,
                    runWhenDependenciesResolved: false,
                    depedenciesFromRelatedServiceChecks: false,
                    runForEachPreSource: false,
                    preCondition: 's.SourceName',
                    preConditionMsg: '',
                    trueMsg: 'True Message Here',
                    falseMsg: 'False Message Here',
                    errorMsg: '',
                    ruleCode: 'data.field = s.SourceName.field;',
                    valueMsgStm: '"test " + data.field',
                    extraInfo: '',
                    ignoreCompare: false,
                    overrideCompare: '',
                    unitTests: [{
                        name: 'Unit Test 1',
                        result: 'OK',
                        inputRecord: '{"sourcesData": {"SourceName": {"field": "my field value"}}}',
                        outputRecord: '{"sourcesData": {"SourceName": {"field": "my field value"}},\
                                       "rulesData": {"MDR001": {"msg": "True Message Here", "result": "OK",\
                                       "rootCauseCategory": "Administration", "status": "done", "type": "Logical", "valueMsg": "test my field value", "extraInfo": null}}}'
                    }],
                    action: null,
                }
            })
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(200);
                chai.expect(res.body).be.like([{
                    name: "Unit Test 1",
                    status: "pass",
                    outputRecord: {
                        rulesData: {
                            MDR001: {
                                msg: "True Message Here",
                                result: "OK",
                                rootCauseCategory: "Administration",
                                status: "done",
                                type: "Logical",
                                valueMsg: "test my field value"
                            }
                        },
                        sourcesData: {
                            SourceName: {
                                field: "my field value"
                            }
                        }
                    }
                }]);
                done();
            });
        });

        it('Unit test rule with two valid unit tests', (done) => {
            request.post('/rules/unittest')
            .send({
                rule: {
                    name: 'MDR001',
                    active: false,
                    hideInResult: false,
                    showReject: false,
                    requiresReview: false,
                    includedInitialServiceCheck: false,
                    title: '',
                    rootCauseCategory: 'Administration',
                    level: 0,
                    description: '',
                    wikiPage: '',
                    ruleType: 'Logical',
                    isWarning: false,
                    displayInSummary: false,
                    failedInSummary: true,
                    preSources: [],
                    preSourcesRunOnFail: false,
                    preRules: [],
                    preRulesRunOnFail: false,
                    runWhenDependenciesResolved: false,
                    depedenciesFromRelatedServiceChecks: false,
                    runForEachPreSource: false,
                    preCondition: 's.SourceName',
                    preConditionMsg: '',
                    trueMsg: 'True Message Here',
                    falseMsg: 'False Message Here',
                    errorMsg: '',
                    ruleCode: 'data.field = s.SourceName.field;',
                    valueMsgStm: '"test " + data.field',
                    extraInfo: '',
                    ignoreCompare: false,
                    overrideCompare: '',
                    unitTests: [{
                        name: 'Unit Test 1',
                        result: 'OK',
                        inputRecord: '{"sourcesData": {"SourceName": {"field": "my field value"}}}',
                        outputRecord: '{"sourcesData": {"SourceName": {"field": "my field value"}},\
                                       "rulesData": {"MDR001": {"msg": "True Message Here", "result": "OK",\
                                       "rootCauseCategory": "Administration", "status": "done", "type": "Logical", "valueMsg": "test my field value", "extraInfo": null}}}'
                    },
                    {
                        name: 'Unit Test 2',
                        result: 'OK',
                        inputRecord: '{"sourcesData": {"SourceName": {"field": "another value here"}}}',
                        outputRecord: '{"sourcesData": {"SourceName": {"field": "another value here"}},\
                                       "rulesData": {"MDR001": {"msg": "True Message Here", "result": "OK",\
                                       "rootCauseCategory": "Administration", "status": "done", "type": "Logical", "valueMsg": "test another value here", "extraInfo": null}}}'
                    }],
                    action: null
                }
            })
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(200);
                chai.expect(res.body).be.like([{
                    name: "Unit Test 1",
                    status: "pass",
                    outputRecord: {
                        rulesData: {
                            MDR001: {
                                msg: "True Message Here",
                                result: "OK",
                                rootCauseCategory: "Administration",
                                status: "done",
                                type: "Logical",
                                valueMsg: "test my field value"
                            }
                        },
                        sourcesData: {
                            SourceName: {
                                field: "my field value"
                            }
                        }
                    }
                },
                {
                    name: "Unit Test 2",
                    status: "pass",
                    outputRecord: {
                        rulesData: {
                            MDR001: {
                                msg: "True Message Here",
                                result: "OK",
                                rootCauseCategory: "Administration",
                                status: "done",
                                type: "Logical",
                                valueMsg: "test another value here"
                            }
                        },
                        sourcesData: {
                            SourceName: {
                                field: "another value here"
                            }
                        }
                    }
                }]);
                done();
            });
        });

        it('Unit test rule with one valid test, one invalid test', (done) => {
            request.post('/rules/unittest')
            .send({
                rule: {
                    name: 'MDR001',
                    active: false,
                    hideInResult: false,
                    showReject: false,
                    requiresReview: false,
                    includedInitialServiceCheck: false,
                    title: '',
                    rootCauseCategory: 'Administration',
                    level: 0,
                    description: '',
                    wikiPage: '',
                    ruleType: 'Logical',
                    isWarning: false,
                    displayInSummary: false,
                    failedInSummary: true,
                    preSources: [],
                    preSourcesRunOnFail: false,
                    preRules: [],
                    preRulesRunOnFail: false,
                    runWhenDependenciesResolved: false,
                    depedenciesFromRelatedServiceChecks: false,
                    runForEachPreSource: false,
                    preCondition: 's.SourceName',
                    preConditionMsg: '',
                    trueMsg: 'True Message Here',
                    falseMsg: 'False Message Here',
                    errorMsg: '',
                    ruleCode: 'data.field = s.SourceName.field;',
                    valueMsgStm: '"test " + data.field',
                    extraInfo: '',
                    ignoreCompare: false,
                    overrideCompare: '',
                    unitTests: [{
                        name: 'Unit Test 1',
                        result: 'OK',
                        inputRecord: '{"sourcesData": {"SourceName": {"field": "my field value"}}}',
                        outputRecord: '{"sourcesData": {"SourceName": {"field": "my field value"}},\
                                       "rulesData": {"MDR001": {"msg": "True Message Here", "result": "OK",\
                                       "rootCauseCategory": "Administration", "status": "done", "type": "Logical", "valueMsg": "test my field value", "extraInfo": null}}}'
                    },
                    {
                        name: 'Unit Test 2',
                        result: 'OK',
                        inputRecord: '{}}',
                        outputRecord: '{}'
                    }],
                    action: null
                }
            })
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(200);
                chai.expect(res.body).be.like([{
                    name: "Unit Test 1",
                    status: "pass",
                    outputRecord: {
                        rulesData: {
                            MDR001: {
                                msg: "True Message Here",
                                result: "OK",
                                rootCauseCategory: "Administration",
                                status: "done",
                                type: "Logical",
                                valueMsg: "test my field value"
                            }
                        },
                        sourcesData: {
                            SourceName: {
                                field: "my field value"
                            }
                        }
                    }
                },
                {
                    name: "Unit Test 2",
                    status: "error",
                    error: "Invalid JSON in input or output record: Unexpected non-whitespace character after JSON at position 2"
                }]);
                done();
            });
        });

        it('Unit test rule with one valid unit test not passing', (done) => {
            request.post('/rules/unittest')
            .send({
                rule: {
                    name: 'MDR001',
                    active: false,
                    hideInResult: false,
                    showReject: false,
                    requiresReview: false,
                    includedInitialServiceCheck: false,
                    title: '',
                    rootCauseCategory: 'Administration',
                    level: 0,
                    description: '',
                    wikiPage: '',
                    ruleType: 'Logical',
                    isWarning: false,
                    displayInSummary: false,
                    failedInSummary: true,
                    preSources: [],
                    preSourcesRunOnFail: false,
                    preRules: [],
                    preRulesRunOnFail: false,
                    runWhenDependenciesResolved: false,
                    depedenciesFromRelatedServiceChecks: false,
                    runForEachPreSource: false,
                    preCondition: 's.SourceName && false',
                    preConditionMsg: 'Precondition Failed Message Here',
                    trueMsg: 'True Message Here',
                    falseMsg: 'False Message Here',
                    errorMsg: '',
                    ruleCode: 'data.field = s.SourceName.field;',
                    valueMsgStm: '"test " + data.field',
                    extraInfo: '',
                    ignoreCompare: false,
                    overrideCompare: '',
                    unitTests: [{
                        name: 'Unit Test 1',
                        result: 'OK',
                        inputRecord: '{"sourcesData": {"SourceName": {"field": "my field value"}}}',
                        outputRecord: '{"sourcesData": {"SourceName": {"field": "my field value"}},\
                                       "rulesData": {"MDR001": {"msg": "True Message Here", "result": "OK",\
                                       "rootCauseCategory": "Administration", "status": "done", "type": "Logical", "valueMsg": "test my field value"}}}'
                    }],
                    action: null,
                }
            })
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(200);
                chai.expect(res.body).be.like([{
                    name: "Unit Test 1",
                    status: "failed",
                    outputRecord: {
                        rulesData: {
                            MDR001: {
                                msg: "Precondition Failed Message Here",
                                result: "Reject",
                                rootCauseCategory: "Administration",
                                status: "done",
                                type: "Logical"
                            }
                        },
                        sourcesData: {
                            SourceName: {
                                field: "my field value"
                            }
                        }
                    },
                    expectedRecord: {
                        rulesData: {
                            MDR001: {
                                msg: "True Message Here",
                                result: "OK",
                                rootCauseCategory: "Administration",
                                status: "done",
                                type: "Logical",
                                valueMsg: "test my field value"
                            }
                        },
                        sourcesData: {
                            SourceName: {
                                field: "my field value"
                            }
                        }
                    }
                }]);
                done();
            });
        });

        it('Unit test saved rule with empty unit tests', (done) => {
            let promises = [];

            promises.push(new Rule({
                name: 'MDR001'
            }).save());

            Promise.all(promises).then(() => {
                request.post('/rules/MDR001/unittest')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body).to.eql([]);
                    done();
                });
            });
        });
    });
});