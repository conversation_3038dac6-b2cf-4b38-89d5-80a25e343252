

.testResultsGrid {
    margin-top: 1rem;
    display: grid;
    grid-template-columns: repeat(2, 1fr); // Two equal-width columns
    gap: 1rem; // Spacing between panels
}

.timestamp {
    margin-bottom: 1rem;
}

.testResult {
    max-width: 32rem !important;
    white-space: nowrap; /* Prevent inline-block elements from wrapping */
}

.testResultHeader {
    display:flex;
    align-items: center;
    justify-content: space-between;
}

.toggle {
    width: fit-content;
}

.toggles {
    display: flex;
    gap: 1rem;
}
