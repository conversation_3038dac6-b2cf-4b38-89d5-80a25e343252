import '@able/react/dist/able-react.min.css'
import '@able/web/dist/able-web.css'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { ErrorBoundary } from 'react-error-boundary'
import { BrowserRouter } from 'react-router'
import { App } from './App'
import { ErrorFallback } from './components/errorFallback/ErrorFallback'
import { SocketProvider } from './context/SocketContext'
import { UserProvider } from './context/UserContext'

const queryClient = new QueryClient({
    defaultOptions: {
        queries: {
            refetchOnWindowFocus: false, // Do not refetch when window regains focus
            refetchOnReconnect: false, // Do not refetch on network reconnection
            refetchInterval: false, // Disable polling/refetch intervals
            staleTime: Infinity, // Data remains fresh indefinitely
            retry: false,
        },
    },
})

createRoot(document.getElementById('react-root')!).render(
    <StrictMode>
        <BrowserRouter>
            <UserProvider>
                <SocketProvider>
                    <QueryClientProvider client={queryClient}>
                        <ErrorBoundary FallbackComponent={ErrorFallback}>
                            <App />
                        </ErrorBoundary>
                    </QueryClientProvider>
                </SocketProvider>
            </UserProvider>
        </BrowserRouter>
    </StrictMode>
)
