
export class NotFoundError extends Error {
    constructor(message) {
        super(message);
        this.name = "NotFoundError";
    }
}


export class NotImplementedError extends Error {
    constructor(message) {
        super(message);
        this.name = "NotImplementedError";
    }
}


export class InactiveError extends Error {
    constructor(message) {
        super(message);
        this.name = "InactiveError";
    }
}

export class ApiUriError extends Error {
    constructor(message, options) {
        super(message, options);
        this.name = "ApiUriError";
    }

    toString() {
        return this.cause ? `${super.toString()} [cause] ${this.cause.toString()}` : super.toString();
    }
}


export class RuleCodeError extends Error {
    constructor(message, options) {
        super(message, options);
        this.name = "RuleCodeError";
    }

    toString() {
        return this.cause ? `${super.toString()} [cause] ${this.cause.toString()}` : super.toString();
    }
}


export class SourceCodeError extends Error {
    constructor(message, options) {
        super(message, options);
        this.name = "SourceCodeError";
    }

    toString() {
        return this.cause ? `${super.toString()} [cause] ${this.cause.toString()}` : super.toString();
    }
}


export class SaveSourceDataError extends Error {
    constructor(message, options) {
        super(message, options);
        this.name = "SaveSourceDataError";
    }

    toString() {
        return this.cause ? `${super.toString()} [cause] ${this.cause.toString()}` : super.toString();
    }
}



export class AuthenticationError extends Error {
    constructor(message) {
        super(message);
        this.name = "AuthenticationError";
    }
}


export class ServiceCheckAccessError extends Error {
    constructor(message) {
        super(message);
        this.name = "ServiceCheckAccessError";
    }
}


export class ApiFunctionError extends Error {
    constructor(message, options) {
        super(message, options);
        this.name = "ApiFunctionError";
    }
}


export class ServiceCheckSearchError extends Error {
    constructor(message, options) {
        super(message, options);
        this.name = "ServiceCheckSearchError";
    }

    toString() {
        return this.cause ? `${super.toString()} [cause] ${this.cause.toString()}` : super.toString();
    }
}


export class ServiceCheckLockError extends Error {
    constructor(message, options) {
        super(message, options);
        this.name = "ServiceCheckLockError";
    }
}


export class RuleNotActionableError extends Error {
    constructor(message, options) {
        super(message, options);
        this.name = "RuleNotActionableError";
    }
}
