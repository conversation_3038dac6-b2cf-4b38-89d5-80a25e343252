{"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1, "nullable": false}, "title": {"type": "string", "nullable": false}, "sections": {"type": "array", "nullable": false, "items": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1, "nullable": false}, "title": {"type": "string", "nullable": false}, "ruleName": {"type": "string", "minLength": 1, "nullable": false}}, "required": ["name", "title", "ruleName"], "additionalProperties": false}}, "productTypes": {"type": "array", "nullable": false, "items": {"type": "string", "minLength": 1, "nullable": false}}, "hasDateRangeFilter": {"type": "boolean", "nullable": false}, "defaultStartOffsetHours": {"type": "number", "nullable": true}}, "required": ["name", "title", "sections", "productTypes", "hasDateRangeFilter", "defaultStartOffsetHours"], "additionalProperties": false}}