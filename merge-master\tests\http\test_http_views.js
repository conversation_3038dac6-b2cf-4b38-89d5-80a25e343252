
import mongoose from 'mongoose';
import chai from 'chai';
import chaiHttp from 'chai-http';
import chai<PERSON>ike from 'chai-like';
const should = chai.should();
import superagentDefaults from 'superagent-defaults';
import supertest from 'supertest';
import _ from 'lodash';
import assert from 'assert';
import cookie from 'cookie';


var app;
var request;
import config from '../config.js';
import helpers from '../helpers.js';
import { UNIT_TEST_ALL_ACCESS_USERNAME, UNIT_TEST_USERS } from '../users.js';


chai.use(chaiLike);
chai.use(chaiHttp);


// Only tests that these endpoints can be accessed and return HTTP 200
describe('Merge Page Views', () => {
    // Setup and connect to mongodb memory server, start merge app instance
    // Set request variable with a configured supertest agent with test JWT and app instance
    before(async function() {
        global.gConfig = _.cloneDeep(config.default);
        app = (await import('../../app.js')).default;
        request = superagentDefaults(supertest(app));
    });

    // Drop all contents of database before each test
    beforeEach(async function() {
        global.gConfig = _.cloneDeep(config.default);
        // Uses deleteMany instead of mongoose.connection.db.dropDatabase() because
        // custom indexes appear to be deleted if dropDatabase() is used
        for (let collectionName in mongoose.connection.collections) {
            await mongoose.connection.collections[collectionName].deleteMany({});
        }
    });

    describe('Home page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/');

            res.should.have.status(200);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/');

                switch(username) {
                    case 'readOnly':
                    case 'level0':
                    case 'level1':
                    case 'level2':
                    case 'level3':
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                    case 'commandAccess':
                    case 'apiAccess':
                    case 'offshoreAccess':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('User info page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/my/info');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/my/info');

                switch(username) {
                    case 'readOnly':
                    case 'level0':
                    case 'level1':
                    case 'level2':
                    case 'level3':
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                    case 'commandAccess':
                    case 'apiAccess':
                    case 'offshoreAccess':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('Service check run page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/serviceCheck');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/serviceCheck');

                switch(username) {
                    case 'level0':
                    case 'level1':
                    case 'level2':
                    case 'level3':
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                    case 'commandAccess':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('Compare service check page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/compareResults');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/compareResults');

                switch(username) {
                    case 'level0':
                    case 'level1':
                    case 'level2':
                    case 'level3':
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                    case 'commandAccess':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('Source status page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/sourceStatus');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/sourceStatus');

                switch(username) {
                    case 'readOnly':
                    case 'level0':
                    case 'level1':
                    case 'level2':
                    case 'level3':
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                    case 'commandAccess':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('Command run page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/command');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/command');

                switch(username) {
                    case 'commandAccess':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('Edit APIs list page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/edit/apis');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/edit/apis');

                switch(username) {
                    case 'admin':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('Edit message buckets list page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/edit/messageBuckets');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/edit/messageBuckets');

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('Edit outcomes list page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/edit/outcomes');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/edit/outcomes');

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('Edit rules list page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/edit/rules');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/edit/rules');

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('Edit sources list page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/edit/sources');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/edit/sources');

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('Edit text templates list page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/edit/templates');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/edit/templates');

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('Rule / source graph page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/config/graph');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/config/graph');

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('Config upload page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/config/upload');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/config/upload');

                switch(username) {
                    case 'admin':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('Statistics graphs page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/admin/graphs');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/admin/graphs');

                switch(username) {
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('Statistics table page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/admin/statistics');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/admin/statistics');

                switch(username) {
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('Service check record history page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/serviceCheck/history/view/all');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests (all)', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/serviceCheck/history/view/all');

                switch(username) {
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Authorization tests (my)', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/serviceCheck/history/view/my');

                switch(username) {
                    case 'level0':
                    case 'level1':
                    case 'level2':
                    case 'level3':
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                    case 'commandAccess':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('Service check unique cases page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/admin/uniqueCasesTool');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/admin/uniqueCasesTool');

                switch(username) {
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('Command record history page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/command/history/view/all');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests (all)', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/command/history/view/all');

                switch(username) {
                    case 'commandAccess':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Authorization tests (my)', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/command/history/view/my');

                switch(username) {
                    case 'commandAccess':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('Config differences page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/config/differences');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/config/differences');

                switch(username) {
                    case 'admin':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('User statistics page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/admin/userStat');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/admin/userStat');

                switch(username) {
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('Rule code errors page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/ruleStatus');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/ruleStatus');

                switch(username) {
                    case 'admin':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('Rule code failed page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/ruleStatusFailed');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/ruleStatusFailed');

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('Banner page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/admin/banner');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/admin/banner');

                switch(username) {
                    case 'admin':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('Service check running status page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/admin/status');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/admin/status');

                switch(username) {
                    case 'admin':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('API docs page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/api-docs/');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/api-docs/');

                switch(username) {
                    case 'readOnly':
                    case 'level0':
                    case 'level1':
                    case 'level2':
                    case 'level3':
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                    case 'commandAccess':
                    case 'apiAccess':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('API docs download', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/api-docs/download');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/api-docs/download');

                switch(username) {
                    case 'readOnly':
                    case 'level0':
                    case 'level1':
                    case 'level2':
                    case 'level3':
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                    case 'commandAccess':
                    case 'apiAccess':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('API docs OKAPI page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/api-docs-okapi/');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/api-docs-okapi/');

                switch(username) {
                    case 'readOnly':
                    case 'level0':
                    case 'level1':
                    case 'level2':
                    case 'level3':
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                    case 'commandAccess':
                    case 'apiAccess':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('API docs OKAPI download', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/api-docs-okapi/download');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/api-docs-okapi/download');

                switch(username) {
                    case 'readOnly':
                    case 'level0':
                    case 'level1':
                    case 'level2':
                    case 'level3':
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                    case 'commandAccess':
                    case 'apiAccess':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('Product types page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/productTypes');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/productTypes');

                switch(username) {
                    case 'readOnly':
                    case 'level0':
                    case 'level1':
                    case 'level2':
                    case 'level3':
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                    case 'commandAccess':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });


    describe('Changelog page', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/changelog');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/changelog');

                switch(username) {
                    case 'readOnly':
                    case 'level0':
                    case 'level1':
                    case 'level2':
                    case 'level3':
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                    case 'commandAccess':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

});