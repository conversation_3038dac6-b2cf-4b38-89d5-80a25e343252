'use strict';

import assert from 'assert';

import { InputTypes } from '../../modules/enumerations.js';
import { identifyInputType } from '../../modules/inputType.js';



describe('Input type functions', () => {
    describe('identifyInputType()', () => {
        it('null', () => {
            assert.strictEqual(identifyInputType(null), InputTypes.other);
        });

        it('undefined', () => {
            assert.strictEqual(identifyInputType(undefined), InputTypes.other);
        });

        it('number type', () => {
            assert.strictEqual(identifyInputType(100), InputTypes.other);
        });

        it('boolean type', () => {
            assert.strictEqual(identifyInputType(true), InputTypes.other);
        });

        it('object', () => {
            assert.strictEqual(identifyInputType({}), InputTypes.other);
        });

        it('empty string', () => {
            assert.strictEqual(identifyInputType(''), InputTypes.other);
        });

        it('FNN test 1', () => {
            assert.strictEqual(identifyInputType('N1234567R'), InputTypes.fnn);
        });

        it('FNN test 2', () => {
            assert.strictEqual(identifyInputType('N1234567L'), InputTypes.fnn);
        });

        it('FNN test 3', () => {
            assert.strictEqual(identifyInputType('N1234567A'), InputTypes.fnn);
        });

        it('Phone number test 1', () => {
            assert.strictEqual(identifyInputType('03 9123 1234'), InputTypes.phoneNumber);
        });

        it('Phone number test 2', () => {
            assert.strictEqual(identifyInputType('0391231234'), InputTypes.phoneNumber);
        });

        it('Phone number test 3', () => {
            assert.strictEqual(identifyInputType('+613 9123 1234'), InputTypes.phoneNumber);
        });

        it('Mobile phone number test 1', () => {
            assert.strictEqual(identifyInputType('04 9123 1234'), InputTypes.mobilePhoneNumber);
        });

        it('Mobile phone number test 2', () => {
            assert.strictEqual(identifyInputType('0491231234'), InputTypes.mobilePhoneNumber);
        });

        it('Mobile phone number test 3', () => {
            assert.strictEqual(identifyInputType('+614 9123 1234'), InputTypes.mobilePhoneNumber);
        });

        it('Device name test 1', () => {
            assert.strictEqual(identifyInputType('ABCDEFGR01C01'), InputTypes.deviceName);
        });

        it('Device name test 2', () => {
            assert.strictEqual(identifyInputType('ABCDEFGHIR01C01'), InputTypes.deviceName);
        });

        it('Device name test 3', () => {
            assert.strictEqual(identifyInputType('ABCDEFIR01GNA'), InputTypes.deviceName);
        });

        it('International link test 1', () => {
            assert.strictEqual(identifyInputType('MEL GIP 9505363'), InputTypes.internationalLink);
        });

        it('International link test 2', () => {
            assert.strictEqual(identifyInputType('ANY TPE EPL 90044519'), InputTypes.internationalLink);
        });

        it('International link test 3', () => {
            assert.strictEqual(identifyInputType('SNG WSH EPL 90033976'), InputTypes.internationalLink);
        });

        it('AVC test 1', () => {
            assert.strictEqual(identifyInputType('AVC123412341234'), InputTypes.avc);
        });

        it('AVC test 2', () => {
            assert.strictEqual(identifyInputType('AVC000000000000'), InputTypes.avc);
        });

        it('AVC test 3', () => {
            assert.strictEqual(identifyInputType('AVC000000123456'), InputTypes.avc);
        });

        it('OVC test 1', () => {
            assert.strictEqual(identifyInputType('OVC123412341234'), InputTypes.ovc);
        });

        it('OVC test 2', () => {
            assert.strictEqual(identifyInputType('OVC000000000000'), InputTypes.ovc);
        });

        it('OVC test 3', () => {
            assert.strictEqual(identifyInputType('OVC000000123456'), InputTypes.ovc);
        });

        it('BDSL FNN test 1', () => {
            assert.strictEqual(identifyInputType('Y00000012345N'), InputTypes.bdslFnn);
        });

        it('BDSL FNN test 2', () => {
            assert.strictEqual(identifyInputType('Y00000012345L'), InputTypes.bdslFnn);
        });

        it('BDSL FNN test 3', () => {
            assert.strictEqual(identifyInputType('Y00000000000N'), InputTypes.bdslFnn);
        });

        it('IMSI test 1', () => {
            assert.strictEqual(identifyInputType('041231231230000'), InputTypes.imsi);
        });

        it('IMSI test 2', () => {
            assert.strictEqual(identifyInputType('511122223333444'), InputTypes.imsi);
        });

        it('IMSI test 3', () => {
            assert.strictEqual(identifyInputType('515151515151222'), InputTypes.imsi);
        });

        it('UUID test 1', () => {
            assert.strictEqual(identifyInputType('724521a6-a63a-4d7a-92c0-d754b09ad344'), InputTypes.uuid);
        });

        it('UUID test 2', () => {
            assert.strictEqual(identifyInputType('3eBC681b-34be-4A10-9e18-91f8b3A6357C'), InputTypes.uuid);
        });

        it('UUID test 3', () => {
            assert.strictEqual(identifyInputType('8B97F616-E82E-4453-A71B-98F0B5F76B34'), InputTypes.uuid);
        });

        it('DOM ID test 1', () => {
            assert.strictEqual(identifyInputType('mns8vmele01mvi'), InputTypes.domId);
        });

        it('DOM ID test 2', () => {
            assert.strictEqual(identifyInputType('s174vmbne02v06'), InputTypes.domId);
        });

        it('DOM ID test 3', () => {
            assert.strictEqual(identifyInputType('szcdtrave03v06'), InputTypes.domId);
        });

        it('CFS ID test 1', () => {
            assert.strictEqual(identifyInputType('AAAA12345678'), InputTypes.cfsId);
        });

        it('CFS ID test 2', () => {
            assert.strictEqual(identifyInputType('EFGH123456782285'), InputTypes.cfsId);
        });

        it('CFS ID test 3', () => {
            assert.strictEqual(identifyInputType('EFGH123456782264'), InputTypes.cfsId);
        });

        it('Other test 1', () => {
            assert.strictEqual(identifyInputType('NOT APPLICABLE'), InputTypes.other);
        });

        it('Other test 2', () => {
            assert.strictEqual(identifyInputType('JASPER M2M 102942320'), InputTypes.other);
        });

        it('Other test 3', () => {
            assert.strictEqual(identifyInputType('Backhaul_FNN_123456'), InputTypes.other);
        });
    });
});