<%- include('header', {}); %>
<%- include('menu', {currentTab: 'Home'}); %>
<div class="container">
    <br>
    <div class="border border-secondary container rounded" style="height:80vh;overflow-y:auto;padding-bottom:16px;">
        <br>
        <h1 class="display-4">Status</h1>
        <div class="row">
            <div class="col-md-10">
                <p id="updatedAt"></p>
            </div>
            <div class="col-md-2">
                <div class="float-right">
                    <button class="btn btn-primary" title="Refresh" id="statusRefresh"><span class="fas fa-sync"></span></button>
                </div>
            </div>
        </div>
        <br>
        <div>
            <p>Instance hostname: <span id="infoHostname" class="badge badge-primary"></span></p>
            <p>Active service checks: <span id="infoServiceCheckRunningCount" class="badge badge-primary"></span></p>
            <ul id="infoServiceCheckRunningList" class="list-group border border-secondary" style="max-height:16em;overflow:scroll;"></ul>
        </div>
    </div>
</div>
<script>
    $(document).ready(function() {
        statusRefresh();

        $("#statusRefresh").click(function() {
            statusRefresh();
        });
    });

    function statusRefresh() {
        setButtonSpinner($("#statusRefresh"), $("<span>").addClass("fas fa-sync"), true);
        $.ajax({
            type: "GET",
            url: "/admin/runningStatus",
            data: {},
            dataType: "json",
            success: function (response) {
                $("#infoHostname").text(response.hostname);

                if (typeof response.serviceChecks === 'object' && response.serviceChecks !== null) {
                    let numActiveServiceChecks = Object.keys(response.serviceChecks).length;
                    $("#infoServiceCheckRunningCount").text(numActiveServiceChecks);
                    $("#infoServiceCheckRunningList").html("");

                    if (numActiveServiceChecks === 0) {
                        $("#infoServiceCheckRunningList").append('<li class="list-group-item"><i>No active service checks</i></li>');
                    } else {
                        for (const [id, status] of Object.entries(response.serviceChecks)) {
                            let startDate = new Date(status.start).toLocaleString('en-GB');
                            let listContents = `<li class="list-group-item">${id} [Started: ${startDate}] <div class="float-right"><b>${status.startedBy}</b></div></li>`;
                            $("#infoServiceCheckRunningList").append(listContents);
                        }
                    }
                }

                let currDate = new Date().toLocaleString('en-GB');
                $("#updatedAt").text(`Updated at: ${currDate}`);
            },
            error: function (err) {
                alert("Error obtaining status information for socket");
            },
            complete: function(err) {
                setButtonSpinner($("#statusRefresh"), $("<span>").addClass("fas fa-sync"), false);
            }
        });
    }
</script>
<%- include('footer', {}); %>
