
import assert from 'assert';
import _ from 'lodash';

import Source from '../../db/model/source.js';
import mergeConfigList from '../../modules/mergeConfigList.js';
import { MERGE_OBJECT_CONFIG_FILES } from '../../modules/helpers/constants.js';
import helpers from '../helpers.js';


describe('Merge Sources', () => {
    it('Unique names', async() => {
        let sources = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.ruleSource)).sources;
        let sourceNames = new Set();

        for (let i in sources) {
            if (sourceNames.has(sources[i].name)) {
                assert.fail(`Duplicate source name ${sources[i].name} in config`);
            }
            sourceNames.add(sources[i].name);
        };
    });

    it('Valid fields', async() => {
        let sources = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.ruleSource)).sources;

        for (let i in sources) {
            let source = new Source(sources[i]);
            try {
                await source.validate();
            } catch(error) {
                let newError = new Error(`Error validating source ${source.name}`);
                newError.original_error = error;
                newError.stack = error.stack;
                throw newError;
            }

        }
    });

    it('Ordered by name ascending', async () => {
        let sources = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.ruleSource)).sources;

        for (let i = 1; i < sources.length; i++) {
            let prevSource = sources[i - 1];
            let currSource = sources[i];

            if (prevSource.name.localeCompare(currSource.name, 'en') > 0) {
                assert.fail(`Source name ${prevSource.name} is out of order, should be after source ${currSource.name}`);
            }
        }
    });

    it('Correct field order', async() => {
        let sources = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.ruleSource)).sources;
        let invalidNames = [];

        for (let i in sources) {
            let sourceJson = new Source(sources[i]).toJSON();
            delete sourceJson.createdBy;
            delete sourceJson.createdOn;

            if (!_.isEqualWith(sources[i], sourceJson, helpers.compareObjectWithKeyOrder)) {
                invalidNames.push(sources[i].name);
            };
        }

        if (invalidNames.length) {
            assert.fail(`Key order for source(s) ${invalidNames.join(',')} does not match database schema order`);
        }
    });
});

