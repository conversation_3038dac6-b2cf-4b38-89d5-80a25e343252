ARG DEPENDENCIES_IMAGE
FROM ${DEPENDENCIES_IMAGE}

WORKDIR /usr/src/app

# Creates log directory and copies all files to workdir
RUN mkdir -p /var/log/merge
RUN mkdir -p /var/backup/db
COPY bin ./bin
COPY clients ./clients
COPY config ./config
COPY db ./db
COPY log ./log
COPY migrations ./migrations
COPY modules ./modules
COPY public ./public
COPY routes ./routes
COPY run ./run
COPY sockets ./sockets
COPY views ./views
COPY app.js .
COPY CHANGELOG.md .
COPY package.json .
COPY package-lock.json .

# Builds React JS project
WORKDIR /usr/src/app/clients/serviceCheckApp
RUN npm install --save-dev
RUN npm run build
WORKDIR /usr/src/app

# Removes config file from image, will need to be mounted with /usr/src/app/config/config.json
# on container creation
RUN rm ./config/config.json

CMD [ "bin/merge.js" ]
