
import mongoose from 'mongoose';

const textTemplateMetadata = new mongoose.Schema({
    version: { type: Number, required: true },
    updatedBy: { type: String, required: true },
    updatedOn: { type: Date, required: true, default: Date.now }
});


textTemplateMetadata.methods.toJSON = function() {
    let obj = this.toObject();
    delete obj._id;
    delete obj.__v;
    return obj;
}


export default mongoose.model('texttemplate_metadata', textTemplateMetadata);