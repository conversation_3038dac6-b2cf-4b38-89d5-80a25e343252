{"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1, "nullable": false}, "responseKeyMap": {"type": "array", "nullable": false, "items": {"type": "string", "minLength": 1, "nullable": false}}, "productTypes": {"type": "array", "nullable": false, "items": {"type": "string", "minLength": 1, "nullable": false}}}, "required": ["name", "responseKeyMap", "productTypes"], "additionalProperties": false}}