# Summary of Findings - Prioritised

This document provides a consolidated list of all identified vulnerabilities, classified and ordered from most critical to informational, based on the defined severity levels.

## Severity Level Definitions

-   **Critical (CVSS 9.0-10.0):** Vulnerabilities that allow for remote code execution, full system compromise, or unauthorized access to and exfiltration/modification of all sensitive data. Exploitation is typically straightforward.
-   **High (CVSS 7.0-8.9):** Vulnerabilities that allow for significant unauthorized access, data modification/exfiltration of a large subset of sensitive data, or denial of service against critical application functions. Exploitation might require some specific conditions but is generally achievable.
-   **Medium (CVSS 4.0-6.9):** Vulnerabilities that allow for limited unauthorized data access or modification, denial of service against specific non-critical functions, or require more complex/less likely exploitation scenarios.
-   **Low (CVSS 0.1-3.9):** Vulnerabilities with minor impact, often requiring unlikely pre-conditions or providing limited advantage to an attacker. These typically involve minor information leaks or weaknesses that might contribute to other attacks in a limited way.
-   **Informational (CVSS N/A):** Observations that are not direct vulnerabilities but represent deviations from security best practices or areas for potential future hardening.

---

## Critical Vulnerabilities

| Vulnerability Title                                                         | OWASP/CWE                                                                 | Severity |
| :-------------------------------------------------------------------------- | :------------------------------------------------------------------------ | :------- |
| C-001: Hardcoded Cryptographic Key in `modules/customEncrypt.js`            | A02:2021 / CWE-321                                                        | CRITICAL |
| C-002: Static/Predictable IV in `modules/customEncrypt.js`                  | A02:2021 / CWE-329                                                        | CRITICAL |

## High Vulnerabilities

| Vulnerability Title                                                              | OWASP/CWE                                   | Severity |
| :------------------------------------------------------------------------------- | :------------------------------------------ | :------- |
| H-001: Disabled TLS Certificate Validation for LDAP (`auth.js`)                  | A02:2021 / CWE-295                          | HIGH     |
| H-002: Disabled TLS Cert Validation for Downstream APIs (`apiCall.js`)           | A02:2021 / CWE-295                          | HIGH     |
| H-003: Potential Argument Injection in Command Execution (`commandParser.js`)    | A03:2021 / CWE-88                           | HIGH     |

## Medium Vulnerabilities

*The following Medium severity findings are detailed in `Detailed_Findings.md`:*

| Finding ID | Vulnerability Title                                          | OWASP/CWE                                   | Severity |
| :--------- | :----------------------------------------------------------- | :------------------------------------------ | :------- |
| M-001      | User Enumeration via Login Response Discrepancy              | A07:2021 / CWE-204                          | MEDIUM   |
| M-002      | Weak Password Hashing Algorithm or Insufficient Work Factor  | A02:2021 / CWE-916, CWE-326                 | MEDIUM   |
| M-003      | Insecure Session Cookie Configuration                        | A05:2021 / CWE-1004, CWE-614, CWE-352       | MEDIUM   |
| M-004      | Lack of Brute-Force Protections on Login Endpoints           | A07:2021 / CWE-307                          | MEDIUM   |

## Low Vulnerabilities

*The following Low severity findings are detailed in `Detailed_Findings.md`:*

| Finding ID | Vulnerability Title                                          | OWASP/CWE                                   | Severity |
| :--------- | :----------------------------------------------------------- | :------------------------------------------ | :------- |
| L-001      | Potential Insecure Direct Object Reference (IDOR) on API Endpoints | A01:2021 / CWE-639                          | LOW      |
| L-002      | Logout CSRF                                                  | A01:2021 / CWE-352                          | LOW      |

---
