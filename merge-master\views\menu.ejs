<!-- Merge Menu v3-->
<% if (bannerMessage) { %>
<div class="d-block banner py-2 bg-warning border-bottom border-secondary">
    <p class="text-center text-bold m-0"><%= bannerMessage %></p>
</div>
<% } %>
<nav class="navbar navbar-expand-lg navbar-light bg-light fixed-top sticky-top">
    <!-- LOGO -->
    <a href="https://telstra.unily.com" class="navbar-right" title="Telstra Interanet Home Page"><img  width="30px" height="44px" src="/public/images/TelstraBrand.svg"></a>
    <a class="navbar-brand" href="/" title="Merge Home Page">Merge</a>
    <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent"
        aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarSupportedContent">
        <ul class="navbar-nav mr-auto">
            <% if (user && (user.level0 || user.level1 || user.level2 || user.level3 || user.commandAccess || user.isDeveloper || user.isAdmin || user.levelLead)) { %>
            <li class="nav-item"><a class="nav-link" href="/serviceCheck">Service Check</a></li>
            <li class="nav-item"><a class="nav-link" href="/compareResults">Compare</a></li>
            <% } %>
            <li class="nav-item"><a class="nav-link" href="/sourceStatus">Status</a></li>
            <% if (user && user.commandAccess) { %>
            <li class="nav-item"><a class="nav-link" href="/command">Quick Command</a></li>
            <% } %>
            <% if (user && user.isAAAUser) { %>
            <li class="nav-item"><a class="nav-link" target="_blank" href="<%=global.gConfig.HAPIURI.slice(0, -5)%>AAAData/index.php?userid=<%=user.encryptedUsername%>">AAA Tool</a></li>
            <% } %>
            <% if (user && (user.isDeveloper || user.isAdmin || user.levelLead)) { %>
            <!-- Develop Menu-->
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="myDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Develop</a>
                <div class="dropdown-menu" aria-labelledby="myDropdown">
                    <% if (user.isAdmin) { %>
                    <a class="dropdown-item" href="/edit/apis" target="_Merge_APIs">Edit APIs</a>
                    <% } %>
                    <a class="dropdown-item" href="/edit/messageBuckets" target="_Merge_MessageBuckets">Edit Message Buckets</a>
                    <a class="dropdown-item" href="/edit/outcomes" target="_Merge_Outcomes">Edit Outcomes</a>
                    <a class="dropdown-item" href="/edit/rules" target="_Merge_Rules">Edit Rules</a>
                    <a class="dropdown-item" href="/edit/sources" target="_Merge_Sources">Edit Sources</a>
                    <a class="dropdown-item" href="/edit/templates" target="_Merge_Templates">Edit Templates</a>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" href="/config/graph">Rules / Sources Graph</a>
                    <% if (user.isAdmin) { %>
                    <a class="dropdown-item" href="/config/graphStatus">Rules / Sources Graph Status</a>
                    <% } %>
                    <div class="dropdown-submenu">
                        <a class="dropdown-item dropdown-toggle" href="#">Download Config</a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/config/download/api">APIs</a></li>
                            <li><a class="dropdown-item" href="/config/download/messagebucket">Message Buckets</a></li>
                            <li><a class="dropdown-item" href="/config/download/outcome">Outcomes</a></li>
                            <li><a class="dropdown-item" href="/config/download/rulesource">Rules / Sources</a></li>
                            <li><a class="dropdown-item" href="/config/download/template">Text Templates</a></li>
                        </ul>
                    </div>
                    <% if (user.isAdmin) { %>
                    <a class="dropdown-item" href="/config/upload">Upload Config</a>
                    <% } %>
                </div>
            </li>
            <% } %>
            <!-- Admin Menu-->
            <% if (user && (user.isAdmin || user.levelLead || user.isDeveloper ) ) { %>
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-toggle="dropdown"
                    aria-haspopup="true" aria-expanded="false">Admin</a>
                <div class="dropdown-menu" aria-labelledby="adminDropdown">
                    <% if (user.isAdmin || user.levelLead || user.isDeveloper) { %>
                        <% if (user.isAdmin || user.levelLead) { %>
                        <a class="dropdown-item" href="/admin/graphs">Service Check Graphs</a>
                        <a class="dropdown-item" href="/admin/statistics">Service Check Statistics</a>
                        <a class="dropdown-item" href="/serviceCheck/history/view/all">Service Check History</a>
                        <a class="dropdown-item" href="/admin/uniqueCasesTool">Service Check Unique Cases Tool</a>
                        <a class="dropdown-item" href="/command/history/view/all">Commands History</a>
                        <a class="dropdown-item" href="/admin/userStat">Users Statistics</a>
                        <% } %>
                        <% if (user.isAdmin || user.levelLead || user.isDeveloper) { %>
                        <a class="dropdown-item" href="/serviceCheck/advancedSearch">Service Check Advanced Search</a>
                        <% } %>
                        <% if (user.isAdmin) { %>
                        <a class="dropdown-item" href="/config/differences">Config Differences / Deploy</a>
                        <% } %>
                        <div class="dropdown-divider"></div>
                    <% } %>
                    <a class="dropdown-item" href="/productTypes">Product Types</a>
                    <div class="dropdown-divider"></div>
                    <% if (user.isAdmin) { %>
                    <a class="dropdown-item" href="/ruleStatus">Rule Status (code errors)</a>
                    <% } %>
                    <a class="dropdown-item" href="/ruleStatusFailed">Rule Status (failed)</a>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" href="https://merge.in.telstra.com.au/">Merge Production</a>
                    <a class="dropdown-item" href="https://s1.merge.in.telstra.com.au/">Merge Production S1 (pucq-merg-002)</a>
                    <a class="dropdown-item" href="https://s2.merge.in.telstra.com.au/">Merge Production S2 (puex-merg-002)</a>
                    <a class="dropdown-item" href="https://s3.merge.in.telstra.com.au/">Merge Production S3 (pucq-merg-003)</a>
                    <a class="dropdown-item" href="https://s4.merge.in.telstra.com.au/">Merge Production S4 (puex-merg-003)</a>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" href="https://merge-stage.in.telstra.com.au/">Merge Stage</a>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" href="https://merge-testing.in.telstra.com.au/">Merge Testing</a>
                    <% if (user.isAdmin) { %>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" href="/admin/banner">Edit Banner Message</a>
                    <a class="dropdown-item" href="/admin/status">Service Check Status</a>
                    <% } %>
                </div>
            </li>
            <% } %>

            <!-- Links -->
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Links</a>
                <div class="dropdown-menu" aria-labelledby="navbarDropdown">
                    <a class="dropdown-item" target="_blank" href="/geospatialMap/coverage">Geospatial Coverage Map</a>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" target="_blank" href="https://jira.tools.telstra.com/secure/RapidBoard.jspa?rapidView=10519&projectKey=TECS&view=planning.nodetail&quickFilter=108779&epics=visible&issueLimit=100">Merge Jira</a>
                    <a class="dropdown-item" target="_blank" href="https://teams.microsoft.com/l/team/19%3a0de00c07a4e04c4dac703e0016db7e0e%40thread.skype/conversations?groupId=849954dd-a75d-4da2-9cd9-b81e07231040&tenantId=49dfc6a3-5fb7-49f4-adea-c54e725bb854">Merge Microsoft Team</a>
                    <!--a class="dropdown-item" target="_blank" href="https://tasks.office.com/teamtelstra.onmicrosoft.com/en-US/Home/Planner#/plantaskboard?groupId=849954dd-a75d-4da2-9cd9-b81e07231040&planId=QWd4E6ZNeE-8n3hvrWnBEWQAFsqk">Merge Feedback Planner</a!-->
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" target="_blank" href="https://lxapp9923.in.telstra.com.au:8443/magpie/app">Magpie MEM North (Merge use it)</a>
                    <a class="dropdown-item" target="_blank" href="https://magpie.glb.in.telstra.com.au/">Magpie Global Load Balanced</a>
                    <a class="dropdown-item" target="_blank" href="http://odin2.ims.telstra.com.au/farm/odin/">ODIN</a>
                    <a class="dropdown-item" target="_blank" href="https://nbnportals.nbnco.net.au/online_customers/page/home#idp=telstra">NBN Portal SSO</a>
                </div>
            </li>
            <!-- Help -->
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="myDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Help</a>
                <div class="dropdown-menu" aria-labelledby="myDropdown">
                    <a class="dropdown-item" target="_Merge_Rules" href="/command/list">List of Commands</a>
                    <div class="dropdown-divider"></div>
                    <div class="dropdown-submenu">
                        <a class="dropdown-item dropdown-toggle" href="#">API Documentation</a>
                        <ul class="dropdown-menu">
                            <a class="dropdown-item" target="_blank" href="/api-docs">Standard</a>
                            <a class="dropdown-item" target="_blank" href="/api-docs-okapi">OKAPI</a>
                        </ul>
                    </div>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" target="_Merge_Wiki" href="https://confluence.tools.telstra.com/display/MERGE">Merge Wiki</a>
                    <a class="dropdown-item" target="_Merge_Manual" href="https://confluence.tools.telstra.com/display/MERGE/Merge+User+Manual">User Manual</a>
                    <a class="dropdown-item" target="_Merge_Demo" href="https://confluence.tools.telstra.com/display/MERGE/Merge+Demos+and+Training">Demos and Training</a>
                    <a class="dropdown-item" target="_Merge_Access" href="https://confluence.tools.telstra.com/display/MERGE/Merge+Access">Get Access</a>
                    <a class="dropdown-item" target="_Merge_RoadMap" href="https://confluence.tools.telstra.com/display/MERGE/Merge+Road+Map">Road Map</a>
                    <a class="dropdown-item" target="_Merge_Scope" href="https://confluence.tools.telstra.com/display/MERGE/Merge+Supported+Services+and+Scope">Supported Scope</a>
                    <div class="dropdown-divider"></div>
                    <div class="dropdown-item">
                        Version <%=global.gConfig.ver%> (<a href="/changelog" title="Changelog">Changelog</a>)
                    </div>
                    <div class="dropdown-item">Environment <%= global.gConfig.env %></div>
            </div>

            </li>

            <li class="nav-item btn-outline-secondary">
                <a class="nav-link" title="Please submit your feedback or request to Merge Team by this link"
                href="https://teams.microsoft.com/l/channel/19%3A72e52cc404e244abac8db5ab20a69023%40thread.skype/Feedback%20and%20SME%20Support?groupId=849954dd-a75d-4da2-9cd9-b81e07231040&tenantId=49dfc6a3-5fb7-49f4-adea-c54e725bb854"
                onclick="return !window.open(this.href, 'Merge Feedback', 'width=800,height=600')"
                target="_blank">
                Feedback/Request <span class="fas fa-comment-dots m-auto"></span>
                </a>
            </li>

        </ul>
        <!-- Version -->
        <div class="navbar-default" style="font-size:10px;">
        <a class="navbar-text" href="#" title="Server"><%= global.gHostname %></a>
        </div>
        <!-- My Menu-->
        <% if (user) { %>
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="myDropdown" role="button" data-toggle="dropdown"
                    aria-haspopup="true" aria-expanded="false"><span class="far fa-user"></span><%= user ? user.username : 'nobody' %>
                    <% if (user.SSU) { %>
                        <span><span class="color-bl"><i class="ssu-letter"><%= user.SSU.charAt(0).toUpperCase() %></i></span><i class="fas fa-circle color-bl ssu-circle"></i></span>
                    <% } %>
                </a>
                <div class="dropdown-menu dropdown-menu-right" aria-labelledby="myDropdown">
                    <a class="dropdown-item" href="/serviceCheck/history/view/my">Service Check History</a>
                    <a class="dropdown-item" href="/command/history/view/my">Commands History</a>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" href="/my/info">My Info</a>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" href="/auth/logout">Logout</a>

                </div>
            </div>
            <% } else {%>
            <a class="nav-item nav-link" href="/auth/login">Login</a>
            <%} %>
        <!-- TODO
        <form class="form-inline my-2 my-lg-0">
            <input class="form-control mr-sm-2" type="search" placeholder="Rule Code" aria-label="Search">
            <button class="btn btn-outline-success my-2 my-sm-0" type="submit">Search</button>
        </form>
        -->

    </div>
</nav>
<!-- End of Menu -->
