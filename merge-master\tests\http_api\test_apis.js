'use strict';

import mongoose from 'mongoose';
import chai from 'chai';
import chaiHttp from 'chai-http';
import chaiLike from 'chai-like';
import chaiDateTime from 'chai-datetime';
import superagentDefaults from 'superagent-defaults';
import supertest from 'supertest';
import cookie from 'cookie';
import _ from 'lodash';
import assert from 'assert';

var app;
var request;
import Api from '../../db/model/api.js';
import config from '../config.js';
import helpers from '../helpers.js';
import { UNIT_TEST_ALL_ACCESS_USERNAME, UNIT_TEST_USERS } from '../users.js';

const should = chai.should();

chaiLike.extend(helpers.dateFieldCheck);
chaiLike.extend(helpers.regexCheck);
chai.use(chaiLike);
chai.use(chaiHttp);
chai.use(chaiDateTime);


const API_KEYS = Object.freeze(Object.keys(Api.schema.obj));


describe('Merge APIs REST endpoints', () => {
    // Setup and connect to mongodb memory server, start merge app instance
    // Set request variable with a configured supertest agent with test JWT and app instance
    before(async function() {
        global.gConfig = _.cloneDeep(config.default);
        app = (await import('../../app.js')).default;
        request = superagentDefaults(supertest(app));
    });

    // Drop all contents of database before each test
    beforeEach(async function() {
        global.gConfig = _.cloneDeep(config.default);

        // Uses deleteMany instead of mongoose.connection.db.dropDatabase() because
        // custom indexes appear to be deleted if dropDatabase() is used
        for (let collectionName in mongoose.connection.collections) {
            await mongoose.connection.collections[collectionName].deleteMany({});
        }

        await helpers.authenticateSession(request, UNIT_TEST_ALL_ACCESS_USERNAME);
    });

    describe('Read list of APIs', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/apis');

            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/apis');

                switch(username) {
                    case 'admin':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Get empty list of APIs', async() => {
            let res = await request.get('/apis');

            res.should.have.status(200);
            chai.expect(res.body).to.eql({
                metadata: {
                    pagination: {
                        limit: 100,
                        offset: 0,
                        total: 0,
                        prev: null,
                        next: null
                    }
                },
                results: []
            });
        });

        it('Get list of APIs with one API (default values)', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'ApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);

            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(API_KEYS);
            });
        });

        it('Get list of APIs with three APIs (default values)', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            promises.push(new Api({
                name: 'ApiName2'
            }).save());

            promises.push(new Api({
                name: 'ApiName3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'ApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'ApiName2',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'ApiName3',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(API_KEYS);
            });
        });

        it('Get list of APIs with one API', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName',
                description: 'API Description Here',
                active: true,
                apiType: 'rest',
                method: 'get',
                parameters: ['a', 'b', 'c'],
                baseUrl: 'config.url',
                uri: '`/path/to/api`',
                queryParams: 'new Object({});',
                header: '`{"header-name": "value"}`',
                body: '`{"key": "value"}`',
                authKeyDb: [{ authKeyHeader: 'Authorization', authKeyName: 'AuthKeyName' }],
                timeout: 60000,
                parseResponse: 'JSON.parse(response.data)',
                pollCondition: '!(response.data.field)',
                proxyRequired: true,
                useCookies: true,
                tlsMinVersion: "TLSv1.1",
                errorCondition: '',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: 'MasslName',
                wikiPage: 'ApiName',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'ApiName',
                description: 'API Description Here',
                active: true,
                apiType: 'rest',
                method: 'get',
                parameters: ['a', 'b', 'c'],
                baseUrl: 'config.url',
                uri: '`/path/to/api`',
                queryParams: 'new Object({});',
                header: '`{"header-name": "value"}`',
                body: '`{"key": "value"}`',
                authKeyDb: [{ authKeyHeader: 'Authorization', authKeyName: 'AuthKeyName' }],
                timeout: 60000,
                parseResponse: 'JSON.parse(response.data)',
                pollCondition: '!(response.data.field)',
                proxyRequired: true,
                useCookies: true,
                tlsMinVersion: "TLSv1.1",
                errorCondition: '',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: 'MasslName',
                wikiPage: 'ApiName',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(API_KEYS);
            });
        });

        it('Get list of APIs with two APIs', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1',
                description: 'API Description Here',
                active: true,
                apiType: 'rest',
                method: 'get',
                parameters: ['a', 'b', 'c'],
                baseUrl: 'config.url',
                uri: '`/path/to/api`',
                queryParams: 'new Object({});',
                header: '`{"header-name": "value"}`',
                body: '`{"key": "value"}`',
                authKeyDb: [{ authKeyHeader: 'Authorization', authKeyName: 'AuthKeyName' }],
                timeout: 60000,
                parseResponse: 'JSON.parse(response.data)',
                pollCondition: '!(response.data.field)',
                proxyRequired: true,
                useCookies: true,
                tlsMinVersion: "TLSv1.1",
                errorCondition: '',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: 'MasslName',
                wikiPage: 'ApiName1',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            }).save());

            promises.push(new Api({
                name: 'ApiName2',
                description: 'Another API description here',
                active: true,
                apiType: 'rest',
                method: 'post',
                parameters: ['d', 'e', 'f'],
                baseUrl: 'config.secondary_url',
                uri: '`/path/to/operation`',
                queryParams: 'new Object({});',
                header: '`{"Authorization": "Bearer token"}`',
                body: '`{"a": 1}`',
                authKeyDb: [{ authKeyHeader: 'Authorization', authKeyName: 'AuthKeyName' }],
                timeout: 60000,
                parseResponse: 'JSON.parse(response.data)',
                pollCondition: '!(response.data.field)',
                proxyRequired: true,
                useCookies: true,
                tlsMinVersion: "TLSv1.1",
                errorCondition: '',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: 'MasslName',
                wikiPage: 'ApiName2',
                createdOn: currDate,
                createdBy: 'merge'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis');

            res.should.have.status(200);

            chai.expect(res.body.results).to.like([{
                name: 'ApiName1',
                description: 'API Description Here',
                active: true,
                apiType: 'rest',
                method: 'get',
                parameters: ['a', 'b', 'c'],
                baseUrl: 'config.url',
                uri: '`/path/to/api`',
                queryParams: 'new Object({});',
                header: '`{"header-name": "value"}`',
                body: '`{"key": "value"}`',
                authKeyDb: [{ authKeyHeader: 'Authorization', authKeyName: 'AuthKeyName' }],
                timeout: 60000,
                parseResponse: 'JSON.parse(response.data)',
                pollCondition: '!(response.data.field)',
                proxyRequired: true,
                useCookies: true,
                tlsMinVersion: "TLSv1.1",
                errorCondition: '',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: 'MasslName',
                wikiPage: 'ApiName1',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            },
            {
                name: 'ApiName2',
                description: 'Another API description here',
                active: true,
                apiType: 'rest',
                method: 'post',
                parameters: ['d', 'e', 'f'],
                baseUrl: 'config.secondary_url',
                uri: '`/path/to/operation`',
                queryParams: 'new Object({});',
                header: '`{"Authorization": "Bearer token"}`',
                body: '`{"a": 1}`',
                authKeyDb: [{ authKeyHeader: 'Authorization', authKeyName: 'AuthKeyName' }],
                timeout: 60000,
                parseResponse: 'JSON.parse(response.data)',
                pollCondition: '!(response.data.field)',
                proxyRequired: true,
                useCookies: true,
                tlsMinVersion: "TLSv1.1",
                errorCondition: '',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: 'MasslName',
                wikiPage: 'ApiName2',
                createdOn: currDate,
                createdBy: 'merge'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(API_KEYS);
            });
        });

        it('Get one API with limit', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            promises.push(new Api({
                name: 'ApiName2'
            }).save());

            promises.push(new Api({
                name: 'ApiName3'
            }).save());

            promises.push(new Api({
                name: 'ApiName4'
            }).save());

            promises.push(new Api({
                name: 'ApiName5'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis?limit=1');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'ApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(API_KEYS);
            });
        });

        it('Get one API with limit and offset', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            promises.push(new Api({
                name: 'ApiName2'
            }).save());

            promises.push(new Api({
                name: 'ApiName3'
            }).save());

            promises.push(new Api({
                name: 'ApiName4'
            }).save());

            promises.push(new Api({
                name: 'ApiName5'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis?limit=2&offset=2');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'ApiName3',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'ApiName4',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(API_KEYS);
            });
        });

        it('Get API list sorted by name ascending', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            promises.push(new Api({
                name: 'ApiName2'
            }).save());

            promises.push(new Api({
                name: 'ApiName3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis?sort=name&order=asc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'ApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'ApiName2',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'ApiName3',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(API_KEYS);
            });
        });

        it('Get API list sorted by name descending', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            promises.push(new Api({
                name: 'ApiName2'
            }).save());

            promises.push(new Api({
                name: 'ApiName3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis?sort=name&order=desc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'ApiName3',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'ApiName2',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'ApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(API_KEYS);
            });
        });

        it('Get API list sorted by name ascending case insensitive', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'Aba'
            }).save());

            promises.push(new Api({
                name: 'aAa'
            }).save());

            promises.push(new Api({
                name: 'aCa'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis?sort=name&order=asc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'aAa',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Aba',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'aCa',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(API_KEYS);
            });
        });

        it('Get API list sorted by name descending case insensitive', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'Aba'
            }).save());

            promises.push(new Api({
                name: 'aAa'
            }).save());

            promises.push(new Api({
                name: 'aCa'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis?sort=name&order=desc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'aCa',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Aba',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'aAa',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(API_KEYS);
            });
        });

        it('Get API list sorted by description ascending', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1',
                description: 'Bravo'
            }).save());

            promises.push(new Api({
                name: 'ApiName2',
                description: 'Charlie'
            }).save());

            promises.push(new Api({
                name: 'ApiName3',
                description: 'Alpha'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis?sort=description&order=asc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'ApiName3',
                description: 'Alpha',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'ApiName1',
                description: 'Bravo',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'ApiName2',
                description: 'Charlie',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(API_KEYS);
            });
        });

        it('Get API list sorted by description descending', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1',
                description: 'Bravo'
            }).save());

            promises.push(new Api({
                name: 'ApiName2',
                description: 'Charlie'
            }).save());

            promises.push(new Api({
                name: 'ApiName3',
                description: 'Alpha'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis?sort=description&order=desc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'ApiName2',
                description: 'Charlie',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'ApiName1',
                description: 'Bravo',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'ApiName3',
                description: 'Alpha',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(API_KEYS);
            });
        });

        it('Get API list sorted by description ascending case insensitive', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1',
                description: 'Alpha'
            }).save());

            promises.push(new Api({
                name: 'ApiName2',
                description: 'alpha'
            }).save());

            promises.push(new Api({
                name: 'ApiName3',
                description: 'ALPHA'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis?sort=description&order=asc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'ApiName2',
                description: 'alpha',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'ApiName1',
                description: 'Alpha',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'ApiName3',
                description: 'ALPHA',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(API_KEYS);
            });
        });

        it('Get API list sorted by description descending case insensitive', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1',
                description: 'Alpha'
            }).save());

            promises.push(new Api({
                name: 'ApiName2',
                description: 'alpha'
            }).save());

            promises.push(new Api({
                name: 'ApiName3',
                description: 'ALPHA'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis?sort=description&order=desc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'ApiName3',
                description: 'ALPHA',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'ApiName1',
                description: 'Alpha',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'ApiName2',
                description: 'alpha',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(API_KEYS);
            });
        });

        it('Get API list filter by name equality 1', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'TestApiName1'
            }).save());

            promises.push(new Api({
                name: 'TestApiName2'
            }).save());

            promises.push(new Api({
                name: 'AnotherApiName1'
            }).save());

            promises.push(new Api({
                name: 'AnotherApiName2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis?name=TestApiName1');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'TestApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(API_KEYS);
            });
        });

        it('Get API list filter by name equality 2', async() => {
            let promises = [];

            promises.push(new Api({
                name: 'TestApiName1'
            }).save());

            promises.push(new Api({
                name: 'TestApiName2'
            }).save());

            promises.push(new Api({
                name: 'AnotherApiName1'
            }).save());

            promises.push(new Api({
                name: 'AnotherApiName2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis?name=Test');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([]);
        });

        it('Get API list filter by name equality 3', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'TestApiName1'
            }).save());

            promises.push(new Api({
                name: 'TestApiName2'
            }).save());

            promises.push(new Api({
                name: 'AnotherApiName1'
            }).save());

            promises.push(new Api({
                name: 'AnotherApiName2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis?name[equal]=AnotherApiName2');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'AnotherApiName2',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(API_KEYS);
            });
        });

        it('Get API list filter by name equality 4', async() => {
            let promises = [];

            promises.push(new Api({
                name: 'TestApiName1'
            }).save());

            promises.push(new Api({
                name: 'TestApiName2'
            }).save());

            promises.push(new Api({
                name: 'AnotherApiName1'
            }).save());

            promises.push(new Api({
                name: 'AnotherApiName2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis?name[equal]=Template');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([]);
        });

        it('Get API list filter by name like 1', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'TestApiName1'
            }).save());

            promises.push(new Api({
                name: 'TestApiName2'
            }).save());

            promises.push(new Api({
                name: 'AnotherApiName1'
            }).save());

            promises.push(new Api({
                name: 'AnotherApiName2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis?name[like]=Test');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'TestApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'TestApiName2',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(API_KEYS);
            });
        });

        it('Get API list filter by name like 2', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'TestApiName1'
            }).save());

            promises.push(new Api({
                name: 'TestApiName2'
            }).save());

            promises.push(new Api({
                name: 'AnotherApiName1'
            }).save());

            promises.push(new Api({
                name: 'AnotherApiName2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis?name[like]=ApiName1');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'AnotherApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'TestApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(API_KEYS);
            });
        });

        it('Get API list filter by name like 3', async() => {
            let promises = [];

            promises.push(new Api({
                name: 'TestApiName1'
            }).save());

            promises.push(new Api({
                name: 'TestApiName2'
            }).save());

            promises.push(new Api({
                name: 'AnotherApiName1'
            }).save());

            promises.push(new Api({
                name: 'AnotherApiName2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis?name[like]=Text');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([]);
        });

        it('Get API list filter by name like 4 (case insensitive)', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'TestApiName1'
            }).save());

            promises.push(new Api({
                name: 'TestApiName2'
            }).save());

            promises.push(new Api({
                name: 'AnotherApiName1'
            }).save());

            promises.push(new Api({
                name: 'AnotherApiName2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis?name[like]=AnOtHer');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'AnotherApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'AnotherApiName2',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(API_KEYS);
            });
        });

        it('Get API list filter by description equality 1', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1',
                description: 'An API description'
            }).save());

            promises.push(new Api({
                name: 'ApiName2',
                description: 'Another API description'
            }).save());

            promises.push(new Api({
                name: 'ApiName3',
                description: 'API description here'
            }).save());

            promises.push(new Api({
                name: 'ApiName4',
                description: 'Yet another API description'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis?description=API%20description%20here');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'ApiName3',
                description: 'API description here',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(API_KEYS);
            });
        });

        it('Get API list filter by description equality 2', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1',
                description: 'An API description'
            }).save());

            promises.push(new Api({
                name: 'ApiName2',
                description: 'Another API description'
            }).save());

            promises.push(new Api({
                name: 'ApiName3',
                description: 'API description here'
            }).save());

            promises.push(new Api({
                name: 'ApiName4',
                description: 'Yet another API description'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis?description[equal]=API%20description%20here');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'ApiName3',
                description: 'API description here',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(API_KEYS);
            });
        });

        it('Get API list filter by description like 1', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1',
                description: 'Extract field here'
            }).save());

            promises.push(new Api({
                name: 'ApiName2',
                description: 'Extract field there'
            }).save());

            promises.push(new Api({
                name: 'ApiName3',
                description: 'Summarise field here'
            }).save());

            promises.push(new Api({
                name: 'ApiName4',
                description: 'Summarise field there'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis?description[like]=Extract');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'ApiName1',
                description: 'Extract field here',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'ApiName2',
                description: 'Extract field there',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(API_KEYS);
            });
        });

        it('Get API list filter by description like 2 (case insensitive)', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1',
                description: 'Extract field here'
            }).save());

            promises.push(new Api({
                name: 'ApiName2',
                description: 'Extract field there'
            }).save());

            promises.push(new Api({
                name: 'ApiName3',
                description: 'Summarise field here'
            }).save());

            promises.push(new Api({
                name: 'ApiName4',
                description: 'Summarise field there'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis?description[like]=THeRE');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'ApiName2',
                description: 'Extract field there',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'ApiName4',
                description: 'Summarise field there',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(API_KEYS);
            });
        });

        it('Get API list invalid limit (non-integer)', async() => {
            let res = await request.get('/apis?limit=notalimit');

            res.should.have.status(400);
        });

        it('Get API list invalid limit (integer equals 0)', async() => {
            let res = await request.get('/apis?limit=0');

            res.should.have.status(400);
        });

        it('Get API list invalid limit (negative integer)', async() => {
            let res = await request.get('/apis?limit=-1');

            res.should.have.status(400);
        });

        it('Get API list invalid offset (non-integer)', async() => {
            let res = await request.get('/apis?offset=notanoffset');

            res.should.have.status(400);
        });

        it('Get API list invalid offset (negative integer)', async() => {
            let res = await request.get('/apis?offset=-1');

            res.should.have.status(400);
        });

        it('Get API list invalid order parameter', async() => {
            let res = await request.get('/apis?order=ascending');

            res.should.have.status(400);
        });

        it('Get API list invalid name filter operator', async() => {
            let res = await request.get('/apis?name[ophere]=test');

            res.should.have.status(400);
        });

        it('Get API list empty description filter operator', async() => {
            let res = await request.get('/apis?description[]=no-op');

            res.should.have.status(400);
        });

        it('Get API list with HTTP GET with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;

            let res = await request.get('/apis');

            res.should.have.status(200);
            chai.expect(res.body).to.eql({
                metadata: {
                    pagination: {
                        limit: 100,
                        offset: 0,
                        total: 0,
                        prev: null,
                        next: null
                    }
                },
                results: []
            });
        });
    });

    describe('Read API', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/apis/ApiName');

            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/apis/ApiName');

                switch(username) {
                    case 'admin':
                        try {
                            res.should.have.status(404);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Get non-existant API', async() => {
            let res = await request.get('/apis/ApiName1');

            res.should.have.status(404);
        });

        it('Get one existing API (default values)', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis/ApiName1');

            res.should.have.status(200);
            chai.expect(res.body).to.like({
                name: 'ApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(res.body).to.have.all.keys(API_KEYS);
        });

        it('Get one existing API', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1',
                description: 'API Description Here',
                active: true,
                apiType: 'rest',
                method: 'get',
                parameters: ['a', 'b', 'c'],
                baseUrl: 'config.url',
                uri: '`/path/to/api`',
                queryParams: 'new Object({});',
                header: '`{"header-name": "value"}`',
                body: '`{"key": "value"}`',
                authKeyDb: [{ authKeyHeader: 'Authorization', authKeyName: 'AuthKeyName' }],
                timeout: 60000,
                parseResponse: 'JSON.parse(response.data)',
                pollCondition: '!(response.data.field)',
                proxyRequired: true,
                useCookies: true,
                tlsMinVersion: "TLSv1.1",
                errorCondition: '',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: 'MasslName',
                wikiPage: 'ApiName1',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis/ApiName1');

            res.should.have.status(200);

            chai.expect(res.body).to.like({
                name: 'ApiName1',
                description: 'API Description Here',
                active: true,
                apiType: 'rest',
                method: 'get',
                parameters: ['a', 'b', 'c'],
                baseUrl: 'config.url',
                uri: '`/path/to/api`',
                queryParams: 'new Object({});',
                header: '`{"header-name": "value"}`',
                body: '`{"key": "value"}`',
                authKeyDb: [{ authKeyHeader: 'Authorization', authKeyName: 'AuthKeyName' }],
                timeout: 60000,
                parseResponse: 'JSON.parse(response.data)',
                pollCondition: '!(response.data.field)',
                proxyRequired: true,
                useCookies: true,
                tlsMinVersion: "TLSv1.1",
                errorCondition: '',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: 'MasslName',
                wikiPage: 'ApiName1',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });

            chai.expect(res.body).to.have.all.keys(API_KEYS);
        });

        it('Get existing API with HTTP GET with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/apis/ApiName1');

            res.should.have.status(200);
            chai.expect(res.body).to.like({
                name: 'ApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(res.body).to.have.all.keys(API_KEYS);
        });
    });

    describe('Create API', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.post('/apis').send({
                name: 'ApiName1'
            });

            res.should.have.status(401);
            chai.expect(await Api.find({ name: 'ApiName1' }).countDocuments()).to.eql(0);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.post('/apis').send({
                    name: 'ApiName1'
                });

                switch(username) {
                    case 'admin':
                        try {
                            res.should.have.status(201);
                            chai.expect(await Api.find({ name: 'ApiName1' }).countDocuments()).to.eql(1);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                            chai.expect(await Api.find({ name: 'ApiName1' }).countDocuments()).to.eql(0);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }

                await Api.deleteMany({});
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Create API with HTTP POST (default values)', async() => {
            let currDate = new Date();

            let postRes = await request.post('/apis').send({
                name: 'ApiName1'
            });

            postRes.should.have.status(201);
            chai.expect(postRes.body).to.like({
                name: 'ApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });

            let getRes = await request.get('/apis/ApiName1');

            getRes.should.have.status(200);
            chai.expect(getRes.body).to.like({
                name: 'ApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(getRes.body).to.have.all.keys(API_KEYS);
        });

        it('Create API with HTTP POST test invalid active field', async() => {
            let res = await request.post('/apis').send({
                name: 'ApiName1',
                active: 117
            });

            res.should.have.status(400);
        });

        it('Create API with HTTP POST test invalid masslCertificateName field 1', async() => {
            let res = await request.post('/apis').send({
                name: 'ApiName1',
                masslCertificateName: {}
            });

            res.should.have.status(400);
        });

        it('Create API with HTTP POST test invalid async poll field 1', async() => {
            let res = await request.post('/apis').send({
                name: 'ApiName1',
                asyncPoll: 100
            });

            res.should.have.status(400);
        });

        it('Create API with HTTP POST test invalid async poll field 2', async() => {
            let res = await request.post('/apis').send({
                name: 'ApiName1',
                asyncPoll: 'notvalid'
            });

            res.should.have.status(400);
        });

        it('Create API with HTTP POST test invalid async poll field 3', async() => {
            let res = await request.post('/apis').send({
                name: 'ApiName1',
                asyncPoll: {}
            });

            res.should.have.status(400);
        });

        it('Create API with HTTP POST test invalid async poll field 4', async() => {
            let res = await request.post('/apis').send({
                name: 'ApiName1',
                asyncPoll: {
                    name: 'testname',
                    interval: 10000,
                    timeout: 60000,
                    uri: 'urihere',
                    queryParams: '',
                    authKeyDb: [],
                    header: '',
                    errorCondition: 'errorconditionhere',
                    parseResponse: 'parseresponsehere',
                    masslCertificateName: null,
                    transform: null,
                    resultAPI: null
                }
            });

            res.should.have.status(400);
        });

        it('Create API with HTTP POST test invalid async poll field 6', async() => {
            let res = await request.post('/apis').send({
                name: 'ApiName1',
                asyncPoll: {
                    name: 'testname',
                    interval: 10000,
                    timeout: 60000,
                    uri: 'urihere',
                    queryParams: '',
                    authKeyDb: [],
                    header: '',
                    doneCondition: 'doneconditionhere',
                    errorCondition: 'errorconditionhere',
                    parseResponse: 'parseresponsehere',
                    masslCertificateName: 'MasslName',
                    transform: null,
                    resultAPI: {
                        name: 'testresultname',
                        timeout: 30000,
                        header: 'headerhere',
                        authKeyDb: [],
                        parseResponse: 'parseresponsehere',
                        transform: null
                    }
                }
            });

            res.should.have.status(400);
        });

        it('Create API with HTTP POST test valid async poll field 1', async() => {
            let currDate = new Date();

            let res = await request.post('/apis').send({
                name: 'ApiName1',
                asyncPoll: {
                    name: 'testname',
                    interval: 10000,
                    timeout: 60000,
                    uri: 'urihere',
                    queryParams: '',
                    authKeyDb: [],
                    header: '',
                    doneCondition: 'doneconditionhere',
                    errorCondition: 'errorconditionhere',
                    parseResponse: 'parseresponsehere',
                    transform: null,
                    resultAPI: null
                }
            });

            res.should.have.status(201);

            chai.expect(res.body).to.like({
                name: 'ApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: {
                    name: 'testname',
                    interval: 10000,
                    timeout: 60000,
                    uri: 'urihere',
                    queryParams: '',
                    authKeyDb: [],
                    header: '',
                    doneCondition: 'doneconditionhere',
                    errorCondition: 'errorconditionhere',
                    parseResponse: 'parseresponsehere',
                    transform: null,
                    resultAPI: null
                },
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(res.body).to.have.all.keys(API_KEYS);
        });

        it('Create API with HTTP POST test valid async poll field 2', async() => {
            let currDate = new Date();

            let res = await request.post('/apis').send({
                name: 'ApiName1',
                asyncPoll: {
                    name: 'testname',
                    interval: 10000,
                    timeout: 60000,
                    uri: 'urihere',
                    queryParams: '',
                    authKeyDb: [],
                    header: '',
                    doneCondition: 'doneconditionhere',
                    errorCondition: 'errorconditionhere',
                    parseResponse: 'parseresponsehere',
                    transform: null,
                    resultAPI: null
                }
            });

            res.should.have.status(201);

            chai.expect(res.body).to.like({
                name: 'ApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: {
                    name: 'testname',
                    interval: 10000,
                    timeout: 60000,
                    uri: 'urihere',
                    queryParams: '',
                    authKeyDb: [],
                    header: '',
                    doneCondition: 'doneconditionhere',
                    errorCondition: 'errorconditionhere',
                    parseResponse: 'parseresponsehere',
                    transform: null,
                    resultAPI: null
                },
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(res.body).to.have.all.keys(API_KEYS);
        });

        it('Create API with HTTP POST test valid async poll field 3', async() => {
            let currDate = new Date();

            let res = await request.post('/apis').send({
                name: 'ApiName1',
                asyncPoll: {
                    name: 'testname',
                    interval: 10000,
                    timeout: 60000,
                    uri: 'urihere',
                    queryParams: '',
                    authKeyDb: [],
                    header: '',
                    doneCondition: 'doneconditionhere',
                    errorCondition: 'errorconditionhere',
                    parseResponse: 'parseresponsehere',
                    transform: {
                        a: '1',
                        b: '2'
                    },
                    resultAPI: {
                        name: 'testresultname',
                        timeout: 30000,
                        header: 'headerhere',
                        uri: 'urihere',
                        queryParams: '',
                        authKeyDb: [],
                        parseResponse: 'parseresponsehere',
                        transform: {
                            c: '3',
                            d: '4'
                        }
                    }
                }
            });

            res.should.have.status(201);

            chai.expect(res.body).to.like({
                name: 'ApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: {
                    name: 'testname',
                    interval: 10000,
                    timeout: 60000,
                    uri: 'urihere',
                    queryParams: '',
                    authKeyDb: [],
                    header: '',
                    doneCondition: 'doneconditionhere',
                    errorCondition: 'errorconditionhere',
                    parseResponse: 'parseresponsehere',
                    transform: {
                        a: '1',
                        b: '2'
                    },
                    resultAPI: {
                        name: 'testresultname',
                        timeout: 30000,
                        header: 'headerhere',
                        uri: 'urihere',
                        queryParams: '',
                        authKeyDb: [],
                        parseResponse: 'parseresponsehere',
                        transform: {
                            c: '3',
                            d: '4'
                        }
                    }
                },
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(res.body).to.have.all.keys(API_KEYS);
        });

        it('Create API with HTTP POST', async() => {
            let currDate = new Date();

            let postRes = await request.post('/apis').send({
                name: 'ApiName1',
                description: 'API Description Here',
                active: true,
                apiType: 'rest',
                method: 'get',
                parameters: ['a', 'b', 'c'],
                baseUrl: 'config.url',
                uri: '`/path/to/api`',
                queryParams: 'new Object({});',
                header: '`{"header-name": "value"}`',
                body: '`{"key": "value"}`',
                authKeyDb: [{ authKeyHeader: 'Authorization', authKeyName: 'AuthKeyName' }],
                timeout: 60000,
                parseResponse: 'JSON.parse(response.data)',
                pollCondition: '!(response.data.field)',
                proxyRequired: true,
                useCookies: true,
                tlsMinVersion: "TLSv1.1",
                errorCondition: '',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: 'MasslName',
                wikiPage: 'ApiName1',
            });

            postRes.should.have.status(201);
            chai.expect(postRes.body).to.like({
                name: 'ApiName1',
                description: 'API Description Here',
                active: true,
                apiType: 'rest',
                method: 'get',
                parameters: ['a', 'b', 'c'],
                baseUrl: 'config.url',
                uri: '`/path/to/api`',
                queryParams: 'new Object({});',
                header: '`{"header-name": "value"}`',
                body: '`{"key": "value"}`',
                authKeyDb: [{ authKeyHeader: 'Authorization', authKeyName: 'AuthKeyName' }],
                timeout: 60000,
                parseResponse: 'JSON.parse(response.data)',
                pollCondition: '!(response.data.field)',
                proxyRequired: true,
                useCookies: true,
                tlsMinVersion: "TLSv1.1",
                errorCondition: '',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: 'MasslName',
                wikiPage: 'ApiName1',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(postRes.body).to.have.all.keys(API_KEYS);

            let getRes = await request.get('/apis/ApiName1');

            getRes.should.have.status(200);
            chai.expect(getRes.body).to.like({
                name: 'ApiName1',
                description: 'API Description Here',
                active: true,
                apiType: 'rest',
                method: 'get',
                parameters: ['a', 'b', 'c'],
                baseUrl: 'config.url',
                uri: '`/path/to/api`',
                queryParams: 'new Object({});',
                header: '`{"header-name": "value"}`',
                body: '`{"key": "value"}`',
                authKeyDb: [{ authKeyHeader: 'Authorization', authKeyName: 'AuthKeyName' }],
                timeout: 60000,
                parseResponse: 'JSON.parse(response.data)',
                pollCondition: '!(response.data.field)',
                proxyRequired: true,
                useCookies: true,
                tlsMinVersion: "TLSv1.1",
                errorCondition: '',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: 'MasslName',
                wikiPage: 'ApiName1',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(getRes.body).to.have.all.keys(API_KEYS);
        });

        it('Create API with HTTP POST test duplicate parameters', async() => {
            let res = await request.post('/apis').send({
                name: 'ApiName1',
                parameters: [
                    'paramName',
                    'paramName'
                ]
            });

            res.should.have.status(400);

            chai.expect(await Api.find({ name: 'ApiName1' }).countDocuments()).to.eql(0);
        });

        it('Create duplicate API with HTTP POST', async() => {
            let postRes1 = await request.post('/apis').send({
                name: 'ApiName1'
            });

            postRes1.should.have.status(201);

            let postRes2 = await request.post('/apis').send({
                name: 'ApiName1'
            });

            postRes2.should.have.status(409);
        });

        it('Create API with HTTP POST test createdBy immutable', async() => {
            let currDate = new Date();

            let res = await request.post('/apis').send({
                name: 'ApiName1',
                createdBy: 'anotheruser'
            });

            res.should.have.status(201);
            chai.expect(res.body).to.like({
                name: 'ApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(res.body).to.have.all.keys(API_KEYS);
        });

        it('Create API with HTTP POST test createdOn immutable', async() => {
            let customDate = new Date('2020-12-15T16:00:00.000Z');
            let currDate = new Date();

            let res = await request.post('/apis').send({
                name: 'ApiName1',
                createdOn: customDate
            });

            res.should.have.status(201);
            chai.expect(res.body).to.like({
                name: 'ApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(res.body).to.have.all.keys(API_KEYS);
        });

        it('Create API with HTTP POST unrecognised fields', async() => {
            let currDate = new Date();

            let postRes = await request.post('/apis').send({
                name: 'ApiName1',
                customField: 'should not show up',
                unrecognisedField: 550
            });

            postRes.should.have.status(201);
            chai.expect(postRes.body).to.like({
                name: 'ApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });

            chai.expect(postRes.body).to.have.all.keys(API_KEYS);
            chai.expect(postRes.body).to.not.have.any.keys('customField', 'unrecognisedField');

            let getRes = await request.get('/apis/ApiName1');

            getRes.should.have.status(200);
            chai.expect(getRes.body).to.like({
                name: 'ApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(getRes.body).to.have.all.keys(API_KEYS);
            chai.expect(getRes.body).to.not.have.any.keys('customField', 'unrecognisedField');
        });

        it('Create API with HTTP POST with disableRuleSourceEditing enabled and user not admin', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            let postRes = await request.post('/apis').send({
                name: 'ApiName1'
            });

            postRes.should.have.status(403);

            let getRes = await request.get('/apis/ApiName1');

            getRes.should.have.status(403);
        });

        it('Create API with HTTP POST with disableRuleSourceEditing enabled and user admin', async() => {
            global.gConfig.disableRuleSourceEditing = true;

            let postRes = await request.post('/apis').send({
                name: 'ApiName1'
            });

            postRes.should.have.status(201);

            let getRes = await request.get('/apis/ApiName1');

            getRes.should.have.status(200);
        });
    });

    describe('Update API', () => {
        it('Unauthenticated request', async() => {
            await new Api({
                name: 'ApiName',
                active: false
            }).save();

            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.put('/apis/ApiName').send({
                active: true
            });

            res.should.have.status(401);
            chai.expect((await Api.findOne({ name: 'ApiName' })).active).to.eql(false);
        });

        it('Authorization tests', async() => {
            await new Api({
                name: 'ApiName',
                active: false
            }).save();

            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.put('/apis/ApiName').send({
                    active: true
                });

                switch(username) {
                    case 'admin':
                        try {
                            res.should.have.status(200);
                            chai.expect((await Api.findOne({ name: 'ApiName' })).active).to.eql(true);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                            chai.expect((await Api.findOne({ name: 'ApiName' })).active).to.eql(false);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
                await Api.updateOne({ name: 'ApiName' }, { $set: { active: false }});
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Update API with HTTP PUT', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/apis/ApiName1').send({
                name: 'ApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
            });

            res.should.have.status(200);
            chai.expect(res.body).to.like({
                name: 'ApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(res.body).to.have.all.keys(API_KEYS);
        });

        it('Update API with HTTP PUT test non-existant API', async() => {
            let res = await request.put('/apis/ApiName1');

            res.should.have.status(404);
        });

        it('Update API with HTTP PUT test invalid active field', async() => {
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/apis/ApiName1').send({
                active: 3
            });

            res.should.have.status(400);
        });

        it('Update API with HTTP PUT test invalid masslCertificateName field 1', async() => {
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/apis/ApiName1').send({
                masslCertificateName: {}
            });

            res.should.have.status(400);
        });

        it('Update API with HTTP PUT test invalid asyncPoll field 1', async() => {
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/apis/ApiName1').send({
                asyncPoll: 100
            });

            res.should.have.status(400);
        });

        it('Update API with HTTP PUT test invalid asyncPoll field 2', async() => {
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/apis/ApiName1').send({
                asyncPoll: 'notvalid'
            });

            res.should.have.status(400);
        });

        it('Update API with HTTP PUT test invalid asyncPoll field 3', async() => {
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/apis/ApiName1').send({
                asyncPoll: {}
            });

            res.should.have.status(400);
        });

        it('Update API with HTTP PUT test invalid asyncPoll field 4', async() => {
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/apis/ApiName1').send({
                asyncPoll: {
                    name: 'testname',
                    interval: 10000,
                    timeout: 60000,
                    uri: 'urihere',
                    queryParams: '',
                    authKeyDb: [],
                    header: '',
                    errorCondition: 'errorconditionhere',
                    parseResponse: 'parseresponsehere',
                    masslCertificateName: null,
                    transform: null,
                    resultAPI: null
                }
            });

            res.should.have.status(400);
        });

        it('Update API with HTTP PUT test invalid asyncPoll field 5', async() => {
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/apis/ApiName1').send({
                asyncPoll: {
                    name: 'testname',
                    interval: 10000,
                    timeout: 60000,
                    uri: 'urihere',
                    queryParams: '',
                    authKeyDb: [],
                    header: '',
                    doneCondition: 'doneconditionhere',
                    errorCondition: 'errorconditionhere',
                    parseResponse: 'parseresponsehere',
                    transform: null,
                    resultAPI: {
                        name: 'testresultname',
                        timeout: 30000,
                        header: 'headerhere',
                        authKeyDb: [],
                        parseResponse: 'parseresponsehere',
                        transform: null
                    }
                }
            });

            res.should.have.status(400);
        });

        it('Update API with HTTP PUT test valid asyncPoll field 1', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/apis/ApiName1').send({
                asyncPoll: {
                    name: 'testname',
                    interval: 10000,
                    timeout: 60000,
                    uri: 'urihere',
                    queryParams: '',
                    authKeyDb: [],
                    header: '',
                    doneCondition: 'doneconditionhere',
                    errorCondition: 'errorconditionhere',
                    parseResponse: 'parseresponsehere',
                    transform: null,
                    resultAPI: null
                }
            });

            res.should.have.status(200);
            chai.expect(res.body).to.like({
                name: 'ApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: {
                    name: 'testname',
                    interval: 10000,
                    timeout: 60000,
                    uri: 'urihere',
                    queryParams: '',
                    authKeyDb: [],
                    header: '',
                    doneCondition: 'doneconditionhere',
                    errorCondition: 'errorconditionhere',
                    parseResponse: 'parseresponsehere',
                    transform: null,
                    resultAPI: null
                },
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(res.body).to.have.all.keys(API_KEYS);
        });

        it('Update API with HTTP PUT test valid asyncPoll field 2', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/apis/ApiName1').send({
                asyncPoll: {
                    name: 'testname',
                    interval: 10000,
                    timeout: 60000,
                    uri: 'urihere',
                    queryParams: '',
                    authKeyDb: [],
                    header: '',
                    doneCondition: 'doneconditionhere',
                    errorCondition: 'errorconditionhere',
                    parseResponse: 'parseresponsehere',
                    transform: null,
                    resultAPI: null
                }
            });

            res.should.have.status(200);
            chai.expect(res.body).to.like({
                name: 'ApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: {
                    name: 'testname',
                    interval: 10000,
                    timeout: 60000,
                    uri: 'urihere',
                    queryParams: '',
                    authKeyDb: [],
                    header: '',
                    doneCondition: 'doneconditionhere',
                    errorCondition: 'errorconditionhere',
                    parseResponse: 'parseresponsehere',
                    transform: null,
                    resultAPI: null
                },
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(res.body).to.have.all.keys(API_KEYS);
        });

        it('Update API with HTTP PUT test valid asyncPoll field 3', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/apis/ApiName1').send({
                asyncPoll: {
                    name: 'testname',
                    interval: 10000,
                    timeout: 60000,
                    uri: 'urihere',
                    queryParams: '',
                    authKeyDb: [],
                    header: '',
                    doneCondition: 'doneconditionhere',
                    errorCondition: 'errorconditionhere',
                    parseResponse: 'parseresponsehere',
                    transform: {
                        a: '1',
                        b: '2'
                    },
                    resultAPI: {
                        name: 'testresultname',
                        timeout: 30000,
                        header: 'headerhere',
                        uri: 'urihere',
                        queryParams: '',
                        authKeyDb: [],
                        parseResponse: 'parseresponsehere',
                        transform: {
                            c: '3',
                            d: '4'
                        }
                    }
                }
            });

            res.should.have.status(200);
            chai.expect(res.body).to.like({
                name: 'ApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: {
                    name: 'testname',
                    interval: 10000,
                    timeout: 60000,
                    uri: 'urihere',
                    queryParams: '',
                    authKeyDb: [],
                    header: '',
                    doneCondition: 'doneconditionhere',
                    errorCondition: 'errorconditionhere',
                    parseResponse: 'parseresponsehere',
                    transform: {
                        a: '1',
                        b: '2'
                    },
                    resultAPI: {
                        name: 'testresultname',
                        timeout: 30000,
                        header: 'headerhere',
                        uri: 'urihere',
                        queryParams: '',
                        authKeyDb: [],
                        parseResponse: 'parseresponsehere',
                        transform: {
                            c: '3',
                            d: '4'
                        }
                    }
                },
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(res.body).to.have.all.keys(API_KEYS);
        });

        it('Update API with HTTP PUT test duplicate elements in parameters', async() => {
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            await Promise.all(promises);

            let res = await request.put('/apis/ApiName1').send({
                parameters: [
                    'paramName',
                    'paramName'
                ]
            });

            res.should.have.status(400);
        });

        it('Update API with HTTP PUT test name immutable', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            await Promise.all(promises);
            let putRes = await request.put('/apis/ApiName1').send({
                name: 'ApiName2'
            });

            putRes.should.have.status(200);

            let getRes1 = await request.get('/apis/ApiName1');

            getRes1.should.have.status(200);
            chai.expect(getRes1.body).to.like({
                name: 'ApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(getRes1.body).to.have.all.keys(API_KEYS);

            let getRes2 = await request.get('/apis/ApiName2');

            getRes2.should.have.status(404);
        });

        it('Update API with HTTP PUT test createdBy immutable', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1',
                createdBy: 'testuser'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/apis/ApiName1').send({
                createdBy: 'anotheruser'
            });

            res.should.have.status(200);
            chai.expect(res.body).to.like({
                name: 'ApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'testuser'
            });
            chai.expect(res.body).to.have.all.keys(API_KEYS);
        });

        it('Update API with HTTP PUT test createdOn immutable', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/apis/ApiName1').send({
                createdOn: '2010-01-15T16:00:00.000Z'
            });

            res.should.have.status(200);
            chai.expect(res.body).to.like({
                name: 'ApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(res.body).to.have.all.keys(API_KEYS);
        });

        it('Update API with HTTP PUT unrecognised fields', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/apis/ApiName1').send({
                badField: 'badValue'
            });

            res.should.have.status(200);

            chai.expect(res.body).to.like({
                name: 'ApiName1',
                description: '',
                active: false,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            });

            chai.expect(res.body).to.have.all.keys(API_KEYS);
            chai.expect(res.body).to.not.have.any.keys('badField');
        });

        it('Update API with HTTP PUT with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            await Promise.all(promises);
            let putRes = await request.put('/apis/ApiName1').send({
                active: true
            });

            putRes.should.have.status(403);

            let getRes = await request.get('/apis/ApiName1');

            getRes.should.have.status(403);
        });

        it('Update API with HTTP PUT with disableRuleSourceEditing enabled and admin user', async() => {
            global.gConfig.disableRuleSourceEditing = true;

            let currDate = new Date();
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            await Promise.all(promises);
            let putRes = await request.put('/apis/ApiName1').send({
                active: true
            });

            putRes.should.have.status(200);

            let getRes = await request.get('/apis/ApiName1');

            getRes.should.have.status(200);
            getRes.body.should.like({
                name: 'ApiName1',
                description: '',
                active: true,
                apiType: 'rest',
                method: 'get',
                parameters: [],
                baseUrl: '',
                uri: '',
                queryParams: '',
                header: '',
                body: '',
                authKeyDb: [],
                timeout: 300000,
                parseResponse: null,
                pollCondition: null,
                proxyRequired: false,
                useCookies: false,
                tlsMinVersion: null,
                errorCondition: 'response.data && typeof response.data.error !== \'undefined\'',
                asyncPoll: null,
                asyncCallback: null,
                masslCertificateName: null,
                wikiPage: '',
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(getRes.body).to.have.all.keys(API_KEYS);
        });
    });

    describe('Delete API', () => {
        it('Unauthenticated request', async() => {
            await new Api({
                name: 'ApiName'
            }).save();

            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.delete('/apis/ApiName');

            res.should.have.status(401);
            chai.expect(await Api.find({ name: 'ApiName' }).countDocuments()).to.eql(1);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                if (await Api.find({ name: 'ApiName' }).countDocuments() === 0) {
                    await new Api({
                        name: 'ApiName'
                    }).save();
                }

                await helpers.authenticateSession(request, username);
                let res = await request.delete('/apis/ApiName');

                switch(username) {
                    case 'admin':
                        try {
                            res.should.have.status(200);
                            chai.expect(await Api.find({ name: 'ApiName' }).countDocuments()).to.eql(0);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                            chai.expect(await Api.find({ name: 'ApiName' }).countDocuments()).to.eql(1);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Delete API with HTTP DELETE', async() => {
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            await Promise.all(promises);
            let deleteRes = await request.delete('/apis/ApiName1');

            deleteRes.should.have.status(200);

            let getRes = await request.get('/apis/ApiName1');

            getRes.should.have.status(404);
        });

        it('Delete API with HTTP DELETE test non-existant API', async() => {
            let res = await request.delete('/apis/ApiName1');

            res.should.have.status(404);
        });

        it('Delete API with HTTP DELETE with disableRuleSourceEditing enabled with user not admin', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            await Promise.all(promises);
            let deleteRes = await request.delete('/apis/ApiName1');

            deleteRes.should.have.status(403);

            let getRes = await request.get('/apis/ApiName1');

            getRes.should.have.status(403);
        });

        it('Delete API with HTTP DELETE with disableRuleSourceEditing enabled with user admin', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            let promises = [];

            promises.push(new Api({
                name: 'ApiName1'
            }).save());

            await Promise.all(promises);
            let deleteRes = await request.delete('/apis/ApiName1');

            deleteRes.should.have.status(200);

            let getRes = await request.get('/apis/ApiName1');

            getRes.should.have.status(404);
        });
    });
});
