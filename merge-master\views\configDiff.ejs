<%- include('header', { title: 'Configuration Differences' }); %>
<%- include('menu', { currentTab: 'develop' }); %>
<link href="/public/stylesheets/ace-diff.min.css" rel="stylesheet">
<link rel="stylesheet" type="text/css" href="/public/stylesheets/select2.min.css">
<link rel="stylesheet" type="text/css" href="/public/stylesheets/jsgrid.min.css">
<link rel="stylesheet" type="text/css" href="/public/stylesheets/jsgrid-theme.min.css">
<script src="/public/javascripts/ace.js" crossorigin="anonymous"></script>
<script src="/public/javascripts/ace-diff.min.js" crossorigin="anonymous"></script>
<script type="text/javascript" src="/public/javascripts/jsgrid.min.js" crossorigin="anonymous"></script>
<script src="/public/javascripts/select2.min.js" crossorigin="anonymous"></script>

<div class=" d-flex flex-grow-1 flex-column" style="padding:1rem;">
    <div class="d-inline">
        <br>
        <div class="jumbotron py-1 px-4 text-white rounded bg-secondary">
            <h3 class="display-6 font-weight-normal">Config differences</h3>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="float-right">
                <button id="refreshAll" class="btn btn-primary">Refresh all</button>
            </div>
        </div>
    </div>

    <ul class="nav nav-tabs" role="tablist">
        <li role="presentation" class="nav-item active">
            <a class="nav-link active" data-target="#rulesDiffTab" data-toggle="tab" role="tab" href="">Rules <span id="rulesDiffCount" class="badge badge-primary">0</span></a>
        </li>
        <li role="presentation" class="nav-item">
            <a class="nav-link" data-target="#sourcesDiffTab" data-toggle="tab" role="tab" href="">Sources <span id="sourcesDiffCount" class="badge badge-primary">0</span></a>
        </li>
        <li role="presentation" class="nav-item">
            <a class="nav-link" data-target="#apisDiffTab" data-toggle="tab" role="tab" href="">APIs <span id="apisDiffCount" class="badge badge-primary">0</span></a>
        </li>
        <li role="presentation" class="nav-item">
            <a class="nav-link" data-target="#textTemplatesDiffTab" data-toggle="tab" role="tab" href="">Text Templates <span id="textTemplatesDiffCount" class="badge badge-primary">0</span></a>
        </li>
        <li role="presentation" class="nav-item">
            <a class="nav-link" data-target="#outcomesDiffTab" data-toggle="tab" role="tab" href="">Outcomes <span id="outcomesDiffCount" class="badge badge-primary">0</span></a>
        </li>
        <li role="presentation" class="nav-item">
            <a class="nav-link" data-target="#messageBucketsDiffTab" data-toggle="tab" role="tab" href="">Message Buckets <span id="messageBucketsDiffCount" class="badge badge-primary">0</span></a>
        </li>
        <li role="presentation" class="nav-item">
            <a class="nav-link" data-target="#deployConfigTab" data-toggle="tab" role="tab" href="">Deploy configuration</a>
        </li>
    </ul>

    <div class="tab-content d-flex flex-grow-1 flex-column">
        <div class="tab-pane active p-2 border border-secondary rounded bg-light flex-grow-1" id="rulesDiffTab">
            <div><p class="float-left" id="displayDiffRulesTotal" class="flex-grow-1"></p><button id="rulesDiffRefresh" class="float-right btn btn-primary" title="Refresh"><span class="fas fa-sync"></span></button></div>
            <div id="rulesDiffGrid" class="flex-grow-1"></div>
        </div>
        <div class="tab-pane p-2 border border-secondary rounded bg-light flex-grow-1" id="sourcesDiffTab">
            <div><p class="float-left" id="displayDiffSourcesTotal" class="flex-grow-1"></p><button id="sourcesDiffRefresh" class="float-right btn btn-primary" title="Refresh"><span class="fas fa-sync"></span></button></div>
            <div id="sourcesDiffGrid" class="flex-grow-1"></div>
        </div>
        <div class="tab-pane p-2 border border-secondary rounded bg-light flex-grow-1" id="apisDiffTab">
            <div><p class="float-left" id="displayDiffApisTotal" class="flex-grow-1"></p><button id="apisDiffRefresh" class="float-right btn btn-primary" title="Refresh"><span class="fas fa-sync"></span></button></div>
            <div id="apisDiffGrid" class="flex-grow-1"></div>
        </div>
        <div class="tab-pane p-2 border border-secondary rounded bg-light flex-grow-1" id="textTemplatesDiffTab">
            <div><p class="float-left" id="displayDiffTextTemplatesTotal" class="flex-grow-1"></p><button id="textTemplatesDiffRefresh" class="float-right btn btn-primary" title="Refresh"><span class="fas fa-sync"></span></button></div>
            <div id="textTemplatesDiffGrid" class="flex-grow-1"></div>
        </div>
        <div class="tab-pane p-2 border border-secondary rounded bg-light flex-grow-1" id="outcomesDiffTab">
            <div><p class="float-left" id="displayDiffOutcomesTotal" class="flex-grow-1"></p><button id="outcomesDiffRefresh" class="float-right btn btn-primary" title="Refresh"><span class="fas fa-sync"></span></button></div>
            <div id="outcomesDiffGrid" class="flex-grow-1"></div>
        </div>
        <div class="tab-pane p-2 border border-secondary rounded bg-light flex-grow-1" id="messageBucketsDiffTab">
            <div><p class="float-left" id="displayDiffMessageBucketsTotal" class="flex-grow-1"></p><button id="messageBucketsDiffRefresh" class="float-right btn btn-primary" title="Refresh"><span class="fas fa-sync"></span></button></div>
            <div id="messageBucketsDiffGrid" class="flex-grow-1"></div>
        </div>

        <div class="tab-pane p-2 border border-secondary rounded bg-light flex-grow-1" id="deployConfigTab">
            <div class="flex-grow-1" style="padding: 2rem;">
                <label for="deployConfigName">Config name</label>
                <select id="deployConfigName">
                    <option></option>
                    <option value="rulesource">Rules / Sources</option>
                    <option value="api">APIs</option>
                    <option value="template">Text Templates</option>
                    <option value="outcome">Outcomes</option>
                    <option value="messagebucket">Message Buckets</option>
                </select>
                <br>
                <label for="commitMessageInput">Commit message</label>
                <textarea class="form-control" id="commitMessageInput" maxlength="100"></textarea>
                <small>Committing the selected configuration to GitLab will increment the version number and push any changes made to the configuration file (config/APIs.json for APIs, config/SCRules.json for Rules / Sources and config/textTemplates.json for Text Templates) onto the git branch for this environment. The checkbox for "Start CI/CD Pipeline" is optional and should only be selected to deploy changes to the config file on the application itself, <b>the only effect</b> this will have is that it will remove any diffs present in the other tabs. Changes made to any APIs / rules / sources / text templates that are being committed are already live in the database and will affect any service checks or text templates that are currently rendered.</small>
                <br>
                <div id="deployRunningPipelineWarning" class="alert alert-warning" style="display:none;">A CI/CD pipeline is already running for this environment, performing a commit now may overwrite other changes to the configuration. <a target="blank" href="<%- global.gConfig.gitPipelineUri %>">pipelines</a></div>
                <button id="deployConfig" class="btn btn-primary" disabled>Commit configuration to GitLab</button>
                <input id="deployConfigStartPipeline" type="checkbox" checked>
                <label class="col-form-label" for="deployConfigStartPipeline">Start CI/CD Pipeline</label>
                <br>
                <br>
                <div id="deployConfigAlert" class="alert alert-success" style="display:none;"></div>
                <br>
                <label for="pipelineProgressBar">CI/CD pipeline job progress</label>
                <div class="progress">
                    <div id="pipelineProgressBar" class="progress-bar progress-bar-striped active" role="progressbar" style="width:0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
                <br>
                <label for="outputConsole" class="text-dark">Console</label>
                <div id="outputConsole" class="border flex-grow-1" style="height: 100%; width: 100%; overflow: hidden;"></div>
            </div>
        </div>
    </div>
</div>

<div id="diffModal" class="modal fade" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-xlg" role="document">
        <div class="modal-content" style="height:96vh;">
            <div class="modal-header">
                <h5 id="diffModalTitle" class="modal-title"></h5>
            </div>
            <div class="modal-body">
                <ul class="nav nav-pills bg-white">
                    <li class="nav-item diffSelect" data-difftype="allFields">
                        <a class="nav-link active" data-toggle="tab" role="tab" href="">All Fields</a>
                    </li>
                    <li class="nav-item diffSelect" data-difftype="preCondition">
                        <a class="nav-link" data-toggle="tab" role="tab" href="">Pre-Condition Code</a>
                    </li>
                    <li class="nav-item diffSelect" data-difftype="ruleCode">
                        <a class="nav-link" data-toggle="tab" role="tab" href="">Rule Code</a>
                    </li>
                    <li class="nav-item diffSelect" data-difftype="valueMsgStm">
                        <a class="nav-link" data-toggle="tab" role="tab" href="">Value Message Code</a>
                    </li>
                    <li class="nav-item diffSelect" data-difftype="extraInfo">
                        <a class="nav-link" data-toggle="tab" role="tab" href="">Extra Info Code</a>
                    </li>
                    <li class="nav-item diffSelect" data-difftype="overrideCompare">
                        <a class="nav-link" data-toggle="tab" role="tab" href="">Override Compare Code</a>
                    </li>
                    <li class="nav-item diffSelect" data-difftype="param">
                        <a class="nav-link" data-toggle="tab" role="tab" href="">Parameter Code</a>
                    </li>
                    <li class="nav-item diffSelect" data-difftype="overrideErrorCondition">
                        <a class="nav-link" data-toggle="tab" role="tab" href="">Override Error Condition Code</a>
                    </li>
                    <li class="nav-item diffSelect" data-difftype="errorMessage">
                        <a class="nav-link" data-toggle="tab" role="tab" href="">Error Message Code</a>
                    </li>
                    <li class="nav-item diffSelect" data-difftype="baseUrl">
                        <a class="nav-link" data-toggle="tab" role="tab" href="">API Base URL</a>
                    </li>
                    <li class="nav-item diffSelect" data-difftype="uri">
                        <a class="nav-link" data-toggle="tab" role="tab" href="">API URI</a>
                    </li>
                    <li class="nav-item diffSelect" data-difftype="queryParams">
                        <a class="nav-link" data-toggle="tab" role="tab" href="">API Query Parameters</a>
                    </li>
                    <li class="nav-item diffSelect" data-difftype="body">
                        <a class="nav-link" data-toggle="tab" role="tab" href="">API Body</a>
                    </li>
                    <li class="nav-item diffSelect" data-difftype="header">
                        <a class="nav-link" data-toggle="tab" role="tab" href="">API Header</a>
                    </li>
                    <li class="nav-item diffSelect" data-difftype="parseResponse">
                        <a class="nav-link" data-toggle="tab" role="tab" href="">Parse Response Code</a>
                    </li>
                    <li class="nav-item diffSelect" data-difftype="pollCondition">
                        <a class="nav-link" data-toggle="tab" role="tab" href="">Poll Condition Code</a>
                    </li>
                    <li class="nav-item diffSelect" data-difftype="errorCondition">
                        <a class="nav-link" data-toggle="tab" role="tab" href="">Error Condition Code</a>
                    </li>
                    <li class="nav-item diffSelect" data-difftype="template">
                        <a class="nav-link" data-toggle="tab" role="tab" href="">Template Code</a>
                    </li>
                    <li class="nav-item diffSelect" data-difftype="messageFrontOfHouse">
                        <a class="nav-link" data-toggle="tab" role="tab" href="">Front of House Message</a>
                    </li>
                    <li class="nav-item diffSelect" data-difftype="messageCustomer">
                        <a class="nav-link" data-toggle="tab" role="tab" href="">Customer Message</a>
                    </li>
                </ul>
                <div class="row">
                    <div class="col-6">
                        <p>Active (in database)</p>
                    </div>
                    <div class="col-6">
                        <p>Config file</p>
                    </div>
                </div>
                <div class="row" style="height:96%;">
                    <div class="col-12">
                        <div id="diffEditorAll"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-outline-secondary">Close</button>
            </div>
        </div>
    </div>
</div>
<script>
    const gitlabPipelineUri = "<%- global.gConfig.gitPipelineUri %>";
    const gitlabCommitsUri = "<%- global.gConfig.gitCommitsUri %>";

    const ConfigType = Object.freeze({
        rule: "rule",
        source: "source",
        api: "api",
        textTemplate: "textTemplate",
        outcome: "outcome",
        messageBucket: "messageBucket"
    });

    const ConfigTypeDisplayName = Object.freeze({
        [ConfigType.rule]: "Rule",
        [ConfigType.source]: "Source",
        [ConfigType.api]: "API",
        [ConfigType.textTemplate]: "Text template",
        [ConfigType.outcome]: "Outcome",
        [ConfigType.messageBucket]: "Message bucket"
    });


    $(document).ready(function() {
        $.ajax({
            cache: false,
            type: "GET",
            url: "/config/deploy/pipelineRunning",
            timeout: 60000,
            success: function (response) {
                if (response && response.pipelineRunningCount > 0) {
                    $("#deployRunningPipelineWarning").show();
                }
            },
            error: function (xhr, status, e) {
                console.error(e);
            },
        });

        let diffEditor = new AceDiff({
            element: "#diffEditorAll",
            mode: "ace/mode/json",
            left: {
                content: "",
                editable: false,
                copyLinkEnabled: false
            },
            right: {
                content: "",
                editable: false,
                copyLinkEnabled: false
            }
        });

        diffEditor.getEditors().left.setOptions({
            minLines: 10,
            maxLines: 40
        });
        diffEditor.getEditors().right.setOptions({
            minLines: 10,
            maxLines: 40
        });

        let pollPipelineJobs = null;

        $("#deployConfigName").select2({
            minimumResultsForSearch: -1,
            width: "16em",
            placeholder: "select config"
        });

        let outputConsole = ace.edit("outputConsole", {
            mode: "ace/mode/text",
            minLines: 20,
            maxLines: 100,
            readOnly: true,
            showLineNumbers: false,
            printMargin: false,
            showGutter: false,
            showFoldWidgets: false
        });

        $("#rulesDiffGrid").jsGrid({
            width: "100%",
            height: "100%",
            filtering: false,
            inserting: false,
            editing: false,
            sorting: true,
            autoload: true,
            paging: true,
            pageLoading: true,
            pageSize: 50,
            pageIndex: 1,
            editButton: false,
            deleteButton: false,
            controller: {
                loadData: function(filter) {
                    let data = $.Deferred();

                    let queryData = setupQueryDataFromFilter(filter);
                    queryData.instanceName = "rule";

                    $.ajax({
                        cache: false,
                        type: "GET",
                        url: "/config/diff",
                        data: queryData,
                        timeout: 60000,
                        success: function (response) {
                            data.resolve({
                                data: response.results,
                                itemsCount: response.metadata.pagination.total
                            });

                            $("#displayDiffRulesTotal").text(`${response.metadata.pagination.total} total`);
                            $("#rulesDiffCount").text(response.metadata.pagination.total);

                            // Enables deployment button if there is at least 1 diff
                            if (response.metadata.pagination.total) {
                                $("#deployConfigRules").removeAttr("disabled");
                            } else {
                                $("#deployConfigRules").attr("disabled", "");
                            }
                        },
                        error: function (xhr, status, e) {
                            // Not the best solution, puts nothing in the table
                            console.error(e);

                            data.resolve({
                                data: [],
                                itemsCount: 0
                            });
                        },
                        complete: function() {
                            setButtonSpinner($("#rulesDiffRefresh"), $("<span>").addClass("fas fa-sync"), false);
                        }
                    });

                    return data.promise();
                }
            },
            fields:  [
                { name: "name", title: "Name", type: "text", itemTemplate: function(value) {
                    return $("<a>").attr("href", `/edit/rules/${encodeURIComponent(value)}`).attr("target", "blank").text(value);
                }},
                { title: "View diff", type: "text", align: "center", width: "2em", sorting: false, filtering: false, itemTemplate: function(value, item) {
                    return $("<button>").addClass("btn btn-secondary").html($("<span>").addClass("fas fa-external-link-alt")).click(setupDiffModal(ConfigType.rule, item, diffEditor));
                }}
            ]
        });

        $("#sourcesDiffGrid").jsGrid({
            width: "100%",
            height: "100%",
            filtering: false,
            inserting: false,
            editing: false,
            sorting: true,
            autoload: true,
            paging: true,
            pageLoading: true,
            pageSize: 50,
            pageIndex: 1,
            editButton: false,
            deleteButton: false,
            controller: {
                loadData: function(filter) {
                    let data = $.Deferred();

                    let queryData = setupQueryDataFromFilter(filter);
                    queryData.instanceName = "source";

                    $.ajax({
                        cache: false,
                        type: "GET",
                        url: "/config/diff",
                        data: queryData,
                        timeout: 60000,
                        success: function (response) {
                            data.resolve({
                                data: response.results,
                                itemsCount: response.metadata.pagination.total
                            });

                            $("#displayDiffSourcesTotal").text(`${response.metadata.pagination.total} total`);
                            $("#sourcesDiffCount").text(response.metadata.pagination.total);

                            // Enables deployment button if there is at least 1 diff
                            if (response.metadata.pagination.total) {
                                $("#deployConfigSources").removeAttr("disabled");
                            } else {
                                $("#deployConfigSources").attr("disabled", "");
                            }
                        },
                        error: function (xhr, status, e) {
                            // Not the best solution, puts nothing in the table
                            console.error(e);

                            data.resolve({
                                data: [],
                                itemsCount: 0
                            });
                        },
                        complete: function() {
                            setButtonSpinner($("#sourcesDiffRefresh"), $("<span>").addClass("fas fa-sync"), false);
                        }
                    });

                    return data.promise();
                }
            },
            fields:  [
                { name: "name", title: "Name", type: "text", width: "8em", itemTemplate: function(value) {
                    return $("<a>").attr("href", `/edit/sources/${encodeURIComponent(value)}`).attr("target", "blank").text(value);
                }},
                { title: "View diff", type: "text", align: "center", width: "2em", sorting: false, filtering: false, itemTemplate: function(value, item) {
                    return $("<button>").addClass("btn btn-secondary").html($("<span>").addClass("fas fa-external-link-alt")).click(setupDiffModal(ConfigType.source, item, diffEditor));
                }}
            ]
        });

        $("#apisDiffGrid").jsGrid({
            width: "100%",
            height: "100%",
            filtering: false,
            inserting: false,
            editing: false,
            sorting: true,
            autoload: true,
            paging: true,
            pageLoading: true,
            pageSize: 50,
            pageIndex: 1,
            editButton: false,
            deleteButton: false,
            controller: {
                loadData: function(filter) {
                    let data = $.Deferred();

                    let queryData = setupQueryDataFromFilter(filter);
                    queryData.instanceName = "api";

                    $.ajax({
                        cache: false,
                        type: "GET",
                        url: "/config/diff",
                        data: queryData,
                        timeout: 60000,
                        success: function (response) {
                            data.resolve({
                                data: response.results,
                                itemsCount: response.metadata.pagination.total
                            });

                            $("#displayDiffApisTotal").text(`${response.metadata.pagination.total} total`);
                            $("#apisDiffCount").text(response.metadata.pagination.total);
                        },
                        error: function (xhr, status, e) {
                            // Not the best solution, puts nothing in the table
                            console.error(e);

                            data.resolve({
                                data: [],
                                itemsCount: 0
                            });
                        },
                        complete: function() {
                            setButtonSpinner($("#apisDiffRefresh"), $("<span>").addClass("fas fa-sync"), false);
                        }
                    });

                    return data.promise();
                }
            },
            fields:  [
                { name: "name", title: "Name", type: "text", itemTemplate: function(value) {
                    return $("<a>").attr("href", `/edit/apis/${encodeURIComponent(value)}`).attr("target", "blank").text(value);
                }},
                { title: "View diff", type: "text", align: "center", width: "2em", sorting: false, filtering: false, itemTemplate: function(value, item) {
                    return $("<button>").addClass("btn btn-secondary").html($("<span>").addClass("fas fa-external-link-alt")).click(setupDiffModal(ConfigType.api, item, diffEditor));
                }}
            ]
        });

        $("#textTemplatesDiffGrid").jsGrid({
            width: "100%",
            height: "100%",
            filtering: false,
            inserting: false,
            editing: false,
            sorting: true,
            autoload: true,
            paging: true,
            pageLoading: true,
            pageSize: 50,
            pageIndex: 1,
            editButton: false,
            deleteButton: false,
            controller: {
                loadData: function(filter) {
                    let data = $.Deferred();

                    let queryData = setupQueryDataFromFilter(filter);
                    queryData.instanceName = "template";

                    $.ajax({
                        cache: false,
                        type: "GET",
                        url: "/config/diff",
                        data: queryData,
                        timeout: 60000,
                        success: function (response) {
                            data.resolve({
                                data: response.results,
                                itemsCount: response.metadata.pagination.total
                            });

                            $("#displayDiffTextTemplatesTotal").text(`${response.metadata.pagination.total} total`);
                            $("#textTemplatesDiffCount").text(response.metadata.pagination.total);
                        },
                        error: function (xhr, status, e) {
                            // Not the best solution, puts nothing in the table
                            console.error(e);

                            data.resolve({
                                data: [],
                                itemsCount: 0
                            });
                        },
                        complete: function() {
                            setButtonSpinner($("#textTemplatesDiffRefresh"), $("<span>").addClass("fas fa-sync"), false);
                        }
                    });

                    return data.promise();
                }
            },
            fields:  [
                { name: "name", title: "Name", type: "text", itemTemplate: function(value) {
                    return $("<a>").attr("href", `/edit/templates/${encodeURIComponent(value)}`).attr("target", "blank").text(value);
                }},
                { title: "View diff", type: "text", align: "center", width: "2em", sorting: false, filtering: false, itemTemplate: function(value, item) {
                    return $("<button>").addClass("btn btn-secondary").html($("<span>").addClass("fas fa-external-link-alt")).click(setupDiffModal(ConfigType.textTemplate, item, diffEditor));
                }}
            ]
        });


        $("#outcomesDiffGrid").jsGrid({
            width: "100%",
            height: "100%",
            filtering: false,
            inserting: false,
            editing: false,
            sorting: true,
            autoload: true,
            paging: true,
            pageLoading: true,
            pageSize: 50,
            pageIndex: 1,
            editButton: false,
            deleteButton: false,
            controller: {
                loadData: function(filter) {
                    let data = $.Deferred();

                    let queryData = setupQueryDataFromFilter(filter);
                    queryData.instanceName = "outcome";

                    $.ajax({
                        cache: false,
                        type: "GET",
                        url: "/config/diff",
                        data: queryData,
                        timeout: 60000,
                        success: function (response) {
                            data.resolve({
                                data: response.results,
                                itemsCount: response.metadata.pagination.total
                            });

                            $("#displayDiffOutcomesTotal").text(`${response.metadata.pagination.total} total`);
                            $("#outcomesDiffCount").text(response.metadata.pagination.total);
                        },
                        error: function (xhr, status, e) {
                            // Not the best solution, puts nothing in the table
                            console.error(e);

                            data.resolve({
                                data: [],
                                itemsCount: 0
                            });
                        },
                        complete: function() {
                            setButtonSpinner($("#outcomesDiffRefresh"), $("<span>").addClass("fas fa-sync"), false);
                        }
                    });

                    return data.promise();
                }
            },
            fields:  [
                { name: "name", title: "Name", type: "text", itemTemplate: function(value) {
                    return $("<a>").attr("href", `/edit/outcomes/${encodeURIComponent(value)}`).attr("target", "blank").text(value);
                }},
                { title: "View diff", type: "text", align: "center", width: "2em", sorting: false, filtering: false, itemTemplate: function(value, item) {
                    return $("<button>").addClass("btn btn-secondary").html($("<span>").addClass("fas fa-external-link-alt")).click(setupDiffModal(ConfigType.outcome, item, diffEditor));
                }}
            ]
        });

        $("#messageBucketsDiffGrid").jsGrid({
            width: "100%",
            height: "100%",
            filtering: false,
            inserting: false,
            editing: false,
            sorting: true,
            autoload: true,
            paging: true,
            pageLoading: true,
            pageSize: 50,
            pageIndex: 1,
            editButton: false,
            deleteButton: false,
            controller: {
                loadData: function(filter) {
                    let data = $.Deferred();

                    let queryData = setupQueryDataFromFilter(filter);
                    queryData.instanceName = "messagebucket";

                    $.ajax({
                        cache: false,
                        type: "GET",
                        url: "/config/diff",
                        data: queryData,
                        timeout: 60000,
                        success: function (response) {
                            data.resolve({
                                data: response.results,
                                itemsCount: response.metadata.pagination.total
                            });

                            $("#displayDiffMessageBucketsTotal").text(`${response.metadata.pagination.total} total`);
                            $("#messageBucketsDiffCount").text(response.metadata.pagination.total);
                        },
                        error: function (xhr, status, e) {
                            // Not the best solution, puts nothing in the table
                            console.error(e);

                            data.resolve({
                                data: [],
                                itemsCount: 0
                            });
                        },
                        complete: function() {
                            setButtonSpinner($("#messageBucketsDiffRefresh"), $("<span>").addClass("fas fa-sync"), false);
                        }
                    });

                    return data.promise();
                }
            },
            fields:  [
                { name: "name", title: "Name", type: "text", itemTemplate: function(value) {
                    return $("<a>").attr("href", `/edit/messageBuckets/${encodeURIComponent(value)}`).attr("target", "blank").text(value);
                }},
                { title: "View diff", type: "text", align: "center", width: "2em", sorting: false, filtering: false, itemTemplate: function(value, item) {
                    return $("<button>").addClass("btn btn-secondary").html($("<span>").addClass("fas fa-external-link-alt")).click(setupDiffModal(ConfigType.messageBucket, item, diffEditor));
                }}
            ]
        });

        $("#rulesDiffRefresh").click(function() {
            setButtonSpinner($("#rulesDiffRefresh"), $("<span>").addClass("fas fa-sync"), true);
            $("#rulesDiffGrid").jsGrid("loadData");
        });

        $("#sourcesDiffRefresh").click(function() {
            setButtonSpinner($("#sourcesDiffRefresh"), $("<span>").addClass("fas fa-sync"), true);
            $("#sourcesDiffGrid").jsGrid("loadData");
        });

        $("#apisDiffRefresh").click(function() {
            setButtonSpinner($("#apisDiffRefresh"), $("<span>").addClass("fas fa-sync"), true);
            $("#apisDiffGrid").jsGrid("loadData");
        });

        $("#textTemplatesDiffRefresh").click(function() {
            setButtonSpinner($("#textTemplatesDiffRefresh"), $("<span>").addClass("fas fa-sync"), true);
            $("#textTemplatesDiffGrid").jsGrid("loadData");
        });

        $("#outcomesDiffRefresh").click(function() {
            setButtonSpinner($("#outcomesDiffRefresh"), $("<span>").addClass("fas fa-sync"), true);
            $("#outcomesDiffGrid").jsGrid("loadData");
        });

        $("#messageBucketsDiffRefresh").click(function() {
            setButtonSpinner($("#messageBucketsDiffRefresh"), $("<span>").addClass("fas fa-sync"), true);
            $("#messageBucketsDiffGrid").jsGrid("loadData");
        });

        $("#refreshAll").click(function() {
            $("#rulesDiffRefresh").click();
            $("#sourcesDiffRefresh").click();
            $("#apisDiffRefresh").click();
            $("#textTemplatesDiffRefresh").click();
            $("#outcomesDiffRefresh").click();
            $("#messageBucketsDiffRefresh").click();
        });

        $("a[data-toggle=\"tab\"]").on("shown.bs.tab", function (e) {
            // Loads data again
            switch(this.dataset.target) {
                case "#rulesDiffTab":
                    $("#rulesDiffGrid").jsGrid("loadData");
                    break;
                case "#sourcesDiffTab":
                    $("#sourcesDiffGrid").jsGrid("loadData");
                    break;
                case "#apisDiffTab":
                    $("#apisDiffGrid").jsGrid("loadData");
                    break;
                case "#textTemplatesDiffTab":
                    $("#textTemplatesDiffGrid").jsGrid("loadData");
                    break;
                case "#outcomesDiffTab":
                    $("#outcomesDiffGrid").jsGrid("loadData");
                    break;
                case "#messageBucketsDiffTab":
                    $("#messageBucketsDiffGrid").jsGrid("loadData");
                    break;
            }
        });

        $("#deployConfigName").on("select2:select", function (e) {
            let commitMessage = $("#commitMessageInput").val();

            if (commitMessage.length) {
                // Will enable deployment as long as a config type is select
                // Does not implement the behvaiour where it will remain disabled if no diffs are detected
                $("#deployConfig").removeAttr("disabled");
            }
        });

        $("#commitMessageInput").bind("input propertychange", function() {
            if (this.value.length && $("#deployConfigName").val()) {
                $("#deployConfig").removeAttr("disabled");
            } else {
                $("#deployConfig").attr("disabled", true);
            }
        });

        $("#deployConfig").click(function() {
            updateProgressBar(0, true);
            setButtonSpinner($("#deployConfig"), "Commit configuration to GitLab", true);

            let commitMessage = $("#commitMessageInput").val();
            let configName = $("#deployConfigName").val();
            let promise = $.Deferred();

            $.ajax({
                type: "POST",
                url: "/config/deploy",
                data: {
                    commitMessage: commitMessage,
                    configName: configName
                },
                timeout: 180000,
                success: function(data) {
                    let commitId = data.id;
                    let version = data.version;

                    $("#deployConfigAlert").addClass("alert-success");
                    $("#deployConfigAlert").removeClass("alert-danger");
                    $("#deployConfigAlert").html(`Commit successful, view commit diff at <a href="${gitlabCommitsUri}/${commitId}" target="blank">${gitlabCommitsUri}/${commitId}</a>`);
                    $("#deployConfigAlert").show();

                    editorAppendText(outputConsole, `Deployment commit ID: ${commitId}`);
                    editorAppendText(outputConsole, `${configName} version incremented to: ${version}`);

                    let startPipeline = $("#deployConfigStartPipeline").prop("checked");

                    if (startPipeline) {
                        // The pipeline for the commit is not created instantly
                        // This solution is not ideal, but it just waits 10 seconds before attempting to find the pipeline ID
                        setTimeout(function() {
                            $.ajax({
                                type: "GET",
                                url: "/config/deploy/pipelineId",
                                data: {
                                    commitId: commitId
                                },
                                timeout: 10000,
                                success: function(data) {
                                    let pipelineId = data.id;
                                    editorAppendText(outputConsole, `Deployment pipeline ID: ${pipelineId}`);

                                    $("#deployConfigAlert").append(`<br>Pipeline progress <a href="${gitlabPipelineUri}" target="blank">${gitlabPipelineUri}</a>`);

                                    $.ajax({
                                        type: "POST",
                                        url: "/config/deploy/pipelineStartJob",
                                        data: {
                                            pipelineId: pipelineId
                                        },
                                        timeout: 20000,
                                        success: function(data) {
                                            editorAppendText(outputConsole, `Started job in pipeline ID: ${pipelineId}`);
                                            promise.resolve(pipelineId);
                                        },
                                        error: function (xhr, status, e) {
                                            promise.reject(new Error(`Could not start manual job for pipeline ${pipelineId}`));
                                        }
                                    });
                                },
                                error: function (xhr, status, e) {
                                    promise.reject(new Error(`Pipeline ID for commit ${commitId} could not be found`));
                                }
                            });
                        }, 10000);
                    }
                },
                error: function (xhr, status, e) {
                    $("#deployConfigAlert").removeClass("alert-success");
                    $("#deployConfigAlert").addClass("alert-danger");
                    $("#deployConfigAlert").text("Commit error, view server logs");
                    $("#deployConfigAlert").show();

                    console.error(e);

                    promise.reject(new Error(`Could not commit ${configName} configuration changes.`));
                },
                complete: function() {
                    setButtonSpinner($("#deployConfig"), "Commit configuration to GitLab", false);
                }
            });

            $.when(promise).done(function(pipelineId) {
                let foundJobs = false;
                let completedJobs = new Set();

                pollPipelineJobs = setInterval(function() {
                    $.ajax({
                        type: "GET",
                        url: "/config/deploy/pipelineJobs",
                        data: {
                            pipelineId: pipelineId
                        },
                        timeout: 10000,
                        success: function(data) {
                            if (!foundJobs) {
                                editorAppendText(outputConsole, `${data.length} job(s) found`);
                                updateProgressBar(data.length+1, true);
                                foundJobs = true;
                                updateProgressBar();
                            }

                            for (let i = 0; i < data.length; i++) {
                                let job = data[i];

                                if (!completedJobs.has(job.id)) {
                                    switch (job.status) {
                                        case "success":
                                            completedJobs.add(job.id);
                                            updateProgressBar();
                                            editorAppendText(outputConsole, `Job ${job.name} for stage ${job.stage} has completed with status ${job.status}`);
                                            break;
                                        case "failed":
                                        case "canceled":
                                        case "skipped":
                                            completedJobs.add(job.id);
                                            updateProgressBar();
                                            editorAppendText(outputConsole, `Warning: Job ${job.name} for stage ${job.stage} has status ${job.status}, please check GitLab for full details`);
                                            break;
                                    }
                                }
                            }

                            $.ajax({
                                type: "GET",
                                url: "/config/deploy/pipelines",
                                data: {
                                    pipelineId: pipelineId
                                },
                                timeout: 10000,
                                success: function(data) {
                                    if (data && data.status) {
                                        switch (data.status) {
                                            case "success":
                                                clearInterval(pollPipelineJobs);
                                                editorAppendText(outputConsole, `Pipeline ${pipelineId} successful`);
                                                break;
                                            case "failed":
                                            case "canceled":
                                            case "skipped":
                                                clearInterval(pollPipelineJobs);
                                                editorAppendText(outputConsole, `Warning: Pipeline ${pipelineId} may have failed with status ${data.status}, please check GitLab for full details`);
                                                break;
                                        }
                                    }
                                },
                                error: function (xhr, status, e) {
                                    editorAppendText(outputConsole, `Error getting pipeline status: ${e}`);
                                    console.error(e);
                                }
                            });
                        },
                        error: function (xhr, status, e) {
                            editorAppendText(outputConsole, `Error getting pipeline jobs: ${e}`);
                            console.error(e);
                        }
                    });

                }, 10000);
            }).catch(function(error) {
                console.error(error);
                editorAppendText(outputConsole, `Error with CI/CD pipeline: ${error.message}`);
            });
        });

        $(".diffSelect").on('click', function() {
            let editors = diffEditor.getEditors();

            let diffType = $(this).attr("data-diffType");
            let active = $(this).attr("data-active");
            let config = $(this).attr("data-config");

            let editorDiffMode;

            switch(diffType) {
                case "allFields":
                    editorDiffMode = "ace/mode/json";
                    break;
                case "messageFrontOfHouse":
                case "messageCustomer":
                    editorDiffMode = "ace/mode/text";
                    break;
                case "template":
                    editorDiffMode = "ace/mode/ejs";
                    break;
                default:
                    editorDiffMode = "ace/mode/javascript";
                    break;
            }

            editors.left.setOptions({
                mode: editorDiffMode
            });

            editors.right.setOptions({
                mode: editorDiffMode
            });

            editors.left.setValue(active);
            editors.left.clearSelection();
            editors.right.setValue(config);
            editors.right.clearSelection();
        });
    });

    function setupQueryDataFromFilter(filter) {
        let limit = filter.pageSize;
        let offset = (filter.pageIndex - 1)*filter.pageSize;

        let queryData = {};

        if (filter.sortField) {
            queryData.order = filter.sortOrder;
        }

        queryData.limit = limit;
        queryData.offset = offset;

        return queryData;
    }

    function editorAppendText(editor, text) {
        let currDate = new Date().toLocaleString('en-GB');
        editor.session.insert({
            row: editor.session.getLength(),
            column: 0
        }, `[${currDate}] ${text}\n`);
    }

    function updateProgressBar(valueMax=0, reset=false) {
        let isComplete = false;

        if (reset) {
            $("#pipelineProgressBar").width("0%");
            $("#pipelineProgressBar").attr('aria-valuenow', 0);
            $("#pipelineProgressBar").attr('aria-valuemax', valueMax);
            $("#pipelineProgressBar").addClass("progress-bar-animated");
        } else {
            let valueNow = parseInt($("#pipelineProgressBar").attr('aria-valuenow')) + 1;
            $("#pipelineProgressBar").attr('aria-valuenow', valueNow);
            let valueMax = parseInt($("#pipelineProgressBar").attr('aria-valuemax'));
            let percentage = valueNow / valueMax * 100;
            $("#pipelineProgressBar").width(`${percentage}%`);

            if (valueNow === valueMax) {
                $("#pipelineProgressBar").removeClass("progress-bar-animated");
                isComplete = true;
            }
        }

        return isComplete;
    }

    function setupDiffModal(name, item, diffEditor) {
        return function() {
            let diffTypes = [];
            switch(name) {
                case ConfigType.rule:
                    diffTypes = [
                        "preCondition",
                        "ruleCode",
                        "valueMsgStm",
                        "extraInfo",
                        "overrideCompare"
                    ];
                    break;
                case ConfigType.source:
                    diffTypes = [
                        "preCondition",
                        "param",
                        "overrideErrorCondition",
                        "errorMessage"
                    ];
                    break;
                case ConfigType.api:
                    diffTypes = [
                        "baseUrl",
                        "uri",
                        "queryParams",
                        "body",
                        "header",
                        "parseResponse",
                        "pollCondition",
                        "errorCondition"
                    ];
                    break;
                case ConfigType.textTemplate:
                    diffTypes = [
                        "template"
                    ];
                    break;
                case ConfigType.outcome:
                    diffTypes = [];
                    break;
                case ConfigType.messageBucket:
                    diffTypes = [
                        "messageFrontOfHouse",
                        "messageCustomer"
                    ];
                    break;
            }

            if (item.active) {
                $(".diffSelect[data-diffType='allFields']").attr("data-active", JSON.stringify(item.active, null, 4));

                for (let diffType of diffTypes) {
                    $(`.diffSelect[data-diffType='${diffType}']`).attr("data-active", item.active[diffType]);
                }
            } else {
                $(".diffSelect[data-diffType='allFields']").attr("data-active", `${ConfigTypeDisplayName[name]} ${item.name} could not be found in database`);
                for (let diffType of diffTypes) {
                    $(`.diffSelect[data-diffType='${diffType}']`).attr("data-active", "");
                }
            }

            if (item.config) {
                $(".diffSelect[data-diffType='allFields']").attr("data-config", JSON.stringify(item.config, null, 4));

                for (let diffType of diffTypes) {
                    $(`.diffSelect[data-diffType='${diffType}']`).attr("data-config", item.config[diffType]);
                }
            } else {
                $(".diffSelect[data-diffType='allFields']").attr("data-config", `${ConfigTypeDisplayName[name]} ${item.name} could not be found in the config file`);
                for (let diffType of diffTypes) {
                    $(`.diffSelect[data-diffType='${diffType}']`).attr("data-config", "");
                }
            }

            // Hides all diff select buttons, then enables the all fields one
            $(".diffSelect").hide();
            $(".diffSelect[data-diffType='allFields']").show();

            for (let diffType of diffTypes) {
                $(`.diffSelect[data-diffType='${diffType}']`).show();
            }

            $(".diffSelect[data-diffType='allFields']>a").click();

            $("#diffModalTitle").text(`${ConfigTypeDisplayName[name]} ${item.name} diff`);
            $("#diffModal").modal("show");
        }
    }
</script>
<%- include('footer', {}); %>