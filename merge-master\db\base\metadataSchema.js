// Base schema for metadata with Merge config objects (such as APIs, outcomes, rules / sources, text templates)

import util from 'util';
import mongoose from 'mongoose';


function MetadataSchema() {
    mongoose.Schema.apply(this, arguments);

    this.add({
        version: { type: Number, required: true },
        updatedBy: { type: String, required: true },
        updatedOn: { type: Date, required: true, default: Date.now }
    });

    this.methods.toJSON = function() {
        let obj = this.toObject();
        delete obj._id;
        delete obj.__v;
        return obj;
    }
}


util.inherits(MetadataSchema, mongoose.Schema);

export default MetadataSchema;
