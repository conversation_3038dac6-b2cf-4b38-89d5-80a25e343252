[{"name": "ADSL", "description": "The service is identified as an ADSL product, from the carriage type, which is determined from the carriage FNN, matching the regex ^A[0-9]{10}$ and doesn't start with A61 or A04"}, {"name": "BDSL", "description": "The service is identified as a BDSL product, from the carriage type, which is determined from the carriage FNN, matching the regex ^Y0[0-9]{10}N$"}, {"name": "Cisco Device", "description": "This indicates that the service is associated with a Cisco device, as determined from the device name, which is obtained from MAGPIE or ODIN or from user input"}, {"name": "FR", "description": "The service is identified as a Frame Relay service, from the carriage type, which is determined from the carriage FNN, matching the regex ^Y216[0-9]{6}N$"}, {"name": "INTERNATIONAL", "description": "The service is identified as an International service, from the carriage type, which is determined from the device name, where its state / international indicator character is I"}, {"name": "IPMAN", "description": "The service is identified as an IPMAN product, from the carriage type, which is determined from the IPMAN component of MAGPIE, if the IPMAN_FNN field natches ^N[0-9]{7}R$, or the carriage FNN matches ^N[0-9]{7}R$ and the service type is in a list of IPMAN service types"}, {"name": "MDN", "description": "This service has a managed device, as determined from MAGPIE or ODIN"}, {"name": "MOBILE", "description": "The service is identified as a mobile product, from the carriage type, which is determined from the carriage FNN, matching the regex ^(A|\\+)?(61|0)4[0-9]{8,9}$"}, {"name": "NBN", "description": "The service is identified as an NBN product, from the carriage type, which is determined from the carriage FNN, matching the regex ^AVC"}, {"name": "NBN EE", "description": "The service is identified as an NBN Enterprise Ethernet product, from the carriage type, which is determined from the carriage FNN, matching the regex ^N[0-9]{7}R$ and from MAGPIE where the AVC field in IPMAN or TE in rawData contains OVC\\d{12}"}, {"name": "Palo Alto Device", "description": "This indicates that the service is associated with a Palo Alto device, as determined from the device name, which is obtained from MAGPIE or ODIN or from user input"}, {"name": "Telstra Fibre Adapt", "description": "The service is identified as a Telstra Fibre product, from the MAGPIE product name in IPMAN / TE rawData, being either Telstra Fibre, BIP Adapt, TID Lite Adapt, TID Premium Adapt"}]