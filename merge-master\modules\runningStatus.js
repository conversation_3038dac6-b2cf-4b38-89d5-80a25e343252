'use strict';

import logger from './logger.js';

const sourcesEnum = Object.freeze({
    API: 'Merge API',
    Socket: 'Socket'
});
const serviceCheckStatus = {};

function serviceCheckStart(serviceCheckRecord, startSource) {
    let id = serviceCheckRecord.id;

    if (id) {
        serviceCheckStatus[id] = {
            start: serviceCheckRecord.createdOn,
            startedBy: startSource
        };
    } else {
        logger.warn(`Service check record was started without "id" field, ${serviceCheckRecord._id}`);
    }
}


function serviceCheckStop(id) {
    if (id in serviceCheckStatus) {
        delete serviceCheckStatus[id];
    } else {
        logger.warn(`Service check record status was stopped without being started, id: ${id}`);
    }
}


function serviceCheckGetStatus() {
    return serviceCheckStatus;
}

export default {
    serviceCheckStart: serviceCheckStart,
    serviceCheckStop: serviceCheckStop,
    serviceCheckGetStatus: serviceCheckGetStatus,
    sourcesEnum: sourcesEnum
};
