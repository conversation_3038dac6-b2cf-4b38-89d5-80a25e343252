import { TextStyle } from '@able/react'
import styles from './ErrorFallback.module.scss'

interface ErrorFallbackProps {
    error: Error
}

export const ErrorFallback = ({ error }: ErrorFallbackProps) => {
    return (
        <div className={styles.errorFallback} role="alert">
            <TextStyle alias="HeadingA">Something went wrong:</TextStyle>
            <pre>{error.message}</pre>
            <TextStyle>Please refresh the page</TextStyle>
        </div>
    )
}
