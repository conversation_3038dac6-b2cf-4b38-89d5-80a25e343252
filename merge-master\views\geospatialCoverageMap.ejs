<%- include('header', { title: title }); %>
<%- include('menu'); %>
<script src="/public/javascripts/react.production.min.js"></script>
<script src="/public/javascripts/react-dom.production.min.js"></script>
<script src="/public/javascripts/babel.min.js"></script>
<script src="/public/javascripts/geo-react.js"></script>

<div id="mapContainer" class="container"></div>
<script type="text/babel">
'use strict';

const { CoverageMap } = window.geoJsLib;

const INITIAL_VALUE_ADDRESS = <%- JSON.stringify(address); %>;
const INITIAL_VALUE_LATITUDE = <%- JSON.stringify(latitude); %>;
const INITIAL_VALUE_LONGITUDE = <%- JSON.stringify(longitude); %>;
const INITIAL_VALUE_ADBOR_ID = <%- JSON.stringify(adborId); %>;
const INITIAL_VALUE_TECHNOLOGY = <%- JSON.stringify(technology); %>;
const API_KEY = <%- JSON.stringify(geospatialServicesClientId); %>;

const NETWORK_TECHNOLOGY_OPTIONS = Object.freeze([
    'NR3600',
    'NR850',
    'LTE2600',
    'LTE2100',
    'LTE1800',
    'LTE700',
    'WCDMA850'
]);

function TextInput(props) {
    const width = props.width ? props.width : 'default';

    return (
        <div class="form-group row">
            <label class="col-1 col-form-label">{props.label}</label>
            <div class="col-1">
                {props.isLoading && <Spinner />}
            </div>
            <div class="col-10">
                <input
                    class={`form-control ${props.isValid === null ? '' : (props.isValid === true ? 'is-valid' : 'is-invalid')}`}
                    value={props.state}
                    style={{ width: width }}
                    type="text"
                    onChange={props.onChange}
                />
                <div class="invalid-feedback">
                    {props.invalidMessage}
                </div>
            </div>
        </div>
    );
}

const TextInputWithOptions = React.forwardRef((props, ref) => {
    const width = props.width ? props.width : 'default';

    return (
        <div class="form-group row" ref={ref}>
            <label class="col-1 col-form-label">{props.label}</label>
            <div class="col-1">
                {props.isLoading && <Spinner />}
            </div>
            <div class="col-10">
                <input
                    class={`form-control ${props.isValid === null ? '' : (props.isValid === true ? 'is-valid' : 'is-invalid')}`}
                    value={props.state}
                    style={{ width: width }}
                    type="text"
                    onChange={props.onChange}
                />
                <div class={`dropdown-menu ${props.options.length > 0 && props.showOptions ? 'show': ''}`} style={{ width: width }}>
                    {props.options.map((option) => <a key={option} onClick={props.onOptionSelect} class="dropdown-item" href="javascript:;">{option}</a>)}
                </div>
                <div class="invalid-feedback">
                    {props.invalidMessage}
                </div>
            </div>
        </div>
    );
});

function NumberInput(props) {
    const width = props.width ? props.width : 'default';

    return (
        <div class="form-group row">
            <label class="col-2 col-form-label">{props.label}</label>
            <div class="col-10">
                <input
                    class={`form-control ${props.isValid === null ? '' : (props.isValid === true ? 'is-valid' : 'is-invalid')}`}
                    value={props.state}
                    onChange={props.onChange}
                    min={props.min}
                    max={props.max}
                    step="any"
                    style={{ width: width }}
                    type="number"
                />
                <div class="invalid-feedback">
                    {props.invalidMessage}
                </div>
            </div>
        </div>
    );
}

function SelectInput(props) {
    const options = Array.isArray(props.options) ? props.options : [];
    const width = props.width ? props.width : 'default';

    return (
        <div class="form-group row">
            <label class="col-2 col-form-label">{props.label}</label>
            <div class="col-10">
                <select value={props.state} onChange={props.onChange} class={`custom-select ${props.isValid ? '' : 'is-invalid'}`} style={{ width: width }}>
                    {typeof props.placeholder === 'string' && <option value="" disabled selected>{props.placeholder}</option>}
                    {options.map((option) => <option value={option}>{option}</option>)}
                </select>
                <div class="invalid-feedback">
                    {props.invalidMessage}
                </div>
            </div>
        </div>
    );
}

function Spinner(props) {
    return (
        <div class="spinner-border" />
    );
}

async function getAddressList(address) {
    const addressListResponse = await fetch('https://tapi.telstra.com/v1/gcm-geoprocessing-service/geocode/unstructured', {
        method: 'POST',
        headers: {
            'apikey': API_KEY,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            query: address,
            pagination: {
                size: '10'
            },
            granularity: [
                'SUBURB',
                'SUBURBVANITY',
                'POSTCODE',
                'REGION',
                'STATE',
                'INTERSECTION',
                'PROPERTY'
            ]
        })
    });

    const addressListResponseData = await addressListResponse.json();

    if (Array.isArray(addressListResponseData?.results)) {
        return addressListResponseData.results
            .filter((addressEntry) => typeof addressEntry?.address?.display === 'string')
            .map((addressEntry) => addressEntry.address.display);
    } else {
        return [];
    }
}

async function checkAdborId(id) {
    // For now, just does a basic regex, but implementation can be replaced with API calls
    // to check whether the ADBOR ID exists or not
    return /^[1-9][0-9]{7,8}$/.test(id);
}

function CoverageMapInput(props) {
    const [address, setAddress] = React.useState(INITIAL_VALUE_ADDRESS);
    const [addressIsValid, setAddressIsValid] = React.useState(null);
    const [addressOptions, setAddressOptions] = React.useState([]);
    const [addressShowOptions, setAddressShowOptions] = React.useState(false);
    const [addressCheckIsLoading, setAddressCheckIsLoading] = React.useState(false);
    const addressSetFromOption = React.useRef(false);
    const [hasInitialAddress, setHasInitialAddress] = React.useState(INITIAL_VALUE_ADDRESS ? true : false);

    const [adborId, setAdborId] = React.useState(INITIAL_VALUE_ADBOR_ID);
    const [adborIdIsValid, setAdborIdIsValid] = React.useState(null);
    const [adborIdCheckIsLoading, setAdborIdCheckIsLoading] = React.useState(false);

    const [latitude, setLatitude] = React.useState(INITIAL_VALUE_LATITUDE);
    const [latitudeIsValid, setLatitudeIsValid] = React.useState(null);
    const [longitude, setLongitude] = React.useState(INITIAL_VALUE_LONGITUDE);
    const [longitudeIsValid, setLongitudeIsValid] = React.useState(null);

    const [technology, setTechnology] = React.useState(NETWORK_TECHNOLOGY_OPTIONS.includes(INITIAL_VALUE_TECHNOLOGY) ? INITIAL_VALUE_TECHNOLOGY : null);
    const [technologyIsSelected, setTechnologyIsSelected] = React.useState(false);

    const technologyIsValid = technology !== null;

    const [showInvalid, setShowInvalid] = React.useState(false);
    const [inputType, setInputType] = React.useState(null);

    let alertContents = <>Please enter an address or ADBOR ID or latitude and longitude and select a technology</>;
    if (address) {
        alertContents = <>Using <b>Address</b> as input for Coverage Map</>;
    } else if (adborId) {
        alertContents = <>Using <b>ADBOR ID</b> as input for Coverage Map</>;
    } else if (latitude && longitude) {
        alertContents = <>Using <b>Latitude / Longitude</b> as input for Coverage Map</>;
    }

    const setCoverageMapState = () => {
        let centrePoint = '';
        if (latitude && longitude) {
            centrePoint = `${latitude},${longitude}`;
        }

        props.updateMapStateHandler({
            technology: technology,
            address: address,
            centrePoint: centrePoint,
            adborId: adborId
        });
    };

    const refreshClickHandler = () => {
        if ((addressIsValid) ||
            (adborIdIsValid) ||
            (latitudeIsValid && longitudeIsValid)) {
            if (technology) {
                setCoverageMapState();
            }
        }
    };

    const addressChangeHandler = (event) => {
        setAddress(event.target.value);

        if (event.target.value) {
            setAdborId('');
            setLatitude('');
            setLongitude('');
        }
    };

    const adborIdChangeHandler = (event) => {
        setAdborId(event.target.value);

        if (event.target.value) {
            setAddress('');
            setLatitude('');
            setLongitude('');
        }
    };

    const latitudeChangeHandler = (event) => {
        setLatitude(event.target.value);

        if (event.target.value) {
            setAddress('');
            setAdborId('');
        }
    };

    const longitudeChangeHandler = (event) => {
        setLongitude(event.target.value);

        if (event.target.value) {
            setAddress('');
            setAdborId('');
        }
    };

    const technologyChangeHandler = (event) => {
        setTechnology(event.target.value);
    };

    const addressSelectHandler = (event) => {
        setAddress(event.target.text);
        setAddressShowOptions(false);
        addressSetFromOption.current = true;
    };

    React.useEffect(() => {
        setAddressIsValid(null);
        setAddressShowOptions(false);

        if (addressSetFromOption.current === true) {
            if (address !== '') {
                // Assumes address if set from option is valid
                setAddressIsValid(true);
                setAddressOptions([address]);
            } else {
                setAddressIsValid(null);
                setAddressOptions([]);
            }

            addressSetFromOption.current = false;
            return () => {};
        } else {
            const debounceFunc = setTimeout(async() => {
                if (address === '') {
                    setAddressOptions([]);
                    return;
                }

                setAddressCheckIsLoading(true);
                let addressList = [];
                try {
                    addressList = await getAddressList(address);
                } catch(error) {
                    console.error(error);
                }

                setAddressOptions(addressList);
                setAddressCheckIsLoading(false);

                // If an initial address is provided and at least 1 address is returned
                // assume it is valid and use it as the input to CoverageMap
                if (hasInitialAddress) {
                    if (addressList.length > 0) {
                        setAddressIsValid(true);
                    } else {
                        setAddressIsValid(false);
                    }
                    setHasInitialAddress(false);
                } else {
                    setAddressShowOptions(true);
                }
            }, 1000);

            return () => clearTimeout(debounceFunc);
        }
    }, [address]);

    React.useEffect(() => {
        setAdborIdIsValid(null);

        const debounceFunc = setTimeout(async() => {
            if (adborId === '') {
                return;
            }

            setAdborIdCheckIsLoading(true);

            let isValid = false;
            try {
                isValid = await checkAdborId(adborId);
            } catch(error) {
                console.error(error);
            }
            setAdborIdIsValid(isValid);
            setAdborIdCheckIsLoading(false);
        }, 1000);

        return () => clearTimeout(debounceFunc);
    }, [adborId]);

    React.useEffect(() => {
        setLatitudeIsValid(null);
        const debounceFunc = setTimeout(async() => {
            let isValid = latitude === '' ? null : (!isNaN(parseFloat(latitude)) && latitude >= -90.0 && latitude <= 90.0);
            setLatitudeIsValid(isValid);
        }, 1000);

        return () => clearTimeout(debounceFunc);
    }, [latitude]);

    React.useEffect(() => {
        setLongitudeIsValid(null);
        const debounceFunc = setTimeout(async() => {
            let isValid = longitude === '' ? null : (!isNaN(parseFloat(longitude)) && longitude >= -180.0 && longitude <= 180.0);
            setLongitudeIsValid(isValid);
        }, 1000);

        return () => clearTimeout(debounceFunc);
    }, [longitude]);

    React.useEffect(() => {
        if ((addressIsValid) ||
            (adborIdIsValid) ||
            (latitudeIsValid && longitudeIsValid)) {
            setTechnologyIsSelected(true);
            if (technology) {
                setCoverageMapState();
            }
        }
    }, [
        address,
        addressIsValid,
        adborIdIsValid,
        latitudeIsValid,
        longitudeIsValid,
        technology
    ]);

    const addressInputRef = React.useRef(null);
    React.useEffect(() => {
        const handleClickOutside = (event) => {
            if (addressInputRef.current && !addressInputRef.current.contains(event.target)) {
                setAddressShowOptions(false);
            } else {
                setAddressShowOptions(true);
            }
        };

        document.addEventListener('click', handleClickOutside, true);
        return () => {
            document.removeEventListener('click', handleClickOutside, true);
        };
    }, []);

    return (
        <>
            <h3>Coverage Map Search</h3>
            <TextInputWithOptions
                label="Address"
                state={address}
                isValid={addressIsValid}
                invalidMessage="Address could not be found"
                onChange={addressChangeHandler}
                options={addressOptions}
                showOptions={addressShowOptions}
                onOptionSelect={addressSelectHandler}
                isLoading={addressCheckIsLoading}
                ref={addressInputRef}
                width="48em"
            />
            <TextInput
                label="ADBOR ID"
                state={adborId}
                isValid={adborIdIsValid}
                invalidMessage="ADBOR ID is not valid"
                onChange={adborIdChangeHandler}
                isLoading={adborIdCheckIsLoading}
                width="12em"
            />
            <NumberInput
                label="Latitude"
                min="-90.0"
                max="90.0"
                state={latitude}
                isValid={latitudeIsValid}
                invalidMessage="Enter a valid latitude between -90.0 and 90.0"
                onChange={latitudeChangeHandler}
                width="12em"
            />
            <NumberInput
                label="Longitude"
                min="-180.0"
                max="180.0"
                state={longitude}
                isValid={longitudeIsValid}
                invalidMessage="Enter a valid longitude between -180.0 and 180.0"
                onChange={longitudeChangeHandler}
                width="12em"
            />
            <SelectInput
                label="Technology"
                state={technology}
                isValid={technologyIsValid || !technologyIsSelected}
                invalidMessage="Please select a technology"
                onChange={technologyChangeHandler}
                placeholder="Select a Technology"
                width="16em"
                options={NETWORK_TECHNOLOGY_OPTIONS}
            />
            <Alert>{alertContents}</Alert>
            <button onClick={refreshClickHandler} class="btn btn-secondary mx-1">Refresh</button>
        </>
    );
}

function Alert(props) {
    return (
        <div class="alert alert-info">{props.children}</div>
    );
}


function CoverageMapSearch(props) {
    const [mapState, setMapState] = React.useState({
        mapid: 'coverage_assurance',
        technology: null,
        selectedAddress: {
            name: '',
            centrePoint: '',
            adborid: ''
        }
    });

    const [mapAddress, setMapAddress] = React.useState('');
    const [mapUpdate, setMapUpdate] = React.useState(null);

    const updateMapStateHandler = (data) => {
        setMapState({
            mapid: 'coverage_assurance',
            technology: data.technology,
            selectedAddress: {
                name: data.address,
                centrePoint: data.centrePoint,
                adborid: data.adborId
            }
        });
    };

    const mapAddressParameterSet = mapState && mapState.technology && mapState.selectedAddress && (
        mapState.selectedAddress.name ||
        mapState.selectedAddress.centrePoint ||
        mapState.selectedAddress.adborid
    );


    // useEffect hooks here force the CoverageMap component to be removed from the DOM
    // whenever mapState changes, because in some cases such as using latitude / longitude only in the mapState,
    // changing the technology value won't cause the map to update
    // The current workaround is for the entire CoverageMap to be removed and rendered again
    React.useEffect(() => {
        setMapUpdate({});
    }, [mapState]);

    React.useEffect(() => {
        if (mapUpdate) {
            setMapUpdate(null);
        }
    }, [mapUpdate]);

    return (
        <div class="border border-secondary rounded p-4">
            <CoverageMapInput updateMapStateHandler={updateMapStateHandler} />
            <hr />
            {mapState && mapAddressParameterSet && !mapUpdate && <CoverageMap apiKey={API_KEY} data={mapState} />}
        </div>
    );
}


const mapContainerDOM = document.querySelector('#mapContainer');
const root = ReactDOM.createRoot(mapContainerDOM);
root.render(<CoverageMapSearch />);
</script>
<%- include('footer', {}); %>