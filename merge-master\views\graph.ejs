<%- include('header', {title: "Merge Service Check Statistics"}) %>
<%- include('menu', {currentTab: 'Form'}); %>
<script type="text/javascript" src="/public/javascripts/chart.min.js"></script>
<script type="text/javascript" src="/public/javascripts/moment.min.js"></script>
<script type="text/javascript" src="/public/javascripts/chartjs-adapter-moment.min.js"></script>
<link rel="stylesheet" type="text/css" href="/public/stylesheets/select2.min.css">
<script type="text/javascript" src="/public/javascripts/select2.min.js"></script>
<script type="text/javascript" src="/public/javascripts/moment-timezone-with-data.min.js"></script>
<script type="text/javascript" src="/public/javascripts/merge_stats.js"></script>
<div class="container">
    <br>
    <div class="jumbotron">
        <h2>Statistics</h2>
        <br>
        <div class="form-row">
            <label class="col-form-label" for="statsLastQuantity">Last</label>
            <input id="statsLastQuantity" type="number" class="" style="width: 4em" value=1 min=1>
            <select id="statsLastUnit" class="">
                <option value="d">days</option>
                <option value="w">weeks</option>
                <option value="m" selected>months</option>
                <option value="y">years</option>
            </select>
            <label class="col-form-label" for="statsIntervalUnit">Group by</label>
            <select id="statsIntervalUnit" class="">
                <option value="h">hour</option>
                <option value="d" selected>day</option>
                <option value="w">week</option>
                <option value="m">month</option>
                <option value="y">year</option>
            </select>
            <label class="col-form-label" for="statsType">Statistics type</label>
            <select id="statsType" class="">
                <option value="scsUsers" selected>Service Check by user</option>
                <option value="scsCarriageTypes">Service Check by carriage type</option>
                <option value="scsFnn">Service Check by FNN</option>
                <option value="scsRules">Service Check by rule</option>
                <option value="scsSources">Service Check by source</option>
                <option value="aaaToolUsers">AAA tool usage by user</option>
            </select>
            <button id="statsTimeApply" class="btn btn-primary">Apply</button>
            <p class="col-form-label" id="statsSince"></p>
        </div>
        <br>
        <div class="form-row">
            <div class="form-check" style="margin-right:20px">
                <input class="form-check-input" type="radio" name="resultFilterRadios" id="resultFilterTop" value="top" checked>
                <label class="form-check-label" for="resultFilterTop">
                    Top
                </label>
                <select id="resultFilterTopNumber" style="width: 4em">
                    <option value=10>10</option>
                    <option value=20 selected>20</option>
                    <option value=30>30</option>
                    <option value=40>40</option>
                    <option value=50>50</option>
                </select>
                <label id="resultFilterTopLabel" class="form-check-label" for="resultFilterTop">
                    users with the most service checks
                </label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="radio" name="resultFilterRadios" id="resultFilterAll" value="all">
                <label id="resultFilterAllLabel" class="form-check-label" for="resultFilterAll">
                    All users
                </label>
            </div>
        </div>
        <label class="col-form-label">Filters</label>
        <a class='expand btn-sm' data-toggle="collapse" data-target="#filterField">
            <span style="color:blue" class="fas fa-plus-square" title="Expand/collapse service check record filters"></span>
        </a>
        <div id="filterField" class="collapse card card-body bg-transparent">
            <div class="form-row">
                <div class="col-12">
                    <label class="col-form-label">Graph Line Filters</label> <span class="fas fa-question-circle" style="color:blue" title="Allows selection of which lines to display on the graph exclusively. Also includes toggling the line displaying the total number of service checks in an interval (not available for all statistics types)."></span>
                </div>
            </div>
            <div class="form-row">
                <div class="col-1">
                    <label id="statsGroupLabel" class="col-form-label" for="statsGroupSelect"></label>
                </div>
                <div class="col-9">
                    <select id="statsGroupSelect" style="width: 100%" multiple="multiple"></select>
                </div>
                <div class="col-2">
                    <input id="statsShowTotal" type="checkbox" checked>
                    <label class="col-form-label" for="statsShowTotal">Show total line</label>
                </div>
            </div>
            <div class="form-row">
                <div class="col-12">
                    <label class="col-form-label">Service Check Filters</label> <span class="fas fa-question-circle" style="color:blue" title="Select filters for which service checks will be included in the numbers rendered in the graphs. Each separate filter field has a logical AND applied over it, whilst multiple filters in the same field have a logical OR applied."></span>
                </div>
            </div>
            <div class="form-row">
                <div class="col-12"><p class="font-weight-light">Change made to these filters will not be applied until the "Apply" button is clicked</p></div>
            </div>
            <div class="form-row">
                <div class="col-1">
                    <label class="col-form-label" for="statsCarriageTypeFilter">Carriage type</label>
                </div>
                <div class="col-11">
                    <select id="statsCarriageTypeFilter" style="width: 100%" multiple="multiple"></select>
                </div>
            </div>
            <hr/>
            <div class="form-row">
                <div class="col-1">
                    <label class="col-form-label" for="statsUserFilter">User</label>
                </div>
                <div class="col-11">
                    <select id="statsUserFilter" style="width: 100%" multiple="multiple"></select>
                </div>
            </div>
            <hr/>
            <div class="form-row">
                <div class="col-1">
                    <label class="col-form-label" for="statsFnnFilter">FNN</label>
                </div>
                <div class="col-11">
                    <select id="statsFnnFilter" style="width: 100%" multiple="multiple"></select>
                </div>
            </div>
            <hr/>
            <div class="form-row">
                <div class="col-1">
                    <label class="col-form-label" for="statsRuleFilter">Rule names</label>
                </div>
                <div class="col-7">
                    <select id="statsRuleFilter" style="width: 100%" multiple="multiple"></select>
                </div>
                <div class="col-1">
                    <label class="col-form-label" for="statsRuleStatusFilter">Status</label>
                </div>
                <div class="col-3">
                    <select id="statsRuleStatusFilter" style="width: 100%" multiple="multiple" disabled>
                        <option value="codeError">Code Error</option>
                        <option value="done">Done</option>
                        <option value="preCondError">Pre-Condition Error</option>
                        <option value="preConError">Pre-Condition OK</option>
                        <option value="preConReject">Pre-Condition Reject</option>
                        <option value="running">Running</option>
                    </select>
                </div>
            </div>
            <br>
            <div class="form-row">
                <div class="col-1">
                    <label class="col-form-label" for="statsRuleTriggerSourceFilter">Trigger source</label>
                </div>
                <div class="col-7">
                    <select id="statsRuleTriggerSourceFilter" style="width: 100%" multiple="multiple" disabled></select>
                </div>
                <div class="col-1">
                    <label class="col-form-label" for="statsRuleResultFilter">Result</label>
                </div>
                <div class="col-3">
                    <select id="statsRuleResultFilter" style="width: 100%" multiple="multiple" disabled>
                        <option value="Actionable">Actionable</option>
                        <option value="Actioned">Actioned</option>
                        <option value="Error">Error</option>
                        <option value="Failed">Failed</option>
                        <option value="OK">OK</option>
                        <option value="Reject">Reject</option>
                        <option value="Warning">Warning</option>
                    </select>
                </div>
            </div>
            <hr/>
            <div class="form-row">
                <div class="col-1">
                    <label class="col-form-label" for="statsSourceFilter">Source names</label>
                </div>
                <div class="col-7">
                    <select id="statsSourceFilter" style="width: 100%" multiple="multiple"></select>
                </div>
                <div class="col-1">
                    <label class="col-form-label" for="statsSourceStatusFilter">Status</label>
                </div>
                <div class="col-3">
                    <select id="statsSourceStatusFilter" style="width: 100%" multiple="multiple" disabled>
                        <option value="API URI error">API URI Error</option>
                        <option value="Collected">Collected</option>
                        <option value="error">Error</option>
                        <option value="Parameter Error">Parameter Error</option>
                        <option value="Pre-Condition Error">Pre-Condition Error</option>
                        <option value="Pre-Condition False">Pre-Condition False</option>
                        <option value="running">Running</option>
                    </select>
                </div>
            </div>
        </div>
        <br>
        <div class="container">
            <canvas id="statisticsChart" width="400" height="200"></canvas>
        </div>
    </div>
</div>

</body>
</html>
