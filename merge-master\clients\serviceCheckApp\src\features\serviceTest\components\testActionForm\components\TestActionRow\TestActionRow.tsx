import { ModalContent } from '@able/react'
import DOMPurify from 'dompurify'
import { useCallback, useMemo, useState } from 'react'
import { useTestAction } from '../../../../../../api/mutations/useTestAction'
import { DetailField } from '../../../../../../components/detailsPanel/DetailField'
import { ErrorPanel } from '../../../../../../components/errorPanel/ErrorPanel'
import { JsonTable } from '../../../../../../components/jsonTable/JsonTable'
import { Panel } from '../../../../../../components/panel/Panel'
import { useUser } from '../../../../../../context/UserContext'
import { formatDate } from '../../../../../../helpers/formatDate'
import { useAppState } from '../../../../../../hooks/useAppState'
import {
    TestActionsModel,
    ValueMsg,
} from '../../../../../../infrastructure/models'
import { LoadingPanel } from '../../../serviceCheckLoadingPanel/LoadingPanel'
import styles from '../../TestActionForm.module.scss'
import { ActionRow } from '../ActionRow/ActionRow'
import { DateInputActionRow } from '../DateInputActionRow/DateInputActionRow'
import { PaginatedTable } from '../PaginatedTable/PaginatedTable'

interface TestActionRowProps {
    action: TestActionsModel
}

export const TestActionRow = ({ action }: TestActionRowProps) => {
    const [showModal, setShowModal] = useState(false)
    const [canCloseModal, setCanCloseModal] = useState(true)
    const { appState, setAppState, refreshApp } = useAppState()
    const { mutateAsync: runTestAction, isPending } = useTestAction()
    const [errorMessage, setErrorMessage] = useState('')
    const { canModify } = useUser()

    // State to hold additionalParameters coming from datetime-locale userInputs.
    const [dateRangeParams, setDateRangeParams] = useState<
        Record<string, string>
    >({})
    const [dateRangeCanRun, setDateRangeCanRun] = useState<boolean>(true)

    const isValidValueMsg = (value: unknown): value is ValueMsg => {
        if (typeof value !== 'object' || value === null) return false
        const maybeValueMsg = value as {
            renderAsTable?: unknown
            data?: unknown
        }
        return (
            typeof maybeValueMsg.renderAsTable === 'boolean' &&
            Array.isArray(maybeValueMsg.data)
        )
    }

    const parsedValueMsg = useMemo((): ValueMsg | null => {
        // If valueMsg is a string, try parsing it.
        if (typeof action.valueMsg === 'string') {
            try {
                const parsed = JSON.parse(action.valueMsg)
                if (isValidValueMsg(parsed)) {
                    return parsed
                }
            } catch (err: unknown) {
                console.log('Json parsing failed for ValueMsg', err)
            }
            // Return null to indicate that we should treat the raw string as plain text.
            return null
        }
        // If valueMsg is already an object, use it if valid.
        if (
            action.valueMsg &&
            typeof action.valueMsg === 'object' &&
            isValidValueMsg(action.valueMsg)
        ) {
            return action.valueMsg
        }
        // For any other case (null, undefined, or an invalid object), return null.
        return null
    }, [action.valueMsg])

    const handleRunAction = useCallback(async () => {
        if (!dateRangeCanRun) return
        console.log(dateRangeParams)
        setShowModal(true)
        setCanCloseModal(false)
        const data = await runTestAction({
            serviceId: appState.id,
            rule: action.name,
            additionalParameters: { ...dateRangeParams },
        })

        // Handle edge case of when we have already run the rule in a different session/tab
        if (data.statusCode === 422) {
            setErrorMessage(data.error ?? 'Action already run. Please refresh.')
            setCanCloseModal(true)
            return
        }

        if (data.statusCode !== 200) {
            setErrorMessage(data.error ?? 'An error occurred')
            setCanCloseModal(true)
            return
        }

        setCanCloseModal(true)

        const updatedActions = appState.testActions.filter(
            (a) => a.name !== action.name
        )

        setAppState((prevState) => ({
            ...appState,
            selectedTab: prevState.selectedTab ?? 'overview',
            testActions: data
                ? [
                      ...updatedActions,
                      {
                          ...data.updatedRule,
                          name: action.name,
                      } as TestActionsModel,
                  ]
                : appState.testActions,
        }))
    }, [
        action.name,
        appState,
        runTestAction,
        setAppState,
        dateRangeParams,
        dateRangeCanRun,
    ])

    const handleViewResult = useCallback(() => {
        setShowModal(true)
    }, [])

    const generateModalContent = () => {
        if (isPending) {
            return <LoadingPanel />
        }
        return (
            <div className={styles.results}>
                {errorMessage ? (
                    <ErrorPanel
                        errorMessage={errorMessage}
                        onRefresh={async () => {
                            await refreshApp()
                        }}
                    />
                ) : (
                    <>
                        <DetailField
                            label="Created On"
                            inline={false}
                            value={formatDate(
                                action.action?.createdOn ?? 'Unknown'
                            )}
                        />
                        <DetailField
                            label="Response Code"
                            inline={false}
                            value={
                                action.action?.status?.toString() ?? 'Unknown'
                            }
                        />
                        {action.action?.response && parsedValueMsg?.renderAsTable ? (
                            <PaginatedTable data={parsedValueMsg.data} />
                        ) : action.action?.response ? (
                            <JsonTable data={action.action.response} />
                        ) : (
                            <Panel>
                                <div
                                    className={styles.testResult}
                                    dangerouslySetInnerHTML={{
                                        __html: DOMPurify.sanitize(
                                            String(action.action?.response ?? '')
                                        ),
                                    }}
                                />
                            </Panel>
                        )}
                    </>
                )}
            </div>
        )
    }

    return (
        <>
            <ActionRow
                action={action}
                handleRunAction={handleRunAction}
                handleViewResult={handleViewResult}
                canModify={canModify(appState.createdBy)}
            />

            {action.userInputs?.map((input, index) => {
                if (input.type === 'daterange-locale') {
                    return (
                        <DateInputActionRow
                            key={index}
                            onDatesChange={(startDate, endDate, canRun) => {
                                const additionalParameters = {
                                    [input.fieldNames[0]]: startDate,
                                    [input.fieldNames[1]]: endDate,
                                }
                                setDateRangeParams(additionalParameters)
                                setDateRangeCanRun(canRun)
                            }}
                        />
                    )
                }
                return null
            })}

            <ModalContent
                variant="Expansive"
                bodyContent={generateModalContent()}
                footerContent={<></>}
                isShowing={showModal}
                title={action.msg}
                developmentUrl="/public/images/able-sprites.svg"
                setHideDialog={() => {
                    if (canCloseModal) setShowModal(false)
                }}
            />
        </>
    )
}
