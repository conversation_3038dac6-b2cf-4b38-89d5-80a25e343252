# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### 7.6.2 (2025-05-28)

### 7.6.1 (2025-05-27)

## 7.6.0 (2025-05-26)


### Features

* **modules/servicecheck.js:** Add 'collectionTime' to source meta data\
  To track source completion times

### 7.5.3 (2025-05-08)


### Bug Fixes

* **user stats module:** User stats showing wrong logged in time
### 7.5.2 (2025-05-08)


### Bug Fixes

* **routes/api/command.js:** Merge showing intrim 502\
  On command run Merge is showing intrim 502 and causing the container to fail and site to go down
please investigate

### 7.5.1 (2025-05-05)


### Bug Fixes

* **routes/api/command.js:** fixing 404 error while trying to fetch QC results in async mode
## 7.5.0 (2025-05-05)


### Features

* **api.json, command.json:** NBN results for SC team\
  Filtered NBN results for SC team

## 7.4.0 (2025-04-22)


### Features

* **api/command.js:** Create async end point for command run
### 7.3.1 (2025-04-03)


### Bug Fixes

* **scrules.json:** Rule MTR007 is referencing PING source which is deprecated\
  Rule MTR007 is referencing PING source which is deprecated. Updating it to use CMI source

## 7.3.0 (2025-03-27)


### Features

* Service check new user interface
## 7.2.0 (2025-01-14)


### Features

* Magpie network diagram for service checks
### 7.1.1 (2025-01-08)


### Bug Fixes

* **scrules.json:** Add r.MTR051.sid assignment to ruleCode
## 7.1.0 (2025-01-07)


### Features

* Add number ranges to sc ribbon. If more than 10, add a + button that displays modal
## 7.0.0 (2024-11-13)


### ⚠ BREAKING CHANGES

* **uuid xdi:** None

### Features

* **config/scrules.json:** Adding SciLo APIs to sources for PAT to consume\
  Adding SciLo APIs to sources for PAT to consume to enable device testing in SciLo instead of MANAT
* **scrules:** Add DNS source and API to fetch DNS details via hangar API

### Bug Fixes

* **alwayson:** AlwaysOn\
  AlwaysOn HTTP 404
* **andig uuid:** ANDIG uuid\
  ANDIG uuid for XDI
* **andig:** andig\
  andig suite
* **config/commands.json:** Validate FNN not working for IP voice\
  Validate FNN is using wrong Commpoilot identifier --IDEN which isn't supported by QC. Adding in
support for --IDEN flag while fetching COMMPILOT through QC
* **flexcab api:** Flexcab API\
  Flexcab API 1 to many
* **flexcab template:** Flexcab Template\
  Flexcab Template Changes
* **ipvoice template:** IPVoice Template\
  IPVoice Template Flexcab
* **ipvoice template:** IPVoice Template\
  IPVoice Delivery Summary Template
* **mdr200 fix:** MDR fix\
  MDR200 fix for CMI set-three commands
* **mdr200:** MDR200\
  Change MDR200 display
* **new template:** New Template\
  IPVoice Delivery Template
* **routes/api/command.js:** Validate FNN not working for IP voice\
  Validate FNN is using wrong Commpoilot identifier --IDEN which isn't supported by QC. Changing that
to — ID
* **uuid xdi:** UUID XDI\
  UUID XDI
* **xdi andig:** XDI ANDig\
  XDI ANDig API
* **xdiandig:** XDIANDig\
  XDIANDig API call fix

## 6.15.0 (2024-09-25)


### Features

* **scrules.json:** Create a new source to obtain the RASS service config info from hangar API\
  feat(apis.json): Create new API to obtain RASS service config info from hangar API


### Bug Fixes

* **xdi for lrd code:** XDI for LRD CODE\
  XDI for LRD code
* **xdi lrd:** XDI LRD\
  XDI LRD WEC

### 6.14.1 (2024-09-20)


### Bug Fixes

* **cmi juniper command set three:** CMI Juniper Command Set Three\
  CMI Juniper Command Set Three
* **feedback comment:** Feedback Comment\
  Positive Feedback Comment
* **mdr200:** MDR200\
  MDR200 for CMI4
* **replace modini with brunofluffy:** replace modini with brunofluffy\
  replace modini with brunofluffy
* **rule mdr199:** Rule MDR199\
  Rule MDR199 change
* **scrules.json:** Create new source and modify MAR039 to run CoS/QoS diagnostics w/ carriageFNN
## 6.14.0 (2024-09-04)


### Features

* Added Geospatial Services Coverage Map page

### Bug Fixes

* **rules:** Rule MOR010\
  Fixing Rule MOR010
* **scrules.json:** Add to CMI interface calls\
  Add commands to CMI interface

## 6.13.0 (2024-08-15)


### Features

* Advanced search for Service Check Records
## 6.12.0 (2024-08-13)


### Features

* **scrules.json:** Adding new command to CMI source\
  Adding new command (getInventory) to CMI source to be used by Fusion robot


### Bug Fixes

* **bandf using msisdn:** BandF sourced using MSISDN\
  Band sourced using MSISDN from CMI commands
* **cmi2:** CMI2 catch\
  Catch CMI2 login failure
* **conen ssp 500:** CONEN SSP 500 Catch\
  Catch AXIS Connen SSP 500
* **edcp:** edcp spurce\
  edcp source parameter fix
* **modini:** modini replacement\
  modini replacement fields
* **modini:** Replace Modini\
  Replace Modini with BrunoFluffy
* **wec double:** WEC double due to input\
  WEC double due to input

## 6.11.0 (2024-07-16)


### Features

* **additional input:** Additiona Input\
  Additional Input for WEC source

## 6.10.0 (2024-07-15)


### Features

* TECS-1385_SolarWinds_Host_Mapping-prod\
  TECS-1385_SolarWinds_Host_Mapping-prod

## 6.9.0 (2024-07-09)


### Features

* Added SHAREDBOSSNTUDEVICE quick command
## 6.8.0 (2024-06-26)


### Features

* TECS-1368_SolarWinds_API-prod\
  TECS-1368_SolarWinds_API-prod

### 6.7.1 (2024-05-31)


### Bug Fixes

* Fix for TID sources
## 6.7.0 (2024-05-31)


### Features

* Added rule to display WAS notes if action is Suspend

### Bug Fixes

* Fixed identification of blank / invalid carriage FNNs from ODIN
### 6.6.1 (2024-05-28)


### Bug Fixes

* Fixed identification of blank / invalid carriage FNNs from ODIN
## 6.6.0 (2024-05-20)


### Features

* **frontend scrules:** Remove Level from Merge\
  Remove Level from Merge and replace with hard coded 0


### Bug Fixes

* Fixed issue with short phone number inputs in ipvoice not running some sources* **rules and sources:** Fix Rule MTR028\
  Change the logic of Rule MTR028

### 6.5.1 (2024-05-15)

## 6.5.0 (2024-05-03)
### Features
* Add WEC source to Merge\
  Add WEC source to Merge for MOBILE

### Bug Fixes
* Optimised page so running multiple service checks will not cause tab to run out of memory

## 6.4.0 (2024-04-29)


### Features

* Removes input symptom feature

### Bug Fixes

* **scrules.json:** Fix Rule MDR106\
  Logic of MDR106 is changed
* **scrules.json:** MDR106 logic Change\
  Rule MDR106 has logic error
* **scrules.json:** Merge not Picking up all the CONEN events\
  The outageInfo Suite not picking up CONEN incidents as incorrect parameter
* **scrules.json:** VET Source Deprication Effect\
  changes to MTR051 due to VET Source Deprication Effect
* Source Whois source is timing out\
  Source Whois source is timing out
* **view / frontend:** image and link in 'easy to extend ' menu item are not pointing to same link\
  Currently there is a feedback planner item in Menu which is not used please remove it. Also the
image and link in 'easy to extend ' menu item are not pointing to same link - fix it

### 6.3.1 (2024-04-17)


### Bug Fixes

* **scrules.json:** Fix Rule MDR106\
  Logic of MDR106 is changed
* **scrules.json:** MDR106 logic Change\
  Rule MDR106 has logic error
* **scrules.json:** Merge not Picking up all the CONEN events\
  The outageInfo Suite not picking up CONEN incidents as incorrect parameter
* Source Whois source is timing out\
  Source Whois source is timing out
* **view / frontend:** image and link in 'easy to extend ' menu item are not pointing to same link\
  Currently there is a feedback planner item in Menu which is not used please remove it. Also the
image and link in 'easy to extend ' menu item are not pointing to same link - fix it

## 6.3.0 (2024-03-26)


### Features

* Add checkbox to show/hide pre-condition failed sources

### Bug Fixes

* MDR049 fix\
  MDR049 fix
* TIPT known issues fix\
  TIPT known issue fix

## 6.2.0 (2024-03-25)


### Features

* **scrules.json:** Add new XDI capability of traversing through a iTAM INC number\
  This change is creating a new Source for traversing through a iTAM INC number


### Bug Fixes

* Fixed issue with parameters to SHAREDBOSS API
### 6.1.1 (2024-03-21)


### Bug Fixes

* Fixed identification of NBN EE carriage type when service type also matches IPMAN
## 6.1.0 (2024-03-18)


### Features

* Added WAS (Internet Direct) API sources for product orders and customer accounts
### 6.0.3 (2024-03-15)


### Bug Fixes

* Fixed MTR063 to identify CONEN tickets correctly
### 6.0.2 (2024-03-13)


### Bug Fixes

* Fixed issue where quick command API causes application crash
### 6.0.1 (2024-03-11)


### Bug Fixes

* Fixed WhoisTelstra source to check whether fieldInterfaceIP is in a Telstra range
## 6.0.0 (2024-03-07)


### ⚠ BREAKING CHANGES

* **security update:** Integration to creating PAT schedule jobs via API will be broken

ESMI-1019

### Bug Fixes

* **config/apis.json routes/api/command.js:** Fix Validate FNN logic\
  fix validate fnn logic so if any of the APIs doesn't return valid response set validate to "false"
currently it sets to "null"

TECS-1281


* **security update:** Update PAT db credentials to be unique to merge\
  As part of security assessment for Merge migration to cloud we have been advised to change the DB
credentials so they are unique to Merge. Currently they are co shared with other app

## 5.5.0 (2024-03-06)


### Features

* **APIs:** Re-enabled Bruno and Fluffy source
### 5.4.4 (2024-03-04)


### Bug Fixes

* Fixed identification of some IPMAN services in service checks
### 5.4.3 (2024-03-03)


### Bug Fixes

* Fixed issue causing sources data to not be loaded in nested service check records
### 5.4.2 (2024-02-15)


### Bug Fixes

* **texttemplates.json:** Fixing listCondition of TextTemplates\
  This fix changes the evaluation of listTemplate condition to primitive datatypes.

TECS-1264

### 5.4.1 (2024-02-02)


### Bug Fixes

* **text templates:** Fixes issue where escaped HTML characters appear in text templates
## 5.4.0 (2024-01-31)


### Features

* Added changelog and changelog page\
  Introduces a changelog into Merge to provide a list of changes to Merge.
