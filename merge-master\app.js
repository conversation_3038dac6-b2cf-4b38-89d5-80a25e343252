'use strict';
//v5 ATK-VM

import cookieParser from 'cookie-parser';
import express from 'express';
import fs from 'fs';
import _ from 'lodash';
import mongoose from 'mongoose';
import passport from 'passport';
import path, { dirname } from 'path';
import { fileURLToPath } from 'url';
import auth from './modules/auth.js';
import expressSession from './modules/expressSession.js';

import debug from 'debug';
import promBundle from 'express-prom-bundle';
import favicon from 'serve-favicon';
import logger, { httpLogger } from './modules/logger.js';

//Swagger for API docs
import bodyParser from 'body-parser';
import createError from 'http-errors';

import adminRoute from './routes/admin.js';
import apiRoute from './routes/api.js';
import callbackRoute from './routes/api/callback.js';
import apiCommandRoute from './routes/api/command.js';
import apiPatRoute from './routes/api/pat.js';
import apiServiceCheckRoute from './routes/api/serviceCheck.js';
import apisRoute from './routes/apis.js';
import authRoute from './routes/auth.js';
import bannerMessageRoute from './routes/bannerMessage.js';
import changelogRoute from './routes/changelog.js';
import commandRoute from './routes/command.js';
import compareResultsRoute from './routes/compareResults.js';
import configRoute from './routes/config.js';
import editRoute from './routes/edit.js';
import geospatialMapRoute from './routes/geospatialMap.js';
import indexRoute from './routes/index.js';
import messageBucketsRoute from './routes/messageBuckets.js';
import outcomesRoute from './routes/outcomes.js';
import productTypesRoute from './routes/productTypes.js';
import rulesRoute from './routes/rules.js';
import ruleStatusRoute from './routes/ruleStatus.js';
import ruleStatusFailedRoute from './routes/ruleStatusFailed.js';
import serviceCheckRoute from './routes/serviceCheck.js';
import sourcesRoute from './routes/sources.js';
import sourceStatusRoute from './routes/sourceStatus.js';
import statsRoute from './routes/stats.js';
import swaggerDocsRoute from './routes/swaggerDocs.js';
import templatesRoute from './routes/templates.js';
import serviceCheckApiRoute from './routes/serviceCheckApi.js';

import { runCronJob } from './modules/apiAuthKeyCron.js';
import { AuthorizationRoles } from './modules/enumerations.js';
import mongooseCollectionWatch from './modules/mongooseCollectionWatch.js';
import './modules/serviceCheckRecordReportCron.js';

import BannerMessage from './db/model/bannerMessage.js';
import sdwanKafkaRead from './modules/sdwanKafkaRead.js';

const debugMsg = debug('merge:app');
const __dirname = dirname(fileURLToPath(import.meta.url));
const app = express();

const isDevelopment = process.env.MERGE_ENV === 'localDev';

app.locals.manifest = null;

if (!isDevelopment) {
    app.use('/clients', express.static(path.join(__dirname, 'clients/serviceCheckApp/dist')));
    const manifestPath = path.join(__dirname, 'clients/serviceCheckApp/dist/.vite/manifest.json');
    try {
        const manifest = JSON.parse(await fs.promises.readFile(manifestPath, 'utf-8'));
        app.locals.manifest = manifest; // Set manifest as a global variable
    } catch (error) {
        logger.error(`Failed to load manifest.json, ${error.toString()}`);
    }
} else {
    app.use((req, res, next) => {
        if (req.url.startsWith('/clients/')) {
            return res.redirect(`http://localhost:5173${req.url}`);
        }
        next();
    });
}

// Dynamic Config Param
global.rootDir = __dirname;


// Create MongoDB connection URI
let dbCredentials = global.gConfig.dbUser ? `${global.gConfig.dbUser}:${global.gConfig.dbPass}@` : '';
let dbReplicaSet = global.gConfig.dbReplicaSet ? `?replicaSet=${global.gConfig.dbReplicaSet}` : '';
let dbUseSsl = global.gConfig.dbUseSsl;


let dbUri = `mongodb://${dbCredentials}${global.gConfig.dbHost}/${global.gConfig.dbName}${dbReplicaSet}`;

// Current known behaviour of mongoose is that if the initall call to connect()
// fails, mongoose will not attempt to establish the connection to MongoDB again,
// but if the initial connection succeeds, but the database is unavailable later,
// it can automatically reconnect to the database
// This function will keep attempting to establish a connection to MongoDB
function connectMongoDb() {
    mongoose.connect(dbUri, {
        ssl: dbUseSsl,
        tlsAllowInvalidCertificates: false,
        tlsCAFile: global.gConfig.dbCaCert ? global.gConfig.dbCaCert : null,
        tlsCertificateKeyFile: global.gConfig.dbClientCert ? global.gConfig.dbClientCert : null
    }).then(() => {
        logger.info('MongoDB: initial connection successful');
    }).catch((error) => {
        logger.error(`MongoDB: initial connection error, ${error.toString()}`);

        // Reattempt initial connection to MongoDB until it is successful
        setTimeout(() => {
            logger.info('MongoDB: reattempting initial connection');
            connectMongoDb();
        }, 10000);
    });
}


// The unit tests connect using mongoose prior to ininitialising this module,
// this prevents a second call to mongoose.connect();
if (mongoose.connection.readyState != 1) {
    connectMongoDb();
}


// Checks if robot account credentials are valid only if it exists
if (global.gConfig.robotAccount01Username && global.gConfig.robotAccount01Password) {
    auth.updateRobotAccountCredentials(global.gConfig.robotAccount01CredentialFile);
}

// Watches robot account-01 credentials from file if it exists
if (global.gConfig.robotAccount01CredentialFile) {
    fs.watchFile(global.gConfig.robotAccount01CredentialFile, (curr, prev) => {
        logger.info(`Account-01 credentials file modified: ${global.gConfig.robotAccount01CredentialFile}`);
        auth.updateRobotAccountCredentials(global.gConfig.robotAccount01CredentialFile);
    });
}


// Include lodash in app local namespace
app.locals._ = _;
app.locals.bannerMessage = null;

app.use(favicon(__dirname + '/public/images/favicon.ico'));
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ limit: '50mb', extended: true, parameterLimit: 50000 }));


// view engine setup
app.set('views', path.join(__dirname, 'views'));
app.set('view engine', 'ejs');
app.enable('trust proxy');

app.use('/public', express.static(path.join(__dirname, 'public')));

app.use(httpLogger);

app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(cookieParser());

debugMsg('#> set Session cookie to domain : "' + global.gConfig.cookieDomain + '" and secure: "' + global.gConfig.cookieSecure + '"');

app.use(expressSession);
app.use(auth.initialize);
app.use(auth.session);
app.use(auth.setUser);

try {
    const prometheusMiddleware = promBundle({
        metricsPath: '/metrics',
        customLabels: {
            uri: 1,
            method: 1
        },
        transformLabels: (labels, req, res) => {
            labels.uri = req.baseUrl + req.path;
            labels.method = req.method;
            labels.status_code = res.statusCode;
        },
        buckets: [
            0.05,
            0.1,
            0.5,
            1,
            5,
            10
        ]
    });

    if (typeof global.gConfig?.prometheusAuth?.username === 'string' &&
        typeof global.gConfig?.prometheusAuth?.password === 'string') {
        app.use('/metrics', passport.authenticate('basic-prometheus', { session: false }));
    }
    app.use(prometheusMiddleware);
} catch (err) {
    if (err.message.indexOf('name http_request_duration_seconds has already been registered') > -1) {
        console.log('Potential error from test case: ', err.message);
    } else {
        throw err;
    }
}

//Middlewares with Mount Point
app.use('/', indexRoute);
app.use('/serviceCheck', serviceCheckRoute);
app.use('/auth', authRoute);
app.use('/api', apiRoute);
app.use('/api/callback', callbackRoute);
app.use('/api/command', auth.authenticateWithJWT(false), auth.authorizeForRoles([
    AuthorizationRoles.apiAccess
], false), apiCommandRoute);
app.use('/api/serviceCheck', auth.authenticateWithJWT(false), auth.authorizeForRoles([
    AuthorizationRoles.apiAccess
], false), apiServiceCheckRoute);
app.use('/api/pat', auth.authenticateWithJWT(false), auth.authorizeForRoles([
    AuthorizationRoles.apiAccess
], false), apiPatRoute);
app.use('/command', commandRoute);
app.use('/rules', rulesRoute);
app.use('/apis', apisRoute);
app.use('/bannerMessage', bannerMessageRoute);
app.use('/sources', sourcesRoute);
app.use('/config', configRoute);
app.use('/edit', editRoute);
app.use('/compareResults', compareResultsRoute);
app.use('/admin', adminRoute);
app.use('/templates', templatesRoute);
app.use('/stats', auth.authenticateWithJWT(true), auth.authorizeForRoles([
    AuthorizationRoles.apiAccess,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), statsRoute);
app.use('/sourceStatus', sourceStatusRoute);
app.use('/ruleStatus', ruleStatusRoute);
app.use('/ruleStatusFailed', ruleStatusFailedRoute);
app.use('/outcomes', outcomesRoute);
app.use('/productTypes', productTypesRoute);
app.use('/messageBuckets', messageBucketsRoute);
app.use('/changelog', changelogRoute);
app.use('/geospatialMap', geospatialMapRoute);
app.use('/serviceCheck/api', serviceCheckApiRoute);
app.use('/', swaggerDocsRoute);


// This middleware handles errors created from the HttpError constructor
app.use(function(error, req, res, next) {
    if (createError.isHttpError(error)) {
        res.status(error.status).send({
            error: error.toString()
        });
    } else {
        next(error);
    }
});


// Middleware to handle all remaining errors
app.use(function(error, req, res, next) {
    // set locals, only providing error in development
    debugMsg('Middleware error handler: ', error);
    logger.error(`Express error handler for URI ${req.originalUrl}, ${error.toString()}`);
    try {
        res.status(500).render('error', { error: error, user: req.user, microsoftTeamsChannelLink: global.gConfig.microsoftTeamsChannelLink });
    } catch(error) {
        logger.error(`Could not render error page, ${error.toString()}`);
        res.sendStatus(500);
    }
});

if (global.gConfig.enableAuthKeyCron) {
    //start cron script
    runCronJob();
}
//back up db
//dbBackUp.runDBCronJob();

// Set up Sdwan Kafka consumer
sdwanKafkaRead.connectConsumer(function(error) {
    if (error) {
        logger.error(`SDWAN Kafka Consumer: error while connecting to kafka broker, ${error.toString()}`);
    } else {
        sdwanKafkaRead.subscribeConsumer();
    }
});


// Sets the banner message on startup
(async function() {
    try {
        let bannerMessage = await BannerMessage.findOne({});
        if (bannerMessage && bannerMessage.message) {
            app.locals.bannerMessage = bannerMessage.message;
        } else {
            app.locals.bannerMessage = null;
        }
    } catch(error) {
        logger.error(`Error setting banner message on startup, ${error.toString()}`);
    }
})();

if (global.gConfig.dbReplicaSet) {
    mongooseCollectionWatch(app);
}


export default app;
