@use '@able/web/src/index' as able;
@keyframes unrollFromTop {
    from {
        clip-path: inset(
            0 0 100% 0
        ); /* Everything hidden except the top edge */
        opacity: 0;
    }
    to {
        clip-path: inset(0 0 0 0); /* Fully revealed */
        opacity: 1;
    }
}

.jsonTable {
    width: 100%;
    border-collapse: collapse;
    border:1px solid  able.color(materialBaseBrandQuaternary);
  
    overflow: hidden;
    animation: unrollFromTop 0.3s ease-out forwards;

    // Alternate row colors in tbody
    tbody {
        tr:nth-of-type(odd) {
            background-color: able.color(materialBaseSecondary);
        }
        tr:nth-of-type(even) {
            background-color: able.color(materialBaseTertiary);
        }
    }
    tr {
        border-bottom: 1px solid able.color(materialBaseBrandQuaternary);
    }
    td:first-child {
        border-right: 1px solid  able.color(materialBaseBrandQuaternary);
    }

    .thead {
        color: able.color(materialBaseBrandOnPrimary);
    }

    .tableHeader {
        text-align: left;
        background-color: able.color(materialBaseTertiary);
        padding: able.spacing(spacing2x);
        border-bottom: 1px solid able.color(materialBaseBrandQuaternary);
        border-right: 1px solid able.color(materialBaseBrandQuaternary);

    }

    .tableHeader:nth-child(1) {
        width: 1px; //hack to get first column to take up space
    }

    .tableCell {
        vertical-align: top;
        padding: able.spacing(spacing2x);
    }

    .expandableLabel {
        background: none;
        border: none;
        padding: 0;
        outline: inherit;
        cursor: pointer;
        user-select: none;
        color: able.color(materialBaseBrandPrimary);
        display: flex;
        align-items: center;
    }

    .expandIcon {
        color: able.color(materialBaseBrandPrimary);
        font-size: x-large;
        margin-left: 0.3em;
    }

    .preformatted {
        white-space: pre-wrap;
        word-wrap: break-word;
        margin: 0;
    }

    .sanitizedText {
        padding: able.spacing(spacing2x);
    }
}
