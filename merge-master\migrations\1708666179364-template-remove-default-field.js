'use strict';

const mongoose = require('mongoose');

const { connect, disconnect } = require('./config');

async function up() {
    await connect();

    await mongoose.connection.db.collection('templates').updateMany({}, {
        $unset: {
            default: 1
        }
    });

    await disconnect();
}


async function down() {
    await connect();

    await mongoose.connection.db.collection('templates').updateMany({}, {
        $unset: {
            defaultPriority: 1
        }
    });

    await disconnect();
}


module.exports = { up, down };
