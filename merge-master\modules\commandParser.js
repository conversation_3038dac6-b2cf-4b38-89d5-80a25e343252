// QCParsing.js
// V1
// =========
// All functions for parsing command and script.
import debug from 'debug';
import minimist from 'minimist';
import shellQuoteParse from './helpers/shellQuote.js'

const debugMsg = debug('merge:commandParser');

const minimistOpts = {
    string : ['f','fnn','t','c','cidn','IDENTIFIER','ID']
}

// Parse a command
function parse(commandArr) {
    var commandParsedArr = minimist(commandArr, minimistOpts);
    
    if (!commandParsedArr._[0]) {
        debugMsg('Cannot find command name in the begining of command!');
    }
    commandParsedArr.name = commandParsedArr._[0].toUpperCase().trim() ; //first word with no option is the command

    debugMsg('Command processed :', commandParsedArr);
    return commandParsedArr;
}

// To parse single command and script line by line
export function parseCommand(command) {
    debugMsg('Parse command : ', command);

    let commandToArg = shellQuoteParse(command);

    let commandParsed = parse(commandToArg);

    return commandParsed;
}

//TODO parse commands and script line by line
/*
function parseCommands(commands) {
    var commandsArr = commands.split(';');
    var commandsParsed =[];
    debugMsg('CommandsArray:', commandsArr);

    for (let command of commandsArr){
        let commandArr = command.trim().split(" ");
        let commandParsed = parse(commandArr);
        commandsParsed.push(commandParsed);
    }
    return commandsParsed;
}

//TODO To parse Script and script line by line
function parseScript(commands) {
    var commandsArr = commands.split(';');
    var commandsParsed =[];
    debugMsg('CommandsArray:', commandsArr);

    for (let command of commandsArr){
        let commandArr = command.trim().split(" ");
        let commandParsed = parse(commandArr);
        commandsParsed.push(commandParsed);
    }
    return commandsParsed;
}
*/