<%- include('header', { title: 'Rule / Sources graph status' }); %>
<%- include('menu'); %>
<div class="container">
    <br>
    <div class="border border-secondary container rounded" style="height:90vh;overflow-y:auto;padding-bottom:16px;">
        <br>
        <h1 class="display-4">Rule / Sources Graph Status</h1>
        <hr>
        <div class="row">
            <div class="col-10">
                <h3 class="display-5">Acyclic</h3>
            </div>
            <div class="col-2">
                <div class="float-right">
                    <button class="btn btn-primary" title="Refresh" id="graphStatusIsAcyclicRefresh"><span class="fas fa-sync"></span></button>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div id="graphStatusAcyclic"></div>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-10">
                <h3 class="display-5">Depends on disable display condition rule <%- global.gConfig.disableDisplayConditionRuleName %></h3>
            </div>
            <div class="col-2">
                <div class="float-right">
                    <button class="btn btn-primary" title="Refresh" id="graphStatusDisableDisplayConditionRuleRefresh"><span class="fas fa-sync"></span></button>
                </div>
            </div>
        </div>
        <div id="graphDisableDisplayConditionRuleStatusError" class="alert alert-danger" style="display:none;">Error retrieving graph disable display condition rule dependency status</div>
        <div class="row">
            <div class="col-6">
                <p class="d-inline">Independent of rule</p>
                <span style="color:blue" class="fas fa-question-circle" data-toggle="tooltip" data-placement="top" title="Rules and sources in this list are not guaranteed to run after the rule to disable displaying a service check is run"></span>
            </div>
            <div class="col-6">
                <p class="d-inline">Dependent on rule</p>
                <span style="color:blue" class="fas fa-question-circle" data-toggle="tooltip" data-placement="top" title="Rules and sources in this list will run after a rule to disable displaying a service check is run"></span>
            </div>
        </div>
        <div class="row">
            <div class="col-6">
                <ul id="graphStatusDisableDisplayConditionRuleIndependent" class="list-group"></ul>
            </div>
            <div class="col-6">
                <ul id="graphStatusDisableDisplayConditionRuleDependent" class="list-group"></ul>
            </div>
        </div>
    </div>
</div>
<script>
$(document).ready(async function() {
    const ACYCLIC_STATUS_IS_ACYCLIC_HTML = `
    <div class="alert alert-success">
        Graph is acyclic
        <span style="color:blue" class="fas fa-question-circle" data-toggle="tooltip" data-placement="top" title="The rule / source graph is acyclic and does not contain any circular dependencies, all rules / sources can potentially be run."></span>
    </div>`;

    const ACYCLIC_STATUS_IS_NOT_ACYCLIC_HTML = `
    <div class="alert alert-warning">
        Graph is not acyclic
        <span style="color:blue" class="fas fa-question-circle" data-toggle="tooltip" data-placement="top" title="The rule / source graph is not acyclic and contains circular dependencies, some rules / sources cannot be run."></span>
    </div>`;

    const ACYCLIC_STATUS_ERROR_HTML = `
    <div class="alert alert-danger">
        Error retrieving graph acyclic status
    </div>`;

    async function loadGraphAcyclicStatus() {
        setButtonSpinner($('#graphStatusIsAcyclicRefresh'), $('<span>').addClass('fas fa-sync'), true);
        $('#graphStatusAcyclic').empty();

        try {
            let response = await $.ajax({
                type: 'GET',
                url: '/config/graphIsAcyclic',
                contentType: "application/json",
                timeout: 60000,
                cache: false
            });

            if (response.isAcyclic) {
                $('#graphStatusAcyclic').html(ACYCLIC_STATUS_IS_ACYCLIC_HTML);
            } else {
                $('#graphStatusAcyclic').html(ACYCLIC_STATUS_IS_NOT_ACYCLIC_HTML);
            }
        } catch(error) {
            $('#graphStatusAcyclic').html(ACYCLIC_STATUS_ERROR_HTML);
        } finally {
            setButtonSpinner($('#graphStatusIsAcyclicRefresh'), $('<span>').addClass('fas fa-sync'), false);
        }
    }

    async function loadGraphDisableDisplayConditionRuleStatus() {
        setButtonSpinner($('#graphStatusDisableDisplayConditionRuleRefresh'), $('<span>').addClass('fas fa-sync'), true);
        $('#graphStatusDisableDisplayConditionRuleIndependent').empty();
        $('#graphStatusDisableDisplayConditionRuleDependent').empty();
        $('#graphDisableDisplayConditionRuleStatusError').hide();

        try {
            let response = await $.ajax({
                type: 'GET',
                url: '/config/graphDisableDisplayConditionRuleStatus',
                contentType: "application/json",
                timeout: 60000,
                cache: false
            });

            if (Array.isArray(response?.rules)) {
                for (let rule of response.rules) {
                    let listElement = rule.isDependent ? $('#graphStatusDisableDisplayConditionRuleDependent') : $('#graphStatusDisableDisplayConditionRuleIndependent');

                    listElement.append(
                        $('<li>').addClass('list-group-item').append(
                            $('<a>').attr('href', `/edit/rules/${encodeURIComponent(rule.name)}`).attr('target', 'blank').text(rule.name)
                        ).append(
                            $('<div>').addClass('float-right').html(
                                $('<div>').addClass('badge badge-primary').text('Rule')
                            )
                        )
                    );
                }
            }

            if (Array.isArray(response?.sources)) {
                for (let source of response.sources) {
                    let listElement = source.isDependent ? $('#graphStatusDisableDisplayConditionRuleDependent') : $('#graphStatusDisableDisplayConditionRuleIndependent');

                    listElement.append(
                        $('<li>').addClass('list-group-item').append(
                            $('<a>').attr('href', `/edit/sources/${encodeURIComponent(source.name)}`).attr('target', 'blank').text(source.name)
                        ).append(
                            $('<div>').addClass('float-right').html(
                                $('<div>').addClass('badge badge-success').text('Source')
                            )
                        )
                    );
                }
            }
        } catch(error) {
            $('#graphDisableDisplayConditionRuleStatusError').show();
        } finally {
            setButtonSpinner($('#graphStatusDisableDisplayConditionRuleRefresh'), $('<span>').addClass('fas fa-sync'), false);
        }
    }

    loadGraphAcyclicStatus();
    loadGraphDisableDisplayConditionRuleStatus();

    $('#graphStatusIsAcyclicRefresh').click(function() {
        loadGraphAcyclicStatus();
    });

    $('#graphStatusDisableDisplayConditionRuleRefresh').click(function() {
        loadGraphDisableDisplayConditionRuleStatus();
    });
});
</script>
<%- include('footer', {}); %>
