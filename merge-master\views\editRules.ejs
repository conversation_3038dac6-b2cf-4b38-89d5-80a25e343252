<%- include('header') %>
<%- include('jsonEditInclude', {}) %>
<%- include('serviceCheckRuleHelpers'); %>
<%- include('menu', {currentTab: 'Form'}); %>
<link href="/public/stylesheets/ace-diff.min.css" rel="stylesheet">
<link rel="stylesheet" type="text/css" href="/public/stylesheets/select2.min.css">
<script src="/socket.io/socket.io.js" crossorigin="anonymous"></script>
<script src="/public/javascripts/ace-diff.min.js" crossorigin="anonymous"></script>
<script src="/public/javascripts/select2.min.js" crossorigin="anonymous"></script>
<script src="/public/javascripts/merge_SC_lib.js" crossorigin="anonymous"></script>
<br>
<div class="container">
    <div class="row">
        <div class="col-8">
            <a href="/edit/rules">< Back to rules</a>
        </div>
        <div class="col-4">
            <div class="float-right">
                <button class="btn btn-primary" id="createRule" data-toggle="modal" data-target="#confirmCreateModal" style="display:none;" title="Create" disabled><span class="fas fa-plus-square"></span></button>
                <button class="btn btn-primary" id="saveRule" data-toggle="modal" data-target="#confirmSaveModal" style="display:none;" title="Save" disabled><span class="fas fa-save"></span></button>
                <button class="btn btn-primary" id="refreshRule" data-toggle="modal" data-target="#confirmRefreshModal"  style="display:none;" title="Refresh"><span class="fas fa-sync"></span></button>
                <button class="btn btn-danger" id="deleteRule" data-toggle="modal" data-target="#confirmDeleteModal" style="display:none;" title="Delete" disabled><span class="fas fa-trash"></span></button>
            </div>
        </div>
    </div>
    <br>
    <div class="row">
        <div class="col-12">
            <div id="readonlyAlert" style="display:none;" class="alert alert-warning">Warning: Rule is read only, editing is disabled.</div>
            <div id="requiresReviewAlert" style="display:none;" class="alert alert-danger"><span class="fas fa-info-circle" style="color:blue"></span> This rule requires review from developers</div>
        </div>
    </div>

    <ul class="nav nav-tabs" role="tablist">
        <li role="presentation" class="nav-item active">
            <a class="nav-link active" data-target="#editRulesTab" data-toggle="tab" role="tab" href="">Edit Rule</a>
        </li>
        <li role="presentation" class="nav-item">
            <a class="nav-link" data-target="#developRuleTab" data-toggle="tab" role="tab" href="">Develop Rule</a>
        </li>
        <li role="presentation" class="nav-item">
            <a class="nav-link" data-target="#runUnitTestsTab" data-toggle="tab" role="tab" href="">Run Unit Tests</a>
        </li>
    </ul>

    <div class="tab-content">
        <div class="tab-pane active border" id="editRulesTab" style="height: 75vh;overflow-y: auto;padding-bottom: 16px;">
            <div style="padding: 2rem;">
                <div id="rulesLoadError" class="alert alert-danger" role="alert" style="display: none;padding-left: 16px;"></div>
                <div id="rulesEdit" style="display: none;"></div>
            </div>
        </div>
        <div class="tab-pane border bg-white" id="developRuleTab" style="height:75vh;overflow-y:auto;padding-bottom:16px;">
            <div style="padding: 2rem;">
                <div class="row border border-primary rounded bg-light">
                    <div class="card-body">
                        <div id="developDiffStatus" class="alert"></div>
                        <div class="form-row">
                            <button class="btn btn-outline-primary ml-1 mr-1" id="developSaveCode" title="Save code to &quot;Edit Rule&quot; tab"><span class="fas fa-save"></span></button>
                            <button class="btn btn-outline-primary ml-1 mr-1" id="developRefreshCode" title="Reset code to values in &quot;Edit Rule&quot; tab"><span class="fas fa-undo"></span></button>
                            <label id="developSaveTime" class="col-form-label"></label>
                        </div>
                        <div class="form-group">
                            <label class="col-form-label">Automatic Save</label>
                            <div class="form-check form-check-inline ml-2 mr-2">
                                <input class="form-check-input" type="radio" name="saveOptionRadios" id="saveOptionDisabled" value="disabled">
                                <label class="form-check-label" for="saveOptionDisabled">
                                    Disabled
                                </label>
                            </div>
                            <div class="form-check form-check-inline ml-2 mr-2">
                                <input class="form-check-input" type="radio" name="saveOptionRadios" id="saveOptionInterval" value="interval">
                                <label class="form-check-label mr-1" for="saveOptionInterval">
                                    Save every
                                </label>
                                <select id="saveOptionIntervalLength" style="width: 4em;">
                                    <option value=10 selected>10</option>
                                    <option value=30>30</option>
                                    <option value=60>60</option>
                                    <option value=300>300</option>
                                    <option value=600>600</option>
                                </select>
                                <label class="form-check-label ml-1" for="saveOptionInterval">
                                    seconds
                                </label>
                            </div>
                        </div>
                        <label class="col-form-label">Editor options:</label>
                        <div class="form-row">
                            <div class="ml-2 mr-2">
                                <input id="editorOptionWordWrap" type="checkbox">
                                <label class="col-form-label" for="editorOptionWordWrap">Word Wrap</label>
                            </div>
                            <div class="ml-2 mr-2">
                                <label class="col-form-label" for="editorOptionFontSize">Font Size</label>
                                <select id="editorOptionFontSize">
                                    <option value="8">8</option>
                                    <option value="10" selected>10</option>
                                    <option value="12">12</option>
                                    <option value="14">14</option>
                                    <option value="16">16</option>
                                </select>
                            </div>
                            <div class="ml-2 mr-2">
                                <label class="col-form-label" for="editorOptionTheme">Theme</label>
                                <select id="editorOptionTheme">
                                    <optgroup label="Light">
                                        <option value="ace/theme/textmate" selected>Default (Textmate)</option>
                                        <option value="ace/theme/eclipse">Eclipse</option>
                                        <option value="ace/theme/github">Github</option>
                                        <option value="ace/theme/xcode">Xcode</option>
                                    </optgroup>
                                    <optgroup label="Dark">
                                        <option value="ace/theme/cobalt">Cobalt</option>
                                        <option value="ace/theme/monokai">Monokai</option>
                                        <option value="ace/theme/tomorrow_night">Tomorrow Night</option>
                                        <option value="ace/theme/twilight">Twilight</option>
                                    </optgroup>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <br>
                <ul class="nav nav-pills bg-white">
                    <li class="nav-item">
                        <a class="nav-link active" data-target="#executeRulesTab" data-toggle="tab" role="tab" href="">Execute Rule</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-target="#compareRulesTab" data-toggle="tab" role="tab" href="">Compare Rule</a>
                    </li>
                </ul>
                <br>
                <div class="tab-content">
                    <div class="tab-pane active" id="executeRulesTab">
                        <div class="row">
                            <div class="col-md-6 border rounded codeBackground" style="padding:8px;">
                                <label for="developRuleVariables" class="codeLabel">Input Variables</label>
                                <button id="resetInputVariables" class="btn btn-primary btn-sm" title="Reset"><span class="fas fa-undo"></span></button>
                                <small class="form-text codeLabel">If r or s are undefined / null / empty objects, the <b>rulesData</b> and <b>sourcesData</b> field will be used instead.</small>
                                <div class="alert alert-warning" role="alert" id="ruleVariablesSizeAlert" style="display:none;">
                                    <small>The input variables text exceeds 8MB. This may cause errors when running precondtion / rule code.</small>
                                </div>
                                <div id="developRuleVariables" style="height: 100%; width: 100%;"></div>
                            </div>
                            <div class="col-md-6 border rounded codeBackground" style="padding:8px;">
                                <label for="outputVariables" class="codeLabel">Output Data</label>
                                <div id="outputVariables" style="height: 100%; width: 100%;"></div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 border rounded codeBackground" style="padding:8px;">
                                <label for="developRulePreCondition" class="codeLabel">Precondition Code</label>
                                <button id="runRulePreCondition" class="btn btn-success btn-sm">Run</button>
                                <small class="form-text codeLabel">Expression that when evaluated as <b>true</b> in a Boolean context, will result in the rule being run. If the expression is <b>false</b> in a Boolean context, such as a null or undefined value, the main rule code will not run.</b></small>
                                <div class="alert p-1" role="alert" id="rulePreConditionResult" style="display:none;">
                                    <small id="rulePreConditionResultText"></small>
                                </div>
                                <div id="developRulePreCondition" style="height: 100%; width: 100%;"></div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 border rounded codeBackground" style="padding:8px;">
                                <label for="developRuleCode" class="codeLabel">Main Rule Code</label>
                                <button id="runRuleCode" class="btn btn-success btn-sm">Run</button>
                                <small class="form-text codeLabel">The last variable declared in this code will be the output and if it is <b>true</b> in a boolean context, the rule will have result "OK", otherwise a <b>false</b> output will have a "Failed" or "Warning" result depending on the rule configuration.</small>
                                <div class="alert alert-danger" role="alert" id="ruleRunAlert" style="display:none;">
                                    <small>An error occurred while running the rule, please check the console at the bottom of the page for details</small>
                                </div>
                                <div id="developRuleCode" style="height: 100%; width: 100%;"></div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 border rounded codeBackground" style="padding:8px;">
                                <label for="developValueMessageCode" class="codeLabel">Value Message Code</label>
                                <small class="form-text codeLabel">Expression that should resolve to a <b>string</b> that will be placed in the "Details" column for the rule.</small>
                                <div id="developValueMessageCode" style="height: 100%; width: 100%;"></div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 border rounded codeBackground" style="padding:8px;">
                                <label for="developExtraInformationCode" class="codeLabel">Extra Information Code</label>
                                <small class="form-text codeLabel">Expression that should resolve to a <b>string</b> that be placed in a modal, activated with a button, in the "Extra Info" column for the rule.</small>
                                <div id="developExtraInformationCode" style="height: 100%; width: 100%;"></div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 border rounded bg-light" style="padding:8px;">
                                <label for="Results" class="text-dark">Rules Output</label>
                                <br>
                                <small id="rulesOutputUpdatedAt" class="text-muted"></small>
                                <div id="Results"></div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 border rounded bg-light" style="padding:8px;">
                                <label for="developOutputConsole" class="text-dark">Console</label>
                                <div id="developOutputConsole" style="height: 100%; width: 100%; max-height: 2160px; resize: vertical; overflow: hidden;"></div>
                            </div>
                        </div>
                    </div>

                    <div class="tab-pane" id="compareRulesTab">
                        <div class="row">
                            <div class="col-md-12 border rounded codeBackground" style="padding:8px;">
                                <label for="ruleOverrideCompare" class="codeLabel">Override Compare Code</label>
                                <button id="runRuleOverrideCompare" class="btn btn-success btn-sm">Run</button>
                                <small class="form-text codeLabel">Code that should define a <b>function</b> called <b>compare</b> with 2 parameters, with each parameter representing the rule data for a service check record. The function should return a Boolean value where <b>true</b> means the rule data between both service check records is considered the same and won't be counted as a difference in Compare Rules.</small>
                                <div class="alert p-1" role="alert" id="ruleOverrideCompareResult" style="display:none;">
                                    <small id="ruleOverrideCompareResultText"></small>
                                </div>
                                <div id="ruleOverrideCompare" style="height: 100%; width: 100%;"></div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 border rounded codeBackground" style="padding:8px;">
                                <label for="compareRuleData1" class="codeLabel">Rule Data 1</label>
                                <button id="resetRuleData1" class="btn btn-primary btn-sm" title="Reset"><span class="fas fa-undo"></span></button>
                                <div class="alert alert-warning" role="alert" id="ruleData1SizeAlert" style="display:none;">
                                    <small>The combined input rule data exceeds 8MB. This may cause errors when running precondtion / rule code.</small>
                                </div>
                                <div id="compareRuleData1" style="height: 100%; width: 100%;"></div>
                            </div>
                            <div class="col-md-6 border rounded codeBackground" style="padding:8px;">
                                <label for="compareRuleData2" class="codeLabel">Rule Data 2</label>
                                <button id="resetRuleData2" class="btn btn-primary btn-sm" title="Reset"><span class="fas fa-undo"></span></button>
                                <div class="alert alert-warning" role="alert" id="ruleData2SizeAlert" style="display:none;">
                                    <small>The combined input rule data exceeds 8MB. This may cause errors when running precondtion / rule code.</small>
                                </div>
                                <div id="compareRuleData2" style="height: 100%; width: 100%;"></div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 border rounded bg-light" style="padding:8px;">
                                <label for="compareOutputConsole" class="text-dark">Console</label>
                                <div id="compareOutputConsole" style="height: 100%; width: 100%; max-height: 2160px; resize: vertical; overflow: hidden;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-pane border" id="runUnitTestsTab" style="height:75vh;overflow-y:auto;padding-bottom:16px;">
            <div style="padding: 2rem;">
                <div class="row">
                    <div class="col-12 border rounded" style="padding:8px;">
                        <p>
                            Run unit tests for rule. Configure unit tests in the "Edit Rule" tab.
                            Rules run from this tab will be based on the data entered in the "Edit Rule" tab, not from the stored rule config.
                        </p>
                        <div class="float-right">
                            <button class="btn btn-info" id="runUnitTests">Run Unit Tests</button>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 border rounded" style="padding:8px;">
                        <label>Unit Test Results</label>
                        <div class="alert alert-info" role="alert" id="unitTestAlert" style="display:none;">
                            <small>No unit test results returned, this rule does not have any valid unit tests configured.</small>
                        </div>
                        <br>
                        <small id="unitTestResultsUpdatedAt" class="text-muted"></small>
                        <table class="table">
                            <thead class="thead-light">
                                <tr>
                                    <th style="width:20%;" scope="col">Name</th>
                                    <th style="width:10%;" scope="col">Status</th>
                                    <th style="width:70%;" scope="col">Result</th>
                                </tr>
                            </thead>
                            <tbody id="unitTestResults">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="createRuleModal" class="modal fade">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Error</h5>
            </div>
            <div class="modal-body">
                <code id="createRuleModalMessage" style="white-space:pre-wrap;"></code>
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-outline-secondary">Close</button>
            </div>
        </div>
    </div>
</div>

<div id="confirmSaveModal" class="modal fade">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Save</h5>
            </div>
            <div class="modal-body">
                Would you like to save the changes for this rule?
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-primary" id="confirmSave">Save</button>
                <button type="button" data-dismiss="modal" class="btn btn-outline-secondary">Cancel</button>
            </div>
        </div>
    </div>
</div>

<div id="confirmRefreshModal" class="modal fade">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Refresh</h5>
            </div>
            <div class="modal-body">
                Would you like to refresh the rule contents? Any changes made will be lost.
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-primary" id="confirmRefresh">Refresh</button>
                <button type="button" data-dismiss="modal" class="btn btn-outline-secondary">Cancel</button>
            </div>
        </div>
    </div>
</div>

<div id="confirmDeleteModal" class="modal fade">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
            </div>
            <div class="modal-body">
                Would you like to delete this rule? This action cannot be reversed.
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-danger" id="confirmDelete">Delete</button>
                <button type="button" data-dismiss="modal" class="btn btn-outline-secondary">Cancel</button>
            </div>
        </div>
    </div>
</div>

<div id="unitTestModal" class="modal fade" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-xlg" role="document">
        <div class="modal-content" style="height:96vh;">
            <div class="modal-header">
                <h5 class="modal-title">Record comparison</h5>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <p>Output record</p>
                    </div>
                    <div class="col-md-6">
                        <p>Expected record</p>
                    </div>
                </div>
                <div class="row" style="height:96%;">
                    <div class="col-md-12">
                        <div id="unitTestRecordComparison"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-outline-secondary">Close</button>
            </div>
        </div>
    </div>
</div>


<script>
    const ruleName = atob("<%- ruleName %>");
    const wikiBaseURL = "<%= global.gConfig.wikiBaseURL %>";
    const createRule = <%= createRule %>;
    const disableRuleSourceEditing = <%- user.isAdmin ? false : global.gConfig.disableRuleSourceEditing %>;
    const socket = io();
    const schema = JSON.parse(atob("<%- schema %>"));

    const INPUT_VARIABLES_DEFAULT = "r = {};\ns = {};\ndata = {};\nrelatedServiceChecks = null;";
    const RULE_DATA_DEFAULT = JSON.stringify({
        extraInfo: null,
        msg: "",
        rootCauseCategory: "",
        result: "OK",
        status: "done",
        TrgSource: "SourceName",
        type: "",
        valueMsg: ""
    }, null, 4);

    var dataLoaded = false;
    var dataModified = false;
    var autosaveInterval = null;

    $(document).ready(function() {
        $("#createRule").prop("disabled", disableRuleSourceEditing);
        $("#saveRule").prop("disabled", disableRuleSourceEditing);
        $("#deleteRule").prop("disabled", disableRuleSourceEditing);
        $("#developSaveCode").prop("disabled", disableRuleSourceEditing);

        let jsonEditor = new JSONEditor(document.getElementById('rulesEdit'), {
            ajax: false,
            enable_array_copy: false,
            schema: schema,
            startval: null,
            no_additional_properties: true,
            required_by_default: true,
            show_errors: "always"
        });

        let preConditionTimeout;
        let ruleCodeTimeout;
        let compareResultTimeout;

        if (disableRuleSourceEditing) {
            jsonEditor.disable();
            $("input[name='saveOptionRadios']").attr("disabled", true);
            $("#readonlyAlert").show();
        }

        if (createRule) {
            $("#createRule").show();
            $("#rulesEdit").show();
        } else {
            $("#saveRule").show();
            $("#refreshRule").show();
            $("#deleteRule").show();
            getRuleToForm(jsonEditor);
        }

        const aceEditors = setupDevelopmentEditors();

        const codeEditors = Object.freeze({
            preCondition: aceEditors.rulePreCondition,
            ruleCode: aceEditors.ruleCode,
            valueMsgStm: aceEditors.ruleValueMessageCode,
            extraInfo: aceEditors.ruleExtraInformationCode,
            overrideCompare: aceEditors.ruleOverrideCompare
        });

        $("#createRule").click(function() {
            createRuleFromForm(jsonEditor);
        });

        $("#confirmSave").click(function() {
            writeRuleFromForm(jsonEditor);
        });

        $("#confirmRefresh").click(function() {
            getRuleToForm(jsonEditor);
        });

        $("#confirmDelete").click(function() {
            $.ajax({
                type: "DELETE",
                url: `/rules/${encodeURIComponent(ruleName)}`,
                success: function (response) {
                    window.location.href = "/edit/rules";
                },
                error: function (error) {
                    alert("Error, could not delete rule:" + error.responseText);
                },
            });
        });

        $("#developSaveCode").click(function() {
            jsonEditor.getEditor('root.preCondition').setValue(codeEditors.preCondition.getValue());
            jsonEditor.getEditor('root.ruleCode').setValue(codeEditors.ruleCode.getValue());
            jsonEditor.getEditor('root.valueMsgStm').setValue(codeEditors.valueMsgStm.getValue());
            jsonEditor.getEditor('root.extraInfo').setValue(codeEditors.extraInfo.getValue());
            jsonEditor.getEditor('root.overrideCompare').setValue(codeEditors.overrideCompare.getValue());

            updateCodeDiffStatus(jsonEditor, codeEditors);
            updateSaveTimeLabel();
        });

        $("#developRefreshCode").click(function() {
            let rule = jsonEditor.getValue();
            codeEditors.preCondition.setValue(rule.preCondition);
            codeEditors.preCondition.clearSelection();
            codeEditors.ruleCode.setValue(rule.ruleCode);
            codeEditors.ruleCode.clearSelection();
            codeEditors.valueMsgStm.setValue(rule.valueMsgStm);
            codeEditors.valueMsgStm.clearSelection();
            codeEditors.extraInfo.setValue(rule.extraInfo);
            codeEditors.extraInfo.clearSelection();
            codeEditors.overrideCompare.setValue(rule.overrideCompare);
            codeEditors.overrideCompare.clearSelection();

            updateCodeDiffStatus(jsonEditor, codeEditors);
        });

        $("#resetInputVariables").click(function() {
            aceEditors.ruleVariables.setValue(INPUT_VARIABLES_DEFAULT);
            aceEditors.ruleVariables.clearSelection();
        });

        $("#resetRuleData1").click(function() {
            aceEditors.compareRuleData1.setValue(RULE_DATA_DEFAULT);
            aceEditors.compareRuleData1.clearSelection();
        });

        $("#resetRuleData2").click(function() {
            aceEditors.compareRuleData2.setValue(RULE_DATA_DEFAULT);
            aceEditors.compareRuleData2.clearSelection();
        });

        $("#saveOptionIntervalLength").select2({
            minimumResultsForSearch: -1,
            width: "resolve"
        });

        $("#editorOptionFontSize").select2({
            minimumResultsForSearch: -1,
            width: "resolve"
        });

        $("#editorOptionTheme").select2({
            minimumResultsForSearch: -1,
            width: "16em"
        });

        $("input[name='saveOptionRadios']").change(function() {
            localStorage.setItem("ruleDevelopAutosaveOption", this.value);
            setupSaveInterval(this.value, $("#saveOptionIntervalLength").val());
        });

        $("#editorOptionWordWrap").change(function() {
            localStorage.setItem("ruleDevelopEditorWordWrap", this.checked);

            for (var editorName in aceEditors) {
                aceEditors[editorName].setOption("wrap", this.checked);
            }
        });

        $("#saveOptionIntervalLength").on('select2:select', function (e) {
            localStorage.setItem("ruleDevelopAutosaveInterval", this.value);
            setupSaveInterval($("input[name='saveOptionRadios']:checked").val(), this.value);
        });

        $("#editorOptionFontSize").on('select2:select', function (e) {
            let fontSize = $("#editorOptionFontSize").val();
            localStorage.setItem("ruleDevelopEditorFontSize", fontSize);

            for (var editorName in aceEditors) {
                if (!["developOutputConsole", "compareOutputConsole"].includes(editorName)) {
                    aceEditors[editorName].setOption("fontSize", `${fontSize}pt`);
                }
            }
        });

        $("#editorOptionTheme").on('select2:select', function (e) {
            let theme = $("#editorOptionTheme").val();
            let themeType = $("#editorOptionTheme :selected").parent("optgroup").attr("label");

            localStorage.setItem("ruleDevelopEditorTheme", theme);

            for (var editorName in aceEditors) {
                if (!["developOutputConsole", "compareOutputConsole"].includes(editorName)) {
                    aceEditors[editorName].setOption("theme", theme);
                }
            }

            switch (themeType) {
                case "Light":
                    $(".codeBackground").addClass("bg-light");
                    $(".codeLabel").addClass("text-dark");

                    $(".codeBackground").removeClass("bg-dark");
                    $(".codeLabel").removeClass("text-light");
                    break;
                case "Dark":
                    $(".codeBackground").addClass("bg-dark");
                    $(".codeLabel").addClass("text-light");

                    $(".codeBackground").removeClass("bg-light");
                    $(".codeLabel").removeClass("text-dark");
                    break;
            }
        });

        // Loads editor options from local storage and sets UI elements to correct configuration
        let autosaveOption = localStorage.getItem("ruleDevelopAutosaveOption") ? localStorage.getItem("ruleDevelopAutosaveOption") : "disabled";
        let autosaveInterval = localStorage.getItem("ruleDevelopAutosaveInterval") ? localStorage.getItem("ruleDevelopAutosaveInterval") : "10";
        let editorOptionFontSize = localStorage.getItem("ruleDevelopEditorFontSize") ?  localStorage.getItem("ruleDevelopEditorFontSize") : "10";
        let editorOptionTheme = localStorage.getItem("ruleDevelopEditorTheme") ? localStorage.getItem("ruleDevelopEditorTheme") : "ace/theme/textmate";
        let editorOptionWordWrap = localStorage.getItem("ruleDevelopEditorWordWrap") ? localStorage.getItem("ruleDevelopEditorWordWrap") : false;

        $("input[name='saveOptionRadios']").filter(function() {
            return $(this).val() == autosaveOption;
        }).attr("checked", true);

        $("#saveOptionIntervalLength").val(autosaveInterval);
        $("#saveOptionIntervalLength").trigger("change");
        $("#saveOptionIntervalLength").trigger("select2:select");

        $("#editorOptionWordWrap").prop("checked", editorOptionWordWrap);

        $("#editorOptionFontSize").val(editorOptionFontSize);
        $("#editorOptionFontSize").trigger("change");
        $("#editorOptionFontSize").trigger("select2:select");

        $("#editorOptionTheme").val(editorOptionTheme);
        $("#editorOptionTheme").trigger("change");
        $("#editorOptionTheme").trigger("select2:select");

        socket.on('ruleDev:preConditionResult', (data) => {
            clearTimeout(preConditionTimeout);
            setButtonSpinner($("#runRulePreCondition"), "Run", false);

            if (data.error) {
                $("#rulePreConditionResultText").text(data.error);
                $("#rulePreConditionResultText").removeClass("font-italic");
                $("#rulePreConditionResult").addClass("alert-danger");
                $("#rulePreConditionResult").removeClass("alert-primary");
                $("#rulePreConditionResult").show();
                return;
            }

            if (data.variables) {
                aceEditors.outputVariables.setValue(`${JSON.stringify(data.variables.data, null, 4)}`);
                aceEditors.outputVariables.clearSelection();
            }

            if (data.preConditionIsUndefined) {
                $("#rulePreConditionResultText").text("undefined");
                $("#rulePreConditionResultText").addClass("font-italic");
            } else {
                $("#rulePreConditionResultText").text(data.preConditionResult);
                $("#rulePreConditionResultText").removeClass("font-italic");
            }

            $("#rulePreConditionResult").addClass("alert-primary");
            $("#rulePreConditionResult").removeClass("alert-danger");
            $("#rulePreConditionResult").show();

            editorAppendText(aceEditors.developOutputConsole, data.message);
        });

        socket.on('ruleDev:runResult', (data) => {
            clearTimeout(ruleCodeTimeout);
            setButtonSpinner($("#runRuleCode"), "Run", false);

            editorAppendText(aceEditors.developOutputConsole, data.message);
            if (data.error) {
                $("#ruleRunAlert").show();
                editorAppendText(aceEditors.developOutputConsole, data.error);
            }

            if (data.variables) {
                let currDate = new Date().toLocaleString("en-GB");
                $("#rulesOutputUpdatedAt").text(`Last updated at: ${currDate}`);
                aceEditors.outputVariables.setValue(`${JSON.stringify(data.variables.data, null, 4)}`);
                aceEditors.outputVariables.clearSelection();

                // Clears the Results div, inits service check elements and adds the rule result
                $("#Results").html(null);
                initNewServiceCheck("ruleSandbox", "", data.variables.data.rulesData);
                $("#status-ruleSandbox").text("");

                ruleCheckMsg("ruleSandbox", jsonEditor.getValue(), data.variables.data.rulesData[ruleName]);
                $("#ruleSandbox-status").text("(done)");
                $("#pills-dicon-ruleSandbox").hide();
            }
        });

        socket.on('ruleDev:compareResult', (data) => {
            clearTimeout(compareResultTimeout);
            setButtonSpinner($("#runRuleOverrideCompare"), "Run", false);

            if (data.error) {
                $("#ruleOverrideCompareResultText").text(data.error).prepend($("<b>").text("Error: "));
                $("#ruleOverrideCompareResultText").removeClass("font-italic");
                $("#ruleOverrideCompareResult").addClass("alert-danger");
                $("#ruleOverrideCompareResult").removeClass("alert-primary");
                $("#ruleOverrideCompareResult").show();

                editorAppendText(aceEditors.compareOutputConsole, data.error);
            } else {
                if (data.compareResultIsUndefined) {
                    $("#ruleOverrideCompareResultText").text("undefined");
                    $("#ruleOverrideCompareResultText").addClass("font-italic");
                } else {
                    $("#ruleOverrideCompareResultText").text(data.compareResult);
                    $("#ruleOverrideCompareResultText").removeClass("font-italic");
                }

                $("#ruleOverrideCompareResult").addClass("alert-primary");
                $("#ruleOverrideCompareResult").removeClass("alert-danger");
                $("#ruleOverrideCompareResult").show();

                editorAppendText(aceEditors.compareOutputConsole, data.message);
            }
        });

        $("#runRulePreCondition").click(function() {
            let rule = jsonEditor.getValue();
            rule.preCondition = codeEditors.preCondition.getValue();

            setButtonSpinner($("#runRulePreCondition"), "Run", true);
            editorAppendText(aceEditors.developOutputConsole, "Precondition run started");
            $("#rulePreConditionResult").hide();

            preConditionTimeout = setTimeout(function() {
                setButtonSpinner($("#runRulePreCondition"), "Run", false);

                $("#rulePreConditionResultText").text("Run pre-condition code: timeout of 10000ms was reached and no response from socket was received");
                $("#rulePreConditionResultText").removeClass("font-italic");
                $("#rulePreConditionResult").addClass("alert-danger");
                $("#rulePreConditionResult").removeClass("alert-primary");
                $("#rulePreConditionResult").show();

                editorAppendText(aceEditors.developOutputConsole, "Run pre-condition code: timeout of 5000ms was reached and no response from socket was received");
            }, 5000);

            socket.emit('ruleDev:preCondition', {
                variables: aceEditors.ruleVariables.getValue(),
                rule: rule
            });
        });

        $("#runRuleCode").click(function() {
            let rule = jsonEditor.getValue();
            rule.preCondition = codeEditors.preCondition.getValue();
            rule.ruleCode = codeEditors.ruleCode.getValue(),
            rule.valueMsgStm = codeEditors.valueMsgStm.getValue();
            rule.extraInfo = codeEditors.extraInfo.getValue();

            setButtonSpinner($("#runRuleCode"), "Run", true);
            editorAppendText(aceEditors.developOutputConsole, "Rule run started");
            $("#ruleRunAlert").hide();

            $("#Results").html(null);

            ruleCodeTimeout = setTimeout(function() {
                setButtonSpinner($("#runRuleCode"), "Run", false);

                $("#ruleRunAlert").show();
                editorAppendText(aceEditors.developOutputConsole, "Run rule code: timeout of 10000ms was reached and no response from socket was received");
            }, 10000);

            socket.emit('ruleDev:run', {
                variables: aceEditors.ruleVariables.getValue(),
                rule: rule
            });
        });

        $("#runRuleOverrideCompare").click(function() {
            let overrideCompare = aceEditors.ruleOverrideCompare.getValue();
            let ruleData1 = aceEditors.compareRuleData1.getValue();
            let ruleData2 = aceEditors.compareRuleData2.getValue();
            editorAppendText(aceEditors.compareOutputConsole, "Rule compare started");
            $("#ruleOverrideCompareResult").hide();

            setButtonSpinner($("#runRuleOverrideCompare"), "Run", true);

            compareResultTimeout = setTimeout(function() {
                setButtonSpinner($("#runRuleOverrideCompare"), "Run", false);

                $("#ruleOverrideCompareResultText").text("Run rule compare function: timeout of 5000ms was reached and no response from socket was received");
                $("#ruleOverrideCompareResultText").removeClass("font-italic");
                $("#ruleOverrideCompareResult").addClass("alert-danger");
                $("#ruleOverrideCompareResult").removeClass("alert-primary");
                $("#ruleOverrideCompareResult").show();

                editorAppendText(aceEditors.compareOutputConsole, "Run rule compare function: timeout of 5000ms was reached and no response from socket was received");
            }, 5000);

            socket.emit('ruleDev:compare', {
                overrideCompare: overrideCompare,
                ruleData1: ruleData1,
                ruleData2: ruleData2
            });
        });

        unitTestDiffEditor = new AceDiff({
            element: '#unitTestRecordComparison',
            mode: "ace/mode/json",
            left: {
                content: '',
                editable: false,
                copyLinkEnabled: false
            },
            right: {
                content: '',
                editable: false,
                copyLinkEnabled: false
            }
        });

        $("#runUnitTests").click(function() {
            setButtonSpinner($("#runUnitTests"), "Run Unit Tests", true);

            $.ajax({
                type: "POST",
                url: `/rules/unittest`,
                contentType: "application/json",
                dataType: "json",
                data: JSON.stringify({ rule: jsonEditor.getValue() }),
                success: function (response) {
                    let unitTestResultsTable = document.getElementById("unitTestResults");

                    // Clear the table
                    while (unitTestResultsTable.rows.length > 0) {
                        unitTestResultsTable.deleteRow(-1);
                    }

                    if (response.length == 0) {
                        $("#unitTestAlert").show();
                    }

                    for (var i in response) {
                        let result = response[i];

                        let unitTestResult = unitTestResultsTable.insertRow(-1);

                        let nameCell = unitTestResult.insertCell(0);
                        let statusCell = unitTestResult.insertCell(1);
                        let resultCell = unitTestResult.insertCell(2);

                        nameCell.innerHTML = result.name;
                        if (result.error) {
                            statusCell.innerHTML = `<span class="badge badge-danger">${result.status}</span>`;
                            resultCell.innerHTML = `<p>${result.error}</p>`;
                        } else if (result.status == 'pass') {
                            statusCell.innerHTML = `<span class="badge badge-success">${result.status}</span>`;
                            resultCell.innerHTML = `Test passed, output record matches. <button class="btn btn-secondary btn-sm unitTestExpand" value="${i}">View <span class="fas fa-external-link-alt"></span></button>`;
                        } else if (result.status == 'failed') {
                            statusCell.innerHTML = `<span class="badge badge-warning">${result.status}</span>`;
                            resultCell.innerHTML = `Output record from rule run does not match expected output record. <button class="btn btn-secondary btn-sm unitTestExpand" value="${i}">View <span class="fas fa-external-link-alt"></span></button>`;
                        }
                    }
                    let currDate = new Date().toLocaleString("en-GB");
                    $("#unitTestResultsUpdatedAt").text(`Last updated at: ${currDate}`);

                    setButtonSpinner($("#runUnitTests"), "Run Unit Tests", false);

                    $(".unitTestExpand").click(function() {
                        let result = response[this.value];

                        let outputRecord = result.outputRecord ? result.outputRecord : {};
                        let expectedRecord = result.expectedRecord ? result.expectedRecord : result.outputRecord;

                        let editors = unitTestDiffEditor.getEditors();
                        editors.left.setValue(JSON.stringify(outputRecord, null, 2));
                        editors.left.clearSelection();
                        editors.right.setValue(JSON.stringify(expectedRecord, null, 2));
                        editors.right.clearSelection();

                        $("#unitTestModal").modal('show');
                    });
                }
            });
        });

        jsonEditor.on("change", function() {
            // If the rule was loaded recently via AJAX, the change event will still be triggered,
            // do not count this instance as the rule being modified by the user
            if (!dataLoaded) {
                dataModified = true;
            } else {
                dataLoaded = false;
            }
        });

        // Sets up listeners on Ace editor instances for rule code
        for (const key in codeEditors) {
            codeEditors[key].on("change", function() {
                updateCodeDiffStatus(jsonEditor, codeEditors);
            });
        }

        aceEditors.ruleVariables.getSession().on('change', function(e) {
            // Warns the user if the input variables are larger than 8MB as a payload greater than
            // 10MB will cause the socket.io connection to be terminated by the server
            if (aceEditors.ruleVariables.getValue().length >= 8e6) {
                $("#ruleVariablesSizeAlert").show();
            } else {
                $("#ruleVariablesSizeAlert").hide();
            }
        });

        aceEditors.compareRuleData1.getSession().on('change', function(e) {
            // Warns the user if the rule data to compare when combined are larger than 8MB
            if (aceEditors.compareRuleData1.getValue().length + aceEditors.compareRuleData2.getValue().length >= 8e6) {
                $("#ruleData1SizeAlert").show();
                $("#ruleData2SizeAlert").show();
            } else {
                $("#ruleData1SizeAlert").hide();
                $("#ruleData2SizeAlert").hide();
            }
        });

        aceEditors.compareRuleData2.getSession().on('change', function(e) {
            // Warns the user if the rule data to compare when combined are larger than 8MB
            if (aceEditors.compareRuleData1.getValue().length + aceEditors.compareRuleData2.getValue().length >= 8e6) {
                $("#ruleData1SizeAlert").show();
                $("#ruleData2SizeAlert").show();
            } else {
                $("#ruleData1SizeAlert").hide();
                $("#ruleData2SizeAlert").hide();
            }
        });

        $(window).on("beforeunload", function(e) {
            let rule = jsonEditor.getValue();

            if (dataModified || editorHasDiff(rule, codeEditors)) {
                return confirm("");
            } else {
                return;
            }
        });
    });

    function getRuleToForm(editor) {
        setButtonSpinner($("#refreshRule"), $("<span>").addClass("fas fa-sync"), true);

        $.ajax({
            cache: false,
            type: "GET",
            url: `/rules/${encodeURIComponent(ruleName)}`,
            dataType: "json",
            success: function (response) {
                dataLoaded = true;
                dataModified = false;

                if (response.requiresReview) {
                    $("#requiresReviewAlert").show();
                }

                editor.setValue(response);
                $("#rulesEdit").show();

                // Load code obtained from request into execute rule editors
                $("#developRefreshCode").click();
            },
            error: function (error) {
                let errorMessage = '';
                if (error.status == 404) {
                    errorMessage = `Rule ${ruleName} does not exist`;
                } else {
                    errorMessage = `Error encountered loading rule ${ruleName}: HTTP ${error.status} (${error.statusText}): ${error.responseText}`;
                }
                $("#rulesLoadError").text(errorMessage);
                $("#rulesLoadError").show();
            },
            complete: function(xhr, status) {
                setButtonSpinner($("#refreshRule"), $("<span>").addClass("fas fa-sync"), false);
            }
        });
    }

    function createRuleFromForm(editor) {
        let errors = editor.validate();

        if (!errors.length) {
            $.ajax({
                type: "POST",
                url: "/rules/",
                contentType: "application/json",
                dataType: "json",
                data: JSON.stringify(editor.getValue()),
                success: function (response) {
                    dataModified = false;
                    window.location.href = `/edit/rules/${encodeURIComponent(editor.getValue().name)}`;
                },
                error: function (error) {
                    if (error.status == 400) {
                        $("#createRuleModalMessage").text(`Error in fields for rule:\n${JSON.stringify(error.responseJSON, null, 4)}`);
                    } else if (error.status == 409) {
                        $("#createRuleModalMessage").text(`A rule with name ${editor.getValue().name} already exists.`);
                    } else {
                        $("#createRuleModalMessage").text(`Unknown Error:\n${error.responseJSON}`);
                    }

                    $("#createRuleModal").modal({ show: true });
                },
            });
        } else {
            $("#createRuleModalMessage").text("Validation errors in editor, please check fields in form.");
            $("#createRuleModal").modal({ show: true });
        }
    }

    function writeRuleFromForm(editor) {
        let errors = editor.validate();

        if (!errors.length) {
            setButtonSpinner($("#saveRule"), $("<span>").addClass("fas fa-save"), true);

            $.ajax({
                cache: false,
                type: "PUT",
                url: `/rules/${encodeURIComponent(ruleName)}`,
                contentType: "application/json",
                dataType: "json",
                data: JSON.stringify(editor.getValue()),
                success: function (_) {
                    dataModified = false;
                },
                error: function(error) {
                    // just alerts the user for now
                    alert("Error, could not save rule:" + error.responseText);
                },
                complete: function(xhr, status) {
                    setButtonSpinner($("#saveRule"), $("<span>").addClass("fas fa-save"), false);
                }
            });
        } else {
            $("#createRuleModalMessage").text("Validation errors in editor, please check fields in form.");
            $("#createRuleModal").modal({ show: true });
        }
    }


    function setupDevelopmentEditors() {
        let editors = {};
        editors.ruleVariables = ace.edit("developRuleVariables", {
            mode: "ace/mode/javascript",
            minLines: 3,
            maxLines: 32
        });

        editors.ruleVariables.setValue(INPUT_VARIABLES_DEFAULT);
        editors.ruleVariables.clearSelection();

        editors.outputVariables = ace.edit("outputVariables", {
            mode: "ace/mode/json",
            minLines: 8,
            maxLines: 32,
            readOnly: true,
            showLineNumbers: false,
            printMargin: false
        });

        editors.rulePreCondition = ace.edit("developRulePreCondition", {
            mode: "ace/mode/javascript",
            minLines: 1,
            maxLines: 50,
            printMargin: false
        });

        editors.ruleCode = ace.edit("developRuleCode", {
            mode: "ace/mode/javascript",
            minLines: 1,
            maxLines: 100
        });

        editors.ruleValueMessageCode = ace.edit("developValueMessageCode", {
            mode: "ace/mode/javascript",
            minLines: 1,
            maxLines: 50,
            printMargin: false
        });

        editors.ruleExtraInformationCode = ace.edit("developExtraInformationCode", {
            mode: "ace/mode/javascript",
            minLines: 1,
            maxLines: 50,
            printMargin: false
        });

        editors.ruleOverrideCompare = ace.edit("ruleOverrideCompare", {
            mode: "ace/mode/javascript",
            minLines: 5,
            maxLines: 20
        });

        editors.compareRuleData1 = ace.edit("compareRuleData1", {
            mode: "ace/mode/json",
            minLines: 10,
            maxLines: 20,
            printMargin: false
        });

        editors.compareRuleData1.setValue(RULE_DATA_DEFAULT);
        editors.compareRuleData1.clearSelection();

        editors.compareRuleData2 = ace.edit("compareRuleData2", {
            mode: "ace/mode/json",
            minLines: 10,
            maxLines: 20,
            printMargin: false
        });

        editors.compareRuleData2.setValue(RULE_DATA_DEFAULT);
        editors.compareRuleData2.clearSelection();

        editors.developOutputConsole = ace.edit("developOutputConsole", {
            mode: "ace/mode/text",
            minLines: 10,
            maxLines: 100,
            readOnly: true,
            showLineNumbers: false,
            printMargin: false,
            showGutter: false,
            showFoldWidgets: false
        });

        editors.compareOutputConsole = ace.edit("compareOutputConsole", {
            mode: "ace/mode/text",
            minLines: 10,
            maxLines: 100,
            readOnly: true,
            showLineNumbers: false,
            printMargin: false,
            showGutter: false,
            showFoldWidgets: false
        });

        return Object.freeze(editors);
    }

    function editorAppendText(editor, text) {
        let currDate = new Date().toLocaleString("en-GB");
        editor.session.insert({
            row: editor.session.getLength(),
            column: 0
        }, `[${currDate}] ${text}\n`);
    }

    function updateCodeDiffStatus(ruleEditor, codeEditors) {
        let rule = ruleEditor.getValue();

        if (editorHasDiff(rule, codeEditors)) {
            $("#developDiffStatus").text("Unsaved changes");
            $("#developDiffStatus").addClass("alert-warning");
            $("#developDiffStatus").removeClass("alert-success");
        } else {
            $("#developDiffStatus").text("No differences with code fields in \"Edit Rule\"");
            $("#developDiffStatus").removeClass("alert-warning");
            $("#developDiffStatus").addClass("alert-success");
        }
    }

    function updateSaveTimeLabel() {
        $("#developSaveTime").text(`Last saved at: ${new Date().toLocaleString("en-GB")}`);
    }

    function setupSaveInterval(value, interval) {
        // Ignores setting interval if readonly is set,
        // but this function usually is not called in this case
        if (disableRuleSourceEditing) {
            return;
        }

        switch (value) {
            case "disabled":
                clearInterval(autosaveInterval);
                break;
            case "interval":
                autosaveInterval = setInterval(function() {
                    $("#developSaveCode").click();
                }, interval * 1000);
                break;
        }
    }

    function editorHasDiff(data, editors) {
        for (const key in editors) {
            if (data[key] != editors[key].getValue()) {
                return true;
            }
        }

        return false;
    }

    // Overrides showCompareResults() from serviceCheckRuleHelpers
    function showCompareResults(_, _) {
        alert("Cannot run service check comparison from the develop rule environment.");
    }
</script>
</body>
</html>
