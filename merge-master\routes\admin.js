'use strict';

import express from 'express';
const router = express.Router();

import auth from '../modules/auth.js';
import { AuthorizationRoles } from '../modules/enumerations.js';
import logger from '../modules/logger.js';
import runningStatus from '../modules/runningStatus.js';


router.get('/userStat', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), function (req, res) {
    res.render('userStat', { user: req.user });
});


router.get('/graphs', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), function (req, res) {
    res.render('graph', { title: 'Service Check Statistics Graphs', user: req.user });
});


router.get('/banner', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], true), function (req, res) {
    res.render('editBanner', { title: 'Edit banner message', user: req.user });
});


router.get('/statistics', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), function (req, res) {
    res.render('statistics', { title: 'Service Check Statistics', user: req.user });
});


router.get('/uniqueCasesTool',  auth.authorizeForRoles([
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), function (req, res) {
    res.render('uniqueCasesTool', { title: 'Service Check Unique Cases Tool', user: req.user });
});


router.get('/status', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], true), function (req, res) {
    res.render('statusPage', { title: 'Socket Status', user: req.user });
});


router.get('/runningStatus', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], false), function(req, res) {
    try {
        let serviceCheckStatus = runningStatus.serviceCheckGetStatus();
        res.send({
            hostname: global.gHostname,
            serviceChecks: serviceCheckStatus
        });
    } catch(error) {
        logger.error(`Admin running status render error, ${error.toString()}`);
        res.sendStatus(500);
    }
});


export default router;
