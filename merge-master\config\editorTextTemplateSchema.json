{"title": "Template", "type": "object", "id": "Template", "headerTemplate": "{{ self.name }}", "options": {"disable_collapse": true, "disable_edit_json": false, "disable_properties": true, "disable_array_add": true, "disable_array_delete": true, "disable_array_reorder": true}, "properties": {"name": {"title": "Name", "type": "string", "minLength": 1, "readonly": false}, "active": {"title": "Active", "type": "boolean", "format": "checkbox", "default": false}, "title": {"title": "Title", "type": "string", "description": "", "minLength": 0}, "description": {"title": "Description", "type": "string", "description": "", "minLength": 0}, "allowedSystemsToAppend": {"type": "array", "title": "Allowed ticketing systems to append this template to", "uniqueItems": true, "items": {"type": "string", "enum": ["SIIAM", "ServiceCentral"], "readonly": false}, "options": {"disable_array_delete": false, "disable_array_reorder": false}}, "suite": {"type": "array", "title": "Testing Suites", "uniqueItems": true, "items": {"type": "string", "enum": ["standard", "andig", "ipvoice", "outageInfo", "wholesale"], "readonly": false}, "options": {"disable_array_delete": false, "disable_array_reorder": false}}, "templateType": {"type": "string", "title": "Template Type", "enum": ["Service Check", "<PERSON><PERSON><PERSON>"]}, "formatType": {"type": "string", "title": "Format Type", "enum": ["Text", "HTML"], "default": "Text"}, "listCondition": {"title": "List Condition Code", "type": "string", "format": "javascript", "description": "Javascript expression to determine whether this template should be listed for a service check", "minLength": 0, "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 5}}}, "defaultPriority": {"title": "Default priority for template", "description": "Sets priority on whether this template should be rendered upon opening the text templates modal in a service check, 0 is to never render the template as a default and any number above 0 will indicate the template may be rendered by default.", "type": "integer", "format": "number", "minimum": 0, "default": 0}, "template": {"title": "Main Template EJS Code", "type": "string", "format": "ejs", "minLength": 0, "options": {"input_height": "300px"}}}}