'use strict';

import mongoose from 'mongoose';
import chai from 'chai';
import chaiHttp from 'chai-http';
import chaiLike from 'chai-like';
import chaiDateTime from 'chai-datetime';
import superagentDefaults from 'superagent-defaults';
import supertest from 'supertest';
import cookie from 'cookie';
import _ from 'lodash';
import sinon from 'sinon';
import assert from 'assert';

var app;
var request;
import config from '../config.js';
import Template from '../../db/model/template.js';
import helpers from '../helpers.js';
import { UNIT_TEST_ALL_ACCESS_USERNAME, UNIT_TEST_USERS } from '../users.js';

const should = chai.should();

chaiLike.extend(helpers.dateFieldCheck);
chaiLike.extend(helpers.regexCheck);
chai.use(chaiLike);
chai.use(chaiHttp);
chai.use(chaiDateTime);


const TEMPLATE_KEYS = Object.freeze(Object.keys(Template.schema.obj));


describe('Merge templates REST endpoints', () => {
    // Setup and connect to mongodb memory server, start merge app instance
    // Set request variable with a configured supertest agent with test JWT and app instance
    before(async function() {
        global.gConfig = _.cloneDeep(config.default);
        app = (await import('../../app.js')).default;
        request = superagentDefaults(supertest(app));
    });

    // Drop all contents of database before each test
    beforeEach(async function() {
        global.gConfig = _.cloneDeep(config.default);

        // Uses deleteMany instead of mongoose.connection.db.dropDatabase() because
        // custom indexes appear to be deleted if dropDatabase() is used
        for (let collectionName in mongoose.connection.collections) {
            await mongoose.connection.collections[collectionName].deleteMany({});
        }

        await helpers.authenticateSession(request, UNIT_TEST_ALL_ACCESS_USERNAME);
    });

    afterEach(async function() {
        sinon.restore();
    });

    describe('Read list of templates', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/templates');

            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/templates');

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Get empty list of templates', async() => {
            let res = await request.get('/templates');

            res.should.have.status(200);
            chai.expect(res.body).to.eql({
                metadata: {
                    pagination: {
                        limit: 100,
                        offset: 0,
                        total: 0,
                        prev: null,
                        next: null
                    }
                },
                results: []
            });
        });

        it('Get list of templates with one template (default values)', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'Template1',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);

            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get list of templates with three templates (default values)', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1'
            }).save());

            promises.push(new Template({
                name: 'Template2'
            }).save());

            promises.push(new Template({
                name: 'Template3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'Template1',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template2',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template3',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get list of templates with one template', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1',
                active: true,
                title: 'Test Template 1',
                description: 'Template for Unit Tests',
                allowedSystemsToAppend: [],
                suite: [
                    "standard",
                    "ipvoice"
                ],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: 'template code',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'Template1',
                active: true,
                title: 'Test Template 1',
                description: 'Template for Unit Tests',
                allowedSystemsToAppend: [],
                suite: [
                    "standard",
                    "ipvoice"
                ],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: 'template code',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get list of templates with two templates', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1',
                active: true,
                title: 'Test Template 1',
                description: 'Template for Unit Tests',
                allowedSystemsToAppend: [],
                suite: [
                    "standard",
                    "ipvoice"
                ],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: 'template code',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            }).save());

            promises.push(new Template({
                name: 'Template2',
                active: true,
                title: 'Test Template 2',
                description: 'Another template for Unit Tests',
                allowedSystemsToAppend: [],
                suite: [
                    "standard",
                    "ipvoice",
                    "outageInfo",
                    "wholesale"
                ],
                templateType: 'Service Check',
                formatType: 'HTML',
                listCondition: '',
                defaultPriority: 0,
                template: '<%= s.SourceName.data %>',
                createdOn: currDate,
                createdBy: 'merge'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates');

            res.should.have.status(200);

            chai.expect(res.body.results).to.like([{
                name: 'Template1',
                active: true,
                title: 'Test Template 1',
                description: 'Template for Unit Tests',
                allowedSystemsToAppend: [],
                suite: [
                    "standard",
                    "ipvoice"
                ],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: 'template code',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            },
            {
                name: 'Template2',
                active: true,
                title: 'Test Template 2',
                description: 'Another template for Unit Tests',
                allowedSystemsToAppend: [],
                suite: [
                    "standard",
                    "ipvoice",
                    "outageInfo",
                    "wholesale"
                ],
                templateType: 'Service Check',
                formatType: 'HTML',
                listCondition: '',
                defaultPriority: 0,
                template: '<%= s.SourceName.data %>',
                createdOn: currDate,
                createdBy: 'merge'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get one template with limit', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1'
            }).save());

            promises.push(new Template({
                name: 'Template2'
            }).save());

            promises.push(new Template({
                name: 'Template3'
            }).save());

            promises.push(new Template({
                name: 'Template4'
            }).save());

            promises.push(new Template({
                name: 'Template5'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?limit=1');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'Template1',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get one template with limit and offset', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1'
            }).save());

            promises.push(new Template({
                name: 'Template2'
            }).save());

            promises.push(new Template({
                name: 'Template3'
            }).save());

            promises.push(new Template({
                name: 'Template4'
            }).save());

            promises.push(new Template({
                name: 'Template5'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?limit=2&offset=2');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'Template3',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template4',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list sorted by name ascending', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1'
            }).save());

            promises.push(new Template({
                name: 'Template2'
            }).save());

            promises.push(new Template({
                name: 'Template3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?sort=name&order=asc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'Template1',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template2',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template3',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list sorted by name descending', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1'
            }).save());

            promises.push(new Template({
                name: 'Template2'
            }).save());

            promises.push(new Template({
                name: 'Template3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?sort=name&order=desc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'Template3',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template2',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template1',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list sorted by name ascending case insensitive', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'TeMPlaTe1'
            }).save());

            promises.push(new Template({
                name: 'templaTe2'
            }).save());

            promises.push(new Template({
                name: 'TEMPLATE3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?sort=name&order=asc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'TeMPlaTe1',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'templaTe2',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'TEMPLATE3',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list sorted by name descending case insensitive', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'TeMPlaTe1'
            }).save());

            promises.push(new Template({
                name: 'templaTe2'
            }).save());

            promises.push(new Template({
                name: 'TEMPLATE3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?sort=name&order=desc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'TEMPLATE3',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'templaTe2',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'TeMPlaTe1',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list sorted by title ascending', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1',
                title: 'Bravo'
            }).save());

            promises.push(new Template({
                name: 'Template2',
                title: 'Charlie'
            }).save());

            promises.push(new Template({
                name: 'Template3',
                title: 'Alpha'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?sort=title&order=asc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'Template3',
                active: false,
                title: 'Alpha',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template1',
                active: false,
                title: 'Bravo',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template2',
                active: false,
                title: 'Charlie',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list sorted by title descending', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1',
                title: 'Bravo'
            }).save());

            promises.push(new Template({
                name: 'Template2',
                title: 'Charlie'
            }).save());

            promises.push(new Template({
                name: 'Template3',
                title: 'Alpha'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?sort=title&order=desc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'Template2',
                active: false,
                title: 'Charlie',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template1',
                active: false,
                title: 'Bravo',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template3',
                active: false,
                title: 'Alpha',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list sorted by title ascending case insensitive', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1',
                title: 'Test Title 1'
            }).save());

            promises.push(new Template({
                name: 'Template2',
                title: 'test title 2'
            }).save());

            promises.push(new Template({
                name: 'Template3',
                title: 'TEST TITLE 3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?sort=title&order=asc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'Template1',
                active: false,
                title: 'Test Title 1',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template2',
                active: false,
                title: 'test title 2',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template3',
                active: false,
                title: 'TEST TITLE 3',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list sorted by title descending case insensitive', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1',
                title: 'Test Title 1'
            }).save());

            promises.push(new Template({
                name: 'Template2',
                title: 'test title 2'
            }).save());

            promises.push(new Template({
                name: 'Template3',
                title: 'TEST TITLE 3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?sort=title&order=desc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'Template3',
                active: false,
                title: 'TEST TITLE 3',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template2',
                active: false,
                title: 'test title 2',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template1',
                active: false,
                title: 'Test Title 1',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list sorted by description ascending', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1',
                description: 'Bravo'
            }).save());

            promises.push(new Template({
                name: 'Template2',
                description: 'Charlie'
            }).save());

            promises.push(new Template({
                name: 'Template3',
                description: 'Alpha'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?sort=description&order=asc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'Template3',
                active: false,
                title: '',
                description: 'Alpha',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template1',
                active: false,
                title: '',
                description: 'Bravo',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template2',
                active: false,
                title: '',
                description: 'Charlie',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list sorted by description descending', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1',
                description: 'Bravo'
            }).save());

            promises.push(new Template({
                name: 'Template2',
                description: 'Charlie'
            }).save());

            promises.push(new Template({
                name: 'Template3',
                description: 'Alpha'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?sort=description&order=desc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'Template2',
                active: false,
                title: '',
                description: 'Charlie',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template1',
                active: false,
                title: '',
                description: 'Bravo',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template3',
                active: false,
                title: '',
                description: 'Alpha',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list sorted by description ascending case insensitive', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1',
                description: 'Test Description 1'
            }).save());

            promises.push(new Template({
                name: 'Template2',
                description: 'test description 2'
            }).save());

            promises.push(new Template({
                name: 'Template3',
                description: 'TEST DESCRIPTION 3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?sort=description&order=asc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'Template1',
                active: false,
                title: '',
                description: 'Test Description 1',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template2',
                active: false,
                title: '',
                description: 'test description 2',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template3',
                active: false,
                title: '',
                description: 'TEST DESCRIPTION 3',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list sorted by description descending case insensitive', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1',
                description: 'Test Description 1'
            }).save());

            promises.push(new Template({
                name: 'Template2',
                description: 'test description 2'
            }).save());

            promises.push(new Template({
                name: 'Template3',
                description: 'TEST DESCRIPTION 3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?sort=description&order=desc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'Template3',
                active: false,
                title: '',
                description: 'TEST DESCRIPTION 3',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template2',
                active: false,
                title: '',
                description: 'test description 2',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template1',
                active: false,
                title: '',
                description: 'Test Description 1',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list filter by name equality 1', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'TestTemplate1'
            }).save());

            promises.push(new Template({
                name: 'TestTemplate2'
            }).save());

            promises.push(new Template({
                name: 'AnotherTemplate1'
            }).save());

            promises.push(new Template({
                name: 'AnotherTemplate2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?name=TestTemplate1');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'TestTemplate1',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list filter by name equality 2', async() => {
            let promises = [];

            promises.push(new Template({
                name: 'TestTemplate1'
            }).save());

            promises.push(new Template({
                name: 'TestTemplate2'
            }).save());

            promises.push(new Template({
                name: 'AnotherTemplate1'
            }).save());

            promises.push(new Template({
                name: 'AnotherTemplate2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?name=Test');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([]);
        });

        it('Get template list filter by name equality 3', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'TestTemplate1'
            }).save());

            promises.push(new Template({
                name: 'TestTemplate2'
            }).save());

            promises.push(new Template({
                name: 'AnotherTemplate1'
            }).save());

            promises.push(new Template({
                name: 'AnotherTemplate2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?name[equal]=AnotherTemplate2');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'AnotherTemplate2',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list filter by name equality 4', async() => {
            let promises = [];

            promises.push(new Template({
                name: 'TestTemplate1'
            }).save());

            promises.push(new Template({
                name: 'TestTemplate2'
            }).save());

            promises.push(new Template({
                name: 'AnotherTemplate1'
            }).save());

            promises.push(new Template({
                name: 'AnotherTemplate2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?name[equal]=Template');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([]);
        });

        it('Get template list filter by name like 1', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'TestTemplate1'
            }).save());

            promises.push(new Template({
                name: 'TestTemplate2'
            }).save());

            promises.push(new Template({
                name: 'AnotherTemplate1'
            }).save());

            promises.push(new Template({
                name: 'AnotherTemplate2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?name[like]=Test');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'TestTemplate1',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'TestTemplate2',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list filter by name like 2', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'TestTemplate1'
            }).save());

            promises.push(new Template({
                name: 'TestTemplate2'
            }).save());

            promises.push(new Template({
                name: 'AnotherTemplate1'
            }).save());

            promises.push(new Template({
                name: 'AnotherTemplate2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?name[like]=Template1');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'AnotherTemplate1',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'TestTemplate1',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list filter by name like 3', async() => {
            let promises = [];

            promises.push(new Template({
                name: 'TestTemplate1'
            }).save());

            promises.push(new Template({
                name: 'TestTemplate2'
            }).save());

            promises.push(new Template({
                name: 'AnotherTemplate1'
            }).save());

            promises.push(new Template({
                name: 'AnotherTemplate2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?name[like]=Text');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([]);
        });

        it('Get template list filter by name like 4 (case insensitive)', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'TestTemplate1'
            }).save());

            promises.push(new Template({
                name: 'TestTemplate2'
            }).save());

            promises.push(new Template({
                name: 'AnotherTemplate1'
            }).save());

            promises.push(new Template({
                name: 'AnotherTemplate2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?name[like]=AnOtHer');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'AnotherTemplate1',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'AnotherTemplate2',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list filter by title equality 1', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1',
                title: 'A template title'
            }).save());

            promises.push(new Template({
                name: 'Template2',
                title: 'Another template title'
            }).save());

            promises.push(new Template({
                name: 'Template3',
                title: 'Template title here'
            }).save());

            promises.push(new Template({
                name: 'Template4',
                title: 'Yet another template title'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?title=Template%20title%20here');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'Template3',
                active: false,
                title: 'Template title here',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list filter by title equality 2', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1',
                title: 'A template title'
            }).save());

            promises.push(new Template({
                name: 'Template2',
                title: 'Another template title'
            }).save());

            promises.push(new Template({
                name: 'Template3',
                title: 'Template title here'
            }).save());

            promises.push(new Template({
                name: 'Template4',
                title: 'Yet another template title'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?title[equal]=Template%20title%20here');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'Template3',
                active: false,
                title: 'Template title here',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list filter by title like 1', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1',
                title: 'Extract field here'
            }).save());

            promises.push(new Template({
                name: 'Template2',
                title: 'Extract field there'
            }).save());

            promises.push(new Template({
                name: 'Template3',
                title: 'Summarise field here'
            }).save());

            promises.push(new Template({
                name: 'Template4',
                title: 'Summarise field there'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?title[like]=Extract');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'Template1',
                active: false,
                title: 'Extract field here',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template2',
                active: false,
                title: 'Extract field there',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list filter by title like 2 (case insensitive)', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1',
                title: 'Extract field here'
            }).save());

            promises.push(new Template({
                name: 'Template2',
                title: 'Extract field there'
            }).save());

            promises.push(new Template({
                name: 'Template3',
                title: 'Summarise field here'
            }).save());

            promises.push(new Template({
                name: 'Template4',
                title: 'Summarise field there'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?title[like]=THeRE');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'Template2',
                active: false,
                title: 'Extract field there',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template4',
                active: false,
                title: 'Summarise field there',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list filter by description equality 1', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1',
                description: 'A template description'
            }).save());

            promises.push(new Template({
                name: 'Template2',
                description: 'Another template description'
            }).save());

            promises.push(new Template({
                name: 'Template3',
                description: 'Template description here'
            }).save());

            promises.push(new Template({
                name: 'Template4',
                description: 'Yet another template description'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?description=Another%20template%20description');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'Template2',
                active: false,
                title: '',
                description: 'Another template description',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list filter by description equality 2', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1',
                description: 'A template description'
            }).save());

            promises.push(new Template({
                name: 'Template2',
                description: 'Another template description'
            }).save());

            promises.push(new Template({
                name: 'Template3',
                description: 'Template description here'
            }).save());

            promises.push(new Template({
                name: 'Template4',
                description: 'Yet another template description'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?description[equal]=Another%20template%20description');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'Template2',
                active: false,
                title: '',
                description: 'Another template description',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list filter by description like 1', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1',
                description: 'Extract field here'
            }).save());

            promises.push(new Template({
                name: 'Template2',
                description: 'Extract field there'
            }).save());

            promises.push(new Template({
                name: 'Template3',
                description: 'Summarise field here'
            }).save());

            promises.push(new Template({
                name: 'Template4',
                description: 'Summarise field there'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?description[like]=Extract');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'Template1',
                active: false,
                title: '',
                description: 'Extract field here',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template2',
                active: false,
                title: '',
                description: 'Extract field there',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list filter by description like 2 (case insensitive)', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1',
                description: 'Extract field here'
            }).save());

            promises.push(new Template({
                name: 'Template2',
                description: 'Extract field there'
            }).save());

            promises.push(new Template({
                name: 'Template3',
                description: 'Summarise field here'
            }).save());

            promises.push(new Template({
                name: 'Template4',
                description: 'Summarise field there'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?description[like]=THeRE');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'Template2',
                active: false,
                title: '',
                description: 'Extract field there',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template4',
                active: false,
                title: '',
                description: 'Summarise field there',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list filter by templateType 1', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1',
                templateType: 'Service Check'
            }).save());

            promises.push(new Template({
                name: 'Template2',
                templateType: 'Module'
            }).save());

            promises.push(new Template({
                name: 'Template3',
                templateType: 'Module'
            }).save());

            promises.push(new Template({
                name: 'Template4',
                templateType: 'Service Check'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?templateType=Module');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'Template2',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Module',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'Template3',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Module',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list filter by templateType 2', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1',
                templateType: 'Service Check'
            }).save());

            promises.push(new Template({
                name: 'Template2',
                templateType: 'Module'
            }).save());

            promises.push(new Template({
                name: 'Template3',
                templateType: 'Module'
            }).save());

            promises.push(new Template({
                name: 'Template4',
                templateType: 'Service Check'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates?templateType=NotATemplateType');

            res.should.have.status(200);
            chai.expect(res.body.results).to.eql([]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(TEMPLATE_KEYS);
            });
        });

        it('Get template list invalid limit (non-integer)', async() => {
            let res = await request.get('/templates?limit=notalimit');

            res.should.have.status(400);
        });

        it('Get template list invalid limit (integer equals 0)', async() => {
            let res = await request.get('/templates?limit=0');

            res.should.have.status(400);
        });

        it('Get template list invalid limit (negative integer)', async() => {
            let res = await request.get('/templates?limit=-1');

            res.should.have.status(400);
        });

        it('Get template list invalid offset (non-integer)', async() => {
            let res = await request.get('/templates?offset=notanoffset');

            res.should.have.status(400);
        });

        it('Get template list invalid offset (negative integer)', async() => {
            let res = await request.get('/templates?offset=-1');

            res.should.have.status(400);
        });

        it('Get template list invalid order parameter', async() => {
            let res = await request.get('/templates?order=ascending');

            res.should.have.status(400);
        });

        it('Get template list invalid name filter operator', async() => {
            let res = await request.get('/templates?name[ophere]=test');

            res.should.have.status(400);
        });

        it('Get template list empty title filter operator', async() => {
            let res = await request.get('/templates?title[]=no-op');

            res.should.have.status(400);
        });

        it('Get template list empty description filter operator', async() => {
            let res = await request.get('/templates?description[]=no-op');

            res.should.have.status(400);
        });

        it('Get template list invalid templateType filter operator', async() => {
            let res = await request.get('/templates?templateType[equal]=Service%20Check');

            res.should.have.status(400);
        });

        it('Get template list with HTTP GET with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            let res = await request.get('/templates');

            res.should.have.status(200);
            chai.expect(res.body).to.eql({
                metadata: {
                    pagination: {
                        limit: 100,
                        offset: 0,
                        total: 0,
                        prev: null,
                        next: null
                    }
                },
                results: []
            });
        });
    });

    describe('Read template', () => {
        it('Unauthenticated request', async() => {
            await new Template({
                name: 'TemplateName'
            }).save();

            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/templates/TemplateName');

            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            await new Template({
                name: 'TemplateName'
            }).save();

            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/templates/TemplateName');

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Get non-existant template', async() => {
            let res = await request.get('/templates/Template1');

            res.should.have.status(404);
        });

        it('Get one existing template (default values)', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates/Template1');

            res.should.have.status(200);
            chai.expect(res.body).to.like({
                name: 'Template1',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(res.body).to.have.all.keys(TEMPLATE_KEYS);
        });

        it('Get one existing template', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1',
                active: false,
                title: 'Template Name 1',
                description: 'Template for unit tests',
                allowedSystemsToAppend: [],
                suite: [
                    "outageInfo"
                ],
                templateType: 'Service Check',
                formatType: 'HTML',
                listCondition: '',
                defaultPriority: 0,
                template: 'test template code',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates/Template1');

            res.should.have.status(200);

            chai.expect(res.body).to.like({
                name: 'Template1',
                active: false,
                title: 'Template Name 1',
                description: 'Template for unit tests',
                allowedSystemsToAppend: [],
                suite: [
                    "outageInfo"
                ],
                templateType: 'Service Check',
                formatType: 'HTML',
                listCondition: '',
                defaultPriority: 0,
                template: 'test template code',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });

            chai.expect(res.body).to.have.all.keys(TEMPLATE_KEYS);
        });

        it('Get existing template with HTTP GET with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/templates/Template1');

            res.should.have.status(200);
            chai.expect(res.body).to.like({
                name: 'Template1',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(res.body).to.have.all.keys(TEMPLATE_KEYS);
        });
    });

    describe('Create template', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.post('/templates').send({
                name: 'Template1'
            });

            res.should.have.status(401);
            chai.expect(await Template.find({ name: 'Template1' }).countDocuments()).to.eql(0);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.post('/templates').send({
                    name: 'Template1'
                });

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(201);
                            chai.expect(await Template.find({ name: 'Template1' }).countDocuments()).to.eql(1);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                            chai.expect(await Template.find({ name: 'Template1' }).countDocuments()).to.eql(0);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }

                await Template.deleteMany({});
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Create template with HTTP POST (default values)', async() => {
            let currDate = new Date();

            let postRes = await request.post('/templates').send({
                name: 'Template1'
            });

            postRes.should.have.status(201);
            chai.expect(postRes.body).to.like({
                name: 'Template1',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });

            let getRes = await request.get('/templates/Template1');

            getRes.should.have.status(200);
            chai.expect(getRes.body).to.like({
                name: 'Template1',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(getRes.body).to.have.all.keys(TEMPLATE_KEYS);
        });

        it('Create template with HTTP POST test invalid active field', async() => {
            let res = await request.post('/templates').send({
                name: 'Template1',
                active: 117
            });

            res.should.have.status(400);
        });

        it('Create template with HTTP POST', async() => {
            let currDate = new Date();

            let postRes = await request.post('/templates').send({
                name: 'Template1',
                active: true,
                title: 'Title Name Here',
                description: 'Description Here',
                allowedSystemsToAppend: [],
                suite: [
                    "standard",
                    "wholesale"
                ],
                templateType: 'Module',
                formatType: 'HTML',
                listCondition: '',
                defaultPriority: 0,
                template: '<%= r.MDR000.field %>'
            });

            postRes.should.have.status(201);
            chai.expect(postRes.body).to.like({
                name: 'Template1',
                active: true,
                title: 'Title Name Here',
                description: 'Description Here',
                allowedSystemsToAppend: [],
                suite: [
                    "standard",
                    "wholesale"
                ],
                templateType: 'Module',
                formatType: 'HTML',
                listCondition: '',
                defaultPriority: 0,
                template: '<%= r.MDR000.field %>',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(postRes.body).to.have.all.keys(TEMPLATE_KEYS);

            let getRes = await request.get('/templates/Template1');

            getRes.should.have.status(200);
            chai.expect(getRes.body).to.like({
                name: 'Template1',
                active: true,
                title: 'Title Name Here',
                description: 'Description Here',
                allowedSystemsToAppend: [],
                suite: [
                    "standard",
                    "wholesale"
                ],
                templateType: 'Module',
                formatType: 'HTML',
                listCondition: '',
                defaultPriority: 0,
                template: '<%= r.MDR000.field %>',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(getRes.body).to.have.all.keys(TEMPLATE_KEYS);
        });

        it('Create template with HTTP POST test duplicate suites', async() => {
            let res = await request.post('/templates').send({
                name: 'Template1',
                allowedSystemsToAppend: [],
                suite: [
                    'standard',
                    'standard'
                ]
            });

            res.should.have.status(400);

            chai.expect(await Template.find({ name: 'Template1' }).countDocuments()).to.eql(0);
        });

        it('Create duplicate template with HTTP POST', async() => {
            let postRes1 = await request.post('/templates').send({
                name: 'Template1'
            });

            postRes1.should.have.status(201);

            let postRes2 = await request.post('/templates').send({
                name: 'Template1',
            });

            postRes2.should.have.status(409);
        });

        it('Create template with HTTP POST test createdBy immutable', async() => {
            let currDate = new Date();

            let res = await request.post('/templates').send({
                name: 'Template1',
                createdBy: 'anotheruser'
            });

            res.should.have.status(201);
            chai.expect(res.body).to.like({
                name: 'Template1',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(res.body).to.have.all.keys(TEMPLATE_KEYS);
        });

        it('Create template with HTTP POST test createdOn immutable', async() => {
            let customDate = new Date('2020-12-15T16:00:00.000Z');
            let currDate = new Date();

            let res = await request.post('/templates').send({
                name: 'Template1',
                createdOn: customDate
            });

            res.should.have.status(201);
            chai.expect(res.body).to.like({
                name: 'Template1',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(res.body).to.have.all.keys(TEMPLATE_KEYS);
        });

        it('Create template with HTTP POST unrecognised fields', async() => {
            let currDate = new Date();

            let postRes = await request.post('/templates').send({
                name: 'Template1',
                customField: 'should not show up',
                unrecognisedField: 550
            });

            postRes.should.have.status(201);
            chai.expect(postRes.body).to.like({
                name: 'Template1',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });

            chai.expect(postRes.body).to.have.all.keys(TEMPLATE_KEYS);
            chai.expect(postRes.body).to.not.have.any.keys('customField', 'unrecognisedField');

            let getRes = await request.get('/templates/Template1');

            getRes.should.have.status(200);
            chai.expect(getRes.body).to.like({
                name: 'Template1',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(getRes.body).to.have.all.keys(TEMPLATE_KEYS);
            chai.expect(getRes.body).to.not.have.any.keys('customField', 'unrecognisedField');
        });

        it('Create template with HTTP POST with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            let postRes = await request.post('/templates').send({
                name: 'Template1'
            });

            postRes.should.have.status(405);

            let getRes = await request.get('/templates/Template1');

            getRes.should.have.status(404);
        });

        it('Create template with HTTP POST with disableRuleSourceEditing enabled and admin user', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'admin');

            let postRes = await request.post('/templates').send({
                name: 'Template1'
            });

            postRes.should.have.status(201);

            let getRes = await request.get('/templates/Template1');

            getRes.should.have.status(200);
        });
    });

    describe('Update template', () => {
        it('Unauthenticated request', async() => {
            await new Template({
                name: 'Template1',
                active: false
            }).save();

            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.put('/templates/Template1').send({
                active: true
            });

            res.should.have.status(401);
            chai.expect((await Template.findOne({ name: 'Template1' })).active).to.eql(false);
        });

        it('Authorization tests', async() => {
            await new Template({
                name: 'Template1',
                active: false
            }).save();

            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.put('/templates/Template1').send({
                    active: true
                });

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                            chai.expect((await Template.findOne({ name: 'Template1' })).active).to.eql(true);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                            chai.expect((await Template.findOne({ name: 'Template1' })).active).to.eql(false);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
                await Template.updateOne({ name: 'Template1' }, { $set: { active: false }});
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Update template with HTTP PUT', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/templates/Template1').send({
                name: 'Template1',
                active: false,
                title: 'Template 1',
                description: 'For unit tests',
                allowedSystemsToAppend: [],
                suite: [
                    "standard",
                    "wholesale"
                ],
                templateType: 'Module',
                formatType: 'HTML',
                listCondition: '',
                defaultPriority: 0,
                template: 'some text here',
            });

            res.should.have.status(200);
            chai.expect(res.body).to.like({
                name: 'Template1',
                active: false,
                title: 'Template 1',
                description: 'For unit tests',
                allowedSystemsToAppend: [],
                suite: [
                    "standard",
                    "wholesale"
                ],
                templateType: 'Module',
                formatType: 'HTML',
                listCondition: '',
                defaultPriority: 0,
                template: 'some text here',
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(res.body).to.have.all.keys(TEMPLATE_KEYS);
        });

        it('Update template with HTTP PUT test non-existant template', async() => {
            let res = await request.put('/templates/Template1');

            res.should.have.status(404);
        });

        it('Update template with HTTP PUT test invalid active field', async() => {
            let promises = [];

            promises.push(new Template({
                name: 'Template1'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/templates/Template1').send({
                active: 3
            });

            res.should.have.status(400);
        });

        it('Update template with HTTP PUT test duplicate elements in suite', async() => {
            let promises = [];

            promises.push(new Template({
                name: 'Template1'
            }).save());

            await Promise.all(promises);

            let res = await request.put('/templates/Template1').send({
                allowedSystemsToAppend: [],
                suite: [
                    'ipvoice',
                    'ipvoice'
                ]
            });

            res.should.have.status(400);
        });

        it('Update template with HTTP PUT test name immutable', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1'
            }).save());

            await Promise.all(promises);
            let putRes = await request.put('/templates/Template1').send({
                name: 'Template2'
            });

            putRes.should.have.status(200);

            let getRes1 = await request.get('/templates/Template1');

            getRes1.should.have.status(200);
            chai.expect(getRes1.body).to.like({
                name: 'Template1',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(getRes1.body).to.have.all.keys(TEMPLATE_KEYS);

            let getRes2 = await request.get('/templates/Template2');

            getRes2.should.have.status(404);
        });

        it('Update template with HTTP PUT test createdBy immutable', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1',
                createdBy: 'testuser'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/templates/Template1').send({
                createdBy: 'anotheruser'
            });

            res.should.have.status(200);
            chai.expect(res.body).to.like({
                name: 'Template1',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'testuser'
            });
            chai.expect(res.body).to.have.all.keys(TEMPLATE_KEYS);
        });

        it('Update template with HTTP PUT test createdOn immutable', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/templates/Template1').send({
                createdOn: '2010-01-15T16:00:00.000Z'
            });

            res.should.have.status(200);
            chai.expect(res.body).to.like({
                name: 'Template1',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(res.body).to.have.all.keys(TEMPLATE_KEYS);
        });

        it('Update template with HTTP PUT unrecognised fields', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/templates/Template1').send({
                badField: 'badValue'
            });

            res.should.have.status(200);

            chai.expect(res.body).to.like({
                name: 'Template1',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            });

            chai.expect(res.body).to.have.all.keys(TEMPLATE_KEYS);
            chai.expect(res.body).to.not.have.any.keys('badField');
        });

        it('Update template with HTTP PUT with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1'
            }).save());

            await Promise.all(promises);
            let putRes = await request.put('/templates/Template1').send({
                active: true
            });

            putRes.should.have.status(405);

            let getRes = await request.get('/templates/Template1');

            getRes.should.have.status(200);
            getRes.body.should.like({
                name: 'Template1',
                active: false,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(getRes.body).to.have.all.keys(TEMPLATE_KEYS);
        });

        it('Create template with HTTP PUT with disableRuleSourceEditing enabled and admin user', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'admin');

            let currDate = new Date();
            let promises = [];

            promises.push(new Template({
                name: 'Template1'
            }).save());

            await Promise.all(promises);
            let putRes = await request.put('/templates/Template1').send({
                active: true
            });

            putRes.should.have.status(200);

            let getRes = await request.get('/templates/Template1');

            getRes.should.have.status(200);
            getRes.body.should.like({
                name: 'Template1',
                active: true,
                title: '',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '',
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(getRes.body).to.have.all.keys(TEMPLATE_KEYS);
        });
    });

    describe('Delete template', () => {
        it('Unauthenticated request', async() => {
            await new Template({
                name: 'Template1'
            }).save();

            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.delete('/templates/Template1');

            res.should.have.status(401);
            chai.expect(await Template.find({ name: 'Template1' }).countDocuments()).to.eql(1);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                if (await Template.find({ name: 'Template1' }).countDocuments() === 0) {
                    await new Template({
                        name: 'Template1'
                    }).save();
                }

                await helpers.authenticateSession(request, username);
                let res = await request.delete('/templates/Template1');

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                            chai.expect(await Template.find({ name: 'Template1' }).countDocuments()).to.eql(0);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                            chai.expect(await Template.find({ name: 'Template1' }).countDocuments()).to.eql(1);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Delete template with HTTP DELETE', async() => {
            let promises = [];

            promises.push(new Template({
                name: 'Template1'
            }).save());

            await Promise.all(promises);
            let deleteRes = await request.delete('/templates/Template1');

            deleteRes.should.have.status(200);

            let getRes = await request.get('/templates/Template1');

            getRes.should.have.status(404);
        });

        it('Delete template with HTTP DELETE test non-existant template', async() => {
            let res = await request.delete('/templates/Template1');

            res.should.have.status(404);
        });

        it('Delete template with HTTP DELETE with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            let promises = [];

            promises.push(new Template({
                name: 'Template1'
            }).save());

            await Promise.all(promises);
            let deleteRes = await request.delete('/templates/Template1');

            deleteRes.should.have.status(405);

            let getRes = await request.get('/templates/Template1');

            getRes.should.have.status(200);
        });

        it('Delete template with HTTP DELETE with disableRuleSourceEditing enabled and admin user', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'admin');

            let promises = [];

            promises.push(new Template({
                name: 'Template1'
            }).save());

            await Promise.all(promises);
            let deleteRes = await request.delete('/templates/Template1');

            deleteRes.should.have.status(200);

            let getRes = await request.get('/templates/Template1');

            getRes.should.have.status(404);
        });
    });

    describe('Render template', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.post('/templates/render').send({
                serviceCheckRecord: {},
                template: {
                    name: 'TestTemplate',
                    active: true,
                    title: 'Test Template',
                    description: '',
                    allowedSystemsToAppend: [],
                    suite: [
                        'standard'
                    ],
                    templateType: 'Service Check',
                    formatType: 'Text',
                    listCondition: '',
                    defaultPriority: 0,
                    template: ''
                }
            });

            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.post('/templates/render').send({
                    serviceCheckRecord: {},
                    template: {
                        name: 'TestTemplate',
                        active: true,
                        title: 'Test Template',
                        description: '',
                        allowedSystemsToAppend: [],
                        suite: [
                            'standard'
                        ],
                        templateType: 'Service Check',
                        formatType: 'Text',
                        listCondition: '',
                        defaultPriority: 0,
                        template: ''
                    }
                });

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('No arguments', async() => {
            let res = await request.post('/templates/render');

            res.should.have.status(400);
        });

        it('String fields', async() => {
            let res = await request.post('/templates/render').send({
                serviceCheckRecord: 'test',
                template: 'test'
            });

            res.should.have.status(400);
        });

        it('Empty object fields', async() => {
            let res = await request.post('/templates/render').send({
                serviceCheckRecord: {},
                template: {}
            });

            res.should.have.status(500);
        });

        it('Valid template field and string service check field', async() => {
            let res = await request.post('/templates/render').send({
                serviceCheckRecord: 'test',
                template: {
                    name: 'TestTemplate',
                    active: true,
                    title: 'Test Template',
                    description: '',
                    allowedSystemsToAppend: [],
                    suite: [
                        'standard'
                    ],
                    templateType: 'Service Check',
                    formatType: 'Text',
                    listCondition: '',
                    defaultPriority: 0,
                    template: ''
                }
            });

            res.should.have.status(400);
        });

        it('Valid template field and empty service check record', async() => {
            let res = await request.post('/templates/render').send({
                serviceCheckRecord: {},
                template: {
                    name: 'TestTemplate',
                    active: true,
                    title: 'Test Template',
                    description: '',
                    allowedSystemsToAppend: [],
                    suite: [
                        'standard'
                    ],
                    templateType: 'Service Check',
                    formatType: 'Text',
                    listCondition: '',
                    defaultPriority: 0,
                    template: ''
                }
            });

            res.should.have.status(200);
            chai.expect(res.text).to.eql('');
        });

        it('Valid template code', async() => {
            let res = await request.post('/templates/render').send({
                serviceCheckRecord: {
                    fnn: 'N1234567R'
                },
                template: {
                    name: 'TestTemplate',
                    active: true,
                    title: 'Test Template',
                    description: '',
                    allowedSystemsToAppend: [],
                    suite: [
                        'standard'
                    ],
                    templateType: 'Service Check',
                    formatType: 'Text',
                    listCondition: '',
                    defaultPriority: 0,
                    template: '<%= data.fnn %>'
                }
            });

            res.should.have.status(200);
            chai.expect(res.text).to.eql('N1234567R');
        });

        it('Invalid template code', async() => {
            let res = await request.post('/templates/render').send({
                serviceCheckRecord: {
                    fnn: 'N1234567R'
                },
                template: {
                    name: 'TestTemplate',
                    active: true,
                    title: 'Test Template',
                    description: '',
                    allowedSystemsToAppend: [],
                    suite: [
                        'standard'
                    ],
                    templateType: 'Service Check',
                    formatType: 'Text',
                    listCondition: '',
                    defaultPriority: 0,
                    template: '<%= data.nested.field %>'
                }
            });

            res.should.have.status(500);
        });

        it('Valid template rendering a valid template module', async() => {
            await new Template({
                name: 'CarriageTypeModule',
                active: true,
                title: 'Carriage Type Module',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Module',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '<%= data.carriageType; -%>'
            }).save();

            let res = await request.post('/templates/render').send({
                serviceCheckRecord: {
                    fnn: 'N1234567R',
                    carriageType: 'NBN'
                },
                template: {
                    name: 'TestTemplate',
                    active: true,
                    title: 'Test Template',
                    description: '',
                    allowedSystemsToAppend: [],
                    suite: [
                        'standard'
                    ],
                    templateType: 'Service Check',
                    formatType: 'Text',
                    listCondition: '',
                    defaultPriority: 0,
                    template: '<%= renderTemplate("CarriageTypeModule"); %>'
                }
            });

            res.should.have.status(200);
            chai.expect(res.text).to.eql('NBN');
        });

        it('Valid template rendering a valid template module with parameters', async() => {
            await new Template({
                name: 'CarriageTypeModule',
                active: true,
                title: 'Carriage Type Module',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Module',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '<%= data.carriageType; %>\n<%= a + b; -%>'
            }).save();

            let res = await request.post('/templates/render').send({
                serviceCheckRecord: {
                    fnn: 'N1234567R',
                    carriageType: 'NBN'
                },
                template: {
                    name: 'TestTemplate',
                    active: true,
                    title: 'Test Template',
                    description: '',
                    allowedSystemsToAppend: [],
                    suite: [
                        'standard'
                    ],
                    templateType: 'Service Check',
                    formatType: 'Text',
                    listCondition: '',
                    defaultPriority: 0,
                    template: '<%= renderTemplate("CarriageTypeModule", { a: 100, b: 200 }); %>'
                }
            });

            res.should.have.status(200);
            chai.expect(res.text).to.eql('NBN\n300');
        });

        it('Valid template rendering an invalid template module (template module has incorrect templateType)', async() => {
            await new Template({
                name: 'CarriageTypeModule',
                active: true,
                title: 'Carriage Type Module',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Service Check',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '<%= data.carriageType; -%>'
            }).save();

            let res = await request.post('/templates/render').send({
                serviceCheckRecord: {
                    fnn: 'N1234567R',
                    carriageType: 'NBN'
                },
                template: {
                    name: 'TestTemplate',
                    active: true,
                    title: 'Test Template',
                    description: '',
                    allowedSystemsToAppend: [],
                    suite: [
                        'standard'
                    ],
                    templateType: 'Service Check',
                    formatType: 'Text',
                    listCondition: '',
                    defaultPriority: 0,
                    template: '<%= renderTemplate("CarriageTypeModule"); %>'
                }
            });

            res.should.have.status(500);
        });

        it('Valid template rendering an invalid template module (template module name does not exist)', async() => {
            let res = await request.post('/templates/render').send({
                serviceCheckRecord: {
                    fnn: 'N1234567R',
                    carriageType: 'NBN'
                },
                template: {
                    name: 'TestTemplate',
                    active: true,
                    title: 'Test Template',
                    description: '',
                    allowedSystemsToAppend: [],
                    suite: [
                        'standard'
                    ],
                    templateType: 'Service Check',
                    formatType: 'Text',
                    listCondition: '',
                    defaultPriority: 0,
                    template: '<%= renderTemplate("CarriageTypeModule"); %>'
                }
            });

            res.should.have.status(500);
        });

        it('Valid template rendering an invalid template module (template module is not active)', async() => {
            await new Template({
                name: 'CarriageTypeModule',
                active: false,
                title: 'Carriage Type Module',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Module',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '<%= data.carriageType; -%>'
            }).save();

            let res = await request.post('/templates/render').send({
                serviceCheckRecord: {
                    fnn: 'N1234567R',
                    carriageType: 'NBN'
                },
                template: {
                    name: 'TestTemplate',
                    active: true,
                    title: 'Test Template',
                    description: '',
                    allowedSystemsToAppend: [],
                    suite: [
                        'standard'
                    ],
                    templateType: 'Service Check',
                    formatType: 'Text',
                    listCondition: '',
                    defaultPriority: 0,
                    template: '<%= renderTemplate("CarriageTypeModule"); %>'
                }
            });

            res.should.have.status(500);
        });

        it('Valid template rendering an invalid template module (template module has invalid code)', async() => {
            await new Template({
                name: 'CarriageTypeModule',
                active: true,
                title: 'Carriage Type Module',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Module',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '<%= data.carriageType;'
            }).save();

            let res = await request.post('/templates/render').send({
                serviceCheckRecord: {
                    fnn: 'N1234567R',
                    carriageType: 'NBN'
                },
                template: {
                    name: 'TestTemplate',
                    active: true,
                    title: 'Test Template',
                    description: '',
                    allowedSystemsToAppend: [],
                    suite: [
                        'standard'
                    ],
                    templateType: 'Service Check',
                    formatType: 'Text',
                    listCondition: '',
                    defaultPriority: 0,
                    template: '<%= renderTemplate("CarriageTypeModule"); %>'
                }
            });

            res.should.have.status(500);
        });

        it('Valid template rendering an invalid template module (template module calls renderTemplate)', async() => {
            await new Template({
                name: 'CarriageFNNModule',
                active: true,
                title: 'Carriage Type Module',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Module',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '<%= data.carriageFNN; -%>'
            }).save();

            await new Template({
                name: 'CarriageTypeModule',
                active: true,
                title: 'Carriage Type Module',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Module',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '<%= data.carriageType; -%><%= renderTemplate("CarriageFNNModule"); -%>'
            }).save();

            let res = await request.post('/templates/render').send({
                serviceCheckRecord: {
                    fnn: 'N1234567R',
                    carriageType: 'NBN'
                },
                template: {
                    name: 'TestTemplate',
                    active: true,
                    title: 'Test Template',
                    description: '',
                    allowedSystemsToAppend: [],
                    suite: [
                        'standard'
                    ],
                    templateType: 'Service Check',
                    formatType: 'Text',
                    listCondition: '',
                    defaultPriority: 0,
                    template: '<%= renderTemplate("CarriageTypeModule"); %>'
                }
            });

            res.should.have.status(500);
        });

        it('Valid template rendering (templateType Module)', async() => {
            let res = await request.post('/templates/render').send({
                serviceCheckRecord: {
                    fnn: 'N1234567R',
                    carriageType: 'NBN'
                },
                template: {
                    name: 'TestTemplate',
                    active: true,
                    title: 'Test Template',
                    description: '',
                    allowedSystemsToAppend: [],
                    suite: [
                        'standard'
                    ],
                    templateType: 'Module',
                    formatType: 'Text',
                    listCondition: '',
                    defaultPriority: 0,
                    template: '<%= data.carriageType; %>'
                }
            });

            res.should.have.status(200);
            chai.expect(res.text).to.eql('NBN');
        });

        it('Valid template rendering (templateType Module with parameters)', async() => {
            let res = await request.post('/templates/render').send({
                serviceCheckRecord: {
                    fnn: 'N1234567R'
                },
                parameters: {
                    testVar: 'testString'
                },
                template: {
                    name: 'TestTemplate',
                    active: true,
                    title: 'Test Template',
                    description: '',
                    allowedSystemsToAppend: [],
                    suite: [
                        'standard'
                    ],
                    templateType: 'Module',
                    formatType: 'Text',
                    listCondition: '',
                    defaultPriority: 0,
                    template: '<%= testVar; %>'
                }
            });

            res.should.have.status(200);
            chai.expect(res.text).to.eql('testString');
        });

        it('Invalid template rendering (templateType Module with parameters, no parameters in request body)', async() => {
            let res = await request.post('/templates/render').send({
                serviceCheckRecord: {
                    fnn: 'N1234567R'
                },
                parameters: {},
                template: {
                    name: 'TestTemplate',
                    active: true,
                    title: 'Test Template',
                    description: '',
                    allowedSystemsToAppend: [],
                    suite: [
                        'standard'
                    ],
                    templateType: 'Module',
                    formatType: 'Text',
                    listCondition: '',
                    defaultPriority: 0,
                    template: '<%= testVar; %>'
                }
            });

            res.should.have.status(500);
        });

        it('Invalid template rendering an valid template module (input template has templateType Module, calls renderTemplate)', async() => {
            await new Template({
                name: 'CarriageTypeModule',
                active: true,
                title: 'Carriage Type Module',
                description: '',
                allowedSystemsToAppend: [],
                suite: [],
                templateType: 'Module',
                formatType: 'Text',
                listCondition: '',
                defaultPriority: 0,
                template: '<%= data.carriageType; -%>'
            }).save();

            let res = await request.post('/templates/render').send({
                serviceCheckRecord: {
                    fnn: 'N1234567R',
                    carriageType: 'NBN'
                },
                template: {
                    name: 'TestTemplate',
                    active: true,
                    title: 'Test Template',
                    description: '',
                    allowedSystemsToAppend: [],
                    suite: [
                        'standard'
                    ],
                    templateType: 'Module',
                    formatType: 'Text',
                    listCondition: '',
                    defaultPriority: 0,
                    template: '<%= renderTemplate("CarriageTypeModule"); %>'
                }
            });

            res.should.have.status(500);
        });
    });
});
