// commandRun schema

import mongoose from 'mongoose';


const commandSchema = new mongoose.Schema({
    id: { type: String, index: true, immutable: true },
    command: { type: String, default: '' },
    duration: { type: String, default: null },
    status: { type: String, default: null },
    error: { type: String, default: null },
    result: { type: String, default: null },
    statusCode: { type: Number, default: null },
    createdBy: { type: String, required: true, immutable: true, default: 'unknown', index: true },
    createdOn: { type: Date, required: true, immutable: true, default: Date.now, index: true }
});


commandSchema.methods.toJSON = function() {
    let obj = this.toObject();
    delete obj._id;
    delete obj.__v;
    return obj;
}


export default mongoose.model('commandRun', commandSchema);
