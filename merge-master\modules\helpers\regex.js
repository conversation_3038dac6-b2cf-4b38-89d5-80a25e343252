
const ruleNameRegex = /^[A-Z]{3}[0-9]{3}$/;
const sourceNameRegex = /^(?!InRequest-.*)[A-Za-z]+[:\w\-\.\s]*$/;
const sourceTitleRegex = /^([A-Za-z]+[:\w\-\.\s]*|)$/;
const apiNameRegex = /^([A-Za-z0-9]+[:\w\-\.\s]*|)$/;
const andigIdRegex = /^A\d{15}$/i;
const fnnRegex = /^N\d{7}[A-Z]$/;
const deviceNameRegex = /^(?:[0-9a-z]{3}|[0-9a-z]{5})[a-z]{5}\d{2}[a-z][0-9a-z]{2}$/i;
const internationalLinkRegex = /^(?:[A-Z]+ )+\d{7,9}$/i;
const avcRegex = /^AVC\d{12}$/;
const ovcRegex = /^OVC\d{12}$/;
const bdslFnnRegex = /^Y\d{11}[NL]$/;
const imsiRegex = /^\d{15}$/;
const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[4][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
const domIdRegex = /^[a-z0-9]{14}$/i;
const cfsIdRegex = /^[a-z]{4}\d{8,12}$/i;

export default {
    ruleName: ruleNameRegex,
    sourceName: sourceNameRegex,
    sourceTitle: sourceTitleRegex,
    apiName: apiNameRegex,
    andigId: andigIdRegex,
    fnn: fnnRegex,
    deviceName: deviceNameRegex,
    internationalLink: internationalLinkRegex,
    avc: avcRegex,
    ovc: ovcRegex,
    bdslFnn: bdslFnnRegex,
    imsi: imsiRegex,
    uuid: uuidRegex,
    domId: domIdRegex,
    cfsId: cfsIdRegex
};
