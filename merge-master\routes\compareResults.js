'use strict';

import express from 'express';
import { query, validationResult } from 'express-validator';
import _ from 'lodash';

import logger from '../modules/logger.js';
import mergeRule from '../modules/mergeRule.js';
import ServiceCheckModel from '../db/model/serviceCheck.js';
import auth from '../modules/auth.js';
import serviceCheck from '../modules/serviceCheck.js';
import compareRuleData from '../modules/compareRuleData.js';
import { AuthorizationRoles, CompareResult } from '../modules/enumerations.js';

const router = express.Router();


/* GET home page. */
router.get('/', auth.authorizeForRoles([
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), function (req, res) {
    res.render('compareResults', {
        title: 'Compare Service Check Results'
    });
});


/* GET one Script. */
router.get('/view', auth.authorizeForRoles([
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), [
    query('id1').isString().trim(),
    query('id2').isString().trim(),
    query('id3').optional().isString().trim(),
], async function (req, res) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let serviceCheckId1 = req.query.id1;
    let serviceCheckId2 = req.query.id2;
    let serviceCheckId3 = req.query.id3;

    try {
        let promises = [];

        promises.push(ServiceCheckModel.findOne({ id: serviceCheckId1 }));
        promises.push(ServiceCheckModel.findOne({ id: serviceCheckId2 }));

        if (serviceCheckId3) {
            promises.push(ServiceCheckModel.findOne({ id: serviceCheckId3 }));
        }

        const serviceCheckRecordKeys = Object.freeze([
            'id',
            'fnn',
            'createdOn',
            'createdBy'
        ]);

        let ruleSourceData = await mergeRule.getRulesAndSources();
        let [serviceCheckRecord1, serviceCheckRecord2, serviceCheckRecord3] = await Promise.all(promises);

        let serviceChecksNotFound = [];
        let recordArray = [];

        if (!serviceCheckRecord1) {
            serviceChecksNotFound.push(serviceCheckId1);
        } else {
            recordArray.push(serviceCheckRecord1);
        }

        if (!serviceCheckRecord2) {
            serviceChecksNotFound.push(serviceCheckId2);
        } else {
            recordArray.push(serviceCheckRecord2);
        }

        if (!serviceCheckRecord3) {
            if (serviceCheckId3) {
                serviceChecksNotFound.push(serviceCheckId3);
            }
        } else {
            recordArray.push(serviceCheckRecord3);
        }

        if (serviceChecksNotFound.length) {
            res.status(404).render('compareResultsViewError', {
                errorMessage: `Service check records could not be found, ID(s): ${serviceChecksNotFound.join(',')}`,
                user: req.user
            });
            return;
        }

        recordArray.sort(function (serviceCheckA, serviceCheckB) {
            return serviceCheckB.createdOn - serviceCheckA.createdOn;
        });

        // Checks each record to ensure data from the record should be displayed and that
        // the current user has the correct level access to view the service check record data
        for (let i in recordArray) {
            let record = recordArray[i];

            let disableDisplayMessage = serviceCheck.disableDisplayCondition(record, req.user);
            let level = _.get(record, ['input', 'level'], 0);

            if (disableDisplayMessage) {
                res.status(403).render('compareResultsViewError', {
                    errorMessage: [record.id, disableDisplayMessage.message, disableDisplayMessage.link].join(' '),
                    user: req.user
                });
                return;
            } else if (!auth.checkUserAuthorizedForServiceCheckLevel(req.user, level)) {
                res.status(403).render('compareResultsViewError', {
                    errorMessage: `You do not have access to view service check record ${record.id} with level: ${level}`,
                    user: req.user
                });
                return;
            }
        }

        const rules = Object.freeze(Object.assign(...ruleSourceData.rules.map(rule => ({ [rule.name]: rule }))));

        let ruleNamesChecked = new Set();
        let serviceCheckDifferentRules = [];
        let serviceCheckAllRules = [];

        for (let i in recordArray) {
            let record = recordArray[i];
            for (var ruleName in record.rulesData) {
                let rule = rules[ruleName];
                let compareStatus;
                let ruleCategory = _.get(rule, ["rootCauseCategory"], "Unknown");

                let ruleMetadata = {
                    name: ruleName,
                    overrideCompare: _.get(rule, ["overrideCompare"], ""),
                    present: rule ? true : false
                };

                // Uses the output message from the first record where this rule is present
                // Does not account for different output messages across different records
                // This behaviour is consistent with the last implementation of compareResults
                let ruleMessage = record.rulesData[ruleName].msg;

                let ruleDataEqual_1_2 = true;
                let ruleDataEqual_1_3 = true;
                let ruleDataEqual_2_3 = true;

                // Skips comparing rule if ignoreCompare or hideInResult is set for rule
                if (rule && (rule.ignoreCompare || rule.hideInResult)) {
                    continue;
                }

                // Skips comparing rule if it has already been checked from another record
                if (ruleNamesChecked.has(ruleName)) {
                    continue;
                }

                let rulesDataRecord1 = _.get(recordArray, [0, "rulesData", ruleName]);
                let rulesDataRecord2 = _.get(recordArray, [1, "rulesData", ruleName]);
                let rulesDataRecord3 = _.get(recordArray, [2, "rulesData", ruleName]);

                try {
                    if (!rulesDataRecord1 || !rulesDataRecord2 || (_.get(recordArray, [2, "rulesData"]) && !rulesDataRecord3)) {
                        compareStatus = CompareResult.ruleDataMissing;
                    } else {
                        ruleDataEqual_1_2 = await compareRuleData.isEqual(rulesDataRecord1, rulesDataRecord2, rule.overrideCompare);

                        if (rulesDataRecord3) {
                            ruleDataEqual_1_3 = await compareRuleData.isEqual(rulesDataRecord1, rulesDataRecord3, rule.overrideCompare);
                            ruleDataEqual_2_3 = await compareRuleData.isEqual(rulesDataRecord2, rulesDataRecord3, rule.overrideCompare);
                        }

                        if (ruleDataEqual_1_2 && ruleDataEqual_1_3 && ruleDataEqual_2_3) {
                            compareStatus = CompareResult.equal;
                        } else {
                            compareStatus = CompareResult.notEqual;
                        }
                    }
                } catch(error) {
                    compareStatus = CompareResult.compareError;
                    ruleMetadata.error = `Error comparing rule data: ${error.message}`;
                }

                let compareResultRow = [
                    compareStatus,
                    ruleCategory,
                    ruleMessage,
                    rulesDataRecord1,
                    rulesDataRecord2,
                    rulesDataRecord3,
                    ruleMetadata
                ];

                switch (compareStatus) {
                    case CompareResult.equal:
                        serviceCheckAllRules.push(compareResultRow);
                        break;
                    case CompareResult.notEqual:
                        serviceCheckAllRules.push(compareResultRow);
                        serviceCheckDifferentRules.push(compareResultRow);
                        break;
                    case CompareResult.ruleDataMissing:
                        serviceCheckAllRules.push(compareResultRow);
                        serviceCheckDifferentRules.push(compareResultRow);
                        break;
                    case CompareResult.compareError:
                        serviceCheckAllRules.push(compareResultRow);
                        serviceCheckDifferentRules.push(compareResultRow);
                        break;
                }

                ruleNamesChecked.add(ruleName);
            }
        }

        res.render('compareResultsView', {
            serviceCheckRecord1: Buffer.from(JSON.stringify(_.pick(recordArray[0], serviceCheckRecordKeys))).toString('base64'),
            serviceCheckRecord2: Buffer.from(JSON.stringify(_.pick(recordArray[1], serviceCheckRecordKeys))).toString('base64'),
            serviceCheckRecord3: Buffer.from(JSON.stringify(_.pick(recordArray[2], serviceCheckRecordKeys))).toString('base64'),
            serviceCheckAllRules: Buffer.from(JSON.stringify(serviceCheckAllRules)).toString('base64'),
            serviceCheckDifferentRules: Buffer.from(JSON.stringify(serviceCheckDifferentRules)).toString('base64'),
            CompareResult: Buffer.from(JSON.stringify(CompareResult)).toString('base64'),
            user: req.user
        });
    } catch(error) {
        logger.error(`Service check compare, error obtaining service check records, ${error.toString()}`);
        res.sendStatus(500);
    };
});


export default router;
