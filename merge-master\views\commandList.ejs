<!-- v1 -->
<%- include('header', {}); %>
<%- include('menu', {currentTab: 'command'}); %>
<div class="container">
    <br>
    <div class="jumbotron py-1">
        <h2 class="display-4"><%= title  %></h2>
        <div id="listOfSCs" class="border">
            <div >

                    <% for ( var C of commands ){ %>
                        <div class="row">
                            <a class='expand btn-sm' data-toggle='collapse' data-target='#Command-<%= C.name  %>'>
                                <span style='color:blue' class='fas fa-plus-square' title='Expand/Collapse'></span>
                                <b><%= C.title %></b>
                            </a>
                            [<span class="text-monospace"><%= C.name %></span>]
                            :&nbsp;
                            <span><%= C.description %></span>
                        </div>
                        <div id='Command-<%= C.name %>' class='border rounded collapse in'>
                            <span class="text-monospace"><%= C.name %></span><span>: <%= C.help %></span><br />
                            <div class="ml-2">
                                <table class="table thead-dark table-sm"><thead><tr><th>Switches</th><th>Description</th></tr></thead>
                                <% for (var param of C.param) {%>
                                    <tr>
                                <td><%= param.switch %> </td><td> <%= param.help  %></td>
                                    </tr>
                                <% } %>
                                </table>
                            </div>
                            <span><b>Example</b>: <%= C.example?C.example:'N/A' %></span>
                        </div>
                    <% } %>

            </div>
        </div>

    </div>
</div>
<%- include('footer', {}); %>
