import { TextStyle } from '@able/react'
import { faInfoCircle } from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { Panel } from '../panel/Panel'
import styles from './InformationPanel.module.scss'

interface InformationPanelProps {
    infoMessage: string
    className?: string
    actions?: React.ReactNode
}

export const InformationPanel = ({
    infoMessage,
    className,
    actions,
}: InformationPanelProps) => {
    return (
        <Panel className={className}>
            <div className={styles.infoPanel}>
                <div className={styles.info}>
                    <FontAwesomeIcon
                        icon={faInfoCircle}
                        size={'lg'}
                        className={styles.infoIcon}
                    />
                    <TextStyle alias="LabelA1">
                        <>{infoMessage}</>
                    </TextStyle>
                </div>
                {actions && <div className={styles.actions}>{actions}</div>}
            </div>
        </Panel>
    )
}
