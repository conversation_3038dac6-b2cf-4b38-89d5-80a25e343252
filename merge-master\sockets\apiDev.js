'use strict';

import ivm from 'isolated-vm';
import _ from 'lodash';

import mergeCollect from '../modules/mergeCollect.js';
import Api from '../db/model/api.js';


export function socketListen(socket) {
    socket.on('apiDev:createUrl', async function (data) {
        if (!_.isPlainObject(data)) {
            socket.emit('apiDev:createUrlResult', {
                error: new TypeError('Input data from socket was not a plain object').toString()
            });
            return;
        }

        let api = data.api;
        let variablesCode = data.variables;
        let parameters = {};

        let apiModelInstance = new Api(api);
        try {
            await apiModelInstance.validate();
        } catch(error) {
            socket.emit('apiDev:createUrlResult', {
                error: error.toString()
            });
            return;
        }

        const isolate = new ivm.Isolate({
            memoryLimit: 32
        });
        let parameterContext = await isolate.createContext();

        try {
            try {
                await parameterContext.eval(variablesCode, { timeout: 1000 });

                for (let parameterName of apiModelInstance.parameters) {
                    parameters[parameterName] = await parameterContext.global.get(parameterName, { copy: true });
                }
            } catch(error) {
                socket.emit('apiDev:createUrlResult', {
                    error: error.toString()
                });
                return;
            }

            try {
                let apiUrl = await mergeCollect.createUrl(api.baseUrl, api.uri, api.queryParams, parameters, null);

                socket.emit('apiDev:createUrlResult', {
                    error: null,
                    url: apiUrl
                });
            } catch(error) {
                socket.emit('apiDev:createUrlResult', {
                    error: error.toString()
                });
            }
        } finally {
            parameterContext.release();
            if (!isolate.isDisposed) {
                isolate.dispose();
            }
        }
    });

    socket.on('apiDev:runApiCode', async function (data) {
        if (!_.isPlainObject(data)) {
            socket.emit('apiDev:runApiCodeResult', {
                error: new TypeError('Input data from socket was not a plain object').toString(),
                message: 'Error with input data'
            });
            return;
        }

        let api = data.api;
        let variablesCode = data.variables;
        let parameters = {};

        let apiModelInstance = new Api(api);
        try {
            await apiModelInstance.validate();
        } catch(error) {
            socket.emit('apiDev:runApiCodeResult', {
                error: error.toString(),
                message: 'Error encountered while validating input API'
            });
            return;
        }

        const isolate = new ivm.Isolate({
            memoryLimit: 32
        });
        let parameterContext = await isolate.createContext();

        try {
            try {
                await parameterContext.eval(variablesCode, { timeout: 1000 });

                for (let parameterName of apiModelInstance.parameters) {
                    parameters[parameterName] = await parameterContext.global.get(parameterName, { copy: true });
                }
            } catch(error) {
                socket.emit('apiDev:runApiCodeResult', {
                    error: error.toString(),
                    message: 'Error encountered while attempting to evaluate variables'
                });
                return;
            }

            try {
                let result = await mergeCollect.collectApi(apiModelInstance, parameters);

                socket.emit('apiDev:runApiCodeResult', {
                    error: null,
                    collectionDuration: result.collectionDuration,
                    response: result.data,
                    status: result.status
                });
            } catch(error) {
                if (error.result) {
                    socket.emit('apiDev:runApiCodeResult', {
                        error: error.toString(),
                        collectionDuration: error.result.collectionDuration,
                        response: error.result.data,
                        status: error.result.status
                    });
                } else {
                    socket.emit('apiDev:runApiCodeResult', {
                        error: error.toString(),
                        collectionDuration: null,
                        response: null,
                        status: null
                    });
                }
            }
        } finally {
            parameterContext.release();
            if (!isolate.isDisposed) {
                isolate.dispose();
            }
        }
    });
}