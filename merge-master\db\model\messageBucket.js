import mongoose from 'mongoose';


const messageBucketSchema = new mongoose.Schema({
    name: { type: String, minLength: 1, default: null, unique: true, required: true, immutable: true },
    description: { type: String, default: '' },
    messageFrontOfHouse: { type: String, default: '' },
    messageCustomer: { type: String, default: '' },
    createdBy: { type: String, required: true, immutable: true, default: 'unknown' },
    createdOn: { type: Date, required: true, immutable: true, default: Date.now }
}, {
    autoIndex: true
});


messageBucketSchema.methods.toJSON = function() {
    let obj = this.toObject();
    delete obj._id;
    delete obj.__v;
    return obj;
}


export default mongoose.model('messagebucket', messageBucketSchema);
