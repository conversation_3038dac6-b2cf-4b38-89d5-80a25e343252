import { CollapsablePanel } from '../../../../components/collapsablePanel/CollapsablePanel'
import { DetailField } from '../../../../components/detailsPanel/DetailField'
import { Panel } from '../../../../components/panel/Panel'
import { formatDate } from '../../../../helpers/formatDate'
import { PlannedOutage } from '../../../../infrastructure/models'
import styles from './PlannedOutagesField.module.scss'
interface PlannedOutagesFieldProps {
    value: PlannedOutage[]
}

export const PlannedOutagesField = ({ value }: PlannedOutagesFieldProps) => {
    if (value === null || value === undefined) {
        return (
            <Panel>
                <DetailField label="Planned Outages" value="Unknown" />
            </Panel>
        )
    }

    if (value.length === 0) {
        return (
            <Panel>
                <DetailField label="Planned Outages" value="None" />
            </Panel>
        )
    }

    return (
        <CollapsablePanel
            headerElement={<DetailField label="Planned Outages" value="" />}
            canOpen={value.length > 0}
            itemCount={value.length}
        >
            <div className={styles.plannedOutages}>
                <div className={styles.outageRow}>
                    {value.map((outage, index) => (
                        <div key={`${outage.calm_id}-${index}`}>
                            <DetailField
                                label="CALM"
                                value={outage.calm_id}
                                inline={true}
                            />
                            <DetailField
                                label="Start"
                                value={formatDate(outage.start_date, 'Unknown')}
                                inline={true}
                            />
                            <DetailField
                                label="End"
                                value={formatDate(outage.end_date, 'Unknown')}
                                inline={true}
                            />
                        </div>
                    ))}
                </div>
            </div>
        </CollapsablePanel>
    )
}
