import { CollapsablePanel } from '../../../../components/collapsablePanel/CollapsablePanel'
import { DetailField } from '../../../../components/detailsPanel/DetailField'
import { Panel } from '../../../../components/panel/Panel'
import { formatDate } from '../../../../helpers/formatDate'
import { PowerOutage } from '../../../../infrastructure/models'
import styles from './PowerOutagesField.module.scss'

interface PowerOutagesFieldProps {
    value: PowerOutage[]
}

export const PowerOutagesField = ({ value }: PowerOutagesFieldProps) => {
    if (value === null || value === undefined) {
        return (
            <Panel>
                <DetailField label="Power Outages" value="Unknown" />
            </Panel>
        )
    }

    if (value.length === 0) {
        return (
            <Panel>
                <DetailField label="Power Outages" value="None" />
            </Panel>
        )
    }

    return (
        <CollapsablePanel
            headerElement={<DetailField label="Power Outages" value="" />}
            canOpen={value.length > 0}
            itemCount={value.length}
        >
            <div className={styles.powerOutages}>
                <div className={styles.outageRow}>
                    {value.map((outage, index) => (
                        <div key={`${outage.reason}-${index}`}>
                            <DetailField
                                label="Reason"
                                value={outage.reason || 'Unknown'}
                                inline={true}
                            />
                            <DetailField
                                label="ETR"
                                value={formatDate(outage.ETR, 'Unknown')}
                                inline={true}
                            />
                        </div>
                    ))}
                </div>
            </div>
        </CollapsablePanel>
    )
}
