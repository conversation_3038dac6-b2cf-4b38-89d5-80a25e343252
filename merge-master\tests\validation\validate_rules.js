
import assert from 'assert';
import _ from 'lodash';

import Rule from '../../db/model/rule.js';
import mergeConfigList from '../../modules/mergeConfigList.js';
import { MERGE_OBJECT_CONFIG_FILES } from '../../modules/helpers/constants.js';
import helpers from '../helpers.js';


describe('Merge Rules', () => {
    it('Unique names', async() => {
        let rules = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.ruleSource)).rules;
        let ruleNames = new Set();

        for (let i in rules) {
            if (ruleNames.has(rules[i].name)) {
                assert.fail(`Duplicate rule name ${rules[i].name} in config`);
            }
            ruleNames.add(rules[i].name);
        };
    });

    it('Valid fields', async() => {
        let rules = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.ruleSource)).rules;

        for (let i in rules) {
            let rule = new Rule(rules[i]);
            try {
                await rule.validate();
            } catch(error) {
                let newError = new Error(`Error validating rule ${rule.name}`);
                newError.original_error = error;
                newError.stack = error.stack;
                throw newError;
            }
        }
    });

    it('Ordered by name ascending', async() => {
        let rules = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.ruleSource)).rules;

        for (let i = 1; i < rules.length; i++) {
            let prevRule = rules[i - 1];
            let currRule = rules[i];

            if (prevRule.name.localeCompare(currRule.name, 'en') > 0) {
                assert.fail(`Rule name ${prevRule.name} is out of order, should be after rule ${currRule.name}`);
            }
        }
    });

    it('Correct field order', async() => {
        let rules = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.ruleSource)).rules;
        let invalidNames = [];

        for (let i in rules) {
            let ruleJson = new Rule(rules[i]).toJSON();
            delete ruleJson.createdBy;
            delete ruleJson.createdOn;

            if (!_.isEqualWith(rules[i], ruleJson, helpers.compareObjectWithKeyOrder)) {
                invalidNames.push(rules[i].name);
            };
        }

        if (invalidNames.length) {
            assert.fail(`Key order for rule(s) ${invalidNames.join(',')} does not match database schema order`);
        }
    });
});

