/**
 * Note: This migration file was created when 'migrate-mongoose' was the npm package
 * used to perform MongoDB migrations, since the 'migrate' npm package is now used
 * the implementation may not be compatible and all functionality is commented out
 */

// const _ = require('lodash');
// const mongoose = require('mongoose');
// const util = require('util');

// const migrate = require('./migrate');


// // Re-defines outcome / symptom schemas here,
// // required as they are referred to by the service check schema
// const outcomeSchema = new mongoose.Schema({
//     name: { type: String, minLength: 1, default: null, unique: true, required: true, immutable: true },
//     title: { type: String, default: "" },
//     description: { type: String, default: "" },
//     header: { type: String, default: "" },
//     display: { type: Boolean, default: false },
//     createdBy: { type: String, required: true, immutable: true, default: "unknown" },
//     createdOn: { type: Date, required: true, immutable: true, default: Date.now }
// }, {
//     autoIndex: true
// });

// const Outcome = mongoose.model("outcome", outcomeSchema);

// const symptomSchema = new mongoose.Schema({
//     name: { type: String, default: null, unique: true, required: true, immutable: true },
//     active: { type: Boolean, default: false },
//     title: { type: String, default: "" },
//     description: { type: String, default: "" },
//     categoryLabel: { type: String, default: "" },
//     suite: {
//         type: [{
//             type: String
//         }],
//         default: [],
//         validate: {
//             validator: function(suite) {
//                 return suite.length === new Set(suite).size;
//             },
//             message: "array should only contain unique string values"
//         }
//     },
//     rules: {
//         type: [{
//             type: String,
//             validate: /^[A-Z]{3}[0-9]{3}$/
//         }],
//         default: [],
//         validate: {
//             validator: function(preRules) {
//                 return preRules.length === new Set(preRules).size;
//             },
//             message: "array should only contain unique string values"
//         }
//     },
//     productTypes: {
//         type: [{
//             type: String,
//             minLength: 1
//         }],
//         default: [],
//         validate: {
//             validator: function(productTypes) {
//                 return productTypes.length === new Set(productTypes).size;
//             },
//             message: "array should only contain unique string values"
//         }
//     },
//     createdBy: { type: String, required: true, immutable: true, default: "unknown" },
//     createdOn: { type: Date, required: true, immutable: true, default: Date.now }
// });

// const Symptom = mongoose.model("symptom", symptomSchema);


// // Input schema is the same for both migrations
// const inputSchema = new mongoose.Schema({
//     searchFNN: { type: String, default: null },
//     suite: {
//         type: String
//     },
//     level: { type: Number, min: 0, max: 6, default: 0 },
//     carriageFNN: { type: String, default: null },
//     carriageType: { type: String, default: null },
//     deviceIP: { type: String, default: null },
//     deviceName: { type: String, default: null },
//     idType: { type: String, default: null },
//     fieldInterfaceIP: { type: String, default: null },
//     ruleNames: { type: [{ type: String }], default: [] },
//     sourceNames: { type: [{ type: String }], default: [] },
//     symptoms: { type: [{ type: String }], default: [] }
// }, { _id: false });


// function ServiceCheckSchema() {
//     mongoose.Schema.apply(this, arguments);

//     this.add({
//         id: { type: String, default: null, index: true, immutable: true },
//         fnn: { type: String, default: "", index: true },
//         phoneNumber: { type: String, default: null, index: true },
//         billingFNN: { type: String, default: null },
//         carriageFNN: { type: String, default: null, index: true },
//         MDNFNN: { type: String, default: null },
//         carriageType: { type: String, default: null, index: true },
//         nbnServiceType: { type: String, default: null },
//         nbnAccessType: { type: String, default: null },
//         nbnSubAccessType: { type: String, default: null },
//         nbnId: { type: String, default: null },
//         deviceName: { type: String, default: null, index: true },
//         CIDN: { type: String, default: null },
//         address: { type: String, default: null },
//         OffshoreResources: { type: String, default: null },
//         serviceType: { type: String, default: null, index: true },
//         siiamCases: {
//             type: [{
//                 type: String
//             }],
//             default: []
//         },
//         siiamCasesLength: { type: Number, default: 0, select: false, index: true },
//         serviceCentralIncidents: {
//             type: [{
//                 type: String
//             }],
//             default: []
//         },
//         serviceCentralIncidentsLength: { type: Number, default: 0, select: false, index: true },
//         status: { type: String, default: null, index: true },
//         endedOn: { type: Date, default: null },
//         durationMilliSec: { type: Number, default: 0 },
//         input: {
//             type: inputSchema,
//             default: {}
//         },
//         additionalParameters: { type: Object, default: {} },
//         rulesData: { type: Object, default: {} },
//         sourcesData: { type: Object, default: {} },
//         sourcesMetadata: { type: Object, default: {} },
//         errorMessage: { type: String, default: null },
//         outcomeNames: {
//             type: [{
//                 type: String
//             }],
//             default: []
//         },
//         messagesFrontOfHouse: {
//             type: [{ type: String }],
//             default: []
//         },
//         messagesCustomer: {
//             type: [{ type: String }],
//             default: []
//         },
//         productTypes: {
//             type: [{
//                 type: String,
//                 minLength: 1
//             }],
//             default: [],
//             validate: {
//                 validator: function(productTypes) {
//                     return productTypes.length === new Set(productTypes).size;
//                 },
//                 message: "array should only contain unique string values"
//             }
//         },
//         createdBy: { type: String, required: true, immutable: true, default: "unknown", index: true },
//         createdOn: { type: Date, required: true, immutable: true, default: Date.now, index: true }
//     });

//     this.virtual('symptoms', {
//         ref: Symptom,
//         localField: 'input.symptoms',
//         foreignField: 'name',
//         justOne: false
//     });

//     this.virtual('outcomes', {
//         ref: Outcome,
//         localField: 'outcomeNames',
//         foreignField: 'name',
//         justOne: false
//     });
// }


// util.inherits(ServiceCheckSchema, mongoose.Schema);

// const feedbackSchema = new mongoose.Schema({
//     isPositive: { type: Boolean, default: false },
//     messageExists: { type: Boolean, default: false },
//     messageRead: { type: Boolean, default: false },
//     message: { type: String, default: null },
//     createdOn: { type: Date, required: true, immutable: true, default: Date.now, index: true }
// }, { _id: false });


// const generatedServiceCheckSchema = new ServiceCheckSchema();
// generatedServiceCheckSchema.set('minimize', false);
// const GeneratedServiceCheckModel = mongoose.model('generatedservicecheck', generatedServiceCheckSchema);

// const serviceCheckSchema = new ServiceCheckSchema();
// serviceCheckSchema.add({
//     generatedServiceChecks: {
//         type: [{
//             type: mongoose.Schema.Types.ObjectId,
//             ref: GeneratedServiceCheckModel
//         }],
//         default: []
//     },
//     feedback: {
//         type: feedbackSchema,
//         default: null
//     }
// });
// serviceCheckSchema.set('minimize', false);
// const ServiceCheckModel = mongoose.model('servicecheck', serviceCheckSchema);


// const serviceCheckSourceDataSchema = new mongoose.Schema({
//     serviceCheckId: { type: mongoose.Schema.Types.ObjectId, required: true },
//     name: { type: String, required: true },
//     data: { type: mongoose.Schema.Types.Mixed, default: null }
// });
// serviceCheckSourceDataSchema.index({ serviceCheckId: 1, name: 1 }, { unique: true });

// const ServiceCheckSourceData = mongoose.model("serviceCheckSourceData", serviceCheckSourceDataSchema);


/**
 * Make any changes you need to make to the database here
 */
async function up () {
    // Write migration here
    // await migrate.connect();

    // // Moves sources data from servicechecks collection to servicechecksourcedatas collection
    // for await (const record of ServiceCheckModel.find({})) {
    //     let sourcesData = record.get('sourcesData');
    //     if (_.isPlainObject(sourcesData)) {
    //         for (let sourceName in sourcesData) {
    //             let sourceData = sourcesData[sourceName];
    //             await new ServiceCheckSourceData({
    //                 serviceCheckId: record._id,
    //                 name: sourceName,
    //                 data: sourceData
    //             }).save({ checkKeys: false });
    //         }
    //     }

    //     record.sourcesData = undefined;
    //     await record.save({ checkKeys: false });
    // }

    // // Moves sources data from generatedservicechecks collection to servicechecksourcedatas collection
    // for await (const record of GeneratedServiceCheckModel.find({})) {
    //     let sourcesData = record.get('sourcesData');
    //     if (_.isPlainObject(sourcesData)) {
    //         for (let sourceName in sourcesData) {
    //             let sourceData = sourcesData[sourceName];
    //             await new ServiceCheckSourceData({
    //                 serviceCheckId: record._id,
    //                 name: sourceName,
    //                 data: sourceData
    //             }).save({ checkKeys: false });
    //         }
    //     }

    //     record.sourcesData = undefined;
    //     await record.save({ checkKeys: false });
    // }
}

/**
 * Make any changes that UNDO the up function side effects here (if possible)
 */
async function down () {
    // Write migration here
    // await migrate.connect();

    // for await (const record of ServiceCheckModel.find({})) {
    //     let sourcesData = record.get('sourcesData');
    //     if (!_.isPlainObject(sourcesData)) {
    //         record.sourcesData = {};
    //     }

    //     let serviceCheckSourceDatas = await ServiceCheckSourceData.find({ serviceCheckId: record._id });
    //     for (let serviceCheckSourceData of serviceCheckSourceDatas) {
    //         let sourceName = serviceCheckSourceData.name;
    //         let sourceData = serviceCheckSourceData.data;
    //         record.sourcesData[sourceName] = sourceData;
    //     }

    //     record.markModified('sourcesData');
    //     await record.save({ checkKeys: false });

    //     await ServiceCheckSourceData.deleteMany({ serviceCheckId: record._id });
    // }

    // for await (const record of GeneratedServiceCheckModel.find({})) {
    //     let sourcesData = record.get('sourcesData');
    //     if (!_.isPlainObject(sourcesData)) {
    //         record.sourcesData = {};
    //     }

    //     let serviceCheckSourceDatas = await ServiceCheckSourceData.find({ serviceCheckId: record._id });
    //     for (let serviceCheckSourceData of serviceCheckSourceDatas) {
    //         let sourceName = serviceCheckSourceData.name;
    //         let sourceData = serviceCheckSourceData.data;
    //         record.sourcesData[sourceName] = sourceData;
    //     }

    //     record.markModified('sourcesData');
    //     await record.save({ checkKeys: false });

    //     await ServiceCheckSourceData.deleteMany({ serviceCheckId: record._id });
    // }
}

module.exports = { up, down };
