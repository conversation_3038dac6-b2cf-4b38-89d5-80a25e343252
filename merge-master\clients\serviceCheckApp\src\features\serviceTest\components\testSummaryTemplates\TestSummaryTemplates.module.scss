@use '@able/web/src/index' as able;
.testSummaryTemplates {

    .summaryRow {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 48px;
        gap: able.spacing(spacing2x); // Add spacing between elements in the row

        .viewButton {
            padding: able.spacing(spacing1x) able.spacing(spacing2x);
            background-color: able.color(materialBaseSecondary); // Success button color
            color: able.color(textOnPrimary); // Text color for success buttons
            border: none;
            cursor: pointer;
            border-radius: 10px;  
        }

    }
    
}



