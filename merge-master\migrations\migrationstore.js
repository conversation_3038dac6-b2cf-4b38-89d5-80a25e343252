
const mongoose = require('mongoose');

const { connect, disconnect } = require('./config');

const MIGRATIONS_COLLECTION_NAME = 'migrations';
const LAST_RUN_COLLECTION_NAME = 'migrations_last_run';


class Store {
    async load(fn) {
        await connect();

        try {
            let migrations = await mongoose.connection.db.collection(MIGRATIONS_COLLECTION_NAME).find(
                {},
                {
                    title: 1,
                    description: 1,
                    timestamp: 1
                }
            ).toArray();

            let lastRunDocument = await mongoose.connection.db.collection(LAST_RUN_COLLECTION_NAME).findOne({});

            fn(null, {
                lastRun: lastRunDocument ? lastRunDocument.lastRun : null,
                migrations: migrations
            });
        } finally {
            await disconnect();
        }
    }

    async save(set, fn) {
        await connect();

        for (let migration of set.migrations) {
            await mongoose.connection.db.collection(MIGRATIONS_COLLECTION_NAME).findOneAndUpdate(
                {
                    title: migration.title
                },
                {
                    $set: {
                        title: migration.title,
                        description: migration.description,
                        timestamp: migration.timestamp
                    }
                },
                {
                    upsert: true
                }
            );
        }

        await mongoose.connection.db.collection(LAST_RUN_COLLECTION_NAME).findOneAndUpdate(
            {},
            {
                $set: {
                    lastRun: set.lastRun
                }
            },
            {
                upsert: true
            }
        );

        await disconnect();

        fn();
    }
}


module.exports = Store;
