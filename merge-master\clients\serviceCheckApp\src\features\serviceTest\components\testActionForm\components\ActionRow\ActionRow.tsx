import { TextStyle } from '@able/react'
import { faPlay } from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { TestActionsModel } from '../../../../../../infrastructure/models'
import { PopupButton } from '../../../popupButton/PopupButton'
import styles from './ActionRow.module.scss'

export interface ActionRowProps {
    action: TestActionsModel
    handleRunAction: () => void
    handleViewResult: () => void
    canModify: boolean
}

export const ActionRow = ({
    action,
    handleRunAction,
    handleViewResult,
    canModify,
}: ActionRowProps) => {
    return (
        <div className={styles.actionRow}>
            <TextStyle>{action.msg}</TextStyle>
            <div className={styles.actions}>
                {action.result === 'Actioned' && (
                    <PopupButton onClick={handleViewResult} />
                )}
                {action.result === 'Actionable' && canModify && (
                    <button
                        onClick={handleRunAction}
                        className={styles.playButton}
                        aria-label={`Run ${action.msg}`}
                    >
                        <FontAwesomeIcon icon={faPlay} />
                        Run
                    </button>
                )}
            </div>
        </div>
    )
}
