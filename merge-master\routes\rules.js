'use strict';

import express from 'express';
import { body, query, validationResult } from 'express-validator';
import _ from 'lodash';
import escapeStringRegexp from 'escape-string-regexp';

import auth from '../modules/auth.js';
import { AuthorizationRoles } from '../modules/enumerations.js';
import logger from '../modules/logger.js';
import pagination from '../modules/pagination.js';
import mergeRule from '../modules/mergeRule.js';
import validators from '../modules/validators.js';
import Rule from '../db/model/rule.js';

const router = express.Router();


router.get('/', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), [
    query('limit').default(100).isInt({ min: 1 }).toInt(),
    query('offset').default(0).isInt({ min: 0 }).toInt(),
    query('sort').default('name').isString(),
    query('order').default('asc').isString().isIn(['asc', 'desc'])
        .withMessage('Order must be either "asc" or "desc"'),
    query('name').optional().custom(validators.createFilterQueryValidator(["like", "equal"]))
        .customSanitizer(validators.createFilterQuerySanitizer())
        .withMessage("Name filter must contain 'like' or 'equal' operator"),
    query('title').optional().custom(validators.createFilterQueryValidator(["like", "equal"]))
        .customSanitizer(validators.createFilterQuerySanitizer())
        .withMessage("Title filter must contain 'like' or 'equal' operator")
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let limit = req.query.limit;
    let offset = req.query.offset;
    let sort = req.query.sort;
    let orderBy = req.query.order;
    let orderByParam = (orderBy === 'asc') ? 1 : -1;

    let querySort = { [sort]: orderByParam };

    let condition = {};
    let filters = [];

    let queryFilters = {
        name: req.query.name,
        title: req.query.title
    };

    // Goes through the query filters and applies an equality or regex match depending on the operation
    for (let filter in queryFilters) {
        if (queryFilters[filter] != undefined) {
            for (let operation in queryFilters[filter]) {
                switch (operation) {
                    case "equal":
                        filters.push({ [filter]: req.query[filter][operation] });
                        break;
                    case "like":
                        filters.push({ [filter]: { $regex: new RegExp(escapeStringRegexp(req.query[filter][operation]), 'i') }});
                        break;
                }
            }
        }
    }

    if (filters.length > 0) {
        condition = { $and: filters };
    }

    try {
        let [rules, countResult] = await Promise.all([
            Rule.aggregate([
                { $match: condition },
                { $sort: querySort },
                { $skip: offset },
                { $limit: limit },
                { $project: {
                    __v: 0,
                    _id: 0
                }}
            ]).collation({ locale: 'en' }),

            Rule.aggregate([
                { $match: condition },
                { $count: 'total' }
            ]).collation({ locale: 'en' }),
        ]);
        let count = _.get(countResult, [0, 'total'], 0);

        let paginationMetadata = pagination.createPaginationMetadata(req, limit, offset, count);

        let response = {
            metadata: {
                pagination: paginationMetadata,
            },
            results: rules
        };

        res.send(response);
    } catch(error) {
        logger.error(`Get all rules error, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.get('/:name', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), async function(req, res) {
    try {
        let rule = await Rule.findOne({ name: req.params.name });

        if (rule) {
            res.send(rule.toJSON());
        } else {
            res.sendStatus(404);
        }
    } catch(error) {
        logger.error(`Get one rule error, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.post('/', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), async function(req, res) {
    // Removes createdOn from keys if present
    if (req.body) {
        delete req.body.createdOn;
    }

    if (!req.user.isAdmin && global.gConfig.disableRuleSourceEditing) {
        res.status(405).send({
            message: "Rule modification is not allowed"
        });
        return;
    }

    const newRule = new Rule(req.body);

    // Set createdBy from session username
    if (req.user) {
        newRule.createdBy = req.user.username;
    }

    try {
        let rule = await newRule.save();
        res.status(201).send(rule.toJSON());
    } catch(error) {
        if (error.name == "MongoServerError" && error.code == 11000) {
            res.status(409).send({
                error: error.toString()
            });
        } else if (error.name == "ValidationError") {
            res.status(400).send({
                error: error.toString(),
                validation: error.errors
            });
        } else if (error.name == "CastError") {
            res.status(400).send({
                error: error.toString()
            });
        } else {
            logger.error(`Create rule unexpected error, ${error.toString()}`);
            res.status(500).send({
                error: error.toString()
            });
        }
    }
});


router.put('/:name', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), async function(req, res) {
    // Removes createdOn from keys if present
    if (req.body) {
        delete req.body.createdOn;
    }

    if (!req.user.isAdmin && global.gConfig.disableRuleSourceEditing) {
        res.status(405).send({
            message: "Rule modification is not allowed"
        });
        return;
    }

    try {
        let rule = await Rule.findOneAndUpdate({ name: req.params.name }, req.body, {
            runValidators: true,
            strict: true,
            new: true,
            useFindAndModify: false
        });

        if (rule) {
            res.send(rule.toJSON());
        } else {
            res.sendStatus(404);
        }
    } catch(error) {
        if (error.name == "MongoServerError" && error.code == 11000) {
            res.status(409).send({
                error: error.toString()
            });
        } else if (error.name == "ValidationError") {
            res.status(400).send({
                error: error.toString(),
                validation: error.errors
            });
        } else if (error.name == "CastError") {
            res.status(400).send({
                error: error.toString()
            });
        } else {
            logger.error(`Update rule unexpected error, ${error.toString()}`);
            res.status(500).send({
                error: error.toString()
            });
        }
    }
});


router.delete('/:name', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), async function(req, res) {
    if (!req.user.isAdmin && global.gConfig.disableRuleSourceEditing) {
        res.status(405).send({
            message: "Rule modification is not allowed"
        });
        return;
    }

    try {
        let result = await Rule.deleteOne({ name: req.params.name });
        if (result.deletedCount) {
            res.sendStatus(200);
        } else {
            res.sendStatus(404);
        }
    } catch(error) {
        logger.error(`Delete rule error, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.post('/unittest', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), [
    body('rule').notEmpty()
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let rule = req.body.rule;
    let ruleModelInstance;

    try {
        ruleModelInstance = new Rule(rule);
        await ruleModelInstance.validate();
    } catch(error) {
        logger.error(`Error when validating input rule, ${error.toString()}`);
        res.sendStatus(500);
    }

    try {
        let unitTestResult = await mergeRule.runUnitTests(ruleModelInstance);
        res.send(unitTestResult);
    } catch(error) {
        logger.error(`Error when running rule unit test, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.post('/:name/unittest', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), async function(req, res) {
    try {
        let rule = await Rule.findOne({ name: req.params.name });
        if (rule) {
            let unitTestResult = await mergeRule.runUnitTests(rule);
            res.send(unitTestResult);
        } else {
            res.sendStatus(404);
        }
    } catch(error) {
        logger.error(`Error when running rule unit test for rule ${req.params.name}, ${error.toString()}`);
        res.sendStatus(500);
    }
});


export default router;
