FROM "node:20.18.2-bookworm"

# Kafka stream dependency
RUN apt-get -o Acquire::http::proxy="${HTTP_PROXY}" -o Acquire::https::proxy="${HTTPS_PROXY}" update
ENV DEBIAN_FRONTEND noninteractive
RUN apt-get -o Acquire::http::proxy="${HTTP_PROXY}" -o Acquire::https::proxy="${HTTPS_PROXY}" install -y krb5-admin-server
RUN apt-get -o Acquire::http::proxy="${HTTP_PROXY}" -o Acquire::https::proxy="${HTTPS_PROXY}" install -y libsasl2-modules-gssapi-mit libsasl2-dev

# Sets the OpenSSL security level to 1, by default it is 2 and some endpoints
# such as LDAP do not work with level 2
RUN sed -i 's/DEFAULT@SECLEVEL=2/DEFAULT@SECLEVEL=1/g' /etc/ssl/openssl.cnf

WORKDIR /usr/src/app
COPY package.json .
COPY package-lock.json .
COPY tests/init_memory_server.js tests/init_memory_server.js
COPY tests/memory_server.js tests/memory_server.js

# Uncomment the line below if npm install does not work without proxy set command
# RUN npm config set proxy ${HTTP_PROXY}
RUN npm install
# Installs MongoDB memory server into image instead of installing it on every run of the unit tests
RUN node tests/init_memory_server.js
