/**
 * <PERSON><PERSON><PERSON> to load APIs, rules, sources and text templates from config file to the database
 */
import { ArgumentParser } from 'argparse';
import fs from 'fs';
import mongoose from 'mongoose';

import ApiMetadata from '../db/model/apiMetadata.js';
import MessageBucketMetadata from '../db/model/messageBucketMetadata.js';
import OutcomeMetadata from '../db/model/outcomeMetadata.js';
import RuleMetadata from '../db/model/ruleMetadata.js';
import TextTemplateMetadata from '../db/model/textTemplateMetadata.js';
import Api from '../db/model/api.js';
import MessageBucket from '../db/model/messageBucket.js';
import Outcome from '../db/model/outcome.js';
import Rule from '../db/model/rule.js';
import Source from '../db/model/source.js';
import Template from '../db/model/template.js';

const config = JSON.parse(await fs.promises.readFile('./config/config.json'));
const ApiConfig = JSON.parse(await fs.promises.readFile('./config/APIs.json'));
const MessageBucketConfig = JSON.parse(await fs.promises.readFile('./config/messageBuckets.json'));
const OutcomeConfig = JSON.parse(await fs.promises.readFile('./config/outcomes.json'));
const RuleSourceConfig = JSON.parse(await fs.promises.readFile('./config/SCRules.json'));
const TextTemplateConfig = JSON.parse(await fs.promises.readFile('./config/textTemplates.json'));

const parser = new ArgumentParser({
    description: 'Script to export rules / sources file to database.'
});

parser.add_argument('-e', '--env', { default: 'localDev', help: 'Environment to write to' });
parser.add_argument('--reset', { action: 'store_true', help: 'Reset updatedBy user in database' });
parser.add_argument('--force', { action: 'store_true', help: 'Force run even if rule version has not changed' });
parser.add_argument('--skip-delete', { action: 'store_true', help: 'Skips deletion of config objects that do not exist in the config files' });

var args = parser.parse_args();
var environment = args.env;

const METADATA_USER = 'merge-deploy';

if (!config[environment]) {
    console.error(`Could not find environment in config: ${environment}`);
    process.exit(1);
}

let dbCredentials = config[environment].dbUser ? `${config[environment].dbUser}:${config[environment].dbPass}@` : '';
let dbReplicaSet = config[environment].dbReplicaSet ? `?replicaSet=${config[environment].dbReplicaSet}` : '';
let dbUseSsl = config[environment].dbUseSsl;
let dbCaCertFile = config[environment].dbCaCert ? config[environment].dbCaCert : null;
let dbClientCertFile = config[environment].dbClientCert ? config[environment].dbClientCert : null;

let dbUri = `mongodb://${dbCredentials}${config[environment].dbHost}/${config[environment].dbName}${dbReplicaSet}`;

async function loadConfiguration() {
    try {
        await mongoose.connect(dbUri, {
            ssl: dbUseSsl,
            tlsAllowInvalidCertificates: false,
            tlsCAFile: dbCaCertFile,
            tlsCertificateKeyFile: dbClientCertFile
        });

        let loadConfigHasError = false;

        try {
            let config = ApiConfig;

            await loadConfigurationFromFile('APIs', config.version, ApiMetadata, [{
                name: 'APIs',
                data: config.apis,
                model: Api
            }], args.reset, args.force, args.skip_delete);
        } catch(error) {
            console.error(`Error when loading APIs config, ${error.toString()}`);
            loadConfigHasError = true;
        }

        try {
            let config = RuleSourceConfig;

            await loadConfigurationFromFile('rules / sources', config.version, RuleMetadata, [{
                name: 'rules',
                data: config.rules,
                model: Rule
            },
            {
                name: 'sources',
                data: config.sources,
                model: Source
            }], args.reset, args.force, args.skip_delete);
        } catch(error) {
            console.error(`Error when loading rules / sources config, ${error.toString()}`);
            loadConfigHasError = true;
        }

        try {
            let config = TextTemplateConfig;

            await loadConfigurationFromFile('text templates', config.version, TextTemplateMetadata, [{
                name: 'text templates',
                data: config.textTemplates,
                model: Template
            }], args.reset, args.force, args.skip_delete);
        } catch(error) {
            console.error(`Error when loading text templates config, ${error.toString()}`);
            loadConfigHasError = true;
        }

        try {
            let config = OutcomeConfig;

            await loadConfigurationFromFile('outcomes', config.version, OutcomeMetadata, [{
                name: 'outcomes',
                data: config.outcomes,
                model: Outcome
            }], args.reset, args.force, args.skip_delete);
        } catch(error) {
            console.error(`Error when loading outcomes config, ${error.toString()}`);
            loadConfigHasError = true;
        }

        try {
            let config = MessageBucketConfig;

            await loadConfigurationFromFile('message buckets', config.version, MessageBucketMetadata, [{
                name: 'messageBuckets',
                data: config.messageBuckets,
                model: MessageBucket
            }], args.reset, args.force, args.skip_delete);
        } catch(error) {
            console.error(`Error when loading message buckets config, ${error.toString()}`);
            loadConfigHasError = true;
        }

        await mongoose.disconnect();

        if (loadConfigHasError) {
            process.exit(1);
        } else {
            process.exit(0);
        }
    } catch(error) {
        console.error(`Error when uploading configuration to database, ${error.toString()}`);
        process.exit(1);
    }
}


async function loadConfigurationFromFile(configName, version, metadataModel, configs, reset, force, skip_delete) {
    let metadata = await metadataModel.findOne({});

    if (!metadata) {
        console.log(`${configName} metadata not present in database, setting metadata version to 1`);

        metadata = new metadataModel({
            version: 1,
            updatedOn: new Date(),
            updatedBy: METADATA_USER
        });

        await metadata.save();
    }

    if (reset) {
        metadata.updatedBy = 'merge-deploy';
        await metadata.save();
    } else {
        if (metadata.version == version && !force) {
            console.log(`Version in config file currently matches database version ${metadata.version}, skip loading of ${configName}`);
        } else {
            let writePromises = [];

            for (let i = 0; i < configs.length; i++) {
                let config = configs[i];

                let name = config.name;
                let instances = config.data;
                let dataModel = config.model;

                let nameSet = new Set();
                for (let j = 0; j < instances.length; j++) {
                    let instance = instances[j];
                    nameSet.add(instance.name);

                    let dataModelInstance = await dataModel.findOne({ name: instance.name });

                    // Catches validation errors and just prints the current instance name
                    try {
                        // Creates a new instance of a document, ensures default values are set correctly
                        if (!dataModelInstance) {
                            dataModelInstance = new dataModel(instance);
                            dataModelInstance.createdBy = METADATA_USER;
                        } else {
                            Object.assign(dataModelInstance, instance);
                        }

                        await dataModelInstance.validate();
                        writePromises.push(dataModelInstance.save());
                    } catch(error) {
                        console.log(`Error when uploading / validating ${name}: ${instance.name}`);
                        throw error;
                    }
                };

                // Removes any config objects that are present in the database, but not in the config file
                if (!skip_delete) {
                    for await (const instance of dataModel.find({}, { name: 1 })) {
                        if (!nameSet.has(instance.name)) {
                            writePromises.push(dataModel.deleteOne({ name: instance.name }));
                        }
                    }
                }

                console.log(`Writing ${instances.length} ${name} to database`);
            };

            await Promise.all(writePromises);

            // Sets metadata version to the one in config file
            metadata.version = version;
            metadata.updatedOn = new Date();
            metadata.updatedBy = METADATA_USER;
            await metadata.save();

            console.log(`Completed saving ${configName} configuration to database`);
        }
    }
}

loadConfiguration();
