'use strict';

const { connect, disconnect } = require('./config');

async function up() {
    await connect();

    const Rule = (await import('../db/model/rule.js')).default;

    await Rule.updateMany({}, {
        $unset: {
            runOnlyWhenSymptomsPresent: 1
        }
    }, { multi: true, strict: false });

    const ServiceCheck = (await import('../db/model/serviceCheck.js')).default;

    await ServiceCheck.updateMany({}, {
        $unset: {
            'input.symptoms': 1
        }
    }, { multi: true, strict: false });

    const GeneratedServiceCheck = (await import('../db/model/generatedServiceCheck.js')).default;

    await GeneratedServiceCheck.updateMany({}, {
        $unset: {
            'input.symptoms': 1
        }
    }, { multi: true, strict: false });

    await disconnect();
}


async function down() {
    await connect();

    await disconnect();
}


module.exports = { up, down };
