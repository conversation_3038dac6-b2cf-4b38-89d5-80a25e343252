import { PanelRow } from '../../components/detailsPanel/PanelRow'
import { NetworkMap } from '../networkMap/NetworkMap'
import { NextBestAction } from './components/nextBestAction/NextBestAction'
import { TestActionForm } from './components/testActionForm/TestActionForm'
import { TestResults } from './components/testResults/TestResults'
import { TestRunForm } from './components/testRunForm/TestRunForm'

import { TestSummaryTemplates } from './components/testSummaryTemplates/TestSummaryTemplates'

export const ServiceTest = () => {
    return (
        <>
            <PanelRow>
                <>
                    <TestRunForm />
                    <TestActionForm />
                </>
            </PanelRow>

            <NetworkMap />
            <PanelRow>
                <>
                    <NextBestAction />
                    <TestSummaryTemplates />
                </>
            </PanelRow>
            <TestResults />
        </>
    )
}
