import { ActionButton } from '@able/react'
import { useAppState } from '../../hooks/useAppState'

export type MergeClassicViewType = 'view' | 'search'
interface MergeClassicButtonProps {
    viewType: MergeClassicViewType
}

export const MergeClassicButton = ({ viewType }: MergeClassicButtonProps) => {
    const { appState } = useAppState()
    const id = appState.id

    return (
        <ActionButton
            variant={'LowEmphasis'}
            label={'Classic UI'}
            element={'button'}
            events={{
                onClick: () => {
                    // Hack to get full page reload
                    // To update: New UI link (classic link may need changing once updated)
                    window.location.href =
                        viewType === 'view'
                            ? `/serviceCheck/view/html/${id}`
                            : '/serviceCheck/'
                },
            }}
        />
    )
}
