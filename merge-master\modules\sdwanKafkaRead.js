'use strict';

import promClient from 'prom-client';
import kafka from 'node-rdkafka';
import { CronJob } from 'cron';

import '../config/config.js';
import logger from './logger.js';
import Sdwan from '../db/model/sdwan.js';

const promSuccessfulMessageCounter = new promClient.Counter({
    name: 'sdwan_kafka_successful_message_count',
    help: 'Number of successful messages ingested from the SDWAN Kafka Consumer'
});

const promParseErrorMessageCounter = new promClient.Counter({
    name: 'sdwan_kafka_parse_error_message_count',
    help: 'Number of messages which failed to parse as JSON from the SDWAN Kafka Consumer'
});

const promSaveErrorMessageCounter = new promClient.Counter({
    name: 'sdwan_kafka_save_error_message_count',
    help: 'Number of messages which failed to save from the SDWAN Kafka Consumer'
});

const promConsumeErrorMessageCounter = new promClient.Counter({
    name: 'sdwan_kafka_consume_error_message_count',
    help: 'Number of messages which failed to be consumed from the SDWAN Kafka Consumer'
});

const promDocumentCountGauge = new promClient.Gauge({
    name: 'sdwan_document_count',
    help: 'Number of total documents in SDWAN collection'
});

function createKafkaConsumer() {
    const brokerList = global.gConfig.kafkaBrokerListSdwan;
    if (!brokerList) {
        logger.info(`SDWAN Kafka Consumer: broker list null or empty`);
        return null;
    }

    try {
        return kafka.KafkaConsumer({
            'metadata.broker.list': global.gConfig.kafkaBrokerListSdwan,
            'security.protocol': 'SASL_SSL',
            'ssl.ca.location': global.gConfig.kafkasdCaLocation,
            'sasl.mechanisms': 'GSSAPI',
            'sasl.kerberos.service.name': 'kafka',
            'sasl.kerberos.keytab': global.gConfig.kafkasdKeyTab,
            'enable.ssl.certificate.verification': false,
            'sasl.kerberos.principal': global.gConfig.kafkasdPrincipal,
            'debug': 'all',
            'socket.keepalive.enable': true,
            'group.id': global.gConfig.kafkasdConsumerGroup,
        });
    } catch (error) {
        logger.error(`SDWAN Kafka Consumer: error creating consumer, ${error.toString()}`);
        return null;
    }
}

const consumer = createKafkaConsumer();

if (consumer) {
    consumer.on('event.error', (error) => { logger.error(`SDWAN Kafka Consumer: event error, ${error.toString()}`); });
    consumer.on('ready', (arg) => { logger.info(`SDWAN Kafka Consumer: ${arg.name} ready`); });
    consumer.on('error', (error) => { logger.error(`SDWAN Kafka Consumer: error, ${error.toString()}`); });
}

async function setSdwanCountPrometheusMetric() {
    try {
        let count = await Sdwan.countDocuments();
        promDocumentCountGauge.set(count);
    } catch(error) {
        logger.error(`Error when obtaining SDWAN document count, ${error.toString()}`);
    }
}

CronJob.from({
    cronTime: '*/5 * * * *',
    onTick: setSdwanCountPrometheusMetric,
    start: true
});
setSdwanCountPrometheusMetric();


// This function will consume messages
function subscribeConsumer() {
    if (consumer) {
        consumer.subscribe([global.gConfig.kafkasdTopic]);
        logger.info(`SDWAN Kafka Consumer: Subscribed to topic ${global.gConfig.kafkasdTopic}`);

        consumer.consume(async (error, message) => {
            if (error) {
                logger.error(`SDWAN Kafka Consumer: error while consuming message, ${error.toString()}`);
                promConsumeErrorMessageCounter.inc();
            } else {
                let jsonMessage;
                try {
                    jsonMessage = JSON.parse(message.value.toString());
                } catch(error) {
                    // Failed to parse message from Kafka, record prometheus metric and exit
                    promParseErrorMessageCounter.inc();
                    return;
                }

                try {
                    let sdwanInstance = new Sdwan(jsonMessage);
                    await sdwanInstance.save();
                    promSuccessfulMessageCounter.inc();
                } catch(error) {
                    // Failed to save to database
                    promSaveErrorMessageCounter.inc();
                }
            }
        });
    }
}


function connectConsumer(callback) {
    if (consumer){
        logger.info('SDWAN Kafka Consumer: request initiated');
        consumer.connect({ timeout: 5000 }, (error) => {
            if (error) {
                callback(error);
            } else {
                logger.info(`SDWAN Kafka Consumer: connected to the Kafka broker`);
                callback(null);
            }
        });
    }
}


function disconnectConsumer() {
    if (consumer) {
        if (consumer._isConnected) {
            consumer.unsubscribe();
            consumer.disconnect((error) => {
                logger.error(`SDWAN Kafka Consumer: error disconnecting consumer, ${error.toString()}`);
            });
        }
    }
}

export default {
    connectConsumer: connectConsumer,
    disconnectConsumer: disconnectConsumer,
    subscribeConsumer: subscribeConsumer
};
