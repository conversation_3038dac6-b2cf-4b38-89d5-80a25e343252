{"title": "Source", "type": "object", "id": "Source", "headerTemplate": "{{ self.name }}", "options": {"disable_collapse": true, "disable_edit_json": false, "disable_properties": true, "disable_array_add": true, "disable_array_delete": true, "disable_array_reorder": true}, "properties": {"name": {"title": "Name", "type": "string", "description": "Source Name should follow format ${System Name} - ${Action}", "minLength": 3, "readonly": false}, "active": {"title": "Active", "type": "boolean", "format": "checkbox", "default": false}, "hideInResult": {"title": "Hides the source data in the Sources Tab", "type": "boolean", "format": "checkbox", "default": false}, "includedInitialServiceCheck": {"title": "Run source as part of initial service check", "type": "boolean", "format": "checkbox", "default": false}, "title": {"title": "Title", "type": "string", "description": "", "minLength": 0}, "level": {"type": "number", "title": "Level", "enum": [0, 1, 2, 3, 4, 5, 6], "default": 0, "options": {"input_width": "100px"}}, "description": {"title": "Description", "type": "string", "format": "textarea", "description": "", "minLength": 0, "options": {"input_height": "75px"}}, "wikiPage": {"title": "Wiki Page", "type": "string", "description": "Name of Page in Merge Wiki"}, "suite": {"type": "array", "title": "Testing Suites", "uniqueItems": true, "items": {"type": "string", "enum": ["standard", "andig", "ipvoice", "outageInfo", "wholesale"], "readonly": false}, "options": {"disable_array_delete": false, "disable_array_reorder": false}}, "api": {"type": "string", "title": "API", "enum": [], "options": {"input_width": "500px"}}, "preSources": {"type": "array", "format": "table", "title": "Pre-Sources", "description": "Source will be collected after all these Sources are executed", "uniqueItems": true, "items": {"type": "string", "readonly": false}, "options": {"disable_array_delete": false, "disable_array_reorder": false}}, "preSourcesRunOnFail": {"title": "Run even pre-sources are failed or rejected", "type": "boolean", "format": "checkbox", "default": false}, "preRules": {"type": "array", "format": "table", "title": "Pre-Rules", "description": "Source will be collected after all these rules are executed", "uniqueItems": true, "items": {"type": "string", "readonly": false}, "options": {"disable_array_delete": false, "disable_array_reorder": false}}, "preRulesRunOnFail": {"title": "Run even pre-rules failed or rejected (Ticking below option will override this option)", "type": "boolean", "format": "checkbox", "default": false}, "runWhenDependenciesResolved": {"title": "Always collects the source, once all of its dependencies (rules and sources) and further chained dependencies have been completed", "type": "boolean", "format": "checkbox", "default": false}, "preCondition": {"title": "Precondition Code", "type": "string", "format": "javascript", "description": "", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 20}}}, "param": {"title": "API Parameters Code", "type": "string", "format": "javascript", "description": "For access to rules data use 'r.', for source data 's.' and for all data use 'data.'", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 20}}}, "overrideErrorCondition": {"title": "Override Error Condition Code", "type": "string", "format": "javascript", "description": "Expression to override when a source would usually result in an error, such as HTTP 4XX responses. Will only apply if the value evaluated is a true in a boolean context.", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 20}}}, "errorMessage": {"title": "Error Message Code", "type": "string", "format": "javascript", "description": "Use ${response.XXXX ? response.XXXX : ''} to print any returned value like error.", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 20}}}}}