'use strict';

import express from 'express';
const router = express.Router();

import _ from 'lodash';
import { param, query, validationResult } from 'express-validator';
import escapeStringRegexp from 'escape-string-regexp';
import fs from 'fs';

import auth from '../modules/auth.js';
import CommandRun from '../db/model/commandRun.js';
import { AuthorizationRoles } from '../modules/enumerations.js';
import logger from '../modules/logger.js';
import pagination from '../modules/pagination.js';
import validators from '../modules/validators.js';
import { calculateStartDate } from '../modules/helpers/date.js';

import { QuickCommandStreamRead } from '../modules/helpers/csv.js';

const commandsListArr = JSON.parse(await fs.promises.readFile('./config/commands.json'));


/* Command Page */
router.get('/', auth.authorizeForRoles([
    AuthorizationRoles.commandAccess
], true), function(req, res) {
    res.render('commandRun', { title: 'Command' , user: req.user });
});


/* Command Page */
router.get('/list', auth.authorizeForRoles([
    AuthorizationRoles.commandAccess
], true), function(req, res) {
    res.render('commandList', { title: 'Command List' , user: req.user , commands: commandsListArr });
});


router.get('/history/view/:type', auth.authorizeForRoles([
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), function(req, res) {
    let historyType = req.params.type;

    if (historyType === 'all') {
        res.render('commandHistory', { title: 'Quick Command History', scope: 'all', user: req.user, limitCommands: false });
    } else if (historyType === 'my') {
        res.render('commandHistory', { title: 'Quick Command History', scope: 'all', user: req.user, limitCommands: true });
    } else {
        res.status(404).send('Not Found');
    }
});


router.get('/history', auth.authorizeForRoles([
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), [
    query('startDate').customSanitizer(validators.startDateSanitizer).isISO8601().toDate(),
    query('endDate').optional().isISO8601().toDate().custom(validators.isValidEndDate).withMessage('End date must be a valid date and after the start date'),
    query('limit').default(100).isInt({ min: 1 }).toInt(),
    query('offset').default(0).isInt({ min: 0 }).toInt(),
    query('command').optional().custom(validators.createFilterQueryValidator(["like", "equal"]))
        .customSanitizer(validators.createFilterQuerySanitizer())
        .withMessage("Command filter must contain 'like' or 'equal' operator"),
    query('status').optional().isString(),
    query('createdBy').optional().isString(),
    query('sort').default('createdOn').isString(),
    query('order').default('desc').isIn(['asc', 'desc'])
        .withMessage('Order must be either "asc" or "desc"')
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    try {
        let [quickCommands, countResult] = await getCommandsFromRequest(req, req.query.startDate, req.query.endDate, true);
        
        let count = _.get(countResult, [0, 'total'], 0);

        let paginationMetadata = pagination.createPaginationMetadata(req, req.query.limit, req.query.offset, count);
        let metadata = {
            pagination: paginationMetadata,
            startDate: req.query.startDate,
            endDate: req.query.endDate
        };

        res.send({
            metadata: metadata,
            results: quickCommands
        });
    } catch(error) {
        logger.error(`Service check history: error retrieving record history, ${error.toString()}`);
        res.status(500).send({ error: error.message });
    }
});


router.get('/history/download', auth.authorizeForRoles([
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), [
    query('startDate').customSanitizer(validators.startDateSanitizer).isISO8601().toDate(),
    query('endDate').optional().isISO8601().toDate().custom(validators.isValidEndDate).withMessage('End date must be a valid date and after the start date'),
    query('limit').default(100).isInt({ min: 1 }).toInt(),
    query('offset').default(0).isInt({ min: 0 }).toInt(),
    query('command').optional().custom(validators.createFilterQueryValidator(["like", "equal"]))
        .customSanitizer(validators.createFilterQuerySanitizer())
        .withMessage("Command filter must contain 'like' or 'equal' operator"),
    query('status').optional().isString(),
    query('createdBy').optional().isString(),
    query('sort').default('createdOn').isString(),
    query('order').default('desc').isIn(['asc', 'desc'])
        .withMessage('Order must be either "asc" or "desc"')
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    try {
        let dateStr = new Date().toISOString().replace(/[^0-9]/g, '');
        let fileName = `Quick_command_history_${global.gConfig.env}-v${global.gConfig.ver}_${dateStr}.csv`;

        let quickCommandQueryPromise = getCommandsFromRequest(req, req.query.startDate, req.query.endDate, false);

        let cursor = quickCommandQueryPromise.cursor();

        let quickCommandStream = new QuickCommandStreamRead(cursor);
        res.attachment(fileName);
        quickCommandStream.on('error', (error) => {
            logger.error(`Quick command history: error downloading record history in stream, ${error.toString()}`);
            res.end();
        });

        quickCommandStream.pipe(res);
    } catch(error) {
        logger.error(`Quick command history: error downloading record history, ${error.toString()}`);
        res.status(500).send({ error: error.message });
    }
});


router.get('/view/:format/:id', auth.authorizeForRoles([
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), [
    param('id').isString().isLength({ min: 1 }).trim(),
    param('format').isString().isIn(['html', 'json'])
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let inputID = req.params.id;
    let format = req.params.format;

    try {
        let commandRunRecord = await CommandRun.findOne({ id : inputID }).select(['-__v', '-_id']);

        if (commandRunRecord) {
            if (format == 'json') {
                res.send(commandRunRecord);
            } else if (format == 'html') {
                let commandRunRecordEncoded = Buffer.from(JSON.stringify(commandRunRecord)).toString('base64');
                res.render('commandView', {
                    title: 'Command View',
                    command: commandRunRecordEncoded,
                    user: req.user
                });
            }
        } else {
            res.status(404).render('commandViewNotFound', { id : inputID });
        }
    } catch(error) {
        logger.error(`Could not obtain command run ${inputID}, ${error.toString()}`);
        res.sendStatus(500);
    }
});


function getCommandsFromRequest(req, startDate, endDate, includeCount) {
    let limit = req.query.limit;
    let offset = req.query.offset;
    let sort = req.query.sort;
    let orderBy = req.query.order;
    let orderByParam = (orderBy === 'desc') ? -1 : 1;

    let querySort = { [sort]: orderByParam };

    let createdOnFilter = { $gte: startDate };
    if (endDate) {
        createdOnFilter['$lt'] = endDate;
    }

    let commandFilters = [{ createdOn: createdOnFilter }];

    if (req.query.command != undefined) {
        for (let operation in req.query.command) {
            switch (operation) {
                case "equal":
                    commandFilters.push({ command: { $eq: req.query.command[operation] }});
                    break;
                case "like":
                    commandFilters.push({ command: { $regex: new RegExp(escapeStringRegexp(req.query.command[operation]), 'i') }});
                    break;
            }
        }
    }

    // Get all the query parameters that can be used to filter quick commands
    let valueFilters = {
        status: req.query.status,
        createdBy: req.query.createdBy
    };

    // Add each query parameter to condition for quick commands based on equal or like operation
    for (let filter in valueFilters) {
        if (valueFilters[filter] != undefined) {
            commandFilters.push({ [filter]: { $eq: valueFilters[filter] }});
        }
    }

    let condition = { $and: commandFilters };

    let documentAggregate = [
        { $match: condition },
        { $sort: querySort },
        { $skip: offset },
        { $limit: limit },
        { $project: {
            _id: 0,
            id: 1,
            command: 1,
            status: 1,
            createdBy: 1,
            createdOn: 1
        }},
    ];

    if (includeCount) {
        return Promise.all([
            CommandRun.aggregate(documentAggregate).collation({ locale: 'en' }),
            CommandRun.aggregate([
                { $match: condition },
                { $count: 'total' }
            ]).collation({ locale: 'en' })
        ]);
    } else {
        return CommandRun.aggregate(documentAggregate).collation({ locale: 'en' });
    }
}

export default router;

/* Command list for api */
export function getCommandsList() {
    let resJSON = JSON.parse(JSON.stringify(commandsListArr));
    for(let i = 0; i < commandsListArr.length; i++) {
        delete resJSON[i]['title'];
        delete resJSON[i]['type'];
        delete resJSON[i]['validateCode'];
        delete resJSON[i]['paramCode'];
        delete resJSON[i]['help'];
        delete resJSON[i]['timeout'];
        delete resJSON[i]['errorMessage'];
    }
    return resJSON;
};
