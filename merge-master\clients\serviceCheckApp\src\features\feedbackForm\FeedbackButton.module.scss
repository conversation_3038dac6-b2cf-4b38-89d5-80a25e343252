@use '@able/web/src/index' as able;

.feedbackButton {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .buttons {
        display: flex;
        gap: 0.5rem;
    }

    .thumbsUp {
        border: 1px solid able.color(mat);
        border-radius: 10px;
        padding: 0.3rem 0.5rem;
        color: green;
        background-color: white;

        &.activePositive {
            background-color: green;
            color: white;
        }

        &.disabledPositive {
            background-color: #bbbbbb;
            color: #ccffcc;
        }
    }

    .thumbsDown {
        border: 1px solid able.color(mat);
        border-radius: 10px;
        padding: 0.3rem 0.5rem;
        color: red;
        background-color: white;

        &.activeNegative {
            background-color: red;
            color: white;
        }

        &.disabledNegative {
            background-color: #bbbbbb;
            color: #ffcccc;
        }
    }
}

.footer {
    display: flex;
    gap: 1rem;
    justify-content: right;
}
