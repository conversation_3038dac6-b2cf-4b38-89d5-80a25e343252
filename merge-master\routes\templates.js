'use strict';

import express from 'express';
import { body, query, validationResult } from 'express-validator';
import _ from 'lodash';
import escapeStringRegexp from 'escape-string-regexp';

import auth from '../modules/auth.js';
import { AuthorizationRoles } from '../modules/enumerations.js';
import logger from '../modules/logger.js';
import textTemplate from '../modules/textTemplate.js';
import Template from '../db/model/template.js';
import pagination from '../modules/pagination.js';
import validators from '../modules/validators.js';

const router = express.Router();


router.get('/', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), [
    query('limit').default(100).isInt({ min: 1 }).toInt(),
    query('offset').default(0).isInt({ min: 0 }).toInt(),
    query('sort').default('name').isString(),
    query('order').default('asc').isString().isIn(['asc', 'desc'])
        .withMessage('Order must be either "asc" or "desc"'),
    query('name').optional().custom(validators.createFilterQueryValidator(["like", "equal"]))
        .customSanitizer(validators.createFilterQuerySanitizer())
        .withMessage("Name filter must contain 'like' or 'equal' operator"),
    query('title').optional().custom(validators.createFilterQueryValidator(["like", "equal"]))
        .customSanitizer(validators.createFilterQuerySanitizer())
        .withMessage("Title filter must contain 'like' or 'equal' operator"),
    query('description').optional().custom(validators.createFilterQueryValidator(["like", "equal"]))
        .customSanitizer(validators.createFilterQuerySanitizer())
        .withMessage("Description filter must contain 'like' or 'equal' operator"),
    query('templateType').optional().isString()
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let limit = req.query.limit;
    let offset = req.query.offset;
    let sort = req.query.sort;
    let orderBy = req.query.order;
    let orderByParam = (orderBy === 'asc') ? 1 : -1;

    let querySort = { [sort]: orderByParam };
    let filters = [];

    let queryFilters = {
        name: req.query.name,
        title: req.query.title,
        description: req.query.description
    };

    // Goes through the query filters and applies an equality or regex match depending on the operation
    for (let filter in queryFilters) {
        if (queryFilters[filter] != undefined) {
            for (let operation in queryFilters[filter]) {
                switch (operation) {
                    case "equal":
                        filters.push({ [filter]: req.query[filter][operation] });
                        break;
                    case "like":
                        filters.push({ [filter]: { $regex: new RegExp(escapeStringRegexp(req.query[filter][operation]), 'i') }});
                        break;
                }
            }
        }
    }

    let valueFilters = {
        templateType: req.query.templateType
    };

    for (let filter in valueFilters) {
        if (valueFilters[filter] != undefined) {
            filters.push({ [filter]: valueFilters[filter] });
        }
    }

    let condition = filters.length ? { $and: filters } : {};

    try {
        let [templates, countResult] = await Promise.all([
            Template.aggregate([
                { $match: condition },
                { $sort: querySort },
                { $skip: offset },
                { $limit: limit },
                { $project: {
                    __v: 0,
                    _id: 0
                }}
            ]).collation({ locale: 'en' }),
            Template.aggregate([
                { $match: condition },
                { $count: 'total' }
            ]).collation({ locale: 'en' }),
        ]);
        let count = _.get(countResult, [0, 'total'], 0);

        let paginationMetadata = pagination.createPaginationMetadata(req, limit, offset, count);

        let response = {
            metadata: {
                pagination: paginationMetadata,
            },
            results: templates
        };

        res.send(response);
    } catch(error) {
        logger.error(`Get all templates error, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.get('/:name', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), async function(req, res) {
    try {
        let template = await Template.findOne({ name: req.params.name });

        if (template) {
            res.send(template.toJSON());
        } else {
            res.sendStatus(404);
        }
    } catch(error) {
        logger.error(`Get one template error, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.post('/', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), async function(req, res) {
    // Removes createdOn from keys if present
    if (req.body) {
        delete req.body.createdOn;
    }

    if (!req.user.isAdmin && global.gConfig.disableRuleSourceEditing) {
        res.status(405).send({
            message: "Template modification is not allowed"
        });
        return;
    }

    const newTemplate = new Template(req.body);

    // Set createdBy from session username
    if (req.user) {
        newTemplate.createdBy = req.user.username;
    }

    try {
        let template = await newTemplate.save();
        res.status(201).send(template.toJSON());
    } catch(error) {
        if (error.name == "MongoServerError" && error.code == 11000) {
            res.status(409).send({
                error: error.toString()
            });
        } else if (error.name == "ValidationError") {
            res.status(400).send({
                error: error.toString(),
                validation: error.errors
            });
        } else if (error.name == "CastError") {
            res.status(400).send({
                error: error.toString()
            });
        } else {
            logger.error(`Create template unexpected error, ${error.toString()}`);
            res.status(500).send({
                error: error.toString()
            });
        }
    }
});


router.put('/:name', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), async function(req, res) {
    // Removes createdOn from keys if present
    if (req.body) {
        delete req.body.createdOn;
    }

    if (!req.user.isAdmin && global.gConfig.disableRuleSourceEditing) {
        res.status(405).send({
            message: "Template modification is not allowed"
        });
        return;
    }

    try {
        let template = await Template.findOneAndUpdate({ name: req.params.name }, req.body, {
            runValidators: true,
            strict: true,
            new: true,
            useFindAndModify: false
        });

        if (template) {
            res.send(template.toJSON());
        } else {
            res.sendStatus(404);
        }
    } catch(error) {
        if (error.name == "MongoServerError" && error.code == 11000) {
            res.status(409).send({
                error: error.toString()
            });
        } else if (error.name == "ValidationError") {
            res.status(400).send({
                error: error.toString(),
                validation: error.errors
            });
        } else if (error.name == "CastError") {
            res.status(400).send({
                error: error.toString()
            });
        } else {
            logger.error(`Update template unexpected error, ${error.toString()}`);
            res.status(500).send({
                error: error.toString()
            });
        }

        if (error.name == "MongoServerError") {
            if (error.code == 11000) {
                res.status(409).send({
                    error: {
                        message: error.errmsg
                    }
                });
            }
        } else if (error.name == "ValidationError") {
            res.status(400).send({
                error: {
                    validation: error.errors,
                    message: error.message
                }
            });
        } else if (error.name == "CastError") {
            res.status(400).send({
                error: {
                    message: error.message
                }
            });
        } else {

            res.status(500).send({
                error: error.toString()
            });
        }
    }
});


router.delete('/:name', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), async function(req, res) {
    if (!req.user.isAdmin && global.gConfig.disableRuleSourceEditing) {
        res.status(405).send({
            message: "Template modification is not allowed"
        });
        return;
    }

    try {
        let result = await Template.deleteOne({ name: req.params.name });
        if (result.deletedCount) {
            res.sendStatus(200);
        } else {
            res.sendStatus(404);
        }
    } catch(error) {
        logger.error(`Delete template error: ${error.toString()}`);
        res.sendStatus(500);
    }
});


/**
 * Renders template from a service check record and template code in body
 */
router.post('/render', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), [
    body('serviceCheckRecord').isObject(),
    body('parameters').optional().isObject(),
    body('showRulesData').default(false).isBoolean().toBoolean(),
    body('template').isObject()
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    try {
        let serviceCheckRecord = req.body.serviceCheckRecord;
        let parameters = req.body.parameters;
        let template = new Template(req.body.template);

        await template.validate();

        let showRulesData = req.body.showRulesData;
        let enableTemplateModules = template.templateType === 'Service Check';

        let renderedTemplate = await textTemplate.renderTemplate(serviceCheckRecord, template.template, template.formatType, showRulesData, enableTemplateModules, parameters);
        res.send(renderedTemplate);
    } catch(error) {
        res.status(500).send(error.message);
    }
});


export default router;
