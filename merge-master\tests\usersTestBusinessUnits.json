{"readOnlyEnterpriseNetworkManagement": {"username": "readOnlyEnterpriseNetworkManagement", "SSU": "NONSP", "name": "Read Only User (Enterprise Network Management)", "email": "N/A", "department": "Unit Testing", "businessUnit": "AU\\Telstra Group\\Telstra\\Global Networks and Technology\\Commercial Engineering\\Enterprise Service Management\\Enterprise Network Management\\Adaptive Networks Problem Management", "readOnly": true, "level0": false, "level1": false, "level2": false, "level3": false, "isAdmin": false, "isDeveloper": false, "levelLead": false, "apiAccess": false, "commandAccess": false, "isRobot": false, "offshoreAccess": false, "outsideAustralia": false, "sourceAPIAccess": false, "isWholesaleUser": false, "passHash": "$6$08uWvZ1Jk4WS.oUJ$2yuRWfcRaFCq1jvcan5i5uWhsymVKf05lmPxWd23488pC8S/jCI8oDFrw7R8ZOy0GhS6ozdxVf4HvXC1rl35X/"}, "apiAccessReadOnlyEnterpriseNetworkManagement": {"username": "apiAccessReadOnlyEnterpriseNetworkManagement", "SSU": "NONSP", "name": "API Access / Read Only User (Enterprise Network Management)", "email": "N/A", "department": "Unit Testing", "businessUnit": "AU\\Telstra Group\\Telstra\\Global Networks and Technology\\Commercial Engineering\\Enterprise Service Management\\Enterprise Network Management\\Adaptive Networks Problem Management", "readOnly": true, "level0": false, "level1": false, "level2": false, "level3": false, "isAdmin": false, "isDeveloper": false, "levelLead": false, "apiAccess": true, "commandAccess": false, "isRobot": false, "offshoreAccess": false, "outsideAustralia": false, "sourceAPIAccess": false, "isWholesaleUser": false, "passHash": "$6$08uWvZ1Jk4WS.oUJ$2yuRWfcRaFCq1jvcan5i5uWhsymVKf05lmPxWd23488pC8S/jCI8oDFrw7R8ZOy0GhS6ozdxVf4HvXC1rl35X/"}}