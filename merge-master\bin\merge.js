#!/usr/bin/env node
/**
 * Module dependencies.
 */

// uncomment below line to test this code against local environment
//process.env.MERGE_ENV = 'localDev';

// config variables
import debug from 'debug';
import http from 'http';
import mongoose from 'mongoose';
import { Server } from 'socket.io';

import '../config/config.js';
import logger from '../modules/logger.js';
import runningStatus from '../modules/runningStatus.js';
import sdwanKafkaRead from '../modules/sdwanKafkaRead.js';
logger.info(`Starting on Env: ${global.gConfig.env}`);

import app from '../app.js';
import auth from '../modules/auth.js';
import { AuthorizationRoles } from '../modules/enumerations.js';
import expressSession from '../modules/expressSession.js';

import { socketListen as serviceCheckSocketListen } from '../sockets/serviceCheck.js';
import { socketListen as commandSocketListen } from '../sockets/command.js';
import { socketListen as apiDevSocketListen } from '../sockets/apiDev.js';
import { socketListen as ruleDevSocketListen } from '../sockets/ruleDev.js';
import { socketListen as sourceDevSocketListen } from '../sockets/sourceDev.js';
import { socketListen as serviceCheckRecordSearchListen } from '../sockets/serviceCheckRecordSearch.js';

const debugMsg = debug('merge:server');

// Set port from Config, or environment variable MERGE_PORt, default is 3000
const port = normalizePort(global.gConfig.listen_port || process.env.MERGE_PORT || 3000);
app.set('port', port);
logger.info(`Listening on port: ${port}`);
/**
 * Create HTTP server.
 */

const server = http.createServer(app);
server.keepAliveTimeout = 60000;


/**
 * Listen on provided port, on all network interfaces.
 */

server.listen(port);

server.on('error', onError);
server.on('listening', onListening);

// 10MB limit for socket.io size
// Sets ping timeout to 5 minutes as large payloads through socket.io can have a delay when
// communicating with a client over a slow connection
const socketConfig = {
  maxHttpBufferSize: 1e7,
  pingTimeout: 300000
};

const io = new Server(server, socketConfig);

// Uses passport authentication to verify socket connection is valid

const wrap = middleware => (socket, next) => middleware(socket.request, {}, next);

io.use(wrap(expressSession));
io.use(wrap(auth.initialize));
io.use(wrap(auth.session));

io.use((socket, next) => {
  if (socket.request.user) {
    next();
  } else {
    // Passing error object here means that socket.io won't call the function for the 'connection' event
    next(new Error('socket.io unauthorized user'));
  }
});


io.on('connection', function (socket) {
  logger.info(`Socket connected from IP "${socket.handshake.address}" by ID "${socket.id}"`);

  // Sets up socket listeners from each module and checks
  // user permissions required to access each one
  if (socket.request && socket.request.user) {
    if (auth.authorizeForRolesSocket([
      AuthorizationRoles.level0,
      AuthorizationRoles.level1,
      AuthorizationRoles.level2,
      AuthorizationRoles.level3,
      AuthorizationRoles.commandAccess,
      AuthorizationRoles.isDeveloper,
      AuthorizationRoles.isAdmin,
      AuthorizationRoles.levelLead
    ], socket.request.user)) {
      serviceCheckSocketListen(socket);
    }

    if (auth.authorizeForRolesSocket([
      AuthorizationRoles.commandAccess
    ], socket.request.user)) {
      commandSocketListen(socket);
    }

    if (auth.authorizeForRolesSocket([
      AuthorizationRoles.isDeveloper,
      AuthorizationRoles.isAdmin,
      AuthorizationRoles.levelLead
    ], socket.request.user)) {
      ruleDevSocketListen(socket);
      sourceDevSocketListen(socket);
    }

    if (auth.authorizeForRolesSocket([
      AuthorizationRoles.isAdmin
    ], socket.request.user)) {
      apiDevSocketListen(socket);
    }

    if (auth.authorizeForRolesSocket([
      AuthorizationRoles.isAdmin,
      AuthorizationRoles.isDeveloper,
    ], socket.request.user)) {
      serviceCheckRecordSearchListen(socket);
    }
  }
});

/**
 * Normalize a port into a number, string, or false.
 */

function normalizePort(val) {
  var port = parseInt(val, 10);

  if (isNaN(port)) {
    // named pipe
    return val;
  }

  if (port >= 0) {
    // port number
    return port;
  }

  return false;
}

/**
 * Event listener for HTTP server "error" event.
 */

function onError(error) {
  if (error.syscall !== 'listen') {
    throw error;
  }

  var bind = typeof port === 'string'
    ? 'Pipe ' + port
    : 'Port ' + port;

  // handle specific listen errors with friendly messages
  switch (error.code) {
    case 'EACCES':
      logger.error(bind + ' requires elevated privileges');
      process.exit(1);
    case 'EADDRINUSE':
      logger.error(bind + ' is already in use');
      process.exit(1);
    default:
      throw error;
  }
}

/**
 * Event listener for HTTP server "listening" event.
 */

function onListening() {
  var addr = server.address();
  var bind = typeof addr === 'string'
    ? 'pipe ' + addr
    : 'port ' + addr.port;
  debugMsg('Listening on ' + bind);
}


process.on('SIGTERM', async () => {
  logger.info('SIGTERM signal handler: received');
  await server.close();
  logger.info('SIGTERM signal handler: closed server for new connections');

  // TODO: Implement a better solution
  // Since the serviceCheck.collectAllSources function is recursive with no callback or promises
  // it is not easy to track whether all service checks are complete.
  // For now, this implementation checks the service check status list every 5 seconds and will terminate
  // the function once it is
  let serviceCheckStatus = runningStatus.serviceCheckGetStatus();
  while (Object.keys(serviceCheckStatus).length > 0) {
    await new Promise(resolve => setTimeout(resolve, 5000));
    serviceCheckStatus = runningStatus.serviceCheckGetStatus();
  }

  logger.info('SIGTERM signal handler: no service checks are running, application will terminate');

  await io.close();
  await mongoose.disconnect();
  sdwanKafkaRead.disconnectConsumer();

  process.exit(0);
});
