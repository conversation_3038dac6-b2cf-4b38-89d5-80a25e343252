import { ActionButton, ModalContent, TextArea, TextStyle } from '@able/react'

import { faThumbsDown, faThumbsUp } from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import classNames from 'classnames'
import { useState } from 'react'
import { useAddOrModifyFeedback } from '../../api/mutations/useAddOrModifyFeedback'
import { useRemoveFeedback } from '../../api/mutations/useRemoveFeedback'
import { useAppState } from '../../hooks/useAppState'
import { ServiceTestFeedbackRequest } from '../../infrastructure/models'
import { LoadingPanel } from '../serviceTest/components/serviceCheckLoadingPanel/LoadingPanel'
import { useUser } from '../../context/UserContext'
import styles from './FeedbackButton.module.scss'

export const FeedbackButton = () => {
    const [isModalOpen, setIsModalOpen] = useState(false)
    const { mutate: addOrModifyFeedbackMutate, isPending: isAddingFeedback } =
        useAddOrModifyFeedback()
    const { mutate: removeFeedbackMutate, isPending: isDeletePending } =
        useRemoveFeedback()
    const { appState, setAppState } = useAppState()
    const [feedbackText, setFeedbackText] = useState(
        appState.serviceDetails.feedback?.message ?? ''
    )
    const [positiveHit, setPositiveHit] = useState<boolean>(false)
    const { canModify } = useUser()
    const feedbackIsDisabled = !canModify(appState.createdBy)

    const maxCharacters = 4000
    const remainingCharacters = maxCharacters - feedbackText.length
    const isInvalid = feedbackText.length > maxCharacters

    const onDelete = () => {
        removeFeedbackMutate(appState.id, {
            onSuccess: () => {
                setIsModalOpen(false)
                setAppState({
                    ...appState,
                    serviceDetails: {
                        ...appState.serviceDetails,
                        feedback: null,
                    },
                })
            },
            onError: () => {
                setIsModalOpen(false)
            },
        })
    }
    const onSubmit = () => {
        if (isInvalid) return
        const variables: ServiceTestFeedbackRequest = {
            serviceCheckId: appState.id,
            feedback: {
                isPositive: positiveHit ?? false,
                message: feedbackText,
            },
        }

        addOrModifyFeedbackMutate(variables, {
            onSuccess: () => {
                setAppState({
                    ...appState,
                    serviceDetails: {
                        ...appState.serviceDetails,
                        feedback: feedback
                            ? {
                                  ...feedback,
                                  isPositive: positiveHit,
                                  message: feedbackText,
                                  messageExists: feedbackText.length > 0,
                              }
                            : {
                                  isPositive: positiveHit,
                                  message: feedbackText,
                                  messageExists: feedbackText.length > 0,
                                  messageRead: false,
                                  createdOn: Date.now().toLocaleString(),
                              },
                    },
                })
                setIsModalOpen(false)
            },
            onError: () => {
                setIsModalOpen(false)
            },
        })
    }
    const feedback = appState.serviceDetails.feedback

    const hasProvidedFeedback = !!feedback
    const existingIsPositive = feedback?.isPositive

    const handleThumbsUp = () => {
        setPositiveHit(true)
        setIsModalOpen(true)
    }

    const handleThumbsDown = () => {
        setPositiveHit(false)
        setIsModalOpen(true)
    }

    return (
        <>
            <div className={styles.feedbackButton}>
                <TextStyle alias="LabelA1">Feedback</TextStyle>
                <div className={styles.buttons}>
                    <button
                        className={classNames(styles.thumbsDown, {
                            [styles.activeNegative]:
                                hasProvidedFeedback && !existingIsPositive,
                            [styles.disabledNegative]:
                                feedbackIsDisabled
                        })}
                        onClick={handleThumbsDown}
                        disabled={feedbackIsDisabled}
                    >
                        <FontAwesomeIcon icon={faThumbsDown} size="1x" />
                    </button>
                    <button
                        className={classNames(styles.thumbsUp, {
                            [styles.activePositive]:
                                hasProvidedFeedback && existingIsPositive,
                            [styles.disabledPositive]:
                                feedbackIsDisabled
                        })}
                        onClick={handleThumbsUp}
                        disabled={feedbackIsDisabled}
                    >
                        <FontAwesomeIcon icon={faThumbsUp} size="1x" />
                    </button>
                </div>
            </div>

            <ModalContent
                variant={'Expansive'}
                bodyContent={
                    isAddingFeedback || isDeletePending ? (
                        <LoadingPanel />
                    ) : (
                        <TextArea
                            helpText={`${remainingCharacters} characters remaining`}
                            name={'additionalDetails'}
                            value={feedbackText}
                            events={{
                                onChange: (
                                    e: React.FormEvent<HTMLInputElement>
                                ) => {
                                    setFeedbackText(e.currentTarget.value)
                                },
                            }}
                            label={
                                'Please provide further details as to what can be improved for this service check'
                            }
                            invalid={isInvalid}
                            invalidInputText={`Feedback exceeds the maximum allowed length of ${maxCharacters} characters`}
                            developmentUrl="/public/images/able-sprites.svg"
                        />
                    )
                }
                footerContent={
                    <div className={styles.footer}>
                        {hasProvidedFeedback && (
                            <ActionButton
                                variant={'HighEmphasisDestructive'}
                                label={'Remove Feedback'}
                                element={'button'}
                                events={{ onClick: onDelete }}
                                developmentUrl="/public/images/able-sprites.svg"
                            />
                        )}
                        <ActionButton
                            variant={'MediumEmphasis'}
                            label={'Send Feedback'}
                            element={'button'}
                            events={{ onClick: onSubmit }}
                            developmentUrl="/public/images/able-sprites.svg"
                        />
                    </div>
                }
                setHideDialog={() => setIsModalOpen(false)}
                isShowing={isModalOpen}
                title={'Feedback'}
                developmentUrl="/public/images/able-sprites.svg"
            />
        </>
    )
}
