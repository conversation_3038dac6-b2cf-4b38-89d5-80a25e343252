@use '@able/web/src/index' as able;

.zoomControls {
    display: flex;
    align-items: center;
    gap: 0.5rem; // Space between label, slider, and value

    margin-top: 0.5rem;
    margin-bottom: 1.5rem;
    background-color: able.color(materialBaseSecondary);
    position: sticky;
    top: 0; // Adjust this value to control when it "sticks"
    left: 0;
    padding: 0.5rem;
    width: fit-content;
    border-radius: 10px;

    z-index: 10; // Ensure it appears above other content if needed

    input[type='range'] {
        -webkit-appearance: none;
        width: 300px;
        height: 6px;
        background: able.color(materialBaseBrandTertiary);
        border-radius: 5px; // Rounded track
        outline: none;
        border: none;

        &::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 16px;
            height: 16px;
            background: able.color(materialBaseBrandPrimary);
            border-radius: 50%;
            cursor: pointer;
        }

        &::-moz-range-thumb {
            width: 16px;
            height: 16px;
            background: able.color(materialBaseBrandPrimary);
            border-radius: 50%;
            cursor: pointer;
        }
    }
}
