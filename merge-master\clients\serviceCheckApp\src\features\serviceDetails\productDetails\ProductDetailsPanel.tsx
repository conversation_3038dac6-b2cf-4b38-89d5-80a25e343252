import classNames from 'classnames'
import { DetailsPanel } from '../../../components/detailsPanel/DetailsPanel'
interface ProductDetailsPanelProps {
    children: React.ReactElement
    className?: string
}

export const ProductDetailsPanel = ({
    children,
    className,
}: ProductDetailsPanelProps) => (
    <DetailsPanel label="Product Details" className={classNames(className)}>
        {children}
    </DetailsPanel>
)
