@use '@able/web/src/index' as able;

.actionList {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 1rem;
}

.actionRow {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 48px;
    gap: able.spacing(spacing2x); // Add spacing between elements in the row
}

.results {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.actions {
    display: flex;
    gap: 1rem;

    .playButton {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
        padding: able.spacing(spacing1x) able.spacing(spacing2x);
        background-color: able.color(
            materialBaseBrandPrimary
        ); // Primary button color
        color: able.color(
            materialBasePrimary
        ); // Text color for primary buttons
        border: none;
        cursor: pointer;
        border-radius: 10px;
    }

    .viewButton {
        padding: able.spacing(spacing1x) able.spacing(spacing2x);
        background-color: able.color(
            materialBaseSecondary
        ); // Success button color
        color: able.color(textOnPrimary); // Text color for success buttons
        border: none;
        cursor: pointer;
        border-radius: 10px;
    }
}
