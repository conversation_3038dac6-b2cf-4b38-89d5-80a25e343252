<!DOCTYPE html>
<html>

<head>
    <title><%= typeof title !== 'undefined' ? title : '' %></title>
    <script >
        if( /msie|trident|edge/g.test(navigator.userAgent.toLowerCase()) ){
            alert('Merge does not support Internet Explorer! Use Chrome.');
        }
    </script>
    <link rel="stylesheet" href="/public/stylesheets/bootstrap.min.css" integrity="sha384-B0vP5xmATw1+K9KRQjQERJvTumQW0nPEzvF6L/Z6nronJ3oUOFUFpCjEUQouq2+l" crossorigin="anonymous">
    <script src="/public/javascripts/jquery.min.js" crossorigin="anonymous"></script>
    <script src="/public/javascripts/popper.min.js" integrity="sha384-ZMP7rVo3mIykV+2+9J3UJ46jBk0WLaUAdn689aCwoqbBJiSnjAK/l8WvCWPIPm49" crossorigin="anonymous"></script>
    <script src="/public/javascripts/bootstrap.min.js" integrity="sha384-+YQ4JLhjyBLPDQt//I+STsc9iw4uQqACwlvpslubQzn4u2UU2UFM80nGisd026JF" crossorigin="anonymous"></script>
    <link rel="stylesheet" href="/public/stylesheets/fontawesome.all.min.css">
    <link rel="stylesheet" href="/public/stylesheets/merge.css">
    <script src="/public/javascripts/clipboard.min.js" crossorigin="anonymous"></script>
    <script src="/public/javascripts/purify.min.js" crossorigin="anonymous"></script>
    <script src="/public/javascripts/merge_lib.js"></script>
    
    <!-- REACT-STATIC-FILES -->
    <style>
        .container {
            max-width: 3600px;
            width: 90%;
            padding-right: 50px;
            padding-left: 50px;
            color : #373737
        }
        .fa-disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .modal-xlg {
            max-width: 80%;
        }
        .modal-xsml {
            max-width: 30%;
        }
        .TelstraThem {
            background-color: #0099f8;
            color : #373737
        }

        body {
            font-family: 'Telstra Akkurat', 'Akkurat','Lato', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        h1,h2,h3,h4,h5,h6 {
            font-family: 'Telstra Akkurat', 'Akkurat', 'Lato', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            font-weight: 700;
        }
    </style>
    <% if(global.gMatomoTracker) { %>
        <!-- Matomo -->
        <script type="text/javascript">
        var _paq = window._paq = window._paq || [];
        /* tracker methods like "setCustomDimension" should be called before "trackPageView" */
        _paq.push(['trackPageView']);
        _paq.push(['enableLinkTracking']);
        (function() {
            var u="https://hangar.in.telstra.com.au/matomo/";
            _paq.push(['setTrackerUrl', u+'matomo.php']);
            _paq.push(['setSiteId', '19']);
            var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];
            g.type='text/javascript'; g.async=true; g.src=u+'matomo.js'; s.parentNode.insertBefore(g,s);
        })();
        </script>
        <!-- End Matomo Code -->
     <% } %>
</head>
<body>
    <!--[if IE]>
    <div class="alert alert-danger" role="alert">
            <br />
            <br />
        <h2 style="color:red;">Merge only works on Firefox or Chrome and does not support Internet Explorer2</h2><br />
    </div>
    <![endif]-->
