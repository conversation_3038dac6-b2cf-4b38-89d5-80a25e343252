<%- include('header') %>
<%- include('jsonEditInclude', {}) %>
<%- include('menu', {currentTab: 'Form'}); %>
<link rel="stylesheet" type="text/css" href="/public/stylesheets/select2.min.css">
<script src="/socket.io/socket.io.js"></script>
<script src="/public/javascripts/select2.min.js" crossorigin="anonymous"></script>
<script src="/public/javascripts/merge_SC_lib.js" crossorigin="anonymous"></script>
<br>
<div class="container">
    <div class="row">
        <div class="col-8">
            <a href="/edit/sources">< Back to sources</a>
        </div>
        <div class="col-4">
            <div class="float-right">
                <button class="btn btn-primary" id="createSource" data-toggle="modal" data-target="#confirmCreateModal" style="display:none;" title="Create" disabled><span class="fas fa-plus-square"></span></button>
                <button class="btn btn-primary" id="saveSource" data-toggle="modal" data-target="#confirmSaveModal" style="display:none;" title="Save" disabled><span class="fas fa-save"></span></button>
                <button class="btn btn-primary" id="refreshSource" data-toggle="modal" data-target="#confirmRefreshModal"  style="display:none;" title="Refresh"><span class="fas fa-sync"></span></button>
                <button class="btn btn-danger" id="deleteSource" data-toggle="modal" data-target="#confirmDeleteModal" style="display:none;" title="Delete" disabled><span class="fas fa-trash"></span></button>
            </div>
        </div>
    </div>
    <br>
    <div class="row">
        <div class="col-12">
            <div id="readonlyAlert" style="display:none;" class="alert alert-warning">Warning: Source is read only, editing is disabled.</div>
        </div>
    </div>

    <ul class="nav nav-tabs" role="tablist">
        <li role="presentation" class="nav-item active">
            <a class="nav-link active" data-target="#editSourcesTab" data-toggle="tab" role="tab" href="">Edit Source</a>
        </li>
        <li role="presentation" class="nav-item">
            <a class="nav-link" data-target="#executeSourcesTab" data-toggle="tab" role="tab" href="">Execute Source</a>
        </li>
    </ul>

    <div class="tab-content">
        <div class="tab-pane active border" id="editSourcesTab" style="height:75vh;overflow-y:auto;padding-bottom:16px;">
            <div style="padding: 2rem;">
                <div id="sourcesLoadError" class="alert alert-danger" role="alert" style="display:none;padding-left:16px;"></div>
                <div id="sourcesEdit" style="display:none;"></div>
            </div>
        </div>
        <div class="tab-pane border" id="executeSourcesTab" style="height:75vh;overflow-y:auto;padding-bottom:16px;">
            <div style="padding: 2rem;">
                <div class="row border border-primary rounded bg-light">
                    <div class="card-body">
                        <div id="developDiffStatus" class="alert"></div>
                        <div class="form-row">
                            <button class="btn btn-outline-primary ml-1 mr-1" id="developSaveCode" title="Save code to &quot;Edit Source&quot; tab"><span class="fas fa-save"></span></button>
                            <button class="btn btn-outline-primary ml-1 mr-1" id="developRefreshCode" title="Reset code to values in &quot;Edit Source&quot; tab"><span class="fas fa-undo"></span></button>
                            <label id="developSaveTime" class="col-form-label"></label>
                        </div>
                        <div class="form-group">
                            <label class="col-form-label">Automatic Save</label>
                            <div class="form-check form-check-inline ml-2 mr-2">
                                <input class="form-check-input" type="radio" name="saveOptionRadios" id="saveOptionDisabled" value="disabled">
                                <label class="form-check-label" for="saveOptionDisabled">
                                    Disabled
                                </label>
                            </div>
                            <div class="form-check form-check-inline ml-2 mr-2">
                                <input class="form-check-input" type="radio" name="saveOptionRadios" id="saveOptionInterval" value="interval">
                                <label class="form-check-label mr-1" for="saveOptionInterval">
                                    Save every
                                </label>
                                <select id="saveOptionIntervalLength" style="width: 4em;">
                                    <option value=10 selected>10</option>
                                    <option value=30>30</option>
                                    <option value=60>60</option>
                                    <option value=300>300</option>
                                    <option value=600>600</option>
                                </select>
                                <label class="form-check-label ml-1" for="saveOptionInterval">
                                    seconds
                                </label>
                            </div>
                        </div>
                        <label class="col-form-label">Editor options:</label>
                        <div class="form-row">
                            <div class="ml-2 mr-2">
                                <input id="editorOptionWordWrap" type="checkbox">
                                <label class="col-form-label" for="editorOptionWordWrap">Word Wrap</label>
                            </div>
                            <div class="ml-2 mr-2">
                                <label class="col-form-label" for="editorOptionFontSize">Font Size</label>
                                <select id="editorOptionFontSize">
                                    <option value="8">8</option>
                                    <option value="10" selected>10</option>
                                    <option value="12">12</option>
                                    <option value="14">14</option>
                                    <option value="16">16</option>
                                </select>
                            </div>
                            <div class="ml-2 mr-2">
                                <label class="col-form-label" for="editorOptionTheme">Theme</label>
                                <select id="editorOptionTheme">
                                    <optgroup label="Light">
                                        <option value="ace/theme/textmate" selected>Default (Textmate)</option>
                                        <option value="ace/theme/eclipse">Eclipse</option>
                                        <option value="ace/theme/github">Github</option>
                                        <option value="ace/theme/xcode">Xcode</option>
                                    </optgroup>
                                    <optgroup label="Dark">
                                        <option value="ace/theme/cobalt">Cobalt</option>
                                        <option value="ace/theme/monokai">Monokai</option>
                                        <option value="ace/theme/tomorrow_night">Tomorrow Night</option>
                                        <option value="ace/theme/twilight">Twilight</option>
                                    </optgroup>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <br>
                <div class="row">
                    <div class="col-6 border rounded codeBackground" style="padding:8px;">
                        <label for="developSourceVariables" class="codeLabel">Input Variables</label>
                        <button id="resetInputVariables" class="btn btn-primary btn-sm" title="Reset"><span class="fas fa-undo"></span></button>
                        <small class="form-text codeLabel">If r or s are undefined / null / empty objects, the <b>rulesData</b> and <b>sourcesData</b> field will be used instead.</small>
                        <div class="alert alert-warning" role="alert" id="sourceVariablesSizeAlert" style="display:none;">
                            <small>The input variables text exceeds 8MB. This may cause errors when running condition to collect / API parameters code.</small>
                        </div>
                        <div id="developSourceVariables" style="height: 100%; width: 100%;"></div>
                    </div>
                    <div class="col-6 border rounded codeBackground" style="padding:8px;">
                        <label for="outputVariables" class="codeLabel">Output Data</label><div class="float-right"><select id="outputVariablesSelect"></select></div>
                        <div id="outputVariables" style="height: 100%; width: 100%;"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 border rounded codeBackground" style="padding:8px;">
                        <label for="developSourcePreCondition" class="codeLabel">Precondition Code</label>
                        <button id="runSourcePreCondition" class="btn btn-success btn-sm">Run</button>
                        <br>
                        <small class="codeLabel">Expression that when evaluated as <b>true</b> in a Boolean context, will result in the source being collected. The source also will be collected if this field is empty.</small>
                        <div class="alert p-1" role="alert" id="sourcePreConditionResult" style="display:none;">
                            <small id="sourcePreConditionResultText"></small>
                        </div>
                        <div id="developSourcePreCondition" style="height: 100%; width: 100%;"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 border rounded codeBackground" style="padding:8px;">
                        <label for="developSourceParameters" class="codeLabel">API Parameters Code</label>
                        <button id="runSourceParameters" class="btn btn-success btn-sm">Run</button>
                        <button id="cancelRunSourceParameters" class="btn btn-secondary btn-sm" title="Cancels collection of source in the case of socket.io disconnect or other cases where no response is received" style="display:none;">Cancel</button>
                        <br>
                        <small class="codeLabel">Declares parameters required for API use by this source. Any variables in the sandbox declared here can be accessed with <b>parameters.&lt;parameter name&gt;</b> from configured APIs.</small>
                        <div class="alert alert-danger" role="alert" id="sourceRunAlert" style="display:none;">
                            <small>An error occurred while running the source, please check the console at the bottom of the page for details</small>
                        </div>
                        <div id="developSourceParameters" style="height: 100%; width: 100%;"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 border rounded codeBackground" style="padding:8px;">
                        <label for="developOverrideErrorCondition" class="codeLabel">Override Error Condition Code</label>
                        <br>
                        <small class="codeLabel">Expression that will run when source collection results in an error and when evaluated as <b>true</b> in a Boolean context, will override the default behaviour to change the source collection status to "Collected".</small>
                        <div id="developOverrideErrorCondition" style="height: 100%; width: 100%;"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 border rounded codeBackground" style="padding:8px;">
                        <label for="developErrorMessage" class="codeLabel">Error Message Code</label>
                        <br>
                        <small class="codeLabel">Expression that should resolve to a <b>string</b> that will display a custom error message visible in the Summary tab (when the source has an error during collection).</small>
                        <div id="developErrorMessage" style="height: 100%; width: 100%;"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 border rounded bg-light" style="padding:8px;">
                        <label for="Results" class="text-dark">Sources Output</label>
                        <br>
                        <small id="sourcesOutputUpdatedAt" class="text-muted"></small>
                        <div id="Results"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 border rounded bg-light" style="padding:8px;">
                        <label for="developOutputConsole" class="text-dark">Console</label>
                        <div id="developOutputConsole" class="border" style="height: 100%; width: 100%; max-height: 2160px; resize: vertical; overflow: hidden;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="createSourceModal" class="modal fade">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Error</h5>
            </div>
            <div class="modal-body">
                <code id="createSourceModalMessage" style="white-space:pre-wrap;"></code>
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-outline-secondary">Close</button>
            </div>
        </div>
    </div>
</div>

<div id="confirmSaveModal" class="modal fade">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Save</h5>
            </div>
            <div class="modal-body">
                Would you like to save the changes for this source?
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-primary" id="confirmSave">Save</button>
                <button type="button" data-dismiss="modal" class="btn btn-outline-secondary">Cancel</button>
            </div>
        </div>
    </div>
</div>

<div id="confirmRefreshModal" class="modal fade">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Refresh</h5>
            </div>
            <div class="modal-body">
                Would you like to refresh the source contents? Any changes made will be lost.
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-primary" id="confirmRefresh">Refresh</button>
                <button type="button" data-dismiss="modal" class="btn btn-outline-secondary">Cancel</button>
            </div>
        </div>
    </div>
</div>

<div id="confirmDeleteModal" class="modal fade">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
            </div>
            <div class="modal-body">
                Would you like to delete this source? This action cannot be reversed.
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-danger" id="confirmDelete">Delete</button>
                <button type="button" data-dismiss="modal" class="btn btn-outline-secondary">Cancel</button>
            </div>
        </div>
    </div>
</div>

<script>
    const sourceName = atob("<%- sourceName %>");
    const wikiBaseURL = "<%= global.gConfig.wikiBaseURL %>";
    const createSource = <%= createSource %>;
    const disableRuleSourceEditing = <%- user.isAdmin ? false : global.gConfig.disableRuleSourceEditing %>;
    const socket = io();
    const schema = JSON.parse(atob("<%- schema %>"));

    const INPUT_VARIABLES_DEFAULT = "r = {};\ns = {};\ndata = {\n    id: \"sourceSandbox\",\n    fnn: \"fnn\"\n};";

    var dataLoaded = false;
    var dataModified = false;
    var autosaveInterval = null;

    $(document).ready(function() {
        $("#createSource").prop("disabled", disableRuleSourceEditing);
        $("#saveSource").prop("disabled", disableRuleSourceEditing);
        $("#deleteSource").prop("disabled", disableRuleSourceEditing);
        $("#developSaveCode").prop("disabled", disableRuleSourceEditing);

        let jsonEditor = new JSONEditor(document.getElementById('sourcesEdit'), {
            ajax: false,
            enable_array_copy: false,
            schema: schema,
            startval: null,
            no_additional_properties: true,
            required_by_default: true,
            show_errors: "always"
        });

        let preConditionTimeout;

        if (disableRuleSourceEditing) {
            jsonEditor.disable();
            $("input[name='saveOptionRadios']").attr("disabled", true);
            $("#readonlyAlert").show();
        }

        if (createSource) {
            $("#createSource").show();
            $("#sourcesEdit").show();
        } else {
            $("#saveSource").show();
            $("#refreshSource").show();
            $("#deleteSource").show();
            getSourceToForm(jsonEditor);
        }

        const aceEditors = setupDevelopmentEditors();
        const codeEditors = Object.freeze({
            preCondition: aceEditors.sourcePreCondition,
            param: aceEditors.sourceParameters,
            overrideErrorCondition: aceEditors.sourceOverrideErrorCondition,
            errorMessage: aceEditors.sourceErrorMessage
        });
        var outputVariables;

        $("#createSource").click(function() {
            createSourceFromForm(jsonEditor);
        });

        $("#confirmSave").click(function() {
            writeSourceFromForm(jsonEditor);
        });

        $("#confirmRefresh").click(function() {
            getSourceToForm(jsonEditor);
        });

        $("#confirmDelete").click(function() {
            $.ajax({
                type: "DELETE",
                url: `/sources/${encodeURIComponent(sourceName)}`,
                success: function (response) {
                    window.location.href = "/edit/sources";
                },
                error: function (error) {
                    alert("Error, could not delete source:" + error.responseText);
                },
            });
        });

        $("#developSaveCode").click(function() {
            jsonEditor.getEditor('root.preCondition').setValue(codeEditors.preCondition.getValue());
            jsonEditor.getEditor('root.param').setValue(codeEditors.param.getValue());
            jsonEditor.getEditor('root.overrideErrorCondition').setValue(codeEditors.overrideErrorCondition.getValue());
            jsonEditor.getEditor('root.errorMessage').setValue(codeEditors.errorMessage.getValue());

            updateCodeDiffStatus(jsonEditor, codeEditors);
            updateSaveTimeLabel();
        });

        $("#developRefreshCode").click(function() {
            let source = jsonEditor.getValue();
            codeEditors.preCondition.setValue(source.preCondition);
            codeEditors.preCondition.clearSelection();
            codeEditors.param.setValue(source.param);
            codeEditors.param.clearSelection();
            codeEditors.overrideErrorCondition.setValue(source.overrideErrorCondition);
            codeEditors.overrideErrorCondition.clearSelection();
            codeEditors.errorMessage.setValue(source.errorMessage);
            codeEditors.errorMessage.clearSelection();

            updateCodeDiffStatus(jsonEditor, codeEditors);
        });

        $("#resetInputVariables").click(function() {
            aceEditors.sourceVariables.setValue(INPUT_VARIABLES_DEFAULT);
            aceEditors.sourceVariables.clearSelection();
        });

        $("#saveOptionIntervalLength").select2({
            minimumResultsForSearch: -1,
            width: "resolve"
        });

        $("#editorOptionFontSize").select2({
            minimumResultsForSearch: -1,
            width: "resolve"
        });

        $("#editorOptionTheme").select2({
            minimumResultsForSearch: -1,
            width: "16em"
        });

        $("input[name='saveOptionRadios']").change(function() {
            localStorage.setItem("sourceDevelopAutosaveOption", this.value);
            setupSaveInterval(this.value, $("#saveOptionIntervalLength").val());
        });

        $("#editorOptionWordWrap").change(function() {
            localStorage.setItem("sourceDevelopEditorWordWrap", this.checked);

            for (var editorName in aceEditors) {
                aceEditors[editorName].setOption("wrap", this.checked);
            }
        });

        $("#saveOptionIntervalLength").on('select2:select', function (e) {
            localStorage.setItem("sourceDevelopAutosaveInterval", this.value);
            setupSaveInterval($("input[name='saveOptionRadios']:checked").val(), this.value);
        });

        $("#editorOptionFontSize").on('select2:select', function (e) {
            let fontSize = $("#editorOptionFontSize").val();
            localStorage.setItem("sourceDevelopEditorFontSize", fontSize);

            for (var editorName in aceEditors) {
                if (editorName !== "outputConsole") {
                    aceEditors[editorName].setOption("fontSize", `${fontSize}pt`);
                }
            }
        });

        $("#editorOptionTheme").on('select2:select', function (e) {
            let theme = $("#editorOptionTheme").val();
            let themeType = $("#editorOptionTheme :selected").parent("optgroup").attr("label");

            localStorage.setItem("sourceDevelopEditorTheme", theme);

            for (var editorName in aceEditors) {
                if (editorName !== "outputConsole") {
                    aceEditors[editorName].setOption("theme", theme);
                }
            }

            switch (themeType) {
                case "Light":
                    $(".codeBackground").addClass("bg-light");
                    $(".codeLabel").addClass("text-dark");

                    $(".codeBackground").removeClass("bg-dark");
                    $(".codeLabel").removeClass("text-light");
                    break;
                case "Dark":
                    $(".codeBackground").addClass("bg-dark");
                    $(".codeLabel").addClass("text-light");

                    $(".codeBackground").removeClass("bg-light");
                    $(".codeLabel").removeClass("text-dark");
                    break;
            }
        });

        let autosaveOption = localStorage.getItem("sourceDevelopAutosaveOption") ? localStorage.getItem("sourceDevelopAutosaveOption") : "disabled";
        let autosaveInterval = localStorage.getItem("sourceDevelopAutosaveInterval") ? localStorage.getItem("sourceDevelopAutosaveInterval") : "10";
        let editorOptionFontSize = localStorage.getItem("sourceDevelopEditorFontSize") ?  localStorage.getItem("sourceDevelopEditorFontSize") : "10";
        let editorOptionTheme = localStorage.getItem("sourceDevelopEditorTheme") ? localStorage.getItem("sourceDevelopEditorTheme") : "ace/theme/textmate";
        let editorOptionWordWrap = localStorage.getItem("sourceDevelopEditorWordWrap") ? localStorage.getItem("sourceDevelopEditorWordWrap") : false;

        $("input[name='saveOptionRadios']").filter(function() {
            return $(this).val() == autosaveOption;
        }).attr("checked", true);

        $("#saveOptionIntervalLength").val(autosaveInterval);
        $("#saveOptionIntervalLength").trigger("change");
        $("#saveOptionIntervalLength").trigger("select2:select");

        $("#editorOptionWordWrap").prop("checked", editorOptionWordWrap);

        $("#editorOptionFontSize").val(editorOptionFontSize);
        $("#editorOptionFontSize").trigger("change");
        $("#editorOptionFontSize").trigger("select2:select");

        $("#editorOptionTheme").val(editorOptionTheme);
        $("#editorOptionTheme").trigger("change");
        $("#editorOptionTheme").trigger("select2:select");

        $('.close').click(function() {
            $(this).parent().hide();
        });

        socket.on('sourceDev:preConditionResult', (data) => {
            clearTimeout(preConditionTimeout);
            setButtonSpinner($("#runSourcePreCondition"), "Run", false);

            if (data.error) {
                $("#sourcePreConditionResultText").text(data.error);
                $("#sourcePreConditionResultText").removeClass("font-italic");
                $("#sourcePreConditionResult").addClass("alert-danger");
                $("#sourcePreConditionResult").removeClass("alert-primary");
                $("#sourcePreConditionResult").show();
                return;
            }

            if (data.variables) {
                outputVariables = data.variables;

                $("#outputVariablesSelect").empty().trigger("change");
                for (var variableGroup in outputVariables) {
                    // Set "data" as default selected variables group
                    $("#outputVariablesSelect").append(new Option(variableGroup, variableGroup, false, variableGroup == "data" ? true : false));
                }

                $("#outputVariablesSelect").trigger("change");
            }

            if (data.preConditionIsUndefined) {
                $("#sourcePreConditionResultText").text("undefined");
                $("#sourcePreConditionResultText").addClass("font-italic");
            } else {
                $("#sourcePreConditionResultText").text(data.preConditionResult);
                $("#sourcePreConditionResultText").removeClass("font-italic");
            }

            $("#sourcePreConditionResult").addClass("alert-primary");
            $("#sourcePreConditionResult").removeClass("alert-danger");
            $("#sourcePreConditionResult").show();

            editorAppendText(aceEditors.outputConsole, data.message);
        });

        socket.on('sourceDev:runResult', (data) => {
            $("#cancelRunSourceParameters").hide();
            setButtonSpinner($("#runSourceParameters"), "Run", false);

            editorAppendText(aceEditors.outputConsole, data.message);
            if (data.error) {
                $("#sourceRunAlert").show();
                editorAppendText(aceEditors.outputConsole, data.error);
            }

            let source = jsonEditor.getValue();

            if (data.variables) {
                let currDate = new Date().toLocaleString('en-GB');
                $("#sourcesOutputUpdatedAt").text(`Last updated at: ${currDate}`);

                outputVariables = data.variables;

                $("#outputVariablesSelect").empty().trigger("change");
                for (var variableGroup in outputVariables) {
                    // Set "data" as default selected variables group
                    $("#outputVariablesSelect").append(new Option(variableGroup, variableGroup, false, variableGroup == "data" ? true : false));
                }

                $("#outputVariablesSelect").trigger("change");

                initNewServiceCheck("sourceSandbox", "", data.variables.data.rulesData);
                $("#status-sourceSandbox").text("");

                for (var sourceName in data.variables.data.sourcesMetadata) {
                    var cd = { category: source.title, name: source.name, SCid: 'sourceSandbox', metadata: data.variables.data.sourcesMetadata[sourceName], data: data.variables.data.sourcesData[sourceName] };

                    if (data.variables.data.sourcesMetadata[sourceName].preCondResult) {
                        // Does not hide sources by default in the sandbox environment
                        updateCollectedData(cd, false, false);
                    }

                    // Print failed Sources
                    if (data.variables.data.sourcesMetadata[sourceName].status == 'error') {
                        let errorMessage = data.variables.data.sourcesMetadata[sourceName].errorMessageSummary ? data.variables.data.sourcesMetadata[sourceName].errorMessageSummary : data.variables.data.sourcesMetadata[sourceName].error;
                        let sourceErrorRow = $('<tr>').attr('class', 'rule rule-Error')
                            .html($('<td>').attr('colspan', '100%').html(
                                $('<span>').attr('style', 'color:red').attr('class', 'fas fa-unlink').attr('title', 'Error').append(
                                    $('<a>').attr('target', sourceName).attr('href', `${wikiBaseURL}${sourceName}`).text(sourceName)
                                ).append(` : ${errorMessage}`)
                            ));

                        addToSCBlock('Summary', 'sourceSandbox', sourceErrorRow, 'prepend');
                    }
                }

                $("#sourceSandbox-status").text("(done)");
                $("#pills-dicon-sourceSandbox").hide();
            }
        });

        socket.on('sourceDev:runResultCodeError', (data) => {
            editorAppendText(aceEditors.outputConsole, `${data.message}, ${data.error}`);
        });

        $("#runSourcePreCondition").click(function() {
            let source = jsonEditor.getValue();
            source.preCondition = codeEditors.preCondition.getValue();

            setButtonSpinner($("#runSourcePreCondition"), "Run", true);
            editorAppendText(aceEditors.outputConsole, "Precondition run started");
            $("#sourcePreConditionResult").hide();

            preConditionTimeout = setTimeout(() => {
                setButtonSpinner($("#runSourcePreCondition"), "Run", false);

                $("#sourcePreConditionResultText").text("Run condition to collect code: timeout of 5000ms was reached and no response from socket was received");
                $("#sourcePreConditionResultText").removeClass("font-italic");
                $("#sourcePreConditionResult").addClass("alert-danger");
                $("#sourcePreConditionResult").removeClass("alert-primary");
                $("#sourcePreConditionResult").show();

                editorAppendText(aceEditors.outputConsole, "Run condition to collect code: timeout of 5000ms was reached and no response from socket was received");
            }, 5000);

            socket.emit('sourceDev:preCondition', {
                variables: aceEditors.sourceVariables.getValue(),
                source: source
            });
        });

        $("#runSourceParameters").click(function() {
            let source = jsonEditor.getValue();
            source.preCondition = codeEditors.preCondition.getValue();
            source.param = codeEditors.param.getValue(),
            source.overrideErrorCondition = codeEditors.overrideErrorCondition.getValue();
            source.errorMessage = codeEditors.errorMessage.getValue();

            setButtonSpinner($("#runSourceParameters"), "Run", true);
            editorAppendText(aceEditors.outputConsole, "Source run started");
            $("#sourceRunAlert").hide();

            $("#Results").html(null);

            $("#cancelRunSourceParameters").show();

            socket.emit('sourceDev:run', {
                variables: aceEditors.sourceVariables.getValue(),
                source: source
            });
        });

        $("#cancelRunSourceParameters").click(function() {
            // Enables the run source parameters button again
            $("#cancelRunSourceParameters").hide();
            setButtonSpinner($("#runSourceParameters"), "Run", false);
        });

        $("#outputVariablesSelect").select2({
            minimumResultsForSearch: -1,
			width: "8em",
            formatNoMatches: function () {
                return "No variables found";
            }
        });

        $("#outputVariablesSelect").on('change', function(e) {
            let selected = $("#outputVariablesSelect").select2('data');

            if (selected.length) {
                let variableId = selected[0].id;
                aceEditors.outputVariables.setValue(`${JSON.stringify(outputVariables[variableId], null, 4)}`);
                aceEditors.outputVariables.clearSelection();
            } else {
                aceEditors.outputVariables.setValue("");
                aceEditors.outputVariables.clearSelection();
            }
        });

        jsonEditor.on("change", function() {
            // If the source was loaded recently via AJAX, the change event will still be triggered,
            // do not count this instance as the source being modified by the user
            if (!dataLoaded) {
                dataModified = true;
            } else {
                dataLoaded = false;
            }
        });

        // Sets up listeners on Ace editor instances for source code
        for (const key in codeEditors) {
            codeEditors[key].on("change", function() {
                updateCodeDiffStatus(jsonEditor, codeEditors);
            });
        }

        aceEditors.sourceVariables.getSession().on('change', function(e) {
            // Warns the user if the input variables are larger than 8MB as a payload greater than
            // 10MB will cause the socket.io connection to be terminated by the server
            if (aceEditors.sourceVariables.getValue().length >= 8e6) {
                $("#sourceVariablesSizeAlert").show();
            } else {
                $("#sourceVariablesSizeAlert").hide();
            }
        });

        $(window).on("beforeunload", function(e) {
            let source = jsonEditor.getValue();

            if (dataModified || editorHasDiff(source, codeEditors)) {
                return confirm("");
            } else {
                return;
            }
        });
    });

    function getSourceToForm(editor) {
        setButtonSpinner($("#refreshSource"), $("<span>").addClass("fas fa-sync"), true);

        $.ajax({
            cache: false,
            type: "GET",
            url: `/sources/${encodeURIComponent(sourceName)}`,
            dataType: "json",
            success: function (response) {
                dataLoaded = true;
                dataModified = false;

                editor.setValue(response);
                $("#sourcesEdit").show();

                // Load code obtained from request into execute source editors
                $("#developRefreshCode").click();
            },
            error: function (error) {
                let errorMessage = '';
                if (error.status == 404) {
                    errorMessage = `Source ${sourceName} does not exist`;
                } else {
                    errorMessage = `Error encountered loading source ${sourceName}: HTTP ${error.status} (${error.statusText}): ${error.responseText}`;
                }
                $("#sourcesLoadError").text(errorMessage);
                $("#sourcesLoadError").show();
            },
            complete: function(xhr, status) {
                setButtonSpinner($("#refreshSource"), $("<span>").addClass("fas fa-sync"), false);
            }
        });
    }

    function createSourceFromForm(editor) {
        let errors = editor.validate();

        if (!errors.length) {
            $.ajax({
                type: "POST",
                url: "/sources/",
                contentType: "application/json",
                dataType: "json",
                data: JSON.stringify(editor.getValue()),
                success: function (response) {
                    dataModified = false;
                    window.location.href = `/edit/sources/${encodeURIComponent(editor.getValue().name)}`;
                },
                error: function (error) {
                    if (error.status == 400) {
                        $("#createSourceModalMessage").text(`Error in fields for source:\n${JSON.stringify(error.responseJSON, null, 4)}`);
                    } else if (error.status == 409) {
                        $("#createSourceModalMessage").text(`A source with name ${editor.getValue().name} already exists.`);
                    } else {
                        $("#createSourceModalMessage").text(`Unknown Error:\n${error.responseJSON}`);
                    }

                    $("#createSourceModal").modal({ show: true });
                },
            });
        } else {
            $("#createSourceModalMessage").text("Validation errors in editor, please check fields in form.");
            $("#createSourceModal").modal({ show: true });
        }
    }

    function writeSourceFromForm(editor) {
        let errors = editor.validate();

        if (!errors.length) {
            setButtonSpinner($("#saveSource"), $("<span>").addClass("fas fa-save"), true);

            $.ajax({
                cache: false,
                type: "PUT",
                url: `/sources/${encodeURIComponent(sourceName)}`,
                contentType: "application/json",
                dataType: "json",
                data: JSON.stringify(editor.getValue()),
                success: function (_) {
                    dataModified = false;
                },
                error: function(error) {
                    // just alerts the user for now
                    alert("Error, could not save source:" + error.responseText);
                },
                complete: function(xhr, status) {
                    setButtonSpinner($("#saveSource"), $("<span>").addClass("fas fa-save"), false);
                }
            });
        } else {
            $("#createSourceModalMessage").text("Validation errors in editor, please check fields in form.");
            $("#createSourceModal").modal({ show: true });
        }
    }

    function setupDevelopmentEditors() {
        let editors = {};

        editors.sourceVariables = ace.edit("developSourceVariables", {
            mode: "ace/mode/javascript",
            minLines: 3,
            maxLines: 16
        });

        editors.sourceVariables.setValue(INPUT_VARIABLES_DEFAULT);
        editors.sourceVariables.clearSelection();

        editors.outputVariables = ace.edit("outputVariables", {
            mode: "ace/mode/json",
            minLines: 16,
            maxLines: 16,
            readOnly: true,
            showLineNumbers: false,
            printMargin: false
        });

        editors.sourcePreCondition = ace.edit("developSourcePreCondition", {
            mode: "ace/mode/javascript",
            minLines: 1,
            maxLines: 50,
            printMargin: false
        });

        editors.sourceParameters = ace.edit("developSourceParameters", {
            mode: "ace/mode/javascript",
            minLines: 1,
            maxLines: 50
        });

        editors.sourceOverrideErrorCondition = ace.edit("developOverrideErrorCondition", {
            mode: "ace/mode/javascript",
            minLines: 1,
            maxLines: 50,
            printMargin: false
        });

        editors.sourceErrorMessage = ace.edit("developErrorMessage", {
            mode: "ace/mode/javascript",
            minLines: 1,
            maxLines: 50,
            printMargin: false
        });

        editors.outputConsole = ace.edit("developOutputConsole", {
            mode: "ace/mode/text",
            minLines: 10,
            maxLines: 100,
            readOnly: true,
            showLineNumbers: false,
            printMargin: false,
            showGutter: false,
            showFoldWidgets: false
        });

        return Object.freeze(editors);
    }

    function editorAppendText(editor, text) {
        let currDate = new Date().toLocaleString('en-GB');
        editor.session.insert({
            row: editor.session.getLength(),
            column: 0
        }, `[${currDate}] ${text}\n`);
    }

    function updateCodeDiffStatus(sourceEditor, codeEditors) {
        let source = sourceEditor.getValue();

        if (editorHasDiff(source, codeEditors)) {
            $("#developDiffStatus").text("Unsaved changes");
            $("#developDiffStatus").addClass("alert-warning");
            $("#developDiffStatus").removeClass("alert-success");
        } else {
            $("#developDiffStatus").text("No differences with code fields in \"Edit Source\"");
            $("#developDiffStatus").removeClass("alert-warning");
            $("#developDiffStatus").addClass("alert-success");
        }
    }

    function updateSaveTimeLabel() {
        $("#developSaveTime").text(`Last saved at: ${new Date().toLocaleString("en-GB")}`);
    }

    function setupSaveInterval(value, interval) {
        // Ignores setting interval if readonly is set,
        // but this function usually is not called in this case
        if (disableRuleSourceEditing) {
            return;
        }

        switch (value) {
            case "disabled":
                clearInterval(autosaveInterval);
                break;
            case "interval":
                autosaveInterval = setInterval(function() {
                    $("#developSaveCode").click();
                }, interval * 1000);
                break;
        }
    }

    function editorHasDiff(data, editors) {
        for (const key in editors) {
            if (data[key] != editors[key].getValue()) {
                return true;
            }
        }

        return false;
    }
</script>
</body>
</html>
