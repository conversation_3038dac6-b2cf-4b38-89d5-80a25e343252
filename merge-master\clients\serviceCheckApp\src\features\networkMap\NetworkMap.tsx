import { PopoverMenu } from '@able/react'
import { useState } from 'react'
import { Accordion } from '../../components/accordian/Accordian'
import Dialog from '../../components/dialog/Dialog'
import { useAppState } from '../../hooks/useAppState'
import { PopupButton } from '../serviceTest/components/popupButton/PopupButton'
import styles from './NetworkMap.module.scss'
import { DiagramExpanded } from './components/DiagramExpanded'
import { DiagramRenderer } from './components/DiagramRenderer'

export const NetworkMap = () => {
    const [selectedIndex, setSelectedIndex] = useState<number>(0)
    const { appState } = useAppState()
    const [isModalOpen, setIsModalOpen] = useState<boolean>(false)
    const diagrams = appState.serviceDetails.diagrams

    if (!diagrams?.length) return null

    // Map diagram types to labels for tabs.
    const diagramLabels: Record<string, string> = {
        schematic: 'Access Schematic',
        schematicMDN: 'MDN Schematic',
    }

    const handleTabClick = (index: number) => {
        setSelectedIndex(index)
    }

    return (
        <>
            <Accordion
                label="Circuit Diagrams"
                defaultOpen={false}
                headerContent={
                    <>
                        <PopoverMenu
                            variant="MediumEmphasis"
                            label={
                                diagramLabels[diagrams[selectedIndex].type] ||
                                `Diagram ${selectedIndex + 1}`
                            }
                            developmentUrl="/public/images/able-sprites.svg"
                        >
                            <ul>
                                {diagrams.map((diagram, index) => {
                                    const label =
                                        diagramLabels[diagram.type] ||
                                        `Diagram ${index + 1}`
                                    return (
                                        <li key={`${diagram.type}_${index}`}>
                                            <button
                                                onClick={(
                                                    e: React.MouseEvent<HTMLButtonElement>
                                                ) => {
                                                    e.stopPropagation()
                                                    handleTabClick(index)
                                                }}
                                            >
                                                {label}
                                            </button>
                                        </li>
                                    )
                                })}
                            </ul>
                        </PopoverMenu>
                        <PopupButton onClick={() => setIsModalOpen(true)} />
                    </>
                }
            >
                <div className={styles.diagramWrapper}>
                    {diagrams.map((diagram, index) => (
                        <div
                            key={`${diagram.type}_${index}`}
                            style={{
                                display:
                                    selectedIndex === index ? 'block' : 'none',
                            }}
                        >
                            <DiagramRenderer diagram={diagram} />
                        </div>
                    ))}
                </div>
            </Accordion>
            <Dialog
                title={
                    diagramLabels[diagrams[selectedIndex].type] ||
                    `Diagram ${selectedIndex + 1}`
                }
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
            >
                <DiagramExpanded
                    diagram={diagrams[selectedIndex]}
                    width={1200}
                    height={600}
                />
            </Dialog>
        </>
    )
}
