
import mongoose from 'mongoose';

const bannerMessageSchema = new mongoose.Schema({
    message: { type: String, default: '', maxLength: 500 },
    updatedBy: { type: String, default: 'unknown' },
    updatedOn: { type: Date, default: Date.now }
});


bannerMessageSchema.methods.toJSON = function() {
    let obj = this.toObject();
    delete obj._id;
    delete obj.__v;
    return obj;
}


export default mongoose.model('bannermessage', bannerMessageSchema);
