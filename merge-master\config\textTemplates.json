{"version": 211, "updatedBy": "e045620", "updatedOn": "2025-06-04T17:00:00.000Z", "textTemplates": [{"name": "AccessInterfaceThroughputModule", "active": true, "title": "Access Interface Throughput (Module)", "description": "Module to display the TXBS and RXBS of interfaces for a device", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nlet rowCreator = new RowCreator();\n-%>\n<% if (s.CMI && s.CMI.interfaceSum && s.CMI.interfaceSum.parsed) {\nlet interfaceSumData = s.CMI.interfaceSum.parsed;\n// Metric array strings labels\nconst METRIC_LABELS = Object.freeze({\n    IHQ: \"pkts in input hold queue\",\n    OHQ: \"pkts in output hold queue\",\n    IQD: \"pkts dropped from input queue\",\n    OQD: \"pkts dropped from output queue\",\n    RXBS: \"rx rate (bits/sec)\",\n    TXBS: \"tx rate (bits/sec)\",\n    RXPS: \"rx rate (pkts/sec)\",\n    TXPS: \"tx rate (pkts/sec)\",\n    TRTL: \"throttle count\"\n});\n-%>\n--Throughput on Interfaces--\n<% for (let metric of interfaceMetrics) { -%>\n<% if (METRIC_LABELS[metric]) { -%>\n    <%= `${metric}: ${METRIC_LABELS[metric]}\\n`; -%>\n<% } -%>\n<% } -%>\n<%= rowCreator.format(\"\", interfaceMetrics, true); -%>\n<% for (let interfaceName in interfaceSumData) {\n    let metricValues = [];\n    for (let metric of interfaceMetrics) {\n        metricValues.push(interfaceSumData[interfaceName][metric]);\n    }\n-%>\n<%= rowCreator.format(interfaceName, metricValues, true); -%>\n<% } -%>\n<% } -%>"}, {"name": "AccessStatusModule", "active": true, "title": "Access Status (Module)", "description": "Module to display the primary and secondary interfaces of a managed device", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nlet rowCreator = new RowCreator();\n-%>\n--Access Status--\n<%= rowCreator.format(\"Primary Interface Count\", r.MDR126 && Array.isArray(r.MDR126.interfaces) ? r.MDR126.interfaces.length : \"Not found\"); -%>\n<% if (r.MDR126 && Array.isArray(r.MDR126.interfaces)) { -%>\n\n<% for (let interfaceInfo of r.MDR126.interfaces) { -%>\n<%= rowCreator.format(\"Name\", interfaceInfo.Interface ? interfaceInfo.Interface : \"Not found\"); -%>\n<%= rowCreator.format(\"Description\", interfaceInfo.Description ? interfaceInfo.Description : \"Not found\"); -%>\n<%= rowCreator.format(\"Status\", interfaceInfo.Status ? interfaceInfo.Status : null); -%>\n<%= rowCreator.format(\"Protocol\", interfaceInfo.Protocol ? interfaceInfo.Protocol : null); -%>\n<%= rowCreator.format(\"Admin\", interfaceInfo.Admin ? interfaceInfo.Admin : null); -%>\n<%= rowCreator.format(\"Link\", interfaceInfo.Link ? interfaceInfo.Link : null); -%>\n\n<% } -%>\n<% } -%>\n<%= rowCreator.format(\"Secondary (Mobile) Interface Count\", r.MDR177 && Array.isArray(r.MDR177.interfaces) ? r.MDR177.interfaces.length : \"Not found\"); -%>\n<% if (r.MDR177 && Array.isArray(r.MDR177.interfaces)) { -%>\n\n<% for (let interfaceInfo of r.MDR177.interfaces) { -%>\n<%= rowCreator.format(\"Name\", interfaceInfo.Interface ? interfaceInfo.Interface : \"Not found\"); -%>\n<%= rowCreator.format(\"Description\", interfaceInfo.Description ? interfaceInfo.Description : \"Not found\"); -%>\n<%= rowCreator.format(\"Status\", interfaceInfo.Status ? interfaceInfo.Status : null); -%>\n<%= rowCreator.format(\"Protocol\", interfaceInfo.Protocol ? interfaceInfo.Protocol : null); -%>\n<%= rowCreator.format(\"Admin\", interfaceInfo.Admin ? interfaceInfo.Admin : null); -%>\n<%= rowCreator.format(\"Link\", interfaceInfo.Link ? interfaceInfo.Link : null); -%>\n\n<% } -%>\n<% } -%>"}, {"name": "AsicStatsPacketLossModule", "active": true, "title": "Platform Asic Stats Packet Loss (Module)", "description": "Module to display whether packet loss occurred in Asic Stats for queue 3 weight 2 for ME-3400 devices", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<% \nlet packetLoss = false;\nif (typeof s['BASEMENTSWITCH-DROPCHECK']?.model === 'string' && s['BASEMENTSWITCH-DROPCHECK'].model.match(/^ME-3400/)) {\n    if (Array.isArray(s['BASEMENTSWITCH-DROPCHECK']?.demarcPointpPlatformPortAsicStats?.parsed?.[3]?.['Queue 3'])) {\n        for (let line of s['BASEMENTSWITCH-DROPCHECK'].demarcPointpPlatformPortAsicStats.parsed[3]['Queue 3']) {\n            let match = line.match(/Weight\\s+(?<weight>\\d+)\\s+Frames\\s+(?<frames>\\d+)/i);\n\n            if (match) {\n                let weight = parseInt(match.groups.weight);\n                let frames = parseInt(match.groups.frames);\n    \n                if (weight === 2 && frames > 0) {\n                    packetLoss = true;\n                }\n            }\n        }\n    } \n}\n-%>\n<% if (packetLoss) { -%>\n--ASIC Stats--\nTRAD ME-3400 device has encountered packet loss in ASIC stats\n\n<% if (Array.isArray(s['BASEMENTSWITCH-DROPCHECK']?.demarcPointpPlatformPortAsicStats?.parsed)) { -%>\n<% for (let asicQueue of s['BASEMENTSWITCH-DROPCHECK'].demarcPointpPlatformPortAsicStats.parsed) { -%>\n<% if (asicQueue && typeof asicQueue === 'object' && Object.keys(asicQueue)[0]) {\nlet asicQueueName = Object.keys(asicQueue)[0];\nlet asicQueueValues = Array.isArray(asicQueue[asicQueueName]) ? asicQueue[asicQueueName] : [];\n-%>\n<%= asicQueueName; %>\n<% for (let value of asicQueueValues) { -%>\n    <%= value; %>\n<% } -%>\n<% } -%>\n<% } -%>\n<% } -%>\n<% } -%>"}, {"name": "BDSLSummaryText", "active": true, "title": "BDSL Summary (Text)", "description": "BDSL Summary default template", "allowedSystemsToAppend": ["ServiceCentral"], "suite": [], "templateType": "Service Check", "formatType": "Text", "listCondition": "data.carriageType === 'BDSL';", "defaultPriority": 2, "template": "<% if (data.carriageType === 'BDSL') {\nlet rowCreator = new RowCreator();\n\nlet RASSPSourceName = null;\nif (s.RASSP && s.RASSP.FNN === data.carriageFNN) {\n    RASSPSourceName = \"RASSP\";\n} else if (s[\"RASSP-C\"] && s[\"RASSP-C\"].FNN === data.carriageFNN) {\n    RASSPSourceName = \"RASSP-C\";\n}\n-%>\nBDSL Summary Issue\n\n<%- renderTemplate(\"CustomerDetailsModule\", { RASSPSourceName: RASSPSourceName }); %>\n<%- renderTemplate(\"DeviceDetailsModule\", { forMDN: false, RASSPSourceName: RASSPSourceName }); %>\n<%- renderTemplate(\"TicketingCasesModule\"); %>\n<%- renderTemplate(\"RassOrdersModule\", { showAllOrders: false, includeCancelledOrders: false }); %>\n<%- renderTemplate(\"OutagesModule\"); %>\n<%- renderTemplate(\"PingResultsModule\", { includeCMIPing: false, includeVPNPing: true, includeVPNPingFullMTU: false }); %>\n<%- renderTemplate(\"GMACSInfoModule\", { showSHDSLCheck: true }); %>\n<%- renderTemplate(\"RASSPBearerAndEquipmentModule\", { RASSPSourceName: RASSPSourceName }); %>\n<% if (r.MDR009 && r.MDR009.manageCategory === 'MDN') { -%>\n\n\n<%- renderTemplate(\"MDNInfoModule\", { showCarriageSectionHeader: false }); %>\n<%- renderTemplate(\"DeviceDetailsModule\", { forMDN: true, RASSPSourceName: \"RASSP\" }); %>\n<%- renderTemplate(\"DeviceStatusModule\"); %>\n<%- renderTemplate(\"AccessStatusModule\"); %>\n<%- renderTemplate(\"AccessInterfaceThroughputModule\", { interfaceMetrics: [\"IQD\", \"OQD\", \"RXBS\", \"TXBS\"] }); %>\n<% } %>\n<%- renderTemplate(\"OutcomesModule\"); %>\n<%- renderTemplate(\"MessageBucketModule\"); %>\nMerge Result Link: <%= mergeUrl %>/serviceCheck/view/html/<%= data.id %>\n<% } else { -%>\nRecord is not for a BDSL service\n<% } %>"}, {"name": "BrunoandFluffyModule", "active": true, "title": "<PERSON> and <PERSON><PERSON><PERSON> (Module)", "description": "Module which shows <PERSON> and <PERSON><PERSON><PERSON> source values", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "s.<PERSON> && s.<PERSON>.results && s.BrunoFluffy.results.APIresponse", "defaultPriority": 0, "template": "<%\nlet rowCreator = new RowCreator();\nlet BandFResponse;\n\nif  (s.<PERSON> && s.<PERSON>.results && s.<PERSON>.results.APIresponse)\n    BandFResponse = s.<PERSON>ffy.results.APIresponse;\n\nif (BandFResponse) {\n    let banfHEA; \n    let banfARP; let banfAAP; let banfBAS; let banfBTK; let banfCAR; let banfCHR; let banfDNM; let banfDHR; let banfICC; let banfIMS; let banfIPA; let banfLRC; let banfMMS; let banfMSA; let banfMSC; let banfMME;\n    let banfMSI; let banfNRR; let banfQCI; let banfSGW; let banfSTT; let banfSAP; let banfTEC; let banfTAP; let banfVRS; let banfSts; let bandAle; let bandFTL; let banfSG1; let banfPG1; let banfPG2; let banfS5G;\n\n    banfHEA = '<PERSON> and <PERSON><PERSON><PERSON>\\n----------------\\n';\n    banfICC = BandFResponse.ICCID                     ? BandFResponse.ICCID                     : ''\n    banfIMS = BandFResponse.IMSI                      ? BandFResponse.IMSI                      : ''\n    banfMSI = BandFResponse.MSISDN                    ? BandFResponse.MSISDN                    : ''\n    banfSts = BandFResponse.status                    ? BandFResponse.status                    : ''\n    banfCAR = BandFResponse.Carrier                   ? BandFResponse.Carrier                   : ''\n    banfARP = BandFResponse.ARP                       ? BandFResponse.ARP                       : ''\n    banfAAP = BandFResponse.Active_APNs               ? BandFResponse.Active_APNs.replace(\".s5\",\"\").replace(\".s8\",\"\") : ''\n    banfBAS = BandFResponse.Base_Station              ? BandFResponse.Base_Station              : ''\n    banfLRC = BandFResponse.LRD_Code                  ? BandFResponse.LRD_Code                  : ''\n    banfBTK = BandFResponse.BlueTick                  ? BandFResponse.BlueTick                  : ''\n    banfDNM = BandFResponse.Device_Name               ? BandFResponse.Device_Name               : ''\n    banfTAP = BandFResponse.Telstra_Approved          ? BandFResponse.Telstra_Approved          : ''\n    banfNRR = BandFResponse.NR                        ? BandFResponse.NR                        : ''\n    banfDHR = BandFResponse.Devil_Headroom            ? BandFResponse.Devil_Headroom            : ''\n    banfTEC = BandFResponse.Technology                ? BandFResponse.Technology                : ''\n    banfSG1 = BandFResponse.Serving_SGWs              ? BandFResponse.Serving_SGWs              : ''\n    banfMMS = BandFResponse.MM_Status                 ? BandFResponse.MM_Status                 : ''\n    banfPG1 = BandFResponse.Serving_PGWs              ? BandFResponse.Serving_PGWs[0]           : ''\n    banfPG2 = BandFResponse.Serving_PGWs              ? BandFResponse.Serving_PGWs[1]           : ''\n    banfMSC = BandFResponse.MSC                       ? BandFResponse.MSC                       : ''\n    banfMSA = BandFResponse.MSA                       ? BandFResponse.MSA                       : ''\n    banfSTT = BandFResponse.State                     ? BandFResponse.State                     : ''\n    banfSAP = BandFResponse.Subscribed_APNs           ? BandFResponse.Subscribed_APNs           : ''\n    banfAAP = BandFResponse.Active_APNs               ? BandFResponse.Active_APNs.replace(\".s5\",\"\").replace(\".s8\",\"\") : ''\n    banfQCI = BandFResponse.QCI                       ? BandFResponse.QCI                       : ''\n    banfARP = BandFResponse.ARP                       ? BandFResponse.ARP                       : ''\n    banfIPA = BandFResponse.IP_Addresses              ? BandFResponse.IP_Addresses              : ''\n    banfVRS = BandFResponse.VoLTE_registration_Status ? BandFResponse.VoLTE_registration_Status : ''\n    banfAle = BandFResponse.Alert                     ? BandFResponse.Alert                     : ''\n    banfCHR = BandFResponse.Charging                  ? BandFResponse.Charging                  : ''\n    banfMME = BandFResponse.MME                       ? BandFResponse.MME                       : ''\n    bandfTL = BandFResponse.TowerLocation             ? BandFResponse.TowerLocation             : ''\nlet mergeLink = '\\nMerge Result Link: https://merge.in.telstra.com.au/serviceCheck/view/html/'  + data.id;\n-%>\n<%= banfHEA -%>\n<%= rowCreator.format('ICCID',banfICC); -%>\n<%= rowCreator.format('IMSI',banfIMS); -%>\n<%= rowCreator.format('MSISDN',banfMSI); -%>\n<%= rowCreator.format('Status',banfSts); -%>\n<%= rowCreator.format('Carrier',banfCAR); -%>\n<%= rowCreator.format('ARP',banfARP); -%>\n<%= rowCreator.format('Active APNs',banfAAP); -%>\n<%= rowCreator.format('Base Station',banfBAS); -%>\n<%= rowCreator.format('LRD Code',banfLRC); -%>\n<%= rowCreator.format('Blue Tick',banfBTK); -%>\n<%= rowCreator.format('Device Name',banfDNM); -%>\n<%= rowCreator.format('Telstra Approved',banfTAP); -%>\n<%= rowCreator.format('NR',banfNRR); -%>\n<%= rowCreator.format('Health Rules(Devil Headroom)',banfDHR); -%>\n<%= rowCreator.format('Technology',banfTEC); -%>\n<%= rowCreator.format('Serving SGWs',banfSGW); -%>\n<%= rowCreator.format('MM Status',banfMMS); -%>\n<%= rowCreator.format('SGW/SG SN',banfSG1); -%>\n<%= rowCreator.format('PGW/GG SN',banfPG1); -%>\n<%= rowCreator.format('PGW/SAPC',banfPG2); -%>\n<%= rowCreator.format('MSC',banfMSC); -%>\n<%= rowCreator.format('MSA',banfMSA); -%>\n<%= rowCreator.format('State',banfSTT); -%>\n<%= rowCreator.format('Subscribed APNs',banfSAP); -%>\n<%= rowCreator.format('Active APNs',banfAAP); -%>\n<%= rowCreator.format('QCI',banfQCI); -%>\n<%= rowCreator.format('ARP',banfARP); -%>\n<%= rowCreator.format('IP Addresses',banfIPA); -%>\n<%= rowCreator.format('VoLTE Registration Status',banfVRS); -%>\n<%= rowCreator.format('Alert',banfAle); -%>\n<%= rowCreator.format('Charging',banfCHR); -%>\n<%= rowCreator.format('Live Incidents',''); -%>\n<%= rowCreator.format('MME/AMF',banfMME); -%>\n<%= rowCreator.format('Model',banfDNM); -%>\n<%= rowCreator.format('Tower Location',bandFTL); -%>\n<% } -%>\n"}, {"name": "BSIPCustomerDetails", "active": true, "title": "Display BSIP Customer Details (Module)", "description": "Module to display BSIP Customer Details", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nlet rowCreator = new RowCreator();\nlet bsipcHeader; let bsipcCustomerName; let bsipcCustomerType; let bsipcContactName; let bsipcContactNumber; let bsipcCountry; let bsipcsSitename;\n\nif (s.BSIPC || (s.BSIPCS && s.BSIPU && s.BSIPU.siteId)) bsipcHeader = 'Customer Details\\n----------------\\n'; \nif (s.BSIPC) {\n    if (s.BSIPC.customerName)                               bsipcCustomerName = s.BSIPC.customerName;\n    if (s.BSIPC.customerType)                               bsipcCustomerType = s.BSIPC.customerType;\n    if (s.BSIPC.contact && s.BSIPC.contact.contactName)     bsipcContactName = s.BSIPC.contact.contactName;\n    if (s.BSIPC.contact && s.BSIPC.contact.contactNumber)   bsipcContactNumber = s.BSIPC.contact.contactNumber;\n    if (s.BSIPC.address && s.BSIPC.address.country)         bsipcCountry = s.BSIPC.address.country;\n}\nif (s.BSIPCS && s.BSIPU && s.BSIPU.siteId) {\n    var ro = Object.keys(s.BSIPCS).length;\n    if (ro > 0) bsipcsSitename = [];\n    for (i = 0; i < ro ; i++) {\n        var ip = '[' + ((i < 10) ? String(i) + '] ' : String(i) + ']');\n        if (s.BSIPCS[i] && s.BSIPCS[i].siteId == s.BSIPU.siteId) if (s.BSIPCS[i].siteName) bsipcsSitename.push(`${ip} ${s.BSIPCS[i].siteName}`);\n    }\n}\n-%>\n<%= bsipcHeader -%>\n<%= rowCreator.format('Customer Name',bsipcCustomerName); -%>\n<%= rowCreator.format('Customer Type',bsipcCustomerType); -%>\n<%= rowCreator.format('Contact Name',bsipcContactName); -%>\n<%= rowCreator.format('Contact Number',bsipcContactNumber); -%>\n<%= rowCreator.format('Country',bsipcCountry); -%>\n<%= rowCreator.format('Site Name',bsipcsSitename, false); -%>\n"}, {"name": "BSIPDefaultTemplate", "active": true, "title": "BSIPDefaultTemplate (Text)", "description": "BSIP information summary template", "allowedSystemsToAppend": ["ServiceCentral"], "suite": [], "templateType": "Service Check", "formatType": "Text", "listCondition": "// Note: This list condition combines the list condition for the now deleted template \"IPVoiceBSIPSummaryText\"\n(s.BSIPU && s.BSIPU.customerId) || (s.COMMPilot && s.COMMPilot.user && s.COMMPilot.user.clusterType && s.COMMPilot.user.clusterType === 'STOA');", "defaultPriority": 1, "template": "<% let rowCreator = new RowCreator();\nif ((s.BSIPU && s.BSIPU.customerId) ||\n    (s.COMMPilot && s.COMMPilot.user && s.COMMPilot.user.clusterType && (s.COMMPilot.user.clusterType !== 'DOT' && s.COMMPilot.user.clusterType !== 'TIPT'))) {\n    // Variables Declaration Begin\n    let bsipcsa1BearerType;\n    let bsipcsa1Encryption;\n    let bsipcsa3AdminId;\n    let bsipcsfpNames = [];\n\n    if (s.BSIPCSA1 && s.BSIPCSA1.bearerType) bsipcsa1BearerType = s.BSIPCSA1.bearerType;\n    if (s.BSIPCSA1 && s.BSIPCSA1.encryption) bsipcsa1Encryption = s.BSIPCSA1.encryption;\n    if (s.BSIPCSA3 && s.BSIPCSA3[0] && s.BSIPCSA3[0].adminId) bsipcsa3AdminId = s.BSIPCSA3[0].adminId;\n\n    if (s.BSIPCSFP)  {\n        let ro = Object.keys(s.BSIPCSFP).length;\n        for (i = 0; i < ro ; i++) {\n            if (s.BSIPCSFP[i]) {\n                var ip = '[' + ((i < 10) ? String(i) + '] ' : String(i) + ']');\n                var f1 = ''; var f2 = ''; var f3 = '';\n                if (s.BSIPCSFP[i].featurePackName)    f1 += s.BSIPCSFP[i].featurePackName.padEnd(25);\n                if (s.BSIPCSFP[i].allocatedQuantity)  f2 += s.BSIPCSFP[i].allocatedQuantity; else f2 += ' 0';\n                if (s.BSIPCSFP[i].purchasedQuantity)  f3 += ' / ' + s.BSIPCSFP[i].purchasedQuantity; else f3 += ' / 0';\n                bsipcsfpNames.push(`${ip} ${f1} ${f2} ${f3}`);\n            }\n        }\n    }\n\n    let customerName = s.MAGPIECIDN?.rawData?.[0]?.Customer;\n    let customerCIDN = s.MAGPIECIDN?.rawData?.[0]?.Attributes;\n\n    let bmCID = s.BATMAGPIE?.CARRIAGEID ? s.BATMAGPIE.CARRIAGEID : null;\n    let biPAT = s.BATIPNBN?.session?.service_location_details?.primary_access_technology ? s.BATIPNBN.session.service_location_details.primary_access_technology : null;\n    let nbnLST = r.MTR023?.serviceTestResult ? r.MTR023.serviceTestResult : null;\n-%>\nBSIP Summary\n============\n<% if (s.CFNN) {\nlet customerStatus = typeof s.CFNN.FirstLine === 'string' ? s.CFNN.FirstLine.trim() : null;\nlet cfnnDetails = typeof s.CFNN.Details === 'string' ? s.CFNN.Details : null;\n-%>\nCFNN\n----\n<%= rowCreator.format('Status', customerStatus); -%>\n<%= rowCreator.format('Details', cfnnDetails); -%>\n<% } -%>\n\n<%= rowCreator.format('Customer', customerName); -%>\n<%= rowCreator.format('CIDN', customerCIDN); -%>\n\n<%- renderTemplate(\"IPVoiceUserDetails\"); -%>\n\n<%- renderTemplate(\"BSIPCustomerDetails\"); -%>\n<%= rowCreator.format('Bearer Type', bsipcsa1BearerType); -%>\n<%= rowCreator.format('Encryption', bsipcsa1Encryption); -%>\n<%= rowCreator.format('Admin Id', bsipcsa3AdminId); -%>\n<%- renderTemplate(\"IPVoiceFeaturePacks\"); -%>\n<%- renderTemplate(\"IPVoicePacksAssigned\"); -%>\n<%- renderTemplate(\"IPVoiceCallForwarding\"); -%>\n<%- renderTemplate(\"IPVoiceGroupServices\"); -%>\n<%- renderTemplate(\"IPVoiceUserInfo\"); -%>\n<%- renderTemplate(\"IPVoiceEnterpiseTrunk\"); -%>\n<% if (s.COMMPilot && s.COMMPilot.registration && s.COMMPilot.registration.registrations) { -%>\n<%- renderTemplate(\"IPVoiceRegistrationInfoCommpilot\"); -%>\n<% } else if (s.CFNN && s.CFNN.registrations) { -%>\n<%- renderTemplate(\"IPVoiceRegistrationInfoCFNN\"); -%>\n<% } -%>\n<%- renderTemplate(\"IPVoiceRegistrationDetails\"); -%>\n<%- renderTemplate(\"IPVoiceIPWorks\"); -%>\n<%- renderTemplate(\"IPVoiceDeviceCustomTags\"); -%>\n<%- renderTemplate(\"IPVoiceIncomingCallControl\"); -%>\n<%- renderTemplate(\"IPVoiceSharedDevices\"); -%>\n<%- renderTemplate(\"IPVoiceGroupCustomTags\"); -%>\n<%- renderTemplate(\"IPVoiceTrunkGroup\"); -%>\n<%- renderTemplate(\"IPVoiceHuntGroup\"); -%>\n<%- renderTemplate(\"IPVoiceSableData\"); -%>\n<% if (s.BSIPCSFP) { -%>\nFeature Packs\n-------------\n                                                                          Assigned / Available\n<%= rowCreator.format('Names', bsipcsfpNames, false); -%>\n\n<% } -%>\n<%- renderTemplate(\"BSIPSTP\"); -%>\n<%- renderTemplate(\"IPVoiceSableHealth\"); -%>\n\n<%- renderTemplate(\"BSIPDTMF\"); -%>\n\n<%= rowCreator.format('Carriage ID', bmCID); -%>\n<%= rowCreator.format('Service Type', biPAT); -%>\n<%= rowCreator.format('NBN Line State Test', nbnLST); -%>\n\nMerge Result Link: <%= mergeUrl %>/serviceCheck/view/html/<%= data.id %>\n<% } -%>\n<%- renderTemplate(\"ShowRules\"); -%>"}, {"name": "BSIPDTMF", "active": true, "title": "Display BSIP DTMF", "description": "Module to display BSIP DTMF", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nif (s.BSIPCSTF) {\n    let rowCreator = new RowCreator();\n    let bsipcstfHeader; let bsipcstfa;\n\n    bsipcstfHeader = 'DTMF Related\\n------------\\n';\n    var bsipcstfName2 = ''; var bsipcstfStateName = '';\n    var ro = Object.keys(s.BSIPCSTF).length;\n    if (ro > 0) bsipcstfa = [];\n    for (i = 0; i < ro ; i++) {\n        var bsipcstfName = ''; var bsipcstfSetting = '';\n        if (s.BSIPCSTF[i] && s.BSIPCSTF[i].name && s.BSIPCSTF[i].name == 'DTMF Settings') {\n            if (s.BSIPCSTF[i].name) {\n                bsipcstfName = s.BSIPCSTF[i].name;\n                bsipcstfa.push(`Name               ${bsipcstfName}`);\n            }\n            if (s.BSIPCSTF[i].isEnabled) if (s.BSIPCSTF[i].on) bsipcstfSetting = 'Yes isEnabled'; else bsipcstfSetting = 'Yes isDisabled';\n            else bsipcstfSetting = 'Not Changable';\n            bsipcstfa.push(`Setting            ${bsipcstfSetting}`);\n            var ro2 = Object.keys(s.BSIPCSTF[i].subFeatures).length;\n            for (j = 0; j < ro2 ; j++) {\n                var bsipcstfSFName = '';\n                if (s.BSIPCSTF[i].subFeatures[j]) {\n                    if (s.BSIPCSTF[i].subFeatures[j].name) {\n                        bsipcstfSFName = s.BSIPCSTF[i].subFeatures[j].name;\n                        bsipcstfa.push(`  FeatureName        ${bsipcstfSFName}`);\n                    }\n                    if (typeof s.BSIPCSTF[i].subFeatures[j].states != 'undefined') {\n                        var ro3 = Object.keys(s.BSIPCSTF[i].subFeatures[j].states).length;\n                        for (k = 0; k < ro3 ; k++) {\n                            var bsipcstfSFSName = '';\n                            if (s.BSIPCSTF[i].subFeatures[j].states[k] && s.BSIPCSTF[i].subFeatures[j].states[k].default) {\n                                if (s.BSIPCSTF[i].subFeatures[j].states[k].name) {\n                                    bsipcstfSFSName = 'Yes ' + s.BSIPCSTF[i].subFeatures[j].states[k].name;\n                                    bsipcstfa.push(`    Statename          ${bsipcstfSFSName}`);\n                               }\n                            } else if (s.BSIPCSTF[i].subFeatures[j].states[k] && typeof s.BSIPCSTF[i].subFeatures[j].states[k].default == 'undefined') {\n                               if (s.BSIPCSTF[i].subFeatures[j].states[k].name) {\n                                    bsipcstfSFSName = 'No  ' + s.BSIPCSTF[i].subFeatures[j].states[k].name;\n                                    bsipcstfa.push(`    Statename          ${bsipcstfSFSName}`);\n                                }\n                            }\n                        }\n                    } else {\n                        if (typeof s.BSIPCSTF[i].subFeatures[j].switchText.disabled != 'undefined') {\n                            bsipcstfSFName = 'Yes ' + s.BSIPCSTF[i].subFeatures[j].switchText.disabled;\n                            bsipcstfa.push(`    Statename          ${bsipcstfSFName}`);\n                        }\n                        if (typeof s.BSIPCSTF[i].subFeatures[j].switchText.enabled  != 'undefined') {\n                            bsipcstfSFName = 'No  ' + s.BSIPCSTF[i].subFeatures[j].switchText.enabled;\n                            bsipcstfa.push(`    Statename          ${bsipcstfSFName}`);\n                        }\n                    }\n                }\n            }\n        }\n        if (s.BSIPCSTF[i] && s.BSIPCSTF[i].name && s.BSIPCSTF[i].name == 'DTMF detection level') {\n            if (s.BSIPCSTF[i].name) bsipcstfName2 = s.BSIPCSTF[i].name;\n                bsipcstfa.push(`Name               ${bsipcstfName2}`);\n                var ro2 = Object.keys(s.BSIPCSTF[i].states).length;\n                for (j = 0; j < ro2 ; j++) {\n                    if (s.BSIPCSTF[i].states[j] && s.BSIPCSTF[i].states[j].default) {\n                        if (s.BSIPCSTF[i].states[j].name) bsipcstfStateName = 'Yes ' + s.BSIPCSTF[i].states[j].name;\n                        bsipcstfa.push(`  StateName          ${bsipcstfStateName}`);\n                    } else if (s.BSIPCSTF[i].states[j] && typeof s.BSIPCSTF[i].states[j].default == 'undefined') {\n                    if (s.BSIPCSTF[i].states[j].name) bsipcstfStateName = 'No  ' + s.BSIPCSTF[i].states[j].name;\n                    bsipcstfa.push(`  StateName          ${bsipcstfStateName}`);\n                }\n            }\n        }\n        if (s.BSIPCSTF[i] && s.BSIPCSTF[i].name && s.BSIPCSTF[i].name == 'DTMF echo handling') {\n            if (s.BSIPCSTF[i].name) bsipcstfName2 = s.BSIPCSTF[i].name;\n            bsipcstfa.push(`Name               ${bsipcstfName2}`);\n            var ro2 = Object.keys(s.BSIPCSTF[i].states).length;\n            for (j = 0; j < ro2 ; j++) {\n                if (s.BSIPCSTF[i].states[j] && s.BSIPCSTF[i].states[j].default) {\n                    if (s.BSIPCSTF[i].states[j].name) bsipcstfStateName = 'Yes ' + s.BSIPCSTF[i].states[j].name;\n                    bsipcstfa.push(`  StateName          ${bsipcstfStateName}`);\n                } else if (s.BSIPCSTF[i].states[j] && typeof s.BSIPCSTF[i].states[j].default == 'undefined') {\n                    if (s.BSIPCSTF[i].states[j].name) bsipcstfStateName = 'No  ' + s.BSIPCSTF[i].states[j].name;\n                    bsipcstfa.push(`  StateName          ${bsipcstfStateName}`);\n                }\n            }\n        }\n    }\n-%>\n<%= bsipcstfHeader -%>\n<%= rowCreator.format('Details',bsipcstfa, false); -%>\n<%} -%>"}, {"name": "BSIPSTP", "active": true, "title": "Display BSIP STP (Module)", "description": "Module to display BSIP STP", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nif (s.BSIPCST) {\n    let rowCreator = new RowCreator();\n    let bsipcstHeader; let bsipcstNtuDeviceType; let bsipcstNtuRegistered; let bsipcstNtuUserAgent; let bsipcstNtuAvailable; let commpilotDeviceName; let commpilotLinePort; let commpilotRegistrationType; let commpilotExpires;\n    let commpilotTAIC; let commpilotTAOC; let commpilotTAC; let bsipcstMaxTrunkCallCapacity; let bsipcstMaxActiveCalls; let bsipcstMaxIncomingCalls; let bsipcstMaxOutgoingCalls; let bsipcstClidSource; let bsipcstHasFeatures;\n\n    bsipcstHeader = 'STP NTU\\n-------\\n';\n    if (s.BSIPCST.ntuDeviceType)        bsipcstNtuDeviceType        = s.BSIPCST.ntuDeviceType;\n    if (s.COMMPilot && s.COMMPilot.registration && s.COMMPilot.registration.registrations) {\n        if (s.COMMPilot.registration.registrations.deviceName)       commpilotDeviceName       = s.COMMPilot.registration.registrations.deviceName;\n        if (s.COMMPilot.registration.registrations.linePort)         commpilotLinePort         = s.COMMPilot.registration.registrations.linePort;\n        if (s.COMMPilot.registration.registrations.registrationType) commpilotRegistrationType = s.COMMPilot.registration.registrations.registrationType;\n        if (s.COMMPilot.registration.registrations.expires)          commpilotExpires          = s.COMMPilot.registration.registrations.expires;\n    }\n    if (s.BSIPCST.ntuRegistered)        bsipcstNtuRegistered        = 'Yes';                          else bsipcstNtuRegistered = 'No';\n    if (s.BSIPCST.ntuUserAgent)         bsipcstNtuUserAgent         = s.BSIPCST.ntuUserAgent;\n    if (s.BSIPCST.ntuAvailable)         bsipcstNtuAvailable         = 'Yes';                          else bsipcstNtuAvailable = 'No';\n    if (s.BSIPCST.maxTrunkCallCapacity) bsipcstMaxTrunkCallCapacity = s.BSIPCST.maxTrunkCallCapacity;\n    if (s.BSIPCST.maxActiveCalls)       bsipcstMaxActiveCalls       = s.BSIPCST.maxActiveCalls;\n    if (s.BSIPCST.maxIncomingCalls)     bsipcstMaxIncomingCalls     = s.BSIPCST.maxIncomingCalls;\n    if (s.BSIPCST.maxOutgoingCalls)     bsipcstMaxOutgoingCalls     = s.BSIPCST.maxOutgoingCalls;\n\n    if (s.COMMPilot && s.COMMPilot.configuredDevice && s.COMMPilot.configuredDevice.trunk) {\n        if (s.COMMPilot.configuredDevice.trunk.totalActiveIncomingCalls) commpilotTAIC = s.COMMPilot.configuredDevice.trunk.totalActiveIncomingCalls;\n        if (s.COMMPilot.configuredDevice.trunk.totalActiveOutgoingCalls) commpilotTAOC = s.COMMPilot.configuredDevice.trunk.totalActiveOutgoingCalls;\n        if (s.COMMPilot.configuredDevice.trunk.totalActiveIncomingCalls && s.COMMPilot.configuredDevice.trunk.totalActiveOutgoingCalls)\n            commpilotTAC = (parseInt(s.COMMPilot.configuredDevice.trunk.totalActiveIncomingCalls) + parseInt(s.COMMPilot.configuredDevice.trunk.totalActiveOutgoingCalls));\n    }\n    if (s.BSIPCST.clidSource)           bsipcstClidSource           = s.BSIPCST.clidSource;\n    if (s.BSIPCST.hasFeatures)          bsipcstHasFeatures          = 'Yes';                          else bsipcstHasFeatures = 'No';\n-%>\n<%= bsipcstHeader -%>\n<%= rowCreator.format('Device Type',bsipcstNtuDeviceType); -%>\n<%= rowCreator.format('Device Name',commpilotDeviceName); -%>\n<%= rowCreator.format('Line Port',commpilotLinePort); -%>\n<%= rowCreator.format('Registration Type',commpilotRegistrationType); -%>\n<%= rowCreator.format('Expires',commpilotExpires); -%>\n<%= rowCreator.format('Registered',bsipcstNtuRegistered); -%>\n<%= rowCreator.format('User Agent',bsipcstNtuUserAgent); -%>\n<%= rowCreator.format('Online',bsipcstNtuAvailable); -%>\n<%= rowCreator.format('Max Trunk Call Cap',bsipcstMaxTrunkCallCapacity); -%>\n<%= rowCreator.format('Max Active Calls',bsipcstMaxActiveCalls); -%>\n<%= rowCreator.format('Max Incoming Calls',bsipcstMaxIncomingCalls); -%>\n<%= rowCreator.format('Max Outgoing Calls',bsipcstMaxOutgoingCalls); -%>\n<%= rowCreator.format('Total Active Incoming Calls',commpilotTAIC); -%>\n<%= rowCreator.format('Total Active Outgoing Calls',commpilotTAOC); -%>\n<%= rowCreator.format('Total Active Calls',commpilotTAC); -%>\n<%= rowCreator.format('Clid Source',bsipcstClidSource); -%>\n<%= rowCreator.format('Has Features',bsipcstHasFeatures); -%>\n<%} -%>"}, {"name": "CDRandRODSDetails", "active": true, "title": "Display CDR and RODS Details (Module)", "description": "Module to display CDR and RODS Details  ", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%  let rowCreator = new RowCreator();\nlet cdrBody;  let hangarBody;\nif (s.CDRLASTModini && s.CDRLASTModini.Data && s.CDRLASTModini.Data.Id) {\n  if (s.CDRLASTModini.Data.Output[0].Rows[0]['First CGI']) cdrBody = s.CDRLASTModini.Data.Output[0].Rows[0]['First CGI'].slice(0,4); else cdrBody = 'Not found';\n}\nif (s['Hangar - RODS Site Data'] && Array.isArray(s['Hangar - RODS Site Data'].rodsData) && s['Hangar - RODS Site Data'].rodsData.length > 0) {\n  hangarBody = s['Hangar - RODS Site Data'].rodsData[0].street_number + ',' + s['Hangar - RODS Site Data'].rodsData[0].street_name + ', ' + s['Hangar - RODS Site Data'].rodsData[0].suburb_or_town + ', ' + s['Hangar - RODS Site Data'].rodsData[0].state + ' - ' + s['Hangar - RODS Site Data'].rodsData[0].postcode;\n} else {\n  hangarBody = 'Not Found';\n}\n-%>\n<%= rowCreator.format('First CGI',cdrBody); -%>\n<%= rowCreator.format('Tower Location',hangarBody); -%>\n"}, {"name": "CiscoIOTTemplate", "active": true, "title": "CiscoIOTTemplate (Text)", "description": "Summary for Cisco IOT devices", "allowedSystemsToAppend": ["ServiceCentral"], "suite": [], "templateType": "Service Check", "formatType": "Text", "listCondition": "data.carriageType == 'MOBILE';", "defaultPriority": 0, "template": "<% let rowCreator = new RowCreator();\n\nlet templateHeader = 'Cisco IOT\\n=========\\n'; \nlet cfnnHeader; let customerStatus; let cfnnDetails;\nif (s.CFNN) {\n    cfnnHeader = 'CFNN\\n----\\n';\n    if (s.CFNN && s.CFNN.FirstLine) customerStatus = s.CFNN.FirstLine.trim();\n    if (s.CFNN && s.CFNN.Details) cfnnDetails = s.CFNN.Details;\n}\n\nlet bsipcHeader; let bsipcCustomerName; let bsipcCustomerType; let bsipcContactName; let bsipcContactNumber; let bsipcCountry;\nif (s.BSIPC) {\n    bsipcHeader = 'Customer Details\\n----------------\\n';\n    if (s.BSIPC.customerName)                               bsipcCustomerName = s.BSIPC.customerName;\n    if (s.BSIPC.customerType)                               bsipcCustomerType = s.BSIPC.customerType;\n    if (s.BSIPC.contact && s.BSIPC.contact.contactName)     bsipcContactName = s.BSIPC.contact.contactName;\n    if (s.BSIPC.contact && s.BSIPC.contact.contactNumber)   bsipcContactNumber = s.BSIPC.contact.contactNumber;\n    if (s.BSIPC.address && s.BSIPC.address.country)         bsipcCountry = s.BSIPC.address.country;\n}\n\nlet bsipcsSitename;\nif (s.BSIPCS && s.BSIPU && s.BSIPU.siteId) {\n    var ro = Object.keys(s.BSIPCS).length;\n    if (ro > 0) bsipcsSitename = [];\n    for (i = 0; i < ro ; i++) {\n      var ip = '[' + ((i < 10) ? String(i) + '] ' : String(i) + ']');\n      if (s.BSIPCS[i] && s.BSIPCS[i].siteId == s.BSIPU.siteId) if (s.BSIPCS[i].siteName) bsipcsSitename.push(`${ip} ${s.BSIPCS[i].siteName}`);\n    }\n}\n\nlet bsipcsa1BearerType; let bsipcsa1Encryption; let bsipcsa3AdminId;\nif (s.BSIPCSA1) {\n    if (s.BSIPCSA1.bearerType) bsipcsa1BearerType = s.BSIPCSA1.bearerType;\n    if (s.BSIPCSA1.encryption) bsipcsa1Encryption = s.BSIPCSA1.encryption;\n}\nif (s.BSIPCSA3) {\n    if (s.BSIPCSA3[0] && s.BSIPCSA3[0].adminId) bsipcsa3AdminId = s.BSIPCSA3[0].adminId;\n}\n\n\nlet ciotddIccid; let ciotddImsi; let ciotddMsisdn; let ciotddStatus; let ciotddRateplan;\nlet ciotddCustomer; let ciotddDateActivated; let ciotddImei; let ciotddFixedIPAddress;\nlet ciotsdIpAddress; let ciotsdDateSessionStarted; let ciotsdFixedIPAddress;\nif (s.CiscoIOTDD) {\n  if (s.CiscoIOTDD.iccid)          ciotddIccid = s.CiscoIOTDD.iccid;\n  if (s.CiscoIOTDD.imsi)           ciotddImsi = s.CiscoIOTDD.imsi;\n  if (s.CiscoIOTDD.msisdn)         ciotddMsisdn = s.CiscoIOTDD.msisdn;\n  if (s.CiscoIOTDD.status)         ciotddStatus = s.CiscoIOTDD.status;\n  if (s.CiscoIOTDD.rateplan)       ciotddRateplan = s.CiscoIOTDD.rateplan;\n  if (s.CiscoIOTDD.customer)       ciotddCustomer = s.CiscoIOTDD.customer;\n  if (s.CiscoIOTDD.dateActivated)  ciotddDateActivated = s.CiscoIOTDD.dateActivated;\n  if (s.CiscoIOTDD.imei)           ciotddImei = s.CiscoIOTDD.imei;\n  if (s.CiscoIOTDD.fixedIPAddress) ciotddFixedIPAddress = s.CiscoIOTDD.fixedIPAddress;\n}\nif (s.CiscoIOTSD) {\n  if (s.CiscoIOTSD.ipAddress)          ciotsdIpAddress = s.CiscoIOTSD.ipAddress;\n  if (s.CiscoIOTSD.dateSessionStarted) ciotsdDateSessionStarted = s.CiscoIOTSD.dateSessionStarted;\n  if (s.CiscoIOTSD.dateSessionEnded)   ciotsdFixedIPAddress = s.CiscoIOTSD.dateSessionEnded;\n}\n\nlet mergeLink = '\\nMerge Result Link: https://merge.in.telstra.com.au/serviceCheck/view/html/'  + data.id;\n-%>\n<%= templateHeader -%>\n<%= cfnnHeader -%>\n<%= rowCreator.format('Details',customerStatus); -%>\n<%= rowCreator.format('Details',cfnnDetails); -%>\n<%- renderTemplate(\"IPVoiceUserDetails\"); -%>\n<%= bsipcHeader -%>\n<%= rowCreator.format('Customer Name',bsipcCustomerName); -%>\n<%= rowCreator.format('Customer Type',bsipcCustomerType); -%>\n<%= rowCreator.format('Contact Name',bsipcContactName); -%>\n<%= rowCreator.format('Contact Number',bsipcContactNumber); -%>\n<%= rowCreator.format('Country',bsipcCountry); -%>\n<%= rowCreator.format('Site Name',bsipcsSitename, false); -%>\n<%= rowCreator.format('Bearer Type',bsipcsa1BearerType); -%>\n<%= rowCreator.format('Encryption',bsipcsa1Encryption); -%>\n<%= rowCreator.format('Admin Id',bsipcsa3AdminId); -%>\n<%- renderTemplate(\"IPVoicePacksAssigned\"); -%>\n<%- renderTemplate(\"IPVoiceCallForwarding\"); -%>\n<%= rowCreator.format('Group Numbers',ciotddIccid); -%>\n<%= rowCreator.format('Group MBN',ciotddImsi); -%>\n<%= rowCreator.format('group MBN UI',ciotddMsisdn); -%>\n<%= rowCreator.format('Type',ciotddStatus); -%>\n<%= rowCreator.format('First Name',ciotddRateplan); -%>\n<%= rowCreator.format('Last Name',ciotddCustomer); -%>\n<%= rowCreator.format('CLID FirstName',ciotddDateActivated); -%>\n<%= rowCreator.format('CLID LastName',ciotddImei); -%>\n<%= rowCreator.format('Default Alias',ciotddFixedIPAddress); -%>\n<%= rowCreator.format('TimeZone',ciotsdIpAddress); -%>\n<%= rowCreator.format('TimeZone Details',ciotsdDateSessionStarted); -%>\n<%= rowCreator.format('Extension',ciotsdFixedIPAddress); -%>\n<%- renderTemplate(\"ModiniDetails\"); -%>\n<%- renderTemplate(\"CDRandRODSDetails\"); -%>\n<%- renderTemplate(\"IncidentsData\"); -%>\n<%= mergeLink -%>\n<%- renderTemplate(\"ShowRules\"); -%>\n"}, {"name": "CTInstructionsNBNTroubleTicket", "active": true, "title": "CT Instructions NBN Trouble Ticket (Module)", "description": "CT instructions to be added towards end of each NBN trouble ticket template", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "\n\nCT please perform the following:\n\n1. Prove network integrity from the NBN NTU to customer or managed router, confirm IP Connectivity.\n2. Determine if internal customer owned cabling repairs are required and advise customer (any repairs to be done at FFS)."}, {"name": "CustomerDetailsModule", "active": true, "title": "Customer Details (Module)", "description": "Module which shows general information on the customer's service", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nlet rowCreator = new RowCreator();\n\nlet RASSPSource = s[RASSPSourceName] ? s[RASSPSourceName] : null;\n\nlet magpieProductData = null;\n\nif (s.MAGPIE && s.MAGPIE.rawData && typeof s.MAGPIE.rawData === 'object') {\n    for (let rawDataKey in s.MAGPIE.rawData) {\n        // Looks for an object in rawData with the \"Product Name\" key\n        if (s.MAGPIE.rawData[rawDataKey] && s.MAGPIE.rawData[rawDataKey]['Product Name']) {\n            magpieProductData = s.MAGPIE.rawData[rawDataKey];\n            break;\n        }\n    }\n}\n\nif (!magpieProductData && s.MAGPIEBATTAAA && s.MAGPIEBATTAAA.rawData && typeof s.MAGPIEBATTAAA.rawData === 'object') {\n    for (let rawDataKey in s.MAGPIEBATTAAA.rawData) {\n        // Looks for an object in rawData with the \"Product Name\" key\n        if (s.MAGPIEBATTAAA.rawData[rawDataKey] && s.MAGPIEBATTAAA.rawData[rawDataKey]['Product Name']) {\n            magpieProductData = s.MAGPIEBATTAAA.rawData[rawDataKey];\n            break;\n        }\n    }\n}\n-%>\n--Customer Details--\n<% if (s.MAGPIE && s.MAGPIE.rawData && s.MAGPIE.rawData.IPWAN) { -%>\n<%= rowCreator.format(\"IPWAN Network\", s.MAGPIE && s.MAGPIE.IPWANNETWORKFNN ? s.MAGPIE.IPWANNETWORKFNN : \"Not found\"); -%>\n<%= rowCreator.format(\"IPWAN Port\", s.MAGPIE && s.MAGPIE.rawData && s.MAGPIE.rawData.IPWAN && s.MAGPIE.rawData.IPWAN.IPWAN_FNN ? s.MAGPIE.rawData.IPWAN.IPWAN_FNN : \"Not found\"); -%>\n<% } -%>\n<% if (s.MAGPIE && s.MAGPIE.rawData && s.MAGPIE.rawData.IPMAN && s.MAGPIE.rawData.IPMAN.IPMAN_FNN) { -%>\n<%= rowCreator.format(\"IPMAN Network\", s.MAGPIE.rawData && s.MAGPIE.rawData.IPMAN && s.MAGPIE.rawData.IPMAN[\"Member of IPMAN Network\"] ? s.MAGPIE.rawData.IPMAN[\"Member of IPMAN Network\"] : \"Not found\"); -%>\n<%= rowCreator.format(\"IPMAN Port FNN\", s.MAGPIE.rawData && s.MAGPIE.rawData.IPMAN && s.MAGPIE.rawData.IPMAN.IPMAN_FNN ? s.MAGPIE.rawData.IPMAN.IPMAN_FNN : \"Not found\"); -%>\n<% } -%>\n<%= rowCreator.format(\"CIDN\", data.CIDN ? data.CIDN : \"Not found\"); -%>\n<%= rowCreator.format(\"Customer\", magpieProductData && magpieProductData.Customer ? magpieProductData.Customer : \"Not found\"); -%>\n<%= rowCreator.format(\"Customer Consent\", r.MDR068 && r.MDR068.customerConsent ? r.MDR068.customerConsent : \"Not found\"); -%>\n<%= rowCreator.format(\"Location\", data.address ? data.address : \"Not found\"); -%>\n<%= rowCreator.format(\"CE IP Address\", magpieProductData && magpieProductData[\"CE IP address\"] ? magpieProductData[\"CE IP address\"] : \"Not found\"); -%>\n<%= rowCreator.format(\"Routing Protocol\", magpieProductData && magpieProductData[\"Routing Protocol\"] ? magpieProductData[\"Routing Protocol\"] : \"Not found\"); -%>\n<%= rowCreator.format(\"Service Speed\", magpieProductData && magpieProductData[\"Service Speed\"] ? magpieProductData[\"Service Speed\"] : \"Not found\"); -%>\n<% if (data.carriageType === 'NBN') {\nlet nbnFnn = null;\nconst nbnFnnRegex = /^N\\d{7}R$/;\nif (typeof data.fnn === 'string' && data.fnn.match(nbnFnnRegex)) {\n    nbnFnn = data.fnn;\n}\n\nif (!nbnFnn && s.MAGPIE && s.MAGPIE.rawData && s.MAGPIE.rawData.SEARCH_SELECTED &&\n    typeof s.MAGPIE.rawData.SEARCH_SELECTED.Attributes === 'string' &&\n    s.MAGPIE.rawData.SEARCH_SELECTED.Attributes.match(nbnFnnRegex)) {\n    nbnFnn = s.MAGPIE.rawData.SEARCH_SELECTED.Attributes;\n}\n\nif (!nbnFnn && s.BATTAAA && s.BATTAAA.session && Array.isArray(s.BATTAAA.session.taaa_resp) &&\n    Array.isArray(s.BATTAAA.session.taaa_resp[0]) && s.BATTAAA.session.taaa_resp[0][0] &&\n    typeof s.BATTAAA.session.taaa_resp[0][0].sid === 'string' && s.BATTAAA.session.taaa_resp[0][0].sid.match(nbnFnnRegex)\n) {\n    nbnFnn = s.BATTAAA.session.taaa_resp[0][0].sid;\n}\n-%>\n<%= rowCreator.format(\"AVC\", data.nbnId ? data.nbnId : \"Not found\"); -%>\n<%= rowCreator.format(\"Access Type\", data.nbnAccessType ? data.nbnAccessType : \"Not found\"); -%>\n<%= rowCreator.format(\"FNN\", nbnFnn ? nbnFnn : \"Not found\"); -%>\n<% } else if (data.carriageType === 'IPMAN') {\nlet adaptServiceFNNs = [];\nif (Array.isArray(data.generatedServiceChecks)) {\n    for (let serviceCheck of data.generatedServiceChecks) {\n        let adaptServiceString = serviceCheck.fnn;\n        if (serviceCheck.sourcesData && serviceCheck.sourcesData.MAGPIE && serviceCheck.sourcesData.MAGPIE.rawData) {\n            if (serviceCheck.sourcesData.MAGPIE.rawData.IPMAN && serviceCheck.sourcesData.MAGPIE.rawData.IPMAN[\"Product Name\"]) {\n                adaptServiceString += ` (${serviceCheck.sourcesData.MAGPIE.rawData.IPMAN[\"Product Name\"]})`;\n            } else if (serviceCheck.sourcesData.MAGPIE.rawData.TE && serviceCheck.sourcesData.MAGPIE.rawData.TE[\"Product Name\"]) {\n                adaptServiceString += ` (${serviceCheck.sourcesData.MAGPIE.rawData.TE[\"Product Name\"]})`;\n            }\n        }\n        adaptServiceFNNs.push(adaptServiceString);\n    }\n}\n\nlet productCharacterstics = {};\nif (Array.isArray(s.WASProductOrderDetails?.productOrderItem?.[0]?.product?.productCharacteristic)) {\n    for (let characteristic of s.WASProductOrderDetails.productOrderItem[0].product.productCharacteristic) {\n        if (typeof characteristic?.name === 'string' && characteristic?.value !== undefined) {\n            productCharacterstics[characteristic.name] = characteristic.value;\n        }\n    }\n}\n-%>\n<%= rowCreator.format(\"Access Bandwidth\", productCharacterstics?.subscribedRate); -%>\n<%= rowCreator.format(\"Access Type\", productCharacterstics?.bcsSubtype); -%>\n<%= rowCreator.format(\"FNN\", s.WASProductOrderDetails?.serviceID); -%>\n<%= rowCreator.format(\"Adapt ID\", magpieProductData && magpieProductData[\"Adapt ID\"] ? magpieProductData[\"Adapt ID\"] : \"Not found\"); -%>\n<%= rowCreator.format(\"Associated Adapt Services\", adaptServiceFNNs); -%>\n<%= rowCreator.format(\"VLAN Domain\", magpieProductData && typeof magpieProductData[\"VLAN Domain\"] === \"string\" ? magpieProductData[\"VLAN Domain\"].replace(\":\", \"\") : null); -%>\n<%= rowCreator.format(\"Media Type\", magpieProductData && magpieProductData[\"Media type\"] ? magpieProductData[\"Media type\"] : \"Not found\"); -%>\n<%= rowCreator.format(\"NTU Type\", magpieProductData && magpieProductData[\"NTU type\"] ? magpieProductData[\"NTU type\"] : null); -%>\n<% } -%>"}, {"name": "DeviceDetailsModule", "active": true, "title": "<PERSON><PERSON> (Module)", "description": "Module to extract product name and service type from MAGPIE and other device information", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nlet rowCreator = new RowCreator();\n\nlet RASSPSource = s[RASSPSourceName] ? s[RASSPSourceName] : null;\nlet magpieProductData = null;\nlet magpieMDNData = s.MAGPIE && s.MAGPIE.rawData && s.MAGPIE.rawData.MDN ? s.MAGPIE.rawData.MDN : null;\n\nif (s.MAGPIE && s.MAGPIE.rawData && typeof s.MAGPIE.rawData === 'object') {\n    for (let rawDataKey in s.MAGPIE.rawData) {\n        // Looks for an object in rawData with the \"Product Name\" key\n        if (s.MAGPIE.rawData[rawDataKey] && s.MAGPIE.rawData[rawDataKey]['Product Name']) {\n            magpieProductData = s.MAGPIE.rawData[rawDataKey];\n            break;\n        }\n    }\n}\n\nif (!magpieProductData && s.MAGPIEBATTAAA && s.MAGPIEBATTAAA.rawData && typeof s.MAGPIEBATTAAA.rawData === 'object') {\n    for (let rawDataKey in s.MAGPIEBATTAAA.rawData) {\n        // Looks for an object in rawData with the \"Product Name\" key\n        if (s.MAGPIEBATTAAA.rawData[rawDataKey] && s.MAGPIEBATTAAA.rawData[rawDataKey]['Product Name']) {\n            magpieProductData = s.MAGPIEBATTAAA.rawData[rawDataKey];\n            break;\n        }\n    }\n}\nlet magpieServiceTypeData = forMDN ? magpieMDNData : magpieProductData;\n\nlet tFibreMagpieData = null;\nif (s.MAGPIE?.rawData?.IPMAN?.['Product Name'] === 'Telstra Fibre') {\n    tFibreMagpieData = s.MAGPIE.rawData.IPMAN;\n} else if (s.MAGPIE?.rawData?.TE?.['Product Name'] === 'Telstra Fibre') {\n    tFibreMagpieData = s.MAGPIE.rawData.TE;\n}\n\nif (Array.isArray(data.generatedServiceChecks)) {\n    for (let serviceCheck of data.generatedServiceChecks) {\n        if (tFibreMagpieData) {\n            break;\n        }\n        \n        if (serviceCheck.sourcesData.MAGPIE?.rawData?.IPMAN?.['Product Name'] === 'Telstra Fibre') {\n            tFibreMagpieData = serviceCheck.sourcesData.MAGPIE.rawData.IPMAN;\n        } else if (serviceCheck.sourcesData.MAGPIE?.rawData?.TE?.['Product Name'] === 'Telstra Fibre') {\n            tFibreMagpieData = serviceCheck.sourcesData.MAGPIE.rawData.TE;\n        }\n    }\n}\n-%>\n--Device Details--\n<%= rowCreator.format(\"Product Name\", magpieProductData && magpieProductData[\"Product Name\"] ? magpieProductData[\"Product Name\"] : \"Not found\"); -%>\n<%= rowCreator.format(\"Service Type\", data.serviceType ? data.serviceType : \"Not found\"); -%>\n<% if (!forMDN) { -%>\n<%= rowCreator.format(\"Category of Service\", r.MDR009 && r.MDR009.manageCategory ? r.MDR009.manageCategory : \"Not found\"); -%>\n<% } -%>\n<% if (data.carriageType === 'IPMAN') { -%>\n<%= rowCreator.format(\"Configuration\", magpieProductData && magpieProductData[\"Configuration\"] ? magpieProductData[\"Configuration\"] : null); -%>\n<%= rowCreator.format(\"Port Type\", magpieProductData && magpieProductData[\"Port Type\"] ? magpieProductData[\"Port Type\"] : null); -%>\n<%= rowCreator.format(\"Port Configuration\", magpieProductData && magpieProductData[\"Port Configuration\"] ? magpieProductData[\"Port Configuration\"] : null); -%>\n<% } -%>\n<% if (Array.isArray(s.WASProductOrderDetails?.productOrderItem?.[0]?.product?.productCharacteristic)) {\nlet productCharacterstics = {};\n\nfor (let characteristic of s.WASProductOrderDetails.productOrderItem[0].product.productCharacteristic) {\n    if (typeof characteristic?.name === 'string' && characteristic?.value !== undefined) {\n        productCharacterstics[characteristic.name] = characteristic.value;\n    }\n}\n-%>\n<%= rowCreator.format(\"Subscribed Rate\", productCharacterstics.subscribedRate || \"Not found\"); -%>\n<%= rowCreator.format(\"Customer IPv4 Address\", productCharacterstics.customerIPv4Address || \"Not found\"); -%>\n<%= rowCreator.format(\"TID IPv4 Address\", productCharacterstics.tidIPv4Address || \"Not found\"); -%>\n<%= rowCreator.format(\"IPv4 Subnet Length\", productCharacterstics.ipV4SubnetLength || \"Not found\"); -%>\n<%= rowCreator.format(\"BCS Service FNN (VLAN)\", productCharacterstics.bcsServiceFnn || \"Not found\"); -%>\n<%= rowCreator.format(\"TID Service Type\", productCharacterstics.serviceType || \"Not found\"); -%>\n<%= rowCreator.format(\"Opshandle\", productCharacterstics.opshandle || \"Not found\"); -%>\n\nAccess\n<%= rowCreator.format(\"BCS Subtype\", productCharacterstics.bcsSubtype || \"Not found\"); -%>\n<%= rowCreator.format(\"Interface Speed\", productCharacterstics.interfaceSpeed || \"Not found\"); -%>\n<%= rowCreator.format(\"Duplex Type\", productCharacterstics.duplexType || \"Not found\"); -%>\n<%= rowCreator.format(\"Access Bandwidth\", productCharacterstics.accessBandwidth || \"Not found\"); -%>\n<%= rowCreator.format(\"OVC ID\", productCharacterstics.ovcId || \"Not found\"); -%>\n<%= rowCreator.format(\"NNI\", productCharacterstics.nbnNniLag || \"Not found\"); -%>\n<%= rowCreator.format(\"SVLAN\", productCharacterstics.sVlan || \"Not found\"); -%>\n<%= rowCreator.format(\"Customer VLAN\", productCharacterstics.vlanId || \"Not found\"); -%>\n<%= rowCreator.format(\"TID Subscriber\", productCharacterstics.opsHandle && productCharacterstics.routerName ? `${productCharacterstics.opsHandle}@${productCharacterstics.routerName}` : \"Not found\"); -%>\n<%= rowCreator.format(\"TID Router I/F\", typeof productCharacterstics.routerName === 'string' && productCharacterstics.interfaceName ? `${productCharacterstics.routerName.split('.')[0]} ${productCharacterstics.interfaceName}` : \"Not found\"); -%>\n<%= rowCreator.format(\"POP\", productCharacterstics.pcsName2 || \"Not found\"); -%>\n<% } -%>\n<% if (tFibreMagpieData) { -%>\n<%= rowCreator.format(\"Service Speed\", tFibreMagpieData[\"Service Speed\"]); -%>\n<%= rowCreator.format(\"Bandwidth Purchased By Customer\", tFibreMagpieData[\"Bandwidth Purchased by Customer\"]); -%>\n<%= rowCreator.format(\"Total Bandwidth Consumed by Adapt Services\", tFibreMagpieData[\"Total Bandwidth consumed by Adapt Services on this port\"]); -%>\n<% } -%>\n\n<%= rowCreator.format(\"RASS Status\", RASSPSource && RASSPSource.NotFound !== true && RASSPSource.FNN ? \"FNN found\": \"FNN not found\"); -%>\n<% if (data.carriageType === 'BDSL') { -%>\n\n<%= rowCreator.format(\"NTU Type\", s.GMACS && s.GMACS.TYPE ? s.GMACS.TYPE : (magpieProductData && magpieProductData[\"NTU type\"] ? magpieProductData[\"NTU type\"] : \"Not found\")); -%>\n<% } -%>"}, {"name": "DeviceStatusModule", "active": true, "title": "Device Status (Module)", "description": "Module to display device status information", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nlet rowCreator = new RowCreator();\n-%>\n--Device Status--\n<%= rowCreator.format(\"Device power / reset status\", r.MDR121 && typeof r.MDR121.valueMsg === \"string\" ? r.MDR121.valueMsg : \"Not found\"); -%>\n<%= rowCreator.format(\"Device status\", r.MDR122 && typeof r.MDR122.valueMsg === \"string\" ? r.MDR122.valueMsg : \"Not found\"); -%>"}, {"name": "eDCPCustomerTemplate", "active": true, "title": "eDCPCustomerTemplate (Text)", "description": "To show eDCP customer template summary", "allowedSystemsToAppend": ["ServiceCentral"], "suite": [], "templateType": "Service Check", "formatType": "Text", "listCondition": "r.MDR081 && r.MDR081.customerNo !== '';", "defaultPriority": 0, "template": "<% let rowCreator = new RowCreator();\nlet eDCPCustomerSRV; let eDCPCustomerSRCSD; let eDCPCustomerSRSMS; let eDCPCustomerSRGPRS; let eDCPCustomerSRCAS; let eDCPCustomerSRSCA;\nlet eDCPCustomerTU; let eDCPCustomerSP; let eDCPCustomerRTT;\nif (s.eDCPCustomer) {\n  if (s.eDCPCustomer.statusResult) {\n    if (s.eDCPCustomer.statusResult.voice                ) eDCPCustomerSRV    = s.eDCPCustomer.statusResult.voice.replace('1','Yes').replace('0','No');\n    if (s.eDCPCustomer.statusResult.CSD                  ) eDCPCustomerSRCSD  = s.eDCPCustomer.statusResult.CSD.replace('1','Yes').replace('0','No');\n    if (s.eDCPCustomer.statusResult.SMS                  ) eDCPCustomerSRSMS  = s.eDCPCustomer.statusResult.SMS.replace('1','Yes').replace('0','No');\n    if (s.eDCPCustomer.statusResult.GPRS                 ) eDCPCustomerSRGPRS = s.eDCPCustomer.statusResult.GPRS.replace('1','Yes').replace('0','No');\n    if (s.eDCPCustomer.statusResult.CustomerAccessService) eDCPCustomerSRCAS  = s.eDCPCustomer.statusResult.CustomerAccessService.replace('1','Yes').replace('0','No');\n    if (s.eDCPCustomer.statusResult.customerSMSCAccount  ) eDCPCustomerSRSCA  = s.eDCPCustomer.statusResult.customerSMSCAccount.replace('1','Yes').replace('0','No');\n  }\n  if (s.eDCPCustomer.trafficUsage) {\n    var rcount = Object.keys(s.eDCPCustomer.trafficUsage).length;\n    if (rcount > 0) eDCPCustomerTU = [];\n    for (i = 0; i < rcount ; i++) {\n      if (s.eDCPCustomer.trafficUsage[i]) {\n        var ip = '[' + ((i < 10) ? String(i) + '] ' : String(i) + ']');\n        var eDCPCustomerTUAD = ''; var eDCPCustomerTUGV = ''; var eDCPCustomerTUSV = '';\n        if (s.eDCPCustomer.trafficUsage[i].aggregationDate) eDCPCustomerTUAD = s.eDCPCustomer.trafficUsage[i].aggregationDate;\n        if (s.eDCPCustomer.trafficUsage[i].gprsVolume     ) eDCPCustomerTUGV = s.eDCPCustomer.trafficUsage[i].gprsVolume;\n        if (s.eDCPCustomer.trafficUsage[i].smsVolume      ) eDCPCustomerTUSV = s.eDCPCustomer.trafficUsage[i].smsVolume;\n          eDCPCustomerTU.push(`${ip} AggregationDate ${eDCPCustomerTUAD} GPRSVolume ${eDCPCustomerTUGV} SMSVolume ${eDCPCustomerTUSV}`);\n      }\n    }\n  }\n  if (s.eDCPCustomer.subscriptionPackages ) {\n    var rcount = Object.keys(s.eDCPCustomer.subscriptionPackages ).length;\n    if (rcount > 0) eDCPCustomerSP = [];\n    for (i = 0; i < rcount ; i++) {\n      if (s.eDCPCustomer.subscriptionPackages[i]) {\n        var ip = '[' + ((i < 10) ? String(i) + '] ' : String(i) + ']');\n        var eDCPCustomerSPSPN = ''; var eDCPCustomerSPSC = ''; var eDCPCustomerSPSPD = '';\n        if (s.eDCPCustomer.subscriptionPackages[i].subscriptionPackageName)        eDCPCustomerSPSPN = s.eDCPCustomer.subscriptionPackages[i].subscriptionPackageName;\n        if (s.eDCPCustomer.subscriptionPackages[i].serviceContract)                eDCPCustomerSPSC  = s.eDCPCustomer.subscriptionPackages[i].serviceContract;\n        if (s.eDCPCustomer.subscriptionPackages[i].subscriptionPackageDescription) eDCPCustomerSPSPD = s.eDCPCustomer.subscriptionPackages[i].subscriptionPackageDescription;\n          eDCPCustomerSP.push(`${ip} AggregationDate ${SubscriptionPackageName} ServiceContract ${eDCPCustomerSPSC} Description ${eDCPCustomerSPSPD}`);\n      }\n    }\n  }\n  if (s.eDCPCustomer.realtimeTrace) {\n    var rcount = Object.keys(s.eDCPCustomer.realtimeTrace).length;\n    if (rcount > 0) eDCPCustomerRTT = [];\n    for (i = 0; i < rcount ; i++) {\n      if (s.eDCPCustomer.realtimeTrace[i]) {\n        var ip = '[' + ((i < 10) ? String(i) + '] ' : String(i) + ']');\n        var eDCPCustomerRTTTS = ''; var eDCPCustomerRTTI = ''; var eDCPCustomerRTTOM = ''; var eDCPCustomerRTTE = '';\n         var eDCPCustomerRTTCC = ''; var eDCPCustomerRTTOC = ''; var eDCPCustomerRTTGC = '';\n        if (s.eDCPCustomer.realtimeTrace[i].timeStamp        ) eDCPCustomerRTTTS = s.eDCPCustomer.realtimeTrace[i].timeStamp;\n        if (s.eDCPCustomer.realtimeTrace[i].imsi             ) eDCPCustomerRTTI  = s.eDCPCustomer.realtimeTrace[i].imsi;\n        if (s.eDCPCustomer.realtimeTrace[i].originatingMsisdn) eDCPCustomerRTTOM = s.eDCPCustomer.realtimeTrace[i].originatingMsisdn;\n        if (s.eDCPCustomer.realtimeTrace[i].event            ) eDCPCustomerRTTE  = s.eDCPCustomer.realtimeTrace[i].event;\n        if (s.eDCPCustomer.realtimeTrace[i].countryCode      ) eDCPCustomerRTTCC = s.eDCPCustomer.realtimeTrace[i].countryCode;\n        if (s.eDCPCustomer.realtimeTrace[i].operatorCode     ) eDCPCustomerRTTOC = s.eDCPCustomer.realtimeTrace[i].operatorCode;\n        if (s.eDCPCustomer.realtimeTrace[i].gtpCause         ) eDCPCustomerRTTGC = s.eDCPCustomer.realtimeTrace[i].gtpCause;\n          eDCPCustomerRTT.push(`${ip} TimeStamp ${eDCPCustomerRTTTS} IMSI ${eDCPCustomerRTTI} OriginatingMSISDN ${eDCPCustomerRTTOM} Event ${eDCPCustomerRTTE} CountryCode ${eDCPCustomerRTTCC} OperatorCode ${eDCPCustomerRTTOC} GtpCause ${eDCPCustomerRTTGC}`);\n      }\n    }\n  }\n}\n\nlet mergeLink = '\\nMerge Result Link: https://merge.in.telstra.com.au/serviceCheck/view/html/'  + data.id;\n-%>\n<%= rowCreator.format('Voice',eDCPCustomerSRV); -%>\n<%= rowCreator.format('CSD',eDCPCustomerSRCSD); -%>\n<%= rowCreator.format('SMS',eDCPCustomerSRSMS); -%>\n<%= rowCreator.format('GPRS',eDCPCustomerSRGPRS); -%>\n<%= rowCreator.format('Customer Access Service',eDCPCustomerSRCAS); -%>\n<%= rowCreator.format('Customer SMS CAccount',eDCPCustomerSRSCA); -%>\n<%= rowCreator.format('Traffic Usage',eDCPCustomerTU, false); -%>\n<%= rowCreator.format('Subscription Packages',eDCPCustomerSP, false); -%>\n<%= rowCreator.format('Real Time Trace',eDCPCustomerRTT, false); -%>\n<%= mergeLink -%>\n<%- renderTemplate(\"ShowRules\"); -%>\n"}, {"name": "eDCPFNNTemplate", "active": true, "title": "eDCPFNNTemplate (Text)", "description": "To show eDCP incident summary", "allowedSystemsToAppend": ["ServiceCentral"], "suite": [], "templateType": "Service Check", "formatType": "Text", "listCondition": "(s.eDCPMSISDN && s.eDCPMSISDN.subscriptionTraffic) ||\n(s.eDCPMSISDN && s.eDCPMSISDN.simResource) ||\n(s.MODINI && s.MODINI['Service Potential Issues'] && s.MODINI['Service Potential Issues'].includes('eDCP')) ||\n(s['MODINI'] && s['MODINI'].hasOwnProperty('APNs')) || (s['CDRLIVE-L'] && s['CDRLIVE-L'].Data && s['CDRLIVE-L'].Data.Id) ||\n(s[\"Hangar - RODS Site Data\"] && Array.isArray(s[\"Hangar - RODS Site Data\"].rodsData) && s[\"Hangar - RODS Site Data\"].rodsData.length > 0);", "defaultPriority": 0, "template": "<% let rowCreator = new RowCreator();\nlet eDCPMSISDNHeader; let eDCPMSISDNMsisdn; let eDCPMSISDNImei; let eDCPMSISDNOperatorCode; let eDCPMSISDNCountryCode; \nlet eDCPMSISDNVlrGt; let eDCPMSISDNTimestamp; let eDCPMSISDNActivation; let eDCPMSISDNIp; let eDCPMSISDNApn; let eDCPMSISDNPdpActive;\nlet eDCPMSISDNSRHeader; let eDCPMSISDNSRIcc; let eDCPMSISDNSRImei; let eDCPMSISDNSRCustomerNo; let eDCPMSISDNSRAccessControlClass; \nlet eDCPMSISDNSRRoamProfileName; let eDCPMSISDNSRPdpContextProfileName; let eDCPMSISDNSRGprs; let eDCPMSISDNSRLte; let eDCPMSISDNSRSmsMo;\nlet eDCPMSISDNSRSmsMt; let eDCPMSISDNSRCsd; let eDCPMSISDNSRVoice; let eDCPMSISDNSRClip; let eDCPMSISDNSRInstallationDate; \nlet eDCPMSISDNSRPbrExitDate; let eDCPMSISDNSRLastSubscriptionDateChange; let eDCPMSISDNSRCustomerName; let eDCPMSISDNSRApns; let eDCPMSISDNSimResources;\n  \nif (s.eDCPMSISDN) {\n  if (s.eDCPMSISDN.subscriptionTraffic) {\n    eDCPMSISDNHeader = '\\nSubscription\\n------------\\n';\n    if (s.eDCPMSISDN.subscriptionTraffic.msisdn)        eDCPMSISDNMsisdn = s.eDCPMSISDN.subscriptionTraffic.msisdn;\n    if (s.eDCPMSISDN.subscriptionTraffic.imei)          eDCPMSISDNImei = s.eDCPMSISDN.subscriptionTraffic.imei;\n    if (s.eDCPMSISDN.subscriptionTraffic.operatorCode)  eDCPMSISDNOperatorCode = s.eDCPMSISDN.subscriptionTraffic.operatorCode;\n    if (s.eDCPMSISDN.subscriptionTraffic.countryCode)   eDCPMSISDNCountryCode = s.eDCPMSISDN.subscriptionTraffic.countryCode;\n    if (s.eDCPMSISDN.subscriptionTraffic.vlrGt)         eDCPMSISDNVlrGt = s.eDCPMSISDN.subscriptionTraffic.vlrGt;\n    if (s.eDCPMSISDN.subscriptionTraffic.timestamp)     eDCPMSISDNTimestamp = s.eDCPMSISDN.subscriptionTraffic.timestamp;\n    if (s.eDCPMSISDN.subscriptionTraffic.activation)    eDCPMSISDNActivation = s.eDCPMSISDN.subscriptionTraffic.activation;\n    if (s.eDCPMSISDN.subscriptionTraffic.ip)            eDCPMSISDNIp = s.eDCPMSISDN.subscriptionTraffic.ip;\n    if (s.eDCPMSISDN.subscriptionTraffic.apn)           eDCPMSISDNApn = s.eDCPMSISDN.subscriptionTraffic.apn;\n    if (s.eDCPMSISDN.subscriptionTraffic.pdpActive) {\n      if (s.eDCPMSISDN.subscriptionTraffic.pdpActive.toUpperCase() == 'TRUE') eDCPMSISDNPdpActive = 'Yes'; else eDCPMSISDNPdpActive = 'No';\n    }\n  }\n\n  if (s.eDCPMSISDN.simResource) {\n    eDCPMSISDNSRHeader = 'SimResource\\n-----------\\n';\n    if (s.eDCPMSISDN.simResource.icc) eDCPMSISDNSRIcc = s.eDCPMSISDN.simResource.icc;\n    if (s.eDCPMSISDN.simResource.imei) eDCPMSISDNSRImei = s.eDCPMSISDN.simResource.imei;\n    if (s.eDCPMSISDN.simResource.customerNo) eDCPMSISDNSRCustomerNo = s.eDCPMSISDN.simResource.customerNo;\n    if (s.eDCPMSISDN.simResource.accessControlClass) eDCPMSISDNSRAccessControlClass = s.eDCPMSISDN.simResource.accessControlClass;\n    if (s.eDCPMSISDN.simResource.roamProfileName) eDCPMSISDNSRRoamProfileName = s.eDCPMSISDN.simResource.roamProfileName;\n    if (s.eDCPMSISDN.simResource.pdpContextProfileName) eDCPMSISDNSRPdpContextProfileName = s.eDCPMSISDN.simResource.pdpContextProfileName;\n    if (s.eDCPMSISDN.simResource.gprs)  { if (s.eDCPMSISDN.simResource.gprs.toUpperCase() == 'TRUE')  eDCPMSISDNSRGprs  = 'Yes'; else eDCPMSISDNSRGprs  = 'No'; }\n    if (s.eDCPMSISDN.simResource.lte)   { if (s.eDCPMSISDN.simResource.lte.toUpperCase() == 'TRUE')   eDCPMSISDNSRLte   = 'Yes'; else eDCPMSISDNSRLte   = 'No'; }\n    if (s.eDCPMSISDN.simResource.smsMo) { if (s.eDCPMSISDN.simResource.smsMo.toUpperCase() == 'TRUE') eDCPMSISDNSRSmsMo = 'Yes'; else eDCPMSISDNSRSmsMo = 'No'; }\n    if (s.eDCPMSISDN.simResource.smsMt) { if (s.eDCPMSISDN.simResource.smsMt.toUpperCase() == 'TRUE') eDCPMSISDNSRSmsMt = 'Yes'; else eDCPMSISDNSRSmsMt = 'No'; }\n    if (s.eDCPMSISDN.simResource.csd)   { if (s.eDCPMSISDN.simResource.csd.toUpperCase() == 'TRUE')   eDCPMSISDNSRCsd   = 'Yes'; else eDCPMSISDNSRCsd   = 'No'; }\n    if (s.eDCPMSISDN.simResource.voice) { if (s.eDCPMSISDN.simResource.voice.toUpperCase() == 'TRUE') eDCPMSISDNSRVoice = 'Yes'; else eDCPMSISDNSRVoice = 'No'; }\n    if (s.eDCPMSISDN.simResource.clip)  { if (s.eDCPMSISDN.simResource.clip.toUpperCase() == 'TRUE')  eDCPMSISDNSRClip  = 'Yes'; else eDCPMSISDNSRClip  = 'No'; }\n    if (s.eDCPMSISDN.simResource.installationDate) eDCPMSISDNSRInstallationDate = s.eDCPMSISDN.simResource.installationDate;\n    if (s.eDCPMSISDN.simResource.pbrExitDate) eDCPMSISDNSRPbrExitDate = s.eDCPMSISDN.simResource.pbrExitDate;\n    if (s.eDCPMSISDN.simResource.lastSubscriptionDateChange) eDCPMSISDNSRLastSubscriptionDateChange = s.eDCPMSISDN.simResource.lastSubscriptionDateChange;\n    if (s.eDCPMSISDN.simResource.customerName) eDCPMSISDNSRCustomerName = s.eDCPMSISDN.simResource.customerName;\n    var rcount = Object.keys(s.eDCPMSISDN.simResource.apns).length;\n    if (rcount > 0) eDCPMSISDNSRApns = [];\n    for (i = 0; i < rcount; i++) {\n      if (s.eDCPMSISDN.simResource.apns[i]) {\n        var ip = '[' + ((i < 10) ? String(i) + '] ' : String(i) + ']');\n        var eDCPMSISDNSRName = ''; var eDCPMSISDNSRDescription = '';\n        if (s.eDCPMSISDN.simResource.apns[i].name) eDCPMSISDNSRName = s.eDCPMSISDN.simResource.apns[i].name;\n        if (s.eDCPMSISDN.simResource.apns[i].description) eDCPMSISDNSRDescription = s.eDCPMSISDN.simResource.apns[i].description;\n        eDCPMSISDNSRApns.push(`${ip} Name ${eDCPMSISDNSRName} Description ${eDCPMSISDNSRDescription}`);\n      }\n    }\n  }\n\n  if (s.eDCPMSISDN.simResources) {  \n    var rcount = Object.keys(s.eDCPMSISDN.simResources).length;\n    if (rcount > 0) eDCPMSISDNSimResources = [];\n    for (i = 0; i < rcount ; i++) {\n      if (s.eDCPMSISDN.simResources[i]) {\n        var ip = '[' + ((i < 10) ? String(i) + '] ' : String(i) + ']');\n        var eDCPMSISDNSSS = ''; var eDCPMSISDNSCD = ''; var eDCPMSISDNSS = ''; var eDCPMSISDNPON = ''; var eDCPMSISDNFAD = ''; \n        if (s.eDCPMSISDN.simResources[i].simSubscriptionStatus)  eDCPMSISDNSSS = s.eDCPMSISDN.simResources[i].simSubscriptionStatus;\n        if (s.eDCPMSISDN.simResources[i].simCardDescription   )  eDCPMSISDNSCD = s.eDCPMSISDN.simResources[i].simCardDescription;\n        if (s.eDCPMSISDN.simResources[i].simSpecification     )  eDCPMSISDNSS = s.eDCPMSISDN.simResources[i].simSpecification;\n        if (s.eDCPMSISDN.simResources[i].productOfferName     )  eDCPMSISDNPON = s.eDCPMSISDN.simResources[i].productOfferName;\n        if (s.eDCPMSISDN.simResources[i].firstActivationDate  )  eDCPMSISDNFAD = s.eDCPMSISDN.simResources[i].firstActivationDate;\n        eDCPMSISDNSimResources.push(`${ip} SimSubscriptionStatus ${eDCPMSISDNSSS} SimCardDescription ${eDCPMSISDNSCD} SimSpecification ${eDCPMSISDNSS} ProductOfferName ${eDCPMSISDNPON} FirstActivationDate ${eDCPMSISDNFAD}`);\n      }\n    }\n  }\n}\nlet mergeLink = '\\nMerge Result Link: https://merge.in.telstra.com.au/serviceCheck/view/html/'  + data.id;\n-%>\n<%= eDCPMSISDNHeader -%>\n<%= rowCreator.format('MSISDN',eDCPMSISDNMsisdn); -%>\n<%= rowCreator.format('IMEI',eDCPMSISDNImei); -%>\n<%= rowCreator.format('Operator Code',eDCPMSISDNOperatorCode); -%>\n<%= rowCreator.format('Country Code',eDCPMSISDNCountryCode); -%>\n<%= rowCreator.format('Vlr Gt',eDCPMSISDNVlrGt); -%>\n<%= rowCreator.format('Timestamp',eDCPMSISDNTimestamp); -%>\n<%= rowCreator.format('Activation',eDCPMSISDNActivation); -%>\n<%= rowCreator.format('IP',eDCPMSISDNIp); -%>\n<%= rowCreator.format('APN',eDCPMSISDNApn); -%>\n<%= rowCreator.format('PdpActive',eDCPMSISDNPdpActive); -%>\n<%= eDCPMSISDNSRHeader -%>\n<%= rowCreator.format('ICC', eDCPMSISDNSRIcc); -%>\n<%= rowCreator.format('IMEI', eDCPMSISDNSRImei); -%>\n<%= rowCreator.format('Customer No', eDCPMSISDNSRCustomerNo); -%>\n<%= rowCreator.format('Access Control Class', eDCPMSISDNSRAccessControlClass); -%>\n<%= rowCreator.format('Roam Profile Name', eDCPMSISDNSRRoamProfileName); -%>\n<%= rowCreator.format('Pdp Context Profile Name', eDCPMSISDNSRPdpContextProfileName); -%>\n<%= rowCreator.format('Gprs', eDCPMSISDNSRGprs); -%>\n<%= rowCreator.format('Lte', eDCPMSISDNSRLte); -%>\n<%= rowCreator.format('SmsMo', eDCPMSISDNSRSmsMo); -%>\n<%= rowCreator.format('SmsMt', eDCPMSISDNSRSmsMt); -%>\n<%= rowCreator.format('Csd', eDCPMSISDNSRCsd); -%>\n<%= rowCreator.format('Voice', eDCPMSISDNSRVoice); -%>\n<%= rowCreator.format('Clip', eDCPMSISDNSRClip); -%>\n<%= rowCreator.format('Installation Date', eDCPMSISDNSRInstallationDate); -%>\n<%= rowCreator.format('Pbr Exit Date', eDCPMSISDNSRPbrExitDate); -%>\n<%= rowCreator.format('Last Subscription Date Change', eDCPMSISDNSRLastSubscriptionDateChange); -%>\n<%= rowCreator.format('Customer Name', eDCPMSISDNSRCustomerName); -%>\n<%= rowCreator.format('APNs', eDCPMSISDNSRApns); -%>\n<%= rowCreator.format('SIM Resources', eDCPMSISDNSimResources); -%>\n<%- renderTemplate(\"ModiniDetails\"); -%>\n<%- renderTemplate(\"CDRandRODSDetails\"); -%>\n<%- renderTemplate(\"IncidentsData\"); -%>\n<%= mergeLink -%>\n<%- renderTemplate(\"ShowRules\"); -%>\n"}, {"name": "FieldTaskFooterModule", "active": true, "title": "Field Task Footer (Module)", "description": "Module to display Field Task Footer", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "\n\n(Please only include your assurance centre contact details.)\nPreferably use Live Chat for technical assistance.\nLive Chat > Assurance > IP DATA > Data & IP Assurance\nAssurance contact phone number [1800 xxx xxx]\nQuote host order ID when connected with consultant."}, {"name": "FlexcabDetailModule", "active": true, "title": "Flexcab Detail (Module)", "description": "Module that extracts details from Flexcab", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nif (s.Flexcab?.flexcabData?.length) {\n    let rowCreator = new RowCreator();\n    let fcTitle = '--Service Details Flexcab--\\n'; \n    let fcHeader = 'service_number serv_conntn_dt serv_termtn_dt invc_argt_id prod_bilg_id bilg_tran_desc                                 bilg_elmt_cd pcms_prod_cd volume\\n';\n    let fcHLine  = '-------------- -------------- -------------- ------------ ------------ --------------                                 ------------ ------------ ------\\n'; \n    let fcData = [];\n\n    for (let i=0; i < s.Flexcab.flexcabData.length; i++) {\n        let fcSN = ''; let fcSD = ''; let fcED = ''; let fcIA = ''; let fcBID = ''; \n        let fcTD = ''; let fcECD = ''; let fcPCD = ''; let fcVol = '';\n        if (s.Flexcab.flexcabData[i].service_number) fcSN  = `${s.Flexcab.flexcabData[i].service_number}`;  \n        if (s.Flexcab.flexcabData[i].serv_conntn_dt) fcSD  = `${s.Flexcab.flexcabData[i].serv_conntn_dt}`;  \n        if (s.Flexcab.flexcabData[i].serv_termtn_dt) fcED  = `${s.Flexcab.flexcabData[i].serv_termtn_dt}`;  \n        if (s.Flexcab.flexcabData[i].invc_argt_id)   fcIA  = `${s.Flexcab.flexcabData[i].invc_argt_id}`;  \n        if (s.Flexcab.flexcabData[i].prod_bilg_id)   fcBID = `${s.Flexcab.flexcabData[i].prod_bilg_id}`;  \n        if (s.Flexcab.flexcabData[i].bilg_tran_desc) fcTD  = `${s.Flexcab.flexcabData[i].bilg_tran_desc}`;\n        if (s.Flexcab.flexcabData[i].bilg_elmt_cd)   fcECD = `${s.Flexcab.flexcabData[i].bilg_elmt_cd}`;\n        if (s.Flexcab.flexcabData[i].pcms_prod_cd)   fcPCD = `${s.Flexcab.flexcabData[i].pcms_prod_cd}`;  \n        if (s.Flexcab.flexcabData[i].volume)         fcVol = `${s.Flexcab.flexcabData[i].volume}`;\n        fcData.push(`${fcSN.padEnd(15)}${fcSD.padEnd(15)}${fcED.padEnd(15)}${fcIA.padEnd(15)}${fcBID.padEnd(13)}${fcTD.padEnd(47)}${fcECD.padEnd(13)}${fcPCD.padEnd(13)}${fcVol}`);\n    }\n-%>\n<%= fcTitle -%>\n<%= fcHLine -%>\n<%= fcHeader -%>\n<%= fcHLine -%>\n<%=fcData.join('\\n') %>\n<%} -%>"}, {"name": "FOHWirelessTestingSuite", "active": true, "title": "FOH Wireless Testing (Text)", "description": "For getting wireless information summary", "allowedSystemsToAppend": ["ServiceCentral"], "suite": [], "templateType": "Service Check", "formatType": "Text", "listCondition": "(data.fnn && ((data.fnn.match(/^04[0-9]{8}$/)) || (data.fnn.match(/^614[0-9]{8}$/)) || (data.fnn.match(/^A614[0-9]{8}$/)) || (data.fnn.match(/^A04[0-9]{8}$/)) || (data.fnn.match(/^50501[0-9]{10}$/)))) ? true : false;", "defaultPriority": 0, "template": "Mobile Number <%= data.fnn %>\n=============\n<%\n\nlet rowCreator = new RowCreator();\nlet banfHEA; \nlet banfARP; let banfAAP; let banfBAS; let banfBTK; let banfCAR; let banfCHR; let banfDNM; let banfDHR; let banfICC; let banfIMS; let banfIPA; let banfLRC; let banfMMS; let banfMSA; let banfMSC; let banfMME;\nlet banfMSI; let banfNRR; let banfQCI; let banfSGW; let banfSTT; let banfSAP; let banfTEC; let banfTAP; let banfVRS; let modiSSN; let modiSST; let banfSts; let modiAAP; let modiNST; \nlet bandAle; let bandFTL; let banfSG1; let banfPG1; let banfPG2; let banfS5G;\n\nlet BandFResponse = (s.<PERSON>ffy && s.<PERSON>luffy.results && s.<PERSON>.results.APIresponse) ? s.<PERSON>ffy.results.APIresponse : null;\n\nif (BandFResponse) {\n    banfHEA = '<PERSON> and <PERSON><PERSON><PERSON>\\n----------------\\n';\n    banfARP = BandFResponse.ARP                       ? BandFResponse.ARP                       : ''\n    banfAAP = BandFResponse.Active_APNs               ? BandFResponse.Active_APNs               : ''\n    banfAle = BandFResponse.Alert                     ? BandFResponse.Alert                     : ''\n    banfBAS = BandFResponse.Base_Station              ? BandFResponse.Base_Station              : ''\n    banfBTK = BandFResponse.BlueTick                  ? BandFResponse.BlueTick                  : ''\n    banfCAR = BandFResponse.Carrier                   ? BandFResponse.Carrier                   : ''\n    banfCHR = BandFResponse.Charging                  ? BandFResponse.Charging                  : ''\n    banfDNM = BandFResponse.Device_Name               ? BandFResponse.Device_Name               : ''\n    banfDHR = BandFResponse.Devil_Headroom            ? BandFResponse.Devil_Headroom            : ''\n    banfICC = BandFResponse.ICCID                     ? BandFResponse.ICCID                     : ''\n    banfIMS = BandFResponse.IMSI                      ? BandFResponse.IMSI                      : ''\n    banfIPA = BandFResponse.IP_Addresses              ? BandFResponse.IP_Addresses              : ''\n    banfLRC = BandFResponse.LRD_Code                  ? BandFResponse.LRD_Code                  : ''\n    banfMMS = BandFResponse.MM_Status                 ? BandFResponse.MM_Status                 : ''\n    banfMME = BandFResponse.MME                       ? BandFResponse.MME                       : ''\n    banfMSA = BandFResponse.MSA                       ? BandFResponse.MSA                       : ''\n    banfMSC = BandFResponse.MSC                       ? BandFResponse.MSC                       : ''\n    banfMSI = BandFResponse.MSISDN                    ? BandFResponse.MSISDN                    : ''\n    banfNRR = BandFResponse.NR                        ? BandFResponse.NR                        : ''\n    banfQCI = BandFResponse.QCI                       ? BandFResponse.QCI                       : ''\n    banfSGW = BandFResponse.Serving_SGWs              ? BandFResponse.Serving_SGWs              : ''\n    banfSTT = BandFResponse.State                     ? BandFResponse.State                     : ''\n    banfSts = BandFResponse.Status                    ? BandFResponse.Status                     : ''\n    banfSG1 = BandFResponse.Serving_SGWs              ? BandFResponse.Serving_SGWs              : ''\n    banfPG1 = BandFResponse.Serving_PGWs              ? BandFResponse.Serving_PGWs[0]           : ''\n    banfPG2 = BandFResponse.Serving_PGWs              ? BandFResponse.Serving_PGWs[1]           : ''\n    banfS5G = BandFResponse.Stripe5G                  ? BandFResponse.Stripe5G                  : ''\n    banfSAP = BandFResponse.Subscribed_APNs           ? BandFResponse.Subscribed_APNs           : ''\n    banfTEC = BandFResponse.Technology                ? BandFResponse.Technology                : ''\n    bandfTL = BandFResponse.TowerLocation             ? BandFResponse.TowerLocation            : ''\n    banfTAP = BandFResponse.Telstra_Approved          ? BandFResponse.Telstra_Approved          : ''\n    banfVRS = BandFResponse.VoLTE_registration_Status ? BandFResponse.VoLTE_registration_Status : ''\n}\nlet mergeLink = '\\nMerge Result Link: https://merge.in.telstra.com.au/serviceCheck/view/html/'  + data.id;\n-%>\n<%= banfHEA -%>\n<%= rowCreator.format('ICCID',banfICC); -%>\n<%= rowCreator.format('IMSI',banfIMS); -%>\n<%= rowCreator.format('MSISDN',banfMSI); -%>\n<%= rowCreator.format('Status',banfSts); -%>\n<%= rowCreator.format('Carrier',banfCAR); -%>\n<%= rowCreator.format('ARP',banfARP); -%>\n<%= rowCreator.format('Active APNs',banfAAP); -%>\n<%= rowCreator.format('Base Station',banfBAS); -%>\n<%= rowCreator.format('LRD Code',banfLRC); -%>\n<%= rowCreator.format('Blue Tick',banfBTK); -%>\n<%= rowCreator.format('Model',banfDNM); -%>\n<%= rowCreator.format('Telstra Approved',banfTAP); -%>\n<%= rowCreator.format('NR',banfNRR); -%>\n<%= rowCreator.format('Tower Location',bandFTL); -%>\n<%= rowCreator.format('Health Rules(Devil Headroom)',banfDHR); -%>\n<%= rowCreator.format('Technology',banfTEC); -%>\n<%= rowCreator.format('Serving SGWs',banfSGW); -%>\n<%= rowCreator.format('MME/AMF',banfMME); -%>\n<%= rowCreator.format('MM Status',banfMMS); -%>\n<%= rowCreator.format('SGW/SG SN',banfSG1); -%>\n<%= rowCreator.format('PGW/GG SN',banfPG1); -%>\n<%= rowCreator.format('PGW/SAPC',banfPG2); -%>\n<%= rowCreator.format('MSC',banfMSC); -%>\n<%= rowCreator.format('MSA',banfMSA); -%>\n<%= rowCreator.format('State',banfSTT); -%>\n<%= rowCreator.format('Subscribed APNs',banfSAP); -%>\n<%= rowCreator.format('Active APNs',banfAAP); -%>\n<%= rowCreator.format('Assigned APNs',modiAAP); -%>\n<%= rowCreator.format('QCI',banfQCI); -%>\n<%= rowCreator.format('ARP',banfARP); -%>\n<%= rowCreator.format('IP Addresses',banfIPA); -%>\n<%= rowCreator.format('Stripe (5G)',banfS5G); -%>\n<%= rowCreator.format('VoLTE Registration Status',banfVRS); -%>\n<%= rowCreator.format('Alert',banfAle); -%>\n<%= rowCreator.format('Charging',banfCHR); -%>\n<%- renderTemplate(\"IncidentsData\"); -%>\n<%= rowCreator.format('SIM Serial',modiSSN); -%>\n<%= rowCreator.format('Service Status',modiSST); -%>\n<%= rowCreator.format('Network Status',modiNST); -%>\n<%- renderTemplate(\"ModiniDetails\"); -%>\n<%- renderTemplate(\"CDRandRODSDetails\"); -%>\n<%= mergeLink -%>\n<%- renderTemplate(\"ShowRules\"); -%>"}, {"name": "GMACSInfoModule", "active": true, "title": "GMACS Info (Module)", "description": "Module to display GMACS diagnostics information for BDSL services", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nlet rowCreator = new RowCreator();\n-%>\n--GMACS--\n<%= rowCreator.format(\"Eth 1/0 Status\", r.MTR017 ? (r.MTR017.result === \"OK\" ? \"Up\" : \"Not up\") : \"Not found\"); -%>\n<%= rowCreator.format(\"Customer interface traffic\", r.MDR016 && typeof r.MDR016.inRun1 === \"number\" && typeof r.MDR016.outRun1 === \"number\" ? `Input packets: ${r.MDR016.inRun1}, Output packets: ${r.MDR016.outRun1}` : \"Not found\"); -%>\n<%= rowCreator.format(\"Loop to NTU\", s.GMACS ? (s.GMACS.OAMPingSuccess === true ? \"Success\" : (s.GMACS.OAMPingSuccess === false && s.GMACS.OAMPingError === null ? \"Failed\" : s.GMACS.OAMPingError)) : \"Unknown\"); -%>\n<% if (showSHDSLCheck) { -%>\n<% if (r.MDR016 && r.MTR020) { -%>\n<%= rowCreator.format(\"SHDSL Check (Telstra side)\", r.MTR020.result === \"OK\" ? \"OK\" : `Failed (Error seconds in last 15min = ${r.MDR016.errorSeconds15min}, UAS Last 15min = ${r.MDR016.UAS15min}, Current SNR is '${r.MDR016.SNR_OK}' and Current Line ATTN is '${r.MDR016.LineATTN}')`); -%>\n<% } else { -%>\n<%= rowCreator.format(\"SHDSL Check (Telstra side)\", \"Not found\"); -%>\n<% } -%>\n<% } -%>"}, {"name": "IncidentsData", "active": true, "title": "Display Incidents Data (Module)", "description": "Module to display Incidents Data", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<% if (s.INCIDENTS && s.INCIDENTS.incidents && (s.INCIDENTS.incidents.itam || s.INCIDENTS.incidents.cmart || s.INCIDENTS.incidents.conen)) {\n    let rowCreator = new RowCreator();\n    let inciTOT; let inciITM; let inciCMA; let inciCON;\n    let itamINC; let cmarINC; let conaINC;\n\n  if (s.INCIDENTS.incidents.itam.length > 0 || s.INCIDENTS.incidents.cmart.length > 0 || s.INCIDENTS.incidents.conen.length > 0)\n      inciTOT = 'There are ' + s.INCIDENTS.incidents.itam.length + ' - ITAM, ' + s.INCIDENTS.incidents.cmart.length + ' - CMART, ' + s.INCIDENTS.incidents.conen.length + ' - CONEN active incident(s).';\n  else\n    inciTOT = 'Not found';\n  if (s.INCIDENTS.incidents.itam) {\n    var icount = Object.keys(s.INCIDENTS.incidents.itam).length;\n    if (icount > 0) itamINC = [];\n    for (i = 0; i < icount; i++) {\n        var ip = '[' + ((i < 10) ? String(i) + '] ' : String(i) + ']');\n        if (s.INCIDENTS.incidents.itam[i].incident_number)          itamINC.push(`${ip} ITAM Ticket          - ${s.INCIDENTS.incidents.itam[i].incident_number}`);\n        if (s.INCIDENTS.incidents.itam[i].detailed_description)      itamINC.push(`     Detailed Description - ${s.INCIDENTS.incidents.itam[i].detailed_description}`);\n        if (s.INCIDENTS.incidents.itam[i].product_name)              itamINC.push(`     Product Name         - ${s.INCIDENTS.incidents.itam[i].product_name}`);\n    }\n  }\n  if (s.INCIDENTS.incidents.cmart) {\n    var icount = Object.keys(s.INCIDENTS.incidents.cmart).length;\n    if (icount > 0) cmarINC = [];\n    for (i = 0; i < icount; i++) {\n        var ip = '[' + ((i < 10) ? String(i) + '] ' : String(i) + ']');\n        if (s.INCIDENTS.incidents.cmart[i].calm_id)                 cmarINC.push(`${ip} CMART Ticket         - ${s.INCIDENTS.incidents.cmart[i]['calm_id']}`);\n        if (s.INCIDENTS.incidents.cmart[i].detailed_description)     cmarINC.push(`     Detailed Description - ${s.INCIDENTS.incidents.cmart[i]['detailed_description']}`);\n        if (s.INCIDENTS.incidents.cmart[i].product_name)             cmarINC.push(`     Product Name         - ${s.INCIDENTS.incidents.cmart[i]['product_name']}`);\n    }\n  }\n  if (s.INCIDENTS.incidents.conen) {\n    var icount = Object.keys(s.INCIDENTS.incidents.conen).length;\n    if (icount > 0) conaINC = [];\n    for (i = 0; i < icount; i++) {\n        var ip = '[' + ((i < 10) ? String(i) + '] ' : String(i) + ']');\n        if (s.INCIDENTS.incidents.conen[i]['REC_ID'])               conaINC.push(`${ip} Conen Ticket         - ${s.INCIDENTS.incidents.conen[i]['REC_ID']}`);\n        if (s.INCIDENTS.incidents.conen[i]['detailed_description'])  conaINC.push(`     Detailed Description - ${s.INCIDENTS.incidents.conen[i]['detailed_description']}`);\n        if (s.INCIDENTS.incidents.conen[i]['product_name'])          conaINC.push(`     Product Name         - ${s.INCIDENTS.incidents.conen[i]['product_name']}`);\n    }\n  }\n-%>\n<%= rowCreator.format('Live Incidents',inciTOT); -%>\n<%= rowCreator.format('iTAM Incidents',inciITM); -%>\n<%= rowCreator.format('CMART Incidents',inciCMA); -%>\n<%= rowCreator.format('Conan Incidents',inciCON); -%>\n<%= rowCreator.format('iTAM Details',itamINC, false); -%>\n<%= rowCreator.format('CMART Details',cmarINC, false); -%>\n<%= rowCreator.format('Conan Details',conaINC, false); -%>\n<%} -%>"}, {"name": "IPMANSummaryText", "active": false, "title": "IPMAN Summary (Text)", "description": "IPMAN Summary default template", "allowedSystemsToAppend": [], "suite": [], "templateType": "Service Check", "formatType": "Text", "listCondition": "let ipmanCarriageType = false;\nif (data.carriageType === 'IPMAN') {\n    ipmanCarriageType = true;\n}\nif (Array.isArray(data.generatedServiceChecks)) {\n    for (let record of data.generatedServiceChecks) {\n        if (record?.carriageType === 'IPMAN') {\n            ipmanCarriageType = true;\n        }\n    }\n}\nipmanCarriageType;", "defaultPriority": 2, "template": "<%\nlet ipmanCarriageType = false;\nif (data.carriageType === 'IPMAN') {\n    ipmanCarriageType = true;\n}\nif (Array.isArray(data.generatedServiceChecks)) {\n    for (let record of data.generatedServiceChecks) {\n        if (record?.carriageType === 'IPMAN') {\n            ipmanCarriageType = true;\n        }\n    }\n}\nif (ipmanCarriageType) { -%>\nIPMAN Summary\n\n<%- renderTemplate(\"CustomerDetailsModule\", { RASSPSourceName: \"RASSP\" }); %>\n<%- renderTemplate(\"DeviceDetailsModule\", { forMDN: false, RASSPSourceName: \"RASSP\" }); %>\n<%- renderTemplate(\"TFibreAdaptiveNetworkSummaryModule\"); %>\n<%- renderTemplate(\"TRADDemarcPointModule\"); %>\n<%- renderTemplate(\"SHAREDBOSSNTUDeviceModule\"); %>\n<%- renderTemplate(\"TRADNextUpstreamDeviceModule\"); %>\n<%- renderTemplate(\"RassOrdersModule\", { showAllOrders: true, includeCancelledOrders: true }); %>\n<%- renderTemplate(\"TicketingCasesModule\"); %>\n<%- renderTemplate(\"OutagesModule\"); %>\n<% if (r.MTR074?.alarmCount > 0) { -%>\n\nServiceTX Alarms are present, please check rule MTR074 in the summary tab for more details.\n\n<% } -%>\n<%- renderTemplate(\"PingResultsModule\", { includeCMIPing: false, includeVPNPing: true, includeVPNPingFullMTU: false }); %>\n<%- renderTemplate(\"AsicStatsPacketLossModule\"); %>\n<% if (r.MDR009 && r.MDR009.manageCategory === 'MDN') { -%>\n\n<%- renderTemplate(\"MDNInfoModule\", { showCarriageSectionHeader: false }); %>\n<%- renderTemplate(\"DeviceDetailsModule\", { forMDN: true, RASSPSourceName: \"RASSP\" }); %>\n<%- renderTemplate(\"DeviceStatusModule\"); %>\n<%- renderTemplate(\"AccessStatusModule\"); %>\n<%- renderTemplate(\"AccessInterfaceThroughputModule\", { interfaceMetrics: [\"RXBS\", \"TXBS\"] }); %>\n<% } -%>\n<%- renderTemplate(\"OutcomesModule\"); %>\n<%- renderTemplate(\"MessageBucketModule\"); %>\nMerge Result Link: <%= mergeUrl %>/serviceCheck/view/html/<%= data.id %>\n<% } else { -%>\nRecord is not for a IPMAN service\n<% } %>"}, {"name": "IPVoiceCallForwarding", "active": true, "title": "Display IPVoice Call Forwarding (Module)", "description": "Module to display IPVoice Call Forwarding", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nif (s.COMMPilot && s.COMMPilot.callForwarding) {\n    let rowCreator = new RowCreator();\n    let cfHeader; let cfaBody; let cfbBody; let cfnaBody; let cfuBody; let cfsBody; let cfsRules;\n\n    if ((s.COMMPilot.callForwarding.callForwardAlways && s.COMMPilot.callForwarding.callForwardAlways.feature && s.COMMPilot.callForwarding.callForwardAlways.feature == 'enabled')                ||\n        (s.COMMPilot.callForwarding.callForwardBusy && s.COMMPilot.callForwarding.callForwardBusy.feature && s.COMMPilot.callForwarding.callForwardBusy.feature == 'enabled')                      ||\n        (s.COMMPilot.callForwarding.callForwardUnavailable && s.COMMPilot.callForwarding.callForwardUnavailable.feature && s.COMMPilot.callForwarding.callForwardUnavailable.feature == 'enabled') ||\n        (s.COMMPilot.callForwarding.callForwardNoAnswer && s.COMMPilot.callForwarding.callForwardNoAnswer.feature && s.COMMPilot.callForwarding.callForwardNoAnswer.feature == 'enabled')          ||\n        (s.COMMPilot.callForwarding.callForwardSelective && s.COMMPilot.callForwarding.callForwardSelective.feature && s.COMMPilot.callForwarding.callForwardSelective.feature == 'enabled')) {\n        cfHeader = '\\nCall Forwarding\\n---------------\\n';\n    }\n    if ( s.COMMPilot.callForwarding.callForwardAlways && s.COMMPilot.callForwarding.callForwardAlways.feature && s.COMMPilot.callForwarding.callForwardAlways.feature == 'enabled' &&\n         s.COMMPilot.callForwarding.callForwardAlways.active &&(s.COMMPilot.callForwarding.callForwardAlways.active == 'true' || s.COMMPilot.callForwarding.callForwardAlways.active == 'enabled')) {\n        cfaBody = 'ON ';\n        if (s.COMMPilot.callForwarding.callForwardAlways.destination) cfaBody += s.COMMPilot.callForwarding.callForwardAlways.destination;\n    } else if (s.COMMPilot.callForwarding.callForwardAlways && s.COMMPilot.callForwarding.callForwardAlways.feature && s.COMMPilot.callForwarding.callForwardAlways.feature == 'enabled') {\n        cfaBody = 'OFF';\n    }\n    if ( s.COMMPilot.callForwarding.callForwardBusy && s.COMMPilot.callForwarding.callForwardBusy.feature && s.COMMPilot.callForwarding.callForwardBusy.feature == 'enabled' &&\n        s.COMMPilot.callForwarding.callForwardBusy.active && (s.COMMPilot.callForwarding.callForwardBusy.active == 'true' || s.COMMPilot.callForwarding.callForwardBusy.active == 'enabled')) {\n        cfbBody = 'ON ';\n        if (s.COMMPilot.callForwarding.callForwardBusy.destination) cfbBody += s.COMMPilot.callForwarding.callForwardBusy.destination;\n    } else if (s.COMMPilot.callForwarding.callForwardBusy.feature && s.COMMPilot.callForwarding.callForwardBusy.feature == 'enabled') {\n        cfbBody = 'OFF';\n    }\n    if ( s.COMMPilot.callForwarding.callForwardNoAnswer && s.COMMPilot.callForwarding.callForwardNoAnswer.feature && s.COMMPilot.callForwarding.callForwardNoAnswer.feature == 'enabled' &&\n        s.COMMPilot.callForwarding.callForwardNoAnswer.active && (s.COMMPilot.callForwarding.callForwardNoAnswer.active == 'true' || s.COMMPilot.callForwarding.callForwardNoAnswer.active == 'enabled')) {\n        cfnaBody = 'ON ';\n       if (s.COMMPilot.callForwarding.callForwardNoAnswer.destination) cfnaBody += s.COMMPilot.callForwarding.callForwardNoAnswer.destination;\n    } else if (s.COMMPilot.callForwarding.callForwardNoAnswer.feature && s.COMMPilot.callForwarding.callForwardNoAnswer.feature == 'enabled') {\n        cfnaBody = 'OFF';\n    }\n    if ( s.COMMPilot.callForwarding.callForwardUnavailable && s.COMMPilot.callForwarding.callForwardUnavailable.feature &&\n         s.COMMPilot.callForwarding.callForwardUnavailable.feature == 'enabled' && s.COMMPilot.callForwarding.callForwardUnavailable.active &&\n        (s.COMMPilot.callForwarding.callForwardUnavailable.active == 'true' || s.COMMPilot.callForwarding.callForwardUnavailable.active == 'enabled')) {\n        cfuBody = 'ON ';\n        if (s.COMMPilot.callForwarding.callForwardUnavailable.destination) cfuBody += s.COMMPilot.callForwarding.callForwardUnavailable.destination;\n    } else if (s.COMMPilot.callForwarding.callForwardUnavailable.feature && s.COMMPilot.callForwarding.callForwardUnavailable.feature == 'enabled') {\n        cfuBody = 'OFF';\n    }\n    if ( s.COMMPilot.callForwarding.callForwardSelective && s.COMMPilot.callForwarding.callForwardSelective.feature &&\n         s.COMMPilot.callForwarding.callForwardSelective.feature == 'enabled'  && s.COMMPilot.callForwarding.callForwardSelective.active   &&\n        (s.COMMPilot.callForwarding.callForwardSelective.active == 'true' || s.COMMPilot.callForwarding.callForwardSelective.active == 'enabled')) {\n        cfsBody = 'ON';\n        if (s.COMMPilot.callForwarding.callForwardSelective.defaultDestination) cfsBody += s.COMMPilot.callForwarding.callForwardSelective.defaultDestination;\n    } else if (s.COMMPilot.callForwarding.callForwardSelective.feature  && s.COMMPilot.callForwarding.callForwardSelective.feature == 'enabled')   {\n        cfsBody = 'OFF';\n    }\n    if (s.COMMPilot.callForwarding.callForwardSelective && s.COMMPilot.callForwarding.callForwardSelective.callForwardSelectiveCriteria &&\n        s.COMMPilot.callForwarding.callForwardSelective.callForwardSelectiveCriteria.rules) {\n        var  ro = Object.keys(s.COMMPilot.callForwarding.callForwardSelective.callForwardSelectiveCriteria.rules).length;\n        if (ro > 0) cfsRules = [];\n        for (i = 0; i < ro ; i++) {\n            if (s.COMMPilot.callForwarding.callForwardSelective.callForwardSelectiveCriteria.rules[i] && s.COMMPilot.callForwarding.callForwardSelective.callForwardSelectiveCriteria.rules[i].name && s.COMMPilot.callForwarding.callForwardSelective.callForwardSelectiveCriteria.rules[i].isActive && s.COMMPilot.callForwarding.callForwardSelective.callForwardSelectiveCriteria.rules[i].isActive == 'true') {\n                var ip = '[' + ((i < 10) ? String(i) + '] ' : String(i) + ']');\n                var ru = ''; var ia = ''; var cz = ''; var dz = ''; var ez = '';\n                if (s.COMMPilot.callForwarding.callForwardSelective.callForwardSelectiveCriteria.rules[i].name) ru += s.COMMPilot.callForwarding.callForwardSelective.callForwardSelectiveCriteria.rules[i].name;\n                if (s.COMMPilot.callForwarding.callForwardSelective.callForwardSelectiveCriteria.rules[i].isActive == 'true') ia += ' isActive: Yes';\n                if (s.COMMPilot.callForwarding.callForwardSelective.callForwardSelectiveCriteria.rules[i].timeSchedule) cz += ' TimeSchedule: ' + s.COMMPilot.callForwarding.callForwardSelective.callForwardSelectiveCriteria.rules[i].timeSchedule;\n                if (s.COMMPilot.callForwarding.callForwardSelective.callForwardSelectiveCriteria.rules[i].applyToCalls) dz += ' ApplyToCalls: ' + s.COMMPilot.callForwarding.callForwardSelective.callForwardSelectiveCriteria.rules[i].applyToCalls;\n                if (s.COMMPilot.callForwarding.callForwardSelective.callForwardSelectiveCriteria.rules[i].destination) ez += ' Destinations: ' + s.COMMPilot.callForwarding.callForwardSelective.callForwardSelectiveCriteria.rules[i].destination;\n                cfsRules.push(`${ip} ${ru}${ia}${cz}${dz}${ez}`);\n            }\n        }\n    }\n-%>\n<%= cfHeader -%>\n<%= rowCreator.format('CFA', cfaBody); -%>\n<%= rowCreator.format('CFB', cfbBody); -%>\n<%= rowCreator.format('CFNA',cfnaBody); -%>\n<%= rowCreator.format('CFU', cfuBody); -%>\n<%= rowCreator.format('CFS', cfsBody); -%>\n<%= rowCreator.format('Rules', cfsRules, false); -%>\n<%} -%>"}, {"name": "IPVoiceCOMMPilotModule", "active": true, "title": "IP Voice COMMPilot Module", "description": "Module that extracts summary information from COMMPilot", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<% \n    \nif (s.COMMPilot) {    \n    let rowCreator = new RowCreator();\n\n    let commpilotProduct; let commpilotFNN; let commpilotServiceStatus;\n    let commpilotPath; let commpilotGroupIntercept; let commpilotGroupCallerID;\n    let commpilotUserIntercept; let commpilotConfiguredDevice;\n    let commpilotConfiguredDeviceLevel; let commpilotUser; \n    let commpilotIPWorksDomain; let commpilotEnumDomain; let commpilotDeviceDetails;\n    \n    const COMMPILOT_FIELD_NOT_FOUND_MSG = \"Not found. Please check COMMPilot data in service check summary tab\";\n    \n    if (s.COMMPilot.user && s.COMMPilot.user.clusterType) {\n        commpilotProduct = s.COMMPilot.user.clusterType.toUpperCase().replace('STOA', 'BSIP');\n        if (s.CFNN && s.CFNN.FirstLine && s.CFNN.FirstLine.toUpperCase().includes('TOTI')) commpilotProduct += ' (TOTI)';\n    }\n    if (s.COMMPilot.user && s.COMMPilot.user.userId) {\n        var f1 = s.COMMPilot.user.userId;\n        var f2 = f1.indexOf('@');\n        var f3 = f1.substring(0,f2);\n        commpilotFNN = f3;\n    } else if (s.CFNN && s.CFNN.number) {\n        commpilotFNN = s.CFNN.number;\n    }\n    if (s.CFNN && s.CFNN.Details && s.CFNN.Details.toUpperCase().includes('ENTERPRISE TRUNK PILOT')) {\n        commpilotServiceStatus = 'Enterp.Trunk';\n    } else {\n        if      (s.COMMPilot.user && s.COMMPilot.user.serviceActivated && s.COMMPilot.user.serviceActivated == 'true') commpilotServiceStatus = 'Activated';\n        else if (s.CFNN && s.CFNN.FirstLine && s.CFNN.FirstLine.toUpperCase().includes('NOT ACTIVATED'))               commpilotServiceStatus = 'Inactivated';\n        else if (s.CFNN && s.CFNN.FirstLine && s.CFNN.FirstLine.toUpperCase().includes('ACTIVATED'))                   commpilotServiceStatus = 'Activated';\n        else                                                                                                           commpilotServiceStatus = 'Inactivated';\n    }\n    // 0747206000 CP.user.userid NULL -> Unassigned\n    // 0296947320,0884334252,0383478333 (AA,CC,HG) -> Virtual\n    // 0884334151 CP.reg.regs.devicename = 'Mobile Intergration' -> Liberate\n    if (s.COMMPilot.user && typeof s.COMMPilot.user.userId !== 'undefined' && s.COMMPilot.user.userId === '') {\n        commpilotServiceStatus += ' Unassigned';\n    } else if (s.COMMPilot.user && s.COMMPilot.user.userType && (s.COMMPilot.user.userType == 'Auto Attendant' || s.COMMPilot.user.userType == 'Call Center' || s.COMMPilot.user.userType == 'Hunt Group')) {\n        commpilotServiceStatus += ' Virtual';\n    } else if (s.COMMPilot.registration && s.COMMPilot.registration.registrations && s.COMMPilot.registration.registrations.deviceName && s.COMMPilot.registration.registrations.deviceName.toUpperCase().includes('MOBILE INTEGRATION')) {\n        commpilotServiceStatus += ' Liberate';\n    } else if (s.COMMPilot.configuredDevice && s.COMMPilot.configuredDevice.trunk && s.COMMPilot.configuredDevice.trunk.enterpriseTrunk && s.COMMPilot.user && s.COMMPilot.user.groupId && s.COMMPilot.user.groupId.toUpperCase().includes('DID')) {\n        commpilotServiceStatus += ' Enterprise Trunk DID';\n    } else if (s.COMMPilot.registration && s.COMMPilot.registration.status) {\n        commpilotServiceStatus += ' ' + (s.COMMPilot.registration.status.charAt(0).toUpperCase() + s.COMMPilot.registration.status.slice(1));\n    } else {\n        var sp = '';\n        if (s.COMMPilot.user && s.COMMPilot.user.serviceProviderId) sp += s.COMMPilot.user.serviceProviderId; else sp += 'XYZXYZXYZ';\n        if (s.CFNN && s.CFNN.registrations && s.CFNN.registrations[0] && s.CFNN.registrations[0]['Trunk FNN'] && s.CFNN.registrations[0]['Trunk FNN'].toUpperCase().includes('NO REGISTERED')) {\n            commpilotServiceStatus += ' Unregistered';\n        } else {\n            if (s.CFNN && s.CFNN.Cluster && s.CFNN.Cluster.includes(sp)) commpilotServiceStatus += ' Registered'; else commpilotServiceStatus += ' Unregistered';\n        }\n    }\n    if (s.COMMPilot.user) {\n        commpilotPath = '';\n        if (s.COMMPilot.user.cluster)                                                                                commpilotPath += 'Cluster ' + s.COMMPilot.user.cluster;\n        if (s.COMMPilot.user.serviceProviderId)                                                                      commpilotPath += '  -  '    + s.COMMPilot.user.serviceProviderId;\n        if (s.COMMPilot.user.groupId) {                                                                              commpilotPath += '  -  '    + s.COMMPilot.user.groupId; }\n        if (s.COMMPilot.groupTrunkGroups && s.COMMPilot.groupTrunkGroups[0] && s.COMMPilot.groupTrunkGroups[0].name) commpilotPath += '  -  '    + s.COMMPilot.groupTrunkGroups[0].name;\n    }\n    if (s.COMMPilot.groupIntercept || (s.COMMPilot.user && s.COMMPilot.user.groupCallerId)) {\n        if (s.COMMPilot.groupIntercept && s.COMMPilot.groupIntercept.active && s.COMMPilot.groupIntercept.active == 'true') commpilotGroupIntercept = 'ON'; else commpilotGroupIntercept = 'OFF';\n        if (s.COMMPilot.user && s.COMMPilot.user.groupCallerId && s.COMMPilot.user.groupCallerId == 'true') commpilotGroupCallerID = 'ON'; else commpilotGroupCallerID = 'OFF';\n    }\n    if (s.COMMPilot.userIntercept && s.COMMPilot.userIntercept.active && s.COMMPilot.userIntercept.active == 'true') commpilotUserIntercept = 'ON'; else commpilotUserIntercept = 'OFF';\n    if (s.COMMPilot.configuredDevice && s.COMMPilot.configuredDevice.feature) {\n        if (s.COMMPilot.configuredDevice.feature == 'enabled') commpilotConfiguredDevice = 'ON'; else commpilotConfiguredDevice = 'OFF';\n        if (s.COMMPilot.configuredDevice.feature == 'enabled' && s.COMMPilot.configuredDevice.deviceLevel) commpilotConfiguredDeviceLevel = s.COMMPilot.configuredDevice.deviceLevel;\n    }\n    if (s.COMMPilot.user && (s.COMMPilot.user.userType || s.COMMPilot.user.userId)) {\n        commpilotUser = '';\n        if (s.COMMPilot.user.userType)      commpilotUser += s.COMMPilot.user.userType;\n        if (s.COMMPilot.user.userId)        commpilotUser += (s.COMMPilot.user.userType ? ' - ' : '') + s.COMMPilot.user.userId;\n    }\n    \n    if (s.IPWorks && s.IPWorks.customerdomain && s.IPWorks.customerdomain !== '') {\n        commpilotIPWorksDomain = s.IPWorks.customerdomain;\n        if (s.IPWorks.customertype) commpilotIPWorksDomain +=  ' ' + s.IPWorks.customertype.toUpperCase();\n    }\n    if (s.COMMPilot.enum) {\n        if (s.COMMPilot.enum.domain && (typeof s.COMMPilot.enum.domain == 'undefined')) commpilotEnumDomain = 'no result ';\n        if (s.COMMPilot.enum.domain === '')                                             commpilotEnumDomain = 'no result ';\n        if (s.COMMPilot.enum.domain && s.COMMPilot.enum.domain !== '')                  commpilotEnumDomain = s.COMMPilot.enum.domain + ' ';\n        if (s.COMMPilot.enum.type)                                                      commpilotEnumDomain += s.COMMPilot.enum.type.toUpperCase();\n    }\n    \n    if (s.COMMPilot.deviceInfo) {\n        let deHeader = 'device_name       device_type                 version\\n';\n        let deHLine  = '---------------   -------------------------   ----------------------\\n'; \n        commpilotDeviceDetails = '\\n' + deHeader + deHLine;\n         \n        let deName = ''; let deType = ''; let deVersion = '';\n        if (s.COMMPilot.deviceInfo.deviceName)  deName  = `${s.COMMPilot.deviceInfo.deviceName}`;  \n        if (s.COMMPilot.deviceInfo.deviceType)  deType  = `${s.COMMPilot.deviceInfo.deviceType}`;  \n        if (s.COMMPilot.deviceInfo.version)  deVersion  = `${s.COMMPilot.deviceInfo.version}`;  \n        commpilotDeviceDetails += `${deName.padEnd(18)}${deType.padEnd(28)}${deVersion}\\n`;\n    }\n    \n    if (s.COMMPilot.deviceList) {\n        let deHeader = 'device_name       device_type                 version\\n';\n        let deHLine  = '---------------   -------------------------   ----------------------\\n'; \n        commpilotDeviceDetails = '\\n' + deHeader + deHLine;\n         \n        let deName = ''; let deType = ''; let deVersion = '';\n        for (let i=0; i < s.COMMPilot.deviceList.length; i++) {\n            if (s.COMMPilot.deviceList[i].deviceName)  deName  = `${s.COMMPilot.deviceList[i].deviceName}`;  \n            if (s.COMMPilot.deviceList[i].deviceType)  deType  = `${s.COMMPilot.deviceList[i].deviceType}`;  \n            if (s.COMMPilot.deviceList[i].version)  deVersion  = `${s.COMMPilot.deviceList[i].version}`;  \n            commpilotDeviceDetails += `${deName.padEnd(18)}${deType.padEnd(28)}${deVersion}\\n`;\n        }\n    }\n    \n-%>\n--System Details COMMPilot--\n\nThe CommPilot search is completed using an active phone number from within the \nentered N-R's number range. The results are user based rather than Group or \nEnterprise based. \n\n<%= rowCreator.format('Product', commpilotProduct ? commpilotProduct : COMMPILOT_FIELD_NOT_FOUND_MSG, false); -%>\n<%= rowCreator.format('Full National Number', commpilotFNN ? commpilotFNN : COMMPILOT_FIELD_NOT_FOUND_MSG, false); -%>\n<%= rowCreator.format('CommPilot Path', commpilotPath ? commpilotPath : COMMPILOT_FIELD_NOT_FOUND_MSG, false); -%>\n<%= rowCreator.format('Configured Device', commpilotConfiguredDevice ? commpilotConfiguredDevice : COMMPILOT_FIELD_NOT_FOUND_MSG, false); -%>\n<%= rowCreator.format('Configured Device Level', commpilotConfiguredDeviceLevel ? commpilotConfiguredDeviceLevel : COMMPILOT_FIELD_NOT_FOUND_MSG, false); -%>\n<%= rowCreator.format('User', commpilotUser ? commpilotUser : COMMPILOT_FIELD_NOT_FOUND_MSG, false); -%>\n<%= rowCreator.format('Service Status', commpilotServiceStatus ? commpilotServiceStatus : COMMPILOT_FIELD_NOT_FOUND_MSG, false); -%>\n<%= rowCreator.format('Group Intercept', commpilotGroupIntercept ? commpilotGroupIntercept : COMMPILOT_FIELD_NOT_FOUND_MSG, false); -%>\n<%= rowCreator.format('Group Caller ID', commpilotGroupCallerID ? commpilotGroupCallerID : COMMPILOT_FIELD_NOT_FOUND_MSG, false); -%>\n<%= rowCreator.format('User Intercept', commpilotUserIntercept ? commpilotUserIntercept : COMMPILOT_FIELD_NOT_FOUND_MSG, false); -%>\n<%= rowCreator.format('IPWorks Domain', commpilotIPWorksDomain ? commpilotIPWorksDomain : COMMPILOT_FIELD_NOT_FOUND_MSG, false); -%>\n<%= rowCreator.format('ENUM Domain', commpilotEnumDomain ? commpilotEnumDomain : COMMPILOT_FIELD_NOT_FOUND_MSG, false); -%>\n<%= rowCreator.format('\\nDevice details', commpilotDeviceDetails ? commpilotDeviceDetails : COMMPILOT_FIELD_NOT_FOUND_MSG, false); -%>\n<% } else { -%>\nCOMMPILOT DATA NOT FOUND FOR <%= data.fnn -%>\n\n<% } -%>"}, {"name": "IPVoiceDeliverySummaryText", "active": true, "title": "IPVoice Delivery Summary (Text)", "description": "For getting IPVoice Delivery Summary Text", "allowedSystemsToAppend": ["ServiceCentral"], "suite": [], "templateType": "Service Check", "formatType": "Text", "listCondition": "s.COMMPilot?.user ? true : false;", "defaultPriority": 0, "template": "<% let rowCreator = new RowCreator(); -%>\n<%- renderTemplate(\"RASSServiceDetailModule\"); -%>\n\n<%- renderTemplate(\"RASSServiceConfigModule\"); -%>\n\n<%- renderTemplate(\"FlexcabDetailModule\"); -%>\n\n<%- renderTemplate(\"IPVoiceCOMMPilotModule\"); -%>\n\nMerge Result Link: <%= mergeUrl %>/serviceCheck/view/html/<%= data.id %>\n<%- renderTemplate(\"ShowRules\"); -%>"}, {"name": "IPVoiceDeviceCustomTags", "active": true, "title": "Display IPVoice Device Custom Tags (Module)", "description": "Module to display IPVoice Device Custom Tags", "allowedSystemsToAppend": ["ServiceCentral"], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nif (s.COMMPilot && s.COMMPilot.device) {\n    let rowCreator = new RowCreator();\n    let dctHeader; let dctBody;\n\n    if (s.COMMPilot.device.deviceCustomTags) var dct = Object.keys(s.COMMPilot.device.deviceCustomTags).length;\n    if (dct > 0) dctHeader = '\\nDevice Custom Tags\\n------------------\\n';\n    if (dct > 0) dctBody = [];\n    for (i = 0; i < dct ; i++) {\n        if (s.COMMPilot.device.deviceCustomTags[i]) {\n            if (s.COMMPilot.device.deviceCustomTags[i].name && s.COMMPilot.device.deviceCustomTags[i].value) {\n                var ip = '[' + ((i < 10) ? String(i) + '] ' : String(i) + ']');\n                var na = s.COMMPilot.device.deviceCustomTags[i].name;\n                var va = s.COMMPilot.device.deviceCustomTags[i].value;\n                dctBody.push(`${ip} ${na}, ${va}`);\n            }\n        }\n    }\n-%>\n<%= dctHeader -%>\n<%= rowCreator.format('Name, Value',dctBody, false) -%>\n<%} -%>"}, {"name": "IPVoiceEnterpiseTrunk", "active": true, "title": "Display IPVoice Enterpise Trunk (Module)", "description": "Module to display IPVoice Enterpise Trunk", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nif (s.COMMPilot && s.COMMPilot.configuredDevice && s.COMMPilot.configuredDevice.trunk) {\n    let rowCreator = new RowCreator();\n    let etrunkName = ''; let etrunkGId; let etrunkTGN; let etruntpUserId; let etrunkPUCNP; let etrunkDLevel;\n    let etrunkDName; let etrunkTAICalls; let etrunkTAOCalls; let etrunkMACalls; let etrunkTACalls; let etrunkSCOM; let etrunkCSFSCP;\n\n    let etrunkHeader = '\\nEnterprise Trunk\\n----------------\\n';\n    if (s.COMMPilot.configuredDevice.trunk.enterpriseTrunkName) etrunkName += s.COMMPilot.configuredDevice.trunk.enterpriseTrunkName;\n    if (s.COMMPilot.configuredDevice.trunk.enterpriseTrunk) {\n        if (s.COMMPilot.configuredDevice.trunk.enterpriseTrunk == 'true') etrunkName += ' AVAILABLE'; else etrunkName += ' UNAVAILABLE';\n    }\n    if (s.COMMPilot.configuredDevice.trunk.groupId)                          etrunkGId      = s.COMMPilot.configuredDevice.trunk.groupId;\n    if (s.COMMPilot.configuredDevice.trunk.trunkGroupName)                   etrunkTGN      = s.COMMPilot.configuredDevice.trunk.trunkGroupName;\n    if (s.COMMPilot.configuredDevice.trunk.pilotUserId)                      etruntpUserId  = s.COMMPilot.configuredDevice.trunk.pilotUserId;\n    if (s.COMMPilot.configuredDevice.trunk.pilotUserChargeNumberPolicy)      etrunkPUCNP    = s.COMMPilot.configuredDevice.trunk.pilotUserChargeNumberPolicy;\n    if (s.COMMPilot.configuredDevice.trunk.deviceLevel)                      etrunkDLevel   = s.COMMPilot.configuredDevice.trunk.deviceLevel;\n    if (s.COMMPilot.configuredDevice.trunk.deviceName)                       etrunkDName    = s.COMMPilot.configuredDevice.trunk.deviceName;\n    if (s.COMMPilot.configuredDevice.trunk.totalActiveIncomingCalls)         etrunkTAICalls = s.COMMPilot.configuredDevice.trunk.totalActiveIncomingCalls;\n    if (s.COMMPilot.configuredDevice.trunk.totalActiveOutgoingCalls)         etrunkTAOCalls = s.COMMPilot.configuredDevice.trunk.totalActiveOutgoingCalls;\n    if (s.COMMPilot.configuredDevice.trunk.maxActiveCalls)                   etrunkMACalls  = s.COMMPilot.configuredDevice.trunk.maxActiveCalls;\n    if (s.COMMPilot.configuredDevice.trunk.maxActiveCalls && s.COMMPilot.configuredDevice.trunk.totalActiveOutgoingCalls)\n        etrunkTACalls = (parseInt(s.COMMPilot.configuredDevice.trunk.maxActiveCalls) + parseInt(s.COMMPilot.configuredDevice.trunk.totalActiveOutgoingCalls));\n    if (s.COMMPilot.configuredDevice.trunk.sendContinuousOptionsMessage)     etrunkSCOM     = s.COMMPilot.configuredDevice.trunk.sendContinuousOptionsMessage;\n    if (s.COMMPilot.configuredDevice.trunk.clidSourceForScreenedCallsPolicy) etrunkCSFSCP   = s.COMMPilot.configuredDevice.trunk.clidSourceForScreenedCallsPolicy;\n-%>\n<%= etrunkHeader -%>\n<%= rowCreator.format('TrunkName',etrunkName); -%>\n<%= rowCreator.format('GroupId',etrunkGId); -%>\n<%= rowCreator.format('GroupName',etrunkTGN); -%>\n<%= rowCreator.format('UserId',etruntpUserId); -%>\n<%= rowCreator.format('PilotUserChargeNumberPolicy',etrunkPUCNP); -%>\n<%= rowCreator.format('DeviceLevel',etrunkDLevel); -%>\n<%= rowCreator.format('DeviceName',etrunkDName); -%>\n<%= rowCreator.format('TotalActiveIncomingCalls',etrunkTAICalls); -%>\n<%= rowCreator.format('TotalActiveOutingCalls',etrunkTAOCalls); -%>\n<%= rowCreator.format('MaxActiveCalls',etrunkMACalls ); -%>\n<%= rowCreator.format('TotalActiveCalls',etrunkTACalls); -%>\n<%= rowCreator.format('ContinuousOptionsMessage',etrunkSCOM); -%>\n<%= rowCreator.format('SourceForScreened',etrunkCSFSCP); -%>\n<%} -%>"}, {"name": "IPVoiceFeaturePacks", "active": true, "title": "Display IPVoice Feature Packs (Module)", "description": "Module to display IPVoice Feature Packs", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nif (s.BSIPCSFP) {\n    let rowCreator = new RowCreator();\n    let featuresHeader; let featurePacks;\n\n    var spa = Object.keys(s.BSIPCSFP).length;\n    if (spa > 0) {\n        featuresHeader = '\\nFeature Packs (BSIP Portal)\\n---------------------------\\n';\n        featurePacks = [];\n        for (i = 0; i < spa ; i++) {\n            if (s.BSIPCSFP[i]) {\n                if (s.BSIPCSFP[i].featurePackName) {\n                    var ip = '[' + ((i < 10) ? String(i) + '] ' : String(i) + ']');\n                    var qa = ''; var qp = '';\n                    if (typeof s.BSIPCSFP[i].allocatedQuantity !== 'undefined') qa += 'QtyAssigned   ' + s.BSIPCSFP[i].allocatedQuantity;\n                    if (typeof s.BSIPCSFP[i].purchasedQuantity !== 'undefined') qp += 'QtyPurchased  ' + s.BSIPCSFP[i].purchasedQuantity;\n                    featurePacks.push(`${ip} ${qa} ${qp} ${s.BSIPCSFP[i].featurePackName}`);\n                }\n            }\n        }\n    }\n-%>\n<%= featuresHeader -%>\n<%= rowCreator.format('FeaturePack',featurePacks, false); -%>\n<%} -%>"}, {"name": "IPVoiceGroupCustomTags", "active": true, "title": "Display IPVoice Group Custom Tags (Module)", "description": "Module to display IPVoice Group Custom Tags", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nif (s.COMMPilot && s.COMMPilot.groupLevel) {\n    let rowCreator = new RowCreator();\n    //let featuresHeader; let featurePacks;\n\n    if (s.COMMPilot.groupLevel.customTags) var ct = Object.keys(s.COMMPilot.groupLevel.customTags).length;\n    if (ct > 0) ctHeader = '\\nGroup Custom Tags\\n-----------------\\n';\n    if (ct > 0) ctBody = [];\n    for (i = 0; i < ct ; i++) {\n        if (s.COMMPilot.groupLevel.customTags[i].name) {\n            var ip = '[' + ((i < 10) ? String(i) + '] ' : String(i) + ']');\n            var n1 = s.COMMPilot.groupLevel.customTags[i].name;\n            var v1 = s.COMMPilot.groupLevel.customTags[i].value;\n            var v2 = s.COMMPilot.groupLevel.customTags[i].actualValue;\n            ctBody.push(`${ip} ${n1}, ${v1}, ${v2}`);\n        }\n    }\n-%>\n<%= ctHeader -%>\n<%= rowCreator.format('Name, Value, ActualValue',ctBody, false); -%>\n<%} -%>"}, {"name": "IPVoiceGroupServices", "active": true, "title": "Display IPVoice Group Services (Module)", "description": "Module to display IPVoice Group Services", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nif (s.COMMPilot && s.COMMPilot.groupServices) {\n    let rowCreator = new RowCreator();\n    let groupNumbers; let groupMBN; let  groupMBNUI;\n\n    if (s.COMMPilot.groupServices.groupNumbers) {\n        var gc = Object.keys(s.COMMPilot.groupServices.groupNumbers).length;\n        if (gc > 0) groupNumbers = [];\n        for (i = 0; i < gc; i++) {\n            var ip = '[' + ((i < 10) ? String(i) + '] ' : String(i) + ']');\n            if (s.COMMPilot.groupServices.groupNumbers[i]) {\n                groupNumbers.push(`${ip} ${s.COMMPilot.groupServices.groupNumbers[i]}`);\n            }\n        }\n    }\n    if (s.COMMPilot.groupServices.groupMBN)       groupMBN   = s.COMMPilot.groupServices.groupMBN;\n    if (s.COMMPilot.groupServices.groupMBNUserID) groupMBNUI = s.COMMPilot.groupServices.groupMBNUserID;\n-%>\n<%= rowCreator.format('GroupNumbers',groupNumbers, false); -%>\n<%= rowCreator.format('GroupMBN',groupMBN); -%>\n<%= rowCreator.format('groupMBNUI',groupMBNUI); -%>\n<%} -%>"}, {"name": "IPVoiceHuntGroup", "active": true, "title": "Display  IPVoice Hunt Group (Module)", "description": "Module to display IPVoice Hunt Group", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nif (s.COMMPilot && s.COMMPilot.huntGroup && (s.COMMPilot.huntGroup.profile || s.COMMPilot.huntGroup.config || s.COMMPilot.huntGroup.agents)) {\n    let rowCreator = new RowCreator();\n    const COLUMN_GAP = 68;\n    let huntHeader; let huntProfileName; let huntPhoneNumberOld; let huntPhoneNumber; let huntOldTimeZone; let huntTimeZone; let huntCAGN; let huntCNOROld; let huntCNOR; let huntAgents; let huntAAGN; let huntAAUI; let huntAAEX;\n\n    huntHeader = '\\nHunt Group\\n----------\\n';\n    if (s.COMMPilot.huntGroup.profile) {\n      huntOldTimeZone = '';\n      if (s.COMMPilot.huntGroup.profile.name)                huntProfileName = s.COMMPilot.huntGroup.profile.name;\n      if (s.COMMPilot.huntGroup.profile.phoneNumber)         huntPhoneNumberOld = s.COMMPilot.huntGroup.profile.phoneNumber;\n      if (s.COMMPilot.huntGroup.profile.extension)           huntPhoneNumber = huntPhoneNumberOld.padEnd(COLUMN_GAP) + ' Extension: ' + s.COMMPilot.huntGroup.profile.extension;\n      if (s.COMMPilot.huntGroup.profile.timeZone)            huntOldTimeZone += s.COMMPilot.huntGroup.profile.timeZone;\n      if (s.COMMPilot.huntGroup.profile.timeZoneDisplayName) huntTimeZone = huntOldTimeZone.padEnd(COLUMN_GAP) + ' TimeZoneDisplayName: ' + s.COMMPilot.huntGroup.profile.timeZoneDisplayName;\n    }\n    if (s.COMMPilot.huntGroup.config) {\n        if (s.COMMPilot.huntGroup.config.agentGroupName)        huntCAGN  = s.COMMPilot.huntGroup.config.agentGroupName;\n        if (s.COMMPilot.huntGroup.config.noAnswerNumberOfRings) huntCNOROld  = s.COMMPilot.huntGroup.config.noAnswerNumberOfRings;\n        if (s.COMMPilot.huntGroup.config.forwardToPhoneNumber)  huntCNOR = huntCNOROld.padEnd(COLUMN_GAP) + ' ForwardToNumber: ' + s.COMMPilot.huntGroup.config.forwardToPhoneNumber;\n    }\n    if (s.COMMPilot.huntGroup.agents) {\n        var act = Object.keys(s.COMMPilot.huntGroup.agents).length;\n        if (act > 0) huntAgents = [];\n        for (i = 0; i < act ; i++) {\n            if (s.COMMPilot.huntGroup.agents[i]) {\n                var ip = '[' + ((i < 10) ? String(i) + '] ' : String(i) + ']');\n                if (s.COMMPilot.huntGroup.agents[i].agentGroupName) huntAAGN  = s.COMMPilot.huntGroup.agents[i].agentGroupName;\n                if (s.COMMPilot.huntGroup.agents[i].agentUserId)    huntAAUI  = s.COMMPilot.huntGroup.agents[i].agentUserId;\n                if (s.COMMPilot.huntGroup.agents[i].agentExtension) huntAAEX  = s.COMMPilot.huntGroup.agents[i].agentExtension;\n                huntAgents.push(`${ip} ${huntAAGN}, ${huntAAUI}, ${huntAAEX}`);\n            }\n        }\n    }\n-%>\n<%= huntHeader -%>\n<%= rowCreator.format('ProfileName',huntProfileName); -%>\n<%= rowCreator.format('TimeZone',huntPhoneNumber); -%>\n<%= rowCreator.format('AgentGroupName',huntTimeZone); -%>\n<%= rowCreator.format('AgentGroupName',huntCAGN); -%>\n<%= rowCreator.format('NoAnswerRings',huntCNOR); -%>\n<%= rowCreator.format('Agent - GroupName, UserId, Extension',huntAgents, false); -%>\n<%} -%>"}, {"name": "IPVoiceIncomingCallControl", "active": true, "title": "Display  IPVoice Incoming Call Control (Module)", "description": "Module to display IPVoice Incoming Call Control", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nif (s.COMMPilot && s.COMMPilot.incomingCallControl) {\n    let rowCreator = new RowCreator();\n    let iccHEA; let iccIIM; let iccCEX; let iccDND; let iccVME; let iccARE; let iccBMO; let iccCNR; let iccCWA; let iccBAAs; let iccCL1; let iccCL2; let iccCDAs; let iccCNAs;\n\n    const txt1 = 'Service is not assigned to this subscriber';\n    var tmp1 = ''; \n    if   (typeof s.COMMPilot.incomingCallControl.commpilotExpress === 'undefined') tmp1 += 'None';\n    else                                                                           tmp1 += 'On - ' + s.COMMPilot.incomingCallControl.commpilotExpress;\n    iccHEA = '\\nIncoming Call Control\\n---------------------\\n';\n    iccBAAs = []; iccCDAs = []; iccCNAs = [];\n    if (s.COMMPilot.incomingCallControl.integratedIMP)        iccIIM =  s.COMMPilot.incomingCallControl.integratedIMP.toUpperCase().includes('ERROR') ? txt1 : 'On';\n    if (s.COMMPilot.incomingCallControl.commpilotExpress)     iccCEX =  tmp1;\n    if (s.COMMPilot.incomingCallControl.doNotDisturb)         iccDND = (s.COMMPilot.incomingCallControl.doNotDisturb === 'true')         ? 'On' : 'Off';\n    if (s.COMMPilot.incomingCallControl.voiceMessaging)       iccVME = (s.COMMPilot.incomingCallControl.voiceMessaging === 'true')       ? 'On' : 'Off';\n    if (s.COMMPilot.incomingCallControl.AnonymousRejection)   iccARE = (s.COMMPilot.incomingCallControl.AnonymousRejection === 'true')   ? 'On' : 'Off';\n    if (s.COMMPilot.incomingCallControl.broadworksMobility)   iccBMO = (s.COMMPilot.incomingCallControl.broadworksMobility === 'true')   ? 'On' : 'Off';\n    if (s.COMMPilot.incomingCallControl.broadworksAnywhere && s.COMMPilot.incomingCallControl.broadworksAnywhere.alertAllLocationsForClickToDialCalls) {\n      tmp1 = (s.COMMPilot.incomingCallControl.broadworksAnywhere.alertAllLocationsForClickToDialCalls === 'true') ? 'On' : 'Off';\n      iccBAAs.push(`[ClickToDial]  ${tmp1}`);\n    }\n    if (s.COMMPilot.incomingCallControl.broadworksAnywhere && s.COMMPilot.incomingCallControl.broadworksAnywhere.alertAllLocationsForGroupPagingCalls) {\n      tmp1 = (s.COMMPilot.incomingCallControl.broadworksAnywhere.alertAllLocationsForGroupPagingCalls === 'true') ? 'On' : 'Off';\n      iccBAAs.push(`[GroupPaging]  ${tmp1}`);\n    }\n    if (s.COMMPilot.incomingCallControl.externalCallingLineID) iccCL1 = (s.COMMPilot.incomingCallControl.externalCallingLineID === 'true') ? 'On' : 'Off';\n    if (s.COMMPilot.incomingCallControl.internalCallingLineID) iccCL2 = (s.COMMPilot.incomingCallControl.internalCallingLineID === 'true') ? 'On' : 'Off';\n    if (s.COMMPilot.incomingCallControl.callingNumberDelivery && s.COMMPilot.incomingCallControl.callingNumberDelivery.isActiveForExternalCalls) {\n      tmp1 = (s.COMMPilot.incomingCallControl.callingNumberDelivery.isActiveForExternalCalls          === 'true') ? 'On' : 'Off';\n      iccCDAs.push(`[External]     ${tmp1}`);\n    }\n    if (s.COMMPilot.incomingCallControl.callingNumberDelivery && s.COMMPilot.incomingCallControl.callingNumberDelivery.isActiveForInternalCalls) {\n      tmp1 = (s.COMMPilot.incomingCallControl.callingNumberDelivery.isActiveForInternalCalls         === 'true') ? 'On' : 'Off';\n      iccCDAs.push(`[Internal]     ${tmp1}`);\n    }\n    if (s.COMMPilot.incomingCallControl.callingNameDelivery && s.COMMPilot.incomingCallControl.callingNameDelivery.isActiveForExternalCalls) {\n      tmp1 = (s.COMMPilot.incomingCallControl.callingNameDelivery.isActiveForExternalCalls            === 'true') ? 'On' : 'Off';\n      iccCNAs.push(`[External]     ${tmp1}`);\n    }\n    if (s.COMMPilot.incomingCallControl.callingNameDelivery && s.COMMPilot.incomingCallControl.callingNameDelivery.isActiveForInternalCalls) {\n      tmp1 = (s.COMMPilot.incomingCallControl.callingNameDelivery.isActiveForInternalCalls            === 'true') ? 'On' : 'Off';\n      iccCNAs.push(`[Internal]     ${tmp1}`);\n    }\n    if (s.COMMPilot.incomingCallControl.callingNameRetreival) iccCNR =  (s.COMMPilot.incomingCallControl.callingNameRetreival === 'true') ? 'On' : 'Off';\n    if (s.COMMPilot.incomingCallControl.callWaiting)          iccCWA =  (s.COMMPilot.incomingCallControl.callWaiting  === 'true')         ? 'On' : 'Off';\n-%>\n<%= iccHEA -%>\n<%= rowCreator.format('IntegratedIMP',iccIIM); -%>\n<%= rowCreator.format('CommpilotExpress',iccCEX); -%>\n<%= rowCreator.format('DoNotDisturb',iccDND); -%>\n<%= rowCreator.format('VoiceMessaging',iccVME); -%>\n<%= rowCreator.format('AnonymousRejection',iccARE); -%>\n<%= rowCreator.format('BroadworksMobility',iccBMO); -%>\n<%= rowCreator.format('BroadworksAnywhere',iccBAAs, false); -%>\n<%= rowCreator.format('ExternalCallingLineID',iccCL1); -%>\n<%= rowCreator.format('InternalCallingLineID',iccCL2); -%>\n<%= rowCreator.format('CallingNameDelivery',iccCNAs, false); -%>\n<%= rowCreator.format('CallingNumberDelivery',iccCDAs,false); -%>\n<%= rowCreator.format('CallingNameRetreival',iccCNR); -%>\n<%= rowCreator.format('CallWaiting',iccCWA); -%>\n<%} -%>"}, {"name": "IPVoiceIPWorks", "active": true, "title": "Display IP Works (Module)", "description": "Module to display IP Works in IPVoice ", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\n    if (s.IPWorks) {\n    let rowCreator = new RowCreator();\n    let ipwHeader; let ipwED; let ipwID; \n\n    ipwHeader = '\\nDomain\\n------\\n';\n    if (s.COMMPilot.enum && (s.COMMPilot.enum.type != 'Unknown')) {\n        ipwED = '';\n        if (s.COMMPilot.enum.domain && (typeof s.COMMPilot.enum.domain == 'undefined')) ipwED += 'no result ';\n        if (s.COMMPilot.enum.domain == '')                                              ipwED += 'no result ';\n        if (s.COMMPilot.enum.domain && s.COMMPilot.enum.domain !== '')                  ipwED += s.COMMPilot.enum.domain + ' ';\n        if (s.COMMPilot.enum.type)                                                      ipwED += s.COMMPilot.enum.type.toUpperCase();\n    }\n    if (s.IPWorks) {\n        ipwID = '';\n        if (s.IPWorks.customerdomain && s.IPWorks.customerdomain !== '') ipwID += s.IPWorks.customerdomain + ' ';\n        if (s.IPWorks.customertype)                                      ipwID += s.IPWorks.customertype.toUpperCase();;\n    }\n-%>\n<%= ipwHeader -%>\n<%= rowCreator.format('ENUM',ipwED); -%>\n<%= rowCreator.format('IPWorks',ipwID); -%>\n<%} -%>"}, {"name": "IPVoicePacksAssigned", "active": true, "title": "Display IPVoice Packs Assigned for CommPilot (Module)", "description": "Module to display IPVoice Packs Assigned for CommPilot", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nif (s.COMMPilot && (s.COMMPilot.servicePacksAssigned || s.COMMPilot.userServicePacksAssigned)) {\n    let rowCreator = new RowCreator();\n    let packsHeader; let servicePacks; let userPacks;\n\n    if (s.COMMPilot.servicePacksAssigned)     var spa = Object.keys(s.COMMPilot.servicePacksAssigned).length;\n    if (s.COMMPilot.userServicePacksAssigned) var usp = Object.keys(s.COMMPilot.userServicePacksAssigned).length;\n    if ((spa > 0) || (usp > 0)) packsHeader = '\\nPacks Assigned (COMMPilot)\\n--------------------------\\n';\n    if (spa > 0) {\n        servicePacks = [];\n        for (i = 0; i < spa ; i++) {\n            if (s.COMMPilot.servicePacksAssigned[i]) {\n                if (s.COMMPilot.servicePacksAssigned[i][0] && s.COMMPilot.servicePacksAssigned[i][1] == 'true') {\n                    var ip = '[' + ((i < 10) ? String(i) + '] ' : String(i) + ']');\n                    servicePacks.push(`${ip} ${s.COMMPilot.servicePacksAssigned[i][0]}`);\n                }\n            }\n        }\n    }\n    if (usp > 0) {\n        userPacks = [];\n        for (i = 0; i < usp ; i++) {\n            if (s.COMMPilot.userServicePacksAssigned[i]) {\n                if (s.COMMPilot.userServicePacksAssigned[i][0] && s.COMMPilot.userServicePacksAssigned[i][1] == 'true') {\n                    var ip = '[' + ((i < 10) ? String(i) + '] ' : String(i) + ']');\n                    userPacks.push(`${ip} ${s.COMMPilot.userServicePacksAssigned[i][0]}`);\n                }\n            }\n        }\n    }\n-%>\n<%= packsHeader -%>\n<%= rowCreator.format('ServicePack',servicePacks, false); -%>\n<%= rowCreator.format('UserPack',userPacks, false); -%>\n<%} -%>"}, {"name": "IPVoiceRegistrationDetails", "active": true, "title": "Display IPVoice Registration Info from CFNN (Module)", "description": "Module to display IPVoice Registration Info from CFNN", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nif (s.CFNN && s.CFNN.registrations && s.CFNN.Details && s.CFNN.Details.toUpperCase().includes('VIRTUAL')) {\n    let rowCreator = new RowCreator();\n    let cregHeader; let cregHGPolicy; let cregAllowCW; let cregSTNext; let cregFAWtg; let cregCNR; let cregAgents; let cregXTOp; let cregSchedule; let cregHolidays;\n    let creg01; let creg02; let creg03; let creg04; let creg05; let creg06; let creg07; let creg08; let creg09; let creg10; let creg11; let creg12; let creg13; let creg15; let creg16; let creg17; let creg18;\n    let creg20; let creg21; let creg22; let creg23; let creg25; let creg26; let creg27; let creg28; let creg210; let creg211; let creg212; let creg213;\n\n    cregHeader = '\\nCFNN Registration Details \\n-------------------------\\n';\n    if (s.CFNN.registrations[0]) {\n        if (s.CFNN.registrations[0]['HG Policy'])     cregHGPolicy = s.CFNN.registrations[0]['HG Policy'];\n        if (s.CFNN.registrations[0]['Allow CW'])      cregAllowCW  = s.CFNN.registrations[0]['Allow CW'];\n        if (s.CFNN.registrations[0]['Skip to Next'])  cregSTNext   = s.CFNN.registrations[0]['Skip to Next'];\n        if (s.CFNN.registrations[0]['Fwd after Wtg']) cregFAWtg    = s.CFNN.registrations[0]['Fwd after Wtg'];\n        if (s.CFNN.registrations[0]['CFwd NR'])       cregCNR      = s.CFNN.registrations[0]['CFwd NR'];\n        if (s.CFNN.registrations[0]['Agents'])        cregAgents   = s.CFNN.registrations[0]['Agents'].replace('+','*').replace(/\\+/g,'\\n                                                +').replace('*','+');\n        if (s.CFNN.registrations[0]['Xfer to Op'])    cregXTOp     = s.CFNN.registrations[0]['Xfer to Op'];\n        if (s.CFNN.registrations[0]['Schedule'])      cregSchedule = s.CFNN.registrations[0]['Schedule'];\n        if (s.CFNN.registrations[0]['Holidays'])      cregHolidays = s.CFNN.registrations[0]['Holidays'];\n    }\n    if (s.CFNN.registrations[1]) {\n        if (s.CFNN.registrations[1][0])               creg10 = s.CFNN.registrations[1][0];\n        if (s.CFNN.registrations[1][1])               creg11 = s.CFNN.registrations[1][1];\n        if (s.CFNN.registrations[1][2])               creg12 = s.CFNN.registrations[1][2];\n        if (s.CFNN.registrations[1][3])               creg13 = s.CFNN.registrations[1][3];\n        if (s.CFNN.registrations[1][5])               creg15 = s.CFNN.registrations[1][5];\n        if (s.CFNN.registrations[1][6])               creg16 = s.CFNN.registrations[1][6];\n        if (s.CFNN.registrations[1][7])               creg17 = s.CFNN.registrations[1][7];\n        if (s.CFNN.registrations[1][8])               creg18 = s.CFNN.registrations[1][8];\n    }\n    if (s.CFNN.registrations[2]){\n        if (s.CFNN.registrations[2][0])               creg20  = s.CFNN.registrations[2][0];\n        if (s.CFNN.registrations[2][1])               creg21  = s.CFNN.registrations[2][1];\n        if (s.CFNN.registrations[2][2])               creg22  = s.CFNN.registrations[2][2];\n        if (s.CFNN.registrations[2][3])               creg23  = s.CFNN.registrations[2][3];\n        if (s.CFNN.registrations[2][5])               creg25  = s.CFNN.registrations[2][5];\n        if (s.CFNN.registrations[2][6])               creg26  = s.CFNN.registrations[2][6];\n        if (s.CFNN.registrations[2][7])               creg27  = s.CFNN.registrations[2][7];\n        if (s.CFNN.registrations[2][8])               creg28  = s.CFNN.registrations[2][8];\n        if (s.CFNN.registrations[2][10])              creg210 = s.CFNN.registrations[2][10];\n        if (s.CFNN.registrations[2][11])              creg211 = s.CFNN.registrations[2][11];\n        if (s.CFNN.registrations[2][12])              creg212 = s.CFNN.registrations[2][12];\n        if (s.CFNN.registrations[2][13])              creg213 = s.CFNN.registrations[2][13];\n    }\n-%>\n<%= cregHeader -%>\n<%= rowCreator.format('HG Policy',cregHGPolicy); -%>\n<%= rowCreator.format('Allow CW',cregAllowCW); -%>\n<%= rowCreator.format('Skip to Next',cregSTNext); -%>\n<%= rowCreator.format('Forward After Wtg',cregFAWtg); -%>\n<%= rowCreator.format('CFwd NR',cregCNR); -%>\n<%= rowCreator.format('Agents',cregAgents); -%>\n<%= rowCreator.format('Xfer to Op',cregXTOp); -%>\n<%= rowCreator.format('Schedule',cregSchedule); -%>\n<%= rowCreator.format('Holidays',cregHolidays); -%>\n<%= rowCreator.format('Key',creg10); -%>\n<%= rowCreator.format('Description1',creg11); -%>\n<%= rowCreator.format('Action1',creg12); -%>\n<%= rowCreator.format('Number1',creg13); -%>\n<%= rowCreator.format('Key',creg15); -%>\n<%= rowCreator.format('Description2',creg16); -%>\n<%= rowCreator.format('Action2',creg17); -%>\n<%= rowCreator.format('Number2',creg18); -%>\n<%= rowCreator.format('Key',creg20); -%>\n<%= rowCreator.format('Description3',creg21); -%>\n<%= rowCreator.format('Action3',creg22); -%>\n<%= rowCreator.format('Number3',creg23); -%>\n<%= rowCreator.format('Key',creg25); -%>\n<%= rowCreator.format('Description4',creg26); -%>\n<%= rowCreator.format('Action4',creg27); -%>\n<%= rowCreator.format('Number4',creg28); -%>\n<%= rowCreator.format('Key',creg210); -%>\n<%= rowCreator.format('Description5',creg211); -%>\n<%= rowCreator.format('Action5',creg212); -%>\n<%= rowCreator.format('Number5',creg213); -%>\n<%} -%>"}, {"name": "IPVoiceRegistrationInfoCFNN", "active": true, "title": "Display IPVoice Registration Info from CFNN (Module)", "description": "Module to display IPVoice Registration Info from CFNN", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nif (s.CFNN && s.CFNN.registrations && s.CFNN.registrations[0] && s.CFNN.registrations[0] && (!s.CFNN.registrations[0]['HG Policy'])) {\n    let rowCreator = new RowCreator();\n    let regHeader; let regRegs;\n\n    var rc = Object.keys(s.CFNN.registrations).length;\n    if (rc > 0) regHeader = '\\nRegistration Info\\n-----------------\\n';\n    if (rc > 0) regRegs = [];\n    for (i = 0; i < rc ; i++) {\n        if (s.CFNN.registrations[i]) {\n            var f00; var f01; var f02; var f03; var f04; var f05; var f06; var f07; var f08; var f09; var f10;\n            var ip = '[' + ((i < 10) ? String(i) + '] ' : String(i) + ']');\n            if (s.CFNN.registrations[i].User)          { f00 = s.CFNN.registrations[i].User;          regRegs.push(`${ip} RegUser    - ${f00}`); }\n            if (s.CFNN.registrations[i].RegType)       { f01 = s.CFNN.registrations[i].RegType;       regRegs.push(`${ip} RegType    - ${f01}`); }\n            if (s.CFNN.registrations[i].Device)        { f02 = s.CFNN.registrations[i].Device;        regRegs.push(`${ip} DeviceName - ${f02}`); }\n            if (s.CFNN.registrations[i]['Device Name']){ f03 = s.CFNN.registrations[i]['Device Name'];regRegs.push(`${ip} DeviceName - ${f03}`); }\n            if (s.CFNN.registrations[i]['Expires'])    { f04 = s.CFNN.registrations[i]['Expires'];    regRegs.push(`${ip} Expires    - ${f04}`); }\n            if (s.CFNN.registrations[i]['Line/Port'])  { f05 = s.CFNN.registrations[i]['Line/Port'];  regRegs.push(`${ip} LinePort   - ${f05}`); }\n            if (s.CFNN.registrations[i]['Expires:'])   { f06 = s.CFNN.registrations[i]['Expires:'];   regRegs.push(`${ip} Expires    - ${f06}`); }\n            if (s.CFNN.registrations[i].UserAgent)     { f07 = s.CFNN.registrations[i].UserAgent;     regRegs.push(`${ip} UserAgent  - ${f07}`); }\n            if (s.CFNN.registrations[i].SBC)           { f08 = s.CFNN.registrations[i].SBC;           regRegs.push(`${ip} SBC        - ${f08}`); }\n            if (s.CFNN.registrations[i]['Call Info'])  { f09 = s.CFNN.registrations[i]['Call Info'];  regRegs.push(`${ip} CallInfo   - ${f09}`); }\n            if (s.CFNN.registrations[i]['Trunk FNN'])  { f10 = s.CFNN.registrations[i]['Trunk FNN'];  regRegs.push(`${ip} TrunkFNN   - ${f10}`); }\n        }\n    }\n-%>\n<%= regHeader -%>\n<%= rowCreator.format('Registrations', regRegs, false); -%>\n<%} -%>"}, {"name": "IPVoiceRegistrationInfoCommpilot", "active": true, "title": "Display IPVoice Registration Info from Commpilot (Module)", "description": "Module to display IPVoice Registration Info from Commpilot", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nif (s.COMMPilot && s.COMMPilot.registration && s.COMMPilot.registration.registrations) {\n    let rowCreator = new RowCreator();\n    let regHeader; let regType; let regDName; let regLPort; let regExpires; let regUAgent;\n    let regMAddress; let regFirmware; let regSBC; let regCInfo; let regTFNN; let regVendor; let regModel;\n\n    if (s.COMMPilot.registration.registrations[0]) {\n        regHeader = '\\nRegistration Info (Commpilot)\\n-----------------------------\\n';\n        if (s.COMMPilot.registration.registrations[0].registrationType)         regType     = s.COMMPilot.registration.registrations[0].registrationType;\n        if (s.COMMPilot.registration.registrations[0].deviceName)               regDName    = s.COMMPilot.registration.registrations[0].deviceName;\n        if (s.COMMPilot.registration.registrations[0].linePort)                 regLPort    = s.COMMPilot.registration.registrations[0].linePort;\n        if (s.COMMPilot.registration.registrations[0].expires)                  regExpires  = s.COMMPilot.registration.registrations[0].expires;\n        if (s.COMMPilot.registration.registrations[0].userAgent)                regUAgent   = s.COMMPilot.registration.registrations[0].userAgent;\n        if (s.COMMPilot.registration.registrations[0].macAddress)               regMAddress = s.COMMPilot.registration.registrations[0].macAddress;\n        if (s.COMMPilot.registration.registrations[0].firmware) {\n            if (s.COMMPilot.registration.registrations[0].userAgent) {\n                if (regUAgent.toUpperCase().includes('ONEACCESS')) {\n                    var first_ch = regUAgent.indexOf('_') + 1;\n                    var last_ch  = regUAgent.lastIndexOf(' ');\n                    regFirmware = regUAgent.substring(first_ch,last_ch);\n                } else {\n                    regFirmware = s.COMMPilot.registration.registrations[0].firmware;\n                }\n            }\n        }\n        if (s.COMMPilot.registration.registrations[0].vendor)                   regVendor   = s.COMMPilot.registration.registrations[0].vendor;\n        if (s.COMMPilot.registration.registrations[0].model)                    regModel    = s.COMMPilot.registration.registrations[0].model;\n    } else {\n        regHeader = '\\nRegistration Info (Commpilot)\\n-----------------------------\\n';\n        if (s.COMMPilot.registration.registrations.registrationType)            regType     = s.COMMPilot.registration.registrations.registrationType;\n        if (s.COMMPilot.registration.registrations.deviceName)                  regDName    = s.COMMPilot.registration.registrations.deviceName;\n        if (s.COMMPilot.registration.registrations.linePort)                    regLPort    = s.COMMPilot.registration.registrations.linePort;\n        if (s.COMMPilot.registration.registrations.expires)                     regExpires  = s.COMMPilot.registration.registrations.expires;\n        if (s.COMMPilot.registration.registrations.userAgent)                   regUAgent   = s.COMMPilot.registration.registrations.userAgent;\n        if (s.COMMPilot.registration.registrations.macAddress)                  regMAddress = s.COMMPilot.registration.registrations.macAddress;\n        if (s.COMMPilot.registration.registrations.firmware) {\n            if (s.COMMPilot.registration.registrations.userAgent) {\n                if (s.COMMPilot.registration.registrations.userAgent.toUpperCase().includes('ONEACCESS')) {\n                    var first_ch = regUAgent.indexOf('_') + 1;\n                    var last_ch  = regUAgent.lastIndexOf(' ');\n                    regFirmware = regUAgent.substring(first_ch,last_ch);\n                } else {\n                    regFirmware = s.COMMPilot.registration.registrations.firmware;\n                }\n            }\n        }\n        if (s.COMMPilot.registration.registrations.vendor)                      regVendor   = s.COMMPilot.registration.registrations.vendor;\n        if (s.COMMPilot.registration.registrations.model)                       regModel    = s.COMMPilot.registration.registrations.model;\n    }\n-%>\n<%= regHeader -%>\n<%= rowCreator.format('Reg Type',regType); -%>\n<%= rowCreator.format('Device Name',regDName); -%>\n<%= rowCreator.format('Line Port',regLPort); -%>\n<%= rowCreator.format('Expires',regExpires); -%>\n<%= rowCreator.format('User Agent',regUAgent); -%>\n<%= rowCreator.format('Mac Address',regMAddress); -%>\n<%= rowCreator.format('Firmware',regFirmware); -%>\n<%= rowCreator.format('SBC',regSBC); -%>\n<%= rowCreator.format('CallInfo',regCInfo); -%>\n<%= rowCreator.format('Trunk FNN',regTFNN); -%>\n<%= rowCreator.format('Vendor',regVendor); -%>\n<%= rowCreator.format('Model',regModel); -%>\n<%} -%>"}, {"name": "IPVoiceSableData", "active": true, "title": "Display  IPVoice Sable Data (Module)", "description": "Module to display IPVoice Sable Data", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nif (s.SableVOIP && s.SableVOIP.results && s.SableVOIP.results[0]) {\n    let rowCreator = new RowCreator();\n    let sableHeader; let sableSBC; let sableSBCS; let sableSDomain; let sableCIP; let sableVID; let sableDModel; let sableAVCID; let sableBNGName; let sableMA4H; let sableMA24H; let sableMLatest; let sableMStatus; let sableSBCM;\n\n    sableHeader = '\\nSable Data\\n----------\\n';\n    if (s.SableVOIP.results[0].sbc)            sableSBC     = s.SableVOIP.results[0].sbc;\n    if (s.SableVOIP.results[0].sbc_state)      sableSBCS    = s.SableVOIP.results[0].sbc_state;\n    if (s.SableVOIP.results[0].sip_domain)     sableSDomain = s.SableVOIP.results[0].sip_domain;\n    if (s.SableVOIP.results[0].customer_ip)    sableCIP     = s.SableVOIP.results[0].customer_ip;\n    if (s.SableVOIP.results[0].vlan_id)        sableVID     = s.SableVOIP.results[0].vlan_id;\n    if (s.SableVOIP.results[0].device_model)   sableDModel  = s.SableVOIP.results[0].device_model;\n    if (s.SableVOIP.results[0].avc_id)         sableAVCID   = s.SableVOIP.results[0].avc_id;\n    if (s.SableVOIP.results[0].bng_name)       sableBNGName = s.SableVOIP.results[0].bng_name;\n    if (s.SableVOIP.results[0].mos_avg_4h)     sableMA4H    = parseFloat(s.SableVOIP.results[0].mos_avg_4h).toFixed(1);\n    if (s.SableVOIP.results[0].mos_avg_24h)    sableMA24H   = parseFloat(s.SableVOIP.results[0].mos_avg_24h).toFixed(1);\n    if (s.SableVOIP.results[0].mos_latest)     sableMLatest = s.SableVOIP.results[0].mos_latest;\n    if (s.SableVOIP.results[0].mos_anomaly && s.SableVOIP.results[0].mos_anomaly == 'true') sableMStatus = 'Error'; else sableMStatus = 'OK';\n    if (s.SableVOIP.results[0].mos_avg_4h || s.SableVOIP.results[0].mos_avg_24h || s.SableVOIP.results[0].mos_latest) sableSBCM = '*MOS score is acceptable if above 3.5\\n';\n-%>\n<%= sableHeader -%>\n<%= rowCreator.format('SBC',sableSBC); -%>\n<%= rowCreator.format('SBCState',sableSBCS); -%>\n<%= rowCreator.format('SIPDomain',sableSDomain); -%>\n<%= rowCreator.format('CustomerIp',sableCIP); -%>\n<%= rowCreator.format('VLANId',sableVID); -%>\n<%= rowCreator.format('DeviceModel',sableDModel); -%>\n<%= rowCreator.format('AVCId',sableAVCID); -%>\n<%= rowCreator.format('BNGName',sableBNGName); -%>\n<%= rowCreator.format('MOS4Hr*',sableMA4H); -%>\n<%= rowCreator.format('MOS24Hr*',sableMA24H); -%>\n<%= rowCreator.format('MOSLatest*',sableMLatest); -%>\n<%= rowCreator.format('MOSStatus',sableMStatus); -%>\n<%= sableSBCM -%>\n<%} -%>"}, {"name": "IPVoiceSableHealth", "active": true, "title": "Display Sable Health in IPVoice (Module)", "description": "Module to display Sable Health in IPVoice", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nif (s.SableRaw && s.SableRaw.results && s.SableRaw.results[0]) {\n    let rowCreator = new RowCreator();\n    let sableRHeader; let sableRPDD; let sableRHDT; let sableRCRPL1; let sableRCRPL2; let sableRRM1; let sableRRM2; let sableRPL1; let sableRPL2; let sableRGMF1; let sableRGMF2; let sableRMessage;\n\n\n    sableRHeader = '\\nSable Health\\n------------\\n';\n    if (s.SableRaw.results[0].Post_Dial_Delay)                sableRPDD   = s.SableRaw.results[0].Post_Dial_Delay;\n    if (s.SableRaw.results[0].h323_disconnect_time)           sableRHDT   = s.SableRaw.results[0].h323_disconnect_time.substring(0,8);\n    if (s.SableRaw.results[0].Called_RTP_Packets_Lost_FS1)    sableRCRPL1 = s.SableRaw.results[0].Called_RTP_Packets_Lost_FS1;\n    if (s.SableRaw.results[0].Called_RTP_Packets_Lost_FS2)    sableRCRPL2 = s.SableRaw.results[0].Called_RTP_Packets_Lost_FS2;\n    if (s.SableRaw.results[0].Called_RTP_MaxJitter_FS1)       sableRRM1   = s.SableRaw.results[0].Called_RTP_MaxJitter_FS1;\n    if (s.SableRaw.results[0].Called_RTP_MaxJitter_FS2)       sableRRM2   = s.SableRaw.results[0].Called_RTP_MaxJitter_FS2;\n    if (s.SableRaw.results[0].Calling_RTP_Packets_Lost_FS1)   sableRPL1   = s.SableRaw.results[0].Calling_RTP_Packets_Lost_FS1;\n    if (s.SableRaw.results[0].Calling_RTP_Packets_Lost_FS2)   sableRPL2   = s.SableRaw.results[0].Calling_RTP_Packets_Lost_FS2;\n    if (s.SableRaw.results[0].Calling_RTP_MaxJitter_FS1)      sableRGMF1  = s.SableRaw.results[0].Calling_RTP_MaxJitter_FS1;\n    if (s.SableRaw.results[0].Calling_RTP_MaxJitter_FS2)      sableRGMF2  = s.SableRaw.results[0].Calling_RTP_MaxJitter_FS2;\n    if (s.SableRaw.results[0].Called_RTP_MaxJitter_FS1  || s.SableRaw.results[0].Called_RTP_MaxJitter_FS2 ||\n        s.SableRaw.results[0].Calling_RTP_MaxJitter_FS1 || s.SableRaw.results[0].Calling_RTP_MaxJitter_FS2) sableRMessage = '#Jitter ms is acceptable if below 30 for audio and video\\n';\n-%>\n<%= sableRHeader -%>\n<%= rowCreator.format('PostDialDelay', sableRPDD); -%>\n<%= rowCreator.format('H323SessionDuration', sableRHDT); -%>\n<%= rowCreator.format('CalledPacketsLostAudio', sableRCRPL1); -%>\n<%= rowCreator.format('CalledPacketsLostVideo', sableRCRPL2); -%>\n<%= rowCreator.format('CalledJitterAudio#', sableRRM1); -%>\n<%= rowCreator.format('CalledJitterVideo#',sableRRM2); -%>\n<%= rowCreator.format('CallingPacketsLostAudio', sableRPL1); -%>\n<%= rowCreator.format('CallingPacketsLostVideo', sableRPL2); -%>\n<%= rowCreator.format('CallingJitterAudio#', sableRGMF1); -%>\n<%= rowCreator.format('CallingJitterVideo#', sableRGMF2); -%>\n<%= sableRMessage -%>\n<%} -%>"}, {"name": "IPVoiceSharedDevices", "active": true, "title": "Display IPVoice Shared Devices (Module)", "description": "Module to display IPVoice Shared Devices", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nif (s.COMMPilot && s.COMMPilot.sca) {\n    let rowCreator = new RowCreator();\n    let sdHeader; let sdBody;\n\n    if (s.COMMPilot.sca.sharedDevices) var sd = Object.keys(s.COMMPilot.sca.sharedDevices).length;\n    if (sd > 0) sdHeader = '\\nShared Devices\\n--------------\\n';\n    if (sd > 0) sdBody = [];\n    for (i = 0; i < sd ; i++) {\n        if (s.COMMPilot.sca.sharedDevices[i]) {\n            if (s.COMMPilot.sca.sharedDevices[i].deviceName && s.COMMPilot.sca.sharedDevices[i].deviceType) {\n                var ip = '[' + ((i < 10) ? String(i) + '] ' : String(i) + ']');\n                var dn = s.COMMPilot.sca.sharedDevices[i].deviceName;\n                var lp = s.COMMPilot.sca.sharedDevices[i].linePort;\n                var dt = s.COMMPilot.sca.sharedDevices[i].deviceType;\n                sdBody.push(`${ip} ${dn}, ${lp}, ${dt}`);\n            }\n        }\n    }\n-%>\n<%= sdHeader -%>\n<%= rowCreator.format('Name, Port, Type',sdBody, false); -%>\n<%} -%>"}, {"name": "IPVoiceSummaryText", "active": true, "title": "IPVoice Summary (Text)", "description": "For getting IPVoice Summary Text", "allowedSystemsToAppend": ["ServiceCentral"], "suite": [], "templateType": "Service Check", "formatType": "Text", "listCondition": "s.COMMPilot && s.COMMPilot.user && s.COMMPilot.user.clusterType && s.COMMPilot.user.clusterType !== 'STOA'", "defaultPriority": 1, "template": "<% \nif (s.COMMPilot) {\n    let rowCreator = new RowCreator();\n    // Variables Declaration Begin\n    let templateHeader; let customerName; let customerCIDN; let bmCID; let biPAT; let nbnLST;\n    // Variables Declaration End\n\n    // Logic Begin\n    if      (s.COMMPilot.user && s.COMMPilot.user.clusterType && s.COMMPilot.user.clusterType == 'STOA') templateHeader = 'BSIP Carriage\\n=============\\n';\n    else if (s.COMMPilot.user && s.COMMPilot.user.clusterType && s.COMMPilot.user.clusterType == 'DOT')  templateHeader = 'DOT Carriage\\n============\\n';\n    else if (s.COMMPilot.user && s.COMMPilot.user.clusterType && s.COMMPilot.user.clusterType == 'TIPT') templateHeader = 'TIPT\\n====\\n';\n\n    if (s.MAGPIECIDN && s.MAGPIECIDN.rawData && s.MAGPIECIDN.rawData[0]) {\n        if (s.MAGPIECIDN.rawData[0].Customer)   customerName = s.MAGPIECIDN.rawData[0].Customer;\n        if (s.MAGPIECIDN.rawData[0].Attributes) customerCIDN = s.MAGPIECIDN.rawData[0].Attributes;\n    }\n\n    if (s.BATMAGPIE && s.BATMAGPIE.CARRIAGEID) bmCID = s.BATMAGPIE.CARRIAGEID;\n    if (s.BATIPNBN && s.BATIPNBN.session && s.BATIPNBN.session.service_location_details && s.BATIPNBN.session.service_location_details.primary_access_technology) biPAT = s.BATIPNBN.session.service_location_details.primary_access_technology;\n    if (r.MTR023 && r.MTR023.serviceTestResult) nbnLST = r.MTR023.serviceTestResult;\n    let mergeLink = '\\nMerge Result Link: https://merge.in.telstra.com.au/serviceCheck/view/html/' + data.id;\n    // Logic End\n\n    // Print Variables\n-%>\n<%= templateHeader -%>\n<%= rowCreator.format('Customer',customerName); -%>\n<%= rowCreator.format('CIDN',customerCIDN); -%>\n<%- renderTemplate(\"IPVoiceUserDetails\"); -%>\n<%- renderTemplate(\"IPVoicePacksAssigned\"); -%>\n<%- renderTemplate(\"IPVoiceFeaturePacks\"); -%>\n<%- renderTemplate(\"IPVoiceCallForwarding\"); -%>\n<%- renderTemplate(\"IPVoiceGroupServices\"); -%>\n<%- renderTemplate(\"IPVoiceUserInfo\"); -%>\n<% if (s.COMMPilot && s.COMMPilot.registration && s.COMMPilot.registration.registrations) { -%><%- renderTemplate(\"IPVoiceRegistrationInfoCommpilot\"); -%>\n<%} else if (s.CFNN && s.CFNN.registrations) { -%><%- renderTemplate(\"IPVoiceRegistrationInfoCFNN\"); -%><%} -%>\n<%- renderTemplate(\"IPVoiceRegistrationDetails\"); -%>\n<%- renderTemplate(\"IPVoiceIPWorks\"); -%>\n<%- renderTemplate(\"IPVoiceDeviceCustomTags\"); -%>\n<%- renderTemplate(\"IPVoiceIncomingCallControl\"); -%>\n<%- renderTemplate(\"IPVoiceSharedDevices\"); -%>\n<%- renderTemplate(\"IPVoiceGroupCustomTags\"); -%>\n<%- renderTemplate(\"IPVoiceEnterpiseTrunk\"); -%>\n<%- renderTemplate(\"IPVoiceTrunkGroup\"); -%>\n<%- renderTemplate(\"IPVoiceHuntGroup\"); -%>\n<%- renderTemplate(\"IPVoiceSableData\"); -%>\n<%- renderTemplate(\"IPVoiceSableHealth\"); -%>\n<%= rowCreator.format('Carriage ID',bmCID); -%>\n<%= rowCreator.format('Service Type',biPAT); -%>\n<%= rowCreator.format('NBN Line State Test',nbnLST); -%>\n<%= mergeLink -%>\n<%} -%>\n<%- renderTemplate(\"ShowRules\"); -%>"}, {"name": "IPVoiceTrunkGroup", "active": true, "title": "Display IPVoice Trunk Group (Module)", "description": "Module to display IPVoice Trunk Group", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nif (s.COMMPilot && s.COMMPilot.trunkGroup) {\n    let rowCreator = new RowCreator();\n    let trunkHeader; let trunkPUI; let trunkRA; let trunkAN; let trunkPUCNP; let trunkSCOM; let trunkGS; let trunkDN; let trunkDL; let trunkMAC; let trunkEB; let trunkTAIC; let trunkTAOC;\n    let trunkIDI; let trunkENAI; let trunkFOSIS; let trunkFTC; let trunkITGIFNC; let trunkIOIFNC; let trunkUCLIFECP; let trunkUCLAIP; let trunkCSFSCP;\n\n    trunkHeader = '\\nTrunk Group\\n-----------\\n';\n    if (s.COMMPilot.trunkGroup.trunkInfo) {\n        if (s.COMMPilot.trunkGroup.trunkInfo.pilotUserId)                  trunkPUI   = s.COMMPilot.trunkGroup.trunkInfo.pilotUserId;\n        if (s.COMMPilot.trunkGroup.trunkInfo.requireAuthentication)        trunkRA    = s.COMMPilot.trunkGroup.trunkInfo.requireAuthentication;\n        if (s.COMMPilot.trunkGroup.trunkInfo.authName)                     trunkAN    = s.COMMPilot.trunkGroup.trunkInfo.authName;\n        if (s.COMMPilot.trunkGroup.trunkInfo.pilotUserChargeNumberPolicy)  trunkPUCNP = s.COMMPilot.trunkGroup.trunkInfo.pilotUserChargeNumberPolicy;\n        if (s.COMMPilot.trunkGroup.trunkInfo.sendContinuousOptionsMessage) trunkSCOM  = s.COMMPilot.trunkGroup.trunkInfo.sendContinuousOptionsMessage;\n        if (s.COMMPilot.trunkGroup.trunkInfo.trunkGroupState)              trunkGS    = s.COMMPilot.trunkGroup.trunkInfo.trunkGroupState;\n    }\n    if (s.COMMPilot.trunkGroup.deviceName)  trunkDN = s.COMMPilot.trunkGroup.deviceName;\n    if (s.COMMPilot.trunkGroup.deviceLevel) trunkDL = s.COMMPilot.trunkGroup.deviceLevel;\n    if (s.COMMPilot.trunkGroup.trunkCalls) {\n        if (s.COMMPilot.trunkGroup.trunkCalls.maxActiveCalls)            trunkMAC = s.COMMPilot.trunkGroup.trunkCalls.maxActiveCalls;\n        if (s.COMMPilot.trunkGroup.trunkCalls.enableBursting) {\n            if (s.COMMPilot.trunkGroup.trunkCalls.enableBursting == 'true') trunkEB = 'True'; else trunkEB = 'False';\n        }\n        if (s.COMMPilot.trunkGroup.trunkCalls.totalActiveIncomingCalls)  trunkTAIC = s.COMMPilot.trunkGroup.trunkCalls.totalActiveIncomingCalls;\n        if (s.COMMPilot.trunkGroup.trunkCalls.totalActiveOutgoingCalls)  trunkTAOC = s.COMMPilot.trunkGroup.trunkCalls.totalActiveOutgoingCalls;\n    }\n    if (s.COMMPilot.trunkGroup.trunkRouting) {\n        if (s.COMMPilot.trunkGroup.trunkRouting.includeDtgIdentity) {\n            if (s.COMMPilot.trunkGroup.trunkRouting.includeDtgIdentity == 'true') trunkIDI = 'True'; else trunkIDI = 'False';\n        }\n        if (s.COMMPilot.trunkGroup.trunkRouting.enableNetworkAddressIdentity) {\n            if (s.COMMPilot.trunkGroup.trunkRouting.enableNetworkAddressIdentity == 'true') trunkENAI = 'True'; else trunkENAI = 'False';\n        }\n        if (s.COMMPilot.trunkGroup.trunkRouting.failureOptionsSendingIntervalSeconds) trunkFOSIS = s.COMMPilot.trunkGroup.trunkRouting.failureOptionsSendingIntervalSeconds;\n        if (s.COMMPilot.trunkGroup.trunkRouting.failureThresholdCounter)              trunkFTC   = s.COMMPilot.trunkGroup.trunkRouting.failureThresholdCounter;\n    }\n    if (s.COMMPilot.trunkGroup.trunkCLID) {\n        if (s.COMMPilot.trunkGroup.trunkCLID.includeTrunkGroupIdentityForNetworkCalls) {\n            if (s.COMMPilot.trunkGroup.trunkCLID.includeTrunkGroupIdentityForNetworkCalls == 'true') trunkITGIFNC = 'True'; else trunkITGIFNC = 'False';\n        }\n        if (s.COMMPilot.trunkGroup.trunkCLID.includeOtgIdentityForNetworkCalls) {\n            if (s.COMMPilot.trunkGroup.trunkCLID.includeOtgIdentityForNetworkCalls == 'true') trunkIOIFNC = 'True'; else trunkIOIFNC = 'False';\n        }\n        if (s.COMMPilot.trunkGroup.trunkCLID.pilotUserCallingLineIdentityForExternalCallsPolicy) trunkUCLIFECP = s.COMMPilot.trunkGroup.trunkCLID.pilotUserCallingLineIdentityForExternalCallsPolicy;\n        if (s.COMMPilot.trunkGroup.trunkCLID.pilotUserCallingLineAssertedIdentityPolicy)         trunkUCLAIP   = s.COMMPilot.trunkGroup.trunkCLID.pilotUserCallingLineAssertedIdentityPolicy;\n        if (s.COMMPilot.trunkGroup.trunkCLID.clidSourceForScreenedCallsPolicy)                   trunkCSFSCP   = s.COMMPilot.trunkGroup.trunkCLID.clidSourceForScreenedCallsPolicy;\n    }\n-%>\n<%= trunkHeader -%>\n<%= rowCreator.format('UserId',trunkPUI); -%>\n<%= rowCreator.format('RequireAuthentication',trunkRA); -%>\n<%= rowCreator.format('TrunkName',trunkAN); -%>\n<%= rowCreator.format('PilotUserChargeNumberPolicy',trunkPUCNP); -%>\n<%= rowCreator.format('ContinuousOptionsMessage',trunkSCOM); -%>\n<%= rowCreator.format('GroupState',trunkGS); -%>\n<%= rowCreator.format('DeviceName',trunkDN); -%>\n<%= rowCreator.format('DeviceLevel',trunkDL); -%>\n<%= rowCreator.format('MaxActiveCalls',trunkMAC); -%>\n<%= rowCreator.format('EnableBursting',trunkEB); -%>\n<%= rowCreator.format('TotalActiveIncomingCalls',trunkTAIC); -%>\n<%= rowCreator.format('TotalActiveOutingCalls',trunkTAOC); -%>\n<%= rowCreator.format('IncludeDigitalIdentity',trunkIDI); -%>\n<%= rowCreator.format('EnableNetworkAddressIdentity',trunkENAI); -%>\n<%= rowCreator.format('FailureOptionsSendingIntervalSecs',trunkFOSIS); -%>\n<%= rowCreator.format('failureThresholdCounter',trunkFTC); -%>\n<%= rowCreator.format('TrunkGrpIdentityForNetworkCalls',trunkITGIFNC); -%>\n<%= rowCreator.format('OtgIdentityForNetworkCalls',trunkIOIFNC); -%>\n<%= rowCreator.format('UserIdentityForExtCallsPolicy',trunkUCLIFECP); -%>\n<%= rowCreator.format('UserAssertedIdentityPolicy',trunkUCLAIP); -%>\n<%= rowCreator.format('CLIDSourceForScreenedCallsPolicy',trunkCSFSCP); -%>\n<%} -%>"}, {"name": "IPVoiceUserDetails", "active": true, "title": "Determine IPVoice User Registration Status and Details (Module)", "description": "Module to display IPVoice User Registration Status and Details", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nif (s.COMMPilot) {\nlet rowCreator = new RowCreator();\nconst COLUMN_GAP = 68;\n//\nlet customerProduct; let fullNNOld = ''; let fullNN; let oldConfigDevice = ''; let configDevice;\nlet commpilotPathOld = ''; let commpilotPath; let groupIntercept = ''; let userIntercept = ''; let userId; let messageSS = '';\n\nif (s.COMMPilot.user && s.COMMPilot.user.clusterType) {\n    customerProduct = s.COMMPilot.user.clusterType.toUpperCase().replace('STOA','BSIP');\n    if (s.CFNN && s.CFNN.FirstLine && s.CFNN.FirstLine.toUpperCase().includes('TOTI')) customerProduct += ' (TOTI)';\n}\nif (s.COMMPilot.user && s.COMMPilot.user.userId) {\n    var f3 = '';\n    if (s.COMMPilot.user.userType) f3 += ' ' + s.COMMPilot.user.userType;\n    var f1 = s.COMMPilot.user.userId;\n    var f2 = f1.indexOf('@');\n    fullNNOld += f1.substring(0,f2);\n    fullNNOld += f3;\n} else if (s.CFNN && s.CFNN.number) {\n    fullNNOld += s.CFNN.number;\n} else {\n    fullNNOld += ' ';\n}\nif (s.CFNN && s.CFNN.Details && s.CFNN.Details.toUpperCase().includes('ENTERPRISE TRUNK PILOT')) {\n    fullNNOld += '     Service Status: Enterp. Trunk';\n} else {\n    if      (s.COMMPilot.user && s.COMMPilot.user.serviceActivated && s.COMMPilot.user.serviceActivated == 'true') fullNNOld += '   Service Status: Activated    ';\n    else if (s.CFNN && s.CFNN.FirstLine && s.CFNN.FirstLine.toUpperCase().includes('NOT ACTIVATED'))               fullNNOld += '   Service Status: Inactivated  ';\n    else if (s.CFNN && s.CFNN.FirstLine && s.CFNN.FirstLine.toUpperCase().includes('ACTIVATED'))                   fullNNOld += '   Service Status: Activated#   ';\n    else                                                                                                           fullNNOld += '   Service Status: Inactivated  ';\n}\n// 0747206000 CP.user.userid NULL -> Unassigned\n// 0296947320,0884334252,0383478333 (AA,CC,HG) -> Virtual\n// 0884334151 CP.reg.regs.devicename = 'Mobile Intergration' -> Liberate\nif (s.COMMPilot.user && typeof s.COMMPilot.user.userId !== 'undefined' && s.COMMPilot.user.userId === '') {\n    fullNNOld += 'Unassigned';\n} else if (s.COMMPilot.user && s.COMMPilot.user.userType && (s.COMMPilot.user.userType == 'Auto Attendant' || s.COMMPilot.user.userType == 'Call Center' || s.COMMPilot.user.userType == 'Hunt Group')) {\n    fullNNOld += 'Virtual';\n} else if (s.COMMPilot.registration && s.COMMPilot.registration.registrations && s.COMMPilot.registration.registrations.deviceName && s.COMMPilot.registration.registrations.deviceName.toUpperCase().includes('MOBILE INTEGRATION')) {\n        fullNNOld += 'Liberate';\n} else if (s.COMMPilot.configuredDevice && s.COMMPilot.configuredDevice.trunk && s.COMMPilot.configuredDevice.trunk.enterpriseTrunk && s.COMMPilot.user && s.COMMPilot.user.groupId && s.COMMPilot.user.groupId.toUpperCase().includes('DID')) {\n    fullNNOld += 'Enterprise Trunk DID';\n} else if (s.COMMPilot.registration && s.COMMPilot.registration.status) {\n    fullNNOld += (s.COMMPilot.registration.status.charAt(0).toUpperCase() + s.COMMPilot.registration.status.slice(1));\n} else {\n    var sp = '';\n    if (s.COMMPilot.user && s.COMMPilot.user.serviceProviderId) sp += s.COMMPilot.user.serviceProviderId; else sp += 'XYZXYZXYZ';\n    if (s.CFNN && s.CFNN.registrations && s.CFNN.registrations[0] && s.CFNN.registrations[0]['Trunk FNN'] && s.CFNN.registrations[0]['Trunk FNN'].toUpperCase().includes('NO REGISTERED')) {\n        fullNNOld += 'Unregistered';\n    } else {\n        if (s.CFNN && s.CFNN.Cluster && s.CFNN.Cluster.includes(sp)) fullNNOld += 'Registered'; else fullNNOld += 'Unregistered';\n    }\n}\nif   (s.COMMPilot.groupIntercept && s.COMMPilot.groupIntercept.active && s.COMMPilot.groupIntercept.active == 'true') fullNN = fullNNOld.padEnd(COLUMN_GAP) + ' Group Intercept: ON'; else fullNN = fullNNOld.padEnd(COLUMN_GAP) + ' Group Intercept: OFF';\n\nif (s.COMMPilot.user) {\n    if (s.COMMPilot.user.cluster)                                                                                commpilotPathOld += 'Cluster ' + s.COMMPilot.user.cluster;\n    if (s.COMMPilot.user.serviceProviderId)                                                                      commpilotPathOld += '  -  '    + s.COMMPilot.user.serviceProviderId;\n    if (s.COMMPilot.user.groupId)                                                                                commpilotPathOld += '  -  '    + s.COMMPilot.user.groupId;\n    if (s.COMMPilot.groupTrunkGroups && s.COMMPilot.groupTrunkGroups[0] && s.COMMPilot.groupTrunkGroups[0].name) commpilotPathOld += '  -  '    + s.COMMPilot.groupTrunkGroups[0].name;\n}\nif   (s.COMMPilot.userIntercept && s.COMMPilot.userIntercept.active && s.COMMPilot.userIntercept.active == 'true') commpilotPath = commpilotPathOld.padEnd(COLUMN_GAP) + ' User Intercept:  ON'; else commpilotPath = commpilotPathOld.padEnd(COLUMN_GAP) + ' User Intercept:  OFF';\nif (s.COMMPilot.configuredDevice && s.COMMPilot.configuredDevice.feature) {\n    if (s.COMMPilot.configuredDevice.feature == 'enabled') oldConfigDevice = 'ON '; else oldConfigDevice = 'OFF ';\n    if (s.COMMPilot.configuredDevice.feature == 'enabled' && s.COMMPilot.configuredDevice.deviceLevel) configDevice = oldConfigDevice.padEnd(COLUMN_GAP) + ' Level: ' + s.COMMPilot.configuredDevice.deviceLevel; else configDevice = oldConfigDevice;\n}\n\n\nif      (s.COMMPilot.user && s.COMMPilot.user.userId) userId = s.COMMPilot.user.userId;\nelse if (s.CFNN && s.CFNN.Details)                    userId = s.CFNN.Details;\nif (s.CFNN && s.CFNN.Details && s.CFNN.Details.toUpperCase().includes('ENTERPRISE TRUNK PILOT')) { null; }\nelse { if      (s.COMMPilot.user && s.COMMPilot.user.serviceActivated && s.COMMPilot.user.serviceActivated == 'true') { null; }\n       else if (s.CFNN && s.CFNN.FirstLine && s.CFNN.FirstLine.toUpperCase().includes('NOT ACTIVATED'))               { null; }\n       else if (s.CFNN && s.CFNN.FirstLine && s.CFNN.FirstLine.toUpperCase().includes('ACTIVATED'))                   { messageSS += '#Service Status is from CFNN as it is missing in COMMPilot\\n'; }\n}\n-%>\n<%= rowCreator.format('Product',customerProduct); -%>\n<%= rowCreator.format('Full National Number',fullNN); -%>\n<%= rowCreator.format('CommPilot Path',commpilotPath); -%>\n<%= rowCreator.format('Configured Device',configDevice); -%>\n<%= rowCreator.format('UserId',userId); -%>\n<%= messageSS -%>\n<%} -%>"}, {"name": "IPVoiceUserInfo", "active": true, "title": "Display IPVoice User Info (Module)", "description": "Module to Display IPVoice User Info", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nif (s.COMMPilot && s.COMMPilot.userInfo) {\n    let rowCreator = new RowCreator();\n    let uiHeader; let userType; let userFName; let userLName; let clidFName; let clidLName; let userDAlias; let userExtension; let userTimeZone; let userTZDetail;\n\n    uiHeader = '\\nUserInfo\\n--------\\n';\n    if (s.COMMPilot.userInfo.isType)                 userType      = s.COMMPilot.userInfo.isType;\n    if (s.COMMPilot.userInfo.firstName)              userFName     = s.COMMPilot.userInfo.firstName;\n    if (s.COMMPilot.userInfo.lastName)               userLName     = s.COMMPilot.userInfo.lastName;\n    if (s.COMMPilot.userInfo.callingLineIdFirstName) clidFName     = s.COMMPilot.userInfo.callingLineIdFirstName;\n    if (s.COMMPilot.userInfo.callingLineIdLastName)  clidLName     = s.COMMPilot.userInfo.callingLineIdLastName;\n    if (s.COMMPilot.userInfo.defaultAlias)           userDAlias    = s.COMMPilot.userInfo.defaultAlias;\n    if (s.COMMPilot.userInfo.extension)              userExtension = s.COMMPilot.userInfo.extension;\n    if (s.COMMPilot.userInfo.timeZone)               userTimeZone  = s.COMMPilot.userInfo.timeZone;\n    if (s.COMMPilot.userInfo.timeZoneDisplayName)    userTZDetail  = s.COMMPilot.userInfo.timeZoneDisplayName;\n-%>\n<%= uiHeader -%>\n<%= rowCreator.format('Type',userType); -%>\n<%= rowCreator.format('FirstName',userFName); -%>\n<%= rowCreator.format('LastName',userLName); -%>\n<%= rowCreator.format('CLID FirstName',clidFName); -%>\n<%= rowCreator.format('CLID LastName',clidLName); -%>\n<%= rowCreator.format('DefaultAlias',userDAlias); -%>\n<%= rowCreator.format('Extension',userExtension); -%>\n<%= rowCreator.format('TimeZone',userTimeZone); -%>\n<%= rowCreator.format('TimeZoneDetails',userTZDetail); -%>\n<%} -%>"}, {"name": "LatestServiceCentralIncidentModule", "active": true, "title": "Find the latest Service Central Incident and display (Module)", "description": "Display the SNI and Create Date of the latest Service Central Incidence", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<% let serviceCentralIncidentDetails = [];\nlet allIncidents = [s['Hangar - Service central incident API']];\nvar oneWeekAgo = new Date();\noneWeekAgo.setDate(oneWeekAgo.getDate() - 7);\nallIncidents.forEach((serviceCentralIncs) => {\n    for (let serviceCentralId in serviceCentralIncs) {\n        let incident = serviceCentralIncs[serviceCentralId];\n    \n        //if (incident && typeof incident === 'object' && (incident.active !== '1' || (incident.u_status_reason==='Confirmed' && incident.incident_state_dv === 'Resolved'))) {\n        if (incident && typeof incident === 'object') {\n            let incOpenAt = Date.parse(incident.opened_at);\n            if (isNaN(incOpenAt)) incOpenAt = 0;\n            if (incOpenAt > oneWeekAgo) {\n                serviceCentralIncidentDetails.push({\n                    id: serviceCentralId,\n                    openedAt: incident.opened_at,\n                    statusReason: incident.u_status_reason,\n                    customerReference: incident.u_customer_reference,\n                    state: incident.incident_state_dv,\n                    fnn: data.fnn,\n                    closedAt: incident.closed_at\n                });\n           }\n        }\n    }\n});\n\nserviceCentralIncidentDetails.sort((case1, case2) => {\n    let timestamp1 = Date.parse(case1.openedAt);\n    let timestamp2 = Date.parse(case2.openedAt);\n    if (isNaN(timestamp1)) timestamp1 = 0;\n    if (isNaN(timestamp2)) timestamp2 = 0;\n    return timestamp2 - timestamp1;\n});\n\nif (serviceCentralIncidentDetails.length > 0) {\n    let mostRecentIncident = serviceCentralIncidentDetails.slice(0, 1);\nif (mostRecentIncident[0].id) { %>NAS NBNCO IPSC Ops <%= mostRecentIncident[0].id -%>\n<% }} else { -%>NAS NBNCO IPSC Ops No SNI %><% } %>\n\n"}, {"name": "MDNFieldTask", "active": true, "title": "MDN Field Task (Text)", "description": "MDN Field Task template", "allowedSystemsToAppend": ["ServiceCentral"], "suite": [], "templateType": "Service Check", "formatType": "Text", "listCondition": "r.MDR009 && r.MDR009.manageCategory === 'MDN';", "defaultPriority": 0, "template": "<% if (r.MDR009 && r.MDR009.manageCategory === 'MDN') {\nlet rowCreator = new RowCreator();\n-%>\n<% if (data.serviceType === 'QQ') { -%>\n<%= rowCreator.format(\"MWAN Device ID\", data.deviceName ? data.deviceName : \"Not found\"); -%>\n\n1 > CHECK THE HWIC CARD ON THE ROUTER.\n2 > RESEAT THE SIM AND CHECK IF THE ANTENNA IS PLUGGED IN PROPERLY.\n3 > PLEASE CHECK ANTENNA AS ITS POSSIBLE IT HAS BEEN INSTALLED INCORRECTLY, M0 MUST HAVE PADDLE WHILE M1 MUST HAVE EXTENSION TO PADDLE AT 15 INCHES APART \n4 > IF REQUIRED CHANGE THE POSITION OF THE ROUTER FOR A BETTER RSSI.\n5 > ******* FOR RSSI. REMOVE THE SIM CARD FROM THE ROUTER AND CHECK FOR RECEPTION BY INSERTING THE SIM IN YOUR MOBILE PHONE. CHECK THE BARS ON THE RECEPTION ****\n6 > CHECK FOR CABLING AND OTHER ISSUES IF ANY.\n7 > PLEASE CARRY A LAPTOP , A CONSOLE CABLE AND A FULLY CHARGED JDSU/SMARTCLASS TO THE SITE AS WELL.\n<% } else { -%>\n<%- renderTemplate(\"SiteDetailsModule\", { showAddress: true }); %>\n<%- renderTemplate(\"MDNFieldTaskInfoModule\"); %>\n\nThis is a Telstra Managed service. Fault is suspected on the Managed Device.\nCT please perform the following:\n\n1. Bring laptop, nextg vpn access, cisco console cable, ethernet cables to confirm or restore managed router/switch configuration for MWAN service;\n2. Ensure cables are seated ok into the router.\n3. Recheck Power to router (customer should have performed these checks)\n4. Assist customer with any interconnection port issues into any NTU.\n<% } -%>\n\nMDN to Field Template\n<% } -%>"}, {"name": "MDNFieldTaskInfoModule", "active": true, "title": "MDN Field Task Info (Module)", "description": "Module to display managed device name, device type and backup status", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nlet rowCreator = new RowCreator();\n-%>\n<% if (r.MDR009 && r.MDR009.manageCategory === \"MDN\") { -%>\n<%= rowCreator.format(\"MWAN Device ID\", data.deviceName ? data.deviceName : \"Not found\"); -%>\n<%= rowCreator.format(\"Device Type\", r.MDR118 && r.MDR118.deviceTypeName ? r.MDR118.deviceTypeName : \"Not found\"); -%>\n<%= rowCreator.format(\"Has Backup (3G / 4G)\", r.MDR177 ? (r.MDR177.interfaces && r.MDR177.interfaces.length ? \"Yes\" : \"No\") : \"Unknown\"); -%>\n<% } else { -%>\n<%= rowCreator.format(\"MWAN Device ID\", \"No Managed Device Found\"); -%>\n<% } -%>"}, {"name": "MDNInfoModule", "active": true, "title": "MDN Info (Module)", "description": "Module to display MDN information from MAGPIE", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nlet rowCreator = new RowCreator();\n\nlet magpieMDNData = s.MAGPIE && s.MAGPIE.rawData && s.MAGPIE.rawData.MDN ? s.MAGPIE.rawData.MDN : null;\n\nlet magpieProductData = null;\nlet magpieProductNetworkFNN = null;\n\nif (s.MAGPIE && s.MAGPIE.rawData && typeof s.MAGPIE.rawData === 'object') {\n    for (let rawDataKey in s.MAGPIE.rawData) {\n        // Looks for an object in rawData with the \"Product Name\" key\n        if (s.MAGPIE.rawData[rawDataKey] && s.MAGPIE.rawData[rawDataKey]['Product Name']) {\n            magpieProductData = s.MAGPIE.rawData[rawDataKey];\n            magpieProductNetworkFNN = s.MAGPIE.rawData[rawDataKey][`Member of ${rawDataKey} Network`];\n            break;\n        }\n    }\n}\n\nif (!magpieProductData && s.MAGPIEBATTAAA && s.MAGPIEBATTAAA.rawData && typeof s.MAGPIEBATTAAA.rawData === 'object') {\n    for (let rawDataKey in s.MAGPIEBATTAAA.rawData) {\n        // Looks for an object in rawData with the \"Product Name\" key\n        if (s.MAGPIEBATTAAA.rawData[rawDataKey] && s.MAGPIEBATTAAA.rawData[rawDataKey]['Product Name']) {\n            magpieProductData = s.MAGPIEBATTAAA.rawData[rawDataKey];\n            magpieProductNetworkFNN = s.MAGPIEBATTAAA.rawData[rawDataKey][`Member of ${rawDataKey} Network`];\n            break;\n        }\n    }\n}\n-%>\n<% if (magpieMDNData) { -%>\n--MDN Details--\n<%= rowCreator.format(\"MDN Network FNN\", magpieMDNData[\"Member of MDN Network\"] ? magpieMDNData[\"Member of MDN Network\"] : \"Not found\"); -%>\n<%= rowCreator.format(\"MDN Service\", magpieMDNData[\"MDN FNN\"] ? magpieMDNData[\"MDN FNN\"] : magpieMDNData[\"SD WAN FNN\"] ? (magpieMDNData[\"SD WAN FNN\"] + \" (SD-WAN FNN)\") : \"Not found\"); -%>\n<%= rowCreator.format(\"Device Name\", data.deviceName ? data.deviceName : \"Not found\"); -%>\n<%= rowCreator.format(\"Customer Code\", r.MDR118 && r.MDR118.customerCode ? r.MDR118.customerCode : \"Not found\"); -%>\n<%= rowCreator.format(\"Device Type\", r.MDR118 && r.MDR118.deviceType ? `${r.MDR118.deviceType} (${r.MDR118.deviceTypeName})` : \"Not found\"); -%>\n<%= rowCreator.format(\"Supplier Code\", r.MDR118 && r.MDR118.supplierCode ? `${r.MDR118.supplierCode} (${r.MDR118.supplierCodeName})` : \"Not found\"); -%>\n<%= rowCreator.format(\"Product Code\", r.MDR118 && r.MDR118.productCode ? r.MDR118.productCode : \"Not found\"); -%>\n<%= rowCreator.format(\"Device Serial Number\", magpieMDNData[\"Serial Number (SNMP)\"] ? magpieMDNData[\"Serial Number (SNMP)\"] : \"Not found\"); -%>\n<% if (showCarriageSectionHeader) { -%>\n\n--Carriage--\n<% } -%>\n<%= rowCreator.format(\"Carriage Type\", data.carriageType ? data.carriageType : \"Not found\"); -%>\n<%= rowCreator.format(\"Carriage FNN\", data.carriageFNN ? data.carriageFNN : \"Not found\"); -%>\n<%= rowCreator.format(\"Backup\", r.MDR177 && Array.isArray(r.MDR177.interfaces) ? (r.MDR177.interfaces.length ? \"Yes\" : \"No\") : \"Unknown\"); -%>\n<%= rowCreator.format(\"Backup / Alternate Access FNN\", r.MDR177 && r.MDR177.fnns ? r.MDR177.fnns.join(', ') : \"Not found\"); -%>\n<%= rowCreator.format(\"Network FNN\", magpieProductNetworkFNN ? magpieProductNetworkFNN : \"Not found\"); -%>\n<%= rowCreator.format(\"CIDN\", data.CIDN ? data.CIDN : \"Not found\"); -%>\n<%= rowCreator.format(\"Customer\", magpieMDNData.Customer ? magpieMDNData.Customer : \"Not found\"); -%>\n<%= rowCreator.format(\"Location\", data.address ? data.address : \"Not found\"); -%>\n<%= rowCreator.format(\"CE IP Address\", magpieProductData && magpieProductData[\"CE IP address\"] ? magpieProductData[\"CE IP address\"] : \"Not found\"); -%>\n<%= rowCreator.format(\"Routing Protocol\", magpieProductData && magpieProductData[\"Routing Protocol\"] ? magpieProductData[\"Routing Protocol\"] : \"Not found\"); -%>\n<%= rowCreator.format(\"Service Speed\", magpieProductData && magpieProductData[\"Service Speed\"] ? magpieProductData[\"Service Speed\"] : \"Not found\"); -%>\n<%= rowCreator.format(\"Basement Switch\", magpieProductData && magpieProductData[\"Basement switch\"] ? magpieProductData[\"Basement switch\"] : \"Not found\"); -%>\n<% } -%>"}, {"name": "MDNMerakiDeviceText", "active": true, "title": "MDN Merak<PERSON> (Text)", "description": "MDN Meraki Device information", "allowedSystemsToAppend": ["ServiceCentral"], "suite": [], "templateType": "Service Check", "formatType": "Text", "listCondition": "r.MDR009 && r.MDR009.manageCategory === 'MDN' && r.MDR118 && r.MDR118.supplierCodeName === 'Meraki';", "defaultPriority": 1, "template": "<%\nlet rowCreator = new RowCreator();\nlet merakiBatIPV4 = s.MerakiBatIPV4 && s.MerakiBatIPV4.session && s.MerakiBatIPV4.session.Session_Info && s.MerakiBatIPV4.session.Session_Info.USER_NAME ? s.MerakiBatIPV4.session.Session_Info.USER_NAME : null;\nlet magpieMDNData = s.MAGPIE && s.MAGPIE.rawData && s.MAGPIE.rawData.MDN ? s.MAGPIE.rawData.MDN : null;\nlet merakiWifiDevice = s.MerakiWifi && s.MerakiWifi.deviceStatus && s.MerakiWifi.deviceStatus.response && typeof s.MerakiWifi.deviceStatus.response.device === 'object' ? s.MerakiWifi.deviceStatus.response.device : null;\nlet merakiWifiNetwork = s.MerakiWifi && s.MerakiWifi.deviceStatus && s.MerakiWifi.deviceStatus.response && typeof s.MerakiWifi.deviceStatus.response.network === 'object' ? s.MerakiWifi.deviceStatus.response.network : null;\nlet failedRules = [];\nfor (let ruleName in r) {\n    if (r[ruleName] && r[ruleName].result === 'Failed') {\n        failedRules.push(ruleName);\n    }\n}\n\n\n-%>\n<% if (magpieMDNData) { -%>\n--Customer details--\n<%= rowCreator.format(\"MDN Network\", magpieMDNData[\"Member of MDN Network\"]); -%>\n<%= rowCreator.format(\"MDN Port\", magpieMDNData[\"MDN FNN\"]); -%>\n<%= rowCreator.format(\"ODIN Status\", magpieMDNData[\"Odin status\"]); -%>\n<%= rowCreator.format(\"CIDN\", magpieMDNData[\"CIDN\"]); -%>\n<% } -%>\n<% if (merakiBatIPV4) { -%>\n<%= rowCreator.format(\"TBB FNN\", merakiBatIPV4); -%>\n<% } -%>\n<% if (magpieMDNData) { -%>\n<%= rowCreator.format(\"Customer\", magpieMDNData[\"Customer\"]); -%>\n<%= rowCreator.format(\"Location\", data.address); -%>\n\n<% if (data.carriageType === 'NBN') { -%>\n<%- renderTemplate(\"NBNServiceHealthModule\"); -%>\n<% } -%>\n<% if (r.MDR118 && r.MDR118.result === 'OK') { -%>\n--Device Details--\n<%= rowCreator.format(\"Device Name\", data.deviceName); -%>\n<%= rowCreator.format(\"Customer Code\", r.MDR118.customerCode); -%>\n<%= rowCreator.format(\"Device Type\", `${r.MDR118.deviceType} (${r.MDR118.deviceTypeName})`); -%>\n<%= rowCreator.format(\"Supplier Code\", `${r.MDR118.supplierCode} (${r.MDR118.supplierCodeName})`); -%>\n<%= rowCreator.format(\"Product Code\", r.MDR118.productCode); -%>\n\n<% } -%>\n<% if (merakiWifiDevice) { -%>\n<%= rowCreator.format(\"Ping\", merakiWifiDevice.ping); -%>\n<%= rowCreator.format(\"Network Online\", merakiWifiDevice.network_online); -%>\n<%= rowCreator.format(\"Portal Status\", merakiWifiDevice.status); -%>\n<%= rowCreator.format(\"Last Reported At (UTC)\", merakiWifiDevice.lastReportedAt); -%>\n<%= rowCreator.format(\"Public IP (BAT)\", merakiWifiDevice.publicIp); -%>\n<%= rowCreator.format(\"Network ID\", merakiWifiDevice.networkId); -%>\n<%= rowCreator.format(\"Carriage AVC (BAT)\", data.carriageFNN); -%>\n<%= rowCreator.format(\"Devices Online\", merakiWifiNetwork && merakiWifiNetwork.devices_online ? merakiWifiNetwork.devices_online : null); -%>\n<%= rowCreator.format(\"Devices Total\", merakiWifiNetwork && merakiWifiNetwork.devices_total ? merakiWifiNetwork.devices_total : null); -%>\n\n<% } -%>\n<%- renderTemplate(\"TicketingCasesModule\"); %>\nMerge Result Link: <%= mergeUrl %>/serviceCheck/view/html/<%= data.id %>\n<% } else { -%>\nService check for fnn <%= data.fnn %> does not contain MDN data\n<% } %>"}, {"name": "MDNSummaryText", "active": true, "title": "MDN Summary (Text)", "description": "MDN Summary template", "allowedSystemsToAppend": ["ServiceCentral"], "suite": [], "templateType": "Service Check", "formatType": "Text", "listCondition": "r.MDR009 && r.MDR009.manageCategory === 'MDN';", "defaultPriority": 0, "template": "<% if (r.MDR009 && r.MDR009.manageCategory === 'MDN') { -%>\nMDN Summary\n\n<%- renderTemplate(\"MDNInfoModule\", { showCarriageSectionHeader: true }); %>\n<%- renderTemplate(\"DeviceDetailsModule\", { forMDN: true, RASSPSourceName: \"RASSP\" }); %>\n<%- renderTemplate(\"TicketingCasesModule\"); %>\n<%- renderTemplate(\"RassOrdersModule\", { showAllOrders: false, includeCancelledOrders: true }); %>\n<%- renderTemplate(\"OutagesModule\"); %>\n<%- renderTemplate(\"DeviceStatusModule\"); %>\n<%- renderTemplate(\"AccessStatusModule\"); %>\n<%- renderTemplate(\"AccessInterfaceThroughputModule\", { interfaceMetrics: [\"IQD\", \"OQD\", \"RXBS\", \"TXBS\"] }); %>\n<%- renderTemplate(\"PingResultsModule\", { includeCMIPing: true, includeVPNPing: true, includeVPNPingFullMTU: true }); %>\n<% if (data.carriageType === 'BDSL') {\nlet RASSPSourceName = null;\nif (s.RASSP && s.RASSP.FNN === data.carriageFNN) {\n    RASSPSourceName = \"RASSP\";\n} else if (s[\"RASSP-C\"] && s[\"RASSP-C\"].FNN === data.carriageFNN) {\n    RASSPSourceName = \"RASSP-C\";\n}\n-%>\n<%- renderTemplate(\"CustomerDetailsModule\", { RASSPSourceName: RASSPSourceName }); %>\n<%- renderTemplate(\"DeviceDetailsModule\", { forMDN: false, RASSPSourceName: RASSPSourceName }); %>\n<%- renderTemplate(\"GMACSInfoModule\", { showSHDSLCheck: true }); %>\n<%- renderTemplate(\"RASSPBearerAndEquipmentModule\", { RASSPSourceName: RASSPSourceName }); %>\n<% } else if (data.carriageType === 'NBN') { -%>\n<%- renderTemplate(\"CustomerDetailsModule\", { RASSPSourceName: \"RASSP\" }); %>\n<%- renderTemplate(\"DeviceDetailsModule\", { forMDN: false, RASSPSourceName: \"RASSP\" }); %>\n<%- renderTemplate(\"NBNServiceHealthModule\"); -%>\n<% } -%>\n<%- renderTemplate(\"BrunoandFluffyModule\"); -%>\n\n<%- renderTemplate(\"OutcomesModule\"); %>\n<%- renderTemplate(\"MessageBucketModule\"); %>\nMerge Result Link: <%= mergeUrl %>/serviceCheck/view/html/<%= data.id %>\n<% } else { -%>\nRecord is not for a MDN service\n<% } -%>"}, {"name": "MessageBucketModule", "active": true, "title": "Message Bucket (Module)", "description": "Shows messages in the current message bucket for the service check record", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\n// Basic processing of message strings to split into lines to not overflow in the template\n// May be improved upon in the future\nfunction splitStringToLines(messageString, messageArray) {\n    if (!messageString) {\n        return;\n    }\n\n    const CHAR_LIMIT = 100;\n\n    let currLine = \"\";\n    let words = messageString.split(/ |(?<=[ \\n])/);\n    for (let word of words) {\n        if (word.includes(\"\\n\")) {\n            currLine += word;\n            messageArray.push(currLine.trim());\n            currLine = \"\";\n        } else {\n            currLine += word + \" \";\n            if (currLine.trim().length > CHAR_LIMIT) {\n                messageArray.push(currLine.trim());\n                currLine = \"\";\n            }\n        }\n    }\n\n    if (currLine) {\n        messageArray.push(currLine.trim());\n    }\n}\n\nconst FRONT_OF_HOUSE_MESSAGE_DEFAULT = \"All test results were within specification, check customer equipment.\";\nlet messageLinesFrontOfHouse = [];\nlet messageLinesCustomer = [];\n\nlet messageFrontOfHouse = null;\nlet messageCustomer = null;\n\nif (data.messageBucket) {\n    messageFrontOfHouse = data.messageBucket.messageFrontOfHouse;\n    messageCustomer = data.messageBucket.messageCustomer\n} else {\n    messageFrontOfHouse = FRONT_OF_HOUSE_MESSAGE_DEFAULT;\n}\n\nsplitStringToLines(messageFrontOfHouse, messageLinesFrontOfHouse);\nsplitStringToLines(messageCustomer, messageLinesCustomer);\n-%>\n<% if (messageLinesFrontOfHouse.length) { -%>\n--Front of House Actions--\n<% for (let line of messageLinesFrontOfHouse) { -%>\n<%= line; %>\n<% } -%>\n<% } -%>\n\n<% if (messageLinesCustomer.length) { -%>\n--Customer Actions--\n<% for (let line of messageLinesCustomer) { -%>\n<%= line; %>\n<% } -%>\n<% } -%>"}, {"name": "ModiniDetails", "active": true, "title": "<PERSON><PERSON><PERSON> (Module)", "description": "Module to display Modini Det<PERSON>", "allowedSystemsToAppend": ["SIIAM"], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nif (s['MODINI'] && s['MODINI'].hasOwnProperty('APNs')) {\n    let rowCreator = new RowCreator();\n    let modiniHeader = 'Modini\\n------\\n'; let modiniBody = data.fnn; let modiniSS; let modiniSS2; let modiniAA; let modiniNS;\n    \n    if (s['MODINI'].hasOwnProperty('SIM Serial No'))  modiniSS = s.MODINI['SIM Serial No'];               else modiniSS  = 'Not found';\n    if (s['MODINI'].hasOwnProperty('Service Status')) modiniSS2 = s.MODINI['Service Status'];             else modiniSS2 = 'Not found';\n    if (s['MODINI'].hasOwnProperty('APNs'))           modiniAA = Object.keys(s.MODINI['APNs']).join(','); else modiniAA  = 'Not found';\n    if (s['MODINI'].hasOwnProperty('Network Details') && s.MODINI['Network Details']['Network status:']) modiniNS = s.MODINI['Network Details']['Network status:']; else modiniNS = 'Not found';\n-%>\n<%= modiniHeader -%>\n<%= rowCreator.format('Mobile Number',modiniBody); -%>\n<%= rowCreator.format('SIM Serial',modiniSS); -%>\n<%= rowCreator.format('Service Status',modiniSS2); -%>\n<%= rowCreator.format('Assigned APNs',modiniAA); -%>\n<%= rowCreator.format('Network status',modiniNS); -%>\n<%} -%>"}, {"name": "NBNEnterpriseEthernetDiagnosticsSummaryText", "active": true, "title": "NBN Enterprise Ethernet Diagnostics Summary (Text)", "description": "Summary of all NBN Enterprise Ethernet diagnostics", "allowedSystemsToAppend": [], "suite": [], "templateType": "Service Check", "formatType": "Text", "listCondition": "data.carriageType === 'NBN EE';", "defaultPriority": 0, "template": "<% if (data.carriageType === \"NBN EE\") {\nlet rowCreator = new RowCreator();\nlet bpiID = s.NBNProductInventory && Array.isArray(s.NBNProductInventory) &&\n            s.NBNProductInventory.length > 0 && s.NBNProductInventory[0].id ? s.NBNProductInventory[0].id : \"Unknown\";\n-%>\nNBN Enterprise Ethernet Diagnostics Summary\n\nBTD Status:\n<% if ((r && r.MDR149) && (s && s.NBNbtdstatusEE)) { -%>\n<%= rowCreator.format(\"Test ID\", s.NBNbtdstatusEE && s.NBNbtdstatusEE.event && s.NBNbtdstatusEE.event.id ? s.NBNbtdstatusEE.event.id : \"Unknown\"); -%>\n<%= rowCreator.format(\"Test Status\", s.NBNbtdstatusEE && s.NBNbtdstatusEE.event && s.NBNbtdstatusEE.event.status ? s.NBNbtdstatusEE.event.status : \"Unknown\"); -%>\n<%= rowCreator.format(\"BPI ID\", bpiID); -%>\n<%= rowCreator.format(\"OVC ID\", s.NBNbtdstatusEE && s.NBNbtdstatusEE.event &&\n                               s.NBNbtdstatusEE.event.serviceTest && s.NBNbtdstatusEE.event.serviceTest.serviceRef &&\n                               s.NBNbtdstatusEE.event.serviceTest.serviceRef.id ?\n                               s.NBNbtdstatusEE.event.serviceTest.serviceRef.id : \"Unknown\"); -%>\n<%= rowCreator.format(\"Errors\", r.MDR149.diagnosticErrors ? r.MDR149.diagnosticErrors : null); -%>\n\nService Test Results\n<%= rowCreator.format(\"BTD Status\", r.MDR149.diagnosticStatus); -%>\n<%= rowCreator.format(\"BTD Status Result\", r.MDR149.diagnosticResult); -%>\n<%= rowCreator.format(\"BTD Operational State\", r.MDR149.indicatorOperationalState); -%>\n<%= rowCreator.format(\"BTD Last Change\", r.MDR149.indicatorLastChangeTime); -%>\n<% } else { -%>\nDiagnostics results not found\n<% } -%>\n\nLoopback test:\n<% if (r && r.MAR015 && r.MAR015.action && r.MAR015.action.response) {\n    let response = r.MAR015.action.response;\n    let serviceTestResults = response && response.event &&\n                             response.event.serviceTest && response.event.serviceTest.serviceTestResults &&\n                             Array.isArray(response.event.serviceTest.serviceTestResults) ?\n                             response.event.serviceTest.serviceTestResults : [];\n\n    let serviceTestResultsLoopbackTest = serviceTestResults.find(result => { return result.type === \"Loopback Test\"; });\n-%>\n<%= rowCreator.format(\"Test ID\", response && response.event &&\n                                response.event.id ? \n                                response.event.id : \"Unknown\"); -%>\n<%= rowCreator.format(\"Test Status\", response && response.event &&\n                                    response.event.status ? \n                                    response.event.status : \"Unknown\"); -%>\n<%= rowCreator.format(\"BPI ID\", bpiID); -%>\n<%= rowCreator.format(\"OVC ID\", response && response.event &&\n                               response.event.serviceTest && response.event.serviceTest.serviceRef &&\n                               response.event.serviceTest.serviceRef.id ?\n                               response.event.serviceTest.serviceRef.id : \"Unknown\"); -%>\n<%= rowCreator.format(\"Errors\", serviceTestResultsLoopbackTest && serviceTestResultsLoopbackTest.errors &&\n                               Array.isArray(serviceTestResultsLoopbackTest.errors) ? \n                               serviceTestResultsLoopbackTest.errors.map(error => `${error.code}: ${error.reason}`) : null); -%>\n<% if (serviceTestResultsLoopbackTest) { -%>\n\nService Test Results\n<%= rowCreator.format(\"Loopback Status\", serviceTestResultsLoopbackTest.status ? serviceTestResultsLoopbackTest.status : \"Unknown\"); -%>\n<%= rowCreator.format(\"Loopback Status Result\", serviceTestResultsLoopbackTest.result ? serviceTestResultsLoopbackTest.result : \"Unknown\"); -%>\n<% } -%>\n<% } else { -%>\nDiagnostics results not found\n<% } -%>\n\nNPT Statistical:\n<% if (r && r.MAR017 && r.MAR017.action && r.MAR017.action.response) {\n    let response = r.MAR017.action.response;\n    let serviceTestResults = response && response.event &&\n                             response.event.serviceTest && response.event.serviceTest.serviceTestResults &&\n                             Array.isArray(response.event.serviceTest.serviceTestResults) ?\n                             response.event.serviceTest.serviceTestResults : [];\n\n    let serviceTestResultsNptStatistical = serviceTestResults.find(result => { return result.type === \"NPT Statistical\"; });\n-%>\n<%= rowCreator.format(\"Test ID\", response && response.event &&\n                                response.event.id ? \n                                response.event.id : \"Unknown\"); -%>\n<%= rowCreator.format(\"Test Status\", response && response.event &&\n                                    response.event.status ? \n                                    response.event.status : \"Unknown\"); -%>\n<%= rowCreator.format(\"BPI ID\", bpiID); -%>\n<%= rowCreator.format(\"OVC ID\", response && response.event &&\n                               response.event.serviceTest && response.event.serviceTest.serviceRef &&\n                               response.event.serviceTest.serviceRef.id ?\n                               response.event.serviceTest.serviceRef.id : \"Unknown\"); -%>\n<%= rowCreator.format(\"Errors\", serviceTestResultsNptStatistical && serviceTestResultsNptStatistical.errors &&\n                               Array.isArray(serviceTestResultsNptStatistical.errors) ? \n                               serviceTestResultsNptStatistical.errors.map(error => `${error.code}: ${error.reason}`) : null); -%>\n<% if (serviceTestResultsNptStatistical) { \n    let nptTestMeasure = Array.isArray(serviceTestResultsNptStatistical.testMeasure) &&\n                         serviceTestResultsNptStatistical.testMeasure[0] ?\n                         serviceTestResultsNptStatistical.testMeasure[0] : {};\n    let nptServiceDetails = {};\n    let nptServiceDetailsMeasurements = Array.isArray(nptTestMeasure.serviceDetails) ? nptTestMeasure.serviceDetails : [];\n    for (let i = 0; i < nptServiceDetailsMeasurements.length; i++) {\n        let measurement = nptServiceDetailsMeasurements[i];\n        if (measurement && measurement.id) {\n            if (measurement.unit) {\n                if (measurement.value !== \"\") {\n                    nptServiceDetails[measurement.id] = `${measurement.value} ${measurement.unit}`;\n                } else {\n                    nptServiceDetails[measurement.id] = \"N/A\";\n                }\n            } else {\n                nptServiceDetails[measurement.id] = measurement.value;\n            }\n        }\n    }\n    \n    let cosProfile = nptTestMeasure.cosProfile ? nptTestMeasure.cosProfile : {};\n    let cosProfileMeasurementsArray = cosProfile && cosProfile.measurements && Array.isArray(cosProfile.measurements) ? cosProfile.measurements : [];\n    let cosProfileMeasurements = {};\n    for (let i = 0; i < cosProfileMeasurementsArray.length; i++) {\n        let measurement = cosProfileMeasurementsArray[i];\n        if (measurement && measurement.id) {\n            cosProfileMeasurements[measurement.id] = measurement.value;\n        }\n    }\n-%>\nService Test Results\n<%= rowCreator.format(\"NPT Statistical Status\", serviceTestResultsNptStatistical.status ? serviceTestResultsNptStatistical.status : \"Unknown\"); -%>\n<%= rowCreator.format(\"NPT Statistical Status Result\", serviceTestResultsNptStatistical.result ? serviceTestResultsNptStatistical.result : \"Unknown\"); -%>\n\nService Details\n<%= rowCreator.format(\"Class of service (Low)\", nptServiceDetails.Low); -%>\n<%= rowCreator.format(\"Class of service (Medium)\", nptServiceDetails.Medium); -%>\n<%= rowCreator.format(\"Class of service (High)\", nptServiceDetails.High); -%>\n<%= rowCreator.format(\"Route Type\", nptServiceDetails[\"Route Type\"]); -%>\nClass of Service Profile\n<%= rowCreator.format(\"Result\", cosProfile.result); -%>\n<%= rowCreator.format(\"Profile\", cosProfile.profile); -%>\n<%= rowCreator.format(\"Completed\", cosProfile.completed); -%>\n<%= rowCreator.format(\"Frame Delay Status\", cosProfileMeasurements[\"Frame Delay Status\"]); -%>\n<%= rowCreator.format(\"Frame Loss Status\", cosProfileMeasurements[\"Frame Loss Status\"]); -%>\n<%= rowCreator.format(\"Jitter Status\", cosProfileMeasurements[\"Jitter Status\"]); -%>\n<% } -%>\n<% } else { -%>\nDiagnostics results not found\n<% } -%>\n\nUNI-E Status:\n<% if ((r && r.MDR150) && (s && s.NBNuniestatusEE)) { -%>\n<%= rowCreator.format(\"Test ID\", s.NBNuniestatusEE && s.NBNuniestatusEE.event &&\n                                s.NBNuniestatusEE.event.id ? \n                                s.NBNuniestatusEE.event.id : \"Unknown\"); -%>\n<%= rowCreator.format(\"Test Status\", s.NBNuniestatusEE && s.NBNuniestatusEE.event &&\n                                    s.NBNuniestatusEE.event.status ? \n                                    s.NBNuniestatusEE.event.status : \"Unknown\"); -%>\n<%= rowCreator.format(\"BPI ID\", bpiID); -%>\n<%= rowCreator.format(\"OVC ID\", s.NBNuniestatusEE && s.NBNuniestatusEE.event &&\n                               s.NBNuniestatusEE.event.serviceTest && s.NBNuniestatusEE.event.serviceTest.serviceRef &&\n                               s.NBNuniestatusEE.event.serviceTest.serviceRef.id ?\n                               s.NBNuniestatusEE.event.serviceTest.serviceRef.id : \"Unknown\"); -%>\n<%= rowCreator.format(\"Errors\", r.MDR150.diagnosticErrors ? r.MDR150.diagnosticErrors : null); -%>\n\nService Test Results\n<%= rowCreator.format(\"UNI-E Status\", r.MDR150.diagnosticStatus); -%>\n<%= rowCreator.format(\"UNI-E Status Result\", r.MDR150.diagnosticResult); -%>\n<%= rowCreator.format(\"UNI-E Admin State\", r.MDR150.indicatorAdminState); -%>\n<%= rowCreator.format(\"UNI-E Operational State\", r.MDR150.indicatorOperationalState); -%>\n<%= rowCreator.format(\"UNI-E Link State\", r.MDR150.indicatorLinkState); -%>\n<%= rowCreator.format(\"UNI-E Last State Change Date\", r.MDR150.indicatorLastChangeTime); -%>\n<%= rowCreator.format(\"UNI-E Port Type\", r.MDR150.indicatorPortType); -%>\n<%= rowCreator.format(\"UNI-E Operational Speed\", r.MDR150.performanceOperationalSpeed); -%>\n<%= rowCreator.format(\"UNI-E Config Speed\", r.MDR150.performanceConfigSpeed); -%>\n<%= rowCreator.format(\"UNI-E Operational Duplex\", r.MDR150.performanceOperationalDuplex); -%>\n<%= rowCreator.format(\"UNI-E Config Duplex\", r.MDR150.performanceConfigDuplex); -%>\n<% } else { -%>\nDiagnostics results not found\n<% } -%>\n\nUNI-E Port Reset:\n<% if (r && r.MAR016 && r.MAR016.action && r.MAR016.action.response) {\n    let response = r.MAR016.action.response;\n    let serviceTestResults = response && response.event &&\n                                               response.event.serviceTest && response.event.serviceTest.serviceTestResults &&\n                                               Array.isArray(response.event.serviceTest.serviceTestResults) ?\n                                               response.event.serviceTest.serviceTestResults : [];\n\n    let serviceTestResultsUniEPortReset = serviceTestResults.find(result => { return result.type === \"UNI-E Port Reset\"; });\n-%>\n<%= rowCreator.format(\"Test ID\", response && response.event &&\n                                response.event.id ? \n                                response.event.id : \"Unknown\"); -%>\n<%= rowCreator.format(\"Test Status\", response && response.event &&\n                                    response.event.status ? \n                                    response.event.status : \"Unknown\"); -%>\n<%= rowCreator.format(\"BPI ID\", bpiID); -%>\n<%= rowCreator.format(\"OVC ID\", response && response.event &&\n                               response.event.serviceTest && response.event.serviceTest.serviceRef &&\n                               response.event.serviceTest.serviceRef.id ?\n                               response.event.serviceTest.serviceRef.id : \"Unknown\"); -%>\n<%= rowCreator.format(\"Errors\", serviceTestResultsUniEPortReset && serviceTestResultsUniEPortReset.errors &&\n                               Array.isArray(serviceTestResultsUniEPortReset.errors) ? \n                               serviceTestResultsUniEPortReset.errors.map(error => `${error.code}: ${error.reason}`) : null); -%>\n<% if (serviceTestResultsUniEPortReset) { -%>\nService Test Results\n<%= rowCreator.format(\"UNI-E Port Reset Status\", serviceTestResultsUniEPortReset.status ? serviceTestResultsUniEPortReset.status : \"Unknown\"); -%>\n<%= rowCreator.format(\"UNI-E Port Reset Status Result\", serviceTestResultsUniEPortReset.result ? serviceTestResultsUniEPortReset.result : \"Unknown\"); -%>\n<% } -%>\n<% } else { -%>\nDiagnostics results not found\n<% } -%>\n\nMerge Result Link: <%= mergeUrl %>/serviceCheck/view/html/<%= data.id %>\n<% } else { -%>\nNo NBN Enterprise Ethernet service found in current record\n<% } -%>"}, {"name": "NBNEnterpriseEthernetSummaryText", "active": true, "title": "NBN Enterprise Ethernet Summary (Text)", "description": "Summary of NBN Enterprise Ethernet service", "allowedSystemsToAppend": ["ServiceCentral"], "suite": [], "templateType": "Service Check", "formatType": "Text", "listCondition": "data.carriageType === 'NBN EE';", "defaultPriority": 0, "template": "<% \nlet nbnEnterpriseEthernetData = s.MAGPIE && s.MAGPIE.rawData ? (s.MAGPIE.rawData.IPWAN ? s.MAGPIE.rawData.IPWAN : s.MAGPIE.rawData.TE) : {};\nlet networkName = nbnEnterpriseEthernetData[\"Network\"];\nlet nbnProductInventoryProductRef = Array.isArray(s.NBNProductInventory) && s.NBNProductInventory[0] && Array.isArray(s.NBNProductInventory[0].productRef) ? s.NBNProductInventory[0].productRef : [];\n\nlet btdProduct = nbnProductInventoryProductRef.find((product) => {\n    return product && product[\"@type\"] === \"BTD\";\n});\nlet uniEProduct = null;\n\nif (btdProduct && Array.isArray(btdProduct.productRef)) {\n    uniEProduct = btdProduct.productRef.find((product) => {\n        return product && product[\"@type\"] === \"UNI-E\";\n    });\n}\n\nlet uniEProductInterfaceType = \"Not found\";\nlet uniEProductOVCType = \"Not found\";\nlet uniEProductUniPort = \"Not found\";\n\nif (uniEProduct && typeof uniEProduct === \"object\") {\n    uniEProductInterfaceType = uniEProduct.interfaceType;\n    uniEProductOVCType = uniEProduct.ovcType;\n    uniEProductUniPort = uniEProduct.uniPort;\n}\n\nlet bpiID = s.NBNProductInventory && Array.isArray(s.NBNProductInventory) &&\n            s.NBNProductInventory.length > 0 && s.NBNProductInventory[0].id ? s.NBNProductInventory[0].id : \"Unknown\";\n\nlet rowCreator = new RowCreator();\n-%>\n<% if (data.carriageType === 'NBN EE') { -%>\nNBN Enterprise Ethernet <%= data.fnn %>\n<%= rowCreator.format(\"Customer\", nbnEnterpriseEthernetData[\"Customer\"]); -%>\n<%= rowCreator.format(`Member of ${networkName} Network`, nbnEnterpriseEthernetData[`Member of ${networkName} Network`]); -%>\n<%= rowCreator.format(\"Trunk FNN\", data.fnn); -%>\n<%= rowCreator.format(\"Product Name\", nbnEnterpriseEthernetData[\"Product Name\"]); -%>\n<%= rowCreator.format(\"Associated Access Service\", nbnEnterpriseEthernetData[\"Associated Access Service\"]); -%>\n<%= rowCreator.format(\"Associated Access Service FNN\", nbnEnterpriseEthernetData[\"Associated Access Service FNN\"]); -%>\n<%= rowCreator.format(\"VLAN Domain\", typeof nbnEnterpriseEthernetData[\"VLAN Domain\"] === \"string\" ? nbnEnterpriseEthernetData[\"VLAN Domain\"].replace(\":\", \"\") : null); -%>\n<%= rowCreator.format(\"VLAN FNN\", nbnEnterpriseEthernetData[\"VLAN FNN\"]); -%>\n<%= rowCreator.format(\"Media Type\", nbnEnterpriseEthernetData[\"Media type\"]); -%>\n<%= rowCreator.format(\"Terminating Product\", nbnEnterpriseEthernetData[\"Terminating Product\"]); -%>\n<%= rowCreator.format(\"Protocol\", nbnEnterpriseEthernetData[\"Protocol\"]); -%>\n<%= rowCreator.format(\"Basement switch\", nbnEnterpriseEthernetData[\"Basement switch\"]); -%>\n<%= rowCreator.format(\"Service Speed\", nbnEnterpriseEthernetData[\"Service Speed\"]); -%>\n<%= rowCreator.format(\"BPI ID\", bpiID); -%>\n<%= rowCreator.format(\"AVC\", nbnEnterpriseEthernetData[\"AVC\"]); -%>\n<%= rowCreator.format(\"Interface Type\", uniEProductInterfaceType); -%>\n<%= rowCreator.format(\"OVC Type\", uniEProductOVCType); -%>\n<%= rowCreator.format(\"UNI Port\", uniEProductUniPort); -%>\n\nMerge Result Link: <%= mergeUrl %>/serviceCheck/view/html/<%= data.id %>\n<% } else { -%>\nNo NBN Enterprise Ethernet service found in current record\n<% } %>"}, {"name": "NBNFieldTask", "active": true, "title": "NBN Field Task (Text)", "description": "NBN Field Task - NBN Co", "allowedSystemsToAppend": ["ServiceCentral"], "suite": [], "templateType": "Service Check", "formatType": "Text", "listCondition": "data.carriageType === 'NBN' && (data.nbnAccessType == 'FTTB' || data.nbnAccessType == 'FTTC' || data.nbnAccessType == 'FTTN' || data.nbnAccessType == 'FTTP' || data.nbnAccessType == 'HFC' || data.nbnAccessType == 'Wireless');", "defaultPriority": 0, "template": "<%\nlet serviceHealthData = s['NBN - Service Health'] ? s['NBN - Service Health'] : null;\nlet nbnAccessType = data.nbnAccessType;\nif (serviceHealthData) {\n\n  let rowCreator = new RowCreator();\n  let overviewIndicator = serviceHealthData.overviewIndicator ? serviceHealthData.overviewIndicator : {};\n\n  // Maps \"Green\" and \"Red\" values from overviewIndicator to other values\n  function overviewStringToState(overviewState) {\n    if (typeof overviewState !== \"string\") return null;\n    switch(overviewState) {\n      case \"Green\":\n          return \"OK\";\n      case \"Red\":\n          return \"Failed\";\n      case \"Amber\":\n          return \"Warning\";\n      default:\n          return \"Unknown\";\n    }\n  }\n\n-%>\n<%- renderTemplate(\"LatestServiceCentralIncidentModule\"); -%>\nAccess type:       <%= nbnAccessType %>\n\n<%= rowCreator.format(\"Site Address\", data.address ? data.address : \"Not found\"); -%>\nSite Contact:\nSite Hours:\n\n<%= rowCreator.format(\"Connectivity\",  overviewStringToState(overviewIndicator.connectivity)); -%>\n<%= rowCreator.format(\"Performance\",   overviewStringToState(overviewIndicator.performance)); -%>\n<%= rowCreator.format(\"Stability\",     overviewStringToState(overviewIndicator.stability)); -%>\n<%- renderTemplate(\"NBNMetricsModule\"); -%>\n<%} -%>\n<%- renderTemplate(\"CTInstructionsNBNTroubleTicket\"); -%>\n\nNBN to Field Template"}, {"name": "NBNMetricsModule", "active": true, "title": "CPE & Operational Status metrics for NBN", "description": "Display metrics for NBN FTTB/C/N/P HFC  & Wireless", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nlet serviceHealthData = s['NBN - Service Health'] ? s['NBN - Service Health'] : null;\n\nif (serviceHealthData) {\n\n  let rowCreator = new RowCreator();\n  let opticalSignalMetrics = {};\n  let cpeMetrics = {};\n  let NTDMetrics = {};\n  let HFCSignalMetrics = {};\n  let healthCategoryMetrics = {};\n  let healthCategory = serviceHealthData.healthCategory && Array.isArray(serviceHealthData.healthCategory) ? serviceHealthData.healthCategory : [];\n\n  // Helper function to extract key value pairs from healthCategoryItem array in NBN Service Health response\n  // Also supports extracting values that require 2 input keys, \"@type\" and \"id\" (eg. the Dropouts health category)\n  function extractMetrics(healthCategoryItems, includeTypeKey=false) {\n      let metrics = {};\n      if (!Array.isArray(healthCategoryItems)) return metrics;\n      if (includeTypeKey) {\n        for (let i = 0; i < healthCategoryItems.length; i++) {\n          if (healthCategoryItems[i] && healthCategoryItems[i].id && healthCategoryItems[i]['@type']) {\n            if (!metrics[healthCategoryItems[i].id]) metrics[healthCategoryItems[i].id] = {};\n            metrics[healthCategoryItems[i].id][healthCategoryItems[i]['@type']] = healthCategoryItems[i];\n          }\n        }\n      } else {\n        for (let i = 0; i < healthCategoryItems.length; i++) {\n          if (healthCategoryItems[i] && healthCategoryItems[i].id) {\n            metrics[healthCategoryItems[i].id] = healthCategoryItems[i];\n          }\n        }\n      }\n      return metrics;\n  }\n\n  for (let i = 0; i < healthCategory.length; i++) {\n    if (healthCategory[i] && healthCategory[i].type && Array.isArray(healthCategory[i].healthCategoryItem))\n      healthCategoryMetrics[healthCategory[i].type] = healthCategory[i].healthCategoryItem;\n  }\n\n  switch (data.nbnAccessType) {\n    case \"FTTB\":\n      operationalStatusMetrics = extractMetrics(healthCategoryMetrics.OperationalStatus);\n\t  break;\n    case \"FTTC\":\n      operationalStatusMetrics = extractMetrics(healthCategoryMetrics.OperationalStatus);\n\t  break;\n    case \"FTTN\":\n      operationalStatusMetrics = extractMetrics(healthCategoryMetrics.OperationalStatus);\n\t  break;\n    case \"FTTP\":\n      operationalStatusMetrics = extractMetrics(healthCategoryMetrics.OperationalStatus);\n      opticalSignalMetrics = extractMetrics(healthCategoryMetrics.OpticalSignal);\n      NTDMetrics = extractMetrics(healthCategoryMetrics.NTD);\n\t  break;\n    case \"HFC\":\n      operationalStatusMetrics = extractMetrics(healthCategoryMetrics.OperationalStatus);\n      cpeMetrics = extractMetrics(healthCategoryMetrics.Cpe);\n      NTDMetrics = extractMetrics(healthCategoryMetrics.NTD);\n      HFCSignalMetrics = extractMetrics(healthCategoryMetrics.Speed);\n\t  break;\n    case \"Wireless\":\n      operationalStatusMetrics = extractMetrics(healthCategoryMetrics.OperationalStatus);\n      cpeMetrics = extractMetrics(healthCategoryMetrics.Cpe);\n\t  break;\n\tdefault:\n\t  break;\n  }\n-%>\n<%= rowCreator.format(\"Service State\", operationalStatusMetrics.serviceState ? operationalStatusMetrics.serviceState.value : null); -%>\n<%= rowCreator.format(\"CPE MAC Address\", cpeMetrics.macAddress ? cpeMetrics.macAddress.value : null); -%>\n<% if (data.nbnAccessType == 'FTTP') { -%><%= rowCreator.format(\"Optical Signal Status\", opticalSignalMetrics.status ? opticalSignalMetrics.status.value : null); -%><% } -%>\n<% if (data.nbnAccessType == 'FTTP') { -%><%= rowCreator.format(\"NTD ID\", NTDMetrics.ntdId ? NTDMetrics.ntdId.value : null); -%><% } -%>\n<%= rowCreator.format(\"NTD Port ID\", NTDMetrics.portId ? NTDMetrics.portId.value : null); -%>\n<%= rowCreator.format(\"NTD Port State\", NTDMetrics.portState ? NTDMetrics.portState.value : null); -%>\n<% if (data.nbnAccessType == 'HFC') { -%><%= rowCreator.format(\"NTD Model\", NTDMetrics.model ? NTDMetrics.model.value : null); -%><% } -%>\n<% if (data.nbnAccessType == 'FTTP') { -%><%= rowCreator.format(\"NTD Serial Number\", NTDMetrics.serialNumber ? NTDMetrics.serialNumber.value : null); -%><% } -%>\n<% if (data.nbnAccessType == 'FTTP') { -%><%= rowCreator.format(\"NTD Make\", NTDMetrics.make && NTDMetrics.make.value ? NTDMetrics.make.value : \"N/A\"); -%><% } -%>\n<% if (data.nbnAccessType == 'FTTP') { -%><%= rowCreator.format(\"NTD Install Location\", NTDMetrics.installLocation && NTDMetrics.installLocation.value ? NTDMetrics.installLocation.value : \"N/A\"); -%><% } -%>\n<% if (data.nbnAccessType == 'FTTP') { -%><%= rowCreator.format(\"NTD Serial Number\", NTDMetrics.currentSpeedAndDuplex && NTDMetrics.currentSpeedAndDuplex.value ? NTDMetrics.currentSpeedAndDuplex.value : \"N/A\"); -%><% } -%>\n<% if (data.nbnAccessType == 'HFC') { -%><%= rowCreator.format(\"NTD MAC Address\", NTDMetrics.macAddress ? NTDMetrics.macAddress.value : \"N/A\"); -%><% } -%>\n<% if (data.nbnAccessType == 'HFC') { -%><%= rowCreator.format(\"Service Configuration\", NTDMetrics.serviceConfiguration ? NTDMetrics.serviceConfiguration.value : \"N/A\"); -%><% } -%>\n<%= rowCreator.format(\"HFC Signal Status\", HFCSignalMetrics.status ? HFCSignalMetrics.status.value : null); -%>\n<%} -%>"}, {"name": "NBNProductInventoryModule", "active": true, "title": "Displays info from NBNProductInventory source (Module)", "description": "Displays NBN Product Inventory order and COS status from WAS", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<% if (s.NBNProductInventory?.[0] && typeof s.NBNProductInventory[0] === 'object') {\nlet productInventoryEntry = s.NBNProductInventory[0];\nlet productRefTypeOvc = null;\nlet productRefTypeBtd = null;\nlet productRefFromBtd = null;\n\nif (Array.isArray(productInventoryEntry.productRef)) {\n    productRefTypeOvc = productInventoryEntry.productRef.find((item) => {\n        return item?.['@type'] === 'OVC';\n    });\n    \n    productRefTypeBtd = productInventoryEntry.productRef.find((item) => {\n        return item?.['@type'] === 'BTD';\n    });\n}\n\nif (Array.isArray(productRefTypeBtd?.productRef) && productRefTypeBtd.productRef.length) {\n    productRefFromBtd = productRefTypeBtd.productRef[0];\n}\n\nlet cosProductInventory = null;\nif (productRefTypeOvc?.cosLowBandwidth) {\n    cosProductInventory = 'Low';\n} else if (productRefTypeOvc?.cosHighBandwidth) {\n    cosProductInventory = 'High';\n}\n\nlet cosWAS = null;\nif (Array.isArray(s.WASProductOrderDetails?.productOrderItem?.[0]?.product?.productCharacteristic)) {\n    for (let characteristic of s.WASProductOrderDetails.productOrderItem[0].product.productCharacteristic) {\n        if (characteristic?.name === 'classOfService') {\n            switch (characteristic.value) {\n                case 'nbnCosL':\n                    cosWAS = 'Low';\n                    break;\n                case 'nbnCosH':\n                    cosWAS = 'High';\n                    break;\n            }\n        }\n    }\n}\n\nlet rowCreator = new RowCreator();\n-%>\n--NBN Product Inventory Details--\n<%= rowCreator.format('ID', productInventoryEntry.id); -%>\n<%= rowCreator.format('State', productInventoryEntry.state); -%>\n<%= rowCreator.format('Disconnected Date', !isNaN(Date.parse(productInventoryEntry.disconnectedDate)) ? new Date(productInventoryEntry.disconnectedDate).toLocaleString() : null); -%>\n<%= rowCreator.format('Service Restoration SLA', productInventoryEntry.serviceRestorationSla); -%>\n<%= rowCreator.format('Location ID', productInventoryEntry.relatedPlace?.id); -%>\n<%= rowCreator.format('OVC', productRefTypeOvc?.id); -%>\n<%= rowCreator.format('NNI Group ID', productRefTypeOvc?.nniGroupId); -%>\n<%= rowCreator.format('sVlan ID', productRefTypeOvc?.sVlanId); -%>\n<%= rowCreator.format('COS (Product Inventory)', cosProductInventory ? cosProductInventory : 'Not Found'); -%>\n<%= rowCreator.format('COS (WAS)', cosWAS ? cosWAS : 'Not Found'); -%>\n<%= rowCreator.format('Preferred BTD Mounting', productRefTypeBtd?.preferredBTDMounting); -%>\n<%= rowCreator.format('Interface Type', productRefFromBtd?.interfaceType); -%>\n<% } -%>"}, {"name": "NBNServiceHealthModule", "active": true, "title": "NBN Service Health (Module)", "description": "Module to extract metrics from the NBN - Service Health source for NBN services", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nlet rowCreator = new RowCreator();\nlet serviceHealthData = s['NBN - Service Health'] ? s['NBN - Service Health'] : null;\nlet nbnAccessType = data.nbnAccessType ? data.nbnAccessType : ((r.MDR064 && r.MDR064.AccessType) ? r.MDR064.AccessType : null);\n\n// Maps \"Green\" and \"Red\" values from overviewIndicator to other values\nfunction overviewStringToState(overviewState) {\n    if (typeof overviewState !== \"string\") {\n        return null;\n    }\n\n    switch(overviewState) {\n        case \"Green\":\n            return \"OK\";\n        case \"Red\":\n            return \"Failed\";\n        case \"Amber\":\n            return \"Warning\";\n        default:\n            return \"Unknown\";\n    }\n}\n\n// Helper function to extract key value pairs from healthCategoryItem array in NBN Service Health response\n// Also supports extracting values that require 2 input keys, \"@type\" and \"id\" (eg. the Dropouts health category)\nfunction extractMetrics(healthCategoryItems, includeTypeKey=false) {\n    let metrics = {};\n\n    if (!Array.isArray(healthCategoryItems)) {\n        return metrics;\n    }\n\n    if (includeTypeKey) {\n        for (let i = 0; i < healthCategoryItems.length; i++) {\n            if (healthCategoryItems[i] && healthCategoryItems[i].id && healthCategoryItems[i]['@type']) {\n                if (!metrics[healthCategoryItems[i].id]) {\n                    metrics[healthCategoryItems[i].id] = {};\n                }\n                metrics[healthCategoryItems[i].id][healthCategoryItems[i]['@type']] = healthCategoryItems[i];\n            }\n        }\n    } else {\n        for (let i = 0; i < healthCategoryItems.length; i++) {\n            if (healthCategoryItems[i] && healthCategoryItems[i].id) {\n                metrics[healthCategoryItems[i].id] = healthCategoryItems[i];\n            }\n        }\n    }\n\n    return metrics;\n}\n\nfunction setMetricArray(metrics, metricNames, includeUnit=false) {\n    let metricsArray = [];\n    if (metrics) {\n        for (let i = 0; i < metricNames.length; i++) {\n            let metricName = metricNames[i];\n            \n            let metricString = \"N/A\";\n            if (metrics[metricName] && metrics[metricName].value !== undefined && metrics[metricName].value !== null) {\n                if (includeUnit && metrics[metricName].unit !== undefined && metrics[metricName].unit !== null) {\n                    metricString = `${metrics[metricName].value}${metrics[metricName].unit}`;\n                } else {\n                    metricString = metrics[metricName].value;\n                }\n            }\n            \n            metricsArray.push(metricString);\n        }\n    } else {\n        metricsArray = Array(metricNames.length).fill(\"N/A\");\n    }\n    return metricsArray;\n}\n\nfunction convertUptimeMetricTime(metricValue, metricUnit) {\n    let uptimeMetric = `${metricValue} ${metricUnit}`;\n    // If the unit is seconds, attempts to do a conversion to hours / minutes / seconds\n    if (metricUnit === 'Seconds') {\n        let uptimeValue = parseInt(metricValue);\n        if (!isNaN(uptimeValue)) {\n            let uptimeHours = Math.floor(uptimeValue / 3600);\n            let uptimeMinutes = Math.floor((uptimeValue % 3600) / 60);\n            let uptimeSeconds = uptimeValue % 60;\n            \n            uptimeMetric = `${uptimeHours} Hours ${uptimeMinutes} Minutes ${uptimeSeconds} Seconds`;\n        }\n    }\n    \n    return uptimeMetric;\n}\n\nfunction splitStringToLines(messageString) {\n    const CHAR_LIMIT = 100;\n\n    let messageLines = [];\n    if (!messageString || typeof messageString !== 'string') {\n        return messageLines;\n    }\n\n    let currLine = \"\";\n    let words = messageString.split(/ |(?<=[ \\n])/);\n    for (let word of words) {\n        if (word.includes(\"\\n\")) {\n            currLine += word;\n            messageLines.push(currLine.trim());\n            currLine = \"\";\n        } else {\n            currLine += word + \" \";\n            if (currLine.trim().length > CHAR_LIMIT) {\n                messageLines.push(currLine.trim());\n                currLine = \"\";\n            }\n        }\n    }\n\n    if (currLine) {\n        messageLines.push(currLine.trim());\n    }\n    \n    // Excludes lines with empty strings\n    return messageLines.filter((line) => line.trim().length);\n}\n-%>\n<% if (serviceHealthData) {\nlet overviewIndicator = serviceHealthData.overviewIndicator ? serviceHealthData.overviewIndicator : {};\nlet healthCategory = serviceHealthData.healthCategory && Array.isArray(serviceHealthData.healthCategory) ? serviceHealthData.healthCategory : [];\nlet hriId = s['NBN - Service Health'] && s['NBN - Service Health'].id ? s['NBN - Service Health'].id : \"Not found\";\nlet avcId = s['NBN - Service Health'] && s['NBN - Service Health'].avcId ? s['NBN - Service Health'].avcId : \"Not found\";\nlet nextAction = s['NBN - Service Health'] && s['NBN - Service Health'].currentCondition &&\n                 s['NBN - Service Health'].currentCondition.nextAction ? s['NBN - Service Health'].currentCondition.nextAction : null;\n-%>\n<%= rowCreator.format(\"NBN Service Health\", hriId); -%>\n<%= rowCreator.format(\"AVC\", avcId); -%>\n<%= rowCreator.format(\"Connectivity\", overviewStringToState(overviewIndicator.connectivity)); -%>\n<%= rowCreator.format(\"Performance\", overviewStringToState(overviewIndicator.performance)); -%>\n<%= rowCreator.format(\"Stability\", overviewStringToState(overviewIndicator.stability)); -%>\n\n<%\nlet healthCategoryMetrics = {};\nfor (let i = 0; i < healthCategory.length; i++) {\n    if (healthCategory[i] && healthCategory[i].type && Array.isArray(healthCategory[i].healthCategoryItem)) {\n        healthCategoryMetrics[healthCategory[i].type] = healthCategory[i].healthCategoryItem;\n    }\n}\n\nlet operationalStatusMetrics;\nlet opticalSignalMetrics;\nlet cpeMetrics;\nlet NCDMetrics;\nlet NTDMetrics;\nlet HFCSignalMetrics;\nlet networkUtilisationMetrics;\nlet dropoutMetrics;\nlet heavyUserMetrics;\nlet networkMetrics;\nlet inHomeWiringMetrics;\nlet plannedNetworkUpgradeMetrics;\nlet uptimeMetric;\nlet wirelessSignalMetrics;\nlet outageMetrics;\n\nconst FTTB_FTTN_DROPOUT_METRIC_NAMES = [\"DoToday\", \"DoYesterday\", \"Do2Days\", \"Do7Days\", \"Do30Days\"];\nconst HFC_DROPOUT_METRIC_NAMES = [\"DoToday\", \"DoYesterday\", \"Do2Days\", \"Do7Days\", \"Do30Days\"];\nconst FTTC_DROPOUT_METRIC_NAMES = [\"DoYesterday\", \"Do2Days\", \"Do7Days\", \"Do30Days\"];\nconst FTTP_DROPOUT_METRIC_NAMES = [\"DoToday\", \"DoYesterday\", \"Do2Days\", \"Do7Days\", \"Do30Days\"];\n-%>\n<%\nswitch (nbnAccessType) {\n    case \"FTTN\":\n    case \"FTTB\": \n        operationalStatusMetrics = extractMetrics(healthCategoryMetrics.OperationalStatus);\n        cpeMetrics = extractMetrics(healthCategoryMetrics.Cpe);\n        inHomeWiringMetrics = extractMetrics(healthCategoryMetrics.InHomeWiring);\n        dropoutMetrics = extractMetrics(healthCategoryMetrics.Dropouts, true);\n        outageMetrics = extractMetrics(healthCategoryMetrics.Outage);\n-%>\n--Operational Status--\n<%= rowCreator.format(\"Service State\", operationalStatusMetrics.serviceState ? operationalStatusMetrics.serviceState.value : null); -%>\n<%= rowCreator.format(\"Line Status Change\", operationalStatusMetrics.lineStatusChange ? operationalStatusMetrics.lineStatusChange.value : null); -%>\n\n--CPE--\n<%= rowCreator.format(\"MAC Address\", cpeMetrics.macAddress ? cpeMetrics.macAddress.value : null); -%>\n<%= rowCreator.format(\"Make\", cpeMetrics.make ? cpeMetrics.make.value : null); -%>\n<%= rowCreator.format(\"Model\", cpeMetrics.model ? cpeMetrics.model.value : null); -%>\n<%= rowCreator.format(\"Serial Number\", cpeMetrics.serialNo ? cpeMetrics.serialNo.value : null); -%>\n<%= rowCreator.format(\"Firmware Version\", cpeMetrics.firmwareVersion ? cpeMetrics.firmwareVersion.value : null); -%>\n\n--In-Home Wiring--\n<%= rowCreator.format(\"In-Home Wiring\", inHomeWiringMetrics.InHomeWiring ? inHomeWiringMetrics.InHomeWiring.value : null); -%>\n\n--Dropouts--\n<%= rowCreator.format(\"\", [\"Today\", \"Yesterday\", \"2 Days\", \"7 Days\", \"30 Days\"], true); -%>\n<%= rowCreator.format(\"Total\", setMetricArray(dropoutMetrics.total, FTTB_FTTN_DROPOUT_METRIC_NAMES), true); -%>\n<%= rowCreator.format(\"Initiated\", setMetricArray(dropoutMetrics.initiated, FTTB_FTTN_DROPOUT_METRIC_NAMES), true); -%>\n<%= rowCreator.format(\"Network\", setMetricArray(dropoutMetrics.network, FTTB_FTTN_DROPOUT_METRIC_NAMES), true); -%>\n<%= rowCreator.format(\"Unexpected\", setMetricArray(dropoutMetrics.unexpected, FTTB_FTTN_DROPOUT_METRIC_NAMES), true); -%>\n\n--Outages--\n<%= rowCreator.format(\"Current Outage\", outageMetrics.currentOutage && outageMetrics.currentOutage.value ? (outageMetrics.currentOutage.value === \"true\" ? \"Yes\" : \"No\") : \"N/A\"); -%>\n<%= rowCreator.format(\"Planned outage ID\", outageMetrics.plannedOutageId && outageMetrics.plannedOutageId.value ? outageMetrics.plannedOutageId.value : null); -%>\n<%= rowCreator.format(\"Unplanned outage ID\", outageMetrics.unplannedOutageIds && outageMetrics.unplannedOutageIds.value ? outageMetrics.unplannedOutageIds.value : null); -%>\n<%= rowCreator.format(\"Network activity ID\", outageMetrics.networkActivityId && outageMetrics.networkActivityId.value ? outageMetrics.networkActivityId.value : null); -%>\n<%      break;\n    case \"HFC\":\n        operationalStatusMetrics = extractMetrics(healthCategoryMetrics.OperationalStatus);\n        cpeMetrics = extractMetrics(healthCategoryMetrics.Cpe);\n        NTDMetrics = extractMetrics(healthCategoryMetrics.NTD);\n        HFCSignalMetrics = extractMetrics(healthCategoryMetrics.Speed);\n        dropoutMetrics = extractMetrics(healthCategoryMetrics.Dropouts, true);\n                \n        let hfcTimeOfLastDropout = null;\n        if (dropoutMetrics.timeOfLastDropout && dropoutMetrics.timeOfLastDropout.DoMain && dropoutMetrics.timeOfLastDropout.DoMain.value) {\n            hfcTimeOfLastDropout = new Date(dropoutMetrics.timeOfLastDropout.DoMain.value);\n        }\n\n        if (operationalStatusMetrics.uptime) {\n            uptimeMetric = convertUptimeMetricTime(operationalStatusMetrics.uptime.value, operationalStatusMetrics.uptime.unit);\n        }\n\n        outageMetrics = extractMetrics(healthCategoryMetrics.Outage);\n-%>\n--Operational Status--\n<%= rowCreator.format(\"Service State\", operationalStatusMetrics.serviceState ? operationalStatusMetrics.serviceState.value : null); -%>\n<%= rowCreator.format(\"Last Status Change\", operationalStatusMetrics.lastStatusChange ? operationalStatusMetrics.lastStatusChange.value : null); -%>\n<%= rowCreator.format(\"Uptime\", uptimeMetric); -%>\n\n--CPE--\n<%= rowCreator.format(\"MAC Address\", cpeMetrics.macAddress ? cpeMetrics.macAddress.value : null); -%>\n\n--NTD--\n<%= rowCreator.format(\"Port State\", NTDMetrics.portState ? NTDMetrics.portState.value : null); -%>\n<%= rowCreator.format(\"Port ID\", NTDMetrics.portId ? NTDMetrics.portId.value : null); -%>\n<%= rowCreator.format(\"Model\", NTDMetrics.model ? NTDMetrics.model.value : null); -%>\n<%= rowCreator.format(\"MAC Address\", NTDMetrics.macAddress ? NTDMetrics.macAddress.value : \"N/A\"); -%>\n<%= rowCreator.format(\"Service Configuration\", NTDMetrics.serviceConfiguration ? NTDMetrics.serviceConfiguration.value : \"N/A\"); -%>\n\n--HFC Signal--\n<%= rowCreator.format(\"Status\", HFCSignalMetrics.status ? HFCSignalMetrics.status.value : null); -%>\n\n--Dropouts--\n<%= rowCreator.format(\"\", [\"Today\", \"Yesterday\", \"2 Days\", \"7 Days\", \"30 Days\"], true); -%>\n<%= rowCreator.format(\"Total\", setMetricArray(dropoutMetrics.total, HFC_DROPOUT_METRIC_NAMES), true); -%>\n<%= rowCreator.format(\"Initiated\", setMetricArray(dropoutMetrics.initiated, HFC_DROPOUT_METRIC_NAMES), true); -%>\n<%= rowCreator.format(\"Network\", setMetricArray(dropoutMetrics.network, HFC_DROPOUT_METRIC_NAMES), true); -%>\n<%= rowCreator.format(\"Unexpected\", setMetricArray(dropoutMetrics.unexpected, HFC_DROPOUT_METRIC_NAMES), true); -%>\n<%= rowCreator.format(\"Time of Last Dropout\", hfcTimeOfLastDropout !== null && !isNaN(hfcTimeOfLastDropout) ? hfcTimeOfLastDropout.toISOString() : \"N/A\"); -%>\n\n--Outages--\n<%= rowCreator.format(\"Current Outage\", outageMetrics.currentOutage && outageMetrics.currentOutage.value ? (outageMetrics.currentOutage.value === \"true\" ? \"Yes\" : \"No\") : \"N/A\"); -%>\n<%= rowCreator.format(\"Planned outage ID\", outageMetrics.plannedOutageId && outageMetrics.plannedOutageId.value ? outageMetrics.plannedOutageId.value : null); -%>\n<%= rowCreator.format(\"Unplanned outage ID\", outageMetrics.unplannedOutageIds && outageMetrics.unplannedOutageIds.value ? outageMetrics.unplannedOutageIds.value : null); -%>\n<%= rowCreator.format(\"Network activity ID\", outageMetrics.networkActivityId && outageMetrics.networkActivityId.value ? outageMetrics.networkActivityId.value : null); -%>\n<%      break;\n    case \"FTTC\":\n        operationalStatusMetrics = extractMetrics(healthCategoryMetrics.OperationalStatus);\n        cpeMetrics = extractMetrics(healthCategoryMetrics.Cpe);\n        NCDMetrics = extractMetrics(healthCategoryMetrics.Ncd);\n        inHomeWiringMetrics = extractMetrics(healthCategoryMetrics.InHomeWiring);\n        dropoutMetrics = extractMetrics(healthCategoryMetrics.Dropouts, true);\n        outageMetrics = extractMetrics(healthCategoryMetrics.Outage);\n\n        let lastStatusChangeFTTC = operationalStatusMetrics.lastStatusChange && operationalStatusMetrics.lastStatusChange.value ? new Date(operationalStatusMetrics.lastStatusChange.value) : null;\n-%>\n--Operational Status--\n<%= rowCreator.format(\"Service State\", operationalStatusMetrics.serviceState ? operationalStatusMetrics.serviceState.value : null); -%>\n<%= rowCreator.format(\"Last Status Change\", lastStatusChangeFTTC !== null && !isNaN(lastStatusChangeFTTC) ? lastStatusChangeFTTC.toISOString() : (operationalStatusMetrics.lastStatusChange ? operationalStatusMetrics.lastStatusChange.value : null)); -%>\n<%= rowCreator.format(\"Reverse Power State\", operationalStatusMetrics.reversePowerState ? operationalStatusMetrics.reversePowerState.value : null); -%>\n\n--CPE--\n<%= rowCreator.format(\"MAC Address\", cpeMetrics.macAddress ? cpeMetrics.macAddress.value : null); -%>\n\n--NCD--\n<%= rowCreator.format(\"Port State\", NCDMetrics.portState ? NCDMetrics.portState.value : null); -%>\n<%= rowCreator.format(\"Port ID\", NCDMetrics.portId ? NCDMetrics.portId.value : null); -%>\n<%= rowCreator.format(\"MAC Address\", NCDMetrics.macAddress ? NCDMetrics.macAddress.value : null); -%>\n<%= rowCreator.format(\"Make and Model\", NCDMetrics.make ? NCDMetrics.make.value : null); -%>\n<%= rowCreator.format(\"Operational Speed and Duplex\", NCDMetrics.currentSpeedAndDuplex ? NCDMetrics.currentSpeedAndDuplex.value : null); -%>\n\n--In-Home Wiring--\n<%= rowCreator.format(\"In-home Wiring\", inHomeWiringMetrics.inHomeWiring ? inHomeWiringMetrics.inHomeWiring.value : null); -%>\n<%= rowCreator.format(\"Line Impairment\", inHomeWiringMetrics.lineImpairments ? inHomeWiringMetrics.lineImpairments.value : null); -%>\n\n--Dropouts--\n<%= rowCreator.format(\"\", [\"Yesterday\", \"2 Days\", \"7 Days\", \"30 Days\"], true); -%>\n<%= rowCreator.format(\"Total\", setMetricArray(dropoutMetrics.total, FTTC_DROPOUT_METRIC_NAMES), true); -%>\n<%= rowCreator.format(\"Initiated\", setMetricArray(dropoutMetrics.initiated, FTTC_DROPOUT_METRIC_NAMES), true); -%>\n<%= rowCreator.format(\"Network\", setMetricArray(dropoutMetrics.network, FTTC_DROPOUT_METRIC_NAMES), true); -%>\n<%= rowCreator.format(\"Unexpected\", setMetricArray(dropoutMetrics.unexpected, FTTC_DROPOUT_METRIC_NAMES), true); -%>\n\n--Outages--\n<%= rowCreator.format(\"Current Outage\", outageMetrics.currentOutage && outageMetrics.currentOutage.value ? (outageMetrics.currentOutage.value === \"true\" ? \"Yes\" : \"No\") : \"N/A\"); -%>\n<%= rowCreator.format(\"Planned outage ID\", outageMetrics.plannedOutageId && outageMetrics.plannedOutageId.value ? outageMetrics.plannedOutageId.value : null); -%>\n<%= rowCreator.format(\"Unplanned outage ID\", outageMetrics.unplannedOutageIds && outageMetrics.unplannedOutageIds.value ? outageMetrics.unplannedOutageIds.value : null); -%>\n<%= rowCreator.format(\"Network activity ID\", outageMetrics.networkActivityId && outageMetrics.networkActivityId.value ? outageMetrics.networkActivityId.value : null); -%>\n<%      break;\n    case \"Wireless\":\n        operationalStatusMetrics = extractMetrics(healthCategoryMetrics.OperationalStatus);\n        NTDMetrics = extractMetrics(healthCategoryMetrics.NTD);\n        heavyUserMetrics = extractMetrics(healthCategoryMetrics.FUP, true);\n        networkMetrics = extractMetrics(healthCategoryMetrics.Network, true);\n        wirelessSignalMetrics = extractMetrics(healthCategoryMetrics.WirelessSignal);\n        outageMetrics = extractMetrics(healthCategoryMetrics.Outage);\n        let heavyUserIndicators = [];\n        if (heavyUserMetrics.heavyUser) {\n        \theavyUserIndicators.push(heavyUserMetrics.heavyUser.Downstream ? heavyUserMetrics.heavyUser.Downstream.value : \"N/A\");\n        \theavyUserIndicators.push(heavyUserMetrics.heavyUser.Upstream ? heavyUserMetrics.heavyUser.Upstream.value : \"N/A\");\n        } else {\n        \theavyUserIndicators = Array(2).fill(\"N/A\");\n        }\n-%>\n--Operational Status--\n<%= rowCreator.format(\"Service State\", operationalStatusMetrics.serviceState ? operationalStatusMetrics.serviceState.value : null); -%>\n\n--NTD--\n<%= rowCreator.format(\"NTD ID\", NTDMetrics.ntdId && NTDMetrics.ntdId.value ? NTDMetrics.ntdId.value : \"N/A\"); -%>\n<%= rowCreator.format(\"Port ID\", NTDMetrics.portId && NTDMetrics.portId.value ? NTDMetrics.portId.value : \"N/A\"); -%>\n<%= rowCreator.format(\"NTD Version\", NTDMetrics.ntdVersion && NTDMetrics.ntdVersion.value ? NTDMetrics.ntdVersion.value : \"N/A\"); -%>\n\n--Wireless Signal--\n<%= rowCreator.format(\"Status\", wirelessSignalMetrics.status && wirelessSignalMetrics.status.value ? wirelessSignalMetrics.status.value : \"N/A\"); -%>\n\n--Outages--\n<%= rowCreator.format(\"Current Outage\", outageMetrics.currentOutage && outageMetrics.currentOutage.value ? (outageMetrics.currentOutage.value === \"true\" ? \"Yes\" : \"No\") : \"N/A\"); -%>\n<%= rowCreator.format(\"Planned outage ID\", outageMetrics.plannedOutageId && outageMetrics.plannedOutageId.value ? outageMetrics.plannedOutageId.value : null); -%>\n<%= rowCreator.format(\"Unplanned outage ID\", outageMetrics.unplannedOutageIds && outageMetrics.unplannedOutageIds.value ? outageMetrics.unplannedOutageIds.value : null); -%>\n<%= rowCreator.format(\"Network activity ID\", outageMetrics.networkActivityId && outageMetrics.networkActivityId.value ? outageMetrics.networkActivityId.value : null); -%>\n\n--Heavy User Indicator--\n<%= rowCreator.format(\"\", [\"Downstream\", \"Upstream\"], true); -%>\n<%= rowCreator.format(\"Heavy User\", heavyUserIndicators, true); -%>\n\n--Network--\n<% if (networkMetrics.busyHourPerformance && networkMetrics.busyHourPerformance.Cell && networkMetrics.busyHourPerformance.Cell.value) { -%>\n<%= rowCreator.format(\"Busy Hour Cell Performance\", \" \"); -%>\n<%= rowCreator.format(`(${networkMetrics.busyHourPerformance.Cell.value})`, overviewStringToState(networkMetrics.busyHourPerformance.Cell.status)); -%>\n<% } else { -%>\n<%= rowCreator.format(\"Busy Hour Cell Performance\", \"N/A\"); -%>\n<% } -%>\n<%= rowCreator.format(\"Forecast Cell Upgrade Date\", networkMetrics.forecastUpgradeDate && networkMetrics.forecastUpgradeDate.Cell &&\n\tnetworkMetrics.forecastUpgradeDate.Cell.value ? networkMetrics.forecastUpgradeDate.Cell.value : \"N/A\"); -%>\n<% if (networkMetrics.plannedActivity && networkMetrics.plannedActivity.Cell && networkMetrics.plannedActivity.Cell.value) { -%>\n<%= rowCreator.format(\"Cell Planned Activity\", \" \"); -%>\n<%= rowCreator.format(`(${networkMetrics.plannedActivity.Cell.value})`, overviewStringToState(networkMetrics.plannedActivity.Cell.status)); -%>\n<% } else { -%>\n<%= rowCreator.format(\"Cell Planned Activity\", \"N/A\"); -%>\n<% } -%>\n<% if (networkMetrics.busyHourPerformance && networkMetrics.busyHourPerformance.Backhaul && networkMetrics.busyHourPerformance.Backhaul.value) { -%>\n<%= rowCreator.format(\"Busy Hour Backhaul Performance\", \" \"); -%>\n<%= rowCreator.format(`(${networkMetrics.busyHourPerformance.Backhaul.value})`, overviewStringToState(networkMetrics.busyHourPerformance.Backhaul.status)); -%>\n<% } else { -%>\n<%= rowCreator.format(\"Busy Hour Backhaul Performance\", \"N/A\"); -%>\n<% } -%>\n<%= rowCreator.format(\"Forecast Backhaul Upgrade Date\", networkMetrics.forecastUpgradeDate && networkMetrics.forecastUpgradeDate.Backhaul &&\n\tnetworkMetrics.forecastUpgradeDate.Backhaul.value ? networkMetrics.forecastUpgradeDate.Backhaul.value : \"N/A\"); -%>\n<% if (networkMetrics.plannedActivity && networkMetrics.plannedActivity.Backhaul && networkMetrics.plannedActivity.Backhaul.value) { -%>\n<%= rowCreator.format(\"Backhaul Planned Activity\", \" \"); -%>\n<%= rowCreator.format(`(${networkMetrics.plannedActivity.Backhaul.value})`, overviewStringToState(networkMetrics.plannedActivity.Backhaul.status)); -%>\n<% } else { -%>\n<%= rowCreator.format(\"Backhaul Planned Activity\", \"N/A\"); -%>\n<% } -%>\n<%      break;\n    case \"FTTP\":\n        operationalStatusMetrics = extractMetrics(healthCategoryMetrics.OperationalStatus);\n        opticalSignalMetrics = extractMetrics(healthCategoryMetrics.OpticalSignal);\n        cpeMetrics = extractMetrics(healthCategoryMetrics.Cpe);\n        NTDMetrics = extractMetrics(healthCategoryMetrics.NTD);\n        dropoutMetrics = extractMetrics(healthCategoryMetrics.DropOuts, true);\n\n        let lastStatusChangeFTTP = operationalStatusMetrics.lastStatusChange && operationalStatusMetrics.lastStatusChange.value ? new Date(operationalStatusMetrics.lastStatusChange.value) : null;\n\n        if (operationalStatusMetrics.uptime) {\n            uptimeMetric = convertUptimeMetricTime(operationalStatusMetrics.uptime.value, operationalStatusMetrics.uptime.unit);\n        }\n\n        outageMetrics = extractMetrics(healthCategoryMetrics.Outage);\n-%>\n--Operational Status--\n<%= rowCreator.format(\"Service State\", operationalStatusMetrics.serviceState ? operationalStatusMetrics.serviceState.value : null); -%>\n<%= rowCreator.format(\"Last Status Change\", lastStatusChangeFTTP !== null && !isNaN(lastStatusChangeFTTP) ? lastStatusChangeFTTP.toISOString() : (operationalStatusMetrics.lastStatusChange ? operationalStatusMetrics.lastStatusChange.value : null)); -%>\n<%= rowCreator.format(\"Last Change Reason\", operationalStatusMetrics.lastChangeReason ? operationalStatusMetrics.lastChangeReason.value : null); -%>\n<%= rowCreator.format(\"Uptime\", uptimeMetric); -%>\n\n--Optical Signal--\n<%= rowCreator.format(\"Status\", opticalSignalMetrics.status ? opticalSignalMetrics.status.value : null); -%>\n\n--CPE--\n<%= rowCreator.format(\"MAC Address\", cpeMetrics.macAddress ? cpeMetrics.macAddress.value : null); -%>\n\n--NTD--\n<%= rowCreator.format(\"ID\", NTDMetrics.ntdId ? NTDMetrics.ntdId.value : null); -%>\n<%= rowCreator.format(\"Port ID\", NTDMetrics.portId ? NTDMetrics.portId.value : null); -%>\n<%= rowCreator.format(\"Port State\", NTDMetrics.portState ? NTDMetrics.portState.value : null); -%>\n<%= rowCreator.format(\"Serial Number\", NTDMetrics.serialNumber ? NTDMetrics.serialNumber.value : null); -%>\n<%= rowCreator.format(\"Make\", NTDMetrics.make && NTDMetrics.make.value ? NTDMetrics.make.value : \"N/A\"); -%>\n<%= rowCreator.format(\"Install Location\", NTDMetrics.installLocation && NTDMetrics.installLocation.value ? NTDMetrics.installLocation.value : \"N/A\"); -%>\n<%= rowCreator.format(\"Current Speed and Duplex\", NTDMetrics.currentSpeedAndDuplex && NTDMetrics.currentSpeedAndDuplex.value ? NTDMetrics.currentSpeedAndDuplex.value : \"N/A\"); -%>\n\n--Dropouts--\n<%= rowCreator.format(\"\", [\"Today\", \"Yesterday\", \"2 Days\", \"7 Days\", \"30 Days\"], true); -%>\n<%= rowCreator.format(\"Total\", setMetricArray(dropoutMetrics.total, FTTP_DROPOUT_METRIC_NAMES), true); -%>\n<%= rowCreator.format(\"Initiated\", setMetricArray(dropoutMetrics.initiated, FTTP_DROPOUT_METRIC_NAMES), true); -%>\n<%= rowCreator.format(\"Network\", setMetricArray(dropoutMetrics.network, FTTP_DROPOUT_METRIC_NAMES), true); -%>\n<%= rowCreator.format(\"Unexpected\", setMetricArray(dropoutMetrics.unexpected, FTTP_DROPOUT_METRIC_NAMES), true); -%>\n\n--Outages--\n<%= rowCreator.format(\"Current Outage\", outageMetrics.currentOutage && outageMetrics.currentOutage.value ? (outageMetrics.currentOutage.value === \"true\" ? \"Yes\" : \"No\") : \"N/A\"); -%>\n<%= rowCreator.format(\"Planned outage ID\", outageMetrics.plannedOutageId && outageMetrics.plannedOutageId.value ? outageMetrics.plannedOutageId.value : null); -%>\n<%= rowCreator.format(\"Unplanned outage ID\", outageMetrics.unplannedOutageIds && outageMetrics.unplannedOutageIds.value ? outageMetrics.unplannedOutageIds.value : null); -%>\n<%= rowCreator.format(\"Network activity ID\", outageMetrics.networkActivityId && outageMetrics.networkActivityId.value ? outageMetrics.networkActivityId.value : null); -%>\n<%      break;\n    default:\n        break;\n} -%>\n<% if (Array.isArray(nextAction) && nextAction.length > 0) { -%>\n\n--NBNCO Next Best Action--\n<% for (let i = 0; i < nextAction.length; i++) {\nlet nextActionDescription = nextAction[i] && nextAction[i].description;\n-%>\n<%= rowCreator.format(\"Code\", nextAction[i] && nextAction[i].code ? nextAction[i].code : null); -%>\n<%= rowCreator.format(\"Message\", typeof nextActionDescription === 'string' ? splitStringToLines(nextActionDescription) : null); -%>\n<% if (i == nextAction.length - 1) { break; } -%>\n\n<% } -%>\n<% } -%>\n\n<% } else { -%>\nNBN Service Health source was not run\n<% } -%>"}, {"name": "NBNServiceHealthSummaryText", "active": true, "title": "NBN Service Health Summary (Text)", "description": "Summary of NBN Service Health results", "allowedSystemsToAppend": ["ServiceCentral"], "suite": [], "templateType": "Service Check", "formatType": "Text", "listCondition": "let listCondition = false;\nif (s['NBN - Service Health']) {\n    listCondition = true;\n}\nlistCondition;", "defaultPriority": 0, "template": "<% \nlet serviceHealthData = s['NBN - Service Health'] ? s['NBN - Service Health'] : null;\nlet nbnAccessType = data.nbnAccessType ? data.nbnAccessType : ((r.MDR064 && r.MDR064.AccessType) ? r.MDR064.AccessType : null);\n\nfunction makeColumnString(fieldName, fieldValue, isRow) {\n    const PAD_LENGTH = 48;\n    const ROW_LENGTH = 16;\n    if (typeof fieldValue === 'undefined' || fieldValue === null) {\n        // If the field value is null / undefined, return an empty string\n        return '';\n    } else {\n        if (Array.isArray(fieldValue)) {\n            if (fieldValue.length) {\n                if (isRow) {\n                    let rowString = fieldName.padEnd(PAD_LENGTH);\n                    for (let i = 0; i < fieldValue.length; i++) {\n                        rowString += fieldValue[i].padEnd(ROW_LENGTH);\n                    }\n                    rowString += \"\\n\";\n                    return rowString;\n                } else {\n                    let columnString = (fieldName + \":\").padEnd(PAD_LENGTH) + fieldValue[0] + \"\\n\";\n                    for (let i = 1; i < fieldValue.length; i++) {\n                        columnString += \"\".padEnd(PAD_LENGTH) + fieldValue[i] + \"\\n\";\n                    }\n                    return columnString;\n                }\n\n            } else {\n                return (fieldName + \":\").padEnd(PAD_LENGTH) + \"None\\n\";\n            }\n        } else {\n            return (fieldName + \":\").padEnd(PAD_LENGTH) + fieldValue + \"\\n\";\n        }\n    }\n}\n\n// Maps \"Green\" and \"Red\" values from overviewIndicator to other values\nfunction overviewStringToState(overviewState) {\n    if (typeof overviewState !== \"string\") {\n        return null;\n    }\n\n    switch(overviewState) {\n        case \"Green\":\n            return \"OK\";\n        case \"Red\":\n            return \"Failed\";\n        case \"Amber\":\n            return \"Warning\";\n        default:\n            return \"Unknown\";\n    }\n}\n\n\n// Helper function to extract key value pairs from healthCategoryItem array in NBN Service Health response\n// Also supports extracting values that require 2 input keys, \"@type\" and \"id\" (eg. the Dropouts health category)\nfunction extractMetrics(healthCategoryItems, includeTypeKey=false) {\n    let metrics = {};\n\n    if (!Array.isArray(healthCategoryItems)) {\n        return metrics;\n    }\n\n    if (includeTypeKey) {\n        for (let i = 0; i < healthCategoryItems.length; i++) {\n            if (healthCategoryItems[i] && healthCategoryItems[i].id && healthCategoryItems[i]['@type']) {\n                if (!metrics[healthCategoryItems[i].id]) {\n                    metrics[healthCategoryItems[i].id] = {};\n                }\n                metrics[healthCategoryItems[i].id][healthCategoryItems[i]['@type']] = healthCategoryItems[i];\n            }\n        }\n    } else {\n        for (let i = 0; i < healthCategoryItems.length; i++) {\n            if (healthCategoryItems[i] && healthCategoryItems[i].id) {\n                metrics[healthCategoryItems[i].id] = healthCategoryItems[i];\n            }\n        }\n    }\n\n    return metrics;\n}\n\nfunction setMetricArray(metrics, metricNames) {\n    let metricsArray = [];\n    if (metrics) {\n        for (let i = 0; i < metricNames.length; i++) {\n            let metricName = metricNames[i];\n            metricsArray.push(metrics[metricName] ? metrics[metricName].value : \"N/A\");\n        }\n    } else {\n        metricsArray = Array(metricNames.length).fill(\"N/A\");\n    }\n    return metricsArray;\n}\n\nfunction convertUptimeMetricTime(metricValue, metricUnit) {\n    let uptimeMetric = `${metricValue} ${metricUnit}`;\n    // If the unit is seconds, attempts to do a conversion to hours / minutes / seconds\n    if (metricUnit === 'Seconds') {\n        let uptimeValue = parseInt(metricValue);\n        if (!isNaN(uptimeValue)) {\n            let uptimeHours = Math.floor(uptimeValue / 3600);\n            let uptimeMinutes = Math.floor((uptimeValue % 3600) / 60);\n            let uptimeSeconds = uptimeValue % 60;\n            \n            uptimeMetric = `${uptimeHours} Hours ${uptimeMinutes} Minutes ${uptimeSeconds} Seconds`;\n        }\n    }\n    \n    return uptimeMetric\n}\n-%>\n<% if (serviceHealthData) {\nlet overviewIndicator = serviceHealthData.overviewIndicator ? serviceHealthData.overviewIndicator : {};\nlet healthCategory = serviceHealthData.healthCategory && Array.isArray(serviceHealthData.healthCategory) ? serviceHealthData.healthCategory : [];\nlet hriId = s['NBN - Service Health'] && s['NBN - Service Health'].id ? s['NBN - Service Health'].id : \"Not found\";\nlet avcId = s['NBN - Service Health'] && s['NBN - Service Health'].avcId ? s['NBN - Service Health'].avcId : \"Not found\";\n-%>\n<%= makeColumnString(\"NBN Service Health\", hriId); -%>\n<%= makeColumnString(\"AVC\", avcId); -%>\n<%= makeColumnString(\"Connectivity\", overviewStringToState(overviewIndicator.connectivity)); -%>\n<%= makeColumnString(\"Performance\", overviewStringToState(overviewIndicator.performance)); -%>\n<%= makeColumnString(\"Stability\", overviewStringToState(overviewIndicator.stability)); -%>\n<%\nlet healthCategoryMetrics = {};\nfor (let i = 0; i < healthCategory.length; i++) {\n    if (healthCategory[i] && healthCategory[i].type && Array.isArray(healthCategory[i].healthCategoryItem)) {\n        healthCategoryMetrics[healthCategory[i].type] = healthCategory[i].healthCategoryItem;\n    }\n}\n\nlet operationalStatusMetrics;\nlet opticalSignalMetrics;\nlet cpeMetrics;\nlet NCDMetrics;\nlet NTDMetrics;\nlet HFCSignalMetrics;\nlet networkUtilisationMetrics;\nlet dropoutMetrics;\nlet heavyUserMetrics;\nlet networkMetrics;\nlet inHomeWiringMetrics;\nlet plannedNetworkUpgradeMetrics;\nlet uptimeMetric;\n\nconst FTTB_FTTN_DROPOUT_METRIC_NAMES = [\"DoToday\", \"DoYesterday\", \"Do2Days\", \"Do7Days\", \"Do30Days\"];\nconst HFC_DROPOUT_METRIC_NAMES = [\"DoToday\", \"DoYesterday\", \"Do2Days\", \"Do7Days\", \"Do30Days\"];\nconst FTTC_DROPOUT_METRIC_NAMES = [\"DoYesterday\", \"Do2Days\", \"Do7Days\", \"Do30Days\"];\nconst FTTP_DROPOUT_METRIC_NAMES = [\"DoToday\", \"DoYesterday\", \"Do2Days\", \"Do7Days\", \"Do30Days\"];\n-%>\n<%\nswitch (nbnAccessType) {\n    case \"FTTN\":\n    case \"FTTB\": \n        operationalStatusMetrics = extractMetrics(healthCategoryMetrics.OperationalStatus);\n        cpeMetrics = extractMetrics(healthCategoryMetrics.Cpe);\n        inHomeWiringMetrics = extractMetrics(healthCategoryMetrics.InHomeWiring);\n        dropoutMetrics = extractMetrics(healthCategoryMetrics.Dropouts, true);\n%>\n--Operational Status--\n<%= makeColumnString(\"Service State\", operationalStatusMetrics.serviceState ? operationalStatusMetrics.serviceState.value : null); -%>\n<%= makeColumnString(\"Line Status Change\", operationalStatusMetrics.lineStatusChange ? operationalStatusMetrics.lineStatusChange.value : null); -%>\n\n--CPE--\n<%= makeColumnString(\"MAC Address\", cpeMetrics.macAddress ? cpeMetrics.macAddress.value : null); -%>\n<%= makeColumnString(\"Make\", cpeMetrics.make ? cpeMetrics.make.value : null); -%>\n<%= makeColumnString(\"Model\", cpeMetrics.model ? cpeMetrics.model.value : null); -%>\n<%= makeColumnString(\"Serial Number\", cpeMetrics.serialNo ? cpeMetrics.serialNo.value : null); -%>\n<%= makeColumnString(\"Firmware Version\", cpeMetrics.firmwareVersion ? cpeMetrics.firmwareVersion.value : null); -%>\n\n--In-home Wiring--\n<%= makeColumnString(\"In-Home Wiring\", inHomeWiringMetrics.InHomeWiring ? inHomeWiringMetrics.InHomeWiring.value : null); -%>\n\n--Dropouts--\n<%= makeColumnString(\"\", [\"Today\", \"Yesterday\", \"2 Days\", \"7 Days\", \"30 Days\"], true); -%>\n<%= makeColumnString(\"Total\", setMetricArray(dropoutMetrics.total, FTTB_FTTN_DROPOUT_METRIC_NAMES), true); -%>\n<%= makeColumnString(\"Initiated\", setMetricArray(dropoutMetrics.initiated, FTTB_FTTN_DROPOUT_METRIC_NAMES), true); -%>\n<%= makeColumnString(\"Network\", setMetricArray(dropoutMetrics.network, FTTB_FTTN_DROPOUT_METRIC_NAMES), true); -%>\n<%= makeColumnString(\"Unexpected\", setMetricArray(dropoutMetrics.unexpected, FTTB_FTTN_DROPOUT_METRIC_NAMES), true); -%>\n<%      break;\n    case \"HFC\":\n        operationalStatusMetrics = extractMetrics(healthCategoryMetrics.OperationalStatus);\n        cpeMetrics = extractMetrics(healthCategoryMetrics.Cpe);\n        NTDMetrics = extractMetrics(healthCategoryMetrics.NTD);\n        HFCSignalMetrics = extractMetrics(healthCategoryMetrics.Speed);\n        dropoutMetrics = extractMetrics(healthCategoryMetrics.Dropouts, true);\n                \n        let hfcTimeOfLastDropout = null;\n        if (dropoutMetrics.timeOfLastDropout && dropoutMetrics.timeOfLastDropout.DoMain && dropoutMetrics.timeOfLastDropout.DoMain.value) {\n            hfcTimeOfLastDropout = new Date(dropoutMetrics.timeOfLastDropout.DoMain.value);\n        }\n        \n        if (operationalStatusMetrics.uptime) {\n            uptimeMetric = convertUptimeMetricTime(operationalStatusMetrics.uptime.value, operationalStatusMetrics.uptime.unit);\n        }\n%>\n--Operational Status--\n<%= makeColumnString(\"Service State\", operationalStatusMetrics.serviceState ? operationalStatusMetrics.serviceState.value : null); -%>\n<%= makeColumnString(\"Last Status Change\", operationalStatusMetrics.lastStatusChange ? operationalStatusMetrics.lastStatusChange.value : null); -%>\n<%= makeColumnString(\"Uptime\", uptimeMetric); -%>\n\n--CPE--\n<%= makeColumnString(\"MAC Address\", cpeMetrics.macAddress ? cpeMetrics.macAddress.value : null); -%>\n\n--NTD--\n<%= makeColumnString(\"Port State\", NTDMetrics.portState ? NTDMetrics.portState.value : \"Not found\"); -%>\n<%= makeColumnString(\"Port ID\", NTDMetrics.portId ? NTDMetrics.portId.value : null); -%>\n<%= makeColumnString(\"Model\", NTDMetrics.model ? NTDMetrics.model.value : null); -%>\n<%= makeColumnString(\"MAC Address\", NTDMetrics.macAddress ? NTDMetrics.macAddress.value : \"N/A\"); -%>\n<%= makeColumnString(\"Service Configuration\", NTDMetrics.serviceConfiguration ? NTDMetrics.serviceConfiguration.value : \"N/A\"); -%>\n\n--HFC Signal--\n<%= makeColumnString(\"Status\", HFCSignalMetrics.status ? HFCSignalMetrics.status.value : null); -%>\n\n--Dropouts--\n<%= makeColumnString(\"\", [\"Today\", \"Yesterday\", \"2 Days\", \"7 Days\", \"30 Days\"], true); -%>\n<%= makeColumnString(\"Total\", setMetricArray(dropoutMetrics.total, HFC_DROPOUT_METRIC_NAMES), true); -%>\n<%= makeColumnString(\"Initiated\", setMetricArray(dropoutMetrics.initiated, HFC_DROPOUT_METRIC_NAMES), true); -%>\n<%= makeColumnString(\"Network\", setMetricArray(dropoutMetrics.network, HFC_DROPOUT_METRIC_NAMES), true); -%>\n<%= makeColumnString(\"Unexpected\", setMetricArray(dropoutMetrics.unexpected, HFC_DROPOUT_METRIC_NAMES), true); -%>\n<%= makeColumnString(\"Time of Last Dropout\", hfcTimeOfLastDropout !== null && !isNaN(hfcTimeOfLastDropout) ? hfcTimeOfLastDropout.toISOString() : \"N/A\"); -%>\n<%      break;\n    case \"FTTC\":\n        operationalStatusMetrics = extractMetrics(healthCategoryMetrics.OperationalStatus);\n        cpeMetrics = extractMetrics(healthCategoryMetrics.Cpe);\n        NCDMetrics = extractMetrics(healthCategoryMetrics.Ncd);\n        inHomeWiringMetrics = extractMetrics(healthCategoryMetrics.InHomeWiring);\n        dropoutMetrics = extractMetrics(healthCategoryMetrics.Dropouts, true);\n        \n        let lastStatusChangeFTTC = operationalStatusMetrics.lastStatusChange && operationalStatusMetrics.lastStatusChange.value ? new Date(operationalStatusMetrics.lastStatusChange.value) : null;\n%>\n--Operational Status--\n<%= makeColumnString(\"Service State\", operationalStatusMetrics.serviceState ? operationalStatusMetrics.serviceState.value : null); -%>\n<%= makeColumnString(\"Last Status Change\", lastStatusChangeFTTC !== null && !isNaN(lastStatusChangeFTTC) ? lastStatusChangeFTTC.toISOString() : (operationalStatusMetrics.lastStatusChange ? operationalStatusMetrics.lastStatusChange.value : null)); -%>\n<%= makeColumnString(\"Reverse Power State\", operationalStatusMetrics.reversePowerState ? operationalStatusMetrics.reversePowerState.value : null); -%>\n\n--CPE--\n<%= makeColumnString(\"MAC Address\", cpeMetrics.macAddress ? cpeMetrics.macAddress.value : null); -%>\n\n--NCD--\n<%= makeColumnString(\"Port State\", NCDMetrics.portState ? NCDMetrics.portState.value : null); -%>\n<%= makeColumnString(\"Port ID\", NCDMetrics.portId ? NCDMetrics.portId.value : null); -%>\n<%= makeColumnString(\"MAC Address\", NCDMetrics.macAddress ? NCDMetrics.macAddress.value : null); -%>\n<%= makeColumnString(\"Make and Model\", NCDMetrics.make ? NCDMetrics.make.value : null); -%>\n<%= makeColumnString(\"Operational Speed and Duplex\", NCDMetrics.currentSpeedAndDuplex ? NCDMetrics.currentSpeedAndDuplex.value : null); -%>\n\n--In-home Wiring--\n<%= makeColumnString(\"In-home Wiring\", inHomeWiringMetrics.inHomeWiring ? inHomeWiringMetrics.inHomeWiring.value : null); -%>\n<%= makeColumnString(\"Line Impairment\", inHomeWiringMetrics.lineImpairments ? inHomeWiringMetrics.lineImpairments.value : null); -%>\n\n--Dropouts--\n<%= makeColumnString(\"\", [\"Yesterday\", \"2 Days\", \"7 Days\", \"30 Days\"], true); -%>\n<%= makeColumnString(\"Total\", setMetricArray(dropoutMetrics.total, FTTC_DROPOUT_METRIC_NAMES), true); -%>\n<%= makeColumnString(\"Initiated\", setMetricArray(dropoutMetrics.initiated, FTTC_DROPOUT_METRIC_NAMES), true); -%>\n<%= makeColumnString(\"Network\", setMetricArray(dropoutMetrics.network, FTTC_DROPOUT_METRIC_NAMES), true); -%>\n<%= makeColumnString(\"Unexpected\", setMetricArray(dropoutMetrics.unexpected, FTTC_DROPOUT_METRIC_NAMES), true); -%>\n<%      break;\n    case \"Wireless\":\n        operationalStatusMetrics = extractMetrics(healthCategoryMetrics.OperationalStatus);\n        NTDMetrics = extractMetrics(healthCategoryMetrics.NTD);\n        heavyUserMetrics = extractMetrics(healthCategoryMetrics.FUP, true);\n        networkMetrics = extractMetrics(healthCategoryMetrics.Network, true);\n        let heavyUserIndicators = [];\n        if (heavyUserMetrics.heavyUser) {\n        \theavyUserIndicators.push(heavyUserMetrics.heavyUser.Downstream ? heavyUserMetrics.heavyUser.Downstream.value : \"N/A\");\n        \theavyUserIndicators.push(heavyUserMetrics.heavyUser.Upstream ? heavyUserMetrics.heavyUser.Upstream.value : \"N/A\");\n        } else {\n        \theavyUserIndicators = Array(2).fill(\"N/A\");\n        }\n%>\n--Operational Status--\n<%= makeColumnString(\"Service State\", operationalStatusMetrics.serviceState ? operationalStatusMetrics.serviceState.value : null); -%>\n\n--NTD--\n<%= makeColumnString(\"NTD Version\", NTDMetrics.ntdVersion ? NTDMetrics.ntdVersion.value : null); -%>\n\n--Heavy User Indicator--\n<%= makeColumnString(\"\", [\"Downstream\", \"Upstream\"], true); -%>\n<%= makeColumnString(\"Heavy User\", heavyUserIndicators, true); -%>\n\n--Network--\n<% if (networkMetrics.busyHourPerformance && networkMetrics.busyHourPerformance.Cell && networkMetrics.busyHourPerformance.Cell.value) { -%>\n<%= makeColumnString(\"Busy Hour Cell Performance\", \" \"); -%>\n<%= makeColumnString(`(${networkMetrics.busyHourPerformance.Cell.value})`, overviewStringToState(networkMetrics.busyHourPerformance.Cell.status)); -%>\n<% } else { -%>\n<%= makeColumnString(\"Busy Hour Cell Performance\", \"N/A\"); -%>\n<% } -%>\n<%= makeColumnString(\"Forecast Cell Upgrade Date\", networkMetrics.forecastUpgradeDate && networkMetrics.forecastUpgradeDate.Cell &&\n\tnetworkMetrics.forecastUpgradeDate.Cell.value ? networkMetrics.forecastUpgradeDate.Cell.value : \"N/A\"); -%>\n<% if (networkMetrics.plannedActivity && networkMetrics.plannedActivity.Cell && networkMetrics.plannedActivity.Cell.value) { -%>\n<%= makeColumnString(\"Cell Planned Activity\", \" \"); -%>\n<%= makeColumnString(`(${networkMetrics.plannedActivity.Cell.value})`, overviewStringToState(networkMetrics.plannedActivity.Cell.status)); -%>\n<% } else { -%>\n<%= makeColumnString(\"Cell Planned Activity\", \"N/A\"); -%>\n<% } -%>\n<% if (networkMetrics.busyHourPerformance && networkMetrics.busyHourPerformance.Backhaul && networkMetrics.busyHourPerformance.Backhaul.value) { -%>\n<%= makeColumnString(\"Busy Hour Backhaul Performance\", \" \"); -%>\n<%= makeColumnString(`(${networkMetrics.busyHourPerformance.Backhaul.value})`, overviewStringToState(networkMetrics.busyHourPerformance.Backhaul.status)); -%>\n<% } else { -%>\n<%= makeColumnString(\"Busy Hour Backhaul Performance\", \"N/A\"); -%>\n<% } -%>\n<%= makeColumnString(\"Forecast Backhaul Upgrade Date\", networkMetrics.forecastUpgradeDate && networkMetrics.forecastUpgradeDate.Backhaul &&\n\tnetworkMetrics.forecastUpgradeDate.Backhaul.value ? networkMetrics.forecastUpgradeDate.Backhaul.value : \"N/A\"); -%>\n<% if (networkMetrics.plannedActivity && networkMetrics.plannedActivity.Backhaul && networkMetrics.plannedActivity.Backhaul.value) { -%>\n<%= makeColumnString(\"Backhaul Planned Activity\", \" \"); -%>\n<%= makeColumnString(`(${networkMetrics.plannedActivity.Backhaul.value})`, overviewStringToState(networkMetrics.plannedActivity.Backhaul.status)); -%>\n<% } else { -%>\n<%= makeColumnString(\"Backhaul Planned Activity\", \"N/A\"); -%>\n<% } -%>\n<%      break;\n    case \"FTTP\":\n        operationalStatusMetrics = extractMetrics(healthCategoryMetrics.OperationalStatus);\n        opticalSignalMetrics = extractMetrics(healthCategoryMetrics.OpticalSignal);\n        cpeMetrics = extractMetrics(healthCategoryMetrics.Cpe);\n        NTDMetrics = extractMetrics(healthCategoryMetrics.NTD);\n        dropoutMetrics = extractMetrics(healthCategoryMetrics.DropOuts, true);\n        \n        let lastStatusChangeFTTP = operationalStatusMetrics.lastStatusChange && operationalStatusMetrics.lastStatusChange.value ? new Date(operationalStatusMetrics.lastStatusChange.value) : null;\n\n        if (operationalStatusMetrics.uptime) {\n            uptimeMetric = convertUptimeMetricTime(operationalStatusMetrics.uptime.value, operationalStatusMetrics.uptime.unit);\n        }\n%>\n--Operational Status--\n<%= makeColumnString(\"Service State\", operationalStatusMetrics.serviceState ? operationalStatusMetrics.serviceState.value : null); -%>\n<%= makeColumnString(\"Last Status Change\", lastStatusChangeFTTP !== null && !isNaN(lastStatusChangeFTTP) ? lastStatusChangeFTTP.toISOString() : (operationalStatusMetrics.lastStatusChange ? operationalStatusMetrics.lastStatusChange.value : null)); -%>\n<%= makeColumnString(\"Last Change Reason\", operationalStatusMetrics.lastChangeReason ? operationalStatusMetrics.lastChangeReason.value : null); -%>\n<%= makeColumnString(\"Uptime\", uptimeMetric); -%>\n\n--Optical Signal--\n<%= makeColumnString(\"Status\", opticalSignalMetrics.status ? opticalSignalMetrics.status.value : null); -%>\n\n--CPE--\n<%= makeColumnString(\"MAC Address\", cpeMetrics.macAddress ? cpeMetrics.macAddress.value : null); -%>\n\n--NTD--\n<%= makeColumnString(\"ID\", NTDMetrics.ntdId ? NTDMetrics.ntdId.value : null); -%>\n<%= makeColumnString(\"Port ID\", NTDMetrics.portId ? NTDMetrics.portId.value : null); -%>\n<%= makeColumnString(\"Port State\", NTDMetrics.portState ? NTDMetrics.portState.value : null); -%>\n<%= makeColumnString(\"Serial Number\", NTDMetrics.serialNumber ? NTDMetrics.serialNumber.value : null); -%>\n<%= makeColumnString(\"Make\", NTDMetrics.make && NTDMetrics.make.value ? NTDMetrics.make.value : \"N/A\"); -%>\n<%= makeColumnString(\"Install Location\", NTDMetrics.installLocation && NTDMetrics.installLocation.value ? NTDMetrics.installLocation.value : \"N/A\"); -%>\n<%= makeColumnString(\"Current Speed and Duplex\", NTDMetrics.currentSpeedAndDuplex && NTDMetrics.currentSpeedAndDuplex.value ? NTDMetrics.currentSpeedAndDuplex.value : \"N/A\"); -%>\n\n--Dropouts--\n<%= makeColumnString(\"\", [\"Today\", \"Yesterday\", \"2 Days\", \"7 Days\", \"30 Days\"], true); -%>\n<%= makeColumnString(\"Total\", setMetricArray(dropoutMetrics.total, FTTP_DROPOUT_METRIC_NAMES), true); -%>\n<%= makeColumnString(\"Initiated\", setMetricArray(dropoutMetrics.initiated, FTTP_DROPOUT_METRIC_NAMES), true); -%>\n<%= makeColumnString(\"Network\", setMetricArray(dropoutMetrics.network, FTTP_DROPOUT_METRIC_NAMES), true); -%>\n<%= makeColumnString(\"Unexpected\", setMetricArray(dropoutMetrics.unexpected, FTTP_DROPOUT_METRIC_NAMES), true); -%>\n<%      break;\n    default:\n        break;\n} %>\n\nMerge Result Link: <%= mergeUrl %>/serviceCheck/view/html/<%= data.id %>\n<% } else { -%>\nNo NBN Service Health results found in current record\n<% } %>"}, {"name": "NBNSummaryText", "active": true, "title": "NBN Summary (Text)", "description": "NBN Summary default template", "allowedSystemsToAppend": ["ServiceCentral"], "suite": [], "templateType": "Service Check", "formatType": "Text", "listCondition": "data.carriageType === 'NBN';", "defaultPriority": 2, "template": "<% if (data.carriageType === 'NBN') { -%>\nNBN Summary\n\n<%- renderTemplate(\"CustomerDetailsModule\", { RASSPSourceName: \"RASSP\" }); %>\n<%- renderTemplate(\"DeviceDetailsModule\", { forMDN: false, RASSPSourceName: \"RASSP\" }); %>\n<%- renderTemplate(\"RassOrdersModule\", { showAllOrders: true, includeCancelledOrders: true }); %>\n<%- renderTemplate(\"TicketingCasesModule\"); %>\n<%- renderTemplate(\"OutagesModule\"); %>\n<%- renderTemplate(\"PingResultsModule\", { includeCMIPing: false, includeVPNPing: true, includeVPNPingFullMTU: false }); %>\n<%- renderTemplate(\"NBNSyncRateModule\"); %>\n<%- renderTemplate(\"NBNServiceHealthModule\"); %>\n<% if (r.MDR009 && r.MDR009.manageCategory === 'MDN') { -%>\n\n<%- renderTemplate(\"MDNInfoModule\", { showCarriageSectionHeader: false }); %>\n<%- renderTemplate(\"DeviceDetailsModule\", { forMDN: true, RASSPSourceName: \"RASSP\" }); %>\n<%- renderTemplate(\"DeviceStatusModule\"); %>\n<%- renderTemplate(\"AccessStatusModule\"); %>\n<%- renderTemplate(\"AccessInterfaceThroughputModule\", { interfaceMetrics: [\"RXBS\", \"TXBS\"] }); %>\n<% } -%>\n<%- renderTemplate(\"OutcomesModule\"); %>\n<%- renderTemplate(\"MessageBucketModule\"); %>\nMerge Result Link: <%= mergeUrl %>/serviceCheck/view/html/<%= data.id %>\n<% } else { -%>\nRecord is not for a NBN service\n<% } %>"}, {"name": "NBNSyncRateModule", "active": true, "title": "NBN Sync Rate (Module)", "description": "Module to display attained and assured line rate for FTTB, FTTC and FTTN NBN services", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<% if ([\"FTTB\", \"FTTC\", \"FTTN\"].includes(data.nbnAccessType) && s[\"NBN - Service Health\"] && Array.isArray(s[\"NBN - Service Health\"].healthCategory)) {\nlet rowCreator = new RowCreator();\n\nfunction extractMetrics(healthCategoryItems, includeTypeKey=false) {\n    let metrics = {};\n\n    if (!Array.isArray(healthCategoryItems)) {\n        return metrics;\n    }\n\n    if (includeTypeKey) {\n        for (let i = 0; i < healthCategoryItems.length; i++) {\n            if (healthCategoryItems[i] && healthCategoryItems[i].id && healthCategoryItems[i]['@type']) {\n                if (!metrics[healthCategoryItems[i].id]) {\n                    metrics[healthCategoryItems[i].id] = {};\n                }\n                metrics[healthCategoryItems[i].id][healthCategoryItems[i]['@type']] = healthCategoryItems[i];\n            }\n        }\n    } else {\n        for (let i = 0; i < healthCategoryItems.length; i++) {\n            if (healthCategoryItems[i] && healthCategoryItems[i].id) {\n                metrics[healthCategoryItems[i].id] = healthCategoryItems[i];\n            }\n        }\n    }\n\n    return metrics;\n}\n\nfunction extractLineRateValues(speedMetrics, keyName) {\n    if (!speedMetrics[keyName]) {\n        return [\"N/A\", \"N/A\"];\n    }\n    \n    return [\n        speedMetrics[keyName].Downstream && speedMetrics[keyName].Downstream.value !== undefined && speedMetrics[keyName].Downstream.value !== null ?\n        `${speedMetrics[keyName].Downstream.value}${speedMetrics[keyName].Downstream.unit ? speedMetrics[keyName].Downstream.unit : ''}` : 'N/A',\n        speedMetrics[keyName].Upstream && speedMetrics[keyName].Upstream.value !== undefined && speedMetrics[keyName].Upstream.value !== null ?\n        `${speedMetrics[keyName].Upstream.value}${speedMetrics[keyName].Upstream.unit ? speedMetrics[keyName].Upstream.unit : ''}` : 'N/A'\n    ];\n}\n\nlet serviceHealthSpeed = s[\"NBN - Service Health\"].healthCategory.find(category => {\n    return category && category.type === \"Speed\";\n});\n\nlet actualLineRates = null;\nlet assuredLineRates = null;\nlet attainableLineRates = null;\nlet attainableLineRates2Hours = null;\nlet attainableLineRates7Days = null;\nlet actualLineRates7Days = null;\n\nif (serviceHealthSpeed) {\n    let speedMetrics = extractMetrics(serviceHealthSpeed.healthCategoryItem, true);\n\n    if (data.nbnAccessType === \"FTTN\" || data.nbnAccessType === \"FTTB\") {\n        actualLineRates = extractLineRateValues(speedMetrics, \"ActualLineRate\");\n        assuredLineRates = extractLineRateValues(speedMetrics, \"AssuredLineRate\");\n        attainableLineRates = extractLineRateValues(speedMetrics, \"AttainableLineRate\");\n        attainableLineRates2Hours = extractLineRateValues(speedMetrics, \"AttainableLineRate2Hrs\");\n        attainableLineRates7Days = extractLineRateValues(speedMetrics, \"AttainableLineRate7Days\");\n    } else {\n        actualLineRates = extractLineRateValues(speedMetrics, \"actualLineRate\");\n        assuredLineRates = extractLineRateValues(speedMetrics, \"assuredLineRate\");\n        actualLineRates7Days = extractLineRateValues(speedMetrics, \"actualLineRate7Days\");\n    }\n}\n\nlet nbnProductSpeed = null;\nif (s.MAGPIE && s.MAGPIE.rawData && typeof s.MAGPIE.rawData === 'object') {\n    for (let key in s.MAGPIE.rawData) {\n        if (s.MAGPIE.rawData[key]['Service Speed']) {\n            nbnProductSpeed = s.MAGPIE.rawData[key]['Service Speed'];\n        }\n    }\n}\n\nif (!nbnProductSpeed &&\n    s.NBNSearch &&\n    s.NBNSearch.avc_data_unidsl_details &&\n    s.NBNSearch.avc_data_unidsl_details.unid_tc4 &&\n    typeof s.NBNSearch.avc_data_unidsl_details.unid_tc4 === 'string') {\n    nbnProductSpeed = s.NBNSearch.avc_data_unidsl_details.unid_tc4;\n}\n-%>\n--NBN Sync Rate--\n<%= rowCreator.format(\"NBN Speed\", nbnProductSpeed ? nbnProductSpeed : \"Not found\"); -%>\n<%= rowCreator.format(\"\", [\"Downstream\", \"Upstream\"], true); -%>\n<%= rowCreator.format(\"Actual Line Rate\", actualLineRates, true); -%>\n<%= rowCreator.format(\"Assured Line Rate\", assuredLineRates, true); -%>\n<% if (data.nbnAccessType === \"FTTN\" || data.nbnAccessType === \"FTTB\") { -%>\n<%= rowCreator.format(\"Attainable Line Rate\", attainableLineRates, true); -%>\n<%= rowCreator.format(\"Attainable Line Rate (2 hours)\", attainableLineRates2Hours, true); -%>\n<%= rowCreator.format(\"Attainable Line Rate (7 days)\", attainableLineRates7Days, true); -%>\n<% } else { -%>\n<%= rowCreator.format(\"Actual Line Rate (7 days)\", actualLineRates, true); -%>\n<% } -%>\n<% } -%>"}, {"name": "OutagesModule", "active": true, "title": "Outages (Module)", "description": "Module to display outages from CMART, CONEN and Hangar - National Power Outage Data", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nlet rowCreator = new RowCreator();\n\nlet plannedOutages = [];\nlet unplannedOutages = [];\nlet powerOutages = [];\nif (s.CMART && Array.isArray(s.CMART.CMART)) {\n    for (let cmartCase of s.CMART.CMART) {\n        plannedOutages.push(`${cmartCase.calm_id} (start: ${cmartCase.start_date}, end: ${cmartCase.end_date})`);\n    }\n}\n\nif (s.CMARTCarriageFnn && Array.isArray(s.CMARTCarriageFnn.CMART)) {\n    for (let cmartCase of s.CMARTCarriageFnn.CMART) {\n        plannedOutages.push(`${cmartCase.calm_id} (start: ${cmartCase.start_date}, end: ${cmartCase.end_date})`);\n    }\n}\n\nif (s.CONEN && s.CONEN.tickets && typeof s.CONEN.tickets === 'object') {\n    for (let conenId in s.CONEN.tickets) {\n        if (s.CONEN.tickets[conenId] && typeof s.CONEN.tickets[conenId] === 'object') {\n            unplannedOutages.push(`${s.CONEN.tickets[conenId].REC_ID} (start: ${s.CONEN.tickets[conenId].EVENT_DATE})`);\n        }\n    }\n}\n\nif (s[\"Hangar - National Power Outage Data\"] && Array.isArray(s[\"Hangar - National Power Outage Data\"].powerData)) {\n    for (let powerDataRecord of s[\"Hangar - National Power Outage Data\"].powerData) {\n        if (powerDataRecord && typeof powerDataRecord === 'object') {\n            powerOutages.push(`${powerDataRecord.reason} (start: ${powerDataRecord.start})`);\n        }\n    }\n}\n-%>\n--Outages--\n<%= rowCreator.format(\"Planned Outage\", plannedOutages.length ? plannedOutages : \"None\"); -%>\n<%= rowCreator.format(\"Unplanned Outage\", unplannedOutages.length ? unplannedOutages : \"None\"); -%>\n<%= rowCreator.format(\"Power Outage\", powerOutages.length ? powerOutages : \"None\"); -%>"}, {"name": "OutcomesModule", "active": true, "title": "Outcomes (Module)", "description": "Module to display outcome headers", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<% if (Array.isArray(data.outcomes) && data.outcomes.length > 0) {\nlet outcomesToDisplay = data.outcomes.filter(outcome => outcome.display === true).sort((outcome1, outcome2) => {\n    if (outcome1.tier !== outcome2.tier) {\n        return outcome1.tier - outcome2.tier;\n    } else {\n        return outcome2.weight - outcome1.weight;\n    }\n});\n-%>\n--Summary--\n<% if (outcomesToDisplay.length) { -%>\n<% for (let outcome of outcomesToDisplay) { -%>\n    <%= outcome.header; %>\n<% } -%>\n<% } else { -%>\nNo summary messages to display\n<% } -%>\n<% } -%>"}, {"name": "OutcomesModuleExcludeTickets", "active": true, "title": "Outcomes Exclude Tickets (Module)", "description": "Module to display outcome headers", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<% if (Array.isArray(data.outcomes) && data.outcomes.length > 0) { -%>\n--Summary--\n<% for (let outcome of data.outcomes) { -%>\n<% if (outcome.display) { -%>\n<% if (outcome.name.indexOf(\"tkt00\") === -1) { -%>\n    <%= outcome.header; %>\n<% } -%>\n<% } -%>\n<% } -%>\n<% } -%>"}, {"name": "PingResultsModule", "active": true, "title": "<PERSON> (Module)", "description": "Module to display Ping from IMS network, VPN Ping and VPN Ping Full MTU results", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nlet rowCreator = new RowCreator();\n\n-%>\n--Ping Results--\n<% if (includeCMIPing) { -%>\n<%= rowCreator.format(\"Device Ping from IMS Network\", r.MTR007 ? (r.MTR007.result === \"OK\" ? \"OK\" : \"Not OK\") : \"Not found\"); -%>\n<% } -%>\n<%\nfunction extractVPNPingResults(serviceCheck) {\n    let pingResult = null;\n    let pingIP = null;\n\n    if (serviceCheck) {\n        if (serviceCheck.sourcesData && serviceCheck.sourcesData.VPNPING && serviceCheck.sourcesData.VPNPING.VPNPing && typeof serviceCheck.sourcesData.VPNPING.VPNPing.result === 'string') {\n            let match = serviceCheck.sourcesData.VPNPING.VPNPing.result.match(/(?<packetsTransmitted>\\d+)\\s+packets transmitted.\\s+(?<packetsReceived>\\d+)\\s+packets received.\\s+(?<packetLossPercentage>\\d+)% packet loss/);\n    \n            if (match) {\n                vpnPingServiceCheck = data;\n    \n                let packetsTransmitted = parseInt(match.groups.packetsTransmitted);\n                let packetsReceived = parseInt(match.groups.packetsReceived);\n                \n                if (packetsTransmitted > 0) {\n                    if (packetsReceived === packetsTransmitted) {\n                        pingResult = \"Success\";\n                    } else {\n                        pingResult = \"Fail\";\n                    }\n                }\n            }\n        }\n    \n        if (serviceCheck.rulesData && serviceCheck.rulesData.MDR023 && serviceCheck.rulesData.MDR023.CPEIP) {\n            if (!serviceCheck.rulesData.MDR023.FAE) {\n                pingIP = ` (WAN IP: ${serviceCheck.rulesData.MDR023.CPEIP})`;\n            } else {\n                pingIP = ` (FAE IP: ${serviceCheck.rulesData.MDR023.CPEIP})`;\n            }\n        }\n    }\n\n    return [pingResult, pingIP];\n}\n\nif (includeVPNPing) {\n    let vpnPingResult = null;\n    let vpnPingIP = null;\n    \n    let allServiceChecks = [data];\n    if (Array.isArray(data.generatedServiceChecks)) {\n        allServiceChecks.push(...data.generatedServiceChecks);\n    }\n\n    for (let serviceCheck of allServiceChecks) {\n        [vpnPingResult, vpnPingIP] = extractVPNPingResults(serviceCheck);\n\n        // Won't use PING result from another service check if valid result is found\n        if (vpnPingResult) {\n            break;\n        }\n    }\n-%>\n<%= rowCreator.format(\"VPN Ping\", vpnPingResult ? `${vpnPingResult}${vpnPingIP}` : \"Not run\"); -%>\n<% } -%>\n<% if (includeVPNPingFullMTU) {\nlet vpnFullPingResult = \"Not run\";\nif (r.MAR018 && r.MAR018.result === \"Actioned\" && typeof r.MAR018.packetsTransmitted === \"number\" && typeof r.MAR018.packetsReceived === \"number\") {\n    if (r.MAR018.packetsTransmitted === r.MAR018.packetsReceived) {\n        vpnFullPingResult = \"OK\"\n    } else {\n        vpnFullPingResult = `Failed (packets transmitted: ${r.MAR018.packetsTransmitted}, packets received: ${r.MAR018.packetsReceived})`;\n    }\n}\n-%>\n<%= rowCreator.format(\"100 Ping Full MTU Size (VPN)\", vpnFullPingResult); -%>\n<% } -%>"}, {"name": "RassOrdersModule", "active": true, "title": "RASS Order (Module)", "description": "Module that extracts cancelled and other orders from RASS", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nlet rowCreator = new RowCreator();\n\nlet allRassOrders = [];\n// Obtains orders from both RASSP and RASSP-C source\nif (s.RASSP && Array.isArray(s.RASSP.Orders)) {\n    for (let order of s.RASSP.Orders) {\n        if (order && typeof order === 'object') {\n            allRassOrders.push(order);\n        }\n    }\n}\n\nif (s[\"RASSP-C\"] && Array.isArray(s[\"RASSP-C\"].Orders)) {\n    for (let order of s[\"RASSP-C\"].Orders) {\n        if (order && typeof order === 'object') {\n            allRassOrders.push(order);\n        }\n    }\n}\n\nconst orderTextMap = (order) => {\n    return `${order.ORDER_NUMBER} (type: ${order.SERVICE_ORDER_TYPE}, customer required date: ${order.CUST_REQUIRED_DATE}, commitment date: ${order.TELECOM_COMMITMENT_DATE})`;\n};\n\nconst activeCANOrders = allRassOrders.filter((order) => {\n    return order.SERVICE_ORDER_TYPE === \"CAN\";\n});\n\nconst activeOtherOrders = allRassOrders.filter((order) => {\n    return order.SERVICE_ORDER_TYPE === \"CAN\";\n});\n-%>\n--Existing RASS Orders--\n<% if (showAllOrders) { -%>\n<%= rowCreator.format(\"All Order(s)\", allRassOrders.length ? allRassOrders.map(orderTextMap) : \"None\"); -%>\n<% } else { -%>\n<% if (includeCancelledOrders) { -%>\n<%= rowCreator.format(\"Active CAN Order(s)\", activeCANOrders.length ? activeCANOrders.map(orderTextMap) : \"None\"); -%>\n<% } -%>\n<%= rowCreator.format(\"Active \\\"Other Status\\\" Order(s)\", activeOtherOrders.length ? activeOtherOrders.map(orderTextMap) : \"None\"); -%>\n<% } -%>"}, {"name": "RASSPBearerAndEquipmentModule", "active": true, "title": "RASS Bearer and Equipment (Module)", "description": "Module to extract and display RASS bearer and equipment details", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nlet rowCreator = new RowCreator();\n\nlet RASSPSource = s[RASSPSourceName] ? s[RASSPSourceName] : null;\nlet RASSPBearerDetails = [];\n\nif (RASSPSource && Array.isArray(RASSPSource.Exchange)) {\n    RASSPBearerDetails = [...RASSPSource.Exchange];\n\n    // Sorts by the string value ORDER_SEQ, change if not accurate\n    RASSPBearerDetails.sort((route1, route2) => {\n        if (route1.ORDER_SEQ < route2.ORDER_SEQ) {\n            return -1;\n        } else if (route1.ORDER_SEQ > route2.ORDER_SEQ) {\n            return 1;\n        } else {\n            return 0;\n        }\n    });\n}\n\nlet GMACSEquipmentDetails = s.GMACS && s.GMACS.equipmentDetails ? s.GMACS.equipmentDetails : null;\n-%>\n<% if (RASSPBearerDetails.length) { -%>\nRASS Bearer Details:\n<%= rowCreator.format(\"\", [\"Order Sequence\", \"Bearer Prefix\", \"Prev Route A\", \"Next Route A\", \"Prev Route B\", \"Next Route B\", \"Line Details\", \"Line Scratchpad\"], true); -%>\n<% for (let route of RASSPBearerDetails) { \nlet orderSequence = typeof route.ORDER_SEQ === \"string\" ? route.ORDER_SEQ : \"N/A\";\nlet bearerPrefix = typeof route.BEARER_PREFIX === \"string\" ? route.BEARER_PREFIX : \"N/A\";\nlet prevRouteACode = typeof route.PREV_ROUTE_A_CODE === \"string\" ? route.PREV_ROUTE_A_CODE : \"N/A\";\nlet nextRouteACode = typeof route.NEXT_ROUTE_A_CODE === \"string\" ? route.NEXT_ROUTE_A_CODE : \"N/A\";\nlet prevRouteBCode = typeof route.PREV_ROUTE_B_CODE === \"string\" ? route.PREV_ROUTE_B_CODE : \"N/A\";\nlet nextRouteBCode = typeof route.NEXT_ROUTE_B_CODE === \"string\" ? route.NEXT_ROUTE_B_CODE : \"N/A\";\nlet lineDetails = typeof route.TRSMN_LINE_DETLS === \"string\" ? route.TRSMN_LINE_DETLS : \"N/A\";\nlet lineScratchpad = typeof route.TRSMN_LINE_SCRATCHPAD === \"string\" ? route.TRSMN_LINE_SCRATCHPAD : \"N/A\";\n-%>\n<%= rowCreator.format(\"\", [orderSequence, bearerPrefix, prevRouteACode, nextRouteACode, prevRouteBCode, nextRouteBCode, lineDetails, lineScratchpad], true); -%>\n<% } -%>\n<% } else { -%>\n<%= rowCreator.format(\"RASS Bearer Details\", \"None\"); -%>\n<% } -%>\n<% if (GMACSEquipmentDetails) { -%>\nRASS Equipment Details:\n<%= rowCreator.format(\"Equipment ID\", GMACSEquipmentDetails.equipment_id ? GMACSEquipmentDetails.equipment_id : \"(Empty)\"); -%>\n<%= rowCreator.format(\"Supplier Code\", GMACSEquipmentDetails.supplier_code ? GMACSEquipmentDetails.supplier_code : \"(Empty)\"); -%>\n<%= rowCreator.format(\"Serial Number\", GMACSEquipmentDetails.serial_no ? GMACSEquipmentDetails.serial_no : \"(Empty)\"); -%>\n<%= rowCreator.format(\"Information\", GMACSEquipmentDetails.information ? GMACSEquipmentDetails.information : \"(Empty)\"); -%>\n<%= rowCreator.format(\"Equipment Sub Type\", GMACSEquipmentDetails.equipment_sub_type ? GMACSEquipmentDetails.equipment_sub_type : \"(Empty)\"); -%>\n<% } else { -%>\n<%= rowCreator.format(\"RASS Equipment Details\", \"Not Found\"); -%>\n<% } -%>"}, {"name": "RASSServiceConfigModule", "active": true, "title": "RASS Service Config (Module)", "description": "Module that extracts service configuration from RASS", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nif (s.RASSServiceConfigInfo?.raw && (s.RASSServiceConfigInfo?.QRPD || s.RASSServiceConfigInfo?.QRSD || s.RASSServiceConfigInfo?.QRTD || s.RASSServiceConfigInfo?.QRFD || s.RASSServiceConfigInfo?.QRID)) {\n    let rowCreator = new RowCreator();\n    let rassCom = []; let rassTed = []; let rassPCMS = []; let rassNR = [];\n    let rassBAC;      let rassGId;      let rassEId;       let rassTG = [];\n    let rassBA;       let rassPT;\n    let propFound = false;              let desFirst = true;\n    \n    const RASS_SCI_NOT_FOUND_MSG = 'Not Found. Please check the RASS Service Configuration source data.';\n    const RASS_NO_TG_MSG = 'NA (for this service, there’s no GS service FNNs in the RASS page)';\n    const regexDesPCMS = /\\bDES\\b /;\n    \n    if (Array.isArray(s.RASSServiceConfigInfo?.Comments)) {\n        for (let i = 0; i < s.RASSServiceConfigInfo.Comments.length; i++) {\n            rassCom.push(`${s.RASSServiceConfigInfo.Comments[i]}`);\n        }\n    }\n\n    if (Array.isArray(s.RASSServiceConfigInfo?.TEDDetails)) {\n        let tedDetails = s.RASSServiceConfigInfo?.TEDDetails;\n        for (let i = 0; i < tedDetails.length; i++) {\n            rassTed.push(`${tedDetails[i]}`.trim());\n        }\n    }\n\n    if (s.RASSServiceConfigInfo?.rawPCMSCodes) {\n        let rawPCMSCodes = s.RASSServiceConfigInfo.rawPCMSCodes.split('\\n');\n        for (let i = 0; i < rawPCMSCodes.length; i++) {\n            let currProd = rawPCMSCodes[i].substr(5,6).toUpperCase();\n            let currDesc = rawPCMSCodes[i].substr(28,41).toUpperCase();\n            let currQty = rawPCMSCodes[i].substr(24,2);\n            \n            if (!currProd.includes('RMKS') && !currProd.includes('PROP') && !regexDesPCMS.test(currDesc)) {\n                let rassPCMSRow = `${currProd}    ${currDesc}    ${currQty}`;\n                // for flexcab match volume\n                if (s.Flexcab?.flexcabData?.length) {\n                    for (let i=0; i < s.Flexcab.flexcabData.length; i++){\n                        if (s.Flexcab.flexcabData[i].pcms_prod_cd && s.Flexcab.flexcabData[i].volume && (currProd.trim() == s.Flexcab.flexcabData[i].pcms_prod_cd.trim())){\n                            let flexcabVolumeMatched = (currQty.trim() == s.Flexcab.flexcabData[i].volume.trim())\n                            rassPCMSRow += `    FlexcabMatched: ${flexcabVolumeMatched}`;\n                        }\n                    }\n                }\n                rassPCMS.push(rassPCMSRow);\n            }\n            if (currDesc.includes('PROPERTY')) {\n                propFound = true;\n            }\n            if (propFound && desFirst && currProd.includes('RMKS')) {\n                let rassDesId; let flexcabInvcId_matched = true;\n                rassBA = currDesc;\n                desFirst = false;\n                // for flexcab match invc_argt_id\n                rassDesId = rassBA.match(/\\d+/) ? rassBA.match(/\\d+/)[0] : null;\n                if(rassDesId && s.Flexcab?.flexcabData?.length){\n                    flexcabInvcId_matched = s.Flexcab.flexcabData.every(item => item.invc_argt_id && rassDesId.trim() == item.invc_argt_id.trim());\n                    rassBA = `${rassBA.trim()}    FlexcabMatched: ${flexcabInvcId_matched}`;\n                }\n            }\n        }\n    }\n    \n    if (Array.isArray(s.RASSServiceConfigInfo?.NumberRanges)) {\n        let numberRanges = s.RASSServiceConfigInfo?.NumberRanges;\n        for (let i = 0; i < numberRanges.length; i++) {\n            rassNR.push(`${numberRanges[i]}`);\n        }\n    }\n\n    if (s.RASSServiceConfigInfo?.QRSD) {\n        let text = s.RASSServiceConfigInfo.QRSD;\n        let regex = new RegExp(\"ST: (.{2}).+\", \"g\");\n        let match = regex.exec(text);\n        if (match != null) {\n            rassPT = match[1];\n        }\n    }\n\n    if (s.RASSServiceConfigInfo?.GroupId)      rassGId = 'GH ' + s.RASSServiceConfigInfo.GroupId;\n    if (s.RASSServiceConfigInfo?.EnterpriseId) rassEId = 'GE ' + s.RASSServiceConfigInfo.EnterpriseId;\n    \n    if (Array.isArray(s.RASSServiceConfigInfo?.TrunkGroup)) {\n        let trunkGroup = s.RASSServiceConfigInfo?.TrunkGroup;\n        \n        for (let i = 0; i < trunkGroup.length; i++) {\n            rassTG.push(`GS ${trunkGroup[i]}`);\n        }\n    }\n\n-%>\n--System Details RASS--\n<%= rowCreator.format('Permanent Comments', rassCom.length ? rassCom : RASS_SCI_NOT_FOUND_MSG, false); -%>\n\n<%= rowCreator.format('PCMS Codes', rassPCMS.length ? rassPCMS : RASS_SCI_NOT_FOUND_MSG, false); -%>\n\n<%= rowCreator.format('Billing, Flexcab, Account details', rassBA, false); -%>\n\n<%= rowCreator.format('Terminal Equipment', rassTed.length ? rassTed : RASS_SCI_NOT_FOUND_MSG, false); -%>\n\n<%= rowCreator.format('Number Ranges', rassNR.length ? rassNR : RASS_SCI_NOT_FOUND_MSG, false); -%>\n\n<%= rowCreator.format('Product Type', rassPT ? rassPT : RASS_SCI_NOT_FOUND_MSG); -%>\n<%= rowCreator.format('Group ID', rassGId ? rassGId : RASS_SCI_NOT_FOUND_MSG); -%>\n<%= rowCreator.format('Enterprise ID', rassEId ? rassEId : RASS_SCI_NOT_FOUND_MSG); -%>\n\n<%= rowCreator.format('Trunk Group', rassTG.length ? rassTG : RASS_NO_TG_MSG); -%>\nGE, GH & GS 'Services of a Network' RASS information is available in the Sources tab within the RASS - Collect Service Configuration Information\n<% } else { -%>\nNEW SERVICE NOT YET CONNECTED - <%= data.fnn; -%>\n<% } -%>"}, {"name": "RASSServiceDetailModule", "active": true, "title": "RASS Service Detail (Module)", "description": "Module that extracts service details from RASS", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nif (s['RASSP'] && s['RASSP'].hasOwnProperty('FNN')) {\n    let rowCreator = new RowCreator();\n    let rasspHeader = '--Service Details--\\n'; let rasspFnn = data.fnn; let rasspCs; let rasspCidn; let rasspAn; let rasspAdd;\n    \n    //rasspCs = '';\n    if   (s['RASSP'].hasOwnProperty('CUSTOMER_ID'))         rasspCidn = s.RASSP['CUSTOMER_ID'];  else  rasspCidn  = '';\n    if   (s['RASSP'].hasOwnProperty('BILLING_NAME'))        rasspAn = s.RASSP['BILLING_NAME'];   else  rasspAn = '';\n    if   (s['RASSP'].hasOwnProperty('ADDRESS_A_END'))       rasspAdd = s.RASSP['ADDRESS_A_END'];  \n    else { if (s['RASSP'].hasOwnProperty('ADDRESS_B_END'))  rasspAdd = s.RASSP['ADDRESS_B_END']; else  rasspAdd  = ''; }\n-%>\n<%= rasspHeader -%>\n<%= rowCreator.format('IP Voice Service',rasspFnn); -%>\n<%= rowCreator.format('Consenting Status',rasspCs); -%>\n<%= rowCreator.format('CIDN',rasspCidn); -%>\n<%= rowCreator.format('Account Name',rasspAn); -%>\n<%= rowCreator.format('Address',rasspAdd); -%>\n<%} -%>"}, {"name": "SHAREDBOSSNTUDeviceModule", "active": true, "title": "Display SHAREDBOSS details for relevant ports (Module)", "description": "Extracts down / up ports from demarc point device and shows port info from SHAREDBOSSNTUDEVICE", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<% if (s.SHAREDBOSSNTUDEVICE) {\n    \nlet rowCreator = new RowCreator();\n\nconst ALLOWED_FIELD_NAMES = Object.freeze([\n    'Type',\n    'Media-select mode',\n    'Link',\n    'Duplex state',\n    'Actual speed'\n]);\n\nfunction processDetails(text) {\n    if (typeof text !== 'string') {\n        return text;\n    }\n\n    let detailsArray = text.replace(/--More--/g, '').split('\\n').map((line) => {\n        line = line.trim();\n\n        let lineMatch = line.match(/(?<fieldName>[A-Za-z-\\s]+):(?<fieldValue>.+)/);\n        if (lineMatch) {\n            let fieldName = lineMatch.groups.fieldName;\n            let fieldValue = lineMatch.groups.fieldValue;\n\n            if (ALLOWED_FIELD_NAMES.includes(fieldName.trim())) {\n                return [fieldName.trim(), fieldValue.trim()];\n            } else {\n                return null;\n            }\n        } else {\n            return line;\n        }\n    }).filter(line => line !== null);\n    \n    // Will not display duplex state if Link field starts with \"OFF\"\n    let linkMetric = detailsArray.find((detail) => Array.isArray(detail) && detail[0] === 'Link');\n    if (linkMetric?.[1]?.match(/^OFF/)) {\n        detailsArray = detailsArray.filter((detail) => !(Array.isArray(detail) && detail[0] === 'Duplex state'));\n    }\n    \n    return detailsArray.map((detail) => {\n        if (Array.isArray(detail)) {\n            return rowCreator.format(detail[0], detail[1]).trim();\n        } else {\n            return detail;\n        }\n    }).join('\\n');\n}\n\n\nfunction processPortNumber(portNumber) {\n    if (typeof portNumber !== 'string') {\n        return portNumber;\n    }\n    \n    let portAndTrunkMatch = portNumber.match(/(?<portNumber>\\d+):\\d*/i);\n    if (portAndTrunkMatch) {\n        return portAndTrunkMatch.groups.portNumber;\n    } else {\n        return portNumber;\n    }\n}\n\nfunction extractTrunkPhysicalPorts(trunkPortNumber) {\n    let physicalPortNumbers = [];\n    if (s.SHAREDBOSSNTUDEVICE?.NTUHealthCheck?.getPortDesc?.parsed) {\n        for (let portNum in s.SHAREDBOSSNTUDEVICE.NTUHealthCheck.getPortDesc.parsed) {\n            if (typeof s.SHAREDBOSSNTUDEVICE.NTUHealthCheck.getPortDesc.parsed[portNum]?.details === 'string') {\n                let trunkMatch = s.SHAREDBOSSNTUDEVICE.NTUHealthCheck.getPortDesc.parsed[portNum].details.match(/Trunk t(?<trunk>\\d+), Port (?<port>\\d+) details:/i);\n                if (trunkMatch && trunkMatch.groups.trunk === trunkPortNumber) {\n                    physicalPortNumbers.push(portNum);\n                }\n            }\n        }\n    }\n    return physicalPortNumbers;\n}\n\n\n\nlet downPorts = new Set();\nlet upPorts = new Set();\nlet downPortEntries = [];\nlet upPortEntries = [];\n\nif (r.MDR021?.demarcPoint?.downIntPort) {\n    let downIntPort = processPortNumber(r.MDR021.demarcPoint.downIntPort);\n    downPorts.add(downIntPort);\n}\n\nif (Array.isArray(r.MDR021?.demarcPoint?.upInt)) {\n    for (let upInt of r.MDR021.demarcPoint.upInt) {\n        if (typeof upInt?.port === 'string') {\n            if (typeof upInt.type === 'string' && upInt.type.match(/^LAG$/i)) {\n                let trunkPhysicalPorts = extractTrunkPhysicalPorts(upInt.port);\n                for (let port of trunkPhysicalPorts) {\n                    upPorts.add(port);\n                }\n            } else {\n                let upIntPort = processPortNumber(upInt.port);\n                upPorts.add(upIntPort);\n            }\n        }\n    }\n}\n\nfor (let port of Array.from(downPorts).sort()) {\n    if (s.SHAREDBOSSNTUDEVICE?.NTUHealthCheck?.getPortDesc?.parsed[port]) {\n        let portDetails = processDetails(s.SHAREDBOSSNTUDEVICE.NTUHealthCheck.getPortDesc.parsed[port].details);\n\n        downPortEntries.push({\n            details: portDetails\n        });\n    }\n}\n\nfor (let port of Array.from(upPorts).sort()) {\n    if (s.SHAREDBOSSNTUDEVICE?.NTUHealthCheck?.getPortDesc?.parsed[port]) {\n        let portDetails = processDetails(s.SHAREDBOSSNTUDEVICE.NTUHealthCheck.getPortDesc.parsed[port].details);\n\n        upPortEntries.push({\n            details: portDetails\n        });\n    }\n}\n\nlet vlanIds = new Set();\n\nlet allRecords = [data, ...data.generatedServiceChecks];\n\nfor (let serviceCheckRecord of allRecords) {\n    let demarcPointName = serviceCheckRecord.rulesData?.MDR021?.demarcPoint?.name;\n\n    // Extract VLAN ID from AlwaysOn first and then MAGPIE as a secondary source\n    if (Array.isArray(serviceCheckRecord.sourcesData?.AlwaysOn?.data)) {\n        for (let alwaysOnEntry of serviceCheckRecord.sourcesData.AlwaysOn.data) {\n            if (Array.isArray(alwaysOnEntry?.access_tails)) {\n                for (let accessTail of alwaysOnEntry?.access_tails) {\n                    if (\n                        (typeof accessTail?.device === 'string' && accessTail.device.toLowerCase() === demarcPointName.toLowerCase()) &&\n                        (typeof accessTail?.vlan_id === 'string' && accessTail.vlan_id.match(/^\\d+$/))\n                    ) {\n                        vlanIds.add(accessTail.vlan_id);\n                        break;\n                    }\n                }\n            }\n        }\n    }\n\n    let magpieProductData = null;\n\n    if (typeof serviceCheckRecord.sourcesData?.MAGPIE?.rawData === 'object') {\n        for (let rawDataKey in serviceCheckRecord.sourcesData.MAGPIE.rawData) {\n            // Looks for an object in rawData with the \"Product Name\" key\n            if (serviceCheckRecord.sourcesData.MAGPIE.rawData[rawDataKey]?.['Product Name']) {\n                magpieProductData = serviceCheckRecord.sourcesData.MAGPIE.rawData[rawDataKey];\n                break;\n            }\n        }\n    }\n\n    if (typeof magpieProductData?.['VLAN Domain'] === 'string') {\n        let magpieVlanDomain = magpieProductData?.['VLAN Domain'].replace(':', '');\n        if (magpieVlanDomain.match(/^\\d+$/)) {\n            vlanIds.add(magpieVlanDomain);\n        }\n    }\n}\n\nlet macAddressTableEntries = [];\nif (vlanIds.size && Array.isArray(s.SHAREDBOSSNTUDEVICE?.NTUHealthCheck?.getMacAddressTable?.parsed)) {\n    for (let macAddressEntry of s.SHAREDBOSSNTUDEVICE.NTUHealthCheck.getMacAddressTable.parsed) {\n        if (typeof macAddressEntry === 'object' && macAddressEntry?.port !== 'Intern' && vlanIds.has(macAddressEntry?.vlanId)) {\n            macAddressTableEntries.push(macAddressEntry);\n        }\n    }\n}\n-%>\n--SHAREDBOSS NTU Device Details--\n<%= rowCreator.format('NTU Device', data?.sourcesMetadata?.SHAREDBOSSNTUDEVICE?.parameters?.device); -%>\nDownstream ports:\n<% if (downPortEntries.length === 0) { -%>\nNo downstream ports found\n<% } -%>\n<% for (let entry of downPortEntries) { -%>\n<%= entry.details; -%>\n<% } -%>\n\nUpstream ports:\n<% if (upPortEntries.length === 0) { -%>\nNo upstream ports found\n<% } -%>\n<% for (let entry of upPortEntries) { -%>\n<%= entry.details; -%>\n<% } -%>\n\n<% if (vlanIds.size) { -%>\nMAC address table:\n<%= rowCreator.format('MAC address', ['VLAN ID', 'Port', 'Mode', 'Last Change'], true); -%>\n<% for (let entry of macAddressTableEntries) { -%>\n<%= rowCreator.format(entry.macAddress, [entry.vlanId, entry.port, entry.mode, entry.lastChange], true); -%>\n<% } -%>\n<% } else { -%>\nNo VLAN ID found in AlwaysOn or MAGPIE to view SHAREDBOSS MAC address table\n<% } -%>\n<% } -%>"}, {"name": "ShowRules", "active": true, "title": "Display all Rules when checkbox ticked (<PERSON><PERSON><PERSON>)", "description": "Module to display all Rules ", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nif(showRulesData) {\n    let rowCreator = new RowCreator();\n    let rules = [];\n\n    for (ruleName in r) {\n        rules.push(`${ruleName} ${r[ruleName].msg} (${r[ruleName].valueMsg})`);\n    }\n-%>\n\n\n<%= rowCreator.format('Rules',rules, false); -%>\n<%} -%>"}, {"name": "SiteDetailsModule", "active": true, "title": "Site Details (Module)", "description": "Module to display site details for a device from ODIN data", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nlet rowCreator = new RowCreator();\nlet adborIdValue = \"Not Found\";\nif (s.WOLF) {\n    if (r.MDR181 && r.MDR181.matchedWolfRecord) {\n        adborIdValue = r.MDR181.matchedWolfRecord.adbor_address_id;\n    } else if (r.MDR181 && r.MDR181.matchedAddress) {\n        adborIdValue = \"Not Found / Not Confirmed, please check WOLF\";\n    } else {\n        adborIdValue = \"Possible address mismatch, please check WOLF\";\n    }\n}\n-%>\n<% if (showAddress) { -%>\n<%= rowCreator.format(\"Site Address\", data.address ? data.address : \"Not found\"); -%>\n<% } -%>\nSite Hours:\nSite Contacts:\n<%= rowCreator.format(\"ADBOR ID\", adborIdValue); -%>"}, {"name": "TelstraFibreSummaryText", "active": true, "title": "Telstra Fibre Summary (Text)", "description": "Summary of Telstra Fibre service", "allowedSystemsToAppend": ["ServiceCentral"], "suite": [], "templateType": "Service Check", "formatType": "Text", "listCondition": "let listCondition = false;\n\nif (s.MAGPIE && s.MAGPIE.rawData && s.MAGPIE.rawData.IPMAN && s.MAGPIE.rawData.IPMAN[\"Product Name\"] === \"Telstra Fibre\") {\n    listCondition = true;\n} else {\n    for (let serviceCheckRecord of data.generatedServiceChecks) {\n        if (serviceCheckRecord.sourcesData && serviceCheckRecord.sourcesData.MAGPIE &&\n            serviceCheckRecord.sourcesData.MAGPIE.rawData && \n            ((serviceCheckRecord.sourcesData.MAGPIE.rawData.IPMAN && serviceCheckRecord.sourcesData.MAGPIE.rawData.IPMAN[\"Product Name\"] === \"Telstra Fibre\") ||\n             (serviceCheckRecord.sourcesData.MAGPIE.rawData.TE && serviceCheckRecord.sourcesData.MAGPIE.rawData.TE[\"Product Name\"] === \"Telstra Fibre\"))) {\n            listCondition = true;\n            break;\n        }\n    }\n}\n\nlistCondition;", "defaultPriority": 0, "template": "<% \nlet tFibreRecord = null;\n\nif (s.MAGPIE && s.MAGPIE.rawData && s.MAGPIE.rawData.IPMAN && s.MAGPIE.rawData.IPMAN[\"Product Name\"] === \"Telstra Fibre\") {\n    tFibreRecord = data;\n} else {\n    for (let serviceCheckRecord of data.generatedServiceChecks) {\n        if (serviceCheckRecord.sourcesData && serviceCheckRecord.sourcesData.MAGPIE &&\n            serviceCheckRecord.sourcesData.MAGPIE.rawData && \n            ((serviceCheckRecord.sourcesData.MAGPIE.rawData.IPMAN && serviceCheckRecord.sourcesData.MAGPIE.rawData.IPMAN[\"Product Name\"] === \"Telstra Fibre\") ||\n             (serviceCheckRecord.sourcesData.MAGPIE.rawData.TE && serviceCheckRecord.sourcesData.MAGPIE.rawData.TE[\"Product Name\"] === \"Telstra Fibre\"))) {\n            tFibreRecord = serviceCheckRecord;\n            break;\n        }\n    }\n}\n\nlet adaptServiceFNNs = [];\nlet bipAdaptServices = {};\nlet tidAdaptServices = {};\n\nlet allRecords = [data, ...data.generatedServiceChecks];\n\nfor (let serviceCheckRecord of allRecords) {\n    let adaptServiceString = serviceCheckRecord.fnn;\n    if (serviceCheckRecord.sourcesData && serviceCheckRecord.sourcesData.MAGPIE && serviceCheckRecord.sourcesData.MAGPIE.rawData) {\n        if (serviceCheckRecord.sourcesData.MAGPIE.rawData.IPMAN && serviceCheckRecord.sourcesData.MAGPIE.rawData.IPMAN[\"Product Name\"]) {\n            if (serviceCheckRecord.sourcesData.MAGPIE.rawData.IPMAN[\"Product Name\"] === \"Telstra Fibre\") {\n                continue;\n            }\n            adaptServiceString += ` (${serviceCheckRecord.sourcesData.MAGPIE.rawData.IPMAN[\"Product Name\"]})`;\n            bipAdaptServices[serviceCheckRecord.fnn] = serviceCheckRecord.sourcesData.MAGPIE.rawData.IPMAN;\n        } else if (serviceCheckRecord.sourcesData.MAGPIE.rawData.TE && serviceCheckRecord.sourcesData.MAGPIE.rawData.TE[\"Product Name\"]) {\n            if (serviceCheckRecord.sourcesData.MAGPIE.rawData.TE[\"Product Name\"] === \"Telstra Fibre\") {\n                continue;\n            }\n            adaptServiceString += ` (${serviceCheckRecord.sourcesData.MAGPIE.rawData.TE[\"Product Name\"]})`;\n            tidAdaptServices[serviceCheckRecord.fnn] = serviceCheckRecord.sourcesData.MAGPIE.rawData.TE;\n        }\n    }\n    adaptServiceFNNs.push(adaptServiceString);\n}\n\nfunction makeColumnString(fieldName, fieldValue) {\n    const PAD_LENGTH = 48;\n    if (typeof fieldValue === 'undefined' || fieldValue === null) {\n        // If the field value is null / undefined, return an empty string\n        return '';\n    } else {\n        if (Array.isArray(fieldValue)) {\n            if (fieldValue.length) {\n                let columnString = (fieldName + \":\").padEnd(PAD_LENGTH) + fieldValue[0] + \"\\n\";\n                for (let i = 1; i < fieldValue.length; i++) {\n                    columnString += \"\".padEnd(PAD_LENGTH) + fieldValue[i] + \"\\n\";\n                }\n                return columnString;\n            } else {\n                return (fieldName + \":\").padEnd(PAD_LENGTH) + \"None\\n\";\n            }\n        } else {\n            return (fieldName + \":\").padEnd(PAD_LENGTH) + fieldValue + \"\\n\";\n        }\n    }\n}\n-%>\n<% if (tFibreRecord) { -%>\n<% let magpieData = tFibreRecord.sourcesData.MAGPIE.rawData.IPMAN ? tFibreRecord.sourcesData.MAGPIE.rawData.IPMAN : tFibreRecord.sourcesData.MAGPIE.rawData.TE; -%>\nAdapt T-Fibre <%= tFibreRecord.fnn %>\n<%= makeColumnString(\"CIDN\", tFibreRecord && tFibreRecord.rulesData && tFibreRecord.rulesData.MDR142 && tFibreRecord.rulesData.MDR142.CIDN ? tFibreRecord.rulesData.MDR142.CIDN : null); -%>\n<%= makeColumnString(\"Customer\", magpieData[\"Customer\"]); -%>\n<%= makeColumnString(\"Member of IPMAN Network\", magpieData[\"Member of IPMAN Network\"]); -%>\n<%= makeColumnString(\"Trunk FNN\", tFibreRecord.fnn); -%>\n<%= makeColumnString(\"Product Name\", magpieData[\"Product Name\"]); -%>\n<%= makeColumnString(\"RASS ST\", magpieData[\"RASS ST\"]); -%>\n<%= makeColumnString(\"Configuration\", magpieData[\"Configuration\"]); -%>\n<%= makeColumnString(\"Port Type\", magpieData[\"Port Type\"]); -%>\n<%= makeColumnString(\"Port Configuration\", magpieData[\"Port Configuration\"]); -%>\n<%= makeColumnString(\"Adapt ID\", magpieData[\"Adapt ID\"]); -%>\n<%= makeColumnString(\"Associated Adapt Services\", adaptServiceFNNs); -%>\n<%= makeColumnString(\"VLAN Domain\", typeof magpieData[\"VLAN Domain\"] === \"string\" ? magpieData[\"VLAN Domain\"].replace(\":\", \"\") : null); -%>\n<%= makeColumnString(\"Media Type\", magpieData[\"Media type\"]); -%>\n<%= makeColumnString(\"Routing Protocol\", magpieData[\"Routing Protocol\"]); -%>\n<%= makeColumnString(\"Service Speed\", magpieData[\"Service Speed\"]); -%>\n<%= makeColumnString(\"Bandwidth Purchased by Customer\", magpieData[\"Bandwidth Purchased by Customer\"]); -%>\n<%= makeColumnString(\"Total Bandwidth Consumed by Adapt Services\", magpieData[\"Total Bandwidth consumed by Adapt Services on this port\"]); -%>\n<%= makeColumnString(\"ICRL type / Policing rate\", magpieData[\"ICRL type / Policing rate\"]); -%>\n\n<% for (let fnn in bipAdaptServices) { -%>\n<% let bipIpmanData = bipAdaptServices[fnn]; -%>\nBIP Adapt Summary <%= fnn %>\n<%= makeColumnString(\"VLAN FNN\", bipIpmanData[\"VLAN FNN\"]); -%>\n<%= makeColumnString(\"Product Name\", bipIpmanData[\"Product Name\"]); -%>\n<%= makeColumnString(\"Associated Access Service FNN\", bipIpmanData[\"Associated Access Service FNN\"]); -%>\n<%= makeColumnString(\"RASS ST\", bipIpmanData[\"RASS ST\"]); -%>\n<%= makeColumnString(\"CE IP Address\", bipIpmanData[\"CE IP address\"]); -%>\n<%= makeColumnString(\"Media Type\", bipIpmanData[\"Media type\"]); -%>\n<%= makeColumnString(\"Routing Protocol\", bipIpmanData[\"Routing Protocol\"]); -%>\n<%= makeColumnString(\"Basement Switch\", bipIpmanData[\"Basement switch\"]); -%>\n<%= makeColumnString(\"VLAN Domain\", typeof bipIpmanData[\"VLAN Domain\"] === \"string\" ? bipIpmanData[\"VLAN Domain\"].replace(\":\", \"\") : null); -%>\n<%= makeColumnString(\"Service Speed\", bipIpmanData[\"Service Speed\"]); -%>\n<%= makeColumnString(\"ICRL type / Policing rate\", bipIpmanData[\"ICRL type / Policing rate\"]); -%>\n<%= makeColumnString(\"DCoS\", bipIpmanData[\"Dynamic Class of Service\"]); -%>\n<%= makeColumnString(\"RAMBO\", bipIpmanData[\"IMSI\"] ? \"Yes\" : \"No\"); -%>\n\n<% if (bipIpmanData[\"IMSI\"]) { -%>\nRAMBO Information\n<%= makeColumnString(\"IMSI\", bipIpmanData[\"IMSI\"]); -%>\n<%= makeColumnString(\"Mobile Bandwidth\", bipIpmanData[\"Mobile Bandwidth\"]); -%>\n<%= makeColumnString(\"WAN IP Address\", bipIpmanData[\"WAN IP-Address\"]); -%>\n<%= makeColumnString(\"MSISDN\", bipIpmanData[\"MSISDN\"]); -%>\n<%= makeColumnString(\"SIM Type\", bipIpmanData[\"SIM Type\"]); -%>\n<%= makeColumnString(\"LTE SVLAN-ID\", bipIpmanData[\"LTE SVLAN-ID\"]); -%>\n<%= makeColumnString(\"Fibre SVLAN-ID\", bipIpmanData[\"Fibre SVLAN-ID\"]); -%>\n<%= makeColumnString(\"Backup Service\", bipIpmanData[\"Backup Service\"]); -%>\n<% } -%>\n<% } -%>\n\n<% for (let fnn in tidAdaptServices) { -%>\n<% let tidTeData = tidAdaptServices[fnn]; -%>\nTID Adapt Summary <%= fnn %>\n<%= makeColumnString(\"VLAN FNN\", tidTeData[\"VLAN FNN\"]); -%>\n<%= makeColumnString(\"TID FNN\", tidTeData[\"Member of TE Network\"]); -%>\n<%= makeColumnString(\"Product Name\", tidTeData[\"Product Name\"]); -%>\n<%= makeColumnString(\"Associated Access Service FNN\", tidTeData[\"Associated Access Service FNN\"]); -%>\n<%= makeColumnString(\"Media Type\", tidTeData[\"Media type\"]); -%>\n<%= makeColumnString(\"Domains\", tidTeData[\"Domains\"]); -%>\n<%= makeColumnString(\"Basement Switch\", tidTeData[\"Basement switch\"]); -%>\n<%= makeColumnString(\"VLAN Domain\", typeof tidTeData[\"VLAN Domain\"] === \"string\" ? tidTeData[\"VLAN Domain\"].replace(\":\", \"\") : null); -%>\n<%= makeColumnString(\"Service Speed\", tidTeData[\"Service Speed\"]); -%>\n<%= makeColumnString(\"DCoS\", tidTeData[\"Dynamic Class of Service\"]); -%>\n<%= makeColumnString(\"RAMBO\", tidTeData[\"IMSI\"] ? \"Yes\" : \"No\"); -%>\n\n<% if (tidTeData[\"IMSI\"]) { -%>\nRAMBO Information\n<%= makeColumnString(\"IMSI\", tidTeData[\"IMSI\"]); -%>\n<%= makeColumnString(\"Mobile Bandwidth\", tidTeData[\"Mobile Bandwidth\"]); -%>\n<%= makeColumnString(\"WAN IP Address\", tidTeData[\"WAN IP-Address\"]); -%>\n<%= makeColumnString(\"MSISDN\", tidTeData[\"MSISDN\"]); -%>\n<%= makeColumnString(\"SIM Type\", tidTeData[\"SIM Type\"]); -%>\n<%= makeColumnString(\"LTE SVLAN-ID\", tidTeData[\"LTE SVLAN-ID\"]); -%>\n<%= makeColumnString(\"Fibre SVLAN-ID\", tidTeData[\"Fibre SVLAN-ID\"]); -%>\n<%= makeColumnString(\"Backup Service\", tidTeData[\"Backup Service\"]); -%>\n<% } -%>\n<% } -%>\n\nMerge Result Link: <%= mergeUrl %>/serviceCheck/view/html/<%= data.id %>\n<% } else { -%>\nNo Telstra Fibre service found in current record\n<% } %>"}, {"name": "TelstraFieldTask", "active": true, "title": "Telstra Field Task (Text)", "description": "Telstra Field Task template for ADSL / BDSL / IPMAN services", "allowedSystemsToAppend": [], "suite": [], "templateType": "Service Check", "formatType": "Text", "listCondition": "(data.carriageType === 'ADSL' || s.MAGPIE && s.MAGPIE.rawData && s.MAGPIE.rawData.DYNAMIC && s.MAGPIE.rawData.DYNAMIC['Product Name'] === 'Telstra Remote Telemetry Legacy') ||\n(data.carriageType === 'BDSL') ||\n(data.carriageType === 'IPMAN');", "defaultPriority": 0, "template": "<%\nlet rowCreator = new RowCreator();\nlet magpieDynamicProductName = s.MAGPIE && s.MAGPIE.rawData && s.MAGPIE.rawData.DYNAMIC && s.MAGPIE.rawData.DYNAMIC['Product Name'];\n-%>\n<% if (data.carriageType === 'ADSL' || magpieDynamicProductName === 'Telstra Remote Telemetry Legacy') { -%>\n<% if (magpieDynamicProductName === 'Telstra Remote Telemetry Legacy') { -%>\n<%= rowCreator.format('Additional Location Information (TCU/Site No.)', s.MAGPIE.rawData.DYNAMIC.Location ? s.MAGPIE.rawData.DYNAMIC.Location : 'Not found'); -%>\n<% } else { -%>\n<%- renderTemplate('SiteDetailsModule', { showAddress: false }); %>\n<%- renderTemplate('MDNFieldTaskInfoModule'); %>\n<% } -%>\n\nThis is a Telstra service delivered via ADSL.\n\nCT please perform the following:\n1. Restore ADSL service to router, confirm IP connectivity to customer;\n2. Check Main cable & O pair(s) to customers premise / Change, Re run or Re-solder jumpers as required.\n3. If the fault is intermittent check Main and or O-pairs for any possible interference from adjacent services in the copper cable/CAN.\n4. Contact Telstra CNM field support via Live chat from the customer's site for cooperative testing and restoration confirmation before closing subcase;\n5. Filter installation and/or internal cabling repairs may be required;\n6. If Managed Router/Device bring laptop, nextg vpn access, cisco console cable, ethernet cables to confirm or restore cisco router configuration for MWAN service (if needed).\n<% } else if (data.carriageType === 'BDSL') {\nlet RASSPSourceName = null;\nif (s.RASSP && s.RASSP.FNN === data.carriageFNN) {\n    RASSPSourceName = 'RASSP';\n} else if (s['RASSP-C'] && s['RASSP-C'].FNN === data.carriageFNN) {\n    RASSPSourceName = 'RASSP-C';\n}\n-%>\n<%- renderTemplate('SiteDetailsModule', { showAddress: true }); %>\n<%- renderTemplate('RASSPBearerAndEquipmentModule', { RASSPSourceName: RASSPSourceName }); %>\n<%- renderTemplate('MDNFieldTaskInfoModule'); %>\n\nThis is a Telstra IPWAN service delivered via BDSL.\n\nCT please perform the following:\n1. Contact Telstra CNM field support via Live chat from the customer's site for cooperative testing and restoration confirmation before closing subcase.\n2. Restore BDSL service to router, confirm IP connectivity to customer.\n3. Replacement BDSL NTU may be required, see terminal equipment details attached.\n4. Check Main cable & O pair(s) to customers premise / Change, Re run or Re-solder jumpers as required.\n5. If the fault is intermittent check Main and or O-pairs for any possible interference from adjacent services in the copper cable/CAN.\n6. Filter installation and/or internal cabling repairs may be required.\n7. If Managed Router/Device bring laptop, VPN access, cisco console cable, ethernet cables to confirm or restore cisco router configuration for MWAN service (if needed).\n<% } else if (data.carriageType === 'IPMAN') {\nlet basementDeviceType = r.MDR178 && r.MDR178.basementDeviceType;\nlet ipmanIdFieldName = 'OMC or NTU ID';\nif (['b'].includes(basementDeviceType)) {\n    ipmanIdFieldName = 'Basement Switch ID'; \n}\n\nlet nextUpstreamDevice = null;\nlet basementLinkId = null;\nif (r.MDR179 && r.MDR179.nextDevices && r.MDR179.nextDevices.length) {\n    nextUpstreamDevice = `${r.MDR179.nextDevices[0].name} (Port: ${r.MDR179.nextDevices[0].portFrom ? r.MDR179.nextDevices[0].portFrom : 'Not found'})`;\n    basementLinkId = r.MDR179.nextDevices[0].link;\n}\n\nlet nextUpstreamDeviceAddress = null;\nif (s['MAGPIE - Next Device From Basement'] &&\n    s['MAGPIE - Next Device From Basement'].rawData &&\n    s['MAGPIE - Next Device From Basement'].rawData.Node &&\n    s['MAGPIE - Next Device From Basement'].rawData.Node.Location\n) {\n    nextUpstreamDeviceAddress = s['MAGPIE - Next Device From Basement'].rawData.Node.Location;\n}\n\n// Expects IPMAN Virtual Operator response to be a plain object, will count as null otherwise\nlet ipmanVOData = s.IPMANVirtualOperator && Object.getPrototypeOf(s.IPMANVirtualOperator) === Object.prototype ? s.IPMANVirtualOperator : null;\n\nlet adaptServiceFNNs = [];\nlet allRecords = [data, ...data.generatedServiceChecks];\n\nfor (let serviceCheckRecord of allRecords) {\n    let adaptServiceString = serviceCheckRecord.fnn;\n    if (serviceCheckRecord.sourcesData && serviceCheckRecord.sourcesData.MAGPIE && serviceCheckRecord.sourcesData.MAGPIE.rawData) {\n        if (serviceCheckRecord.sourcesData.MAGPIE.rawData.IPMAN && serviceCheckRecord.sourcesData.MAGPIE.rawData.IPMAN[\"Product Name\"]) {\n            if (serviceCheckRecord.sourcesData.MAGPIE.rawData.IPMAN[\"Product Name\"] === \"Telstra Fibre\") {\n                continue;\n            }\n            adaptServiceString += ` (${serviceCheckRecord.sourcesData.MAGPIE.rawData.IPMAN[\"Product Name\"]})`;\n        } else if (serviceCheckRecord.sourcesData.MAGPIE.rawData.TE && serviceCheckRecord.sourcesData.MAGPIE.rawData.TE[\"Product Name\"]) {\n            if (serviceCheckRecord.sourcesData.MAGPIE.rawData.TE[\"Product Name\"] === \"Telstra Fibre\") {\n                continue;\n            }\n            adaptServiceString += ` (${serviceCheckRecord.sourcesData.MAGPIE.rawData.TE[\"Product Name\"]})`;\n        }\n    }\n    adaptServiceFNNs.push(adaptServiceString);\n}\n-%>\n<%- renderTemplate('SiteDetailsModule', { showAddress: true }); %>\n<%= rowCreator.format('IPMAN Port FNN', s.MAGPIE.rawData && s.MAGPIE.rawData.IPMAN && s.MAGPIE.rawData.IPMAN.IPMAN_FNN ? s.MAGPIE.rawData.IPMAN.IPMAN_FNN : 'Not found'); -%>\n<%= rowCreator.format('Associated Adapt Services', adaptServiceFNNs); -%>\n<%= rowCreator.format(ipmanIdFieldName, r.MDR178 && r.MDR178.basementDeviceName ? r.MDR178.basementDeviceName : 'Not found'); -%>\n\n<%= rowCreator.format('Link ID', ipmanVOData && ipmanVOData.linkId ? s.IPMANVirtualOperator.linkId : 'Not found'); -%>\n<%= rowCreator.format('Link length', ipmanVOData && ipmanVOData.length ? s.IPMANVirtualOperator.length : 'Not found'); -%>\n<%= rowCreator.format('Link sheath', ipmanVOData && ipmanVOData.sheath ? s.IPMANVirtualOperator.sheath : 'Not found'); -%>\n\n<%= rowCreator.format('Next upstream device from basement / NTU', typeof nextUpstreamDevice ? nextUpstreamDevice : 'Not found'); -%>\n<%= rowCreator.format('Next upstream device location', nextUpstreamDeviceAddress ? nextUpstreamDeviceAddress : 'Not found'); -%>\n\n<%= rowCreator.format('POP switch', ipmanVOData && ipmanVOData.popSwitch ? s.IPMANVirtualOperator.popSwitch : 'Not found'); -%>\n<%= rowCreator.format('POP switch address', ipmanVOData && ipmanVOData.popAddress ? s.IPMANVirtualOperator.popAddress : 'Not found'); -%>\n<%= rowCreator.format('POP switch address rack', ipmanVOData && ipmanVOData.popaddressRack ? s.IPMANVirtualOperator.popaddressRack : 'Not found'); -%>\n<%= rowCreator.format('MPN Code', ipmanVOData && ipmanVOData.bsMpncode ? s.IPMANVirtualOperator.bsMpncode : 'Not found'); -%>\n\n<% if (ipmanVOData?.txNode) { -%>\n<%= rowCreator.format('TX node', ipmanVOData && ipmanVOData.txNode ? ipmanVOData.txNode : 'Not found'); -%>\n<%= rowCreator.format('TX node address', ipmanVOData && ipmanVOData.txNodeAddress ? ipmanVOData.txNodeAddress : 'Not found'); -%>\n<%= rowCreator.format('TX node rack', ipmanVOData && ipmanVOData.txNodeRack ? ipmanVOData.txNodeRack : 'Not found'); -%>\n\n<% } -%>\n<%- renderTemplate('MDNFieldTaskInfoModule'); %>\n\nCT will need to carry the following equipment:\n1) OTDR.\n2) Video Scope\n3) Power Meter.\n4) Fibre loop back cords\n5) Fibre Cleaning Kit\n\nTech to clean fibres, provide optical loopback or shoot fibres if necessary or swap GBIC/SFP.\nTech to attend Exchange first and if issue not resolved to attend customer (A-)end.\n<% } -%>\n\n<% if (data.carriageType === 'ADSL' || magpieDynamicProductName === 'Telstra Remote Telemetry Legacy') { -%>\nADSL to Field Template\n<% } else if (data.carriageType === 'BDSL') { -%>\nBDSL to Field Template\n<% } else if (data.carriageType === 'IPMAN') { -%>\nIPMAN to Field Template\n<% } -%>"}, {"name": "TFibreAdaptiveNetworkSummaryModule", "active": true, "title": "", "description": "", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nlet tFibreRecord = null;\nlet magpieData = null;\n\nlet serviceCheckRecords = [data];\nif (Array.isArray(data.generatedServiceChecks)) {\n    serviceCheckRecords.push(...data.generatedServiceChecks);\n}\n\nfor (let record of serviceCheckRecords) {\n    let magpieRawData = record?.sourcesData?.MAGPIE?.rawData;\n    if (magpieRawData?.IPMAN?.['Product Name'] === 'Telstra Fibre' || magpieRawData?.TE?.['Product Name'] === 'Telstra Fibre') {\n        tFibreRecord = record;\n        magpieData = magpieRawData.IPMAN || magpieRawData.TE;\n    }\n\n    if (tFibreRecord) {\n        break;\n    }\n}\n\nlet adaptServiceFNNs = [];\nlet bipAdaptServices = {};\nlet tidAdaptServices = {};\n\nfor (let record of serviceCheckRecords) {\n    if (record !== tFibreRecord) {\n        let adaptServiceString = record.fnn;\n        if (record.sourcesData?.MAGPIE?.rawData) {\n            if (record.sourcesData.MAGPIE.rawData.IPMAN?.['Product Name']) {\n                adaptServiceString += ` (${record.sourcesData.MAGPIE.rawData.IPMAN['Product Name']})`;\n                bipAdaptServices[record.fnn] = record.sourcesData.MAGPIE.rawData.IPMAN;\n            } else if (record.sourcesData.MAGPIE.rawData.TE?.['Product Name']) {\n                adaptServiceString += ` (${record.sourcesData.MAGPIE.rawData.TE['Product Name']})`;\n                tidAdaptServices[record.fnn] = record.sourcesData.MAGPIE.rawData.TE;\n            }\n        }\n        adaptServiceFNNs.push(adaptServiceString);\n    }\n}\n\nlet rowCreator = new RowCreator();\nif (tFibreRecord) {\n-%>\n--T-Fibre Adapt Service Details--\n\nAdapt T-Fibre <%= tFibreRecord.fnn; %>\n<%= rowCreator.format('CIDN', tFibreRecord.rulesData?.MDR142?.CIDN); -%>\n<%= rowCreator.format('Customer', magpieData['Customer']); -%>\n<%= rowCreator.format('Member of IPMAN Network', magpieData['Member of IPMAN Network']); -%>\n<%= rowCreator.format('Trunk FNN', data.fnn); -%>\n<%= rowCreator.format('Product Name', magpieData['Product Name']); -%>\n<%= rowCreator.format('RASS ST', magpieData['RASS ST']); -%>\n<%= rowCreator.format('Configuration', magpieData['Configuration']); -%>\n<%= rowCreator.format('Port Type', magpieData['Port Type']); -%>\n<%= rowCreator.format('Port Configuration', magpieData['Port Configuration']); -%>\n<%= rowCreator.format('Adapt ID', magpieData['Adapt ID']); -%>\n<%= rowCreator.format('Associated Adapt Services', adaptServiceFNNs); -%>\n<%= rowCreator.format('VLAN Domain', typeof magpieData['VLAN Domain'] === 'string' ? magpieData['VLAN Domain'].replace(':', '') : null); -%>\n<%= rowCreator.format('Media Type', magpieData['Media type']); -%>\n<%= rowCreator.format('Routing Protocol', magpieData['Routing Protocol']); -%>\n<%= rowCreator.format('Service Speed', magpieData['Service Speed']); -%>\n<%= rowCreator.format('Bandwidth Purchased by Customer', magpieData['Bandwidth Purchased by Customer']); -%>\n<%= rowCreator.format('Total Bandwidth Consumed by Adapt Services', magpieData['Total Bandwidth consumed by Adapt Services on this port']); -%>\n<%= rowCreator.format('ICRL type / Policing rate', magpieData['ICRL type / Policing rate']); -%>\n\n<% for (let fnn in bipAdaptServices) {\nlet bipIpmanData = bipAdaptServices[fnn];\n-%>\nBIP Adapt Summary <%= fnn; %>\n<%= rowCreator.format('VLAN FNN', bipIpmanData['VLAN FNN']); -%>\n<%= rowCreator.format('Product Name', bipIpmanData['Product Name']); -%>\n<%= rowCreator.format('Associated Access Service FNN', bipIpmanData['Associated Access Service FNN']); -%>\n<%= rowCreator.format('RASS ST', bipIpmanData['RASS ST']); -%>\n<%= rowCreator.format('CE IP Address', bipIpmanData['CE IP address']); -%>\n<%= rowCreator.format('Media Type', bipIpmanData['Media type']); -%>\n<%= rowCreator.format('Routing Protocol', bipIpmanData['Routing Protocol']); -%>\n<%= rowCreator.format('Basement Switch', bipIpmanData['Basement switch']); -%>\n<%= rowCreator.format('VLAN Domain', typeof bipIpmanData['VLAN Domain'] === 'string' ? bipIpmanData['VLAN Domain'].replace(':', '') : null); -%>\n<%= rowCreator.format('Service Speed', bipIpmanData['Service Speed']); -%>\n<%= rowCreator.format('ICRL type / Policing rate', bipIpmanData['ICRL type / Policing rate']); -%>\n<%= rowCreator.format('DCoS', bipIpmanData['Dynamic Class of Service']); -%>\n<%= rowCreator.format('RAMBO', bipIpmanData['IMSI'] ? 'Yes' : 'No'); -%>\n<% if (bipIpmanData['IMSI']) { -%>\n\nRAMBO Information\n<%= rowCreator.format('IMSI', bipIpmanData['IMSI']); -%>\n<%= rowCreator.format('Mobile Bandwidth', bipIpmanData['Mobile Bandwidth']); -%>\n<%= rowCreator.format('WAN IP Address', bipIpmanData['WAN IP-Address']); -%>\n<%= rowCreator.format('MSISDN', bipIpmanData['MSISDN']); -%>\n<%= rowCreator.format('SIM Type', bipIpmanData['SIM Type']); -%>\n<%= rowCreator.format('LTE SVLAN-ID', bipIpmanData['LTE SVLAN-ID']); -%>\n<%= rowCreator.format('Fibre SVLAN-ID', bipIpmanData['Fibre SVLAN-ID']); -%>\n<%= rowCreator.format('Backup Service', bipIpmanData['Backup Service']); -%>\n<% } -%>\n<% } -%>\n<% for (let fnn in tidAdaptServices) {\nlet tidTeData = tidAdaptServices[fnn];\n-%>\nTID Adapt Summary <%= fnn; %>\n<%= rowCreator.format('VLAN FNN', tidTeData['VLAN FNN']); -%>\n<%= rowCreator.format('TID FNN', tidTeData['Member of TE Network']); -%>\n<%= rowCreator.format('Product Name', tidTeData['Product Name']); -%>\n<%= rowCreator.format('Associated Access Service FNN', tidTeData['Associated Access Service FNN']); -%>\n<%= rowCreator.format('Media Type', tidTeData['Media type']); -%>\n<%= rowCreator.format('Domains', tidTeData['Domains']); -%>\n<%= rowCreator.format('Basement Switch', tidTeData['Basement switch']); -%>\n<%= rowCreator.format('VLAN Domain', typeof tidTeData['VLAN Domain'] === 'string' ? tidTeData['VLAN Domain'].replace(':', '') : null); -%>\n<%= rowCreator.format('Service Speed', tidTeData['Service Speed']); -%>\n<%= rowCreator.format('DCoS', tidTeData['Dynamic Class of Service']); -%>\n<%= rowCreator.format('RAMBO', tidTeData['IMSI'] ? 'Yes' : 'No'); -%>\n<% if (tidTeData['IMSI']) { -%>\n\nRAMBO Information\n<%= rowCreator.format('IMSI', tidTeData['IMSI']); -%>\n<%= rowCreator.format('Mobile Bandwidth', tidTeData['Mobile Bandwidth']); -%>\n<%= rowCreator.format('WAN IP Address', tidTeData['WAN IP-Address']); -%>\n<%= rowCreator.format('MSISDN', tidTeData['MSISDN']); -%>\n<%= rowCreator.format('SIM Type', tidTeData['SIM Type']); -%>\n<%= rowCreator.format('LTE SVLAN-ID', tidTeData['LTE SVLAN-ID']); -%>\n<%= rowCreator.format('Fibre SVLAN-ID', tidTeData['Fibre SVLAN-ID']); -%>\n<%= rowCreator.format('Backup Service', tidTeData['Backup Service']); -%>\n<% } -%>\n<% } -%>\n<% } -%>"}, {"name": "TicketingCasesModule", "active": true, "title": "Ticketing Cases (Module)", "description": "Module containing the logic to extract the 3 most recent ticketing cases for SIIAM, Service Central and Promise", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nlet rowCreator = new RowCreator();\n\nlet siiamActiveCases = [];\nlet siiamHistoryCases = [];\nlet serviceCentralIncidents = [];\nlet promiseTasks = [];\n\n\nfunction isRecentDate(date) {\n    let timestamp = Date.parse(date);\n\n    if (isNaN(timestamp)) {\n        return false;\n    }\n\n    // Considers tickets which are less than 1 year old\n    let earliestDate = new Date();\n    earliestDate.setYear(earliestDate.getYear() - 1);\n\n    return earliestDate < timestamp;\n}\n\nif (s.SIIAMfnn && s.SIIAMfnn.activeCases && typeof s.SIIAMfnn.activeCases === 'object') {\n     for (let siiamCaseId in s.SIIAMfnn.activeCases) {\n        if (s.SIIAMfnn.activeCases[siiamCaseId] &&\n            typeof s.SIIAMfnn.activeCases[siiamCaseId] === 'object' &&\n            isRecentDate(s.SIIAMfnn.activeCases[siiamCaseId].CREATION_TIME)) {\n            siiamActiveCases.push({\n                id: siiamCaseId,\n                creationTime: s.SIIAMfnn.activeCases[siiamCaseId].CREATION_TIME,\n                fnn: data.fnn,\n                status: s.SIIAMfnn.activeCases[siiamCaseId].STATUS\n            });\n        }\n    }\n}\n\nif (s.SIIAMCarriageFnn && s.SIIAMCarriageFnn.activeCases && typeof s.SIIAMCarriageFnn.activeCases === 'object') {\n    for (let siiamCaseId in s.SIIAMCarriageFnn.activeCases) {\n        if (s.SIIAMCarriageFnn.activeCases[siiamCaseId] &&\n            typeof s.SIIAMCarriageFnn.activeCases[siiamCaseId] === 'object' &&\n            isRecentDate(s.SIIAMCarriageFnn.activeCases[siiamCaseId].CREATION_TIME)) {\n            siiamActiveCases.push({\n                id: siiamCaseId,\n                creationTime: s.SIIAMCarriageFnn.activeCases[siiamCaseId].CREATION_TIME,\n                fnn: data.carriageFNN,\n                status: s.SIIAMCarriageFnn.activeCases[siiamCaseId].STATUS\n            });\n        }\n    }\n}\n\nif (s.SIIAMfnn && s.SIIAMfnn.caseHistory && typeof s.SIIAMfnn.caseHistory === 'object') {\n     for (let siiamCaseId in s.SIIAMfnn.caseHistory) {\n        if (s.SIIAMfnn.caseHistory[siiamCaseId] &&\n            typeof s.SIIAMfnn.caseHistory[siiamCaseId] === 'object' &&\n            s.SIIAMfnn.caseHistory[siiamCaseId].STATUS === 'Closed' &&\n            isRecentDate(s.SIIAMfnn.caseHistory[siiamCaseId].CREATION_TIME)) {\n            siiamHistoryCases.push({\n                id: siiamCaseId,\n                creationTime: s.SIIAMfnn.caseHistory[siiamCaseId].CREATION_TIME,\n                fnn: data.fnn,\n                status: s.SIIAMfnn.caseHistory[siiamCaseId].STATUS\n            });\n        }\n    }\n}\n\nif (s.SIIAMCarriageFnn && s.SIIAMCarriageFnn.caseHistory && typeof s.SIIAMCarriageFnn.caseHistory === 'object') {\n     for (let siiamCaseId in s.SIIAMCarriageFnn.caseHistory) {\n        if (s.SIIAMCarriageFnn.caseHistory[siiamCaseId] &&\n            typeof s.SIIAMCarriageFnn.caseHistory[siiamCaseId] === 'object' &&\n            s.SIIAMCarriageFnn.caseHistory[siiamCaseId].STATUS === 'Closed' &&\n            isRecentDate(s.SIIAMCarriageFnn.caseHistory[siiamCaseId].CREATION_TIME)) {\n            siiamHistoryCases.push({\n                id: siiamCaseId,\n                creationTime: s.SIIAMCarriageFnn.caseHistory[siiamCaseId].CREATION_TIME,\n                fnn: data.carriageFNN,\n                status: s.SIIAMCarriageFnn.caseHistory[siiamCaseId].STATUS\n            });\n        }\n    }\n}\n\nif (s[\"Hangar - Service central incident API\"] && typeof s[\"Hangar - Service central incident API\"] === 'object') {\n    for (let serviceCentralId in s[\"Hangar - Service central incident API\"]) {\n        if (s[\"Hangar - Service central incident API\"][serviceCentralId] &&\n            typeof s[\"Hangar - Service central incident API\"][serviceCentralId] === 'object' &&\n            isRecentDate(s[\"Hangar - Service central incident API\"][serviceCentralId].opened_at)) {\n            serviceCentralIncidents.push({\n                id: serviceCentralId,\n                openedAt: s[\"Hangar - Service central incident API\"][serviceCentralId].opened_at,\n                fnn: data.fnn,\n                state: s[\"Hangar - Service central incident API\"][serviceCentralId].incident_state_dv\n            });\n        }\n    }\n}\n\nif (s[\"Hangar - Service central incident API Carriage FNN\"] && typeof s[\"Hangar - Service central incident API Carriage FNN\"] === 'object') {\n    for (let serviceCentralId in s[\"Hangar - Service central incident API Carriage FNN\"]) {\n        if (s[\"Hangar - Service central incident API Carriage FNN\"][serviceCentralId] &&\n            typeof s[\"Hangar - Service central incident API Carriage FNN\"][serviceCentralId] === 'object' &&\n            isRecentDate(s[\"Hangar - Service central incident API Carriage FNN\"][serviceCentralId].opened_at)) {\n            serviceCentralIncidents.push({\n                id: serviceCentralId,\n                openedAt: s[\"Hangar - Service central incident API Carriage FNN\"][serviceCentralId].opened_at,\n                fnn: data.carriageFNN,\n                state: s[\"Hangar - Service central incident API Carriage FNN\"][serviceCentralId].incident_state_dv\n            });\n        }\n    }\n}\n\nif (s.Promise && Array.isArray(s.Promise.tasks)) {\n    for (let promiseTask of s.Promise.tasks) {\n        if (promiseTask && typeof promiseTask === 'object' && isRecentDate(promiseTask.SLADate)) {\n            promiseTasks.push({\n                crn: promiseTask.crn,\n                slaDate: promiseTask.SLADate,\n                status: promiseTask.status\n            });\n        }\n    }\n}\n\n// Reduces cases to the 3 most recent for each array\nsiiamActiveCases = siiamActiveCases.sort((case1, case2) => {\n    let timestamp1 = Date.parse(case1.creationTime);\n    let timestamp2 = Date.parse(case2.creationTime);\n\n    if (isNaN(timestamp1)) {\n        timestamp1 = 0;\n    }\n\n    if (isNaN(timestamp2)) {\n        timestamp2 = 0;\n    }\n\n    return timestamp2 - timestamp1;\n}).slice(0, 3);\n\nsiiamHistoryCases = siiamHistoryCases.sort((case1, case2) => {\n    let timestamp1 = Date.parse(case1.creationTime);\n    let timestamp2 = Date.parse(case2.creationTime);\n\n    if (isNaN(timestamp1)) {\n        timestamp1 = 0;\n    }\n\n    if (isNaN(timestamp2)) {\n        timestamp2 = 0;\n    }\n\n    return timestamp2 - timestamp1;\n}).slice(0, 3);\n\nserviceCentralIncidents = serviceCentralIncidents.sort((case1, case2) => {\n    let timestamp1 = Date.parse(case1.openedAt);\n    let timestamp2 = Date.parse(case2.openedAt);\n\n    if (isNaN(timestamp1)) {\n        timestamp1 = 0;\n    }\n\n    if (isNaN(timestamp2)) {\n        timestamp2 = 0;\n    }\n\n    return timestamp2 - timestamp1;\n}).slice(0, 3);\n\npromiseTasks = promiseTasks.sort((case1, case2) => {\n    let timestamp1 = Date.parse(case1.slaDate);\n    let timestamp2 = Date.parse(case2.slaDate);\n\n    if (isNaN(timestamp1)) {\n        timestamp1 = 0;\n    }\n\n    if (isNaN(timestamp2)) {\n        timestamp2 = 0;\n    }\n\n    return timestamp2 - timestamp1;\n}).slice(0, 3);\n\nlet siiamActiveCasesFormatted = siiamActiveCases.map((currCase) => {\n    return `${currCase.id} (created on: ${currCase.creationTime}, fnn: ${currCase.fnn}, status: ${currCase.status})`;\n});\n\nlet siiamHistoryCasesFormatted = siiamHistoryCases.map((currCase) => {\n    return `${currCase.id} (created on: ${currCase.creationTime}, fnn: ${currCase.fnn}, status: ${currCase.status})`;\n});\n\nlet serviceCentralIncidentsFormatted = serviceCentralIncidents.map((currIncident) => {\n    return `${currIncident.id} (opened on: ${currIncident.openedAt}, fnn: ${currIncident.fnn}, state: ${currIncident.state})`;\n});\n\nlet promiseTasksFormatted = promiseTasks.map((currTask) => {\n    return `${currTask.crn} (SLA date: ${currTask.slaDate}, status: ${currTask.status})`;\n});\n-%>\n--Existing Tickets--\n<%= rowCreator.format(\"SIIAM Active Cases\", siiamActiveCasesFormatted.length ? siiamActiveCasesFormatted : \"None\"); -%>\n<%= rowCreator.format(\"SIIAM History Cases\", siiamHistoryCasesFormatted.length ? siiamHistoryCasesFormatted : \"None\"); -%>\n<%= rowCreator.format(\"Service Central Incidents\", serviceCentralIncidentsFormatted.length ? serviceCentralIncidentsFormatted : \"None\"); -%>\n<%= rowCreator.format(\"Promise Tasks\", promiseTasksFormatted.length ? promiseTasksFormatted : \"None\"); -%>"}, {"name": "TIDAdaptSummaryText", "active": false, "title": "TID Adapt Summary (Text)", "description": "TID Adapt Summary default template", "allowedSystemsToAppend": [], "suite": [], "templateType": "Service Check", "formatType": "Text", "listCondition": "let isTidService = false;\nif (['TP', 'TL'].includes(data.serviceType)) {\n    isTidService = true;\n}\nif (Array.isArray(data.generatedServiceChecks)) {\n    for (let record of data.generatedServiceChecks) {\n        if (['TP', 'TL'].includes(record?.serviceType)) {\n            isTidService = true;\n        }\n    }\n}\nisTidService;", "defaultPriority": 3, "template": "<%\nlet isTidService = false;\nif (['TP', 'TL'].includes(data.serviceType)) {\n    isTidService = true;\n}\nif (Array.isArray(data.generatedServiceChecks)) {\n    for (let record of data.generatedServiceChecks) {\n        if (['TP', 'TL'].includes(record?.serviceType)) {\n            isTidService = true;\n        }\n    }\n}\nif (isTidService) { -%>\nTID Adapt Summary\n\n<%- renderTemplate(\"CustomerDetailsModule\", { RASSPSourceName: \"RASSP\" }); %>\n<%- renderTemplate(\"DeviceDetailsModule\", { forMDN: false, RASSPSourceName: \"RASSP\" }); %>\n<%- renderTemplate(\"NBNProductInventoryModule\"); %>\n<%- renderTemplate(\"RassOrdersModule\", { showAllOrders: true, includeCancelledOrders: true }); %>\n<%- renderTemplate(\"TicketingCasesModule\"); %>\n<%- renderTemplate(\"OutagesModule\"); %>\n<%- renderTemplate(\"PingResultsModule\", { includeCMIPing: false, includeVPNPing: true, includeVPNPingFullMTU: false }); %>\n\n<%- renderTemplate(\"OutcomesModule\"); %>\n<%- renderTemplate(\"MessageBucketModule\"); %>\nMerge Result Link: <%= mergeUrl %>/serviceCheck/view/html/<%= data.id %>\n<% } else { -%>\nRecord is not for a TID Adapt Service\n<% } %>"}, {"name": "TRADDemarcPointModule", "active": true, "title": "TRAD telnet to demarc point device details (Module)", "description": "Module to display details for TRADDEMAPOINT source", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<%\nlet downInt = r.MDR021?.demarcPoint?.downInt;\nlet demarcInterfaceDescriptions = {};\n\n// TRADDEMAPOINT interface descriptions are parsed here from the interfaceDesc section\nif (typeof s.TRADDEMAPOINT?.showInterfacesDesc?.result === 'string') {\n    let lines = s.TRADDEMAPOINT.showInterfacesDesc.result.split(/\\r?\\n/);\n    if (lines.length) {\n        let columns = lines[0].split(/\\s+/).filter(line => line);\n        let columnStarts = [];\n\n        for (let column of columns) {\n            columnStarts.push([column, lines[0].indexOf(column)]);\n        }\n\n        columnStarts.sort((s1, s2) => s1[1] - s2[1]);\n\n        if (columnStarts.length > 1) {\n            for (let i = 1; i < lines.length; i++) {\n                if (lines[i].length >= columnStarts[1][1]) {\n                    let interfaceName = null;\n                    let interfaceInfo = {};\n\n                    for (let j = 0; j < columnStarts.length; j++) {\n                        let fieldName = columnStarts[j][0];\n                        let start = columnStarts[j][1];\n                        let end = j < columnStarts.length - 1 ? columnStarts[j + 1][1] : lines[i].length;\n\n                        if (j === 0) {\n                            interfaceName = lines[i].substring(start, end).trim();\n                        } else {\n                            interfaceInfo[fieldName] = lines[i].substring(start, end).trim();\n                        }\n                    }\n\n                    if (interfaceName) {\n                        demarcInterfaceDescriptions[interfaceName] = interfaceInfo;\n                    }\n                }\n            }\n        }\n    }\n}\n\nlet demarcCustomerIfStatus = null;\nlet demarcCustomerIfParsed = {\n    ...s.TRADDEMAPOINT?.interfaces?.[downInt]?.status?.parsed,\n    Description: demarcInterfaceDescriptions[downInt]?.Description\n};\n\nif (r.MTR034) {\n    demarcCustomerIfStatus = `${s.TRADDEMAPOINT?.device || ''}: ${r.MTR034?.downIntStatusLine || ''}`.trim();\n}\n\nlet demarcNetworkIfStatus = [];\nlet demarcNetworkIfParsed = [];\n\nif (r.MTR089?.upIntStatus) {\n    for (let [upInt, upIntStatus] of Object.entries(r.MTR089?.upIntStatus)) {\n        if (s.TRADDEMAPOINT?.interfaces?.[upInt]?.status?.parsed && upIntStatus.statusLine) {\n            demarcNetworkIfStatus.push(`${s.TRADDEMAPOINT?.device || ''}: ${upIntStatus.statusLine || ''}`.trim());\n            demarcNetworkIfParsed.push({\n                ...s.TRADDEMAPOINT.interfaces[upInt].status.parsed,\n                Description: demarcInterfaceDescriptions[upInt]?.Description\n            });\n        }\n    }\n}\n\n\nlet rowCreator = new RowCreator();\nif (demarcCustomerIfStatus || demarcNetworkIfStatus.length) {\n-%>\n--Demarc Point Details--\n<%= rowCreator.format('Demarc Customer Facing Interface Status', demarcCustomerIfStatus ? demarcCustomerIfStatus : 'Not found'); -%>\n<%= rowCreator.format('Description', demarcCustomerIfParsed?.Description); -%>\n<%= rowCreator.format('Duplex', demarcCustomerIfParsed?.Duplex); -%>\n<%= rowCreator.format('Speed', demarcCustomerIfParsed?.Speed); -%>\n<% for (let i = 0; i < demarcNetworkIfStatus.length; i++) {\nlet networkIfStatus = demarcNetworkIfStatus[i];\nlet networkIfParsed = demarcNetworkIfParsed[i];\n-%>\n<%= rowCreator.format('Demarc Network Facing Interface Status', networkIfStatus ? networkIfStatus : 'Not found'); -%>\n<%= rowCreator.format('Description', networkIfParsed?.Description); -%>\n<%= rowCreator.format('Duplex', networkIfParsed?.Duplex); -%>\n<%= rowCreator.format('Speed', networkIfParsed?.Speed); -%>\n<% } -%>\n<% if (typeof s['BASEMENTSWITCH-DROPCHECK']?.demarcPointAddressTable?.raw === 'string') {\nlet macAddressTableEntries = [];\n\nlet addressTableLines = s['BASEMENTSWITCH-DROPCHECK'].demarcPointAddressTable.raw.split('\\n').map((line) => line.trim());\nfor (let line of addressTableLines) {\n    let lineMatch = line.match(/(?<vlanId>\\d+)\\s+(?<macAddress>[0-9a-f]{4}\\.[0-9a-f]{4}\\.[0-9a-f]{4})\\s+(?<type>.*)\\s+(?<ports>[a-z0-9\\/]+)/i);\n    \n    if (lineMatch) {\n        macAddressTableEntries.push({\n            macAddress: lineMatch.groups.macAddress,\n            vlanId: lineMatch.groups.vlanId,\n            type: lineMatch.groups.type,\n            ports: lineMatch.groups.ports\n        });\n    }\n}\n\nif (macAddressTableEntries.length) {\n-%>\n\nMAC address table:\n<%= rowCreator.format('MAC address', ['VLAN', 'Type', 'Ports'], true); -%>\n<% for (let entry of macAddressTableEntries) { -%>\n<%= rowCreator.format(entry.macAddress, [entry.vlanId, entry.type, entry.ports], true); -%>\n<% } -%>\n<% } else { -%>\n\nMAC address table for demarc point was not collected or had no entries\n<% } -%>\n<% } -%>\n<% } -%>"}, {"name": "TRADNextUpstreamDeviceModule", "active": true, "title": "TRAD telnet to next device / edge device (Module)", "description": "Module to display details for TRADNextUpstreamDevice / TRADEDGE1 source", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "", "defaultPriority": 0, "template": "<% if (r.MTR090?.nextUpstreamIntStatus && Object.keys(r.MTR090.nextUpstreamIntStatus).length) {\nlet rowCreator = new RowCreator();\n\nlet demarcInterfaceDescriptions = {};\n\nif (typeof s[r.MTR090.tradSourceName]?.interfaces === 'object' && s[r.MTR090.tradSourceName].interfaces) {\n    for (let intName in s[r.MTR090.tradSourceName]?.interfaces) {\n        if (typeof s[r.MTR090.tradSourceName]?.interfaces[intName]?.status?.result === 'string') {\n            let descriptionMatch = s[r.MTR090.tradSourceName].interfaces[intName].status.result.match(/^\\s+Description: (?<description>.*)$/m);\n            if (descriptionMatch) {\n                demarcInterfaceDescriptions[intName] = descriptionMatch.groups.description;\n            }\n        }\n    }\n}\n\n-%>\n--Next Upstream Device Details--\n<% for (let [intName, intStatus] of Object.entries(r.MTR090.nextUpstreamIntStatus)) {\nlet nextUpstreamIfParsed = {\n    ...s[r.MTR090.tradSourceName]?.interfaces?.[intName]?.status?.parsed,\n    Description: demarcInterfaceDescriptions[intName]\n};\nlet interfaceStatus = `${s[r.MTR090.tradSourceName]?.device ? s[r.MTR090.tradSourceName].device : ''}: ${intStatus.statusLine ? intStatus.statusLine : ''}`.trim();\nif (intStatus.statusLine) {\n-%>\n<%= rowCreator.format('Next Upstream Device Interface', interfaceStatus); -%>\n<%= rowCreator.format('Description', nextUpstreamIfParsed?.Description); -%>\n<%= rowCreator.format('Duplex', nextUpstreamIfParsed?.Duplex); -%>\n<%= rowCreator.format('Speed', nextUpstreamIfParsed?.Speed); -%>\n<% } -%>\n<% } -%>\n<% } -%>"}, {"name": "WECModule", "active": true, "title": "WEC (Module)", "description": "Module which shows WEC source values", "allowedSystemsToAppend": [], "suite": [], "templateType": "<PERSON><PERSON><PERSON>", "formatType": "Text", "listCondition": "s.WECAdbor && s.WECAdbor.results;", "defaultPriority": 0, "template": "<%\nlet rowCreator = new RowCreator();\n\nif (s.WECAdbor && s.WECAdbor.results) {\n\n    let WECHeading = 'Wireless Experience Checker\\n===========================\\n'; let newTestResult;\n    let WECAlertMsg; let WECOutageSmy; let WECExperCheck; let WECDevDetails; let WECProdDetails;\n\n    for (let i = 0; i < s.WECAdbor.results.alerts.length; i++) {\n        WECAlertMsg = 'Outage Details\\n--------------\\n';\n        for (let j = 0; j < s.WECAdbor.results.alerts[i].length; j++) {\n            if (s.WECAdbor.results.alerts[i][j].severity && s.WECAdbor.results.alerts[i][j].message) WECAlertMsg += 'Message' + i  + ': ['+ s.WECAdbor.results.alerts[i][j].severity + '] ' +  s.WECAdbor.results.alerts[i][j].message + '\\n';\n        }    \n    }\n\n    if (s.WECAdbor.results.outage_summary && s.WECAdbor.results.outage_summary[0]) {\n        WECOutageSmy = '\\nOutage Summary\\n--------------\\n';\n        for (let i = 0; i < s.WECAdbor.results.outage_summary.length; i++) {\n            if (s.WECAdbor.results.outage_summary[i].text)           WECOutageSmy += 'Text: '   + s.WECAdbor.results.outage_summary[i].text   + ' ';\n            if (s.WECAdbor.results.outage_summary[i].colour)         WECOutageSmy += 'Status: ' + s.WECAdbor.results.outage_summary[i].colour + '\\n';\n            if (s.WECAdbor.results.outage_summary[i].affected_cells) {\n                WECOutageSmy += 'Affected Cells:';\n                for (let j = 0; j < s.WECAdbor.results.outage_summary[i].affected_cells.length; j++) { WECOutageSmy += '[' + j + '] ' + s.WECAdbor.results.outage_summary[i].affected_cells[j] + '\\n'; }\n            }\n        }\n    }\n\n    if (s.WECAdbor.results.experience_checker && s.WECAdbor.results.experience_checker[0]) {\n        WECExperCheck = '\\nExperience Checker\\n------------------\\n';\n        for (let i = 0; i < s.WECAdbor.results.experience_checker.length; i++) {\n            if (s.WECAdbor.results.experience_checker[i].product_name) WECExperCheck += 'Product Name: '   + s.WECAdbor.results.experience_checker[i].product_name + '\\n';\n            for (let j = 0; j < s.WECAdbor.results.experience_checker[i].results.length; j++) {\n                if (s.WECAdbor.results.experience_checker[i].results[j] && s.WECAdbor.results.experience_checker[i].results[j].use_case && s.WECAdbor.results.experience_checker[i].results[j].test_result) {\n                    newTestResult = s.WECAdbor.results.experience_checker[i].results[j].test_result.trim().toUpperCase().replace('UNSUPPORTED','U').replace('SUPPORTED','S').replace('REDUCED EXPERIENCE','R');\n                    WECExperCheck += s.WECAdbor.results.experience_checker[i].results[j].use_case + ': ' + newTestResult + ' ';\n                }\n            }\n            WECExperCheck += '\\n';\n        }\n    }\n\n    if (s.WECAdbor.results.device_details) {\n        WECDevDetails = '\\nDevice Details\\n--------------\\n';\n        if (s.WECAdbor.results.device_details.device_name)   WECDevDetails += 'Device Name: '   + s.WECAdbor.results.device_details.device_name   + '\\n';\n        if (s.WECAdbor.results.device_details.technology)    WECDevDetails += 'Technology: '    + s.WECAdbor.results.device_details.technology    + '\\n';\n        if (s.WECAdbor.results.device_details.device_status) WECDevDetails += 'Device Status: ' + s.WECAdbor.results.device_details.device_status + '\\n';\n    }\n\n    if (s.WECAdbor.results.product_details && s.WECAdbor.results.product_details[0]) {\n        WECProdDetails = '\\nProduct Details\\n---------------\\n';\n        for (let i = 0; i < s.WECAdbor.results.product_details.length; i++) {\n            if (s.WECAdbor.results.product_details[i].product_name)        WECProdDetails += 'Product Name: ' + s.WECAdbor.results.product_details[i].product_name        + '\\n';\n            if (s.WECAdbor.results.product_details[i].performance_message) WECProdDetails += 'Availabilty: '  + s.WECAdbor.results.product_details[i].performance_message + '\\n';\n            if (s.WECAdbor.results.product_details[i].upgrade_message)     WECProdDetails += 'Upgrades: '     + s.WECAdbor.results.product_details[i].upgrade_message     + '\\n';\n            if (s.WECAdbor.results.product_details[i].congestion_message)  WECProdDetails += 'Congestion: '   + s.WECAdbor.results.product_details[i].congestion_message  + '\\n';\n            if (s.WECAdbor.results.product_details[i].dl_min && s.WECAdbor.results.product_details[i].dl_max && \n                s.WECAdbor.results.product_details[i].ul_min && s.WECAdbor.results.product_details[i].ul_max &&\n                s.WECAdbor.results.product_details[i].busy_hour) {\n                WECProdDetails += 'Download: '  + s.WECAdbor.results.product_details[i].dl_min + '-'+ s.WECAdbor.results.product_details[i].dl_max    + ' Mbps ';\n                WECProdDetails += 'Upload: '    + s.WECAdbor.results.product_details[i].ul_min + '-'+ s.WECAdbor.results.product_details[i].ul_max    + ' Mbps ';\n                WECProdDetails += 'Busy Hour: ' + s.WECAdbor.results.product_details[i].busy_hour + '\\n';\n            }\n            if (s.WECAdbor.results.product_details[i].performance && s.WECAdbor.results.product_details[i].throughput &&\n                s.WECAdbor.results.product_details[i].placement) {\n                WECProdDetails += 'Throughput: ' + s.WECAdbor.results.product_details[i].throughput  + ' ';\n                WECProdDetails += 'Strength: '   + s.WECAdbor.results.product_details[i].performance + ' ';\n                WECProdDetails += 'Placement: '  + s.WECAdbor.results.product_details[i].placement  + '\\n';\n            }\n            WECProdDetails += '\\n';\n        }\n    }\n\n    let mergeLink = '\\nMerge Result Link: https://merge.in.telstra.com.au/serviceCheck/view/html/'  + data.id;\n-%>\n<%= WECHeading -%>\n<%= WECAlertMsg -%>\n<%= WECOutageSmy -%>\n<%= WECExperCheck -%>\n<%= WECDevDetails -%>\n<%= WECProdDetails -%>\n<% } -%>\n"}]}