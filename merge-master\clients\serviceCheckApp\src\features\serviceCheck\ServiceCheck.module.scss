@use '@able/web/src/index' as able;

.able-ModalContent--Expansive .modal-scroll {
    overscroll-behavior: contain;
}

.serviceCheckStatus {
    margin-left: auto;
}

.infoPanel {
    margin-top: 1rem;
    margin-bottom: 1rem;
}

.serviceCheck {
    display: flex;
    flex-direction: column;
    margin-top: 1rem;
    margin-bottom: 5rem;
    max-width: 1860px;
    margin-left: auto;
    margin-right: auto;

    .searchRow {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 2rem;
        align-items: stretch;
    }

    .tabNavigation {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;

        .tabs {
            display: flex;
            gap: 1rem;
            button {
                all: unset;
                cursor: pointer;
            }

            .activeTab {
                box-shadow: inset 0px -2px 0px 0px able.color(materialBaseBrandPrimary);
            }
        }
    }

    .tabContent {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-top: 2rem;
    }
}
