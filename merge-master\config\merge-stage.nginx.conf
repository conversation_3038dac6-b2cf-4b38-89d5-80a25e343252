server {
    listen 80;
    server_name merge-stage.in.telstra.com.au *.merge-stage.in.telstra.com.au;
    return 301 https://$server_name$request_uri;
}

upstream backend_merge_staging {
    hash $remote_addr consistent;
    server s1.merge-stage.in.telstra.com.au:443;
    server s2.merge-stage.in.telstra.com.au:443;
}

## Main app Server
server {
    listen 443 ssl;
    listen [::]:443 ssl;

    server_name                 merge-stage.in.telstra.com.au;
    ssl_certificate             /etc/ssl/certs/merge_signed_v2.crt;
    ssl_certificate_key         /etc/ssl/certs/merge_signed_v2.key;

    client_max_body_size 10M;

    location /public {
        proxy_pass https://backend_merge_testing;
        proxy_set_header Host '*.merge-stage.in.telstra.com.au';
        access_log off;
        expires -1;
    }

    location /favicon.ico {
        proxy_pass https://backend_merge_testing;
        proxy_set_header Host '*.merge-testing.in.telstra.com.au';
        access_log off;
        expires -1;
    }

    location / {
        if (-f /opt/merge/merge-staging/config/maintenance_on) {
            return 503;
        }

        proxy_pass https://backend_merge_staging;
        proxy_http_version 1.1;

        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;

        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host '*.merge-stage.in.telstra.com.au';
        proxy_cache_bypass $http_upgrade;

        proxy_next_upstream error timeout http_502;
    }
}

server {
    listen 443 ssl;
    listen [::]:443 ssl;

    server_name                 *.merge-stage.in.telstra.com.au;
    ssl_certificate             /etc/ssl/certs/merge_signed_v2.crt;
    ssl_certificate_key         /etc/ssl/certs/merge_signed_v2.key;

    client_max_body_size 10M;

    location /public {
        alias /opt/merge/merge-staging/public;
        access_log off;
        expires -1;
    }

    location /favicon.ico {
        proxy_pass http://localhost:3006;
        access_log off;
        expires -1;
    }

    location / {
        if (-f /opt/merge/merge-staging/config/maintenance_on) {
            return 503;
        }

        proxy_pass http://localhost:3006;
        proxy_http_version 1.1;

        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;

        set_real_ip_from 127.0.0.1;
        real_ip_header X-Real-IP;
    }

    # Maintenance pages.
    error_page 503 /maintenance.html;
    location = /maintenance.html {
        root /opt/merge/merge-staging/public/maintenance/;
    }
}
