'use strict';

import chai from 'chai';
import _ from 'lodash';

import config from '../config.js';
import GeoliteCall from '../../db/model/geoliteCall.js';
import { geoliteCallCountCheck } from '../../modules/geoliteCallCount.js';


describe('check()', () => {
    before(() => {
        global.gConfig = _.cloneDeep(config.default);
        global.gConfig.APIUriConfig = {
            GeoliteAPI: {
                cityURL: "https://test.host/path/to/api"
            }
        };
    });

    afterEach(async() => {
        await GeoliteCall.deleteMany({});
    });

    it('undefined', async() => {
        await geoliteCallCountCheck(undefined);
        chai.expect(await GeoliteCall.find({}).countDocuments()).to.equal(0);
    });

    it('null', async() => {
        await geoliteCallCountCheck(null);
        chai.expect(await GeoliteCall.find({}).countDocuments()).to.equal(0);
    });

    it('Invalid URL', async() => {
        await geoliteCallCountCheck("<<notaurl>>");
        chai.expect(await GeoliteCall.find({}).countDocuments()).to.equal(0);
    });

    it('Valid URL, matching GeoliteAPI in config test 1', async() => {
        await geoliteCallCountCheck("https://test.host/call");
        chai.expect(await GeoliteCall.find({}).countDocuments()).to.equal(1);
    });

    it('Valid URL, matching GeoliteAPI in config test 1', async() => {
        await geoliteCallCountCheck("https://test.host/path/to/api/*******");
        chai.expect(await GeoliteCall.find({}).countDocuments()).to.equal(1);
    });

    it('Valid URL, matching GeoliteAPI in config test 1', async() => {
        await geoliteCallCountCheck("https://test.host/another/alternate/path/*******");
        chai.expect(await GeoliteCall.find({}).countDocuments()).to.equal(1);
    });

    it('Valid URL, not matching GeoliteAPI in config', async() => {
        await geoliteCallCountCheck("http://test.host/call");
        chai.expect(await GeoliteCall.find({}).countDocuments()).to.equal(0);
    });

    it('Valid URL, not matching GeoliteAPI in config', async() => {
        await geoliteCallCountCheck("https://another.host/path/to/api");
        chai.expect(await GeoliteCall.find({}).countDocuments()).to.equal(0);
    });

    it('Valid URL, not matching GeoliteAPI in config', async() => {
        await geoliteCallCountCheck("https://another.host");
        chai.expect(await GeoliteCall.find({}).countDocuments()).to.equal(0);
    });
});
