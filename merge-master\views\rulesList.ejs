<!-- v1 -->
<%- include('header', {}); %>
<%- include('menu', {currentTab: 'Help'}); %>
<div class="container">
    <br>
    <div class="jumbotron">
        <h2 class="display-4">Rules and Data Sources R<%= SCRules.version %></h2>
        <span class="display-5"> Updated on <%= SCRules.updatedOn %> by <%= SCRules.updatedBy %></span>
        <br><hr>
        <div id="listOfSources" class="">
            <h3 class="display-5">Data Sources / Systems Interfaces</h3>
            <div >
                <% SCRules.sources.forEach(function(source){ %>
                    <div class="row">
                        <a class='expand btn-sm' data-toggle='collapse' data-target='#Source-<%= source.name %>'>
                            <span style='color:blue' class='fas fa-plus-square' title='Expand/Collapse'></span>
                        </a>
                        [L<%= source.level %>]&nbsp;<a target="<%= source.name %>" href="<%= global.gConfig.wikiBaseURL + (source.wikiPage?source.wikiPage:(source.api && APIs[source.api] && APIs[source.api].wikiPage?APIs[source.api].wikiPage:'')) %>"><%= source.name %></a>:&nbsp;<span class="<%= source.active?'':'font-italic text-muted' %>"> <%= source.title %><%= source.active?'':' (Deactive)' %></span>
                    </div>
                    <div id='Source-<%= source.name %>' class='border rounded collapse in'>
                        <div class="row">
                            <div class="col-sm-10">
                                <b>Description</b> : <%= source.description %></br>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm">
                                <b>API name</b> : <%= source.api %>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm">
                                <b>Parameters</b> : <%= source.param %>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm">
                                <b>Depend to Sources</b> : <%= source.preSources %></br>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm">
                                <b>Depended to Rules</b>: <pre><%= source.preRules %></pre>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm">
                                <b>Condition To Collect</b> : <%= source.preCondition %>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm">
                                <b>Error Message</b>: <%= source.errorMessage %>
                            </div>
                        </div>
                    </div>
                  <% })%>
                </div>
        </div>

        <div id="listOfRules" class="">
            <h3 class="display-5">Rules</h3>
            <div >
            <% SCRules.rules.forEach(function(rule){ %>
                <div class="row">
                    <a class='expand btn-sm' data-toggle='collapse' data-target='#Rule-<%= rule.name %>'>
                        <span style='color:blue' class='fas fa-plus-square' title='Expand/Collapse'></span>
                    </a>
                    [L<%= rule.level %>][<%= rule.ruleType %>][<%= rule.isWarning ? 'W' : '--' %>]&nbsp;<a target="<%= rule.name %>" href="<%= global.gConfig.wikiBaseURL + (rule.wikiPage?rule.wikiPage:rule.name) %>"><%= rule.name %></a>:&nbsp;<span class="<%= rule.active?'':'font-italic text-muted' %>"> <%= rule.title %><%= rule.active?'':' (Deactive)' %></span>
                </div>
                <div id='Rule-<%= rule.name %>' class='border rounded collapse in'>
                    <div class="row">
                        <div class="col-sm-10">
                            <b>Description</b> : <%= rule.description %></br>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm">
                            <b>Pre-Sources</b> : <%= rule.preSources %>
                        </div>
                        <div class="col-sm">
                            <b>Pre-Rules</b> : <%= rule.preRules %>
                        </div>
                        <div class="col-sm">
                                <b>Rule Type</b>: <%= rule.ruleType %>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-10">
                            <b>Precondition Msg</b> : <%= rule.preConditionMsg %></br>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm">
                            <b>Precondition</b>: <pre><%= rule.preCondition %></pre>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm">
                            <b>True Msg</b> : <%= rule.trueMsg %>
                        </div>
                        <div class="col-sm">
                            <b>False Msg</b> : <%= rule.falseMsg %>
                        </div>
                        <div class="col-sm">
                            <b>Error Msg</b>: <%= rule.errorMsg %>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm">
                            <b>Rule Code</b>: <pre><%= rule.ruleCode %></pre>
                            <b>Value Msg</b>: <pre><%= rule.valueMsgStm %></pre>
                            <b>Extra Info</b>: <pre><%= rule.extraInfo %></pre>
                        </div>
                    </div>
                </div>
              <% })%>
            </div>
        </div>
    </div>
</div>
<%- include('footer', {}); %>
