import axios from 'axios'
import {
    ServiceDetailsModel,
    TestActionsModel,
    TestActionsResponse,
} from '../infrastructure/models'
import axiosInstance from './axiosInstance'
import legacyAxiosInstance from './axiosLegacyInstance'

interface ServiceResponse {
    id: ServiceDetailsModel['id']
    createdBy: ServiceDetailsModel['createdBy']
    createdOn: ServiceDetailsModel['createdOn']
    status: ServiceDetailsModel['status']
    feedback: ServiceDetailsModel['feedback']
    productTypes: ServiceDetailsModel['productTypes']
    customerDetails: ServiceDetailsModel['customerDetails']
    productDetails: ServiceDetailsModel['productDetails']
    deviceDetails: ServiceDetailsModel['deviceDetails']
    accountStatus: ServiceDetailsModel['accountStatus']
    outages: ServiceDetailsModel['outages']
    activeIncidents: ServiceDetailsModel['activeIncidents']
    testPackages: ServiceDetailsModel['testPackages']
    testActions?: ServiceDetailsModel['testActions']
    nextBestAction?: ServiceDetailsModel['nextBestAction']
    textTemplates: ServiceDetailsModel['textTemplates']
    testResults: ServiceDetailsModel['testResults']
    diagrams: ServiceDetailsModel['diagrams']
}

// Custom error class to ensure we're throwing an Error instance
export class ApiError extends Error {
    status: number
    constructor(status: number, message: string) {
        super(message)
        this.status = status
        Object.setPrototypeOf(this, ApiError.prototype)
    }
}
export const addOrModifyFeedback = async (
    serviceCheckId: string,
    feedback: { isPositive: boolean; message?: string }
): Promise<ServiceResponse> => {
    try {
        const response = await legacyAxiosInstance.put(
            `/${serviceCheckId}/feedback`,
            feedback
        )
        return response.data as ServiceResponse
    } catch (error: unknown) {
        if (axios.isAxiosError(error) && error.response) {
            throw new ApiError(
                error.response.status,
                error.response.data?.message || 'Add/modify feedback failed'
            )
        }
        throw new ApiError(500, 'An unexpected error occurred')
    }
}

export const removeFeedback = async (
    serviceCheckId: string
): Promise<ServiceResponse> => {
    try {
        const response = await legacyAxiosInstance.delete(
            `/${serviceCheckId}/feedback`
        )
        return response.data as ServiceResponse
    } catch (error: unknown) {
        if (axios.isAxiosError(error) && error.response) {
            throw new ApiError(
                error.response.status,
                error.response.data?.message || 'Remove feedback failed'
            )
        }
        throw new ApiError(500, 'An unexpected error occurred')
    }
}

export const retryServiceCheck = async (
    serviceCheckId: string
): Promise<ServiceResponse> => {
    try {
        const response = await axiosInstance.patch(
            `records/${serviceCheckId}/retryStart`
        )
        return response.data as ServiceResponse
    } catch (error: unknown) {
        if (axios.isAxiosError(error) && error.response) {
            throw new ApiError(
                error.response.status,
                error.response.data?.message || 'Retry service check failed'
            )
        }
        throw new ApiError(500, 'An unexpected error occurred')
    }
}

export const getTextTemplate = async (
    serviceId: string,
    template: string
): Promise<string | undefined> => {
    if (template === '') return

    try {
        const response = await legacyAxiosInstance.get(
            `/${serviceId}/renderTemplate?name=${template}`
        )
        return response.data as string
    } catch (error: unknown) {
        if (axios.isAxiosError(error) && error.response) {
            throw new ApiError(
                error.response.status,
                error.response.data?.message || 'Text template failed'
            )
        }
        throw new ApiError(500, 'An unexpected error occurred')
    }
}

export const runTestAction = async ({
    serviceId,
    rule,
    additionalParameters,
}: {
    serviceId: string
    rule: string
    additionalParameters: Record<string, string>
}): Promise<TestActionsResponse> => {
    try {
        const response = await legacyAxiosInstance.post(
            `/${serviceId}/action/`,
            {
                rule,
                userInputs: JSON.stringify(additionalParameters),
            }
        )

        return {
            updatedRule: response.data.updatedRule as TestActionsModel,
            statusCode: response.status,
        }
    } catch (error: unknown) {
        if (axios.isAxiosError(error) && error.response) {
            if (error.response.status === 422) {
                // Handle 422 error by returning a TestActionsResponse with null updatedRule
                return {
                    updatedRule: null,
                    statusCode: 422,
                }
            }
            // If any other error, we need to recover
            return {
                updatedRule: null,
                statusCode: error.response.status,
                error: error.message,
            }
        }
        throw new ApiError(500, 'An unexpected error occurred')
    }
}

export const getService = async (
    serviceId: string
): Promise<ServiceResponse> => {
    try {
        const response = await axiosInstance.get<ServiceResponse>(
            `/records/${serviceId}`
        )
        return response.data
    } catch (error: unknown) {
        if (axios.isAxiosError(error) && error.response) {
            throw new ApiError(
                error.response.status,
                error.response.data?.message || 'Service was not found'
            )
        }
        throw new ApiError(500, 'An unexpected error occurred')
    }
}

export const searchServiceNumber = async (
    serviceNumber: string
): Promise<ServiceResponse> => {
    try {
        // Cleanse the number
        const cleansedFnn = serviceNumber.replace(/[^0-9a-zA-Z-]/g, '')

        const response = await axiosInstance.post<ServiceResponse>('/start', {
            fnn: cleansedFnn,
        })
        return response.data
    } catch (error: unknown) {
        if (axios.isAxiosError(error) && error.response) {
            throw new ApiError(
                error.response.status, // e.g., 404
                error.response.data?.message || 'Service was not found'
            )
        }
        throw new ApiError(500, 'An unexpected error occurred')
    }
}