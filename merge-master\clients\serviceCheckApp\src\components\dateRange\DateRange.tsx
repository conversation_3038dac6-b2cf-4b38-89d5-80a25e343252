import { TextStyle } from '@able/react'
import styles from './DateRange.module.scss'

interface DateRangeProps {
    startDate: string
    endDate: string
    onStartDateChange: (startDate: string) => void
    onEndDateChange: (endDate: string) => void
    errorMessage?: string
}

export const DateRange = ({
    startDate,
    endDate,
    onStartDateChange,
    onEndDateChange,
    errorMessage,
}: DateRangeProps) => {
    const formatDateForInput = (date: string) => {
        const d = new Date(date)
        const year = d.getFullYear()
        const month = (d.getMonth() + 1).toString().padStart(2, '0')
        const day = d.getDate().toString().padStart(2, '0')
        const hours = d.getHours().toString().padStart(2, '0')
        const minutes = d.getMinutes().toString().padStart(2, '0')
        return `${year}-${month}-${day}T${hours}:${minutes}`
    }

    return (
        <div className={styles.dateRange}>
            <div className={styles.inputs}>
                <TextStyle alias="LabelA1">Start:</TextStyle>
                <input
                    type="datetime-local"
                    value={formatDateForInput(startDate)}
                    onChange={(e) => onStartDateChange(e.target.value)}
                />
                <TextStyle alias="LabelA1">End:</TextStyle>
                <input
                    type="datetime-local"
                    value={formatDateForInput(endDate)}
                    onChange={(e) => onEndDateChange(e.target.value)}
                />
            </div>

            {errorMessage && (
                <span className={styles.error}>{errorMessage}</span>
            )}
        </div>
    )
}
