body {
  font-family: 'Telstra Akkurat', 'Akkurat','Lato', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

h1,h2,h3,h4,h5,h6 {
  font-family: 'Telstra Akkurat', 'Ak<PERSON><PERSON>', 'Lato', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-weight: 700;
}

header.masthead {
  position: relative;
  background-color: #0099f8;
  background: url("/public/images/bg-masthead.jpg") no-repeat center center;
  background-size: cover;
  padding-top: 8rem;
  padding-bottom: 8rem;
}

header.masthead .overlay {
  position: absolute;
  background-color: #0099f8;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  opacity: 0.3;
}

header.masthead h1 {
  font-size: 2rem;
}

@media (min-width: 768px) {
  header.masthead {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }
  header.masthead h1 {
    font-size: 3rem;
  }
}

.showcase .showcase-text {
  padding: 3rem;
}

.showcase .showcase-img {
  min-height: 30rem;
  background-size: cover;
}

@media (min-width: 768px) {
  .showcase .showcase-text {
    padding: 5rem;
  }
}

.features-icons {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.features-icons .features-icons-item {
  max-width: 20rem;
}

.features-icons .features-icons-item .features-icons-icon {
  height: 5rem;
}

.features-icons .features-icons-item .features-icons-icon span {
  font-size: 4.5rem;
}

.features-icons .features-icons-item:hover .features-icons-icon span {
  font-size: 5rem;
}

.testimonials {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.testimonials .testimonial-item {
  max-width: 18rem;
}

.testimonials .testimonial-item img {
  max-width: 8rem;
  -webkit-box-shadow: 0px 5px 5px 0px #adb5bd;
  box-shadow: 0px 5px 5px 0px #adb5bd;
}

.call-to-action {
  position: relative;
  background: url("/public/images/bg-masthead.jpg") no-repeat center center;
  background-size: cover;
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.call-to-action .overlay {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  opacity: 0.3;
}
