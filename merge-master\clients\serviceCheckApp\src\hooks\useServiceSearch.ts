import { useNavigate } from 'react-router'
import { useSearchServiceNumber } from '../api/mutations/useSearchServiceNumber'
import { AppState, ServiceDetailsModel } from '../infrastructure/models'
import { useAppState } from './useAppState'
export interface UseServiceSearch {
    onSearch: () => Promise<boolean>
    isPending: boolean
    isError: boolean
    error: Error | null
}

type InputType =
    | 'fnn'
    | 'phoneNumber'
    | 'deviceName'
    | 'internationalLink'
    | 'avc'
    | 'ovc'
    | 'bdsl'
    | 'uuid'
    | 'domId'
    | 'cfsId'
    | ''

const determineInputType = (incomingFnn: string): InputType => {
    const fnn = incomingFnn.replace(/[^0-9a-zA-Z-]/g, '')

    if (/^[A-Z]\d{7}[A-Z]$/.test(fnn)) {
        return 'fnn'
    } else if (/^A?\d{9,10}$/i.test(fnn) || /^A?61\d{9}$/i.test(fnn)) {
        return 'phoneNumber'
    } else if (
        /^(?<customerCode>[A-Z0-9]{3})(?<state>[A-Z]{1})(?<locationCode>[A-Z]{3})(?<deviceType>[A-Z]{1})(?<deviceCount>\d{2})(?<supplierCode>[A-Z]{1})(?<productCode>\d{2})$/i.test(
            fnn
        ) ||
        /^(?<customerCode>[A-Z0-9]{5})(?<state>[A-Z]{1})(?<locationCode>[A-Z]{3})(?<deviceType>[A-Z]{1})(?<deviceCount>\d{2})(?<supplierCode>[A-Z]{1})(?<productCode>\d{2})$/i.test(
            fnn
        )
    ) {
        return 'deviceName'
    } else if (/^([A-Z0-9]+ )+\d{7,9}$/i.test(fnn)) {
        return 'internationalLink'
    } else if (/^AVC\d{12}$/.test(fnn)) {
        return 'avc'
    } else if (/^OVC\d{12}$/.test(fnn)) {
        return 'ovc'
    } else if (/^Y\d{11}N$/.test(fnn)) {
        return 'bdsl'
    } else if (
        /^[0-9a-f]{8}-[0-9a-f]{4}-[4][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(
            fnn
        )
    ) {
        return 'uuid'
    } else if (/^(.{9}\d{2}[a-z]\d{2}|.{11}[a-z]{3})$/i.test(fnn)) {
        return 'domId'
    } else if (/^[a-z]{4}\d{8,12}$/i.test(fnn)) {
        return 'cfsId'
    } else {
        return ''
    }
}

export const useServiceSearch = (): UseServiceSearch => {
    const { appState, setAppState } = useAppState()
    const navigate = useNavigate()
    const {
        mutateAsync: runServiceCheck,
        isPending,
        isError,
        error,
    } = useSearchServiceNumber()

    const onSearch = async (): Promise<boolean> => {
        // Empty input is handled by ABLE Text component
        // Perform Regex check
        const type = determineInputType(appState.serviceNumber)
        if (type === '') {
            setAppState({
                ...appState,
                serviceNumberErrorMessage: 'Service Number is invalid',
            })
            return false
        }

        let data: ServiceDetailsModel | null = null
        try {
            data = await runServiceCheck(appState.serviceNumber)
            // Once successfully found, we push to the url
            navigate(`?id=${data.id}`)
        } catch (err: unknown) {
            console.log(err)
            return false
        }

        if (!data) return false

        setAppState({
            ...appState,
            serviceDetails: data,
            testPackages: data.testPackages ?? [],
            testActions: data.testActions ?? [],
            nextBestAction: data.nextBestAction,
            textTemplates: data.textTemplates,
            createdBy: data.createdBy,
            createOn: data.createdOn,
            id: data.id ?? '',
        } as AppState)

        return true
    }

    return {
        onSearch,
        isPending,
        isError,
        error,
    }
}
