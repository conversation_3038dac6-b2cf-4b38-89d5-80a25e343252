'use strict';

import _ from 'lodash';
import fs from 'fs';
import os from 'os';
import debug from 'debug';

// Only sets config if it isn't set before (can be set by tests)
// Can be refactored if a better solution is found to set config used in unit tests
if (!global.gConfig) {
    // Currently on how to import JSON files using ES6 (may be an experimental feature),
    // reads the files on module load instead
    const config = JSON.parse(await fs.promises.readFile('./config/config.json'));
    const subdomains = JSON.parse(await fs.promises.readFile('./config/subdomains.json'));
    const packageConfig = JSON.parse(await fs.promises.readFile('./package.json'));

    const debugMsg = debug('merge:config');

    var hostname = process.env.MERGE_HOSTNAME ? process.env.MERGE_HOSTNAME : os.hostname();
    var domainName  = '';
    var domainNameR = '';
    var domainNameS = '';
    var socketIOURL = '';
    var matomoTracker = false;

    if (!process.env.MERGE_ENV || process.env.MERGE_ENV == 'localDev') {
        domainName = 'localhost';
        domainNameR = 'localhost';
        domainNameS = '127.0.0.1';
        socketIOURL = 'http://'+ domainNameS + ':3004';
    } else if (process.env.MERGE_ENV == 'dev') {
        domainName = 'merge-dev';
        domainNameR = domainName + '.in.telstra.com.au';
        if (hostname in subdomains.hosts) {
            domainNameS = subdomains.hosts[hostname] + "." + domainNameR;
        } else {
            domainNameS = domainNameR;
        }
        socketIOURL = 'http://'+ domainNameS + ':8005';
    } else {
        if (process.env.MERGE_ENV == 'testing') {
            domainName = 'merge-testing';
        } else if (process.env.MERGE_ENV == 'stage') {
            domainName = 'merge-stage';
        } else if (process.env.MERGE_ENV == 'prd') {
            domainName = 'merge';
            matomoTracker = true;
        }
        domainNameR = domainName + '.in.telstra.com.au';
        if (hostname in subdomains.hosts) {
            domainNameS = subdomains.hosts[hostname] + "." + domainNameR;
        } else {
            domainNameS = domainNameR;
        }
        socketIOURL = 'https://'+ domainNameS;
    }

    // module variables
    const defaultConfig = config.default;
    const environment = process.env.MERGE_ENV || 'localDev';
    const environmentConfig = config[environment];
    var finalConfig = _.merge(defaultConfig, environmentConfig);

    // Sets version from package.json
    defaultConfig.ver = packageConfig.version;

    // log global.gConfig
    debugMsg('Run by OS User:', os.userInfo() );
    debugMsg(`Environment variable MERGE_ENV:${process.env.MERGE_ENV}, running with environment ${environment}`);
    debugMsg(`global.gConfig: ${JSON.stringify(finalConfig, null, 4)}`);

    global.gConfig = finalConfig;
    global.gHostname = hostname;
    global.gdomainName = domainName;
    global.gdomainNameR = domainNameR;
    global.gdomainNameS = domainNameS;
    global.gSocketIOURL = socketIOURL;
    global.gMatomoTracker = matomoTracker;

    debugMsg(`domainName,R,S,Socket: ${global.gdomainName}, ${global.gdomainNameR}, ${global.gdomainNameS}, ${global.gSocketIOURL}`);
}
