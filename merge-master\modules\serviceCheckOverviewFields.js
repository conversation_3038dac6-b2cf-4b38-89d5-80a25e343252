
import Ajv from 'ajv';
import fs from 'fs';
import _ from 'lodash';


import logger from './logger.js';

const ajv = new Ajv({ allErrors: true });
const serviceCheckOverviewFields = JSON.parse(await fs.promises.readFile('./config/serviceCheckOverviewFields.json'));
const serviceCheckOverviewFieldsSchema = JSON.parse(await fs.promises.readFile('./config/serviceCheckOverviewFieldsSchema.json'));
const validate = ajv.compile(serviceCheckOverviewFieldsSchema);

if (!validate(serviceCheckOverviewFields)) {
    logger.error(`Service check info fields: config file does not match schema, ${JSON.stringify(validate.errors)}`);

    throw new Error(`Service check info fields configuration file does not match ` +
                    `JSON schema\n` +
                    `${JSON.stringify(validate.errors, null, 4)}`);
}


export function getServiceCheckOverviewFields() {
    return serviceCheckOverviewFields;
}

export function getServiceCheckOverviewFieldsForProductTypes(serviceCheckProductTypes) {
    return serviceCheckOverviewFields.filter((overviewField) => {
        let includesProductType = false;

        for (let productType of overviewField.productTypes) {
            if (serviceCheckProductTypes.includes(productType)) {
                includesProductType = true;
            }
            // Overview field with 'All' in their product types are always included
            if (productType === 'All') {
                includesProductType = true;
            }
        }

        return includesProductType;
    });
}

function extractNBNHealthMetrics(healthCategoryItems, includeTypeKey=false) {
    let metrics = {};

    if (!Array.isArray(healthCategoryItems)) {
        return metrics;
    }

    if (includeTypeKey) {
        for (let i = 0; i < healthCategoryItems.length; i++) {
            if (healthCategoryItems[i] && healthCategoryItems[i].id && healthCategoryItems[i]['@type']) {
                if (!metrics[healthCategoryItems[i].id]) {
                    metrics[healthCategoryItems[i].id] = {};
                }
                metrics[healthCategoryItems[i].id][healthCategoryItems[i]['@type']] = healthCategoryItems[i];
            }
        }
    } else {
        for (let i = 0; i < healthCategoryItems.length; i++) {
            if (healthCategoryItems[i] && healthCategoryItems[i].id) {
                metrics[healthCategoryItems[i].id] = healthCategoryItems[i];
            }
        }
    }

    return metrics;
}

export function extractOverviewFields(serviceCheckRecord, overviewFields) {
    const data = serviceCheckRecord.toJSON();
    const r = data.rulesData;
    const s = data.sourcesData;

    let magpieProductData = null;
    if (_.isPlainObject(s.MAGPIE?.rawData)) {
        for (let rawDataKey in s.MAGPIE.rawData) {
            // Looks for an object in rawData with the 'Product Name' key
            if (s.MAGPIE.rawData[rawDataKey] && s.MAGPIE.rawData[rawDataKey]['Product Name']) {
                magpieProductData = s.MAGPIE.rawData[rawDataKey];
                break;
            }
        }
    }

    let plannedOutages = [];
    let unplannedOutages = [];
    let powerOutages = [];
    let nbnOutages = [];
    if (Array.isArray(s.CMART?.CMART)) {
        for (let cmartCase of s.CMART.CMART) {
            plannedOutages.push(cmartCase);
        }
    }

    if (Array.isArray(s.CMARTCarriageFnn?.CMART)) {
        for (let cmartCase of s.CMARTCarriageFnn.CMART) {
            plannedOutages.push(cmartCase);
        }
    }

    if (_.isPlainObject(s.CONEN?.tickets)) {
        for (let conenId in s.CONEN.tickets) {
            if (_.isPlainObject(s.CONEN.tickets[conenId])) {
                unplannedOutages.push(s.CONEN.tickets[conenId]);
            }
        }
    }

    if (Array.isArray(s['Hangar - National Power Outage Data']?.powerData)) {
        for (let powerDataRecord of s['Hangar - National Power Outage Data'].powerData) {
            if (_.isPlainObject(powerDataRecord)) {
                powerOutages.push(powerDataRecord);
            }
        }
    }

    // Code from NBNServiceHealthModule with modifications
    let serviceHealthData = s['NBN - Service Health'] ? s['NBN - Service Health'] : null;    
    if (serviceHealthData) {
        let outageMetrics;
        let healthCategory = serviceHealthData.healthCategory && Array.isArray(serviceHealthData.healthCategory) ? serviceHealthData.healthCategory : [];
        
        let healthCategoryMetrics = {};
        for (let i = 0; i < healthCategory.length; i++) {
            if (healthCategory[i] && healthCategory[i].type && Array.isArray(healthCategory[i].healthCategoryItem)) {
                healthCategoryMetrics[healthCategory[i].type] = healthCategory[i].healthCategoryItem;
            }
        }

        outageMetrics = extractNBNHealthMetrics(healthCategoryMetrics.Outage);

        if (outageMetrics?.currentOutage?.value) {
            nbnOutages.push({
                outage_type: "Current Outage",
                value: outageMetrics.currentOutage.value === 'true' ? 'Yes' : 'No'
            });
        } 

        if (outageMetrics?.plannedOutageId?.value) {
            nbnOutages.push({
                outage_type: "Planned Outage Id",
                value: outageMetrics.plannedOutageId.value
            });
        }

        if (outageMetrics?.unplannedOutageIds?.value) {
            nbnOutages.push({
                outage_type: "Unplanned Outage Id",
                value: outageMetrics.unplannedOutageIds.value
            });
        }

        if (outageMetrics?.networkActivityId?.value) {
            nbnOutages.push({
                outage_type: "Network Activity Id",
                value: outageMetrics.networkActivityId.value
            });
        } 
    }


    let siiamActiveCases = [];
    let siiamHistoryCases = [];
    let serviceCentralIncidents = [];
    let promiseTasks = [];

    function isRecentDate(date) {
        let timestamp = Date.parse(date);

        if (isNaN(timestamp)) {
            return false;
        }

        // Considers tickets which are less than 1 year old
        let earliestDate = new Date();
        earliestDate.setFullYear(earliestDate.getFullYear() - 1);

        return earliestDate < timestamp;
    }

    if (_.isPlainObject(s.SIIAMfnn?.activeCases)) {
        for (let siiamCaseId in s.SIIAMfnn.activeCases) {
            if (_.isPlainObject(s.SIIAMfnn.activeCases[siiamCaseId]) &&
                isRecentDate(s.SIIAMfnn.activeCases[siiamCaseId].CREATION_TIME)) {
                siiamActiveCases.push({
                    id: siiamCaseId,
                    creationTime: s.SIIAMfnn.activeCases[siiamCaseId].CREATION_TIME,
                    fnn: data.fnn,
                    status: s.SIIAMfnn.activeCases[siiamCaseId].STATUS
                });
            }
        }
    }

    if (_.isPlainObject(s.SIIAMCarriageFnn?.activeCases)) {
        for (let siiamCaseId in s.SIIAMCarriageFnn.activeCases) {
            if (_.isPlainObject(s.SIIAMCarriageFnn.activeCases[siiamCaseId]) &&
                isRecentDate(s.SIIAMCarriageFnn.activeCases[siiamCaseId].CREATION_TIME)) {
                siiamActiveCases.push({
                    id: siiamCaseId,
                    creationTime: s.SIIAMCarriageFnn.activeCases[siiamCaseId].CREATION_TIME,
                    fnn: data.carriageFNN,
                    status: s.SIIAMCarriageFnn.activeCases[siiamCaseId].STATUS
                });
            }
        }
    }

    if (_.isPlainObject(s.SIIAMfnn?.caseHistory)) {
        for (let siiamCaseId in s.SIIAMfnn.caseHistory) {
            if (_.isPlainObject(s.SIIAMfnn.caseHistory[siiamCaseId]) &&
                s.SIIAMfnn.caseHistory[siiamCaseId].STATUS === 'Closed' &&
                isRecentDate(s.SIIAMfnn.caseHistory[siiamCaseId].CREATION_TIME)) {
                siiamHistoryCases.push({
                    id: siiamCaseId,
                    creationTime: s.SIIAMfnn.caseHistory[siiamCaseId].CREATION_TIME,
                    fnn: data.fnn,
                    status: s.SIIAMfnn.caseHistory[siiamCaseId].STATUS
                });
            }
        }
    }

    if (_.isPlainObject(s.SIIAMCarriageFnn?.caseHistory)) {
        for (let siiamCaseId in s.SIIAMCarriageFnn.caseHistory) {
            if (_.isPlainObject(s.SIIAMCarriageFnn.caseHistory[siiamCaseId]) &&
                s.SIIAMCarriageFnn.caseHistory[siiamCaseId].STATUS === 'Closed' &&
                isRecentDate(s.SIIAMCarriageFnn.caseHistory[siiamCaseId].CREATION_TIME)) {
                siiamHistoryCases.push({
                    id: siiamCaseId,
                    creationTime: s.SIIAMCarriageFnn.caseHistory[siiamCaseId].CREATION_TIME,
                    fnn: data.carriageFNN,
                    status: s.SIIAMCarriageFnn.caseHistory[siiamCaseId].STATUS
                });
            }
        }
    }

    if (_.isPlainObject(s['Hangar - Service central incident API'])) {
        for (let serviceCentralId in s['Hangar - Service central incident API']) {
            if (_.isPlainObject(s['Hangar - Service central incident API'][serviceCentralId]) &&
                isRecentDate(s['Hangar - Service central incident API'][serviceCentralId].opened_at)) {
                serviceCentralIncidents.push({
                    id: serviceCentralId,
                    openedAt: s['Hangar - Service central incident API'][serviceCentralId].opened_at,
                    fnn: data.fnn,
                    state: s['Hangar - Service central incident API'][serviceCentralId].incident_state_dv
                });
            }
        }
    }

    if (_.isPlainObject(s['Hangar - Service central incident API Carriage FNN'])) {
        for (let serviceCentralId in s['Hangar - Service central incident API Carriage FNN']) {
            if (_.isPlainObject(s['Hangar - Service central incident API Carriage FNN'][serviceCentralId]) &&
                isRecentDate(s['Hangar - Service central incident API Carriage FNN'][serviceCentralId].opened_at)) {
                serviceCentralIncidents.push({
                    id: serviceCentralId,
                    openedAt: s['Hangar - Service central incident API Carriage FNN'][serviceCentralId].opened_at,
                    fnn: data.carriageFNN,
                    state: s['Hangar - Service central incident API Carriage FNN'][serviceCentralId].incident_state_dv
                });
            }
        }
    }

    if (s.Promise && Array.isArray(s.Promise.tasks)) {
        for (let promiseTask of s.Promise.tasks) {
            if (_.isPlainObject(promiseTask) && isRecentDate(promiseTask.SLADate)) {
                promiseTasks.push({
                    crn: promiseTask.crn,
                    slaDate: promiseTask.SLADate,
                    status: promiseTask.status
                });
            }
        }
    }

    siiamActiveCases = Array.from(new Set(siiamActiveCases.map((incident) => incident.id)))
        .map((id) => siiamActiveCases.find((incident) => incident.id === id));
    siiamActiveCases = siiamActiveCases.sort((case1, case2) => {
        let timestamp1 = Date.parse(case1.creationTime);
        let timestamp2 = Date.parse(case2.creationTime);

        if (isNaN(timestamp1)) {
            timestamp1 = 0;
        }

        if (isNaN(timestamp2)) {
            timestamp2 = 0;
        }

        return timestamp2 - timestamp1;
    });

    siiamHistoryCases = Array.from(new Set(siiamHistoryCases.map((incident) => incident.id)))
        .map((id) => siiamHistoryCases.find((incident) => incident.id === id));
    siiamHistoryCases = siiamHistoryCases.sort((case1, case2) => {
        let timestamp1 = Date.parse(case1.creationTime);
        let timestamp2 = Date.parse(case2.creationTime);

        if (isNaN(timestamp1)) {
            timestamp1 = 0;
        }

        if (isNaN(timestamp2)) {
            timestamp2 = 0;
        }

        return timestamp2 - timestamp1;
    });

    serviceCentralIncidents = Array.from(new Set(serviceCentralIncidents.map((incident) => incident.id)))
        .map((id) => serviceCentralIncidents.find((incident) => incident.id === id));
    serviceCentralIncidents = serviceCentralIncidents.sort((case1, case2) => {
        let timestamp1 = Date.parse(case1.openedAt);
        let timestamp2 = Date.parse(case2.openedAt);

        if (isNaN(timestamp1)) {
            timestamp1 = 0;
        }

        if (isNaN(timestamp2)) {
            timestamp2 = 0;
        }

        return timestamp2 - timestamp1;
    });

    promiseTasks = promiseTasks.sort((case1, case2) => {
        let timestamp1 = Date.parse(case1.slaDate);
        let timestamp2 = Date.parse(case2.slaDate);

        if (isNaN(timestamp1)) {
            timestamp1 = 0;
        }

        if (isNaN(timestamp2)) {
            timestamp2 = 0;
        }

        return timestamp2 - timestamp1;
    });

    let basementSwitchDevice = r.MDR021?.demarcPoint?.name && r.MDR021?.demarcPoint?.downInt && r.MDR087?.isNTUDevice === 'No' ? r.MDR021.demarcPoint.name : null;

    let ntuDevice = null;
    if (/(-ntu-)\w+/.test(s.MAGPIE?.PAT?.basementDeviceName)) {
        ntuDevice = s.MAGPIE.PAT.basementDeviceName;
    } else if (/(-ntu-)\w+/.test(r.MDR021?.demarcPoint?.name)) {
        ntuDevice = r.MDR021.demarcPoint.name;
    }

    let nbnEnterpriseEthernetData = s.MAGPIE?.rawData?.IPWAN || s.MAGPIE?.rawData?.TE || {};

    let nbnProductInventoryProductRef = Array.isArray(s.NBNProductInventory?.[0]?.productRef) ? s.NBNProductInventory[0].productRef : [];

    let btdProduct = nbnProductInventoryProductRef.find((product) => {
        return product && product['@type'] === 'BTD';
    });
    let uniEProduct = null;

    if (btdProduct && Array.isArray(btdProduct.productRef)) {
        uniEProduct = btdProduct.productRef.find((product) => {
            return product && product['@type'] === 'UNI-E';
        });
    }

    let productName = null;
    if (typeof s.COMMPilot?.user?.clusterType === 'string') {
        productName = s.COMMPilot.user.clusterType.toUpperCase().replace('STOA', 'BSIP');
    } else if (magpieProductData?.['Product Name']) {
        productName = magpieProductData['Product Name'];
    }

    // CFNN device status
    let cfnnDeviceStatus = null;
    if (s.CFNN && s.CFNN.FirstLine) {
        cfnnDeviceStatus = s.CFNN.FirstLine.replace(/\s+/g, ' ').replace(/<b>/g, ' ').replace(/<\/b>/g, ' ').trim();
    }

    // CFNN registeration status
    let cfnnDeviceRegistrationsStatus = null;
    if (s.COMMPilot && s.COMMPilot.user && typeof s.COMMPilot.user.userId !== 'undefined' && s.COMMPilot.user.userId === '') {
        cfnnDeviceRegistrationsStatus = 'Unassigned';
    } else if (s.COMMPilot && s.COMMPilot.user && s.COMMPilot.user.userType && (s.COMMPilot.user.userType == 'Auto Attendant' || s.COMMPilot.user.userType == 'Call Center' || s.COMMPilot.user.userType == 'Hunt Group')) {
        cfnnDeviceRegistrationsStatus = 'Virtual';
    } else if (s.COMMPilot && s.COMMPilot.registration && s.COMMPilot.registration.registrations && s.COMMPilot.registration.registrations.deviceName && s.COMMPilot.registration.registrations.deviceName.toUpperCase().includes('MOBILE INTEGRATION')) {
        cfnnDeviceRegistrationsStatus = 'Liberate';
    } else if (s.COMMPilot && s.COMMPilot.configuredDevice && s.COMMPilot.configuredDevice.trunk && s.COMMPilot.configuredDevice.trunk.enterpriseTrunk && s.COMMPilot.user && s.COMMPilot.user.groupId && s.COMMPilot.user.groupId.toUpperCase().includes('DID')) {
        cfnnDeviceRegistrationsStatus = 'Enterprise Trunk DID';
    } else if (s.CFNN && s.CFNN['Trunk FNN'] && s.CFNN.FirstLine && s.CFNN.registrations && s.CFNN.registrations[0] && s.CFNN.registrations[0]['Line/Port']) {
        var statusNumber = Number(s.CFNN.FirstLine.replace('<div style=\"li','').replace('(<b>Activated</b>)' ,'').trim());
        var linePortArray = s.CFNN.registrations[0]['Line/Port'].split('@');
        var linePortNumber = linePortArray[0];
        if (statusNumber !== linePortNumber) {
            cfnnDeviceRegistrationsStatus = 'Registered (Pilot Trunk - ' + linePortNumber + ')';
        } else {
            cfnnDeviceRegistrationsStatus = 'Unregistered';
        }
    } else if (s.COMMPilot && s.COMMPilot.registration && s.COMMPilot.registration.status) {
        cfnnDeviceRegistrationsStatus = (s.COMMPilot.registration.status.charAt(0).toUpperCase() + s.COMMPilot.registration.status.slice(1));
    }  else {
        var sp = '';
        if (s.COMMPilot && s.COMMPilot.user && s.COMMPilot.user.serviceProviderId) sp += s.COMMPilot.user.serviceProviderId; else sp += 'XYZXYZXYZ';
        if (s.CFNN && s.CFNN.registrations && s.CFNN.registrations[0] && s.CFNN.registrations[0]['Trunk FNN'] && s.CFNN.registrations[0]['Trunk FNN'].toUpperCase().includes('NO REGISTERED')) {
            cfnnDeviceRegistrationsStatus = 'Unregistered';
        } else {
            if (s.CFNN && s.CFNN.Cluster && s.CFNN.Cluster.includes(sp)) cfnnDeviceRegistrationsStatus = 'Registered'; else cfnnDeviceRegistrationsStatus = 'Unregistered';
        }
    }

    // CFNN IPWorks
    let cfnnIPWorksDomain = null;
    if (s.IPWorks) {
        if (s.IPWorks.customerdomain && s.IPWorks.customerdomain !== '') {cfnnIPWorksDomain = s.IPWorks.customerdomain;}
        if (s.IPWorks.customertype) {cfnnIPWorksDomain += ' '                + s.IPWorks.customertype.toUpperCase();}
    }


    // CFNN registrations
    let cfnnRegistrations = null;
    if (Array.isArray(s.CFNN?.registrations)) {
        cfnnRegistrations = [];
        for (let registration of s.CFNN.registrations) {
            cfnnRegistrations.push({
                user: registration?.User || null,
                regType: registration?.RegType || null,
                device: registration?.Device || null,
                deviceBak: registration?.['Device Name'] || null,
                linePort: registration?.['Line/Port'] || null,
                expires: registration?.Expires  || null,
                expiresBak: registration?.['Expires:'] || null,
                userAgent: registration?.UserAgent || null,
                SBC: registration?.SBC || null,
                callInfo: registration?.['Call Info'] || null,
                trunkFnn: registration?.['Trunk FNN'] || null
            });
        }
    }

    let fieldValues = {};

    for (let overviewField of overviewFields) {
        let fieldValue = null;

        try {
            // Define all overview field values here
            switch (overviewField.name) {
                case 'customerDetailsCustomerName':
                    fieldValue = r.MDR005?.customerName || null;
                    break;
                case 'customerDetailsLocation':
                    fieldValue = r.MDR005?.address || null;
                    break;
                case 'customerDetailsCIDN':
                    fieldValue = data.CIDN;
                    break;
                case 'customerDetailsCustomerConsent':
                    fieldValue = r.MDR068?.customerConsent || null;
                    break;
                case 'productDetailsProductName':
                    fieldValue = productName;
                    break;
                case 'productDetailsFnn':
                    fieldValue = data.fnn;
                    break;
                case 'productDetailsServiceType':
                    fieldValue = data.serviceType;
                    break;
                case 'productDetailsServiceSpeed':
                    fieldValue = magpieProductData?.['Service Speed'] || null;
                    break;
                case 'productDetailsCarriageFnn':
                    fieldValue = data.carriageFNN;
                    break;
                case 'productDetailsCarriageType':
                    fieldValue = data.carriageType;
                    break;
                case 'productDetailsAccessType':
                    fieldValue = data.nbnAccessType;
                    break;
                case 'productDetailsAvc':
                    fieldValue = data.nbnId;
                    break;
                case 'productDetailsIpwanNetwork':
                    fieldValue = s.MAGPIE?.rawData?.IPWAN?.['Member of IPWAN Network'] || null;
                    break;
                case 'productDetailsIpwanPort':
                    fieldValue = s.MAGPIE?.rawData?.IPWAN?.IPWAN_FNN || null;
                    break;
                case 'productDetailsCeIpAddress':
                    fieldValue = magpieProductData?.['CE IP address'] || null;
                    break;
                case 'productDetailsRoutingProtocol':
                    fieldValue = magpieProductData?.['Routing Protocol'] || null;
                    break;
                case 'productDetailsAssociatedAccessService':
                    fieldValue = nbnEnterpriseEthernetData['Associated Access Service'];
                    break;
                case 'productDetailsAssociatedAccessServiceFNN':
                    fieldValue = nbnEnterpriseEthernetData['Associated Access Service FNN'];
                    break;
                case 'productDetailsMediaType':
                    fieldValue = nbnEnterpriseEthernetData['Media type'];
                    break;
                case 'productDetailsBpiId':
                    fieldValue = Array.isArray(s.NBNProductInventory) && s.NBNProductInventory[0]?.id ? s.NBNProductInventory[0].id : null;
                    break;
                case 'productDetailsAvcNBNEE':
                    fieldValue = nbnEnterpriseEthernetData['AVC'];
                    break;
                case 'productDetailsInterfaceType':
                    fieldValue = uniEProduct?.interfaceType || null;
                    break;
                case 'productDetailsOvcType':
                    fieldValue = uniEProduct?.ovcType || null;
                    break;
                case 'productDetailsUniPort':
                    fieldValue = uniEProduct?.uniPort || null;
                    break;
                case 'productDetailsCategoryOfService':
                    fieldValue = r.MDR009?.manageCategory || null;
                    break;
                case 'productDetailsMDNNetworkFnn':
                    fieldValue = s.MAGPIE?.rawData?.MDN?.['Member of MDN Network'] || null;
                    break;
                case 'productDetailsMDNService':
                    fieldValue = s.MAGPIE?.rawData?.MDN?.['MDN FNN'] || null;
                    break;
                case 'productDetailsPhoneNumberRanges':
                    fieldValue = Array.isArray(r.MDR203?.numberRanges) ? r.MDR203.numberRanges : null;
                    break;
                case 'deviceDetailsDeviceName':
                    fieldValue = data.deviceName;
                    break;
                case 'deviceDetailsDeviceType':
                    fieldValue = r.MDR118?.deviceTypeName || null;
                    break;
                case 'deviceDetailsSupplierCode':
                    fieldValue = r.MDR118?.supplierCodeName || null;
                    break;
                case 'deviceDetailsProductCode':
                    fieldValue = r.MDR118?.productCode || null;
                    break;
                case 'deviceDetailsDeviceSerialNumber':
                    fieldValue = s.MAGPIE?.rawData?.MDN?.['Serial Number (SNMP)'] || null;
                    break;
                case 'deviceDetailsBasementSwitch':
                    fieldValue = basementSwitchDevice;
                    break;
                case 'deviceDetailsNTU':
                    fieldValue = ntuDevice;
                    break;
                case 'deviceDetailsNTUType':
                    fieldValue = s.MAGPIE?.rawData?.IPWAN?.['NTU type'] || null;
                    break;
                case 'deviceDetailsDeviceStatusVoip':
                    fieldValue = cfnnDeviceStatus || null;
                    break;
                case 'deviceDetailsRegisterationStatusVoipField':
                    fieldValue = cfnnDeviceRegistrationsStatus || null;
                    break;
                case 'deviceDetailsClusterVoipField':
                    fieldValue = s.CFNN?.Cluster || null;
                    break;
                case 'deviceDetailsDeviceIPWorksDomainVoip':
                    fieldValue = cfnnIPWorksDomain || null;
                    break;
                case 'deviceDetailsDeviceUserFNNVoip':
                    fieldValue = s.CFNN?.['User FNN'] || null;
                    break;
                case 'deviceDetailsDeviceGroupFNNVoip':
                    fieldValue = s.CFNN?.['Group FNN'] || null;
                    break;
                case 'deviceDetailsDeviceTrunkFNNVoip':
                    fieldValue = s.CFNN?.['Trunk FNN'] || null;
                    break;
                case 'deviceDetailsDeviceSiteFNNVoip':
                    fieldValue = s.CFNN?.['Site FNN'] || null;
                    break;
                case 'deviceDetailsDeviceNameVoip':
                    fieldValue = s.CFNN?.Device || null;
                    break;
                case 'deviceDetailsDeviceVersionVoip':
                    fieldValue = s.CFNN?.Version || null;
                    break;
                case 'deviceDetailsDeviceNumberVoip':
                    fieldValue = s.CFNN?.number || null;
                    break;
                case 'deviceDetailsDeviceDetailsVoip':
                    fieldValue = s.CFNN?.Details || null;
                    break;
                case 'deviceDetailsDeviceRegistrationsVoip':
                    fieldValue = cfnnRegistrations;
                    break;
                case 'accountStatusRassStatus':
                    fieldValue = (s.RASSP?.NotFound !== true && s.RASSP?.FNN) ||
                                 (s['RASSP-C']?.NotFound !== true && s['RASSP-C']?.FNN) ||
                                 (s['RASSP-B']?.NotFound !== true && s['RASSP-B']?.FNN) ? 'FNN found': 'FNN not found';
                    break;
                case 'accountStatusRassCancelledOrders':
                    fieldValue = Array.isArray(r.MTR035?.cancelledOrders) ? r.MTR035.cancelledOrders : [];
                    break;
                case 'accountStatusRassMiscellaneousOrders':
                    fieldValue = Array.isArray(r.MTR028?.miscellaneousOrders) ? r.MTR028.miscellaneousOrders : [];
                    break;
                case 'outagesPlanned':
                    fieldValue = plannedOutages;
                    break;
                case 'outagesUnplanned':
                    fieldValue = unplannedOutages;
                    break;
                case 'outagesPower':
                    fieldValue = powerOutages;
                    break;
                case 'NBNHealthCheckOutages':
                    fieldValue = nbnOutages;
                    break;
                case 'activeIncidentsSIIAMActiveCases':
                    fieldValue = siiamActiveCases;
                    break;
                case 'activeIncidentsSIIAMHistoryCases':
                    fieldValue = siiamHistoryCases;
                    break;
                case 'activeIncidentsServiceCentralIncidents':
                    fieldValue = serviceCentralIncidents;
                    break;
                case 'activeIncidentsPromiseTasks':
                    fieldValue = promiseTasks;
                    break;
            }

            fieldValues[overviewField.name] = fieldValue;
        } catch(error) {
            logger.debug(`Overview fields, failed to extract field ${overviewField.name}, ${error.toString()}`);
        }
    }

    return fieldValues;
}

