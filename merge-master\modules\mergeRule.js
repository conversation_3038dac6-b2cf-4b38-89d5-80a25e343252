// collect.js
// V4
// =========
// All functions for parsing command and script.
import ivm from 'isolated-vm';
import _ from 'lodash';
import debug from 'debug';

import Api from '../db/model/api.js';
import Rule from '../db/model/rule.js';
import RuleMetadata from '../db/model/ruleMetadata.js';
import Source from '../db/model/source.js';
import ServiceCheckModel from '../db/model/serviceCheck.js';
import GeneratedServiceCheckModel from '../db/model/generatedServiceCheck.js';
import { RuleCodeError } from '../modules/error.js';

//Result type and acceptable value
import { InputTypes, ProductTypes, RuleResult, RuleStatus } from '../modules/enumerations.js';
import mergeCollect from './mergeCollect.js';

const debugMsg = debug('merge:rule');

// Stores names of fields in service check record that can be written to from rule code
// May be defined somewhere else
const SERVICE_CHECK_MUTABLE_FIELDS = Object.freeze([
    'fnn',
    'billingFNN',
    'carriageFNN',
    'MDNFNN',
    'carriageType',
    'nbnServiceType',
    'nbnAccessType',
    'nbnSubAccessType',
    'nbnId',
    'deviceName',
    'CIDN',
    'address',
    'OffshoreResources',
    'serviceType',
    'siiamCases',
    'serviceCentralIncidents',
    'outcomeNames',
    'productTypes'
]);


async function getRulesAndSources() {
    let ruleSourceData = {};
    let promises = [];

    promises.push(Rule.find({}).select().sort('name').collation({ locale: 'en' }));
    promises.push(Source.find({}).select().sort('name').collation({ locale: 'en' }));
    promises.push(RuleMetadata.findOne({}).select(['-__v', '-_id']));

    let [rules, sources, ruleMetadata] = await Promise.all(promises);

    // Save metadata entry in database if it does not exist.
    if (ruleMetadata === null) {
        ruleMetadata = new RuleMetadata({
            version: 1,
            updatedBy: 'unknown',
            updatedOn: new Date()
        });

        await ruleMetadata.save();
    }

    Object.assign(ruleSourceData, ruleMetadata.toJSON());
    ruleSourceData.rules = [];
    ruleSourceData.sources = [];

    rules.forEach(rule => {
        let ruleJson = rule.toJSON();
        delete ruleJson.createdBy;
        delete ruleJson.createdOn;

        ruleSourceData.rules.push(ruleJson);
    });

    sources.forEach(source => {
        let sourceJson = source.toJSON();
        delete sourceJson.createdBy;
        delete sourceJson.createdOn;

        ruleSourceData.sources.push(sourceJson);
    });

    return ruleSourceData;
}


async function getRules() {
    return Rule.find({}).sort('name');
}


async function getSources() {
    return Source.find({}).sort('name');
}


function incrementRuleVersion(username) {
    return RuleMetadata.findOneAndUpdate(
        {},
        { $inc: {"version": 1}, $set: {"updatedBy": username, "updatedOn": new Date()} },
        { upsert: true, strict: "throw", runValidators: true, setDefaultsOnInsert: true, useFindAndModify: false }
    )
}


function getRuleMetadata(callback) {
    return RuleMetadata.findOne({}).select(['-__v', '-_id']).exec(function(err, metadata) {
        callback(err, metadata);
    });
}


function setRuleMetadata(username, version) {
    return RuleMetadata.findOneAndUpdate(
        {},
        { $set: {"version": version, "updatedBy": username, "updatedOn": new Date()} },
        { upsert: true, strict: "throw", runValidators: true, setDefaultsOnInsert: true, useFindAndModify: false }
    );
}


/**
 *
 * @param {*} rule Rule to run
 * @param {*} SCr Service Check Record object which contains the data required by rules to run
 * @returns Array with 4 elements with the rule's output, its status, result, value message and extra info
 */
async function runRule(rule, SCr, relatedServiceCheckRecords=null) {
    let ruleResult;
    let valueMsg;
    let extraInfo;

    if (!(rule instanceof Rule)) {
        throw TypeError("Input rule must be an instance of the Rule model");
    }

    // TODO: temporary, maybe find an inheritance method or a cleaner way
    if (!(SCr instanceof ServiceCheckModel || SCr instanceof GeneratedServiceCheckModel)) {
        throw TypeError("Input service check record must be an instance of the ServiceCheckModel model");
    }

    SCr.rulesData[rule.name] = {};

    const isolate = new ivm.Isolate({
        memoryLimit: 64
    });
    const ruleDataContext = await isolate.createContext();

    try {
        let serviceCheckRecordCopy = SCr.toJSON();
        await ruleDataContext.global.set('data', new ivm.ExternalCopy(serviceCheckRecordCopy).copyInto());
        await ruleDataContext.global.set('r', ruleDataContext.global.getSync('data').getSync('rulesData').derefInto());
        await ruleDataContext.global.set('s', ruleDataContext.global.getSync('data').getSync('sourcesData').derefInto());
        await ruleDataContext.global.set('InputTypes', new ivm.ExternalCopy(InputTypes).copyInto());
        await ruleDataContext.global.set('ProductTypes', new ivm.ExternalCopy(ProductTypes).copyInto());

        // Adds related service checks into the context
        if (rule.depedenciesFromRelatedServiceChecks) {
            // Double check this doesn't kill anything
            let relatedServiceCheckRecordsObjects = null;
            if (Array.isArray(relatedServiceCheckRecords)) {
                relatedServiceCheckRecordsObjects = [];

                for (let record of relatedServiceCheckRecords) {
                    relatedServiceCheckRecordsObjects.push(record.toJSON());
                }
            }

            await ruleDataContext.global.set('relatedServiceChecks', new ivm.ExternalCopy(relatedServiceCheckRecordsObjects).copyInto());
        }

        if (rule.preCondition) {
            try {
                let preCondResult = await ruleDataContext.eval(rule.preCondition, { copy: true, timeout: 2000 });

                // Copies the data from SCr.rulesData[rule.name] in the isolate context back into the service check
                // Note: Rules data and some service check fields can be written by the pre-condition
                // this is currently intentional for backwards compatibility as some rules exist at the moment that
                // mutate rules data
                let rulesDataReference = await getReferenceByKeys(ruleDataContext.global, ['data', 'rulesData', rule.name]);

                if (rulesDataReference instanceof ivm.Reference && rulesDataReference.typeof === 'object') {
                    let rulesDataFromContext = await rulesDataReference.copy();

                    if (_.isPlainObject(rulesDataFromContext)) {
                        Object.assign(SCr.rulesData[rule.name], rulesDataFromContext);
                    }
                }

                let serviceCheckDataReference = await getReferenceByKeys(ruleDataContext.global, ['data']);

                if (serviceCheckDataReference instanceof ivm.Reference && serviceCheckDataReference.typeof === 'object') {
                    for (let fieldName of SERVICE_CHECK_MUTABLE_FIELDS) {
                        let contextValue = await serviceCheckDataReference.get(fieldName, { copy: true });

                        // Assigns value from rule data context to service check record if it has changed from the original value
                        if (!_.isEqual(serviceCheckRecordCopy[fieldName], contextValue)) {
                            SCr[fieldName] = contextValue;
                        }
                    }
                }

                debugMsg(`#> precondition rule ${rule.name} is '${rule.preCondition}' result is : ` + (preCondResult ? 'true' : 'false' ));
                if (!preCondResult) {
                    // Reject if preCondition is a false equivalent value
                    debugMsg(`#> preCondition is NOT OK for ${rule.name}`);

                    SCr.rulesData[rule.name].status = RuleStatus.done;
                    ruleResult = RuleResult.reject;

                    return [ruleResult, null, null, null];
                }
            } catch (error) {
                debugMsg(`!> rule ${rule.name} precondition has error: `, error);
                SCr.rulesData[rule.name].status = RuleStatus.codeError;
                let ruleCodeError = new RuleCodeError(`Error when determining preCondition for rule ${rule.name}`, { cause: error });
                throw ruleCodeError;
            }
        }

        let ruleCodeReturnValue;
        try {
            // Note: copy is true in the options as some rules may currently
            // evaluate as a non-transferable variable such as an Object
            ruleCodeReturnValue = await ruleDataContext.eval(rule.ruleCode, { copy: true, timeout: 2000 });

            // Copies the data from SCr.rulesData[rule.name] in the isolate context back into the service check
            let rulesDataReference = await getReferenceByKeys(ruleDataContext.global, ['data', 'rulesData', rule.name]);

            if (rulesDataReference instanceof ivm.Reference && rulesDataReference.typeof === 'object') {
                let rulesDataFromContext = await rulesDataReference.copy();

                if (_.isPlainObject(rulesDataFromContext)) {
                    Object.assign(SCr.rulesData[rule.name], rulesDataFromContext);
                }
            }

            // Copies any sources data with the InRequest- prefix into the service check sources data object
            // This is to maintain compatability as sources data with InRequest- can be set when starting a service check
            // with the API, which are read by some rules
            let sourcesDataReference = await getReferenceByKeys(ruleDataContext.global, ['data', 'sourcesData']);

            if (sourcesDataReference instanceof ivm.Reference && sourcesDataReference.typeof === 'object') {
                let sourcesDataFromContext = await sourcesDataReference.copy();

                if (_.isPlainObject(sourcesDataFromContext)) {
                    for (let sourceName in sourcesDataFromContext) {
                        if (typeof sourceName === 'string' && sourceName.startsWith('InRequest-')) {
                            SCr.sourcesData[sourceName] = sourcesDataFromContext[sourceName];
                        }
                    }
                }
            }

            let serviceCheckDataReference = await getReferenceByKeys(ruleDataContext.global, ['data']);

            // Note: This is not an ideal implementation
            // For primitive types like strings it checks if it's not undefined / null in the global context
            // It will also set any objects (including arrays) from the context directly overwriting other rules
            if (serviceCheckDataReference instanceof ivm.Reference && serviceCheckDataReference.typeof === 'object') {
                for (let fieldName of SERVICE_CHECK_MUTABLE_FIELDS) {
                    let contextValue = await serviceCheckDataReference.get(fieldName, { copy: true });

                    // Assigns value from rule data context to service check record if it has changed from the original value
                    if (!_.isEqual(serviceCheckRecordCopy[fieldName], contextValue)) {
                        SCr[fieldName] = contextValue;
                    }
                }
            }

            debugMsg(`#> ruleCode Result for ${rule.name} : ${ruleCodeReturnValue}`);

            // Sets rule result if it is not an action rule
            if (rule.ruleType !== 'Action') {
                if (ruleCodeReturnValue) {
                    ruleResult = RuleResult.ok;
                } else if (!ruleCodeReturnValue && rule.isWarning) {
                    ruleResult = RuleResult.warning;
                } else if (!ruleCodeReturnValue) {
                    ruleResult = RuleResult.failed;
                }
            }

            valueMsg = rule.valueMsgStm ? await ruleDataContext.eval(rule.valueMsgStm, { timeout: 2000 }) : null;
            extraInfo = rule.extraInfo ? await ruleDataContext.eval(rule.extraInfo, { timeout: 2000 }) : null;

            // Converts value message and extra info to strings if a non-string is returned from code
            if (!_.isNil(valueMsg)) {
                valueMsg = _.toString(valueMsg);
            }

            if (!_.isNil(extraInfo)) {
                extraInfo = _.toString(extraInfo);
            }

            SCr.rulesData[rule.name].status = RuleStatus.done;
        } catch (error) {
            debugMsg(`!> rule ${rule.name} has error:`, error);
            SCr.rulesData[rule.name].status = RuleStatus.codeError;
            let ruleCodeError = new RuleCodeError(`Error when running rule / value message / extra info code for rule ${rule.name}`, { cause: error });
            throw ruleCodeError;
        }

        // Handles action results
        if (rule.ruleType === 'Action') {
            // Only run action if auto execution is enabled for the action
            if (rule.action && rule.action.autoExecution) {
                let actionCondition = true;

                if (rule.action.codeCondition) {
                    try {
                        actionCondition = await ruleDataContext.eval(rule.action.codeCondition, { timeout: 2000 });
                    } catch(error) {
                        SCr.rulesData[rule.name].status = RuleStatus.codeError;
                        let ruleCodeError = new RuleCodeError(`Error when determining action condition for rule ${rule.name}`, { cause: error });
                        throw ruleCodeError;
                    }
                }

                // If the rule code condition matches the set action condition, the action
                // will now run automatically
                if (actionCondition) {
                    try {
                        SCr.rulesData[rule.name].status = RuleStatus.running;
                        let [actionResult, valueMsgAction, extraInfoAction] = await runRuleAction(rule, SCr);

                        valueMsg = valueMsgAction;
                        extraInfo = extraInfoAction;

                        if (actionResult.error) {
                            ruleResult = RuleResult.actionable;
                        }

                        if (actionResult.error) {
                            ruleResult = RuleResult.actionable;
                        } else {
                            ruleResult = RuleResult.actioned;
                            SCr.rulesData[rule.name].msg = valueMsgAction ? valueMsgAction : rule.trueMsg;
                        }

                        SCr.rulesData[rule.name].status = RuleStatus.done;
                    } catch(error) {
                        // Sets status to codeError if runRuleAction throws this type of exception
                        if (error instanceof RuleCodeError) {
                            SCr.rulesData[rule.name].status = RuleStatus.codeError;
                        } else {
                            SCr.rulesData[rule.name].status = RuleStatus.done;
                        }

                        throw error;
                    }
                } else {
                    ruleResult = RuleResult.warning;
                }
            } else {
                ruleResult = RuleResult.actionable;
            }
        }

        return [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo];
    } finally {
        ruleDataContext.release();
        if (!isolate.isDisposed) {
            isolate.dispose();
        }
    }
}


// Run unit tests usually does not behave like an asynchronous function as rule code
// is purely synchronous, however runRule() is an async function as it can run actions which use async functions
async function runUnitTests(rule) {
    let UTResult = [];

    if (typeof rule.unitTests === 'undefined' || rule.unitTests == null) {
        debugMsg('#> no UnitTest for "' + rule.name + '"');
        return UTResult;
    }

    for (let unitTest of rule.unitTests) {
        debugMsg('#> run UnitTest "' + unitTest.name + '"');
        let UTSCr;
        let UTOutputRecord;

        try {
            UTSCr = new ServiceCheckModel(JSON.parse(unitTest.inputRecord));
            UTOutputRecord = new ServiceCheckModel(JSON.parse(unitTest.outputRecord));
        } catch (err) {
            UTResult.push({name: unitTest.name, status: "error", error: 'Invalid JSON in input or output record: ' + err.message });
            debugMsg(`#> rule "${rule.name}" UnitTest "${unitTest.name}" has input record error. error=${err}`);
            continue;
        }

        try {
            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await runRule(rule, UTSCr);
            // Add some metadata to Rule
            UTSCr.rulesData[rule.name].type = rule.ruleType;
            UTSCr.rulesData[rule.name].valueMsg = valueMsg;
            UTSCr.rulesData[rule.name].extraInfo = extraInfo;
            UTSCr.rulesData[rule.name].rootCauseCategory = rule.rootCauseCategory;
            UTSCr.rulesData[rule.name].result = ruleResult;

            switch (ruleResult) {
                case RuleResult.reject:
                    UTSCr.rulesData[rule.name].msg = rule.preConditionMsg;
                    UTSCr.rulesData[rule.name].error = null;
                    break;
                case RuleResult.ok:
                case RuleResult.actionable:
                    UTSCr.rulesData[rule.name].msg = rule.trueMsg;
                    break;
                case RuleResult.actioned:
                    UTSCr.rulesData[rule.name].msg = valueMsg ? valueMsg : rule.trueMsg;
                    break;
                case RuleResult.warning:
                case RuleResult.failed:
                default:
                    UTSCr.rulesData[rule.name].msg = rule.falseMsg;
                    break;
            }
        } catch(error) {
            UTResult.push({ name: unitTest.name, status: "error", error: error.message });
        } finally {
            // Fix to omit undefined fields in rulesData for this rule, as result, valueMsg or extraInfo could be undefined or null
            let UTSCrJson = UTSCr.toJSON();
            let UTOutputRecordJson = UTOutputRecord.toJSON();
            UTSCrJson.rulesData[rule.name] = _(UTSCrJson.rulesData[rule.name]).omitBy(_.isUndefined).value();

            // Ignore createdOn field during comparison
            UTSCrJson.createdOn = null;
            UTOutputRecordJson.createdOn = null;
            let recordIsEqual = _.isEqual(UTSCrJson, UTOutputRecordJson);
            let status = recordIsEqual ? 'pass' : 'failed';

            if (recordIsEqual) {
                UTResult.push({ name: unitTest.name, status: status, outputRecord: UTSCrJson });
            } else {
                UTResult.push({ name: unitTest.name, status: status, expectedRecord: UTOutputRecordJson, outputRecord: UTSCrJson });
            }
        }
    }

    return UTResult;
}


async function runRuleAction(rule, SCr) {
    if (!(rule instanceof Rule)) {
        throw TypeError("Input rule must be an instance of the Rule model");
    }

    if (!(SCr instanceof ServiceCheckModel || SCr instanceof GeneratedServiceCheckModel)) {
        throw TypeError("Input service check record must be an instance of the ServiceCheckModel model");
    }

    if (rule.action == null) {
        throw new Error(`Action is not set for rule ${rule.name}`);
    }

    const isolate = new ivm.Isolate({
        memoryLimit: 64
    });
    const parameterContext = await isolate.createContext();

    try {
        await parameterContext.global.set('data', new ivm.ExternalCopy(SCr.toJSON()).copyInto());
        await parameterContext.global.set('r', parameterContext.global.getSync('data').getSync('rulesData').derefInto());
        await parameterContext.global.set('s', parameterContext.global.getSync('data').getSync('sourcesData').derefInto());
        await parameterContext.global.set('u', new ivm.ExternalCopy(rule.userInputs).copyInto()); // u assigned to access userinput in rule code
        
        let api = await Api.findOne({ name: rule.action.api });
        if (api === null) {
            throw new Error(`API ${rule.action.api} does not exist`);
        }

        // Determines parameters here because the parameter names are required from the API
        // to extract from the context
        let parameters = {};

        try {
            await parameterContext.eval(rule.action.parameterCode);
            
            for (let parameterName of api.parameters) {
                let parameterValue = await parameterContext.global.get(parameterName, { copy: true });
                if (parameterValue !== undefined) {
                    parameters[parameterName] = parameterValue;
                }
            }
        } catch(error) {
            SCr.rulesData[rule.name].status = RuleStatus.codeError;
            let ruleCodeError = new RuleCodeError(`Error when determining parameters for action rule ${rule.name}`, { cause: error });
            throw ruleCodeError;
        }

        let result = null;
        let collectApiError = null;

        try {
            result = await mergeCollect.collectApi(api, parameters);
        } catch(error) {
            // If there was a response, the error from collectApi() will contain a "result" field
            if (error.result) {
                result = error.result;
                collectApiError = error.toString();
            } else {
                throw error;
            }
        }

        let actionResult = {
            createdOn: new Date(),
            status: result.status,
            response: result.data
        };
        let valueMsg;
        let extraInfo;

        if (result) {
            if (collectApiError) {
                actionResult.error = collectApiError;
            }

            try {
                // Sets action in rules data, which could be used by the value message / extra info code
                if (_.isPlainObject(_.get(SCr, ['rulesData', rule.name]))) {
                    SCr.rulesData[rule.name].action = actionResult;
                }

                // This copies the service check record again, may be optimised in the future
                await parameterContext.global.set('data', new ivm.ExternalCopy(SCr.toJSON()).copyInto());
                await parameterContext.global.set('r', parameterContext.global.getSync('data').getSync('rulesData').derefInto());
                await parameterContext.global.set('s', parameterContext.global.getSync('data').getSync('sourcesData').derefInto());

                // Re-runs rule code if it exists
                if (rule.ruleCode) {
                    let ruleCodeReturnValue = await parameterContext.eval(rule.ruleCode, { timeout: 2000 });

                    // Copies the data from SCr.rulesData[rule.name] in the isolate context back into the service check
                    let rulesDataReference = await getReferenceByKeys(parameterContext.global, ['data', 'rulesData', rule.name]);

                    if (rulesDataReference instanceof ivm.Reference && rulesDataReference.typeof === 'object') {
                        let rulesDataFromContext = await rulesDataReference.copy();

                        if (_.isPlainObject(rulesDataFromContext)) {
                            Object.assign(SCr.rulesData[rule.name], rulesDataFromContext);
                        }
                    }

                    if (!ruleCodeReturnValue) {
                        actionResult.error = "Rule code: condition did not pass";
                    }
                }

                // Re-evaluates value message and extra info code after
                // response is received from action
                valueMsg = rule.valueMsgStm ? await parameterContext.eval(rule.valueMsgStm, { timeout: 2000 }) : null;
                extraInfo = rule.extraInfo ? await parameterContext.eval(rule.extraInfo, { timeout: 2000 }) : null;

                // Converts value message and extra info to strings if a non-string is returned from code
                if (!_.isNil(valueMsg)) {
                    valueMsg = _.toString(valueMsg);
                }

                if (!_.isNil(extraInfo)) {
                    extraInfo = _.toString(extraInfo);
                }
            } catch(error) {
                SCr.rulesData[rule.name].status = RuleStatus.codeError;
                let ruleCodeError = new RuleCodeError("Error when evaluating value message or extra info after action", { cause: error });
                throw ruleCodeError;
            }
        }

        return [actionResult, valueMsg, extraInfo];
    } finally {
        parameterContext.release();
        if (!isolate.isDisposed) {
            isolate.dispose();
        }
    }
}


async function getReferenceByKeys(baseReference, keys) {
    let reference = baseReference;

    for (let key of keys) {
        if (!(reference instanceof ivm.Reference)) {
            reference = null;
            break;
        }

        reference = await reference.get(key);
    }

    return reference;
}


export default {
    runRule: runRule,
    runRuleAction: runRuleAction,
    runUnitTests: runUnitTests,
    getRulesAndSources: getRulesAndSources,
    getRules: getRules,
    getSources: getSources,
    getRuleMetadata: getRuleMetadata,
    incrementRuleVersion: incrementRuleVersion,
    setRuleMetadata: setRuleMetadata
};
