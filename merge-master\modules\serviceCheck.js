// run.js
//v3
// =========
// All functions for running fnn. First Layer

import axiosModule from 'axios';
import escapeStringRegexp from 'escape-string-regexp';
import _ from 'lodash';
import https from 'https';
import ivm from 'isolated-vm';
import promClient from 'prom-client';
import moment from 'moment';
import debug from 'debug';
import { isValidPhoneNumber, parsePhoneNumber } from 'libphonenumber-js';

import auth from './auth.js';
import mergeCollect from './mergeCollect.js';
import logger from './logger.js';
import mergeRule from './mergeRule.js';
import Api from '../db/model/api.js';
import Rule from '../db/model/rule.js';
import Source from '../db/model/source.js';
import ServiceCheckModel from '../db/model/serviceCheck.js';
import GeneratedServiceCheckModel from '../db/model/generatedServiceCheck.js';
import runningStatus from './runningStatus.js';
import { sendServiceCheckMessage } from './kafkaWrite.js';
import { getTestPackagesForProductTypes } from './testPackages.js';
import authEmitter from './authEmitter.js';
import constants from './helpers/constants.js';
import { identifyInputType } from './inputType.js';
import { DependencyGraph } from './dependencyGraph.js';
import mergeLib from './mergeLib.js';
import { ApiUriError, RuleNotActionableError, ServiceCheckAccessError, RuleCodeError, SourceCodeError } from './error.js';
import { InputTypes, ProductTypes, RuleResult, ServiceCheckStatus, SourceStatus } from './enumerations.js';
import textTemplate from './textTemplate.js';
import { extractOverviewFields, getServiceCheckOverviewFields, getServiceCheckOverviewFieldsForProductTypes } from './serviceCheckOverviewFields.js';
import { acquireLock, releaseLock } from './serviceCheckLock.js';

const axios = axiosModule.create({
    httpsAgent: new https.Agent({
        rejectUnauthorized: false
    })
});
const debugMsg = debug('merge:serviceCheck');

const promHistogramSourceDuration = new promClient.Histogram({
    name: 'source_collect_duration_seconds',
    help: 'Duration of source collection in milliseconds',
    labelNames: ['source_name', 'status'],
    buckets: [
        1,
        5,
        10,
        30,
        60,
        120,
        180,
        240,
        300
    ]
});

const promHistogramRuleDuration = new promClient.Histogram({
    name: 'rule_run_duration_seconds',
    help: 'Duration of rule runtime in milliseconds',
    labelNames: ['rule_name', 'result'],
    buckets: promClient.linearBuckets(0.5, 0.5, 10)
});


const SERVICE_CHECK_RECORD_UPDATE_RULE_NAMES = [
    'MDR005',
    'MDR012',
    'MDR106',
    'MDR068',
    'MDR203'
];

// Note: This will replicate the previous functionality of the 'outageInfo' suite without depending on
// the 'suite' field in the sources
const OUTAGE_INFO_SOURCE_NAMES = [
    'CIDNCustomerConsentExclusion',
    'Flexcab',
    'Magpie - PFC API',
    'MergeServiceCheckHistory',
    'RFOAlarms',
    'ServiceTXAlarms',
];

const SERVICE_CHECK_INITIAL_TIMEOUT_LIMIT = 90000;


export async function start(SCr, user=null, socket=null, runOutageInfo=false) {
    if (!(SCr instanceof ServiceCheckModel)) {
        throw new TypeError("Service check record passed to start() function must be an instance of the ServiceCheckModel model.");
    }

    await acquireLock(SCr);

    try {
        let rules;
        let sources;

        try {
            // Retrieves all rules and sources from the database
            // Includes inactive sources and sources with a higher level / non-matching suites as this is needed
            // for the dependency graph to function
            [rules, sources] = await Promise.all([mergeRule.getRules(), mergeRule.getSources()]);
        } catch(error) {
            logger.error(`Error obtaining rules and sources data when running service check ${SCr.id}, ${error.toString()}`);
            return;
        }

        try {
            SCr.status = ServiceCheckStatus.running;
            await SCr.save();
        } catch(error) {
            logger.error(`Error with initial service check ${SCr.id} save, ${error.toString()}`);
            return;
        }

        // Stores socket in an object so that it is passed into run rule and collect source functions by reference
        // This is because there exists some conditions where the socket needs to be disabled to prevent
        // data from some rules or sources to be sent back to the client
        let socketObj = {
            socket: socket
        };

        try {
            sendServiceCheckMessage(JSON.stringify(SCr), SCr.id + '- START');

            // Creates dependency graph from rules and sources
            let dependencyGraph = new DependencyGraph(rules, sources);

            let ruleNamesToRun = new Set();
            let sourceNamesToRun = new Set();

            let disableDisplayConditionRule = dependencyGraph.vertices.find((vertex) => {
                return vertex instanceof Rule && vertex.name === global.gConfig.disableDisplayConditionRuleName;
            });

            if (SCr.input.ruleNames && SCr.input.ruleNames.length) {
                for (let ruleName of SCr.input.ruleNames) {
                    ruleNamesToRun.add(ruleName);

                    let rule = dependencyGraph.vertices.find((vertex) => {
                        return vertex instanceof Rule && vertex.name === ruleName;
                    });

                    // Adds rule for disable display condition if it depends on at least 1 of the rules the service check is limited to
                    if (rule && disableDisplayConditionRule && dependencyGraph.findIfVertexIsDependency(rule, disableDisplayConditionRule)) {
                        ruleNamesToRun.add(global.gConfig.disableDisplayConditionRuleName);
                    }
                }
            }

            if (SCr.input.sourceNames && SCr.input.sourceNames.length) {
                for (let sourceName of SCr.input.sourceNames) {
                    sourceNamesToRun.add(sourceName);

                    let source = dependencyGraph.vertices.find((vertex) => {
                        return vertex instanceof Source && vertex.name === sourceName;
                    });

                    // Adds rule for disable display condition if it depends on at least 1 of the sources the service check is limited to
                    if (source && disableDisplayConditionRule && dependencyGraph.findIfVertexIsDependency(source, disableDisplayConditionRule)) {
                        ruleNamesToRun.add(global.gConfig.disableDisplayConditionRuleName);
                    }
                }
            }

            // If there is at least 1 rule or source in the filter sets,
            // reduce the dependency graph to includes the filtered rules and sources
            if (ruleNamesToRun.size || sourceNamesToRun.size) {
                dependencyGraph.filterVertices(ruleNamesToRun, sourceNamesToRun);
            }

            let sortedVertices = dependencyGraph.topologicalSort();

            SCr.inputType = identifyInputType(SCr.input.searchFNN);

            let serviceCheck = new ServiceCheck(SCr, user, socketObj, sortedVertices, runOutageInfo, false, false);
            await serviceCheck.runServiceCheck();
        } catch(error) {
            logger.error(`Error when starting service check ${SCr.id}, ${error.toString()}`);
        } finally {
            // Sends socket message that service check is completed
            if (socketObj.socket) {
                let socket = socketObj.socket;

                socket.emit('SCStatus', SCr.id, SCr.status);
                if (SCr.errorMessage) {
                    socket.emit('serviceCheckError', SCr.id, SCr.errorMessage);
                }
            }

            // Currently don't know if this can throw an exception, will wrap it in try catch for now
            try {
                sendServiceCheckMessage(JSON.stringify(SCr, null, 5), SCr.id + '- STOP');
            } catch(error) {
                logger.warn(`Error in post handling for service check ${SCr.id}, ${error.toString()}`);
            }

            if (SCr.resultCallbackURL) {
                debugMsg(`#> Callback URL ${SCr.resultCallbackURL}`);

                let disableDisplayMessage = disableDisplayCondition(SCr, user);
                if (disableDisplayMessage) {
                    logger.info(`Service check ${SCr.id} resultCallbackURL was skipped as disable display condition is true`);
                } else {
                    try {
                        let response = await axios.post(SCr.resultCallbackURL, { 'result': SCr });
                        debugMsg(`#> Successfully posted service check results to ${SCr.resultCallbackURL} and returned ${response.data}`);
                    } catch(error) {
                        debugMsg(`!> Couldn't post service check results to ${SCr.resultCallbackURL} and received error ${error}`);
                    }
                }
            }
        }
    } finally {
        await releaseLock(SCr);
    }
}


export async function runServiceCheckInitial(SCr, user=null, socket=null) {
    if (!(SCr instanceof ServiceCheckModel)) {
        throw new TypeError("Service check record passed to start() function must be an instance of the ServiceCheckModel model.");
    }

    await acquireLock(SCr);

    try {
        let rules;
        let sources;

        try {
            // Retrieves all rules and sources from the database
            // Includes inactive sources and sources with a higher level / non-matching suites as this is needed
            // for the dependency graph to function
            [rules, sources] = await Promise.all([mergeRule.getRules(), mergeRule.getSources()]);
        } catch(error) {
            logger.error(`Error obtaining rules and sources data when running service check ${SCr.id}, ${error.toString()}`);
            return;
        }

        let isPartialServiceCheck = ![ServiceCheckStatus.done, ServiceCheckStatus.completedWithError].includes(SCr.status);

        try {
            SCr.status = ServiceCheckStatus.running;
            await SCr.save();
        } catch(error) {
            logger.error(`Error with initial service check ${SCr.id} save, ${error.toString()}`);
            return;
        }

        // Stores socket in an object so that it is passed into run rule and collect source functions by reference
        // This is because there exists some conditions where the socket needs to be disabled to prevent
        // data from some rules or sources to be sent back to the client
        let socketObj = {
            socket: socket
        };

        try {
            sendServiceCheckMessage(JSON.stringify(SCr), SCr.id + '- START');

            // Creates dependency graph from rules and sources
            let dependencyGraph = new DependencyGraph(rules, sources);

            let ruleNamesToRun = new Set();
            let sourceNamesToRun = new Set();

            for (let rule of rules) {
                if (rule.includedInitialServiceCheck) {
                    ruleNamesToRun.add(rule.name);
                }
            }

            for (let source of sources) {
                if (source.includedInitialServiceCheck) {
                    sourceNamesToRun.add(source.name);
                }
            }

            // If there is at least 1 rule or source in the filter sets,
            // reduce the dependency graph to includes the filtered rules and sources
            if (ruleNamesToRun.size || sourceNamesToRun.size) {
                dependencyGraph.filterVertices(ruleNamesToRun, sourceNamesToRun);
            }

            let sortedVertices = dependencyGraph.topologicalSort();

            SCr.inputType = identifyInputType(SCr.input.searchFNN);

            let serviceCheck = new ServiceCheck(SCr, user, socketObj, sortedVertices, false, isPartialServiceCheck, false);

            let serviceCheckPromise = serviceCheck.runServiceCheck();

            // Extracts the promises for the rules / sources that are part of the initial service check
            let initialServiceCheckPromises = [];

            for (let i = 0; i < sortedVertices.length; i++) {
                let vertex = sortedVertices[i];
                if (vertex.includedInitialServiceCheck) {
                    initialServiceCheckPromises.push(serviceCheck.ruleSourcePromises[i]);
                }
            }

            let abortTimeout = setTimeout(() => {
                serviceCheck.abort(false);
            }, SERVICE_CHECK_INITIAL_TIMEOUT_LIMIT);

            await Promise.all(initialServiceCheckPromises);

            let initialServiceCheckAborted = serviceCheck.abortController.signal.aborted;

            clearTimeout(abortTimeout);
            serviceCheck.abort(true);

            // Waits for remaining rules / sources in service check to be resolved
            await serviceCheckPromise;

            if (initialServiceCheckAborted) {
                SCr.status = ServiceCheckStatus.abortedInitial;

                try {
                    await SCr.save({ checkKeys: false });
                } catch(error) {
                    logger.error(`Service check initial search error updating status for ${SCr.id} to aborted initial`);
                }
            }
        } catch(error) {
            logger.error(`Error when starting service check ${SCr.id}, ${error.toString()}`);
        } finally {
            // Sends socket message that service check is completed
            if (socketObj.socket) {
                let socket = socketObj.socket;

                // TODO: Error handling for new service check UI
            }

            // Currently don't know if this can throw an exception, will wrap it in try catch for now
            try {
                sendServiceCheckMessage(JSON.stringify(SCr, null, 5), SCr.id + '- STOP');
            } catch(error) {
                logger.warn(`Error in post handling for service check ${SCr.id}, ${error.toString()}`);
            }
        }
    } finally {
        await releaseLock(SCr);
    }

}



export async function runServiceCheckTestPackages(SCr, testPackagesInput, user=null, socket=null) {
    if (!(SCr instanceof ServiceCheckModel)) {
        throw new TypeError("Service check record passed to start() function must be an instance of the ServiceCheckModel model.");
    }

    await acquireLock(SCr);

    try {
        let rules;
        let sources;

        let isPartialServiceCheck = ![ServiceCheckStatus.done, ServiceCheckStatus.completedWithError].includes(SCr.status);

        try {
            // Retrieves all rules and sources from the database
            // Includes inactive sources and sources with a higher level / non-matching suites as this is needed
            // for the dependency graph to function
            [rules, sources] = await Promise.all([mergeRule.getRules(), mergeRule.getSources()]);
        } catch(error) {
            logger.error(`Error obtaining rules and sources data when running service check ${SCr.id}, ${error.toString()}`);
            return;
        }

        try {
            SCr.status = ServiceCheckStatus.running;
            await SCr.save();
        } catch(error) {
            logger.error(`Error with initial service check ${SCr.id} save, ${error.toString()}`);
            return;
        }

        let testPackagesToRun = [];
        let testPackageRuleNames = [];
        let testPackageParameters = {};

        let serviceCheckTestPackages = getTestPackagesForProductTypes(SCr.productTypes);

        for (let testPackageItem of testPackagesInput) {
            let testPackage = serviceCheckTestPackages.find((currTestPackage) => testPackageItem.name === currTestPackage.name);

            if (testPackage) {
                testPackagesToRun.push(testPackage);

                for (let section of testPackage.sections) {
                    testPackageRuleNames.push(section.ruleName);
                }

                if (testPackage.hasDateRangeFilter) {
                    testPackageParameters[testPackage.name] = testPackageItem.parameters;
                }
            }
        }

        // Stores socket in an object so that it is passed into run rule and collect source functions by reference
        // This is because there exists some conditions where the socket needs to be disabled to prevent
        // data from some rules or sources to be sent back to the client
        let socketObj = {
            socket: socket
        };

        let initialVertices = [];
        // Gets sorted vertices of initial service check rules and
        {
            let dependencyGraph = new DependencyGraph(rules, sources);

            let ruleNamesToRun = new Set();
            let sourceNamesToRun = new Set();

            for (let rule of rules) {
                if (rule.includedInitialServiceCheck) {
                    ruleNamesToRun.add(rule.name);
                }
            }

            for (let source of sources) {
                if (source.includedInitialServiceCheck) {
                    sourceNamesToRun.add(source.name);
                }
            }

            // If there is at least 1 rule or source in the filter sets,
            // reduce the dependency graph to includes the filtered rules and sources
            if (ruleNamesToRun.size || sourceNamesToRun.size) {
                dependencyGraph.filterVertices(ruleNamesToRun, sourceNamesToRun);
            }

            initialVertices = dependencyGraph.vertices;
        }

        try {
            // Creates dependency graph from rules and sources
            let dependencyGraph = new DependencyGraph(rules, sources);

            let ruleNamesToRun = new Set();
            let sourceNamesToRun = new Set();

            if (testPackageRuleNames.length) {
                for (let ruleName of testPackageRuleNames) {
                    ruleNamesToRun.add(ruleName);
                }
            }

            // If there is at least 1 rule or source in the filter sets,
            // reduce the dependency graph to includes the filtered rules and sources
            if (ruleNamesToRun.size || sourceNamesToRun.size) {
                dependencyGraph.filterVertices(ruleNamesToRun, sourceNamesToRun);
            }

            let sortedVertices = dependencyGraph.topologicalSort();

            let serviceCheck = new ServiceCheck(SCr, user, socketObj, sortedVertices, false, isPartialServiceCheck, true);
            serviceCheck.setTestPackages(testPackagesToRun, testPackageParameters);
            serviceCheck.setInitialVertices(initialVertices);

            await serviceCheck.runServiceCheck();
        } catch(error) {
            logger.error(`Error when running test packages for service check ${SCr.id}, ${error.toString()}`);
        } finally {
            // Sends socket message that service check is completed
            if (socketObj.socket) {
                let socket = socketObj.socket;

                // TODO: Error handling for new service check UI
            }
        }
    } finally {
        await releaseLock(SCr);
    }

}


export async function runServiceCheckFull(SCr, user=null, socket=null) {
    if (!(SCr instanceof ServiceCheckModel)) {
        throw new TypeError("Service check record passed to start() function must be an instance of the ServiceCheckModel model.");
    }

    await acquireLock(SCr);

    try {
        let rules;
        let sources;

        try {
            // Retrieves all rules and sources from the database
            // Includes inactive sources and sources with a higher level / non-matching suites as this is needed
            // for the dependency graph to function
            [rules, sources] = await Promise.all([mergeRule.getRules(), mergeRule.getSources()]);
        } catch(error) {
            logger.error(`Error obtaining rules and sources data when running service check ${SCr.id}, ${error.toString()}`);
            return;
        }

        try {
            SCr.status = ServiceCheckStatus.running;
            await SCr.save();
        } catch(error) {
            logger.error(`Error with initial service check ${SCr.id} save, ${error.toString()}`);
            return;
        }

        // Stores socket in an object so that it is passed into run rule and collect source functions by reference
        // This is because there exists some conditions where the socket needs to be disabled to prevent
        // data from some rules or sources to be sent back to the client
        let socketObj = {
            socket: socket
        };

        try {
            // Creates dependency graph from rules and sources
            let dependencyGraph = new DependencyGraph(rules, sources);

            let ruleNamesToRun = new Set();
            let sourceNamesToRun = new Set();

            // If there is at least 1 rule or source in the filter sets,
            // reduce the dependency graph to includes the filtered rules and sources
            if (ruleNamesToRun.size || sourceNamesToRun.size) {
                dependencyGraph.filterVertices(ruleNamesToRun, sourceNamesToRun);
            }

            let sortedVertices = dependencyGraph.topologicalSort();

            let serviceCheck = new ServiceCheck(SCr, user, socketObj, sortedVertices, false, false, false);

            await serviceCheck.runServiceCheck();

            if (socketObj.socket) {
                let socket = socketObj.socket;

                socket.emit('serviceCheck:runFullCompleted', {
                    id: SCr.id
                });
            }
        } catch(error) {
            logger.error(`Error when running test packages for service check ${SCr.id}, ${error.toString()}`);
        } finally {
            // Sends socket message that service check is completed
            if (socketObj.socket) {
                let socket = socketObj.socket;

                // TODO: Error handling for new service check UI
            }


        }
    } finally {
        await releaseLock(SCr);
    }
}


export async function runServiceCheckAction(SCr, rule) {
    if (!(SCr instanceof ServiceCheckModel)) {
        throw new TypeError("Service check record passed to runServiceCheckAction() function must be an instance of the ServiceCheckModel model.");
    }

    if (!(rule instanceof Rule)) {
        throw new TypeError("Rule passed to runServiceCheckAction() function must be an instance of the Rule model.");
    }

    await acquireLock(SCr);
    const originalServiceCheckStatus = SCr.status;

    try {
        SCr.status = ServiceCheckStatus.running;
        await SCr.save();

        let actionResult = null;
        let valueMsg = null;
        let extraInfo = null;

        if (SCr.rulesData?.[rule.name]?.result === RuleResult.actionable) {
            try {
                // This runs the action rules and assign result to rulesData aka the r. variable
                [actionResult, valueMsg, extraInfo] = await mergeRule.runRuleAction(rule, SCr);

                SCr.rulesData[rule.name].action = actionResult;
                SCr.rulesData[rule.name].valueMsg = valueMsg;
                SCr.rulesData[rule.name].extraInfo = extraInfo;
                SCr.rulesData[rule.name].updatedOn = new Date().toISOString();

                if (!actionResult.error) {
                    SCr.rulesData[rule.name].result = RuleResult.actioned;
                    SCr.rulesData[rule.name].msg = rule.trueMsg;
                }
            } catch(error) {
                if (error instanceof RuleCodeError) {
                    // Handles exceptions thrown by rule code
                    SCr.rulesData[rule.name].error = error.toString();

                    SCr.rulesData[rule.name].result = RuleResult.error;
                    SCr.rulesData[rule.name].valueMsg = null;
                    SCr.rulesData[rule.name].extraInfo = null;
                    SCr.rulesData[rule.name].updatedOn = new Date().toISOString();
                    SCr.rulesData[rule.name].msg = rule.errorMsg;
                }

                throw error;
            } finally {
                SCr.markModified('rulesData');
                await SCr.save();
            }
        } else {
            throw new RuleNotActionableError(`Rule ${rule.name} is not actionable for this service check.`);
        }

        return actionResult;
    } finally {
        SCr.status = originalServiceCheckStatus;
        await SCr.save();

        await releaseLock(SCr);
    }
}


class ServiceCheck {
    constructor(serviceCheckRecord, user, socketObj, sortedVertices, runOutageInfo, isPartialServiceCheck, overwriteRuleSourceData, originServiceCheck=null) {
        this.serviceCheckRecord = serviceCheckRecord;
        this.user = user;
        this.socketObj = socketObj;
        this.sortedVertices = sortedVertices;
        this.runOutageInfo = runOutageInfo;
        // Stores promises for rules / sources
        this.ruleSourcePromises = [];

        // Stores promises for rules / sources from related service checks that rules depend on
        this.rulesSourcePromisesFromDependencies = [];

        // These 2 fields (originServiceCheck and generatedService Checks)
        // may not be following an already established design pattern, refactor if there is a
        // better / more standard way

        // Stores originating ServiceCheck in a field
        this.originServiceCheck = originServiceCheck;

        // Stores all other generated ServiceCheck instances
        // For now this is empty for all generated service checks (as generated service checks do not start other service checks)
        this.generatedServiceChecks = [];

        // Stores promises returned from runServiceCheck() by generated service checks
        this.generatedServiceCheckPromises = [];

        this.checkedFNNs = new Set();

        this.disableDisplayConditionPromise = null;

        // Stores the source names that have been sent via the socket for this
        // service check to avoid multiple sends of the same InRequest- source
        this.inRequestSentSourceNames = new Set();

        this.rulesIndexByName = {};
        this.sourcesIndexByName = {};

        this.testPackages = [];
        this.testPackageParameters = {};

        this.isPartialServiceCheck = isPartialServiceCheck;
        this.overwriteRuleSourceData = overwriteRuleSourceData;

        this.initialVertices = [];

        this.abortController = new AbortController();

        this.initialCheckCompleted = false;
    }

    setTestPackages(testPackagesToRun, testPackageParameters) {
        this.testPackages = testPackagesToRun;
        this.testPackageParameters = testPackageParameters;
    }

    setInitialVertices(initialVertices) {
        this.initialVertices = initialVertices;
    }

    abort(initialCheckCompleted) {
        this.abortController.abort();
        this.initialCheckCompleted = initialCheckCompleted;
    }

    async runServiceCheck() {
        let SCr = this.serviceCheckRecord;

        // Note: The source of the service check is currently being determined over the existance of a socket object
        // when the service check is initialized, this is currently not accurate

        // Can refactor to add additional sources such as from legacy / current UI etc.
        if (this.socketObj?.socket) {
            runningStatus.serviceCheckStart(SCr, runningStatus.sourcesEnum.Socket);
        } else {
            runningStatus.serviceCheckStart(SCr, runningStatus.sourcesEnum.API);
        }

        let serviceCheckSavePromises = [];
        let serviceCheckSaveInterval = null;

        let startedOn = new Date();

        try {
            this.ruleSourcePromises = [];

            let relatedServiceChecks = [];
            // Adds related service checks from the origin service check to this one
            if (this.originServiceCheck) {
                relatedServiceChecks.push(this.originServiceCheck);

                for (let serviceCheck of this.originServiceCheck.generatedServiceChecks) {
                    if (serviceCheck !== this) {
                        relatedServiceChecks.push(serviceCheck);
                    }
                }
            }

            for (let i = 0; i < this.sortedVertices.length; i++) {
                let vertex = this.sortedVertices[i];
                let relatedServiceCheckDependencyPromises = [];

                if (vertex instanceof Rule) {
                    this.rulesIndexByName[vertex.name] = i;

                    let rulePromise;

                    if (vertex.runForEachPreSource && vertex.preSources.length > 0) {
                        let rulePromisesAll = [];

                        for (let j = 0; j < vertex.preSources.length; j++) {
                            let [dependencyPromises, relatedServiceCheckDependencyPromises] = this.determineRuleDependencies(
                                vertex,
                                i,
                                relatedServiceChecks,
                                vertex.preRules,
                                vertex.preSources.slice(0, j + 1),
                                j === vertex.preSources.length - 1
                            );
                            this.rulesSourcePromisesFromDependencies.push(relatedServiceCheckDependencyPromises);

                            // Add the previous runs of the rule as another dependency
                            // Could be moved into the determineRuleDependencies function
                            dependencyPromises.push(...rulePromisesAll);

                            let currRulePromise = this.runRule(vertex, vertex.preRules, vertex.preSources.slice(0, j + 1), dependencyPromises, relatedServiceCheckDependencyPromises);
                            rulePromisesAll.push(currRulePromise);
                        }

                        rulePromise = new Promise((resolve, reject) => {
                            for (let currRulePromise of rulePromisesAll) {
                                currRulePromise.then(() => {
                                    if (SCr.rulesData[vertex.name]?.result === RuleResult.ok) {
                                        resolve();
                                    }
                                });
                            }

                            Promise.all(rulePromisesAll).then(() => {
                                resolve();
                            });
                        })
                    } else {
                        let [dependencyPromises, relatedServiceCheckDependencyPromises] = this.determineRuleDependencies(
                            vertex,
                            i,
                            relatedServiceChecks,
                            vertex.preRules,
                            vertex.preSources,
                            true
                        );
                        this.rulesSourcePromisesFromDependencies.push(relatedServiceCheckDependencyPromises);
                        rulePromise = this.runRule(vertex, vertex.preRules, vertex.preSources, dependencyPromises, relatedServiceCheckDependencyPromises);
                    }

                    this.ruleSourcePromises.push(rulePromise);

                    if (vertex.name === global.gConfig.disableDisplayConditionRuleName) {
                        this.disableDisplayConditionPromise = rulePromise;
                    }
                } else if (vertex instanceof Source) {
                    this.sourcesIndexByName[vertex.name] = i;

                    let dependencyPromises = [];

                    for (let preRule of vertex.preRules) {
                        let preRuleIndex = this.rulesIndexByName[preRule];
                        dependencyPromises.push(this.ruleSourcePromises[preRuleIndex]);
                    }

                    for (let preSource of vertex.preSources) {
                        let preSourceIndex = this.sourcesIndexByName[preSource];
                        dependencyPromises.push(this.ruleSourcePromises[preSourceIndex]);
                    }

                    // relatedServiceCheckDependencyPromises will just be an empty array for source, as dependencies
                    // between service checks is currently not implemented for sources
                    this.rulesSourcePromisesFromDependencies.push(relatedServiceCheckDependencyPromises);
                    this.ruleSourcePromises.push(this.collectSource(vertex, dependencyPromises));
                }
            }

            for (let testPackage of this.testPackages) {
                let testPackageRulePromises = [];

                for (let section of testPackage.sections) {
                    let ruleIndex = this.rulesIndexByName[section.ruleName];

                    if (this.ruleSourcePromises[ruleIndex] instanceof Promise) {
                        testPackageRulePromises.push(this.ruleSourcePromises[ruleIndex]);
                    }
                }

                // Sends test package results after all rules in a test package have been resolved
                Promise.all(testPackageRulePromises).then(() => {
                    if (this.socketObj?.socket) {
                        let socket = this.socketObj.socket;

                        let testPackageResults = [];

                        for (let section of testPackage.sections) {
                            testPackageResults.push({
                                name: section.name,
                                title: section.title,
                                ruleName: section.ruleName,
                                data: SCr.rulesData[section.ruleName] ? SCr.rulesData[section.ruleName] : null
                            });
                        }

                        socket.emit('serviceCheck:testPackageResult', {
                            id: SCr.id,
                            name: testPackage.name,
                            results: testPackageResults
                        });
                    }
                });
            }

            // In the service check lifecycle, the source data will not be saved every 30 seconds
            // as there may be a case where a large response from an API will cause a slowdown when
            // it fails to save (may affect other database operations as well)
            SCr.$skipSavingSourcesData = true;

            // Saves the service check record every 30 seconds
            // serviceCheckSaveInterval = setInterval(async function() {
            //     try {
            //         SCr.markModified('rulesData');
            //         SCr.markModified('sourcesMetadata');

            //         let savePromise = SCr.save({ checkKeys: false });
            //         serviceCheckSavePromises.push(savePromise);
            //         await savePromise;
            //     } catch(error) {
            //         logger.error(`Error saving service check ${SCr.id} in interval: ${error.toString()}`);
            //     }
            // }, 30000);

            await Promise.all(this.ruleSourcePromises);

            // Waits for completion of all service checks generated by this service check
            await Promise.all(this.generatedServiceCheckPromises);

            // Clears interval that saves service check record
            clearInterval(serviceCheckSaveInterval);

            // Waits for all calls to save() for the service check record from interval
            // to resolve before saving the document for the final time
            // allSettled is used as the status of the promise (resolve / rejected) does not matter
            await Promise.allSettled(serviceCheckSavePromises);

            let allSourcesSuccessful = Object.keys(SCr.sourcesMetadata).every(sourceName => {
                let sourceStatus = _.get(SCr, ['sourcesMetadata', sourceName, 'status']);
                return [SourceStatus.preConditionFalse, SourceStatus.collected].includes(sourceStatus);
            });

            let serviceCheckStatus;

            if (this.isPartialServiceCheck) {
                if (allSourcesSuccessful) {
                    serviceCheckStatus = ServiceCheckStatus.donePartial;
                } else {
                    serviceCheckStatus = ServiceCheckStatus.completedWithErrorPartial;
                }
            } else {
                if (allSourcesSuccessful) {
                    serviceCheckStatus = ServiceCheckStatus.done;
                } else {
                    serviceCheckStatus = ServiceCheckStatus.completedWithError;
                }
            }

            SCr.status = serviceCheckStatus;

            SCr.endedOn = new Date();
            SCr.durationMilliSec += SCr.endedOn - startedOn;

            await SCr.populate([{
                path: 'outcomes',
                select: {
                    _id: 0,
                    __v: 0,
                    createdBy: 0,
                    createdOn: 0
                }
            }]);

            // Sorts the outcomes by tier ascending and weight descending
            let sortedOutcomes = [...SCr.outcomes];
            sortedOutcomes.sort((outcome1, outcome2) => {
                if (outcome1.tier !== outcome2.tier) {
                    return outcome1.tier - outcome2.tier;
                } else {
                    return outcome2.weight - outcome1.weight;
                }
            });

            if (sortedOutcomes.length > 0) {
                let selectedOutcome = sortedOutcomes[0];

                await selectedOutcome.populate([{
                    path: 'messageBucket',
                    select: {
                        _id: 0,
                        __v: 0,
                        createdBy: 0,
                        createdOn: 0
                    }
                }]);

                // Determines message bucket for service check
                if (selectedOutcome.messageBucket) {
                    SCr.messageBucketName = selectedOutcome.messageBucketName;
                }
            }

            // Saves source data after the service check is completed
            SCr.$skipSavingSourcesData = false;
            try {
                SCr.markModified('rulesData');
                SCr.markModified('sourcesMetadata');

                await SCr.save({ checkKeys: false });
            } catch(error) {
                // May occur when there are keys containing "$" at the start of the string
                // These are not valid for MongoDB, convert the rules data, sources data,
                // sources metadata and additionalParameters fields to a string
                logger.error(`Error saving service check ${SCr.id} after completion: ${error.toString()}`);
                SCr.status = ServiceCheckStatus.error;

                // Converts the rules / sources data and additional parameters to a string to avoid
                // updating the service check record with $ prefixed keys
                SCr.rulesData = JSON.stringify(SCr.rulesData);
                SCr.sourcesMetadata = JSON.stringify(SCr.sourcesMetadata);

                SCr.errorMessage = error.toString();

                await SCr.save({ checkKeys: false });
            }
        } catch(error) {
            // If there is an exception thrown from above that wasn't handled,
            // save it and the error message to the database
            logger.error(`Unhandled error when running service check ${SCr.id}, ${error.toString()}`);

            try {
                clearInterval(serviceCheckSaveInterval);
                await Promise.allSettled(serviceCheckSavePromises);

                SCr.status = ServiceCheckStatus.error;
                SCr.errorMessage = error.toString();

                SCr.markModified('rulesData');
                SCr.markModified('sourcesMetadata');

                await SCr.save({ checkKeys: false });
            } catch(error) {
                logger.error(`Error saving service check ${SCr.id} in unhandled error block: ${error.toString()}`);
            }
        } finally {
            runningStatus.serviceCheckStop(SCr.id);
        }
    }

    determineRuleDependencies(vertex, index, relatedServiceChecks, preRules, preSources, setupRelatedServiceCheckDependencies) {
        let dependencyPromises = [];
        let relatedServiceCheckDependencyPromises = [];

        for (let preRule of preRules) {
            let preRuleIndex = this.rulesIndexByName[preRule];

            dependencyPromises.push(this.ruleSourcePromises[preRuleIndex]);

            // Adds runRule() promises from other ServiceCheck instances that this rule depends on
            if (vertex.depedenciesFromRelatedServiceChecks && setupRelatedServiceCheckDependencies) {
                for (let serviceCheck of relatedServiceChecks) {
                    // Adds promise for preRule that start service checks from related service check
                    // to the first dependenciesPromise array
                    // This is required because a rule that starts service checks could possibly create
                    // another generated ServiceCheck instance (separate from the current one)
                    // where the population of the array this.ruleSourcePromises has not been completed
                    // As a result, it is possible that the list of in the relatedServiceCheckDependencyPromises
                    // array will not contain promises from generated service checks made after the current one
                    if (this.sortedVertices[preRuleIndex].ruleType === "Start Service Check") {
                        dependencyPromises.push(serviceCheck.ruleSourcePromises[preRuleIndex]);
                    }

                    relatedServiceCheckDependencyPromises.push(serviceCheck.ruleSourcePromises[preRuleIndex]);
                    serviceCheck.rulesSourcePromisesFromDependencies[index].push(this.ruleSourcePromises[preRuleIndex]);
                }
            }
        }

        for (let preSource of preSources) {
            let preSourceIndex = this.sourcesIndexByName[preSource];
            dependencyPromises.push(this.ruleSourcePromises[preSourceIndex]);

            // Adds collectSource() promises from other ServiceCheck instances that this rule depends on
            if (vertex.depedenciesFromRelatedServiceChecks && setupRelatedServiceCheckDependencies) {
                for (let serviceCheck of relatedServiceChecks) {
                    relatedServiceCheckDependencyPromises.push(serviceCheck.ruleSourcePromises[preSourceIndex]);
                    serviceCheck.rulesSourcePromisesFromDependencies[index].push(this.ruleSourcePromises[preSourceIndex]);
                }
            }
        }

        return [dependencyPromises, relatedServiceCheckDependencyPromises];
    }

    async runRule(rule, preRules, preSources, dependencyPromises, relatedServiceCheckDependencyPromises) {
        let SCr = this.serviceCheckRecord;

        await Promise.all(dependencyPromises);

        // Also awaits dependencies from related service checks (origin or generated) for the same
        // rules and sources running on those service check instances
        await Promise.all(relatedServiceCheckDependencyPromises);

        // Note: The condition is present for rules that may run multiple times to resolve earlier
        // However this implementation may affect running test packages if any rule has
        // 'runForEachPreSource' to true
        // TODO: Revise in the future
        if (SCr.rulesData?.[rule.name]?.result === RuleResult.ok && rule.runForEachPreSource) {
            return;
        }

        try {
            if (
                rule.level <= SCr.input.level &&
                rule.active &&
                (
                    rule.runForEachPreSource ||
                    !SCr.rulesData?.[rule.name] ||
                    (this.overwriteRuleSourceData && !this.initialVertices.includes(rule))
                )
            ) {
                this.abortController.signal.throwIfAborted();

                let allDependedRulesSuccessful = preRules.every(preRule => {
                    let ruleResult = _.get(SCr, ['rulesData', preRule, 'result']);
                    return rule.preRulesRunOnFail ? ruleResult : ruleResult === RuleResult.ok;
                });

                let allDependedSourcesSuccessful = preSources.every(preSource => {
                    let sourceStatus = _.get(SCr, ['sourcesMetadata', preSource, 'status']);
                    return rule.preSourcesRunOnFail ? sourceStatus : sourceStatus === SourceStatus.collected;
                });

                if ((allDependedRulesSuccessful && allDependedSourcesSuccessful) || rule.runWhenDependenciesResolved) {
                    let startTime = moment();
                    try {
                        let relatedServiceCheckRecords = null;
                        if (rule.depedenciesFromRelatedServiceChecks) {
                            relatedServiceCheckRecords = [];
                            let relatedServiceChecks = [...this.generatedServiceChecks];

                            if (this.originServiceCheck) {
                                relatedServiceCheckRecords.push(this.originServiceCheck.serviceCheckRecord);

                                relatedServiceChecks.push(...this.originServiceCheck.generatedServiceChecks);
                            }

                            for (let serviceCheck of relatedServiceChecks) {
                                if (serviceCheck !== this) {
                                    relatedServiceCheckRecords.push(serviceCheck.serviceCheckRecord);
                                }
                            }
                        }

                        let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, SCr, relatedServiceCheckRecords);

                        // Add some metadata to Rule
                        SCr.rulesData[rule.name].type = rule.ruleType;
                        SCr.rulesData[rule.name].rootCauseCategory = rule.rootCauseCategory;

                        SCr.rulesData[rule.name].result = ruleResult;
                        SCr.rulesData[rule.name].valueMsg = valueMsg;
                        SCr.rulesData[rule.name].extraInfo = extraInfo;
                        SCr.rulesData[rule.name].updatedOn = new Date().toISOString();

                        switch (ruleResult) {
                            case RuleResult.reject:
                                SCr.rulesData[rule.name].msg = rule.preConditionMsg;
                                break;
                            case RuleResult.ok:
                                SCr.rulesData[rule.name].msg = rule.trueMsg;
                                break;
                            case RuleResult.actionable:
                                SCr.rulesData[rule.name].msg = rule.falseMsg;
                                break;
                            case RuleResult.actioned:
                                SCr.rulesData[rule.name].msg = valueMsg ? valueMsg : rule.trueMsg;
                                break;
                            case RuleResult.warning:
                            case RuleResult.failed:
                            default:
                                SCr.rulesData[rule.name].msg = rule.falseMsg;
                                break;
                        }

                        // Generated service checks cannot be created from other generated service checks
                        if (rule.ruleType === 'Start Service Check' && SCr instanceof ServiceCheckModel) {
                            if (ruleResult == RuleResult.ok && Array.isArray(ruleCodeReturnValue)) {
                                // Only allows non-empty strings from the return code value to be used for new service checks
                                let newCheckFNNs = ruleCodeReturnValue.filter(element => {
                                    return typeof element === 'string' && element.length;
                                });

                                // Ensure that a service check is not run on the same FNN
                                // Adds the input FNN and record FNN field (which may differ) to the set of checked FNNs
                                this.checkedFNNs.add(SCr.fnn);
                                this.checkedFNNs.add(SCr.input.searchFNN);

                                for (let fnn of newCheckFNNs) {
                                    if (!this.checkedFNNs.has(fnn)) {
                                        this.checkedFNNs.add(fnn);

                                        // Inputs other than the suite and level from the original service check will not be
                                        // used in a generated service check
                                        let generatedSCr = new GeneratedServiceCheckModel();
                                        generatedSCr.id = generatedSCr._id;
                                        generatedSCr.input = {
                                            searchFNN: fnn,
                                            level: SCr.input.level
                                        };

                                        generatedSCr.fnn = generatedSCr.input.searchFNN.trim().toUpperCase();

                                        generatedSCr.createdBy = this.serviceCheckRecord.createdBy;
                                        await generatedSCr.save();

                                        if (this.socketObj && this.socketObj.socket) {
                                            let socket = this.socketObj.socket;
                                            socket.emit('serviceCheckStarted', generatedSCr.id, SCr.id, SCr.fnn);
                                        }

                                        SCr.generatedServiceChecks.push(generatedSCr._id);

                                        let generatedServiceCheck = new ServiceCheck(generatedSCr, this.user, this.socketObj, this.sortedVertices, this.runOutageInfo, this.isPartialServiceCheck, this.overwriteRuleSourceData, this);
                                        this.generatedServiceChecks.push(generatedServiceCheck);
                                        this.generatedServiceCheckPromises.push(generatedServiceCheck.runServiceCheck());
                                    }
                                }
                            }
                        }
                    } catch(error) {
                        SCr.rulesData[rule.name].error = error.toString();

                        SCr.rulesData[rule.name].type = rule.ruleType;
                        SCr.rulesData[rule.name].rootCauseCategory = rule.rootCauseCategory;

                        SCr.rulesData[rule.name].result = RuleResult.error;
                        SCr.rulesData[rule.name].valueMsg = null;
                        SCr.rulesData[rule.name].extraInfo = null;
                        SCr.rulesData[rule.name].updatedOn = new Date().toISOString();
                        SCr.rulesData[rule.name].msg = rule.errorMsg;
                    } finally {
                        let ruleRuntime = moment().diff(startTime) / 1000;

                        promHistogramRuleDuration.labels(rule.name, SCr.rulesData[rule.name].result).observe(ruleRuntime);

                        // Sends rule status through socket
                        if (this.socketObj && this.socketObj.socket) {
                            let socket = this.socketObj.socket;

                            let disableDisplayMessage = disableDisplayCondition(SCr, this.user);
                            if (disableDisplayMessage) {
                                socket.emit('serviceCheckDisableDisplayCondition', SCr.id, [disableDisplayMessage.message, disableDisplayMessage.link].join(' '));

                                // Disables socket for service check record to prevent sending further updates to client
                                this.socketObj.socket = null;
                            }
                        }

                        const sendSocketRuleData = () => {
                            if (this.socketObj && this.socketObj.socket) {
                                let socket = this.socketObj.socket;

                                // Updates data for InRequest- sources which are created from rules
                                for (let sourceName in SCr.sourcesData) {
                                    if (sourceName.indexOf("InRequest-") === 0 && !this.inRequestSentSourceNames.has(sourceName)) {
                                        this.inRequestSentSourceNames.add(sourceName);

                                        let collectDataUpdate = {
                                            category: sourceName,
                                            name: sourceName,
                                            SCid: SCr.id,
                                            data: SCr.sourcesData[sourceName],
                                            hideInResult: false
                                        };

                                        socket.emit('CollectDataUpdate', collectDataUpdate);
                                    }
                                }

                                socket.emit('ruleCheckMsg', SCr.id, rule, SCr.rulesData[rule.name]);
                            }
                        }

                        if (this.disableDisplayConditionPromise) {
                            this.disableDisplayConditionPromise.then(() => {
                                sendSocketRuleData();
                            });
                        } else {
                            sendSocketRuleData();
                        }
                    }
                }
            }
        } catch(error) {
            if (error.name !== 'AbortError') {
                // Should never occur, as errors are handled for runRule()
                logger.error(`Unexpected error in service check ${SCr.id} when running rule ${rule.name}, ${error.toString()}`);
            }
        }
    }

    async collectSource(source, dependencyPromises) {
        let SCr = this.serviceCheckRecord;
        await Promise.all(dependencyPromises);

        try {
            if (
                source.level <= SCr.input.level &&
                source.active &&
                (!this.runOutageInfo || (this.runOutageInfo && OUTAGE_INFO_SOURCE_NAMES.includes(source.name))) &&
                (
                    !SCr.sourcesMetadata?.[source.name] ||
                    (this.overwriteRuleSourceData && !this.initialVertices.includes(source))
                )
            ) {
                this.abortController.signal.throwIfAborted();

                // If preRulesRunOnFail is true, expect rules data to be present for all preRules, otherwise
                // every pre rule must have a result of "OK"
                let preRulesSuccessful = source.preRules.every(preRule => {
                    let ruleResult = _.get(SCr, ['rulesData', preRule, 'result']);
                    return source.preRulesRunOnFail ? ruleResult : ruleResult === RuleResult.ok;
                });

                // If preSourcesRunOnFail is true, expect sources metadata to be present for all preSources, otherwise
                // every pre source must have a status of "Collected"
                let preSourcesSuccessful = source.preSources.every(preSource => {
                    let sourceStatus = _.get(SCr, ['sourcesMetadata', preSource, 'status']);
                    return source.preSourcesRunOnFail ? sourceStatus : sourceStatus === SourceStatus.collected;
                });

                if ((preRulesSuccessful && preSourcesSuccessful) || source.runWhenDependenciesResolved) {
                    SCr.sourcesMetadata[source.name] = {
                        preCondResult: false
                    };

                    let parameters = {};

                    const isolate = new ivm.Isolate({
                        memoryLimit: 64
                    });
                    let sourceCodeContext = await isolate.createContext();
                    let sourceErrorMessageContext = await isolate.createContext();

                    try {
                        await sourceCodeContext.global.set('data', new ivm.ExternalCopy(SCr.toJSON()).copyInto());
                        await sourceCodeContext.global.set('r', sourceCodeContext.global.getSync('data').getSync('rulesData').derefInto());
                        await sourceCodeContext.global.set('s', sourceCodeContext.global.getSync('data').getSync('sourcesData').derefInto());
                        await sourceCodeContext.global.set('InputTypes', new ivm.ExternalCopy(InputTypes).copyInto());
                        await sourceCodeContext.global.set('ProductTypes', new ivm.ExternalCopy(ProductTypes).copyInto());

                        // Currently only sets specific functions from mergeLib
                        // TODO: implement dynamic loading of all functions from merge lib into the isolate context
                        await sourceCodeContext.global.set('checkIsValidPhoneNumber', function(...args) {
                            return mergeLib.checkIsValidPhoneNumber(...args);
                        });
                        await sourceCodeContext.global.set('checkIsMobilePhoneNumber', function(...args) {
                            return mergeLib.checkIsMobilePhoneNumber(...args);
                        });
                        await sourceCodeContext.global.set('formatE164PhoneNumber', function (...args) {
                            return mergeLib.formatE164PhoneNumber(...args);
                        });
                        await sourceCodeContext.global.set('formatNationalPhoneNumber', function (...args) {
                            return mergeLib.formatNationalPhoneNumber(...args);
                        });

                        if (source.preCondition) {
                            try {
                                SCr.sourcesMetadata[source.name].preCondResult = await sourceCodeContext.eval(source.preCondition, { copy: true, timeout: 2000 });
                            } catch(error) {
                                SCr.sourcesMetadata[source.name].preCondResult = false;
                                SCr.sourcesMetadata[source.name].error = error.toString();
                                SCr.sourcesMetadata[source.name].status = SourceStatus.preConditionError;

                                if (this.socketObj && this.socketObj.socket) {
                                    let socket = this.socketObj.socket;

                                    socket.emit('sourceError', SCr.id, source.title, SCr.sourcesMetadata[source.name].error);

                                    let collectDataUpdate = {
                                        category: source.title,
                                        name: source.name,
                                        SCid: SCr.id,
                                        metadata: SCr.sourcesMetadata[source.name],
                                        hideInResult: source.hideInResult
                                    };

                                    socket.emit('CollectDataUpdate', collectDataUpdate);
                                }

                                return;
                            }
                        } else {
                            // If the precondition is an empty string
                            SCr.sourcesMetadata[source.name].preCondResult = true;
                        }

                        if (SCr.sourcesMetadata[source.name].preCondResult) {
                            let api = null;

                            try {
                                // Note: Retrieval of API could be moved to a $lookup with the source
                                // Also if this fails it will be counted as a parameter error
                                api = await Api.findOne({ name: source.api });

                                debugMsg(`#> run parameter of '${source.name}' : ${source.param}`);

                                await sourceCodeContext.eval(source.param, { timeout: 2000 });

                                if (api) {
                                    for (let parameterName of api.parameters) {
                                        let parameterValue = await sourceCodeContext.global.get(parameterName, { copy: true });
                                        if (parameterValue !== undefined) {
                                            parameters[parameterName] = parameterValue;
                                        }
                                    }
                                }

                                // Determines if the current source is a direct dependency of a test package rule with parameters,
                                // If so, inject the test package parameters into the API
                                // This implementation means that the source needs to be a direct dependency of the rule that is
                                // tied to the test package
                                // Also may not work if multiple test packages have parameter inputs as it will use the last test package
                                // where these conditions apply
                                let isInTestPackageWithParameters = false;
                                let sourceTestPackage = null;

                                for (let testPackage of this.testPackages) {
                                    for (let section of testPackage.sections) {
                                        let rule = this.sortedVertices.find((vertex) => vertex instanceof Rule && vertex.name === section.ruleName);
                                        if (rule && rule.preSources.includes(source.name)) {
                                            isInTestPackageWithParameters = true;
                                            sourceTestPackage = testPackage;
                                        }
                                    }
                                }

                                if (isInTestPackageWithParameters) {
                                    if (!isNaN(Date.parse(this.testPackageParameters?.[sourceTestPackage.name]?.startDate))) {
                                        parameters.startDate = new Date(this.testPackageParameters[sourceTestPackage.name].startDate).toISOString();
                                    }

                                    if (!isNaN(Date.parse(this.testPackageParameters?.[sourceTestPackage.name]?.endDate))) {
                                        parameters.endDate = new Date(this.testPackageParameters[sourceTestPackage.name].endDate).toISOString();
                                    }
                                }

                                SCr.sourcesMetadata[source.name].parameters = parameters;
                            } catch(error) {
                                SCr.sourcesMetadata[source.name].error = error.toString();
                                SCr.sourcesMetadata[source.name].status = SourceStatus.parameterError;
                                debugMsg(`!> ${source.name} Parameter error `, error.message);

                                if (this.socketObj && this.socketObj.socket) {
                                    let socket = this.socketObj.socket;

                                    socket.emit('sourceError', SCr.id, source.title, SCr.sourcesMetadata[source.name].error);

                                    let collectDataUpdate = {
                                        category: source.title,
                                        name: source.name,
                                        SCid: SCr.id,
                                        metadata: SCr.sourcesMetadata[source.name],
                                        hideInResult: source.hideInResult
                                    };

                                    socket.emit('CollectDataUpdate', collectDataUpdate);
                                }

                                return;
                            }

                            try {
                                if (api === null) {
                                    throw new Error(`API ${source.api} does not exist`);
                                }

                                SCr.sourcesMetadata[source.name].status = SourceStatus.running;

                                if (this.socketObj && this.socketObj.socket) {
                                    let socket = this.socketObj.socket;

                                    socket.emit('CollectDataStart', source.title, source.name, SCr.id, source.hideInResult);
                                }

                                let result = await mergeCollect.collectApi(api, parameters, this.abortController.signal, this.socketObj);

                                SCr.sourcesMetadata[source.name].status = SourceStatus.collected;
                                SCr.sourcesMetadata[source.name].collectionDuration = result.collectionDuration;
                                SCr.sourcesMetadata[source.name].collectionTime = (moment().diff(SCr.createdOn) / 1000).toFixed(2);
                                SCr.sourcesMetadata[source.name].statusCode = result.status;
                                SCr.sourcesData[source.name] = result.data;

                                // Checks if service check meets a condition that if met, will stop updating the client with
                                // further information about the service check.
                                if (this.socketObj && this.socketObj.socket) {
                                    let socket = this.socketObj.socket;

                                    let disableDisplayMessage = disableDisplayCondition(SCr, this.user);
                                    if (disableDisplayMessage) {
                                        socket.emit('serviceCheckDisableDisplayCondition', SCr.id, [disableDisplayMessage.message, disableDisplayMessage.link].join(' '));

                                        // Disables socket for service check record to prevent sending further updates to client
                                        this.socketObj.socket = null;
                                    }
                                }
                            } catch(error) {
                                if (error.name === 'CanceledError') {
                                    // Sets the status to error (eventually may be sent through socket, but is not stored)
                                    SCr.sourcesMetadata[source.name].status = SourceStatus.error;
                                    SCr.sourcesMetadata[source.name].error = 'Source collection aborted';

                                    // Re-throws error
                                    throw error;
                                } else {
                                    if (error.message == constants.PROXY_AUTH_ERROR_MESSAGE) {
                                        authEmitter.emit('AuthError');
                                    }

                                    let overrideErrorResult = false;

                                    // More error types could be added here (and in the collectApi() function)
                                    // if there needs to be a distinction between them
                                    if (error instanceof ApiUriError) {
                                        SCr.sourcesMetadata[source.name].status = SourceStatus.apiUriError;
                                    } else {
                                        SCr.sourcesMetadata[source.name].status = SourceStatus.error;
                                    }

                                    // Handles cases where the HTTP response was received but an error occurred,
                                    // such as 500 status or error condition
                                    if (error.result) {
                                        SCr.sourcesMetadata[source.name].collectionDuration = error.result.collectionDuration;
                                        SCr.sourcesMetadata[source.name].collectionTime = (moment().diff(SCr.createdOn) / 1000).toFixed(2);
                                        SCr.sourcesMetadata[source.name].statusCode = error.result.status;
                                        SCr.sourcesData[source.name] = error.result.data;

                                        // Adds the result from error as 'response' in the vm sandbox
                                        await sourceErrorMessageContext.global.set('response', new ivm.ExternalCopy(error.result).copyInto());

                                        if (source.overrideErrorCondition) {
                                            try {
                                                overrideErrorResult = await sourceErrorMessageContext.eval(source.overrideErrorCondition, { copy: true, timeout: 2000 });
                                            } catch(overrideErrorResultError) {
                                                // Overrides current error with error message regarding the exception thrown from running
                                                // the override error condition code
                                                error = new SourceCodeError('Error when determining override error result from code', { cause: overrideErrorResultError });
                                            }
                                        }

                                        // Same check as above to stop sending data to client if condition to disable display is true
                                        if (this.socketObj && this.socketObj.socket) {
                                            let socket = this.socketObj.socket;

                                            let disableDisplayMessage = disableDisplayCondition(SCr, this.user);
                                            if (disableDisplayMessage) {
                                                socket.emit('serviceCheckDisableDisplayCondition', SCr.id, [disableDisplayMessage.message, disableDisplayMessage.link].join(' '));

                                                // Disables socket for service check record to prevent sending further updates to client
                                                this.socketObj.socket = null;
                                            }
                                        }
                                    } else {
                                        await sourceErrorMessageContext.global.set('response', new ivm.ExternalCopy({}).copyInto());
                                    }

                                    if (source.errorMessage) {
                                        try {
                                            let errorMessageSummary = await sourceErrorMessageContext.eval(source.errorMessage, { copy: true, timeout: 2000 });

                                            if (!_.isNil(errorMessageSummary)) {
                                                SCr.sourcesMetadata[source.name].errorMessageSummary = _.toString(errorMessageSummary);
                                            }
                                        } catch(errorMessageSummaryError) {
                                            // Overrides current error with error message regarding the exception thrown from running
                                            // the override error condition code
                                            error = new SourceCodeError('Error when determining error message summary from code', { cause: errorMessageSummaryError });
                                        }
                                    }

                                    if (overrideErrorResult) {
                                        // Sets source status to collected if override error code resolves as true
                                        SCr.sourcesMetadata[source.name].status = SourceStatus.collected;
                                    } else {
                                        SCr.sourcesMetadata[source.name].error = error.toString();

                                        if (this.socketObj && this.socketObj.socket) {
                                            let socket = this.socketObj.socket;
                                            let errorMessage = SCr.sourcesMetadata[source.name].errorMessageSummary ? SCr.sourcesMetadata[source.name].errorMessageSummary : SCr.sourcesMetadata[source.name].error;
                                            socket.emit('sourceError', SCr.id, source.title, errorMessage);
                                        }
                                    }
                                }
                            } finally {
                                // collectionDuration is stored as a string, attempt to convert before sending metrics
                                let sourceRuntime = parseFloat(SCr.sourcesMetadata[source.name].collectionDuration);
                                SCr.sourcesMetadata[source.name].collectionTime = (moment().diff(SCr.createdOn) / 1000).toFixed(2);
                                if (!isNaN(sourceRuntime)) {
                                    promHistogramSourceDuration.labels(source.name, SCr.sourcesMetadata[source.name].status).observe(sourceRuntime);
                                }
                                SCr.sourcesMetadata[source.name].updatedOn = new Date().toISOString();

                                const sendSocketSourceData = () => {
                                    if (this.socketObj && this.socketObj.socket) {
                                        let socket = this.socketObj.socket;

                                        let collectDataUpdate = {
                                            category: source.title,
                                            name: source.name,
                                            SCid: SCr.id,
                                            metadata: SCr.sourcesMetadata[source.name],
                                            data: SCr.sourcesData[source.name],
                                            hideInResult: source.hideInResult
                                        };

                                        debugMsg(`Socket Emit CollectDataUpdate '${source.name}' hide '${source.hideInResult}`);
                                        socket.emit('CollectDataUpdate', collectDataUpdate);

                                        let serviceCheckRecordObject = SCr.toJSON();

                                        // Basic space optimisation to not send the entire service check,
                                        // excluding most of the rules data and all the sources data from the payload
                                        serviceCheckRecordObject.rulesData = _.pick(serviceCheckRecordObject.rulesData, SERVICE_CHECK_RECORD_UPDATE_RULE_NAMES);
                                        delete serviceCheckRecordObject.sourcesData;
                                        delete serviceCheckRecordObject.sourcesMetadata;

                                        socket.emit('SCRecordUpdate', serviceCheckRecordObject);
                                    }
                                };

                                if (this.disableDisplayConditionPromise) {
                                    this.disableDisplayConditionPromise.then(() => {
                                        sendSocketSourceData();
                                    });
                                } else {
                                    sendSocketSourceData();
                                }
                            }
                        } else {
                            // Checks if the error message matches case where authentication to the
                            // HTTP proxy fails and emits an AuthError event
                            SCr.sourcesMetadata[source.name].status = SourceStatus.preConditionFalse;
                            SCr.sourcesMetadata[source.name].updatedOn = new Date().toISOString();
                        }
                    } finally {
                        sourceCodeContext.release();
                        sourceErrorMessageContext.release();
                        if (!isolate.isDisposed) {
                            isolate.dispose();
                        }
                    }
                }
            }
        } catch(error) {
            // Handles AbortError from throwIfAborted() and CanceledError from axios
            if (error.name === 'AbortError' || error.name === 'CanceledError') {
                if (!this.initialCheckCompleted && error.name === 'CanceledError') {
                    logger.warn(`Service check ${SCr.id}, source ${source.name} collection aborted during initial check`);
                }
                delete SCr.sourcesMetadata[source.name];
            } else {
                // Should never occur, as errors are handled for collectApi()
                logger.error(`Unexpected error in service check ${SCr.id} when collecting source ${source.name}, ${error.toString()}`);
            }
        }
    }
}


export function disableDisplayCondition(SCr, user) {
    let displayConditionError = null;

    if (((_.get(SCr, ['OffshoreResources']) === 'No' || _.get(SCr, ['OffshoreResources']) === 'Conditional') &&
        !(_.get(SCr, ['sourcesData', 'CIDNCustomerConsentExclusion', 'excluded']) === true)) &&
        (!_.get(user, ['offshoreAccess'], false))
    ) {
        displayConditionError = {
            message: "You don't have access to view this customer's information. If you need to view the information, please apply for role 'Australian based staff' through AGS.",
            link: "<a href=\"https://confluence.tools.telstra.com/display/MERGE/Merge+Access\">More info here.</a>"
        };
    } else if (_.get(SCr, ['sourcesData', 'MODINI', 'Service Potential Issues']) && /service managed by Telstra Wholesale systems/i.test(SCr.sourcesData['MODINI']['Service Potential Issues'])) {
        displayConditionError = {
            message: "The service you are looking for is that of a wholesale customer. Merge currently does not allow viewing wholesale customer data."
        };
    } else if (_.get(SCr, ['rulesData', 'MTR076', 'result']) && SCr.rulesData['MTR076'].result === 'Failed') {
        displayConditionError = {
            message: "The service CIDN provided doesn't match the CIDN details in Magpie. Merge currently does not allow viewing records with incorrect CIDN data."
        };
    }

    return displayConditionError;
}


export function setReadOnlyFields(SCr) {
    // Service check record fields that are set to non-configurable / non-writable in
    // vm2 environment
    Object.defineProperties(SCr, {
        input: {
            configurable: false,
            writable: false,
            value: SCr.input
        },
        additionalParameters: {
            configurable: false,
            writable: false,
            value: SCr.additionalParameters
        },
        rulesData: {
            configurable: false,
            writable: false,
            value: SCr.rulesData
        },
        sourcesData: {
            configurable: false,
            writable: false,
            value: SCr.sourcesData
        },
        sourcesMetadata: {
            configurable: false,
            writable: false,
            value: SCr.sourcesMetadata
        },
        outcomeNames: {
            configurable: false,
            writable: false,
            value: SCr.outcomeNames
        },
        feedback: {
            configurable: false,
            writable: false,
            value: SCr.feedback
        }
    });

    // Makes each rule / source name in rulesData, sourcesData and sourcesMetadata
    // non-configurable / non-writable
    for (let ruleName in SCr.rulesData) {
        Object.defineProperty(SCr.rulesData, ruleName, {
            configurable: false,
            writable: false,
            value: SCr.rulesData[ruleName]
        });
    }

    for (let sourceName in SCr.sourcesData) {
        let isMutable = false;
        for (let sourceNamePrefix of constants.SOURCE_NAME_PREFIXES_MUTABLE) {
            if (typeof sourceName === "string" && sourceName.startsWith(sourceNamePrefix)) {
                isMutable = true;
                break;
            }
        }

        if (!isMutable) {
            Object.defineProperty(SCr.sourcesData, sourceName, {
                configurable: false,
                writable: false,
                value: SCr.sourcesData[sourceName]
            });
        }
    }

    for (let sourceName in SCr.sourcesMetadata) {
        Object.defineProperty(SCr.sourcesMetadata, sourceName, {
            configurable: false,
            writable: false,
            value: SCr.sourcesMetadata[sourceName]
        });
    }
}


export function validateAccessForUser(serviceCheckRecord, user) {
    if (!(serviceCheckRecord instanceof ServiceCheckModel)) {
        throw new TypeError("Service check record passed to function must be an instance of the ServiceCheckModel model.");
    }

    // Ensures user is authorized to view data inside this service check
    let disableDisplayMessage = disableDisplayCondition(serviceCheckRecord, user);
    let level = serviceCheckRecord.input.level;

    if (disableDisplayMessage) {
        throw new ServiceCheckAccessError(disableDisplayMessage.message);
    } else if (!auth.checkUserAuthorizedForServiceCheckLevel(user, level)) {
        throw new ServiceCheckAccessError(`You do not have access to view service check record ${serviceCheckRecord.id} with level: ${level}`);
    }
}


/**
 *
 * @param {String} id ID of service check
 * @param {Object} select
 * @returns
 */
export async function findAndPopulate(id, select) {
    let serviceCheckRecord = await ServiceCheckModel.findOne({ id: id }, select).populate([{
        path: 'generatedServiceChecks',
        populate: [{
            path: 'outcomes',
            select: {
                _id: 0,
                __v: 0,
                createdBy: 0,
                createdOn: 0
            }
        }],
        select: {
            ...select
        }
    },
    {
        path: 'outcomes',
        select: {
            _id: 0,
            __v: 0,
            createdBy: 0,
            createdOn: 0
        }
    },
    {
        path: 'messageBucket',
        select: {
            _id: 0,
            __v: 0,
            createdBy: 0,
            createdOn: 0
        }
    }]);

    if (serviceCheckRecord) {
        await serviceCheckRecord.populateSourcesData();
        for (let generatedServiceCheckRecord of serviceCheckRecord.generatedServiceChecks) {
            await generatedServiceCheckRecord.populateSourcesData();
        }
    }

    return serviceCheckRecord;
}


export async function serviceCheckHistoryByFnn(fnn, suite, limit, offset, startDate) {
    let conditions = [
        { createdOn: { $gte: startDate }},
        { fnn: fnn }
    ];

    if (suite) {
        conditions.push({ 'input.suite': suite });
    }

    let [serviceChecks, countResult] = await Promise.all([
        ServiceCheckModel.aggregate([
            {
                $match: { $and: conditions }
            },
            { $sort: { createdOn: -1 }},
            { $skip: offset },
            { $limit: limit },
            { $project : {
                _id: 0,
                id: 1,
                createdBy: 1,
                createdOn: 1,
                status: 1,
                'input.suite': 1
            }}
        ]),
        ServiceCheckModel.aggregate([
            {
                $match: { $and: conditions }
            },
            { $count: 'total' }
        ])
    ]);
    let count = _.get(countResult, [0, 'total'], 0);

    return [serviceChecks, count];
}


export function getRecords(options, includeCount) {
    let {
        limit,
        offset,
        sort,
        order,
        fnn,
        status,
        suite,
        carriageType,
        carriageFNN,
        deviceName,
        siiamCases,
        serviceCentralIncidents,
        createdBy,
        billingFNN,
        MDNFNN,
        CIDN,
        nbnAccessType,
        nbnId,
        serviceType,
        feedback,
        startMethod,
        startDate,
        endDate
    } = options;

    let orderByParam = (order === 'desc') ? -1 : 1;

    // Rewrites sorting by suite field here as $sort occurs before $project
    // The aggregation pipeline needs this order as using $project before $sort
    // can cause memory limit issues when sorting with projected fields
    if (sort === 'suite') {
        sort = 'input.suite';
    }

    let querySort = { [sort]: orderByParam };

    // Get all the query parameters that can be used to filter service checks that does not require case sensitivity
    let valueFilters = {
        fnn,
        status,
        suite,
        carriageType,
        carriageFNN,
        createdBy,
        billingFNN,
        MDNFNN,
        CIDN,
        nbnAccessType,
        nbnId,
        serviceType,
        startMethod
    };

    // Get all the query parameters that need to searched case insensitively or partially
    let queryFilters = {
        deviceName
    };

    let booleanFilters = {
        siiamCasesLength: siiamCases,
        serviceCentralIncidentsLength: serviceCentralIncidents
    };

    let createdOnFilter = { $gte: startDate };
    if (endDate) {
        createdOnFilter['$lt'] = endDate;
    }

    let serviceCheckFilters = [{ createdOn: createdOnFilter }];

    // Add each query parameter to condition for service checks
    for (let filter in valueFilters) {
        if (valueFilters[filter] != undefined) {
            if (filter === "fnn" && isValidPhoneNumber(valueFilters[filter], "AU")) {
                let phoneNumber = parsePhoneNumber(valueFilters[filter], "AU").format("E.164");
                serviceCheckFilters.push({ phoneNumber: { $eq: phoneNumber }});
            } else if (filter === "suite") {
                serviceCheckFilters.push({ "input.suite": { $eq: valueFilters[filter] }});
            } else {
                serviceCheckFilters.push({ [filter]: { $eq: valueFilters[filter] }});
            }
        }
    }

    // Add every query parameter to check partial text or case insensitively
    for (let filter in queryFilters) {
        if (queryFilters[filter] != undefined) {
            for (let operation in queryFilters[filter]) {
                switch (operation) {
                    case "equal":
                        serviceCheckFilters.push({ [filter]: { $eq: queryFilters[filter][operation] }});
                        break;
                    case "like":
                        serviceCheckFilters.push({ [filter]: { $regex: new RegExp(escapeStringRegexp(queryFilters[filter][operation]), 'i') }});
                        break;
                }
            }
        }
    }

    switch (feedback) {
        case 'feedbackNotExists':
            serviceCheckFilters.push({ feedback: null });
            break;
        case 'feedbackExists':
            serviceCheckFilters.push({ feedback: { $ne: null }});
            break;
        case 'feedbackIsPositive':
            serviceCheckFilters.push({ 'feedback.isPositive': true });
            break;
        case 'feedbackIsNegative':
            serviceCheckFilters.push({ 'feedback.isPositive': false });
            break;
        case 'feedbackIsNegativeWithMessage':
            serviceCheckFilters.push(...[
                { 'feedback.isPositive': false },
                { 'feedback.messageExists': true }
            ]);
            break;
        case 'feedbackIsNegativeWithMessageRead':
            serviceCheckFilters.push(...[
                { 'feedback.isPositive': false },
                { 'feedback.messageExists': true },
                { 'feedback.messageRead': true }
            ]);
            break;
        case 'feedbackIsNegativeWithMessageUnread':
            serviceCheckFilters.push(...[
                { 'feedback.isPositive': false },
                { 'feedback.messageExists': true },
                { 'feedback.messageRead': false }
            ]);
            break;
        case 'feedbackIsPositiveWithMessage':
            serviceCheckFilters.push(...[
                { 'feedback.isPositive': true},
                { 'feedback.messageExists': true}
            ]);
            break;
        case 'feedbackIsPositiveWithMessageRead':
            serviceCheckFilters.push(...[
                { 'feedback.isPositive': true},
                { 'feedback.messageExists': true},
                { 'feedback.messageRead': true}
            ]);
            break;
        case 'feedbackIsPositiveWithMessageUnread':
            serviceCheckFilters.push(...[
                { 'feedback.isPositive': true},
                { 'feedback.messageExists': true},
                { 'feedback.messageRead': false}
            ]);
            break;
        case 'feedbackWithMessage':
            serviceCheckFilters.push(...[
                { 'feedback.messageExists': true }
            ]);
            break;
        case 'feedbackWithMessageRead':
            serviceCheckFilters.push(...[
                { 'feedback.messageExists': true },
                { 'feedback.messageRead': true }
            ]);
            break;
        case 'feedbackWithMessageUnread':
            serviceCheckFilters.push(...[
                { 'feedback.messageExists': true },
                { 'feedback.messageRead': false }
            ]);
            break;
    }

    for (let filter in booleanFilters) {
        if (booleanFilters[filter] != undefined) {
            if (booleanFilters[filter] == true) {
                serviceCheckFilters.push({ [filter]: { $gt: 0 }});
            } else {
                serviceCheckFilters.push({ [filter]: { $eq: 0 }});
            }
        }
    }

    let condition = { $and: serviceCheckFilters };

    let documentAggregate = [
        { $match: condition },
        { $sort: querySort },
        { $skip: offset }
    ];

    if (typeof limit === 'number') {
        documentAggregate.push({ $limit: limit });
    }

    documentAggregate.push({
        $project: {
            _id: 0,
            id: 1,
            fnn: 1,
            status: 1,
            suite: '$input.suite',
            billingFNN: 1,
            carriageType: 1,
            carriageFNN: 1,
            MDNFNN: 1,
            CIDN: 1,
            nbnAccessType: 1,
            nbnId: 1,
            createdBy: 1,
            deviceName: 1,
            serviceType: 1,
            siiamCases: 1,
            serviceCentralIncidents: 1,
            feedback: 1,
            startMethod: 1,
            createdOn: 1
        }
    });

    if (includeCount) {
        return [
            ServiceCheckModel.aggregate(documentAggregate).collation({ locale: 'en', strength: 1 }),
            ServiceCheckModel.aggregate([
                { $match: condition },
                { $count: 'total' }
            ]).collation({ locale: 'en', strength: 1 })
        ];
    } else {
        return ServiceCheckModel.aggregate(documentAggregate).collation({ locale: 'en', strength: 1 });
    }
}


export async function transformServiceCheck(serviceCheckRecord, user, includeTestResults) {
    const data = serviceCheckRecord.toJSON();
    const r = data.rulesData;
    const s = data.sourcesData;
    
    let magpieDiagrams = [];

    if (typeof s.MagpieSchematics?.schematics === 'string') {
        magpieDiagrams.push({
            type: 'schematic',
            fnn: data.sourcesMetadata?.MagpieSchematics?.parameters?.fnn,
            xml: s.MagpieSchematics.schematics
        });
    }

    if (typeof s.MagpieSchematicsCarriageFNN?.schematics === 'string') {
        magpieDiagrams.push({
            type: 'schematic',
            fnn: data.sourcesMetadata?.MagpieSchematicsCarriageFNN?.parameters?.fnn,
            xml: s.MagpieSchematicsCarriageFNN.schematics
        });
    }

    if (typeof s.MagpieMDNSchematics?.data?.schematics === 'string') {
        magpieDiagrams.push({
            type: 'schematicMDN',
            fnn: data.sourcesMetadata?.MagpieMDNSchematics?.parameters?.fnn,
            xml: s.MagpieMDNSchematics.data.schematics
        });
    }

    let serviceCheckTestPackages = getTestPackagesForProductTypes(data.productTypes).map((testPackage) => {

        return {
            ..._.omit(testPackage, ['sections', 'productTypes']),
            sectionTitles: testPackage.sections.map((section) => section.title)
        };
    });

    let serviceCheckRecordResponse = {
        id: data.id,
        status: data.status,
        productTypes: data.productTypes,
        createdBy: data.createdBy,
        createdOn: data.createdOn,
        feedback: data.feedback,
        customerDetails: {},
        productDetails: {},
        deviceDetails: {},
        accountStatus: {},
        outages: {},
        activeIncidents: {},
        diagrams: magpieDiagrams,
        testPackages: serviceCheckTestPackages
    };

    let serviceCheckOverviewFields = getServiceCheckOverviewFields();

    let fieldValues = extractOverviewFields(serviceCheckRecord, serviceCheckOverviewFields);

    let serviceCheckInfoFieldsProductTypes = getServiceCheckOverviewFieldsForProductTypes(data.productTypes);

    for (let infoField of serviceCheckInfoFieldsProductTypes) {
        _.set(serviceCheckRecordResponse, infoField.responseKeyMap, fieldValues[infoField.name]);
    }

    if (includeTestResults) {
        // Uses messageFrontOfHouse field as the next best action field for now
        serviceCheckRecordResponse.nextBestAction = serviceCheckRecord.messageBucket?.messageFrontOfHouse ? serviceCheckRecord.messageBucket.messageFrontOfHouse : null;
        serviceCheckRecordResponse.testActions = [];
        serviceCheckRecordResponse.textTemplates = await textTemplate.listActiveTemplatesServiceCheck(serviceCheckRecord, user);
        serviceCheckRecordResponse.testResults = [];
        
        let rules = await mergeRule.getRules();
        for (let ruleName in r) {
            const rule = r[ruleName];
            const extraRuleData = rules.filter(f => f.name === ruleName)[0]
            if ([RuleResult.actionable, RuleResult.actioned].includes(rule?.result)) {
                
                serviceCheckRecordResponse.testActions.push({
                    name: ruleName,
                    ..._.pick(rule, ['action', 'msg', 'result', 'updatedOn', 'valueMsg']),
                    userInputs: extraRuleData?.action.userInputs
                });
            }
        }
        
        let serviceCheckTestPackages = getTestPackagesForProductTypes(serviceCheckRecord.productTypes);

        for (let testPackage of serviceCheckTestPackages) {
            let testPackageResults = [];

            for (let section of testPackage.sections) {
                testPackageResults.push({
                    name: section.name,
                    title: section.title,
                    ruleName: section.ruleName,
                    data: serviceCheckRecord.rulesData[section.ruleName] || null
                });
            }

            serviceCheckRecordResponse.testResults.push({
                packageName: testPackage.name,
                packageTitle: testPackage.title,
                results: testPackageResults
            });
        }
    }

    return serviceCheckRecordResponse;
}



export default {
    start: start,
    runServiceCheckInitial: runServiceCheckInitial,
    runServiceCheckTestPackages: runServiceCheckTestPackages,
    runServiceCheckFull: runServiceCheckFull,
    disableDisplayCondition: disableDisplayCondition,
    findAndPopulate: findAndPopulate,
    setReadOnlyFields: setReadOnlyFields,
    validateAccessForUser: validateAccessForUser
};
