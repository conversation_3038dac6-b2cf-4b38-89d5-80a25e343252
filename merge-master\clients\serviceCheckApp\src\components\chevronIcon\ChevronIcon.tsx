import { faChevronDown, faChevronUp } from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'

interface ChevronIconProps {
    isOpen: boolean
}

export const ChevronIcon = ({ isOpen }: ChevronIconProps) => {
    return (
        <>
            {!isOpen ? (
                <FontAwesomeIcon icon={faChevronDown} />
            ) : (
                <FontAwesomeIcon icon={faChevronUp} />
            )}
        </>
    )
}
