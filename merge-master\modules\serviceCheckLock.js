
import ServiceCheckModel from '../db/model/serviceCheck.js';
import { ServiceCheckLockError } from './error.js';
import logger from './logger.js';


export async function acquireLock(serviceCheckRecord) {
    const id = serviceCheckRecord.id;

    if (serviceCheckRecord.$isNew) {
        await serviceCheckRecord.save();
    }

    serviceCheckRecord = await ServiceCheckModel.findOneAndUpdate({ id: id }, {
        $set: {
            locked: true,
            lockedDatetime: new Date(),
            lockedDuration: 600000
        }
    },
    {
        new: false
    }).select({
        locked: 1,
        lockedDatetime: 1,
        lockedDuration: 1
    });

    if (serviceCheckRecord.locked &&
        serviceCheckRecord.lockedDatetime instanceof Date &&
        serviceCheckRecord.lockedDatetime.getTime() + serviceCheckRecord.lockedDuration > new Date().getTime()) {
        throw new ServiceCheckLockError(`Service check ${id} is currently locked at ${serviceCheckRecord.lockedDatetime.toLocaleString()}`);
    }
}


export async function releaseLock(serviceCheckRecord) {
    const id = serviceCheckRecord.id;

    try {
        await ServiceCheckModel.updateOne({ id: id }, {
            locked: false,
            lockedDatetime: null,
            lockedDuration: null
        });
    } catch(error) {
        logger.warn(`Service check lock: failed to release lock for ${id}, ${error.toString()}`);
    }
}
