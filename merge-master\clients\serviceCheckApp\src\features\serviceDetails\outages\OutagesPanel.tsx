import classNames from 'classnames'
import { useMemo } from 'react'
import { useAppState } from '../../../hooks/useAppState'

import { DetailsPanel } from '../../../components/detailsPanel/DetailsPanel'
import { GreenCheckIcon } from '../../../components/statusIcon/GreenCheckIcon'
import { OrangeWarningIcon } from '../../../components/statusIcon/OrangeWarningIcon'
import { PlannedOutagesField } from './fields/PlannedOutagesField'
import { PowerOutagesField } from './fields/PowerOutagesField'
import { UnplannedOutagesField } from './fields/UnplannedOutagesField'
import { NBNOutagesField } from './fields/NBNOutagesField'


export const OutagesPanel = ({ className }: { className?: string }) => {
    const { appState } = useAppState()
    const outages = appState.serviceDetails?.outages || {}
    const hasOutages = useMemo(() => {
        return (
            (outages.plannedOutages?.length ?? 0) > 0 ||
            (outages.unplannedOutages?.length ?? 0) > 0 ||
            (outages.powerOutages?.length ?? 0) > 0 ||
            (outages.nbnOutages?.length ?? 0)
        )
    }, [outages])

    return (
        <DetailsPanel
            label="Outages"
            className={classNames(className)}
            panelElements={
                hasOutages ? <OrangeWarningIcon /> : <GreenCheckIcon />
            }
        >
            <>
                <PlannedOutagesField value={outages.plannedOutages ?? []} />
                <UnplannedOutagesField value={outages.unplannedOutages ?? []} />
                <PowerOutagesField value={outages.powerOutages ?? []} />
                <NBNOutagesField value={outages.nbnOutages ?? []} />
            </>
        </DetailsPanel>
    )
}
