{"version": 174, "updatedBy": "d387642", "updatedOn": "2025-05-28T17:00:00.000Z", "apis": [{"name": "AlwaysOn", "active": true, "description": "Get service information from Always On", "apiType": "rest", "method": "get", "parameters": ["fnn"], "baseUrl": "config.HAPIURI", "uri": "'rAlwaysOn'", "queryParams": "new Object({\n    fnn: parameters.fnn\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": ""}, {"name": "B2B", "active": true, "description": "B2B data from Network Service OKAPI", "apiType": "rest", "method": "get", "parameters": ["fnn"], "baseUrl": "config.B2BAPI.tasksURI", "uri": "`networkservice`", "queryParams": "new Object({\n    serviceNumber: parameters.fnn\n});", "body": "", "header": "`{\"Source-System\": \"${config.B2BSourceSystem}\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "B2BAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "B2B"}, {"name": "B2BS", "active": true, "description": "B2B data from Service OKAPI", "apiType": "rest", "method": "get", "parameters": ["fnn"], "baseUrl": "config.B2BAPI.tasksURI", "uri": "`service`", "queryParams": "new Object({\n    serviceNumber: parameters.fnn\n});", "body": "", "header": "`{\"Source-System\": \"${config.B2BSourceSystem}\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "B2BAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "B2B"}, {"name": "BandF", "active": true, "description": "Virtual Operator API for Bruno and Fluffy API", "apiType": "rest", "method": "post", "parameters": ["fnn", "id"], "baseUrl": "config.BandF.baseURI", "uri": "", "queryParams": "", "body": "JSON.stringify({\n    number: parameters.fnn,\n    id: parameters.id,\n    result: {\n        vo_task: 'bruno-merge'\n    },\n    search_name: 'bruno-merge',\n    callbackEnv: config.BandF && config.BandF.callbackEnv\n});", "header": "JSON.stringify({\n    'Content-Type': 'application/json'\n});", "authKeyDb": [], "timeout": 30000, "parseResponse": "", "pollCondition": "parameters.id;", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": {"timeout": 180000, "enabledEnvs": ["dev", "testing", "stage", "prd"], "idField": "response && response.data && response.data.id ? response.data.id : null;", "parseResponse": "", "doneCondition": "response.data;", "errorCondition": ""}, "masslCertificateName": null, "wikiPage": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "BasementPortReset", "active": true, "description": "Reset basement switch port via API in hangar", "apiType": "rest", "method": "get", "parameters": ["FNN"], "baseUrl": "config.HAPIURI", "uri": "\"rBasementSwitchRestart\"", "queryParams": "new Object({\n    fnn: parameters.FNN\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 60000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "BasementSwitchPortReset"}, {"name": "BATIPV4", "active": true, "description": "Broadband Assurance Tool Session by IPv4 Address", "apiType": "rest", "method": "get", "parameters": ["BATipv4"], "baseUrl": "config.HAPIURI", "uri": "\"rBAT\"", "queryParams": "new Object({\n    ip: parameters.BATipv4,\n    type: \"ipv4\"\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "BAT"}, {"name": "BATIPV6", "active": true, "description": "Broadband Assurance Tool Session by IPv6 Address", "apiType": "rest", "method": "get", "parameters": ["BATipv6"], "baseUrl": "config.HAPIURI", "uri": "\"rBAT\"", "queryParams": "new Object({\n    ip: parameters.BATipv6,\n    type: \"ipv6\"\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "BAT"}, {"name": "BATMODEM", "active": true, "description": "Broadband Assurance Tool Session by MODEM", "apiType": "rest", "method": "get", "parameters": ["BATserial", "BATmodel"], "baseUrl": "config.HAPIURI", "uri": "\"rBAT\"", "queryParams": "new Object({\n    serial: parameters.BATserial,\n    model: parameters.BATmodel,\n    type: \"modemMerge\"\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "BAT"}, {"name": "BATNBN", "active": true, "description": "Broadband Assurance Tool Session by NBN", "apiType": "rest", "method": "get", "parameters": ["BATavc"], "baseUrl": "config.HAPIURI", "uri": "\"rBAT\"", "queryParams": "new Object({\n    avc: parameters.BATavc,\n    type: \"nbn\"\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "BAT"}, {"name": "BATNEMDMS", "active": true, "description": "Broadband Assurance Tool Session by NEMDMS", "apiType": "rest", "method": "get", "parameters": ["BATinput"], "baseUrl": "config.HAPIURI", "uri": "\"rBAT\"", "queryParams": "new Object({\n    input: parameters.BATinput,\n    type: \"nemdms\"\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "BAT"}, {"name": "BATSID", "active": true, "description": "Broadband Assurance Tool Session by Session ID", "apiType": "rest", "method": "get", "parameters": ["BATsid"], "baseUrl": "config.HAPIURI", "uri": "\"rBAT\"", "queryParams": "new Object({\n    sid: parameters.BATsid,\n    type: \"sid\"\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "BAT"}, {"name": "BATTAAA", "active": true, "description": "Broadband Assurance Tool Session by TAAA", "apiType": "rest", "method": "get", "parameters": ["BATuid"], "baseUrl": "config.HAPIURI", "uri": "\"rBAT\"", "queryParams": "new Object({\n    uid: parameters.BATuid,\n    type: \"taaa\"\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "BAT"}, {"name": "BSIPC", "active": true, "description": "BSIP customer data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPCC", "active": true, "description": "BSIP customer CDR data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}/cdr`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPCS", "active": true, "description": "BSIP customer site data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}/sites`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPCSA1", "active": true, "description": "BSIP customer site access data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid", "sid"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}/sites/${parameters.sid}/access`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPCSA2", "active": true, "description": "BSIP customer site activation data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid", "sid"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}/sites/${parameters.sid}/activation`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPCSA3", "active": true, "description": "BSIP customer site admins data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid", "sid"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}/sites/${parameters.sid}/admins`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPCSA4", "active": true, "description": "BSIP customer site analogue data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid", "sid"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}/sites/${parameters.sid}/analogue`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPCSAU", "active": true, "description": "BSIP customer site analogue users data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid", "sid"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}/sites/${parameters.sid}/analogueUsers`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPCSBC", "active": true, "description": "BSIP customer site business continuity data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid", "sid"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}/sites/${parameters.sid}/businessContinuity`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPCSCB", "active": true, "description": "BSIP customer site call barring data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid", "sid"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}/sites/${parameters.sid}/callBarring`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPCSD", "active": true, "description": "BSIP customer site devices data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid", "sid"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}/sites/${parameters.sid}/devices`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPCSE", "active": true, "description": "BSIP customer site extensions data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid", "sid"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}/sites/${parameters.sid}/extensions`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPCSFP", "active": true, "description": "BSIP customer site feature packs data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid", "sid"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}/sites/${parameters.sid}/featurePacks`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPCSHG", "active": true, "description": "BSIP customer site hunt groups data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid", "sid"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}/sites/${parameters.sid}/huntGroups`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPCSN", "active": true, "description": "BSIP customer site numbers data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid", "sid"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}/sites/${parameters.sid}/numbers`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPCSS", "active": true, "description": "BSIP customer site services data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid", "sid"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}/sites/${parameters.sid}/services`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPCSSS", "active": true, "description": "BSIP customer site site-services data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid", "sid"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}/sites/${parameters.sid}/siteServices`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPCST", "active": true, "description": "BSIP customer site trunk data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid", "sid"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}/sites/${parameters.sid}/trunk`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPCSTC1", "active": true, "description": "BSIP customer site trunk contact from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid", "sid"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}/sites/${parameters.sid}/trunk/contact`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPCSTC2", "active": true, "description": "BSIP customer site trunk credentials from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid", "sid"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}/sites/${parameters.sid}/trunk/credentials`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPCSTF", "active": true, "description": "BSIP customer site trunk features from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid", "sid"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}/sites/${parameters.sid}/trunk/features`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPCSTP", "active": true, "description": "BSIP customer site trunk partition data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid", "sid"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}/sites/${parameters.sid}/trunk/partitions`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPCSTRD", "active": true, "description": "BSIP customer site trunk reporting data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid", "sid", "ayear", "amonth", "getmetrics"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}/sites/${parameters.sid}/trunk/reporting/data`", "queryParams": "new Object({\n    year: parameters.ayear,\n    month: parameters.amonth,\n    metrics: parameters.getmetrics\n});", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPCSTRM", "active": true, "description": "BSIP customer site trunk reporting metrics data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid", "sid"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}/sites/${parameters.sid}/trunk/reporting/metrics`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPCSTU", "active": true, "description": "BSIP customer site trunk users data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid", "sid"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}/sites/${parameters.sid}/trunkUsers`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPCSU", "active": true, "description": "BSIP customer site users data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid", "sid"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}/sites/${parameters.sid}/users`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPCSVR", "active": true, "description": "BSIP customer site virtual receptionists data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["cid", "sid"], "baseUrl": "config.BSIPAPI.customerURI", "uri": "`${parameters.cid}/sites/${parameters.sid}/virtualReceptionists`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPS", "active": true, "description": "BSIP service provider data from OKAPI", "apiType": "rest", "method": "get", "parameters": [], "baseUrl": "config.BSIPAPI.serviceURI", "uri": "", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPU", "active": true, "description": "BSIP user data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["fnn"], "baseUrl": "config.BSIPAPI.userURI", "uri": "`${parameters.fnn}`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPUCF", "active": true, "description": "BSIP user calling features from OKAPI", "apiType": "rest", "method": "get", "parameters": ["fnn"], "baseUrl": "config.BSIPAPI.userURI", "uri": "`${parameters.fnn}/callingFeatures`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPUCFA", "active": true, "description": "BSIP user call forwarding always data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["fnn"], "baseUrl": "config.BSIPAPI.userURI", "uri": "`${parameters.fnn}/CallForwardingAlways`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPUCFB", "active": true, "description": "BSIP user call forwarding busy data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["fnn"], "baseUrl": "config.BSIPAPI.userURI", "uri": "`${parameters.fnn}/CallForwardingBusy`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPUCFNA", "active": true, "description": "BSIP user call forwarding no answering data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["fnn"], "baseUrl": "config.BSIPAPI.userURI", "uri": "`${parameters.fnn}/CallForwardingNoAnswer`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPUCFNR", "active": true, "description": "BSIP user call forwarding not reachable data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["fnn"], "baseUrl": "config.BSIPAPI.userURI", "uri": "`${parameters.fnn}/CallForwardingNoReachable`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPUCLDB", "active": true, "description": "BSIP user calling line delivery blocking data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["fnn"], "baseUrl": "config.BSIPAPI.userURI", "uri": "`${parameters.fnn}/CallingLineIDDeliveryBlocking`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPUCW", "active": true, "description": "BSIP user call waiting data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["fnn"], "baseUrl": "config.BSIPAPI.userURI", "uri": "`${parameters.fnn}/CallWaiting`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPURO", "active": true, "description": "BSIP user remote office data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["fnn"], "baseUrl": "config.BSIPAPI.userURI", "uri": "`${parameters.fnn}/RemoteOffice`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPUSRP", "active": true, "description": "BSIP user simultaneous ring personal data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["fnn"], "baseUrl": "config.BSIPAPI.userURI", "uri": "`${parameters.fnn}/SimultaneousRingPersonal`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "BSIPUVM", "active": true, "description": "BSIP user voice mail data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["fnn"], "baseUrl": "config.BSIPAPI.userURI", "uri": "`${parameters.fnn}/RemoteOffice`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "BSIPAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "BSIP"}, {"name": "CDRLive", "active": true, "description": "CDR Live data last record", "apiType": "rest", "method": "post", "parameters": ["TYPE", "IDENTIFIER", "PERIODTO", "PERIODFROM"], "baseUrl": "config.CDRLive.bdliveApiURI", "uri": "\"reports/execute\"", "queryParams": "", "body": "`{\"ReportCode\": \"PUMA-${parameters.TYPE}-SRCH-L\",\"ReportParams\":[{\"Name\": \"${parameters.TYPE}\",\"Value\": [ \"${parameters.IDENTIFIER}\" ]},{\"Name\": \"PERIOD\",\"Value\": [ \"${parameters.PERIODFROM}\", \"${parameters.PERIODTO}\" ]},{\"Name\": \"CALLOWN\",\"Value\": [ \"OWN\" ]},{\"Name\": \"EXCLCALLTYPE\",\"Value\": [ \"Include All\" ]}]}`", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "CDRLive"}], "timeout": 300000, "parseResponse": "typeof response.data === 'string' ? JSON.parse(response.data) : response.data", "pollCondition": "!(response.data.Data.RunId)", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": {"name": "CDRLive", "interval": 15000, "uri": "`tabs/status/${response.data.Data.RunId}`", "queryParams": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [], "timeout": 300000, "parseResponse": "typeof response.data === 'string' ? JSON.parse(response.data) : response.data", "doneCondition": "response.data.Data.Status!='Running'", "errorCondition": "!response.data.Data.RunId && response.data.Messages[0] != 'Too many sessions - 30 currently in use. Please wait and request again later.'", "transform": null, "resultAPI": {"name": "CDRLive", "timeout": 60000, "uri": "`tabs/output/${response.data.Data.RunId}`", "queryParams": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [], "parseResponse": "typeof response.data === 'string' ? JSON.parse(response.data) : response.data", "transform": null}}, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "CDRLive"}, {"name": "CDRPurgeAll", "active": true, "description": "To free All API connections by purging all CDRLive reports", "apiType": "rest", "method": "get", "parameters": [], "baseUrl": "config.CDRLive.purgeAllURI", "uri": "", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "CDRLive"}], "timeout": 300000, "parseResponse": "typeof response.data === 'string' ? JSON.parse(response.data) : response.data", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "CDRLive"}, {"name": "CDRPurgeOne", "active": true, "description": "To free a particular API connection by purging it using RunId", "apiType": "rest", "method": "get", "parameters": ["RunId"], "baseUrl": "config.CDRLive.purgeOneURI", "uri": "`${parameters.RunId}`", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "CDRLive"}], "timeout": 300000, "parseResponse": "typeof response.data === 'string' ? JSON.parse(response.data) : response.data", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "CDRLive"}, {"name": "CDRQueue", "active": true, "description": "To get a list of previously executed CDRLive report(s)", "apiType": "rest", "method": "get", "parameters": [], "baseUrl": "config.CDRLive.statusURI", "uri": "\"0\"", "queryParams": "", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "CDRLive"}], "timeout": 300000, "parseResponse": "typeof response.data === 'string' ? JSON.parse(response.data) : response.data", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "CDRLive"}, {"name": "CFNN", "active": true, "description": "To check where the number exists in the system , if the number is registered and is the number reachable", "apiType": "rest", "method": "get", "parameters": ["IDENTIFIER"], "baseUrl": "config.HAPIURI", "uri": "\"rCFNN\"", "queryParams": "new Object({\n    fnn: parameters.IDENTIFIER\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 40000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "CFNN"}, {"name": "CFNNRegisteredNumberRange", "active": true, "description": "CFNN - Get registered phone number in a range", "apiType": "rest", "method": "get", "parameters": ["phoneNumbers"], "baseUrl": "config.MergeExternalCollectorAPI.baseURI", "uri": "'cfnn/get-registered-number-in-range'", "queryParams": "new Object({\n    phoneNumbers: parameters.phoneNumbers\n});", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.mergeExternalCollectorUsername}:${config.mergeExternalCollectorPassword}`)}`\n});", "authKeyDb": [], "timeout": 120000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "CFNNRegisteredNumberRange"}, {"name": "CIDNCustomerConsentExclusion", "active": true, "description": "Merge external collector - reads Hangar database to determine if any CIDN in a list of CIDNs should be displayed if the customer consent is conditional", "apiType": "rest", "method": "get", "parameters": ["cidn"], "baseUrl": "config.MergeExternalCollectorAPI.baseURI", "uri": "'cidn/check-exclusion-for-customer-consent'", "queryParams": "new Object({\n    cidn: parameters.cidn\n});", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.mergeExternalCollectorUsername}:${config.mergeExternalCollectorPassword}`)}`\n});", "authKeyDb": [], "timeout": 60000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": ""}, {"name": "CiscoIOTAD", "active": true, "description": "To get Cisco IOT account details information", "apiType": "rest", "method": "get", "parameters": ["AccountID"], "baseUrl": "config.CiscoIOT.diagURI", "uri": "`/accounts/${parameters.AccountID}`", "queryParams": "", "body": "", "header": "`{\"Authorization\": \"Basic ${config.CiscoIOT.apiToken}\", \"accept\": \"application/json\"}`", "authKeyDb": [], "timeout": 40000, "parseResponse": "", "pollCondition": "", "proxyRequired": true, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "CiscoIOT"}, {"name": "CiscoIOTDD", "active": true, "description": "To get Cisco IOT device details information", "apiType": "rest", "method": "get", "parameters": ["ICCID"], "baseUrl": "config.CiscoIOT.diagURI", "uri": "`devices/${parameters.ICCID}`", "queryParams": "", "body": "", "header": "`{\"Authorization\": \"Basic ${config.CiscoIOT.apiToken}\", \"accept\": \"application/json\"}`", "authKeyDb": [], "timeout": 60000, "parseResponse": "", "pollCondition": "", "proxyRequired": true, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "CiscoIOT"}, {"name": "CiscoIOTDU", "active": true, "description": "To get Cisco IOT device usage", "apiType": "rest", "method": "get", "parameters": ["ICCID"], "baseUrl": "config.CiscoIOT.diagURI", "uri": "`devices/${parameters.ICCID}/ctdUsages`", "queryParams": "", "body": "", "header": "`{\"Authorization\": \"Basic ${config.CiscoIOT.apiToken}\", \"accept\": \"application/json\"}`", "authKeyDb": [], "timeout": 60000, "parseResponse": "", "pollCondition": "", "proxyRequired": true, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "CiscoIOT"}, {"name": "CiscoIOTSD", "active": true, "description": "To get Cisco IOT session details", "apiType": "rest", "method": "get", "parameters": ["ICCID"], "baseUrl": "config.CiscoIOT.diagURI", "uri": "`devices/${parameters.ICCID}/sessionInfo`", "queryParams": "", "body": "", "header": "`{\"Authorization\": \"Basic ${config.CiscoIOT.apiToken}\", \"accept\": \"application/json\"}`", "authKeyDb": [], "timeout": 60000, "parseResponse": "", "pollCondition": "", "proxyRequired": true, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "CiscoIOT"}, {"name": "ClusterLocator", "active": false, "description": "Locate Cluster from https://cluster.see.in.telstra.com.au/", "apiType": "rest", "method": "post", "parameters": ["CLTerm"], "baseUrl": "config.ClusterLocator.searchURI", "uri": "`${parameters.CLTerm}`", "queryParams": "", "body": "'{}'", "header": "`{}`", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "ClusterLocator"}, {"name": "CMART", "active": true, "description": "Fetch CMART data", "apiType": "rest", "method": "get", "parameters": ["CMARTfnn"], "baseUrl": "config.HAPIURI", "uri": "\"rCMART\"", "queryParams": "new Object({\n    fnn: parameters.CMARTfnn\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": ""}, {"name": "CMIBNA", "active": true, "description": "CMI IOM Device Health", "apiType": "rest", "method": "get", "parameters": ["IOMDevice"], "baseUrl": "config.HAPIURI", "uri": "\"rCMIBNA.php\"", "queryParams": "new Object({\n    device: parameters.IOMDevice,\n    commands: \"iosDeviceHealth\"\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "CMIBNA"}, {"name": "CMIn", "active": true, "description": "Get all info and commands", "apiType": "rest", "method": "get", "parameters": ["CMIdevice", "CMIinterfaces", "CMIcommands"], "baseUrl": "config.HAPIURI", "uri": "\"rCMIn\"", "queryParams": "let queryParameters = new Object({\n    device: parameters.CMIdevice,\n    commands: parameters.CMIcommands\n});\nif (parameters.CMIinterfaces) {\n    queryParameters.interfaces = parameters.CMIinterfaces;\n}\nqueryParameters;", "body": "", "header": "", "authKeyDb": [], "timeout": 400000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined' && response.data.error != 'Cannot Login to the device!!'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "CMI"}, {"name": "CNSDI", "active": true, "description": "To check where the number active in CNSDI and compare with Magpie details", "apiType": "rest", "method": "get", "parameters": ["id", "type"], "baseUrl": "config.MergeExternalCollectorAPI.baseURI", "uri": "`cnsdi/get-records`", "queryParams": "new Object({\n    id: parameters.id,\n    type: parameters.type\n});", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.mergeExternalCollectorUsername}:${config.mergeExternalCollectorPassword}`)}`\n});", "authKeyDb": [], "timeout": 180000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "CNSDI"}, {"name": "COMMPilot", "active": true, "description": "To check where the number exists in the system , if the number is registered and is the number reachable", "apiType": "rest", "method": "post", "parameters": ["IDENTIFIER"], "baseUrl": "config.HAPIURI", "uri": "`rCOMMPilot`", "queryParams": "", "body": "`{ \"request\": {\"type\": \"basic\",  \"fnn\": \"${parameters.IDENTIFIER}\", \"token\": \"${config.COMMPILOT.apiToken}\" } }`", "header": "`{\"content-type\":\"application/json\"}`", "authKeyDb": [], "timeout": 40000, "parseResponse": "if (response && response.data && response.data.response && response.data.response.data) { response.data.response.data } else { response.data }", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "COMMPilot"}, {"name": "COMMPilotUpdate", "active": true, "description": "To update incoming call control settings", "apiType": "rest", "method": "post", "parameters": ["IDENTIFIER", "Arg1", "Arg2", "Arg3"], "baseUrl": "config.HAPIURI", "uri": "`rCOMMPilot`", "queryParams": "", "body": "`{ \"request\": {\"type\": \"basic\", \"arg1\": \"${parameters.Arg1}\", \"arg2\": \"${parameters.Arg2}\", \"arg3\": \"${parameters.Arg3}\", \"fnn\": \"${parameters.IDENTIFIER}\", \"token\": \"${config.COMMPILOT.apiToken}\" } }`", "header": "`{\"content-type\":\"application/json\"}`", "authKeyDb": [], "timeout": 40000, "parseResponse": "if (response && response.data && response.data.response && response.data.response.data) { response.data.response.data } else { response.data }", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "COMMPilot"}, {"name": "CONEN", "active": true, "description": "Read CONEN BlackHawk Database", "apiType": "rest", "method": "get", "parameters": ["conenTicketID", "<PERSON>n<PERSON><PERSON><PERSON><PERSON>", "conenFacilityTerm", "conenLocationTerm", "conenHistoryOption"], "baseUrl": "config.HAPIURI", "uri": "\"rCONEN\"", "queryParams": "new Object({\n    ticketID: parameters.conenTicketID,\n    facilityTerm: parameters.conenFacilityTerm,\n    locationTerm: parameters.conenLocationTerm,\n    commands: parameters.conenCommands,\n    historyOption: parameters.conenHistoryOption\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "<PERSON><PERSON>"}, {"name": "CONENn", "active": true, "description": "Read CONEN BlackHawk Database", "apiType": "rest", "method": "get", "parameters": ["<PERSON>n<PERSON><PERSON><PERSON><PERSON>", "conenFacilityTerm"], "baseUrl": "config.HAPIURI", "uri": "\"rCONEN\"", "queryParams": "new Object({\n    facilityTerm: parameters.conenFacilityTerm,\n    commands: parameters.conenCommands,\n    open: true\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "<PERSON><PERSON>"}, {"name": "CongestionBoard", "active": true, "description": "Check DSLAM in Congestion Board", "apiType": "rest", "method": "get", "parameters": ["CBDSLAM"], "baseUrl": "config.HAPIURI", "uri": "\"rOutageBoard\"", "queryParams": "new Object({\n    DSLAM: parameters.CBDSLAM\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": ""}, {"name": "CUSTALIGN", "active": true, "description": "Read CustAlign Customer Database", "apiType": "rest", "method": "get", "parameters": ["custalignCIDN", "custalignCommands"], "baseUrl": "config.HAPIURI", "uri": "\"rCUSTALIGN\"", "queryParams": "new Object({\n    CIDN: parameters.custalignCIDN,\n    commands: parameters.custalignCommands\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "CustAlign"}, {"name": "CustomerConsent", "active": true, "description": "Customer Consent data sourced from https://teamtelstra.sharepoint.com/:x:/r/sites/ContractManagementServices/_layouts/15/doc2.aspx?sourcedoc=%7B29eef655-b2da-491a-a13a-e5d8555ac5b3%7D&action=view&activeCell=%27Customer%20List%27!A282&wdrcid=7bbb705f-866c-4da0-9b53-ab5ebc27460a&wdrldc=1", "apiType": "rest", "method": "get", "parameters": ["UCIDN", "CIDN"], "baseUrl": "config.HAPIURI", "uri": "\"rCustomerConsent\"", "queryParams": "new Object({\n    CIDN: parameters.CIDN,\n    limit: 1\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "ContractManagementServices"}, {"name": "DNS", "active": false, "description": "DNS device from ovmsn1 or ovmcv1 servers", "apiType": "rest", "method": "get", "parameters": ["dnsDevice", "dnsCommands"], "baseUrl": "config.HAPIURI", "uri": "\"rDNS\"", "queryParams": "new Object({\n    device: parameters.dnsDevice,\n    commands: parameters.dnsCommands\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "IMSNetJumpBox"}, {"name": "eDCP", "active": true, "description": "To search the eDCP", "apiType": "rest", "method": "post", "parameters": ["INPUT", "TYPE", "RANGE", "TRACE", "STARTDATE", "ENDDATE"], "baseUrl": "config.HAPIURI", "uri": "\"rEDCP\"", "queryParams": "", "body": "`{ \"request\": { \"username\": \"${config.robotAccount01Username}\", \"password\": \"${config.robotAccount01eDCPPassword}\", \"input\": \"${parameters.INPUT}\", \"type\": \"${parameters.TYPE}\", \"range\": \"${parameters.RANGE}\", \"trace\": \"${parameters.TRACE}\", \"startdate\": \"${parameters.STARTDATE}\", \"enddate\": \"${parameters.ENDDATE}\" } }`", "header": "`{\"content-type\":\"application/json\"}`", "authKeyDb": [], "timeout": 40000, "parseResponse": "if (response && response.data && response.data.response) { response.data.response } else { response.data }", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "eDCP"}, {"name": "FetchCMART", "active": true, "description": "Fetch CMART information from the given mart ID", "apiType": "rest", "method": "get", "parameters": ["ticketID"], "baseUrl": "config.HAPIURI", "uri": "\"rCMART\"", "queryParams": "new Object({\n    ticketID: parameters.ticketID\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "Fetch CMART"}, {"name": "Flexcab", "active": true, "description": "Fetch Flexcab information from the static file in MEC", "apiType": "rest", "method": "get", "parameters": ["fnn"], "baseUrl": "config.MergeExternalCollectorAPI.baseURI", "uri": "'flexcab'", "queryParams": "new Object({\n    fnn: parameters.fnn\n});", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.mergeExternalCollectorUsername}:${config.mergeExternalCollectorPassword}`)}`\n});", "authKeyDb": [], "timeout": 60000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "flexcab"}, {"name": "Geolite", "active": true, "description": "Geolite city plus service", "apiType": "rest", "method": "get", "parameters": ["ip<PERSON><PERSON><PERSON>"], "baseUrl": "config.GeoliteAPI.cityURL", "uri": "`${parameters.ipAddress}`", "queryParams": "", "body": "", "header": "`{ \"Authorization\": \"Basic ${base64(`${config.GeoliteUsername}:${config.GeolitePassword}`)}\" }`", "authKeyDb": [], "timeout": 60000, "parseResponse": "", "pollCondition": "", "proxyRequired": true, "useCookies": false, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "Geolite"}, {"name": "GMACS", "active": true, "description": "Do the BDSL test by using GMACS tool", "apiType": "rest", "method": "get", "parameters": ["GMACSfnn"], "baseUrl": "config.HAPIURI", "uri": "\"rGMACS\"", "queryParams": "new Object({\n    FNN: parameters.GMACSfnn\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "GMACS"}, {"name": "INCIDENTS", "active": true, "description": "Live Incidents", "apiType": "rest", "method": "get", "parameters": ["FIRSTCGI"], "baseUrl": "config.HAPIURI", "uri": "\"rIncidents\"", "queryParams": "new Object({\n    fCGI: parameters.FIRSTCGI\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": ""}, {"name": "InternetDirectCustomerAccounts", "active": true, "description": "Internet Direct OKAPI (WAS API) - Customer accounts endpoint", "apiType": "rest", "method": "get", "parameters": ["accountNumber"], "baseUrl": "config.InternetDirect.customerAccountsURI", "uri": "`${parameters.accountNumber}`;", "queryParams": "", "body": "", "header": "", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "InternetDirect"}], "timeout": 120000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "InternetDirectCustomerAccounts"}, {"name": "InternetDirectProductOrderDetails", "active": true, "description": "Internet Direct OKAPI (WAS API) - Product order details endpoint", "apiType": "rest", "method": "get", "parameters": ["id"], "baseUrl": "config.InternetDirect.productOrdersURI", "uri": "`${parameters.id}`;", "queryParams": "", "body": "", "header": "", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "InternetDirect"}], "timeout": 120000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "InternetDirectProductOrderDetails"}, {"name": "InternetDirectProductOrders", "active": true, "description": "Internet Direct OKAPI (WAS API) - Product orders endpoint", "apiType": "rest", "method": "get", "parameters": ["fnn"], "baseUrl": "config.InternetDirect.productOrdersURI", "uri": "", "queryParams": "new Object({\n    serviceID: parameters.fnn\n});", "body": "", "header": "", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "InternetDirect"}], "timeout": 120000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "InternetDirectProductOrders"}, {"name": "IPMANVirtualOperator", "active": true, "description": "Virtual Operator API for IPMAN data", "apiType": "rest", "method": "post", "parameters": ["id", "magpiePayload"], "baseUrl": "config.IPMANVirtualOperator.baseURI", "uri": "", "queryParams": "", "body": "JSON.stringify({\n    search_name: \"IPMAN_Data_MERGE3\",\n    payLoad: parameters.magpiePayload,\n    id: parameters.id,\n    callbackEnv: config.IPMANVirtualOperator && config.IPMANVirtualOperator.callbackEnv\n});", "header": "JSON.stringify({\n    \"Content-Type\": \"application/json\"\n});", "authKeyDb": [], "timeout": 180000, "parseResponse": "", "pollCondition": "parameters.id;", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": {"timeout": 60000, "enabledEnvs": ["dev", "testing", "stage", "prd"], "idField": "response && response.data && response.data.id ? response.data.id : null;", "parseResponse": "", "doneCondition": "response.data;", "errorCondition": ""}, "masslCertificateName": null, "wikiPage": "IPMANVirtualOperator"}, {"name": "IPWorks", "active": true, "description": "IPWorks Enum Scheduled List", "apiType": "rest", "method": "post", "parameters": ["ms_isdn"], "baseUrl": "config.IPWorks.diagURI", "uri": "", "queryParams": "", "body": "`{ \"msisdn\": \"${parameters.ms_isdn}\", \"rangetest\": true }`", "header": "`{\"Authorization\": \"Basic ${base64(`${config.IPWorksUsername}:${config.IPWorksPassword}`)}\", \"Content-Type\": \"application/json\"}`", "authKeyDb": [], "timeout": 180000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": "TLSv1", "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "IPWorks"}, {"name": "MAGPIE", "active": true, "description": "", "apiType": "rest", "method": "get", "parameters": ["MAGPIEfnn", "NOCACHE"], "baseUrl": "config.HAPIURI", "uri": "\"rMAGPIE\"", "queryParams": "new Object({\n    FNN: parameters.MAGPIEfnn,\n    NOCACHE: \"Y\"\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "<PERSON><PERSON><PERSON>"}, {"name": "MagpieAPIDiagnosticAvailableTests", "active": true, "description": "Magpie API - Get a list of available diagnostic tests for a service", "apiType": "rest", "method": "get", "parameters": ["cidn", "fnn"], "baseUrl": "config.MagpieAPI.diagnosticAvailableTestsURI", "uri": "`${parameters.fnn}`", "queryParams": "new Object({\n    cidns: parameters.cidn,\n    consumer: 'magpie'\n});", "body": "", "header": "", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "MagpieAPI"}], "timeout": 120000, "parseResponse": "", "pollCondition": "", "proxyRequired": true, "useCookies": false, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "MagpieAPIDiagnosticAvailableTests"}, {"name": "MagpieAPIDiagnosticRun", "active": true, "description": "Magpie API - Run MAGPIE diagnostic for a service", "apiType": "rest", "method": "post", "parameters": ["cidn", "diagnosticName", "fnn"], "baseUrl": "config.MagpieAPI.diagnosticStartURI", "uri": "", "queryParams": "", "body": "JSON.stringify({\n    cidns: parameters.cidn,\n    service: parameters.fnn,\n    diagnosticName: parameters.diagnosticName,\n    responseFormat: 'Summary',\n    schematicType: 'magpie'\n});", "header": "", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "MagpieAPI"}], "timeout": 120000, "parseResponse": "", "pollCondition": "", "proxyRequired": true, "useCookies": false, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": {"name": "MagpieAPIDiagnosticRunPoll", "interval": 10000, "uri": "`${response.data.diagnosticId}`", "queryParams": "", "header": "", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "MagpieAPI"}], "timeout": 180000, "parseResponse": "", "doneCondition": "response?.data?.diagnosticStatus?.status === 'Completed';", "errorCondition": "!response", "transform": null, "resultAPI": null}, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "MagpieAPIDiagnosticRun"}, {"name": "MagpieAPIMDNSchematics", "active": true, "description": "Magpie API - Get Schematics Diagram for a service", "apiType": "rest", "method": "get", "parameters": ["fnn"], "baseUrl": "config.MagpieAPI.mdnSchematicsURI", "uri": "`${parameters.fnn}`", "queryParams": "", "body": "", "header": "", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "MagpieAPI"}], "timeout": 120000, "parseResponse": "", "pollCondition": "", "proxyRequired": true, "useCookies": false, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "MagpieAPISchematics"}, {"name": "MagpieAPISchematics", "active": true, "description": "Magpie API - Get Schematics Diagram for a service", "apiType": "rest", "method": "get", "parameters": ["cidn", "fnn"], "baseUrl": "config.MagpieAPI.schematicsURI", "uri": "`${parameters.fnn}`", "queryParams": "new Object({\n    cidns: parameters.cidn\n});", "body": "", "header": "", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "MagpieAPI"}], "timeout": 120000, "parseResponse": "", "pollCondition": "", "proxyRequired": true, "useCookies": false, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "MagpieAPISchematics"}, {"name": "MagpiePFCAPI", "active": true, "description": "Magpie API - Details need for RFO", "apiType": "rest", "method": "get", "parameters": ["FNN"], "baseUrl": "config.MagpieAPI.mergeOutageInfoURI", "uri": "`${parameters.FNN}`", "queryParams": "new Object({\n    test: 'prefaultAlarm'\n});", "body": "", "header": "", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "MagpieAPI"}], "timeout": 120000, "parseResponse": "", "pollCondition": "", "proxyRequired": true, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "MagpiePFCAPI"}, {"name": "MANAT", "active": true, "description": "Get Community Strings and Route IPs from MANAT", "apiType": "rest", "method": "get", "parameters": ["MANATdevice", "MANATcommands"], "baseUrl": "config.HAPIURI", "uri": "\"rMANAT\"", "queryParams": "new Object({\n    device: parameters.MANATdevice,\n    commands: parameters.MANATcommands\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "MANAT"}, {"name": "<PERSON><PERSON><PERSON>", "active": true, "description": "Meraki API", "apiType": "rest", "method": "get", "parameters": ["device", "commands"], "baseUrl": "config.HAPIURI", "uri": "\"rMERAKI\"", "queryParams": "new Object({\n    device: parameters.device,\n    commands: parameters.commands\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "<PERSON><PERSON><PERSON>"}, {"name": "MergeServiceCheckHistory", "active": true, "description": "Merge API Service Check History for a FNN", "apiType": "function", "method": null, "parameters": ["fnn"], "baseUrl": "getServiceCheckHistoryByFnn", "uri": "", "queryParams": "new Object({\n    limit: 2,\n    offset: 1,\n    last: '2m',\n    fnn: parameters.fnn,\n    suite: null\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 120000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "MergeServiceCheckHistory"}, {"name": "MODINI", "active": false, "description": "Modini API actual data being scraped from modini website", "apiType": "rest", "method": "get", "parameters": ["IDENTIFIER", "TYPE"], "baseUrl": "config.MODINI.apnURI", "uri": "", "queryParams": "new Object({\n    id: parameters.IDENTIFIER,\n    type: parameters.TYPE\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "<PERSON><PERSON><PERSON>"}, {"name": "MozartPSTNAccountStatusCheck", "active": true, "description": "Gets the account status for a PSTN / ISDN service", "apiType": "rest", "method": "get", "parameters": ["pstnFnn"], "baseUrl": "config.Mozart.pstnURI", "uri": "`account-status-check/${parameters.pstnFnn}`", "queryParams": "new Object({\n    retry: false\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": true, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "<PERSON>"}, {"name": "MozartPSTNDeviceTest", "active": true, "description": "Performs a device test on a PSTN / ISDN service", "apiType": "rest", "method": "get", "parameters": ["pstnFnn"], "baseUrl": "config.Mozart.pstnURI", "uri": "`device-test/${parameters.pstnFnn}`", "queryParams": "new Object({\n    retry: false \n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": true, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "<PERSON>"}, {"name": "MozartPSTNExchangeDeviceCheck", "active": true, "description": "Performs a exchange device check on a PSTN / ISDN service", "apiType": "rest", "method": "get", "parameters": ["pstnFnn"], "baseUrl": "config.Mozart.pstnURI", "uri": "`exchange-device-check/${parameters.pstnFnn}`", "queryParams": "new Object({\n    retry: false\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": true, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "<PERSON>"}, {"name": "MozartPSTNFacilityProgrammingCheck", "active": true, "description": "Performs a facility programming check on a PSTN / ISDN service", "apiType": "rest", "method": "get", "parameters": ["pstnFnn"], "baseUrl": "config.Mozart.pstnURI", "uri": "`facility-programming-check/${parameters.pstnFnn}`", "queryParams": "new Object({\n    retry: false\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": true, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "<PERSON>"}, {"name": "MozartPSTNPlannedUnplannedOutage", "active": true, "description": "Gets the outage status for a PSTN / ISDN service", "apiType": "rest", "method": "get", "parameters": ["pstnFnn"], "baseUrl": "config.Mozart.pstnURI", "uri": "`planned-unplanned-outage/${parameters.pstnFnn}`", "queryParams": "new Object({\n    retry: false\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": true, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "<PERSON>"}, {"name": "MozartPSTNSultanLineTest", "active": true, "description": "Performs a Sultan line test on a PSTN / ISDN service", "apiType": "rest", "method": "get", "parameters": ["pstnFnn"], "baseUrl": "config.Mozart.pstnURI", "uri": "`sultan-line-test/${parameters.pstnFnn}`", "queryParams": "new Object({\n    retry: false\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": true, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "<PERSON>"}, {"name": "NAASMobileAccess", "active": true, "description": "NAAS data from NaaS+ MobileAccess with ServiceDepth0", "apiType": "rest", "method": "get", "parameters": ["id"], "baseUrl": "config.NAASAPI.mobileAccessURI", "uri": "`${parameters.id}`", "queryParams": "", "body": "", "header": "`{\"X-Group-ID\": \"${config.NAASXGroupId}\", \"X-Correlation-ID\": \"${uuidv4()}\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "NAASAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "NAAS"}, {"name": "NAASMobileAccessSD1", "active": true, "description": "NAAS data from NaaS+ MobileAccess with ServiceDepth1", "apiType": "rest", "method": "get", "parameters": ["id"], "baseUrl": "config.NAASAPI.mobileAccessURI", "uri": "`${parameters.id}`", "queryParams": "new Object({\n    serviceDepth: 1\n})", "body": "", "header": "`{\"X-Group-ID\": \"${config.NAASXGroupId}\", \"X-Correlation-ID\": \"${uuidv4()}\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "NAASAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "NAAS"}, {"name": "NBNDiagnostics", "active": true, "description": "NBN service health API for a service", "apiType": "rest", "method": "post", "parameters": ["id", "type", "testSpecification", "testParameters"], "baseUrl": "config.NBNDiagnostics.diagnosticsURI", "uri": "", "queryParams": "", "body": "`{\"serviceRef\": { \"id\": \"${parameters.id}\", \"type\": \"${parameters.type}\" }, \"testSpecificationRef\": ${JSON.stringify(parameters.testSpecification)}, \"testParameters\": ${JSON.stringify(parameters.testParameters)} }`", "header": "`{\"Content-Type\": \"application/json\", \"NBN-TransactionID\": \"${uuidv4()}\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "NBNDiagnostics"}], "timeout": 60000, "parseResponse": "", "pollCondition": "response.data && response.data.status === \"Acknowledged\" && response.data.id ? response.data.id : null;", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": {"timeout": 180000, "enabledEnvs": ["testing", "prd"], "idField": "response && response.data && response.data.event && response.data.event.id ? response.data.event.id : null;", "parseResponse": "", "doneCondition": "response.data && response.data.event && response.data.event.status === \"Completed\";", "errorCondition": "response.data && response.data.event && [\"Cancelled\", \"Rejected\"].includes(response.data.event.status);"}, "masslCertificateName": "OKAPIClient", "wikiPage": "NBNDiagnostics"}, {"name": "NBNDIAGOKAPI", "active": true, "description": "NBN Diagnostics AVC and return result", "apiType": "rest", "method": "post", "parameters": ["DIAGNAME", "NBNAVC", "ServiceID", "ServiceType", "AccessType", "SubAccessType"], "baseUrl": "config.NBNOKAPI.diagURI", "uri": "", "queryParams": "", "body": "`{\"diagnosticName\":\"${parameters.DIAGNAME}\", \"requireCachedResults\": \"false\",\"services\":[{\"avc\":\"${parameters.NBNAVC}\", \"accessType\":\"${parameters.AccessType}\",\"subAccessType\":\"${parameters.SubAccessType}\",\"serviceType\": \"${parameters.ServiceType}\",\"serviceId\": \"${parameters.ServiceID}\"}] }`", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "NBNOKAPI"}], "timeout": 180000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data.serverError", "asyncPoll": {"name": "NBNDIAGOKAPI", "interval": 15000, "uri": "`${response.data.diagnosticId}`", "queryParams": "new Object({\n    testDetails: true\n});", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "NBNOKAPI"}], "timeout": 180000, "parseResponse": "", "doneCondition": "response.data.diagnosticStatus=='COMPLETED'", "errorCondition": "!response", "transform": {"testDetails": "response.data.testDetails.reduce((t,p)=>{ key = p.testName + ' ('+p.domain+')'; t[key]=p; return t},{})", "services": "response.data.services.reduce((t,p)=>{ key = p.serviceType; t[key]=p; return t},{})"}, "resultAPI": null}, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "NBNOKAPI"}, {"name": "NBNFilteredResults", "active": true, "description": "Filtered NBN results for SC in MEC", "apiType": "rest", "method": "get", "parameters": ["fnn"], "baseUrl": "config.MergeExternalCollectorAPI.baseURI", "uri": "'nbn-filtered'", "queryParams": "new Object({\n    fnn: parameters.fnn\n});", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.mergeExternalCollectorUsername}:${config.mergeExternalCollectorPassword}`)}`\n});", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "NBNFilteredResults"}, {"name": "NBNProductInventory", "active": true, "description": "NBN Product Inventory API", "apiType": "rest", "method": "get", "parameters": ["ovc"], "baseUrl": "config.NBNProductInventory.productsURI", "uri": "", "queryParams": "new Object({\n    \"$.[*].productRef[?(@.productSpecification.id=='OVC')].id\": parameters.ovc\n});", "body": "", "header": "`{\"NBN-TransactionID\": \"${uuidv4()}\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "NBNDiagnostics"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "NBNProductInventory"}, {"name": "NBNReset", "active": true, "description": "Reset NBN port via API in hangar", "apiType": "rest", "method": "get", "parameters": ["NBNFnn"], "baseUrl": "config.HAPIURI", "uri": "\"rNBN\"", "queryParams": "new Object({\n    fnn: parameters.NBNFnn,\n    commands: \"resetNB<PERSON>\"\n})", "body": "", "header": "", "authKeyDb": [], "timeout": 60000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "NBN Reset API"}, {"name": "NBNSearch", "active": true, "description": "NBN Search AVC and return product info", "apiType": "rest", "method": "post", "parameters": ["NBNTerm"], "baseUrl": "config.NBN.searchURI", "uri": "", "queryParams": "", "body": "`{ \"term\": \"${parameters.NBNTerm}\" }`", "header": "`{\"Authorization\": \"${config.NBNMaestroApiKey}\", \"Content-Type\": \"application/json\"}`", "authKeyDb": [], "timeout": 120000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "NBNPortal"}, {"name": "OATS", "active": true, "description": "", "apiType": "rest", "method": "get", "parameters": ["OATSfnn", "OATScommands"], "baseUrl": "config.HAPIURI", "uri": "\"rOATS\"", "queryParams": "new Object({\n    FNN: parameters.OATSfnn,\n    commands: parameters.OATScommands\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "OATS"}, {"name": "ODIN", "active": true, "description": "", "apiType": "rest", "method": "get", "parameters": ["ODINfnn"], "baseUrl": "config.HAPIURI", "uri": "\"rODIN\"", "queryParams": "new Object({\n    FNN: parameters.ODINfnn\n});\n", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "ODIN"}, {"name": "ODINDeviceDetails", "active": true, "description": "Obtains additional device details from ODIN", "apiType": "rest", "method": "get", "parameters": ["deviceId"], "baseUrl": "config.HAPIURI", "uri": "\"rODINDeviceDetails\"", "queryParams": "new Object({\n    MNEID: parameters.deviceId\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": ""}, {"name": "ODINDeviceName", "active": true, "description": "Obtain ODIN results via devicename", "apiType": "rest", "method": "get", "parameters": ["ODINdevice"], "baseUrl": "config.HAPIURI", "uri": "\"rODIN\"", "queryParams": "new Object({\n    DEVICE: parameters.ODINdevice\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "ODINDevice"}, {"name": "PING", "active": false, "description": "Ping device from ovmsn1 or ovmcv1 servers", "apiType": "rest", "method": "get", "parameters": ["pingDevice", "pingCommands"], "baseUrl": "config.HAPIURI", "uri": "\"rPing\"", "queryParams": "new Object({\n    device: parameters.pingDevice,\n    commands: parameters.pingCommands\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "IMSNetJumpBox"}, {"name": "PowerOutage", "active": true, "description": "Power outage info based on state and suburb from HAPI", "apiType": "rest", "method": "get", "parameters": ["suburb", "state"], "baseUrl": "config.HAPIURI", "uri": "\"rPowerOutage\"", "queryParams": "new Object({\n    locality: parameters.suburb,\n    state: parameters.state\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "PowerOutage"}, {"name": "Promise", "active": true, "description": "Promise task data from OKAPI", "apiType": "rest", "method": "get", "parameters": ["fnn"], "baseUrl": "config.PromiseAPI.tasksURI", "uri": "", "queryParams": "new Object({\n    fnn: parameters.fnn\n});", "body": "", "header": "`{\"UserId\": \"d289768\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "PromiseAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "Promise"}, {"name": "RASSP", "active": true, "description": "", "apiType": "rest", "method": "get", "parameters": ["RASSPfnn"], "baseUrl": "config.HAPIURI", "uri": "\"rRASS_PV3\"", "queryParams": "new Object({\n    FNN: parameters.RASSPfnn\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "RASSP"}, {"name": "RASSSCI", "active": true, "description": "", "apiType": "rest", "method": "get", "parameters": ["RASSSCIfnn"], "baseUrl": "config.HAPIURI", "uri": "\"rRASS_SCI\"", "queryParams": "new Object({\n    FNN: parameters.RASSSCIfnn\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "RASSSCI"}, {"name": "RDNBOSS", "active": true, "description": "", "apiType": "rest", "method": "get", "parameters": ["RDNBOSSfnn", "RDNBOSScommands"], "baseUrl": "config.HAPIURI", "uri": "\"rRDNBOSS\"", "queryParams": "new Object({\n    fnn: parameters.RDNBOSSfnn,\n    commands: parameters.RDNBOSScommands\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "RDNBOSS"}, {"name": "RFOAlarms", "active": true, "description": "Alarms and outages information sourced from Tomahawk API", "apiType": "rest", "method": "post", "parameters": ["linkID", "dateFrom", "dateTo", "sourcesData"], "baseUrl": "config.HAPIURI", "uri": "\"rTomahawkRFO\"", "queryParams": "", "body": "`{ \"linkID\": \"${parameters.linkID}\", \"dateFrom\": \"${parameters.dateFrom}\", \"dateTo\": \"${parameters.dateTo}\", \"QUERY\":\"RFO\", \"sourcesData\": \"${JSON.stringify(JSON.stringify(parameters.sourcesData)).slice(1,-1)}\", \"thawkURL\":\"${config.TomahawkURI.baseURI}\" }`", "header": "`{\"tomahawk-key\": \"${config.serviceTXAlarmsKey}\", \"content-type\":\"application/json\"}`", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "RFOAlarms"}, {"name": "Riverbed", "active": true, "description": "Riverbed data from Hangar API", "apiType": "rest", "method": "get", "parameters": ["device", "org"], "baseUrl": "config.HAPIURI", "uri": "\"rRiverbed\"", "queryParams": "new Object({\n    device: parameters.device,\n    org: parameters.org\n})", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "Riverbed"}, {"name": "RODS", "active": true, "description": "Rods data from hangar", "apiType": "rest", "method": "get", "parameters": ["cellName"], "baseUrl": "config.HAPIURI", "uri": "\"rRODS\"", "queryParams": "new Object({\n    cellName: parameters.cellName\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "RODS"}, {"name": "SableNBNAlert", "active": true, "description": "NGAPI for NBN ALert Version 2", "apiType": "rest", "method": "get", "parameters": ["identifier"], "baseUrl": "config.MergeExternalCollectorAPI.baseURI", "uri": "`sable/sable-nbn-alert/${parameters.identifier}`", "queryParams": "", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.mergeExternalCollectorUsername}:${config.mergeExternalCollectorPassword}`)}`\n});", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "Sable"}, {"name": "SableRaw", "active": true, "description": "NGAPI for Raw Cdr data Version 2", "apiType": "rest", "method": "get", "parameters": ["identifier"], "baseUrl": "config.MergeExternalCollectorAPI.baseURI", "uri": "`sable/sable-raw/${parameters.identifier}`", "queryParams": "new Object({\n    size: 100\n});", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.mergeExternalCollectorUsername}:${config.mergeExternalCollectorPassword}`)}`\n});", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "Sable"}, {"name": "SableVOIP", "active": true, "description": "NGAPI for TIPT and IPVPN data Version 2", "apiType": "rest", "method": "get", "parameters": ["identifier"], "baseUrl": "config.MergeExternalCollectorAPI.baseURI", "uri": "`sable/sable-voip/${parameters.identifier}`", "queryParams": "new Object({\n    size: 100\n});", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.mergeExternalCollectorUsername}:${config.mergeExternalCollectorPassword}`)}`\n});", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "Sable"}, {"name": "SableVOIPHistory", "active": true, "description": "NGAPI for TIPT and IPVPN data Version 2", "apiType": "rest", "method": "get", "parameters": ["identifier"], "baseUrl": "config.MergeExternalCollectorAPI.baseURI", "uri": "`sable/sable-voip-history/${parameters.identifier}`", "queryParams": "new Object({\n    size: 100\n});", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.mergeExternalCollectorUsername}:${config.mergeExternalCollectorPassword}`)}`\n});", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "Sable"}, {"name": "ScienceLogicGridList", "active": true, "description": "ScienceLogic - <PERSON> Grid List Details", "apiType": "rest", "method": "get", "parameters": [], "baseUrl": "config.PatTsoApi.gridListURI", "uri": "", "queryParams": "", "body": "", "header": "", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "ScienceLogicOKAPI"}], "timeout": 120000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "ScienceLogicGridList"}, {"name": "ScienceLogicInterface", "active": true, "description": "ScienceLogic - <PERSON> Get Interface Details", "apiType": "rest", "method": "post", "parameters": ["deviceName"], "baseUrl": "config.PatTsoApi.patBaseURI", "uri": "'process/:Telstra_PAT_Uplift:get_interface/execute'", "queryParams": "new Object({\n    mode: 'async'\n});", "body": "JSON.stringify(\n  {\n     'inputParameters': [\n      {\n         'name': 'Device_Name',\n            'value': parameters.deviceName\n        }\n     ]\n });", "header": "", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "ScienceLogicOKAPI"}, {"authKeyHeader": "Authentication-Token", "authKeyName": "ScienceLogicTsoAPI"}], "timeout": 120000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data.serverError", "asyncPoll": {"name": "ScienceLogicJobStatusAPI", "interval": 15000, "uri": "`job/${response.data[0].jobId}/status`", "queryParams": "", "header": "", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "ScienceLogicOKAPI"}, {"authKeyHeader": "Authentication-Token", "authKeyName": "ScienceLogicTsoAPI"}], "timeout": 180000, "parseResponse": "", "doneCondition": "response.data && response.data.status === 'COMPLETED';", "errorCondition": "!response", "transform": null, "resultAPI": null}, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "ScienceLogicInterface"}, {"name": "ScienceLogicIpDetails", "active": true, "description": "ScienceLogic - Pat <PERSON> IP Details", "apiType": "rest", "method": "post", "parameters": ["deviceName"], "baseUrl": "config.PatTsoApi.patBaseURI", "uri": "'process/:Telstra_PAT_Uplift:get_ip_details/execute'", "queryParams": "new Object({\n    mode: 'sync'\n});", "body": "JSON.stringify(\n  {\n     'inputParameters': [\n      {\n         'name': 'Device_Name',\n            'value': parameters.deviceName\n        }\n     ]\n });", "header": "", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "ScienceLogicOKAPI"}, {"authKeyHeader": "Authentication-Token", "authKeyName": "ScienceLogicTsoAPI"}], "timeout": 120000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "ScienceLogicIpDetails"}, {"name": "ScienceLogicNodeStatus", "active": true, "description": "ScienceLogic - <PERSON> Status", "apiType": "rest", "method": "post", "parameters": ["deviceName"], "baseUrl": "config.PatTsoApi.patBaseURI", "uri": "'process/:Telstra_PAT_Uplift:get_node_status/execute'", "queryParams": "new Object({\n    mode: 'sync'\n});", "body": "JSON.stringify(\n  {\n     'inputParameters': [\n      {\n         'name': 'Device_Name',\n            'value': parameters.deviceName\n        }\n     ]\n });", "header": "", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "ScienceLogicOKAPI"}, {"authKeyHeader": "Authentication-Token", "authKeyName": "ScienceLogicTsoAPI"}], "timeout": 120000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "ScienceLogicNodeStatus"}, {"name": "ScienceLogicSNMP", "active": true, "description": "ScienceLogic - <PERSON> SNMP Details", "apiType": "rest", "method": "post", "parameters": ["deviceName"], "baseUrl": "config.PatTsoApi.patBaseURI", "uri": "'process/:Telstra_PAT_Uplift:get_snmp/execute'", "queryParams": "new Object({\n    mode: 'sync'\n});", "body": "JSON.stringify(\n  {\n     'inputParameters': [\n      {\n         'name': 'Device_Name',\n            'value': parameters.deviceName\n        }\n     ]\n });", "header": "", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "ScienceLogicOKAPI"}, {"authKeyHeader": "Authentication-Token", "authKeyName": "ScienceLogicTsoAPI"}], "timeout": 120000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "ScienceLogicSNMP"}, {"name": "SDWAN", "active": true, "description": "Fetchs messages from SDWAN kafka topic", "apiType": "function", "method": null, "parameters": ["fnn", "deviceName"], "baseUrl": "getSDWANDocument", "uri": "", "queryParams": "if (parameters.deviceName && !parameters.fnn) {\n    new Object({\n        deviceName: parameters.deviceName\n    });\n} else {\n    new Object({\n        fnn: parameters.fnn\n    });\n}", "body": "", "header": "", "authKeyDb": [], "timeout": 60000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": " "}, {"name": "ServiceCentralIncident", "active": true, "description": "Retrieve Service Central Incidents", "apiType": "rest", "method": "get", "parameters": ["fnn", "number", "lastWorklog", "limit", "offset", "startDate", "endDate"], "baseUrl": "config.MergeExternalCollectorAPI.baseURI", "uri": "'service-now-incidents';", "queryParams": "let queryParameters = {};\nif (parameters.fnn) {\n    queryParameters.fnn = parameters.fnn;\n} else if (parameters.number) {\n    queryParameters.number = parameters.number;\n}\nif (parameters.lastWorklog !== undefined) {\n    queryParameters.lastWorklog = parameters.lastWorklog;\n}\nif (parameters.limit !== undefined) {\n    queryParameters.limit = parameters.limit;\n}\nif (parameters.limit !== undefined) {\n    queryParameters.offset = parameters.offset;\n}\nif (parameters.startDate) {\n    queryParameters.startDate = parameters.startDate;\n}\nif (parameters.endDate) {\n    queryParameters.endDate = parameters.endDate;\n}\nqueryParameters;", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.mergeExternalCollectorUsername}:${config.mergeExternalCollectorPassword}`)}`\n});", "authKeyDb": [], "timeout": 240000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "ServiceCentralIncident"}, {"name": "ServiceHealth", "active": true, "description": "NBN service health API for a service", "apiType": "rest", "method": "post", "parameters": ["AVCID", "ServiceHealthSpec"], "baseUrl": "config.ServiceHealth.serviceCheckURI", "uri": "", "queryParams": "", "body": "`{\"avcId\":\"${parameters.AVCID}\", \"serviceHealthSpecification\": ${JSON.stringify(parameters.ServiceHealthSpec)} }`", "header": "`{\"Content-Type\": \"application/json\", \"NBN-TransactionID\": \"${uuidv4()}\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "ServiceHealth"}], "timeout": 60000, "parseResponse": "Object.assign(response.data, {'NBN-TransactionID': response.request.headers['NBN-TransactionID']})", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data.serverError", "asyncPoll": {"name": "NBNSERVICEHEALTHAPI", "interval": 15000, "uri": "`${response.data.id}`", "queryParams": "", "header": "`{\"Content-Type\": \"application/json\", \"NBN-TransactionID\": \"${uuidv4()}\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "ServiceHealth"}], "timeout": 180000, "parseResponse": "Object.assign(response.data, {'NBN-TransactionID': response.request.headers['NBN-TransactionID']})", "doneCondition": "response.data && response.data.status === 'Complete';", "errorCondition": "!response", "transform": null, "resultAPI": null}, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "ServiceHealth"}, {"name": "ServiceTXAlarms", "active": true, "description": "Alarms and outages information sourced from Tomahawk API", "apiType": "rest", "method": "post", "parameters": ["linkID", "sourcesData"], "baseUrl": "config.HAPIURI", "uri": "\"rTomahawk\"", "queryParams": "", "body": "`{ \"link_id\": \"${parameters.linkID}\", \"QUERY\":\"SERVICE_TX_ALARMS\", \"sourcesData\": \"${JSON.stringify(JSON.stringify(parameters.sourcesData)).slice(1,-1)}\", \"thawkURL\":\"${config.TomahawkURI.baseURI}\" }`", "header": "`{\"tomahawk-key\": \"${config.serviceTXAlarmsKey}\", \"content-type\":\"application/json\"}`", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "ServiceTXAlarms"}, {"name": "SHAREDBOSS", "active": true, "description": "SHAREDBOSS to Network Elements", "apiType": "rest", "method": "get", "parameters": ["device", "commands", "fnn"], "baseUrl": "config.HAPIURI", "uri": "\"rSHAREDBOSS\"", "queryParams": "new Object({\n    device: parameters.device,\n    commands: parameters.commands,\n    fnn: parameters.fnn\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "SHAREDBOSS"}, {"name": "SIIAM", "active": true, "description": "Read SIIAM", "apiType": "rest", "method": "get", "parameters": ["SIIAMfnn", "SIIAMcaseID", "SIIAMcommands"], "baseUrl": "config.HAPIURI", "uri": "\"rSIIAM\"", "queryParams": "new Object({\n    FNN: parameters.SIIAMfnn,\n    caseID: parameters.SIIAMcaseID,\n    commands: parameters.SIIAMcommands\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "SIIAM"}, {"name": "SMDM", "active": true, "description": "B2C or Wholesale data from SMDM OKAPI", "apiType": "rest", "method": "get", "parameters": ["fnn", "serviceType"], "baseUrl": "config.SMDMAPI.tasksURI", "uri": "`${parameters.serviceType}/${parameters.fnn}`", "queryParams": "new Object({\n   exclude: \"physical\"\n});", "body": "", "header": "`{\"Content-Type\": \"application/json\"}`", "authKeyDb": [{"authKeyHeader": "Authorization", "authKeyName": "SMDMAPI"}], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.data.fault !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "SMDM"}, {"name": "SolarWindsAgl", "active": true, "description": "SolarWinds Instance for AGL", "apiType": "rest", "method": "get", "parameters": ["deviceName"], "baseUrl": "config.SolarWinds.aglBaseURI", "uri": "'SolarWinds/InformationService/v3/Json/Query'", "queryParams": "new Object({\n query: `SELECT Nodes.NodeID AS node_NodeID, Nodes.ObjectSubType AS node_ObjectSubType, Nodes.IPAddress AS node_IPAddress, Nodes.IPAddressType AS node_IPAddressType, Nodes.DynamicIP AS node_DynamicIP, Nodes.Caption AS node_Caption, Nodes.NodeDescription AS node_NodeDescription, Nodes.Description AS node_Description, Nodes.DNS AS node_DNS, Nodes.SysName AS node_SysName, Nodes.Vendor AS node_Vendor, Nodes.SysObjectID AS node_SysObjectID, Nodes.Location AS node_Location, Nodes.Contact AS node_Contact, Nodes.VendorIcon AS node_VendorIcon, Nodes.Icon AS node_Icon, Nodes.Status AS node_Status, Nodes.StatusLED AS node_StatusLED, Nodes.StatusDescription AS node_StatusDescription, Nodes.CustomStatus AS node_CustomStatus, Nodes.IOSImage AS node_IOSImage, Nodes.IOSVersion AS node_IOSVersion, Nodes.GroupStatus AS node_GroupStatus, Nodes.LastBoot AS node_LastBoot, Nodes.SystemUpTime AS node_SystemUpTime, Nodes.ResponseTime AS node_ResponseTime, Nodes.PercentLoss AS node_PercentLoss, Nodes.AvgResponseTime AS node_AvgResponseTime, Nodes.MinResponseTime AS node_MinResponseTime, Nodes.MaxResponseTime AS node_MaxResponseTime, Nodes.CPUCount AS node_CPUCount, Nodes.CPULoad AS node_CPULoad, Nodes.MemoryUsed AS node_MemoryUsed, Nodes.MemoryAvailable AS node_MemoryAvailable, Nodes.PercentMemoryUsed AS node_PercentMemoryUsed, Nodes.PercentMemoryAvailable AS node_PercentMemoryAvailable, Nodes.LastSync AS node_LastSync, Nodes.LastSystemUpTimePollUtc AS node_LastSystemUpTimePollUtc, Nodes.MachineType AS node_MachineType, Nodes.IsServer AS node_IsServer, Nodes.Severity AS node_Severity, Nodes.UiSeverity AS node_UiSeverity, Nodes.ChildStatus AS node_ChildStatus, Nodes.Allow64BitCounters AS node_Allow64BitCounters, Nodes.AgentPort AS node_AgentPort, Nodes.TotalMemory AS node_TotalMemory, Nodes.CMTS AS node_CMTS, Nodes.CustomPollerLastStatisticsPoll AS node_CustomPollerLastStatisticsPoll, Nodes.CustomPollerLastStatisticsPollSuccess AS node_CustomPollerLastStatisticsPollSuccess, Nodes.SNMPVersion AS node_SNMPVersion, Nodes.PollInterval AS node_PollInterval, Nodes.EngineID AS node_EngineID, Nodes.RediscoveryInterval AS node_RediscoveryInterval, Nodes.NextPoll AS node_NextPoll, Nodes.NextRediscovery AS node_NextRediscovery, Nodes.StatCollection AS node_StatCollection, Nodes.External AS node_External, Nodes.Community AS node_Community, Nodes.RWCommunity AS node_RWCommunity, Nodes.IP AS node_IP, Nodes.IPAddressGUID AS node_IPAddressGUID, Nodes.BlockUntil AS node_BlockUntil, Nodes.OrionIdPrefix AS node_OrionIdPrefix, Nodes.OrionIdColumn AS node_OrionIdColumn, Nodes.EntityType AS node_EntityType, Nodes.DetailsUrl AS node_DetailsUrl, Nodes.DisplayName AS node_DisplayName, Nodes.Category AS node_Category, Nodes.IsOrionServer AS node_IsOrionServer, Nodes.UnManaged AS node_UnManaged, Nodes.UnManageFrom AS node_UnManageFrom, Nodes.UnManageUntil AS node_UnManageUntil, Nodes.Image AS node_Image, Nodes.InstanceType AS node_InstanceType, Nodes.Uri AS node_Uri, Nodes.InstanceSiteId AS node_InstanceSiteId, HardwareInfo.Manufacturer AS hardware_Manufacturer, HardwareInfo.Model AS hardware_Model, HardwareInfo.ServiceTag AS hardware_ServiceTag, HardwareInfo.IsDisabled AS hardware_IsDisabled, HardwareInfo.CategoriesWithProblems AS hardware_CategoriesWithProblems, HardwareInfo.CategoriesWithStatus AS hardware_CategoriesWithStatus, HardwareInfo.FullyQualifiedName AS hardware_FullyQualifiedName, HardwareInfo.UnManaged AS hardware_UnManaged, HardwareInfo.StatusDescription AS hardware_StatusDescription, HardwareInfo.Description AS hardware_Description, HardwareInfo.StatusLED AS hardware_StatusLED, HardwareInfo.DisplayName AS hardware_DisplayName, ActiveAlerts.AlertID AS alert_AlertID, ActiveAlerts.AlertTime AS alert_AlertTime, ActiveAlerts.ObjectType AS alert_ObjectType, ActiveAlerts.ObjectID AS alert_ObjectID, ActiveAlerts.ObjectName AS alert_ObjectName, ActiveAlerts.EventMessage AS alert_EventMessage, ActiveAlerts.PropertyID AS alert_PropertyID, ActiveAlerts.Monitoredproperty AS alert_Monitoredproperty, ActiveAlerts.CurrentValue AS alert_CurrentValue, ActiveAlerts.TriggerValue AS alert_TriggerValue, ActiveAlerts.ResetValue AS alert_ResetValue, ActiveAlerts.EngineID AS alert_EngineID, ActiveAlerts.AlertNotes AS alert_AlertNotes, ActiveAlerts.ExpireTime AS alert_ExpireTime, ActiveAlerts.Description AS alert_Description, ActiveAlerts.InstanceType AS alert_InstanceType, ActiveAlerts.Uri AS alert_Uri, ActiveAlerts.InstanceSiteId AS alert_InstanceSiteId, Interfaces.InterfaceID AS interface_InterfaceID, Interfaces.ObjectSubType AS interface_ObjectSubType, Interfaces.Name AS interface_Name, Interfaces.TypeName AS interface_TypeName, Interfaces.TypeDescription AS interface_TypeDescription, Interfaces.Speed AS interface_Speed, Interfaces.MTU AS interface_MTU, Interfaces.PhysicalAddress AS interface_PhysicalAddress, Interfaces.AdminStatus AS interface_AdminStatus, Interfaces.OperStatus AS interface_OperStatus, Interfaces.StatusIcon AS interface_StatusIcon, Interfaces.InBandwidth AS interface_InBandwidth, Interfaces.OutBandwidth AS interface_OutBandwidth, Interfaces.Caption AS interface_Caption, Interfaces.FullName AS interface_FullName, Interfaces.Alias AS interface_Alias, Interfaces.IfName AS interface_IfName, Interfaces.CustomBandwidth AS interface_CustomBandwidth, Interfaces.InterfaceSpeed AS interface_InterfaceSpeed, Interfaces.InterfaceCaption AS interface_InterfaceCaption, Interfaces.MAC AS interface_MAC, Interfaces.InterfaceName AS interface_InterfaceName, Interfaces.InterfaceTypeName AS interface_InterfaceTypeName, Interfaces.InterfaceAlias AS interface_InterfaceAlias, Interfaces.InterfaceMTU AS interface_InterfaceMTU, Interfaces.InterfaceTypeDescription AS interface_InterfaceTypeDescription, Interfaces.DuplexMode AS interface_DuplexMode, Interfaces.Status AS interface_Status, Interfaces.Description AS interface_Description, Interfaces.StatusDescription AS interface_StatusDescription, Interfaces.UnManaged AS interface_UnManaged, Interfaces.DisplayName AS interface_DisplayName, NodesCustomProperties.CarriageType AS custom_CarriageType, AddressId AS custom_AddressId, NodesCustomProperties.DevAlias AS custom_DevAlias, NodesCustomProperties.DevClass AS custom_DevClass, NodesCustomProperties.DevStatus AS custom_DevStatus, NodesCustomProperties.SiteName AS custom_SiteName, NodesCustomProperties.SiteType AS custom_SiteType, NodesCustomProperties._3LC AS custom_3LC, NodesCustomProperties._3PPConnected AS custom_3PPConnected, NodesCustomProperties.IOM_BAU_Status AS custom_IOM_BAU_Status, NodesCustomProperties.CPUThreshold AS custom_MgmtClass, NodesCustomProperties.IndependenceStatus AS custom_IndependenceStatus, NodesCustomProperties.swCustomer AS custom_swCustomer, NodesCustomProperties.RoomNumber AS custom_RoomNumber, NodesCustomProperties.FloorNumber AS custom_FloorNumber, NodesCustomProperties.StreetNumber AS custom_StreetNumber, NodesCustomProperties.StreetName AS custom_StreetName, NodesCustomProperties.StreetType AS custom_StreetType, NodesCustomProperties.StreetSuffix AS custom_StreetSuffix, NodesCustomProperties.City AS custom_City, NodesCustomProperties.PostCode AS custom_PostCode, NodesCustomProperties.State AS custom_State, NodesCustomProperties.Country AS custom_Country, NodesCustomProperties.Latitude AS custom_Latitude, NodesCustomProperties.Longitude AS custom_Longitude, InterfacesCustomProperties.InterfaceAlertEnable AS custom_InterfaceAlertEnable, InterfacesCustomProperties.InterfaceCategory AS custom_InterfaceCategory, InterfacesCustomProperties.InterfaceRole AS custom_InterfaceRole FROM Orion.Nodes AS Nodes LEFT JOIN Orion.HardwareHealth.HardwareInfo AS HardwareInfo ON Nodes.NodeID = HardwareInfo.NodeID LEFT JOIN Orion.ActiveAlerts AS ActiveAlerts ON Nodes.NodeID = ActiveAlerts.NodeID LEFT JOIN Orion.NPM.Interfaces AS Interfaces ON Nodes.NodeID = Interfaces.NodeID LEFT JOIN Orion.NodesCustomProperties AS NodesCustomProperties ON Nodes.NodeID = NodesCustomProperties.NodeID LEFT JOIN Orion.NPM.InterfacesCustomProperties AS InterfacesCustomProperties ON Interfaces.InterfaceID = InterfacesCustomProperties.InterfaceID WHERE Nodes.NodeName = '${parameters.deviceName}'`\n});", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.SolarWindsUserName}:${config.SolarWindsPassword}`)}`\n});", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "SolarWindsAgl"}, {"name": "SolarWindsArrow", "active": true, "description": "SolarWinds Instance for Arrow", "apiType": "rest", "method": "get", "parameters": ["deviceName"], "baseUrl": "config.SolarWinds.arrowBaseURI", "uri": "'SolarWinds/InformationService/v3/Json/Query'", "queryParams": "new Object({\n query: `SELECT Nodes.NodeID AS node_NodeID, Nodes.ObjectSubType AS node_ObjectSubType, Nodes.IPAddress AS node_IPAddress, Nodes.IPAddressType AS node_IPAddressType, Nodes.DynamicIP AS node_DynamicIP, Nodes.Caption AS node_Caption, Nodes.NodeDescription AS node_NodeDescription, Nodes.Description AS node_Description, Nodes.DNS AS node_DNS, Nodes.SysName AS node_SysName, Nodes.Vendor AS node_Vendor, Nodes.SysObjectID AS node_SysObjectID, Nodes.Location AS node_Location, Nodes.Contact AS node_Contact, Nodes.VendorIcon AS node_VendorIcon, Nodes.Icon AS node_Icon, Nodes.Status AS node_Status, Nodes.StatusLED AS node_StatusLED, Nodes.StatusDescription AS node_StatusDescription, Nodes.CustomStatus AS node_CustomStatus, Nodes.IOSImage AS node_IOSImage, Nodes.IOSVersion AS node_IOSVersion, Nodes.GroupStatus AS node_GroupStatus, Nodes.LastBoot AS node_LastBoot, Nodes.SystemUpTime AS node_SystemUpTime, Nodes.ResponseTime AS node_ResponseTime, Nodes.PercentLoss AS node_PercentLoss, Nodes.AvgResponseTime AS node_AvgResponseTime, Nodes.MinResponseTime AS node_MinResponseTime, Nodes.MaxResponseTime AS node_MaxResponseTime, Nodes.CPUCount AS node_CPUCount, Nodes.CPULoad AS node_CPULoad, Nodes.MemoryUsed AS node_MemoryUsed, Nodes.MemoryAvailable AS node_MemoryAvailable, Nodes.PercentMemoryUsed AS node_PercentMemoryUsed, Nodes.PercentMemoryAvailable AS node_PercentMemoryAvailable, Nodes.LastSync AS node_LastSync, Nodes.LastSystemUpTimePollUtc AS node_LastSystemUpTimePollUtc, Nodes.MachineType AS node_MachineType, Nodes.IsServer AS node_IsServer, Nodes.Severity AS node_Severity, Nodes.UiSeverity AS node_UiSeverity, Nodes.ChildStatus AS node_ChildStatus, Nodes.Allow64BitCounters AS node_Allow64BitCounters, Nodes.AgentPort AS node_AgentPort, Nodes.TotalMemory AS node_TotalMemory, Nodes.CMTS AS node_CMTS, Nodes.CustomPollerLastStatisticsPoll AS node_CustomPollerLastStatisticsPoll, Nodes.CustomPollerLastStatisticsPollSuccess AS node_CustomPollerLastStatisticsPollSuccess, Nodes.SNMPVersion AS node_SNMPVersion, Nodes.PollInterval AS node_PollInterval, Nodes.EngineID AS node_EngineID, Nodes.RediscoveryInterval AS node_RediscoveryInterval, Nodes.NextPoll AS node_NextPoll, Nodes.NextRediscovery AS node_NextRediscovery, Nodes.StatCollection AS node_StatCollection, Nodes.External AS node_External, Nodes.Community AS node_Community, Nodes.RWCommunity AS node_RWCommunity, Nodes.IP AS node_IP, Nodes.IPAddressGUID AS node_IPAddressGUID, Nodes.BlockUntil AS node_BlockUntil, Nodes.OrionIdPrefix AS node_OrionIdPrefix, Nodes.OrionIdColumn AS node_OrionIdColumn, Nodes.EntityType AS node_EntityType, Nodes.DetailsUrl AS node_DetailsUrl, Nodes.DisplayName AS node_DisplayName, Nodes.Category AS node_Category, Nodes.IsOrionServer AS node_IsOrionServer, Nodes.UnManaged AS node_UnManaged, Nodes.UnManageFrom AS node_UnManageFrom, Nodes.UnManageUntil AS node_UnManageUntil, Nodes.Image AS node_Image, Nodes.InstanceType AS node_InstanceType, Nodes.Uri AS node_Uri, Nodes.InstanceSiteId AS node_InstanceSiteId, HardwareInfo.Manufacturer AS hardware_Manufacturer, HardwareInfo.Model AS hardware_Model, HardwareInfo.ServiceTag AS hardware_ServiceTag, HardwareInfo.IsDisabled AS hardware_IsDisabled, HardwareInfo.CategoriesWithProblems AS hardware_CategoriesWithProblems, HardwareInfo.CategoriesWithStatus AS hardware_CategoriesWithStatus, HardwareInfo.FullyQualifiedName AS hardware_FullyQualifiedName, HardwareInfo.UnManaged AS hardware_UnManaged, HardwareInfo.StatusDescription AS hardware_StatusDescription, HardwareInfo.Description AS hardware_Description, HardwareInfo.StatusLED AS hardware_StatusLED, HardwareInfo.DisplayName AS hardware_DisplayName, ActiveAlerts.AlertID AS alert_AlertID, ActiveAlerts.AlertTime AS alert_AlertTime, ActiveAlerts.ObjectType AS alert_ObjectType, ActiveAlerts.ObjectID AS alert_ObjectID, ActiveAlerts.ObjectName AS alert_ObjectName, ActiveAlerts.EventMessage AS alert_EventMessage, ActiveAlerts.PropertyID AS alert_PropertyID, ActiveAlerts.Monitoredproperty AS alert_Monitoredproperty, ActiveAlerts.CurrentValue AS alert_CurrentValue, ActiveAlerts.TriggerValue AS alert_TriggerValue, ActiveAlerts.ResetValue AS alert_ResetValue, ActiveAlerts.EngineID AS alert_EngineID, ActiveAlerts.AlertNotes AS alert_AlertNotes, ActiveAlerts.ExpireTime AS alert_ExpireTime, ActiveAlerts.Description AS alert_Description, ActiveAlerts.InstanceType AS alert_InstanceType, ActiveAlerts.Uri AS alert_Uri, ActiveAlerts.InstanceSiteId AS alert_InstanceSiteId, Interfaces.InterfaceID AS interface_InterfaceID, Interfaces.ObjectSubType AS interface_ObjectSubType, Interfaces.Name AS interface_Name, Interfaces.TypeName AS interface_TypeName, Interfaces.TypeDescription AS interface_TypeDescription, Interfaces.Speed AS interface_Speed, Interfaces.MTU AS interface_MTU, Interfaces.PhysicalAddress AS interface_PhysicalAddress, Interfaces.AdminStatus AS interface_AdminStatus, Interfaces.OperStatus AS interface_OperStatus, Interfaces.StatusIcon AS interface_StatusIcon, Interfaces.InBandwidth AS interface_InBandwidth, Interfaces.OutBandwidth AS interface_OutBandwidth, Interfaces.Caption AS interface_Caption, Interfaces.FullName AS interface_FullName, Interfaces.Alias AS interface_Alias, Interfaces.IfName AS interface_IfName, Interfaces.CustomBandwidth AS interface_CustomBandwidth, Interfaces.InterfaceSpeed AS interface_InterfaceSpeed, Interfaces.InterfaceCaption AS interface_InterfaceCaption, Interfaces.MAC AS interface_MAC, Interfaces.InterfaceName AS interface_InterfaceName, Interfaces.InterfaceTypeName AS interface_InterfaceTypeName, Interfaces.InterfaceAlias AS interface_InterfaceAlias, Interfaces.InterfaceMTU AS interface_InterfaceMTU, Interfaces.InterfaceTypeDescription AS interface_InterfaceTypeDescription, Interfaces.DuplexMode AS interface_DuplexMode, Interfaces.Status AS interface_Status, Interfaces.Description AS interface_Description, Interfaces.StatusDescription AS interface_StatusDescription, Interfaces.UnManaged AS interface_UnManaged, Interfaces.DisplayName AS interface_DisplayName, NodesCustomProperties.CarriageType AS custom_CarriageType, AddressId AS custom_AddressId, NodesCustomProperties.DevAlias AS custom_DevAlias, NodesCustomProperties.DevClass AS custom_DevClass, NodesCustomProperties.DevStatus AS custom_DevStatus, NodesCustomProperties.SiteName AS custom_SiteName, NodesCustomProperties.SiteType AS custom_SiteType, NodesCustomProperties._3LC AS custom_3LC, NodesCustomProperties._3PPConnected AS custom_3PPConnected, NodesCustomProperties.IOM_BAU_Status AS custom_IOM_BAU_Status, NodesCustomProperties.CPUThreshold AS custom_MgmtClass, NodesCustomProperties.IndependenceStatus AS custom_IndependenceStatus, NodesCustomProperties.swCustomer AS custom_swCustomer, NodesCustomProperties.RoomNumber AS custom_RoomNumber, NodesCustomProperties.FloorNumber AS custom_FloorNumber, NodesCustomProperties.StreetNumber AS custom_StreetNumber, NodesCustomProperties.StreetName AS custom_StreetName, NodesCustomProperties.StreetType AS custom_StreetType, NodesCustomProperties.StreetSuffix AS custom_StreetSuffix, NodesCustomProperties.City AS custom_City, NodesCustomProperties.PostCode AS custom_PostCode, NodesCustomProperties.State AS custom_State, NodesCustomProperties.Country AS custom_Country, NodesCustomProperties.Latitude AS custom_Latitude, NodesCustomProperties.Longitude AS custom_Longitude, InterfacesCustomProperties.InterfaceAlertEnable AS custom_InterfaceAlertEnable, InterfacesCustomProperties.InterfaceCategory AS custom_InterfaceCategory, InterfacesCustomProperties.InterfaceRole AS custom_InterfaceRole FROM Orion.Nodes AS Nodes LEFT JOIN Orion.HardwareHealth.HardwareInfo AS HardwareInfo ON Nodes.NodeID = HardwareInfo.NodeID LEFT JOIN Orion.ActiveAlerts AS ActiveAlerts ON Nodes.NodeID = ActiveAlerts.NodeID LEFT JOIN Orion.NPM.Interfaces AS Interfaces ON Nodes.NodeID = Interfaces.NodeID LEFT JOIN Orion.NodesCustomProperties AS NodesCustomProperties ON Nodes.NodeID = NodesCustomProperties.NodeID LEFT JOIN Orion.NPM.InterfacesCustomProperties AS InterfacesCustomProperties ON Interfaces.InterfaceID = InterfacesCustomProperties.InterfaceID WHERE Nodes.NodeName = '${parameters.deviceName}'`\n});", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.SolarWindsUserName}:${config.SolarWindsPassword}`)}`\n});", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "SolarWindsArrow"}, {"name": "SolarWindsBP", "active": true, "description": "SolarWinds Instance for BP", "apiType": "rest", "method": "get", "parameters": ["deviceName"], "baseUrl": "config.SolarWinds.bpBaseURI", "uri": "'SolarWinds/InformationService/v3/Json/Query'", "queryParams": "new Object({\n query: `SELECT Nodes.NodeID AS node_NodeID, Nodes.ObjectSubType AS node_ObjectSubType, Nodes.IPAddress AS node_IPAddress, Nodes.IPAddressType AS node_IPAddressType, Nodes.DynamicIP AS node_DynamicIP, Nodes.Caption AS node_Caption, Nodes.NodeDescription AS node_NodeDescription, Nodes.Description AS node_Description, Nodes.DNS AS node_DNS, Nodes.SysName AS node_SysName, Nodes.Vendor AS node_Vendor, Nodes.SysObjectID AS node_SysObjectID, Nodes.Location AS node_Location, Nodes.Contact AS node_Contact, Nodes.VendorIcon AS node_VendorIcon, Nodes.Icon AS node_Icon, Nodes.Status AS node_Status, Nodes.StatusLED AS node_StatusLED, Nodes.StatusDescription AS node_StatusDescription, Nodes.CustomStatus AS node_CustomStatus, Nodes.IOSImage AS node_IOSImage, Nodes.IOSVersion AS node_IOSVersion, Nodes.GroupStatus AS node_GroupStatus, Nodes.LastBoot AS node_LastBoot, Nodes.SystemUpTime AS node_SystemUpTime, Nodes.ResponseTime AS node_ResponseTime, Nodes.PercentLoss AS node_PercentLoss, Nodes.AvgResponseTime AS node_AvgResponseTime, Nodes.MinResponseTime AS node_MinResponseTime, Nodes.MaxResponseTime AS node_MaxResponseTime, Nodes.CPUCount AS node_CPUCount, Nodes.CPULoad AS node_CPULoad, Nodes.MemoryUsed AS node_MemoryUsed, Nodes.MemoryAvailable AS node_MemoryAvailable, Nodes.PercentMemoryUsed AS node_PercentMemoryUsed, Nodes.PercentMemoryAvailable AS node_PercentMemoryAvailable, Nodes.LastSync AS node_LastSync, Nodes.LastSystemUpTimePollUtc AS node_LastSystemUpTimePollUtc, Nodes.MachineType AS node_MachineType, Nodes.IsServer AS node_IsServer, Nodes.Severity AS node_Severity, Nodes.UiSeverity AS node_UiSeverity, Nodes.ChildStatus AS node_ChildStatus, Nodes.Allow64BitCounters AS node_Allow64BitCounters, Nodes.AgentPort AS node_AgentPort, Nodes.TotalMemory AS node_TotalMemory, Nodes.CMTS AS node_CMTS, Nodes.CustomPollerLastStatisticsPoll AS node_CustomPollerLastStatisticsPoll, Nodes.CustomPollerLastStatisticsPollSuccess AS node_CustomPollerLastStatisticsPollSuccess, Nodes.SNMPVersion AS node_SNMPVersion, Nodes.PollInterval AS node_PollInterval, Nodes.EngineID AS node_EngineID, Nodes.RediscoveryInterval AS node_RediscoveryInterval, Nodes.NextPoll AS node_NextPoll, Nodes.NextRediscovery AS node_NextRediscovery, Nodes.StatCollection AS node_StatCollection, Nodes.External AS node_External, Nodes.Community AS node_Community, Nodes.RWCommunity AS node_RWCommunity, Nodes.IP AS node_IP, Nodes.IPAddressGUID AS node_IPAddressGUID, Nodes.BlockUntil AS node_BlockUntil, Nodes.OrionIdPrefix AS node_OrionIdPrefix, Nodes.OrionIdColumn AS node_OrionIdColumn, Nodes.EntityType AS node_EntityType, Nodes.DetailsUrl AS node_DetailsUrl, Nodes.DisplayName AS node_DisplayName, Nodes.Category AS node_Category, Nodes.IsOrionServer AS node_IsOrionServer, Nodes.UnManaged AS node_UnManaged, Nodes.UnManageFrom AS node_UnManageFrom, Nodes.UnManageUntil AS node_UnManageUntil, Nodes.Image AS node_Image, Nodes.InstanceType AS node_InstanceType, Nodes.Uri AS node_Uri, Nodes.InstanceSiteId AS node_InstanceSiteId, HardwareInfo.Manufacturer AS hardware_Manufacturer, HardwareInfo.Model AS hardware_Model, HardwareInfo.ServiceTag AS hardware_ServiceTag, HardwareInfo.IsDisabled AS hardware_IsDisabled, HardwareInfo.CategoriesWithProblems AS hardware_CategoriesWithProblems, HardwareInfo.CategoriesWithStatus AS hardware_CategoriesWithStatus, HardwareInfo.FullyQualifiedName AS hardware_FullyQualifiedName, HardwareInfo.UnManaged AS hardware_UnManaged, HardwareInfo.StatusDescription AS hardware_StatusDescription, HardwareInfo.Description AS hardware_Description, HardwareInfo.StatusLED AS hardware_StatusLED, HardwareInfo.DisplayName AS hardware_DisplayName, ActiveAlerts.AlertID AS alert_AlertID, ActiveAlerts.AlertTime AS alert_AlertTime, ActiveAlerts.ObjectType AS alert_ObjectType, ActiveAlerts.ObjectID AS alert_ObjectID, ActiveAlerts.ObjectName AS alert_ObjectName, ActiveAlerts.EventMessage AS alert_EventMessage, ActiveAlerts.PropertyID AS alert_PropertyID, ActiveAlerts.Monitoredproperty AS alert_Monitoredproperty, ActiveAlerts.CurrentValue AS alert_CurrentValue, ActiveAlerts.TriggerValue AS alert_TriggerValue, ActiveAlerts.ResetValue AS alert_ResetValue, ActiveAlerts.EngineID AS alert_EngineID, ActiveAlerts.AlertNotes AS alert_AlertNotes, ActiveAlerts.ExpireTime AS alert_ExpireTime, ActiveAlerts.Description AS alert_Description, ActiveAlerts.InstanceType AS alert_InstanceType, ActiveAlerts.Uri AS alert_Uri, ActiveAlerts.InstanceSiteId AS alert_InstanceSiteId, Interfaces.InterfaceID AS interface_InterfaceID, Interfaces.ObjectSubType AS interface_ObjectSubType, Interfaces.Name AS interface_Name, Interfaces.TypeName AS interface_TypeName, Interfaces.TypeDescription AS interface_TypeDescription, Interfaces.Speed AS interface_Speed, Interfaces.MTU AS interface_MTU, Interfaces.PhysicalAddress AS interface_PhysicalAddress, Interfaces.AdminStatus AS interface_AdminStatus, Interfaces.OperStatus AS interface_OperStatus, Interfaces.StatusIcon AS interface_StatusIcon, Interfaces.InBandwidth AS interface_InBandwidth, Interfaces.OutBandwidth AS interface_OutBandwidth, Interfaces.Caption AS interface_Caption, Interfaces.FullName AS interface_FullName, Interfaces.Alias AS interface_Alias, Interfaces.IfName AS interface_IfName, Interfaces.CustomBandwidth AS interface_CustomBandwidth, Interfaces.InterfaceSpeed AS interface_InterfaceSpeed, Interfaces.InterfaceCaption AS interface_InterfaceCaption, Interfaces.MAC AS interface_MAC, Interfaces.InterfaceName AS interface_InterfaceName, Interfaces.InterfaceTypeName AS interface_InterfaceTypeName, Interfaces.InterfaceAlias AS interface_InterfaceAlias, Interfaces.InterfaceMTU AS interface_InterfaceMTU, Interfaces.InterfaceTypeDescription AS interface_InterfaceTypeDescription, Interfaces.DuplexMode AS interface_DuplexMode, Interfaces.Status AS interface_Status, Interfaces.Description AS interface_Description, Interfaces.StatusDescription AS interface_StatusDescription, Interfaces.UnManaged AS interface_UnManaged, Interfaces.DisplayName AS interface_DisplayName, NodesCustomProperties.CarriageType AS custom_CarriageType, AddressId AS custom_AddressId, NodesCustomProperties.DevAlias AS custom_DevAlias, NodesCustomProperties.DevClass AS custom_DevClass, NodesCustomProperties.DevStatus AS custom_DevStatus, NodesCustomProperties.SiteName AS custom_SiteName, NodesCustomProperties.SiteType AS custom_SiteType, NodesCustomProperties._3LC AS custom_3LC, NodesCustomProperties._3PPConnected AS custom_3PPConnected, NodesCustomProperties.IOM_BAU_Status AS custom_IOM_BAU_Status, NodesCustomProperties.CPUThreshold AS custom_MgmtClass, NodesCustomProperties.IndependenceStatus AS custom_IndependenceStatus, NodesCustomProperties.swCustomer AS custom_swCustomer, NodesCustomProperties.RoomNumber AS custom_RoomNumber, NodesCustomProperties.FloorNumber AS custom_FloorNumber, NodesCustomProperties.StreetNumber AS custom_StreetNumber, NodesCustomProperties.StreetName AS custom_StreetName, NodesCustomProperties.StreetType AS custom_StreetType, NodesCustomProperties.StreetSuffix AS custom_StreetSuffix, NodesCustomProperties.City AS custom_City, NodesCustomProperties.PostCode AS custom_PostCode, NodesCustomProperties.State AS custom_State, NodesCustomProperties.Country AS custom_Country, NodesCustomProperties.Latitude AS custom_Latitude, NodesCustomProperties.Longitude AS custom_Longitude, InterfacesCustomProperties.InterfaceAlertEnable AS custom_InterfaceAlertEnable, InterfacesCustomProperties.InterfaceCategory AS custom_InterfaceCategory, InterfacesCustomProperties.InterfaceRole AS custom_InterfaceRole FROM Orion.Nodes AS Nodes LEFT JOIN Orion.HardwareHealth.HardwareInfo AS HardwareInfo ON Nodes.NodeID = HardwareInfo.NodeID LEFT JOIN Orion.ActiveAlerts AS ActiveAlerts ON Nodes.NodeID = ActiveAlerts.NodeID LEFT JOIN Orion.NPM.Interfaces AS Interfaces ON Nodes.NodeID = Interfaces.NodeID LEFT JOIN Orion.NodesCustomProperties AS NodesCustomProperties ON Nodes.NodeID = NodesCustomProperties.NodeID LEFT JOIN Orion.NPM.InterfacesCustomProperties AS InterfacesCustomProperties ON Interfaces.InterfaceID = InterfacesCustomProperties.InterfaceID WHERE Nodes.NodeName = '${parameters.deviceName}'`\n});", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.SolarWindsUserName}:${config.SolarWindsPassword}`)}`\n});", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "SolarWindsBP"}, {"name": "SolarWindsCBA", "active": true, "description": "SolarWinds Instance for CBA", "apiType": "rest", "method": "get", "parameters": ["deviceName"], "baseUrl": "config.SolarWinds.cbaBaseURI", "uri": "'SolarWinds/InformationService/v3/Json/Query'", "queryParams": "new Object({\n query: `SELECT Nodes.NodeID AS node_NodeID, Nodes.ObjectSubType AS node_ObjectSubType, Nodes.IPAddress AS node_IPAddress, Nodes.IPAddressType AS node_IPAddressType, Nodes.DynamicIP AS node_DynamicIP, Nodes.Caption AS node_Caption, Nodes.NodeDescription AS node_NodeDescription, Nodes.Description AS node_Description, Nodes.DNS AS node_DNS, Nodes.SysName AS node_SysName, Nodes.Vendor AS node_Vendor, Nodes.SysObjectID AS node_SysObjectID, Nodes.Location AS node_Location, Nodes.Contact AS node_Contact, Nodes.VendorIcon AS node_VendorIcon, Nodes.Icon AS node_Icon, Nodes.Status AS node_Status, Nodes.StatusLED AS node_StatusLED, Nodes.StatusDescription AS node_StatusDescription, Nodes.CustomStatus AS node_CustomStatus, Nodes.IOSImage AS node_IOSImage, Nodes.IOSVersion AS node_IOSVersion, Nodes.GroupStatus AS node_GroupStatus, Nodes.LastBoot AS node_LastBoot, Nodes.SystemUpTime AS node_SystemUpTime, Nodes.ResponseTime AS node_ResponseTime, Nodes.PercentLoss AS node_PercentLoss, Nodes.AvgResponseTime AS node_AvgResponseTime, Nodes.MinResponseTime AS node_MinResponseTime, Nodes.MaxResponseTime AS node_MaxResponseTime, Nodes.CPUCount AS node_CPUCount, Nodes.CPULoad AS node_CPULoad, Nodes.MemoryUsed AS node_MemoryUsed, Nodes.MemoryAvailable AS node_MemoryAvailable, Nodes.PercentMemoryUsed AS node_PercentMemoryUsed, Nodes.PercentMemoryAvailable AS node_PercentMemoryAvailable, Nodes.LastSync AS node_LastSync, Nodes.LastSystemUpTimePollUtc AS node_LastSystemUpTimePollUtc, Nodes.MachineType AS node_MachineType, Nodes.IsServer AS node_IsServer, Nodes.Severity AS node_Severity, Nodes.UiSeverity AS node_UiSeverity, Nodes.ChildStatus AS node_ChildStatus, Nodes.Allow64BitCounters AS node_Allow64BitCounters, Nodes.AgentPort AS node_AgentPort, Nodes.TotalMemory AS node_TotalMemory, Nodes.CMTS AS node_CMTS, Nodes.CustomPollerLastStatisticsPoll AS node_CustomPollerLastStatisticsPoll, Nodes.CustomPollerLastStatisticsPollSuccess AS node_CustomPollerLastStatisticsPollSuccess, Nodes.SNMPVersion AS node_SNMPVersion, Nodes.PollInterval AS node_PollInterval, Nodes.EngineID AS node_EngineID, Nodes.RediscoveryInterval AS node_RediscoveryInterval, Nodes.NextPoll AS node_NextPoll, Nodes.NextRediscovery AS node_NextRediscovery, Nodes.StatCollection AS node_StatCollection, Nodes.External AS node_External, Nodes.Community AS node_Community, Nodes.RWCommunity AS node_RWCommunity, Nodes.IP AS node_IP, Nodes.IPAddressGUID AS node_IPAddressGUID, Nodes.BlockUntil AS node_BlockUntil, Nodes.OrionIdPrefix AS node_OrionIdPrefix, Nodes.OrionIdColumn AS node_OrionIdColumn, Nodes.EntityType AS node_EntityType, Nodes.DetailsUrl AS node_DetailsUrl, Nodes.DisplayName AS node_DisplayName, Nodes.Category AS node_Category, Nodes.IsOrionServer AS node_IsOrionServer, Nodes.UnManaged AS node_UnManaged, Nodes.UnManageFrom AS node_UnManageFrom, Nodes.UnManageUntil AS node_UnManageUntil, Nodes.Image AS node_Image, Nodes.InstanceType AS node_InstanceType, Nodes.Uri AS node_Uri, Nodes.InstanceSiteId AS node_InstanceSiteId, HardwareInfo.Manufacturer AS hardware_Manufacturer, HardwareInfo.Model AS hardware_Model, HardwareInfo.ServiceTag AS hardware_ServiceTag, HardwareInfo.IsDisabled AS hardware_IsDisabled, HardwareInfo.CategoriesWithProblems AS hardware_CategoriesWithProblems, HardwareInfo.CategoriesWithStatus AS hardware_CategoriesWithStatus, HardwareInfo.FullyQualifiedName AS hardware_FullyQualifiedName, HardwareInfo.UnManaged AS hardware_UnManaged, HardwareInfo.StatusDescription AS hardware_StatusDescription, HardwareInfo.Description AS hardware_Description, HardwareInfo.StatusLED AS hardware_StatusLED, HardwareInfo.DisplayName AS hardware_DisplayName, ActiveAlerts.AlertID AS alert_AlertID, ActiveAlerts.AlertTime AS alert_AlertTime, ActiveAlerts.ObjectType AS alert_ObjectType, ActiveAlerts.ObjectID AS alert_ObjectID, ActiveAlerts.ObjectName AS alert_ObjectName, ActiveAlerts.EventMessage AS alert_EventMessage, ActiveAlerts.PropertyID AS alert_PropertyID, ActiveAlerts.Monitoredproperty AS alert_Monitoredproperty, ActiveAlerts.CurrentValue AS alert_CurrentValue, ActiveAlerts.TriggerValue AS alert_TriggerValue, ActiveAlerts.ResetValue AS alert_ResetValue, ActiveAlerts.EngineID AS alert_EngineID, ActiveAlerts.AlertNotes AS alert_AlertNotes, ActiveAlerts.ExpireTime AS alert_ExpireTime, ActiveAlerts.Description AS alert_Description, ActiveAlerts.InstanceType AS alert_InstanceType, ActiveAlerts.Uri AS alert_Uri, ActiveAlerts.InstanceSiteId AS alert_InstanceSiteId, Interfaces.InterfaceID AS interface_InterfaceID, Interfaces.ObjectSubType AS interface_ObjectSubType, Interfaces.Name AS interface_Name, Interfaces.TypeName AS interface_TypeName, Interfaces.TypeDescription AS interface_TypeDescription, Interfaces.Speed AS interface_Speed, Interfaces.MTU AS interface_MTU, Interfaces.PhysicalAddress AS interface_PhysicalAddress, Interfaces.AdminStatus AS interface_AdminStatus, Interfaces.OperStatus AS interface_OperStatus, Interfaces.StatusIcon AS interface_StatusIcon, Interfaces.InBandwidth AS interface_InBandwidth, Interfaces.OutBandwidth AS interface_OutBandwidth, Interfaces.Caption AS interface_Caption, Interfaces.FullName AS interface_FullName, Interfaces.Alias AS interface_Alias, Interfaces.IfName AS interface_IfName, Interfaces.CustomBandwidth AS interface_CustomBandwidth, Interfaces.InterfaceSpeed AS interface_InterfaceSpeed, Interfaces.InterfaceCaption AS interface_InterfaceCaption, Interfaces.MAC AS interface_MAC, Interfaces.InterfaceName AS interface_InterfaceName, Interfaces.InterfaceTypeName AS interface_InterfaceTypeName, Interfaces.InterfaceAlias AS interface_InterfaceAlias, Interfaces.InterfaceMTU AS interface_InterfaceMTU, Interfaces.InterfaceTypeDescription AS interface_InterfaceTypeDescription, Interfaces.DuplexMode AS interface_DuplexMode, Interfaces.Status AS interface_Status, Interfaces.Description AS interface_Description, Interfaces.StatusDescription AS interface_StatusDescription, Interfaces.UnManaged AS interface_UnManaged, Interfaces.DisplayName AS interface_DisplayName, NodesCustomProperties.CIDN AS custom_CIDN, NodesCustomProperties.CarriageType AS custom_CarriageType, NodesCustomProperties.AddressId AS custom_AddressId, NodesCustomProperties.DevAlias AS custom_DevAlias, NodesCustomProperties.DevClass AS custom_DevClass, NodesCustomProperties.DevStatus AS custom_DevStatus, NodesCustomProperties.SiteName AS custom_SiteName, NodesCustomProperties.SiteType AS custom_SiteType, NodesCustomProperties._3LC AS custom_3LC, NodesCustomProperties._3PPConnected AS custom_3PPConnected, NodesCustomProperties.IOM_BAU_Status AS custom_IOM_BAU_Status, NodesCustomProperties.CPUThreshold AS custom_MgmtClass, NodesCustomProperties.IndependenceStatus AS custom_IndependenceStatus, NodesCustomProperties.swCustomer AS custom_swCustomer, NodesCustomProperties.RoomNumber AS custom_RoomNumber, NodesCustomProperties.FloorNumber AS custom_FloorNumber, NodesCustomProperties.StreetNumber AS custom_StreetNumber, NodesCustomProperties.StreetName AS custom_StreetName, NodesCustomProperties.StreetType AS custom_StreetType, NodesCustomProperties.StreetSuffix AS custom_StreetSuffix, NodesCustomProperties.City AS custom_City, NodesCustomProperties.PostCode AS custom_PostCode, NodesCustomProperties.State AS custom_State, NodesCustomProperties.Country AS custom_Country, NodesCustomProperties.Latitude AS custom_Latitude, NodesCustomProperties.Longitude AS custom_Longitude, InterfacesCustomProperties.InterfaceAlertEnable AS custom_InterfaceAlertEnable, InterfacesCustomProperties.InterfaceCategory AS custom_InterfaceCategory, InterfacesCustomProperties.InterfaceRole AS custom_InterfaceRole FROM Orion.Nodes AS Nodes LEFT JOIN Orion.HardwareHealth.HardwareInfo AS HardwareInfo ON Nodes.NodeID = HardwareInfo.NodeID LEFT JOIN Orion.ActiveAlerts AS ActiveAlerts ON Nodes.NodeID = ActiveAlerts.NodeID LEFT JOIN Orion.NPM.Interfaces AS Interfaces ON Nodes.NodeID = Interfaces.NodeID LEFT JOIN Orion.NodesCustomProperties AS NodesCustomProperties ON Nodes.NodeID = NodesCustomProperties.NodeID LEFT JOIN Orion.NPM.InterfacesCustomProperties AS InterfacesCustomProperties ON Interfaces.InterfaceID = InterfacesCustomProperties.InterfaceID WHERE Nodes.NodeName = '${parameters.deviceName}'`\n});", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.SolarWindsUserName}:${config.SolarWindsPassword}`)}`\n});", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "SolarWindsCBA"}, {"name": "SolarWindsCCA", "active": true, "description": "SolarWinds Instance for CCA/CCEP", "apiType": "rest", "method": "get", "parameters": ["deviceName"], "baseUrl": "config.SolarWinds.ccaBaseURI", "uri": "'SolarWinds/InformationService/v3/Json/Query'", "queryParams": "new Object({\n query: `SELECT Nodes.NodeID AS node_NodeID, Nodes.ObjectSubType AS node_ObjectSubType, Nodes.IPAddress AS node_IPAddress, Nodes.IPAddressType AS node_IPAddressType, Nodes.DynamicIP AS node_DynamicIP, Nodes.Caption AS node_Caption, Nodes.NodeDescription AS node_NodeDescription, Nodes.Description AS node_Description, Nodes.DNS AS node_DNS, Nodes.SysName AS node_SysName, Nodes.Vendor AS node_Vendor, Nodes.SysObjectID AS node_SysObjectID, Nodes.Location AS node_Location, Nodes.Contact AS node_Contact, Nodes.VendorIcon AS node_VendorIcon, Nodes.Icon AS node_Icon, Nodes.Status AS node_Status, Nodes.StatusLED AS node_StatusLED, Nodes.StatusDescription AS node_StatusDescription, Nodes.CustomStatus AS node_CustomStatus, Nodes.IOSImage AS node_IOSImage, Nodes.IOSVersion AS node_IOSVersion, Nodes.GroupStatus AS node_GroupStatus, Nodes.LastBoot AS node_LastBoot, Nodes.SystemUpTime AS node_SystemUpTime, Nodes.ResponseTime AS node_ResponseTime, Nodes.PercentLoss AS node_PercentLoss, Nodes.AvgResponseTime AS node_AvgResponseTime, Nodes.MinResponseTime AS node_MinResponseTime, Nodes.MaxResponseTime AS node_MaxResponseTime, Nodes.CPUCount AS node_CPUCount, Nodes.CPULoad AS node_CPULoad, Nodes.MemoryUsed AS node_MemoryUsed, Nodes.MemoryAvailable AS node_MemoryAvailable, Nodes.PercentMemoryUsed AS node_PercentMemoryUsed, Nodes.PercentMemoryAvailable AS node_PercentMemoryAvailable, Nodes.LastSync AS node_LastSync, Nodes.LastSystemUpTimePollUtc AS node_LastSystemUpTimePollUtc, Nodes.MachineType AS node_MachineType, Nodes.IsServer AS node_IsServer, Nodes.Severity AS node_Severity, Nodes.UiSeverity AS node_UiSeverity, Nodes.ChildStatus AS node_ChildStatus, Nodes.Allow64BitCounters AS node_Allow64BitCounters, Nodes.AgentPort AS node_AgentPort, Nodes.TotalMemory AS node_TotalMemory, Nodes.CMTS AS node_CMTS, Nodes.CustomPollerLastStatisticsPoll AS node_CustomPollerLastStatisticsPoll, Nodes.CustomPollerLastStatisticsPollSuccess AS node_CustomPollerLastStatisticsPollSuccess, Nodes.SNMPVersion AS node_SNMPVersion, Nodes.PollInterval AS node_PollInterval, Nodes.EngineID AS node_EngineID, Nodes.RediscoveryInterval AS node_RediscoveryInterval, Nodes.NextPoll AS node_NextPoll, Nodes.NextRediscovery AS node_NextRediscovery, Nodes.StatCollection AS node_StatCollection, Nodes.External AS node_External, Nodes.Community AS node_Community, Nodes.RWCommunity AS node_RWCommunity, Nodes.IP AS node_IP, Nodes.IPAddressGUID AS node_IPAddressGUID, Nodes.BlockUntil AS node_BlockUntil, Nodes.OrionIdPrefix AS node_OrionIdPrefix, Nodes.OrionIdColumn AS node_OrionIdColumn, Nodes.EntityType AS node_EntityType, Nodes.DetailsUrl AS node_DetailsUrl, Nodes.DisplayName AS node_DisplayName, Nodes.Category AS node_Category, Nodes.IsOrionServer AS node_IsOrionServer, Nodes.UnManaged AS node_UnManaged, Nodes.UnManageFrom AS node_UnManageFrom, Nodes.UnManageUntil AS node_UnManageUntil, Nodes.Image AS node_Image, Nodes.InstanceType AS node_InstanceType, Nodes.Uri AS node_Uri, Nodes.InstanceSiteId AS node_InstanceSiteId, HardwareInfo.Manufacturer AS hardware_Manufacturer, HardwareInfo.Model AS hardware_Model, HardwareInfo.ServiceTag AS hardware_ServiceTag, HardwareInfo.IsDisabled AS hardware_IsDisabled, HardwareInfo.CategoriesWithProblems AS hardware_CategoriesWithProblems, HardwareInfo.CategoriesWithStatus AS hardware_CategoriesWithStatus, HardwareInfo.FullyQualifiedName AS hardware_FullyQualifiedName, HardwareInfo.UnManaged AS hardware_UnManaged, HardwareInfo.StatusDescription AS hardware_StatusDescription, HardwareInfo.Description AS hardware_Description, HardwareInfo.StatusLED AS hardware_StatusLED, HardwareInfo.DisplayName AS hardware_DisplayName, ActiveAlerts.AlertID AS alert_AlertID, ActiveAlerts.AlertTime AS alert_AlertTime, ActiveAlerts.ObjectType AS alert_ObjectType, ActiveAlerts.ObjectID AS alert_ObjectID, ActiveAlerts.ObjectName AS alert_ObjectName, ActiveAlerts.EventMessage AS alert_EventMessage, ActiveAlerts.PropertyID AS alert_PropertyID, ActiveAlerts.Monitoredproperty AS alert_Monitoredproperty, ActiveAlerts.CurrentValue AS alert_CurrentValue, ActiveAlerts.TriggerValue AS alert_TriggerValue, ActiveAlerts.ResetValue AS alert_ResetValue, ActiveAlerts.EngineID AS alert_EngineID, ActiveAlerts.AlertNotes AS alert_AlertNotes, ActiveAlerts.ExpireTime AS alert_ExpireTime, ActiveAlerts.Description AS alert_Description, ActiveAlerts.InstanceType AS alert_InstanceType, ActiveAlerts.Uri AS alert_Uri, ActiveAlerts.InstanceSiteId AS alert_InstanceSiteId, Interfaces.InterfaceID AS interface_InterfaceID, Interfaces.ObjectSubType AS interface_ObjectSubType, Interfaces.Name AS interface_Name, Interfaces.TypeName AS interface_TypeName, Interfaces.TypeDescription AS interface_TypeDescription, Interfaces.Speed AS interface_Speed, Interfaces.MTU AS interface_MTU, Interfaces.PhysicalAddress AS interface_PhysicalAddress, Interfaces.AdminStatus AS interface_AdminStatus, Interfaces.OperStatus AS interface_OperStatus, Interfaces.StatusIcon AS interface_StatusIcon, Interfaces.InBandwidth AS interface_InBandwidth, Interfaces.OutBandwidth AS interface_OutBandwidth, Interfaces.Caption AS interface_Caption, Interfaces.FullName AS interface_FullName, Interfaces.Alias AS interface_Alias, Interfaces.IfName AS interface_IfName, Interfaces.CustomBandwidth AS interface_CustomBandwidth, Interfaces.InterfaceSpeed AS interface_InterfaceSpeed, Interfaces.InterfaceCaption AS interface_InterfaceCaption, Interfaces.MAC AS interface_MAC, Interfaces.InterfaceName AS interface_InterfaceName, Interfaces.InterfaceTypeName AS interface_InterfaceTypeName, Interfaces.InterfaceAlias AS interface_InterfaceAlias, Interfaces.InterfaceMTU AS interface_InterfaceMTU, Interfaces.InterfaceTypeDescription AS interface_InterfaceTypeDescription, Interfaces.DuplexMode AS interface_DuplexMode, Interfaces.Status AS interface_Status, Interfaces.Description AS interface_Description, Interfaces.StatusDescription AS interface_StatusDescription, Interfaces.UnManaged AS interface_UnManaged, Interfaces.DisplayName AS interface_DisplayName, NodesCustomProperties.CIDN AS custom_CIDN, NodesCustomProperties.CarriageType AS custom_CarriageType, NodesCustomProperties.AddressId AS custom_AddressId, NodesCustomProperties.DevAlias AS custom_DevAlias, NodesCustomProperties.DevClass AS custom_DevClass, NodesCustomProperties.DevStatus AS custom_DevStatus, NodesCustomProperties.SiteName AS custom_SiteName, NodesCustomProperties.SiteType AS custom_SiteType, NodesCustomProperties._3LC AS custom_3LC, NodesCustomProperties._3PPConnected AS custom_3PPConnected, NodesCustomProperties.IOM_BAU_Status AS custom_IOM_BAU_Status, NodesCustomProperties.CPUThreshold AS custom_MgmtClass, NodesCustomProperties.IndependenceStatus AS custom_IndependenceStatus, NodesCustomProperties.swCustomer AS custom_swCustomer, NodesCustomProperties.RoomNumber AS custom_RoomNumber, NodesCustomProperties.FloorNumber AS custom_FloorNumber, NodesCustomProperties.StreetNumber AS custom_StreetNumber, NodesCustomProperties.StreetName AS custom_StreetName, NodesCustomProperties.StreetType AS custom_StreetType, NodesCustomProperties.StreetSuffix AS custom_StreetSuffix, NodesCustomProperties.City AS custom_City, NodesCustomProperties.PostCode AS custom_PostCode, NodesCustomProperties.State AS custom_State, NodesCustomProperties.Country AS custom_Country, NodesCustomProperties.Latitude AS custom_Latitude, NodesCustomProperties.Longitude AS custom_Longitude, InterfacesCustomProperties.InterfaceAlertEnable AS custom_InterfaceAlertEnable, InterfacesCustomProperties.InterfaceCategory AS custom_InterfaceCategory, InterfacesCustomProperties.InterfaceRole AS custom_InterfaceRole FROM Orion.Nodes AS Nodes LEFT JOIN Orion.HardwareHealth.HardwareInfo AS HardwareInfo ON Nodes.NodeID = HardwareInfo.NodeID LEFT JOIN Orion.ActiveAlerts AS ActiveAlerts ON Nodes.NodeID = ActiveAlerts.NodeID LEFT JOIN Orion.NPM.Interfaces AS Interfaces ON Nodes.NodeID = Interfaces.NodeID LEFT JOIN Orion.NodesCustomProperties AS NodesCustomProperties ON Nodes.NodeID = NodesCustomProperties.NodeID LEFT JOIN Orion.NPM.InterfacesCustomProperties AS InterfacesCustomProperties ON Interfaces.InterfaceID = InterfacesCustomProperties.InterfaceID WHERE Nodes.NodeName = '${parameters.deviceName}'`\n});", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.SolarWindsUserName}:${config.SolarWindsPassword}`)}`\n});", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "SolarWindsCCA"}, {"name": "SolarWindsChevron", "active": true, "description": "SolarWinds Instance for Chevron", "apiType": "rest", "method": "get", "parameters": ["deviceName"], "baseUrl": "config.SolarWinds.chevBaseURI", "uri": "'SolarWinds/InformationService/v3/Json/Query'", "queryParams": "new Object({\n query: `SELECT Nodes.NodeID AS node_NodeID, Nodes.ObjectSubType AS node_ObjectSubType, Nodes.IPAddress AS node_IPAddress, Nodes.IPAddressType AS node_IPAddressType, Nodes.DynamicIP AS node_DynamicIP, Nodes.Caption AS node_Caption, Nodes.NodeDescription AS node_NodeDescription, Nodes.Description AS node_Description, Nodes.DNS AS node_DNS, Nodes.SysName AS node_SysName, Nodes.Vendor AS node_Vendor, Nodes.SysObjectID AS node_SysObjectID, Nodes.Location AS node_Location, Nodes.Contact AS node_Contact, Nodes.VendorIcon AS node_VendorIcon, Nodes.Icon AS node_Icon, Nodes.Status AS node_Status, Nodes.StatusLED AS node_StatusLED, Nodes.StatusDescription AS node_StatusDescription, Nodes.CustomStatus AS node_CustomStatus, Nodes.IOSImage AS node_IOSImage, Nodes.IOSVersion AS node_IOSVersion, Nodes.GroupStatus AS node_GroupStatus, Nodes.LastBoot AS node_LastBoot, Nodes.SystemUpTime AS node_SystemUpTime, Nodes.ResponseTime AS node_ResponseTime, Nodes.PercentLoss AS node_PercentLoss, Nodes.AvgResponseTime AS node_AvgResponseTime, Nodes.MinResponseTime AS node_MinResponseTime, Nodes.MaxResponseTime AS node_MaxResponseTime, Nodes.CPUCount AS node_CPUCount, Nodes.CPULoad AS node_CPULoad, Nodes.MemoryUsed AS node_MemoryUsed, Nodes.MemoryAvailable AS node_MemoryAvailable, Nodes.PercentMemoryUsed AS node_PercentMemoryUsed, Nodes.PercentMemoryAvailable AS node_PercentMemoryAvailable, Nodes.LastSync AS node_LastSync, Nodes.LastSystemUpTimePollUtc AS node_LastSystemUpTimePollUtc, Nodes.MachineType AS node_MachineType, Nodes.IsServer AS node_IsServer, Nodes.Severity AS node_Severity, Nodes.UiSeverity AS node_UiSeverity, Nodes.ChildStatus AS node_ChildStatus, Nodes.Allow64BitCounters AS node_Allow64BitCounters, Nodes.AgentPort AS node_AgentPort, Nodes.TotalMemory AS node_TotalMemory, Nodes.CMTS AS node_CMTS, Nodes.CustomPollerLastStatisticsPoll AS node_CustomPollerLastStatisticsPoll, Nodes.CustomPollerLastStatisticsPollSuccess AS node_CustomPollerLastStatisticsPollSuccess, Nodes.SNMPVersion AS node_SNMPVersion, Nodes.PollInterval AS node_PollInterval, Nodes.EngineID AS node_EngineID, Nodes.RediscoveryInterval AS node_RediscoveryInterval, Nodes.NextPoll AS node_NextPoll, Nodes.NextRediscovery AS node_NextRediscovery, Nodes.StatCollection AS node_StatCollection, Nodes.External AS node_External, Nodes.Community AS node_Community, Nodes.RWCommunity AS node_RWCommunity, Nodes.IP AS node_IP, Nodes.IPAddressGUID AS node_IPAddressGUID, Nodes.BlockUntil AS node_BlockUntil, Nodes.OrionIdPrefix AS node_OrionIdPrefix, Nodes.OrionIdColumn AS node_OrionIdColumn, Nodes.EntityType AS node_EntityType, Nodes.DetailsUrl AS node_DetailsUrl, Nodes.DisplayName AS node_DisplayName, Nodes.Category AS node_Category, Nodes.IsOrionServer AS node_IsOrionServer, Nodes.UnManaged AS node_UnManaged, Nodes.UnManageFrom AS node_UnManageFrom, Nodes.UnManageUntil AS node_UnManageUntil, Nodes.Image AS node_Image, Nodes.InstanceType AS node_InstanceType, Nodes.Uri AS node_Uri, Nodes.InstanceSiteId AS node_InstanceSiteId, HardwareInfo.Manufacturer AS hardware_Manufacturer, HardwareInfo.Model AS hardware_Model, HardwareInfo.ServiceTag AS hardware_ServiceTag, HardwareInfo.IsDisabled AS hardware_IsDisabled, HardwareInfo.CategoriesWithProblems AS hardware_CategoriesWithProblems, HardwareInfo.CategoriesWithStatus AS hardware_CategoriesWithStatus, HardwareInfo.FullyQualifiedName AS hardware_FullyQualifiedName, HardwareInfo.UnManaged AS hardware_UnManaged, HardwareInfo.StatusDescription AS hardware_StatusDescription, HardwareInfo.Description AS hardware_Description, HardwareInfo.StatusLED AS hardware_StatusLED, HardwareInfo.DisplayName AS hardware_DisplayName, ActiveAlerts.AlertID AS alert_AlertID, ActiveAlerts.AlertTime AS alert_AlertTime, ActiveAlerts.ObjectType AS alert_ObjectType, ActiveAlerts.ObjectID AS alert_ObjectID, ActiveAlerts.ObjectName AS alert_ObjectName, ActiveAlerts.EventMessage AS alert_EventMessage, ActiveAlerts.PropertyID AS alert_PropertyID, ActiveAlerts.Monitoredproperty AS alert_Monitoredproperty, ActiveAlerts.CurrentValue AS alert_CurrentValue, ActiveAlerts.TriggerValue AS alert_TriggerValue, ActiveAlerts.ResetValue AS alert_ResetValue, ActiveAlerts.EngineID AS alert_EngineID, ActiveAlerts.AlertNotes AS alert_AlertNotes, ActiveAlerts.ExpireTime AS alert_ExpireTime, ActiveAlerts.Description AS alert_Description, ActiveAlerts.InstanceType AS alert_InstanceType, ActiveAlerts.Uri AS alert_Uri, ActiveAlerts.InstanceSiteId AS alert_InstanceSiteId, Interfaces.InterfaceID AS interface_InterfaceID, Interfaces.ObjectSubType AS interface_ObjectSubType, Interfaces.Name AS interface_Name, Interfaces.TypeName AS interface_TypeName, Interfaces.TypeDescription AS interface_TypeDescription, Interfaces.Speed AS interface_Speed, Interfaces.MTU AS interface_MTU, Interfaces.PhysicalAddress AS interface_PhysicalAddress, Interfaces.AdminStatus AS interface_AdminStatus, Interfaces.OperStatus AS interface_OperStatus, Interfaces.StatusIcon AS interface_StatusIcon, Interfaces.InBandwidth AS interface_InBandwidth, Interfaces.OutBandwidth AS interface_OutBandwidth, Interfaces.Caption AS interface_Caption, Interfaces.FullName AS interface_FullName, Interfaces.Alias AS interface_Alias, Interfaces.IfName AS interface_IfName, Interfaces.CustomBandwidth AS interface_CustomBandwidth, Interfaces.InterfaceSpeed AS interface_InterfaceSpeed, Interfaces.InterfaceCaption AS interface_InterfaceCaption, Interfaces.MAC AS interface_MAC, Interfaces.InterfaceName AS interface_InterfaceName, Interfaces.InterfaceTypeName AS interface_InterfaceTypeName, Interfaces.InterfaceAlias AS interface_InterfaceAlias, Interfaces.InterfaceMTU AS interface_InterfaceMTU, Interfaces.InterfaceTypeDescription AS interface_InterfaceTypeDescription, Interfaces.DuplexMode AS interface_DuplexMode, Interfaces.Status AS interface_Status, Interfaces.Description AS interface_Description, Interfaces.StatusDescription AS interface_StatusDescription, Interfaces.UnManaged AS interface_UnManaged, Interfaces.DisplayName AS interface_DisplayName, NodesCustomProperties.CIDN AS custom_CIDN, NodesCustomProperties.CarriageType AS custom_CarriageType, NodesCustomProperties.AddressId AS custom_AddressId, NodesCustomProperties.DevAlias AS custom_DevAlias, NodesCustomProperties.DevClass AS custom_DevClass, NodesCustomProperties.DevStatus AS custom_DevStatus, NodesCustomProperties.SiteName AS custom_SiteName, NodesCustomProperties.SiteType AS custom_SiteType, NodesCustomProperties._3LC AS custom_3LC, NodesCustomProperties._3PPConnected AS custom_3PPConnected, NodesCustomProperties.IOM_BAU_Status AS custom_IOM_BAU_Status, NodesCustomProperties.CPUThreshold AS custom_MgmtClass, NodesCustomProperties.IndependenceStatus AS custom_IndependenceStatus, NodesCustomProperties.swCustomer AS custom_swCustomer, NodesCustomProperties.RoomNumber AS custom_RoomNumber, NodesCustomProperties.FloorNumber AS custom_FloorNumber, NodesCustomProperties.StreetNumber AS custom_StreetNumber, NodesCustomProperties.StreetName AS custom_StreetName, NodesCustomProperties.StreetType AS custom_StreetType, NodesCustomProperties.StreetSuffix AS custom_StreetSuffix, NodesCustomProperties.City AS custom_City, NodesCustomProperties.PostCode AS custom_PostCode, NodesCustomProperties.State AS custom_State, NodesCustomProperties.Country AS custom_Country, NodesCustomProperties.Latitude AS custom_Latitude, NodesCustomProperties.Longitude AS custom_Longitude, InterfacesCustomProperties.InterfaceAlertEnable AS custom_InterfaceAlertEnable, InterfacesCustomProperties.InterfaceCategory AS custom_InterfaceCategory, InterfacesCustomProperties.InterfaceRole AS custom_InterfaceRole FROM Orion.Nodes AS Nodes LEFT JOIN Orion.HardwareHealth.HardwareInfo AS HardwareInfo ON Nodes.NodeID = HardwareInfo.NodeID LEFT JOIN Orion.ActiveAlerts AS ActiveAlerts ON Nodes.NodeID = ActiveAlerts.NodeID LEFT JOIN Orion.NPM.Interfaces AS Interfaces ON Nodes.NodeID = Interfaces.NodeID LEFT JOIN Orion.NodesCustomProperties AS NodesCustomProperties ON Nodes.NodeID = NodesCustomProperties.NodeID LEFT JOIN Orion.NPM.InterfacesCustomProperties AS InterfacesCustomProperties ON Interfaces.InterfaceID = InterfacesCustomProperties.InterfaceID WHERE Nodes.NodeName = '${parameters.deviceName}'`\n});", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.SolarWindsUserName}:${config.SolarWindsPassword}`)}`\n});", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "SolarWindsChevron"}, {"name": "SolarWindsDFAT", "active": true, "description": "SolarWinds Instance for DFAT", "apiType": "rest", "method": "get", "parameters": ["deviceName"], "baseUrl": "config.SolarWinds.dfatBaseURI", "uri": "'SolarWinds/InformationService/v3/Json/Query'", "queryParams": "new Object({\n query: `SELECT Nodes.NodeID AS node_NodeID, Nodes.ObjectSubType AS node_ObjectSubType, Nodes.IPAddress AS node_IPAddress, Nodes.IPAddressType AS node_IPAddressType, Nodes.DynamicIP AS node_DynamicIP, Nodes.Caption AS node_Caption, Nodes.NodeDescription AS node_NodeDescription, Nodes.Description AS node_Description, Nodes.DNS AS node_DNS, Nodes.SysName AS node_SysName, Nodes.Vendor AS node_Vendor, Nodes.SysObjectID AS node_SysObjectID, Nodes.Location AS node_Location, Nodes.Contact AS node_Contact, Nodes.VendorIcon AS node_VendorIcon, Nodes.Icon AS node_Icon, Nodes.Status AS node_Status, Nodes.StatusLED AS node_StatusLED, Nodes.StatusDescription AS node_StatusDescription, Nodes.CustomStatus AS node_CustomStatus, Nodes.IOSImage AS node_IOSImage, Nodes.IOSVersion AS node_IOSVersion, Nodes.GroupStatus AS node_GroupStatus, Nodes.LastBoot AS node_LastBoot, Nodes.SystemUpTime AS node_SystemUpTime, Nodes.ResponseTime AS node_ResponseTime, Nodes.PercentLoss AS node_PercentLoss, Nodes.AvgResponseTime AS node_AvgResponseTime, Nodes.MinResponseTime AS node_MinResponseTime, Nodes.MaxResponseTime AS node_MaxResponseTime, Nodes.CPUCount AS node_CPUCount, Nodes.CPULoad AS node_CPULoad, Nodes.MemoryUsed AS node_MemoryUsed, Nodes.MemoryAvailable AS node_MemoryAvailable, Nodes.PercentMemoryUsed AS node_PercentMemoryUsed, Nodes.PercentMemoryAvailable AS node_PercentMemoryAvailable, Nodes.LastSync AS node_LastSync, Nodes.LastSystemUpTimePollUtc AS node_LastSystemUpTimePollUtc, Nodes.MachineType AS node_MachineType, Nodes.IsServer AS node_IsServer, Nodes.Severity AS node_Severity, Nodes.UiSeverity AS node_UiSeverity, Nodes.ChildStatus AS node_ChildStatus, Nodes.Allow64BitCounters AS node_Allow64BitCounters, Nodes.AgentPort AS node_AgentPort, Nodes.TotalMemory AS node_TotalMemory, Nodes.CMTS AS node_CMTS, Nodes.CustomPollerLastStatisticsPoll AS node_CustomPollerLastStatisticsPoll, Nodes.CustomPollerLastStatisticsPollSuccess AS node_CustomPollerLastStatisticsPollSuccess, Nodes.SNMPVersion AS node_SNMPVersion, Nodes.PollInterval AS node_PollInterval, Nodes.EngineID AS node_EngineID, Nodes.RediscoveryInterval AS node_RediscoveryInterval, Nodes.NextPoll AS node_NextPoll, Nodes.NextRediscovery AS node_NextRediscovery, Nodes.StatCollection AS node_StatCollection, Nodes.External AS node_External, Nodes.Community AS node_Community, Nodes.RWCommunity AS node_RWCommunity, Nodes.IP AS node_IP, Nodes.IPAddressGUID AS node_IPAddressGUID, Nodes.BlockUntil AS node_BlockUntil, Nodes.OrionIdPrefix AS node_OrionIdPrefix, Nodes.OrionIdColumn AS node_OrionIdColumn, Nodes.EntityType AS node_EntityType, Nodes.DetailsUrl AS node_DetailsUrl, Nodes.DisplayName AS node_DisplayName, Nodes.Category AS node_Category, Nodes.IsOrionServer AS node_IsOrionServer, Nodes.UnManaged AS node_UnManaged, Nodes.UnManageFrom AS node_UnManageFrom, Nodes.UnManageUntil AS node_UnManageUntil, Nodes.Image AS node_Image, Nodes.InstanceType AS node_InstanceType, Nodes.Uri AS node_Uri, Nodes.InstanceSiteId AS node_InstanceSiteId, HardwareInfo.Manufacturer AS hardware_Manufacturer, HardwareInfo.Model AS hardware_Model, HardwareInfo.ServiceTag AS hardware_ServiceTag, HardwareInfo.IsDisabled AS hardware_IsDisabled, HardwareInfo.CategoriesWithProblems AS hardware_CategoriesWithProblems, HardwareInfo.CategoriesWithStatus AS hardware_CategoriesWithStatus, HardwareInfo.FullyQualifiedName AS hardware_FullyQualifiedName, HardwareInfo.UnManaged AS hardware_UnManaged, HardwareInfo.StatusDescription AS hardware_StatusDescription, HardwareInfo.Description AS hardware_Description, HardwareInfo.StatusLED AS hardware_StatusLED, HardwareInfo.DisplayName AS hardware_DisplayName, ActiveAlerts.AlertID AS alert_AlertID, ActiveAlerts.AlertTime AS alert_AlertTime, ActiveAlerts.ObjectType AS alert_ObjectType, ActiveAlerts.ObjectID AS alert_ObjectID, ActiveAlerts.ObjectName AS alert_ObjectName, ActiveAlerts.EventMessage AS alert_EventMessage, ActiveAlerts.PropertyID AS alert_PropertyID, ActiveAlerts.Monitoredproperty AS alert_Monitoredproperty, ActiveAlerts.CurrentValue AS alert_CurrentValue, ActiveAlerts.TriggerValue AS alert_TriggerValue, ActiveAlerts.ResetValue AS alert_ResetValue, ActiveAlerts.EngineID AS alert_EngineID, ActiveAlerts.AlertNotes AS alert_AlertNotes, ActiveAlerts.ExpireTime AS alert_ExpireTime, ActiveAlerts.Description AS alert_Description, ActiveAlerts.InstanceType AS alert_InstanceType, ActiveAlerts.Uri AS alert_Uri, ActiveAlerts.InstanceSiteId AS alert_InstanceSiteId, Interfaces.InterfaceID AS interface_InterfaceID, Interfaces.ObjectSubType AS interface_ObjectSubType, Interfaces.Name AS interface_Name, Interfaces.TypeName AS interface_TypeName, Interfaces.TypeDescription AS interface_TypeDescription, Interfaces.Speed AS interface_Speed, Interfaces.MTU AS interface_MTU, Interfaces.PhysicalAddress AS interface_PhysicalAddress, Interfaces.AdminStatus AS interface_AdminStatus, Interfaces.OperStatus AS interface_OperStatus, Interfaces.StatusIcon AS interface_StatusIcon, Interfaces.InBandwidth AS interface_InBandwidth, Interfaces.OutBandwidth AS interface_OutBandwidth, Interfaces.Caption AS interface_Caption, Interfaces.FullName AS interface_FullName, Interfaces.Alias AS interface_Alias, Interfaces.IfName AS interface_IfName, Interfaces.CustomBandwidth AS interface_CustomBandwidth, Interfaces.InterfaceSpeed AS interface_InterfaceSpeed, Interfaces.InterfaceCaption AS interface_InterfaceCaption, Interfaces.MAC AS interface_MAC, Interfaces.InterfaceName AS interface_InterfaceName, Interfaces.InterfaceTypeName AS interface_InterfaceTypeName, Interfaces.InterfaceAlias AS interface_InterfaceAlias, Interfaces.InterfaceMTU AS interface_InterfaceMTU, Interfaces.InterfaceTypeDescription AS interface_InterfaceTypeDescription, Interfaces.DuplexMode AS interface_DuplexMode, Interfaces.Status AS interface_Status, Interfaces.Description AS interface_Description, Interfaces.StatusDescription AS interface_StatusDescription, Interfaces.UnManaged AS interface_UnManaged, Interfaces.DisplayName AS interface_DisplayName, NodesCustomProperties.CIDN AS custom_CIDN, NodesCustomProperties.CarriageType AS custom_CarriageType, NodesCustomProperties.AddressId AS custom_AddressId, NodesCustomProperties.DevAlias AS custom_DevAlias, NodesCustomProperties.DevClass AS custom_DevClass, NodesCustomProperties.DevStatus AS custom_DevStatus, NodesCustomProperties.SiteName AS custom_SiteName, NodesCustomProperties.SiteType AS custom_SiteType, NodesCustomProperties._3LC AS custom_3LC, NodesCustomProperties._3PPConnected AS custom_3PPConnected, NodesCustomProperties.IOM_BAU_Status AS custom_IOM_BAU_Status, NodesCustomProperties.CPUThreshold AS custom_MgmtClass, NodesCustomProperties.IndependenceStatus AS custom_IndependenceStatus, NodesCustomProperties.swCustomer AS custom_swCustomer, NodesCustomProperties.RoomNumber AS custom_RoomNumber, NodesCustomProperties.FloorNumber AS custom_FloorNumber, NodesCustomProperties.StreetNumber AS custom_StreetNumber, NodesCustomProperties.StreetName AS custom_StreetName, NodesCustomProperties.StreetType AS custom_StreetType, NodesCustomProperties.StreetSuffix AS custom_StreetSuffix, NodesCustomProperties.City AS custom_City, NodesCustomProperties.PostCode AS custom_PostCode, NodesCustomProperties.State AS custom_State, NodesCustomProperties.Country AS custom_Country, NodesCustomProperties.Latitude AS custom_Latitude, NodesCustomProperties.Longitude AS custom_Longitude, InterfacesCustomProperties.InterfaceAlertEnable AS custom_InterfaceAlertEnable, InterfacesCustomProperties.InterfaceCategory AS custom_InterfaceCategory, InterfacesCustomProperties.InterfaceRole AS custom_InterfaceRole FROM Orion.Nodes AS Nodes LEFT JOIN Orion.HardwareHealth.HardwareInfo AS HardwareInfo ON Nodes.NodeID = HardwareInfo.NodeID LEFT JOIN Orion.ActiveAlerts AS ActiveAlerts ON Nodes.NodeID = ActiveAlerts.NodeID LEFT JOIN Orion.NPM.Interfaces AS Interfaces ON Nodes.NodeID = Interfaces.NodeID LEFT JOIN Orion.NodesCustomProperties AS NodesCustomProperties ON Nodes.NodeID = NodesCustomProperties.NodeID LEFT JOIN Orion.NPM.InterfacesCustomProperties AS InterfacesCustomProperties ON Interfaces.InterfaceID = InterfacesCustomProperties.InterfaceID WHERE Nodes.NodeName = '${parameters.deviceName}'`\n});", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.SolarWindsUserName}:${config.SolarWindsPassword}`)}`\n});", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "SolarWindsDFAT"}, {"name": "SolarWindsDowner", "active": true, "description": "SolarWinds Instance for Downer(DCP)", "apiType": "rest", "method": "get", "parameters": ["deviceName"], "baseUrl": "config.SolarWinds.downerBaseURI", "uri": "'SolarWinds/InformationService/v3/Json/Query'", "queryParams": "new Object({\n query: `SELECT Nodes.NodeID AS node_NodeID, Nodes.ObjectSubType AS node_ObjectSubType, Nodes.IPAddress AS node_IPAddress, Nodes.IPAddressType AS node_IPAddressType, Nodes.DynamicIP AS node_DynamicIP, Nodes.Caption AS node_Caption, Nodes.NodeDescription AS node_NodeDescription, Nodes.Description AS node_Description, Nodes.DNS AS node_DNS, Nodes.SysName AS node_SysName, Nodes.Vendor AS node_Vendor, Nodes.SysObjectID AS node_SysObjectID, Nodes.Location AS node_Location, Nodes.Contact AS node_Contact, Nodes.VendorIcon AS node_VendorIcon, Nodes.Icon AS node_Icon, Nodes.Status AS node_Status, Nodes.StatusLED AS node_StatusLED, Nodes.StatusDescription AS node_StatusDescription, Nodes.CustomStatus AS node_CustomStatus, Nodes.IOSImage AS node_IOSImage, Nodes.IOSVersion AS node_IOSVersion, Nodes.GroupStatus AS node_GroupStatus, Nodes.LastBoot AS node_LastBoot, Nodes.SystemUpTime AS node_SystemUpTime, Nodes.ResponseTime AS node_ResponseTime, Nodes.PercentLoss AS node_PercentLoss, Nodes.AvgResponseTime AS node_AvgResponseTime, Nodes.MinResponseTime AS node_MinResponseTime, Nodes.MaxResponseTime AS node_MaxResponseTime, Nodes.CPUCount AS node_CPUCount, Nodes.CPULoad AS node_CPULoad, Nodes.MemoryUsed AS node_MemoryUsed, Nodes.MemoryAvailable AS node_MemoryAvailable, Nodes.PercentMemoryUsed AS node_PercentMemoryUsed, Nodes.PercentMemoryAvailable AS node_PercentMemoryAvailable, Nodes.LastSync AS node_LastSync, Nodes.LastSystemUpTimePollUtc AS node_LastSystemUpTimePollUtc, Nodes.MachineType AS node_MachineType, Nodes.IsServer AS node_IsServer, Nodes.Severity AS node_Severity, Nodes.UiSeverity AS node_UiSeverity, Nodes.ChildStatus AS node_ChildStatus, Nodes.Allow64BitCounters AS node_Allow64BitCounters, Nodes.AgentPort AS node_AgentPort, Nodes.TotalMemory AS node_TotalMemory, Nodes.CMTS AS node_CMTS, Nodes.CustomPollerLastStatisticsPoll AS node_CustomPollerLastStatisticsPoll, Nodes.CustomPollerLastStatisticsPollSuccess AS node_CustomPollerLastStatisticsPollSuccess, Nodes.SNMPVersion AS node_SNMPVersion, Nodes.PollInterval AS node_PollInterval, Nodes.EngineID AS node_EngineID, Nodes.RediscoveryInterval AS node_RediscoveryInterval, Nodes.NextPoll AS node_NextPoll, Nodes.NextRediscovery AS node_NextRediscovery, Nodes.StatCollection AS node_StatCollection, Nodes.External AS node_External, Nodes.Community AS node_Community, Nodes.RWCommunity AS node_RWCommunity, Nodes.IP AS node_IP, Nodes.IPAddressGUID AS node_IPAddressGUID, Nodes.BlockUntil AS node_BlockUntil, Nodes.OrionIdPrefix AS node_OrionIdPrefix, Nodes.OrionIdColumn AS node_OrionIdColumn, Nodes.EntityType AS node_EntityType, Nodes.DetailsUrl AS node_DetailsUrl, Nodes.DisplayName AS node_DisplayName, Nodes.Category AS node_Category, Nodes.IsOrionServer AS node_IsOrionServer, Nodes.UnManaged AS node_UnManaged, Nodes.UnManageFrom AS node_UnManageFrom, Nodes.UnManageUntil AS node_UnManageUntil, Nodes.Image AS node_Image, Nodes.InstanceType AS node_InstanceType, Nodes.Uri AS node_Uri, Nodes.InstanceSiteId AS node_InstanceSiteId, HardwareInfo.Manufacturer AS hardware_Manufacturer, HardwareInfo.Model AS hardware_Model, HardwareInfo.ServiceTag AS hardware_ServiceTag, HardwareInfo.IsDisabled AS hardware_IsDisabled, HardwareInfo.CategoriesWithProblems AS hardware_CategoriesWithProblems, HardwareInfo.CategoriesWithStatus AS hardware_CategoriesWithStatus, HardwareInfo.FullyQualifiedName AS hardware_FullyQualifiedName, HardwareInfo.UnManaged AS hardware_UnManaged, HardwareInfo.StatusDescription AS hardware_StatusDescription, HardwareInfo.Description AS hardware_Description, HardwareInfo.StatusLED AS hardware_StatusLED, HardwareInfo.DisplayName AS hardware_DisplayName, ActiveAlerts.AlertID AS alert_AlertID, ActiveAlerts.AlertTime AS alert_AlertTime, ActiveAlerts.ObjectType AS alert_ObjectType, ActiveAlerts.ObjectID AS alert_ObjectID, ActiveAlerts.ObjectName AS alert_ObjectName, ActiveAlerts.EventMessage AS alert_EventMessage, ActiveAlerts.PropertyID AS alert_PropertyID, ActiveAlerts.Monitoredproperty AS alert_Monitoredproperty, ActiveAlerts.CurrentValue AS alert_CurrentValue, ActiveAlerts.TriggerValue AS alert_TriggerValue, ActiveAlerts.ResetValue AS alert_ResetValue, ActiveAlerts.EngineID AS alert_EngineID, ActiveAlerts.AlertNotes AS alert_AlertNotes, ActiveAlerts.ExpireTime AS alert_ExpireTime, ActiveAlerts.Description AS alert_Description, ActiveAlerts.InstanceType AS alert_InstanceType, ActiveAlerts.Uri AS alert_Uri, ActiveAlerts.InstanceSiteId AS alert_InstanceSiteId, Interfaces.InterfaceID AS interface_InterfaceID, Interfaces.ObjectSubType AS interface_ObjectSubType, Interfaces.Name AS interface_Name, Interfaces.TypeName AS interface_TypeName, Interfaces.TypeDescription AS interface_TypeDescription, Interfaces.Speed AS interface_Speed, Interfaces.MTU AS interface_MTU, Interfaces.PhysicalAddress AS interface_PhysicalAddress, Interfaces.AdminStatus AS interface_AdminStatus, Interfaces.OperStatus AS interface_OperStatus, Interfaces.StatusIcon AS interface_StatusIcon, Interfaces.InBandwidth AS interface_InBandwidth, Interfaces.OutBandwidth AS interface_OutBandwidth, Interfaces.Caption AS interface_Caption, Interfaces.FullName AS interface_FullName, Interfaces.Alias AS interface_Alias, Interfaces.IfName AS interface_IfName, Interfaces.CustomBandwidth AS interface_CustomBandwidth, Interfaces.InterfaceSpeed AS interface_InterfaceSpeed, Interfaces.InterfaceCaption AS interface_InterfaceCaption, Interfaces.MAC AS interface_MAC, Interfaces.InterfaceName AS interface_InterfaceName, Interfaces.InterfaceTypeName AS interface_InterfaceTypeName, Interfaces.InterfaceAlias AS interface_InterfaceAlias, Interfaces.InterfaceMTU AS interface_InterfaceMTU, Interfaces.InterfaceTypeDescription AS interface_InterfaceTypeDescription, Interfaces.DuplexMode AS interface_DuplexMode, Interfaces.Status AS interface_Status, Interfaces.Description AS interface_Description, Interfaces.StatusDescription AS interface_StatusDescription, Interfaces.UnManaged AS interface_UnManaged, Interfaces.DisplayName AS interface_DisplayName, NodesCustomProperties.CIDN AS custom_CIDN, NodesCustomProperties.CarriageType AS custom_CarriageType, NodesCustomProperties.AddressId AS custom_AddressId, NodesCustomProperties.DevAlias AS custom_DevAlias, NodesCustomProperties.DevClass AS custom_DevClass, NodesCustomProperties.DevStatus AS custom_DevStatus, NodesCustomProperties.SiteName AS custom_SiteName, NodesCustomProperties.SiteType AS custom_SiteType, NodesCustomProperties._3LC AS custom_3LC, NodesCustomProperties._3PPConnected AS custom_3PPConnected, NodesCustomProperties.IOM_BAU_Status AS custom_IOM_BAU_Status, NodesCustomProperties.CPUThreshold AS custom_MgmtClass, NodesCustomProperties.IndependenceStatus AS custom_IndependenceStatus, NodesCustomProperties.swCustomer AS custom_swCustomer, NodesCustomProperties.RoomNumber AS custom_RoomNumber, NodesCustomProperties.FloorNumber AS custom_FloorNumber, NodesCustomProperties.StreetNumber AS custom_StreetNumber, NodesCustomProperties.StreetName AS custom_StreetName, NodesCustomProperties.StreetType AS custom_StreetType, NodesCustomProperties.StreetSuffix AS custom_StreetSuffix, NodesCustomProperties.City AS custom_City, NodesCustomProperties.PostCode AS custom_PostCode, NodesCustomProperties.State AS custom_State, NodesCustomProperties.Country AS custom_Country, NodesCustomProperties.Latitude AS custom_Latitude, NodesCustomProperties.Longitude AS custom_Longitude, InterfacesCustomProperties.InterfaceAlertEnable AS custom_InterfaceAlertEnable, InterfacesCustomProperties.InterfaceCategory AS custom_InterfaceCategory, InterfacesCustomProperties.InterfaceRole AS custom_InterfaceRole FROM Orion.Nodes AS Nodes LEFT JOIN Orion.HardwareHealth.HardwareInfo AS HardwareInfo ON Nodes.NodeID = HardwareInfo.NodeID LEFT JOIN Orion.ActiveAlerts AS ActiveAlerts ON Nodes.NodeID = ActiveAlerts.NodeID LEFT JOIN Orion.NPM.Interfaces AS Interfaces ON Nodes.NodeID = Interfaces.NodeID LEFT JOIN Orion.NodesCustomProperties AS NodesCustomProperties ON Nodes.NodeID = NodesCustomProperties.NodeID LEFT JOIN Orion.NPM.InterfacesCustomProperties AS InterfacesCustomProperties ON Interfaces.InterfaceID = InterfacesCustomProperties.InterfaceID WHERE Nodes.NodeName = '${parameters.deviceName}'`\n});", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.SolarWindsUserName}:${config.SolarWindsPassword}`)}`\n});", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "SolarWindsDowner"}, {"name": "SolarWindsMedibank", "active": true, "description": "SolarWinds Instance for Medibank", "apiType": "rest", "method": "get", "parameters": ["deviceName"], "baseUrl": "config.SolarWinds.medBaseURI", "uri": "'SolarWinds/InformationService/v3/Json/Query'", "queryParams": "new Object({\n query: `SELECT Nodes.NodeID AS node_NodeID, Nodes.ObjectSubType AS node_ObjectSubType, Nodes.IPAddress AS node_IPAddress, Nodes.IPAddressType AS node_IPAddressType, Nodes.DynamicIP AS node_DynamicIP, Nodes.Caption AS node_Caption, Nodes.NodeDescription AS node_NodeDescription, Nodes.Description AS node_Description, Nodes.DNS AS node_DNS, Nodes.SysName AS node_SysName, Nodes.Vendor AS node_Vendor, Nodes.SysObjectID AS node_SysObjectID, Nodes.Location AS node_Location, Nodes.Contact AS node_Contact, Nodes.VendorIcon AS node_VendorIcon, Nodes.Icon AS node_Icon, Nodes.Status AS node_Status, Nodes.StatusLED AS node_StatusLED, Nodes.StatusDescription AS node_StatusDescription, Nodes.CustomStatus AS node_CustomStatus, Nodes.IOSImage AS node_IOSImage, Nodes.IOSVersion AS node_IOSVersion, Nodes.GroupStatus AS node_GroupStatus, Nodes.LastBoot AS node_LastBoot, Nodes.SystemUpTime AS node_SystemUpTime, Nodes.ResponseTime AS node_ResponseTime, Nodes.PercentLoss AS node_PercentLoss, Nodes.AvgResponseTime AS node_AvgResponseTime, Nodes.MinResponseTime AS node_MinResponseTime, Nodes.MaxResponseTime AS node_MaxResponseTime, Nodes.CPUCount AS node_CPUCount, Nodes.CPULoad AS node_CPULoad, Nodes.MemoryUsed AS node_MemoryUsed, Nodes.MemoryAvailable AS node_MemoryAvailable, Nodes.PercentMemoryUsed AS node_PercentMemoryUsed, Nodes.PercentMemoryAvailable AS node_PercentMemoryAvailable, Nodes.LastSync AS node_LastSync, Nodes.LastSystemUpTimePollUtc AS node_LastSystemUpTimePollUtc, Nodes.MachineType AS node_MachineType, Nodes.IsServer AS node_IsServer, Nodes.Severity AS node_Severity, Nodes.UiSeverity AS node_UiSeverity, Nodes.ChildStatus AS node_ChildStatus, Nodes.Allow64BitCounters AS node_Allow64BitCounters, Nodes.AgentPort AS node_AgentPort, Nodes.TotalMemory AS node_TotalMemory, Nodes.CMTS AS node_CMTS, Nodes.CustomPollerLastStatisticsPoll AS node_CustomPollerLastStatisticsPoll, Nodes.CustomPollerLastStatisticsPollSuccess AS node_CustomPollerLastStatisticsPollSuccess, Nodes.SNMPVersion AS node_SNMPVersion, Nodes.PollInterval AS node_PollInterval, Nodes.EngineID AS node_EngineID, Nodes.RediscoveryInterval AS node_RediscoveryInterval, Nodes.NextPoll AS node_NextPoll, Nodes.NextRediscovery AS node_NextRediscovery, Nodes.StatCollection AS node_StatCollection, Nodes.External AS node_External, Nodes.Community AS node_Community, Nodes.RWCommunity AS node_RWCommunity, Nodes.IP AS node_IP, Nodes.IPAddressGUID AS node_IPAddressGUID, Nodes.BlockUntil AS node_BlockUntil, Nodes.OrionIdPrefix AS node_OrionIdPrefix, Nodes.OrionIdColumn AS node_OrionIdColumn, Nodes.EntityType AS node_EntityType, Nodes.DetailsUrl AS node_DetailsUrl, Nodes.DisplayName AS node_DisplayName, Nodes.Category AS node_Category, Nodes.IsOrionServer AS node_IsOrionServer, Nodes.UnManaged AS node_UnManaged, Nodes.UnManageFrom AS node_UnManageFrom, Nodes.UnManageUntil AS node_UnManageUntil, Nodes.Image AS node_Image, Nodes.InstanceType AS node_InstanceType, Nodes.Uri AS node_Uri, Nodes.InstanceSiteId AS node_InstanceSiteId, HardwareInfo.Manufacturer AS hardware_Manufacturer, HardwareInfo.Model AS hardware_Model, HardwareInfo.ServiceTag AS hardware_ServiceTag, HardwareInfo.IsDisabled AS hardware_IsDisabled, HardwareInfo.CategoriesWithProblems AS hardware_CategoriesWithProblems, HardwareInfo.CategoriesWithStatus AS hardware_CategoriesWithStatus, HardwareInfo.FullyQualifiedName AS hardware_FullyQualifiedName, HardwareInfo.UnManaged AS hardware_UnManaged, HardwareInfo.StatusDescription AS hardware_StatusDescription, HardwareInfo.Description AS hardware_Description, HardwareInfo.StatusLED AS hardware_StatusLED, HardwareInfo.DisplayName AS hardware_DisplayName, ActiveAlerts.AlertID AS alert_AlertID, ActiveAlerts.AlertTime AS alert_AlertTime, ActiveAlerts.ObjectType AS alert_ObjectType, ActiveAlerts.ObjectID AS alert_ObjectID, ActiveAlerts.ObjectName AS alert_ObjectName, ActiveAlerts.EventMessage AS alert_EventMessage, ActiveAlerts.PropertyID AS alert_PropertyID, ActiveAlerts.Monitoredproperty AS alert_Monitoredproperty, ActiveAlerts.CurrentValue AS alert_CurrentValue, ActiveAlerts.TriggerValue AS alert_TriggerValue, ActiveAlerts.ResetValue AS alert_ResetValue, ActiveAlerts.EngineID AS alert_EngineID, ActiveAlerts.AlertNotes AS alert_AlertNotes, ActiveAlerts.ExpireTime AS alert_ExpireTime, ActiveAlerts.Description AS alert_Description, ActiveAlerts.InstanceType AS alert_InstanceType, ActiveAlerts.Uri AS alert_Uri, ActiveAlerts.InstanceSiteId AS alert_InstanceSiteId, Interfaces.InterfaceID AS interface_InterfaceID, Interfaces.ObjectSubType AS interface_ObjectSubType, Interfaces.Name AS interface_Name, Interfaces.TypeName AS interface_TypeName, Interfaces.TypeDescription AS interface_TypeDescription, Interfaces.Speed AS interface_Speed, Interfaces.MTU AS interface_MTU, Interfaces.PhysicalAddress AS interface_PhysicalAddress, Interfaces.AdminStatus AS interface_AdminStatus, Interfaces.OperStatus AS interface_OperStatus, Interfaces.StatusIcon AS interface_StatusIcon, Interfaces.InBandwidth AS interface_InBandwidth, Interfaces.OutBandwidth AS interface_OutBandwidth, Interfaces.Caption AS interface_Caption, Interfaces.FullName AS interface_FullName, Interfaces.Alias AS interface_Alias, Interfaces.IfName AS interface_IfName, Interfaces.CustomBandwidth AS interface_CustomBandwidth, Interfaces.InterfaceSpeed AS interface_InterfaceSpeed, Interfaces.InterfaceCaption AS interface_InterfaceCaption, Interfaces.MAC AS interface_MAC, Interfaces.InterfaceName AS interface_InterfaceName, Interfaces.InterfaceTypeName AS interface_InterfaceTypeName, Interfaces.InterfaceAlias AS interface_InterfaceAlias, Interfaces.InterfaceMTU AS interface_InterfaceMTU, Interfaces.InterfaceTypeDescription AS interface_InterfaceTypeDescription, Interfaces.DuplexMode AS interface_DuplexMode, Interfaces.Status AS interface_Status, Interfaces.Description AS interface_Description, Interfaces.StatusDescription AS interface_StatusDescription, Interfaces.UnManaged AS interface_UnManaged, Interfaces.DisplayName AS interface_DisplayName, NodesCustomProperties.CIDN AS custom_CIDN, NodesCustomProperties.CarriageType AS custom_CarriageType, NodesCustomProperties.AddressId AS custom_AddressId, NodesCustomProperties.DevAlias AS custom_DevAlias, NodesCustomProperties.DevClass AS custom_DevClass, NodesCustomProperties.DevStatus AS custom_DevStatus, NodesCustomProperties.SiteName AS custom_SiteName, NodesCustomProperties.SiteType AS custom_SiteType, NodesCustomProperties._3LC AS custom_3LC, NodesCustomProperties._3PPConnected AS custom_3PPConnected, NodesCustomProperties.IOM_BAU_Status AS custom_IOM_BAU_Status, NodesCustomProperties.CPUThreshold AS custom_MgmtClass, NodesCustomProperties.IndependenceStatus AS custom_IndependenceStatus, NodesCustomProperties.swCustomer AS custom_swCustomer, NodesCustomProperties.RoomNumber AS custom_RoomNumber, NodesCustomProperties.FloorNumber AS custom_FloorNumber, NodesCustomProperties.StreetNumber AS custom_StreetNumber, NodesCustomProperties.StreetName AS custom_StreetName, NodesCustomProperties.StreetType AS custom_StreetType, NodesCustomProperties.StreetSuffix AS custom_StreetSuffix, NodesCustomProperties.City AS custom_City, NodesCustomProperties.PostCode AS custom_PostCode, NodesCustomProperties.State AS custom_State, NodesCustomProperties.Country AS custom_Country, NodesCustomProperties.Latitude AS custom_Latitude, NodesCustomProperties.Longitude AS custom_Longitude, InterfacesCustomProperties.InterfaceAlertEnable AS custom_InterfaceAlertEnable, InterfacesCustomProperties.InterfaceCategory AS custom_InterfaceCategory, InterfacesCustomProperties.InterfaceRole AS custom_InterfaceRole FROM Orion.Nodes AS Nodes LEFT JOIN Orion.HardwareHealth.HardwareInfo AS HardwareInfo ON Nodes.NodeID = HardwareInfo.NodeID LEFT JOIN Orion.ActiveAlerts AS ActiveAlerts ON Nodes.NodeID = ActiveAlerts.NodeID LEFT JOIN Orion.NPM.Interfaces AS Interfaces ON Nodes.NodeID = Interfaces.NodeID LEFT JOIN Orion.NodesCustomProperties AS NodesCustomProperties ON Nodes.NodeID = NodesCustomProperties.NodeID LEFT JOIN Orion.NPM.InterfacesCustomProperties AS InterfacesCustomProperties ON Interfaces.InterfaceID = InterfacesCustomProperties.InterfaceID WHERE Nodes.NodeName = '${parameters.deviceName}'`\n});", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.SolarWindsUserName}:${config.SolarWindsPassword}`)}`\n});", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "SolarWindsMedibank"}, {"name": "SolarWindsOrigin", "active": true, "description": "SolarWinds Instance for Origin", "apiType": "rest", "method": "get", "parameters": ["deviceName"], "baseUrl": "config.SolarWinds.orgBaseURI", "uri": "'SolarWinds/InformationService/v3/Json/Query'", "queryParams": "new Object({\n query: `SELECT Nodes.NodeID AS node_NodeID, Nodes.ObjectSubType AS node_ObjectSubType, Nodes.IPAddress AS node_IPAddress, Nodes.IPAddressType AS node_IPAddressType, Nodes.DynamicIP AS node_DynamicIP, Nodes.Caption AS node_Caption, Nodes.NodeDescription AS node_NodeDescription, Nodes.Description AS node_Description, Nodes.DNS AS node_DNS, Nodes.SysName AS node_SysName, Nodes.Vendor AS node_Vendor, Nodes.SysObjectID AS node_SysObjectID, Nodes.Location AS node_Location, Nodes.Contact AS node_Contact, Nodes.VendorIcon AS node_VendorIcon, Nodes.Icon AS node_Icon, Nodes.Status AS node_Status, Nodes.StatusLED AS node_StatusLED, Nodes.StatusDescription AS node_StatusDescription, Nodes.CustomStatus AS node_CustomStatus, Nodes.IOSImage AS node_IOSImage, Nodes.IOSVersion AS node_IOSVersion, Nodes.GroupStatus AS node_GroupStatus, Nodes.LastBoot AS node_LastBoot, Nodes.SystemUpTime AS node_SystemUpTime, Nodes.ResponseTime AS node_ResponseTime, Nodes.PercentLoss AS node_PercentLoss, Nodes.AvgResponseTime AS node_AvgResponseTime, Nodes.MinResponseTime AS node_MinResponseTime, Nodes.MaxResponseTime AS node_MaxResponseTime, Nodes.CPUCount AS node_CPUCount, Nodes.CPULoad AS node_CPULoad, Nodes.MemoryUsed AS node_MemoryUsed, Nodes.MemoryAvailable AS node_MemoryAvailable, Nodes.PercentMemoryUsed AS node_PercentMemoryUsed, Nodes.PercentMemoryAvailable AS node_PercentMemoryAvailable, Nodes.LastSync AS node_LastSync, Nodes.LastSystemUpTimePollUtc AS node_LastSystemUpTimePollUtc, Nodes.MachineType AS node_MachineType, Nodes.IsServer AS node_IsServer, Nodes.Severity AS node_Severity, Nodes.UiSeverity AS node_UiSeverity, Nodes.ChildStatus AS node_ChildStatus, Nodes.Allow64BitCounters AS node_Allow64BitCounters, Nodes.AgentPort AS node_AgentPort, Nodes.TotalMemory AS node_TotalMemory, Nodes.CMTS AS node_CMTS, Nodes.CustomPollerLastStatisticsPoll AS node_CustomPollerLastStatisticsPoll, Nodes.CustomPollerLastStatisticsPollSuccess AS node_CustomPollerLastStatisticsPollSuccess, Nodes.SNMPVersion AS node_SNMPVersion, Nodes.PollInterval AS node_PollInterval, Nodes.EngineID AS node_EngineID, Nodes.RediscoveryInterval AS node_RediscoveryInterval, Nodes.NextPoll AS node_NextPoll, Nodes.NextRediscovery AS node_NextRediscovery, Nodes.StatCollection AS node_StatCollection, Nodes.External AS node_External, Nodes.Community AS node_Community, Nodes.RWCommunity AS node_RWCommunity, Nodes.IP AS node_IP, Nodes.IPAddressGUID AS node_IPAddressGUID, Nodes.BlockUntil AS node_BlockUntil, Nodes.OrionIdPrefix AS node_OrionIdPrefix, Nodes.OrionIdColumn AS node_OrionIdColumn, Nodes.EntityType AS node_EntityType, Nodes.DetailsUrl AS node_DetailsUrl, Nodes.DisplayName AS node_DisplayName, Nodes.Category AS node_Category, Nodes.IsOrionServer AS node_IsOrionServer, Nodes.UnManaged AS node_UnManaged, Nodes.UnManageFrom AS node_UnManageFrom, Nodes.UnManageUntil AS node_UnManageUntil, Nodes.Image AS node_Image, Nodes.InstanceType AS node_InstanceType, Nodes.Uri AS node_Uri, Nodes.InstanceSiteId AS node_InstanceSiteId, HardwareInfo.Manufacturer AS hardware_Manufacturer, HardwareInfo.Model AS hardware_Model, HardwareInfo.ServiceTag AS hardware_ServiceTag, HardwareInfo.IsDisabled AS hardware_IsDisabled, HardwareInfo.CategoriesWithProblems AS hardware_CategoriesWithProblems, HardwareInfo.CategoriesWithStatus AS hardware_CategoriesWithStatus, HardwareInfo.FullyQualifiedName AS hardware_FullyQualifiedName, HardwareInfo.UnManaged AS hardware_UnManaged, HardwareInfo.StatusDescription AS hardware_StatusDescription, HardwareInfo.Description AS hardware_Description, HardwareInfo.StatusLED AS hardware_StatusLED, HardwareInfo.DisplayName AS hardware_DisplayName, ActiveAlerts.AlertID AS alert_AlertID, ActiveAlerts.AlertTime AS alert_AlertTime, ActiveAlerts.ObjectType AS alert_ObjectType, ActiveAlerts.ObjectID AS alert_ObjectID, ActiveAlerts.ObjectName AS alert_ObjectName, ActiveAlerts.EventMessage AS alert_EventMessage, ActiveAlerts.PropertyID AS alert_PropertyID, ActiveAlerts.Monitoredproperty AS alert_Monitoredproperty, ActiveAlerts.CurrentValue AS alert_CurrentValue, ActiveAlerts.TriggerValue AS alert_TriggerValue, ActiveAlerts.ResetValue AS alert_ResetValue, ActiveAlerts.EngineID AS alert_EngineID, ActiveAlerts.AlertNotes AS alert_AlertNotes, ActiveAlerts.ExpireTime AS alert_ExpireTime, ActiveAlerts.Description AS alert_Description, ActiveAlerts.InstanceType AS alert_InstanceType, ActiveAlerts.Uri AS alert_Uri, ActiveAlerts.InstanceSiteId AS alert_InstanceSiteId, Interfaces.InterfaceID AS interface_InterfaceID, Interfaces.ObjectSubType AS interface_ObjectSubType, Interfaces.Name AS interface_Name, Interfaces.TypeName AS interface_TypeName, Interfaces.TypeDescription AS interface_TypeDescription, Interfaces.Speed AS interface_Speed, Interfaces.MTU AS interface_MTU, Interfaces.PhysicalAddress AS interface_PhysicalAddress, Interfaces.AdminStatus AS interface_AdminStatus, Interfaces.OperStatus AS interface_OperStatus, Interfaces.StatusIcon AS interface_StatusIcon, Interfaces.InBandwidth AS interface_InBandwidth, Interfaces.OutBandwidth AS interface_OutBandwidth, Interfaces.Caption AS interface_Caption, Interfaces.FullName AS interface_FullName, Interfaces.Alias AS interface_Alias, Interfaces.IfName AS interface_IfName, Interfaces.CustomBandwidth AS interface_CustomBandwidth, Interfaces.InterfaceSpeed AS interface_InterfaceSpeed, Interfaces.InterfaceCaption AS interface_InterfaceCaption, Interfaces.MAC AS interface_MAC, Interfaces.InterfaceName AS interface_InterfaceName, Interfaces.InterfaceTypeName AS interface_InterfaceTypeName, Interfaces.InterfaceAlias AS interface_InterfaceAlias, Interfaces.InterfaceMTU AS interface_InterfaceMTU, Interfaces.InterfaceTypeDescription AS interface_InterfaceTypeDescription, Interfaces.DuplexMode AS interface_DuplexMode, Interfaces.Status AS interface_Status, Interfaces.Description AS interface_Description, Interfaces.StatusDescription AS interface_StatusDescription, Interfaces.UnManaged AS interface_UnManaged, Interfaces.DisplayName AS interface_DisplayName, NodesCustomProperties.CIDN AS custom_CIDN, NodesCustomProperties.CarriageType AS custom_CarriageType, NodesCustomProperties.AddressId AS custom_AddressId, NodesCustomProperties.DevAlias AS custom_DevAlias, NodesCustomProperties.DevClass AS custom_DevClass, NodesCustomProperties.DevStatus AS custom_DevStatus, NodesCustomProperties.SiteName AS custom_SiteName, NodesCustomProperties.SiteType AS custom_SiteType, NodesCustomProperties._3LC AS custom_3LC, NodesCustomProperties._3PPConnected AS custom_3PPConnected, NodesCustomProperties.IOM_BAU_Status AS custom_IOM_BAU_Status, NodesCustomProperties.CPUThreshold AS custom_MgmtClass, NodesCustomProperties.IndependenceStatus AS custom_IndependenceStatus, NodesCustomProperties.swCustomer AS custom_swCustomer, NodesCustomProperties.RoomNumber AS custom_RoomNumber, NodesCustomProperties.FloorNumber AS custom_FloorNumber, NodesCustomProperties.StreetNumber AS custom_StreetNumber, NodesCustomProperties.StreetName AS custom_StreetName, NodesCustomProperties.StreetType AS custom_StreetType, NodesCustomProperties.StreetSuffix AS custom_StreetSuffix, NodesCustomProperties.City AS custom_City, NodesCustomProperties.PostCode AS custom_PostCode, NodesCustomProperties.State AS custom_State, NodesCustomProperties.Country AS custom_Country, NodesCustomProperties.Latitude AS custom_Latitude, NodesCustomProperties.Longitude AS custom_Longitude, InterfacesCustomProperties.InterfaceAlertEnable AS custom_InterfaceAlertEnable, InterfacesCustomProperties.InterfaceCategory AS custom_InterfaceCategory, InterfacesCustomProperties.InterfaceRole AS custom_InterfaceRole FROM Orion.Nodes AS Nodes LEFT JOIN Orion.HardwareHealth.HardwareInfo AS HardwareInfo ON Nodes.NodeID = HardwareInfo.NodeID LEFT JOIN Orion.ActiveAlerts AS ActiveAlerts ON Nodes.NodeID = ActiveAlerts.NodeID LEFT JOIN Orion.NPM.Interfaces AS Interfaces ON Nodes.NodeID = Interfaces.NodeID LEFT JOIN Orion.NodesCustomProperties AS NodesCustomProperties ON Nodes.NodeID = NodesCustomProperties.NodeID LEFT JOIN Orion.NPM.InterfacesCustomProperties AS InterfacesCustomProperties ON Interfaces.InterfaceID = InterfacesCustomProperties.InterfaceID WHERE Nodes.NodeName = '${parameters.deviceName}'`\n});", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.SolarWindsUserName}:${config.SolarWindsPassword}`)}`\n});", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "SolarWindsOrigin"}, {"name": "SolarWindsOriginClayton", "active": true, "description": "SolarWinds Instance for Origin Clayton", "apiType": "rest", "method": "get", "parameters": ["deviceName"], "baseUrl": "config.SolarWinds.orgcBaseURI", "uri": "'SolarWinds/InformationService/v3/Json/Query'", "queryParams": "new Object({\n query: `SELECT Nodes.NodeID AS node_NodeID, Nodes.ObjectSubType AS node_ObjectSubType, Nodes.IPAddress AS node_IPAddress, Nodes.IPAddressType AS node_IPAddressType, Nodes.DynamicIP AS node_DynamicIP, Nodes.Caption AS node_Caption, Nodes.NodeDescription AS node_NodeDescription, Nodes.Description AS node_Description, Nodes.DNS AS node_DNS, Nodes.SysName AS node_SysName, Nodes.Vendor AS node_Vendor, Nodes.SysObjectID AS node_SysObjectID, Nodes.Location AS node_Location, Nodes.Contact AS node_Contact, Nodes.VendorIcon AS node_VendorIcon, Nodes.Icon AS node_Icon, Nodes.Status AS node_Status, Nodes.StatusLED AS node_StatusLED, Nodes.StatusDescription AS node_StatusDescription, Nodes.CustomStatus AS node_CustomStatus, Nodes.IOSImage AS node_IOSImage, Nodes.IOSVersion AS node_IOSVersion, Nodes.GroupStatus AS node_GroupStatus, Nodes.LastBoot AS node_LastBoot, Nodes.SystemUpTime AS node_SystemUpTime, Nodes.ResponseTime AS node_ResponseTime, Nodes.PercentLoss AS node_PercentLoss, Nodes.AvgResponseTime AS node_AvgResponseTime, Nodes.MinResponseTime AS node_MinResponseTime, Nodes.MaxResponseTime AS node_MaxResponseTime, Nodes.CPUCount AS node_CPUCount, Nodes.CPULoad AS node_CPULoad, Nodes.MemoryUsed AS node_MemoryUsed, Nodes.MemoryAvailable AS node_MemoryAvailable, Nodes.PercentMemoryUsed AS node_PercentMemoryUsed, Nodes.PercentMemoryAvailable AS node_PercentMemoryAvailable, Nodes.LastSync AS node_LastSync, Nodes.LastSystemUpTimePollUtc AS node_LastSystemUpTimePollUtc, Nodes.MachineType AS node_MachineType, Nodes.IsServer AS node_IsServer, Nodes.Severity AS node_Severity, Nodes.UiSeverity AS node_UiSeverity, Nodes.ChildStatus AS node_ChildStatus, Nodes.Allow64BitCounters AS node_Allow64BitCounters, Nodes.AgentPort AS node_AgentPort, Nodes.TotalMemory AS node_TotalMemory, Nodes.CMTS AS node_CMTS, Nodes.CustomPollerLastStatisticsPoll AS node_CustomPollerLastStatisticsPoll, Nodes.CustomPollerLastStatisticsPollSuccess AS node_CustomPollerLastStatisticsPollSuccess, Nodes.SNMPVersion AS node_SNMPVersion, Nodes.PollInterval AS node_PollInterval, Nodes.EngineID AS node_EngineID, Nodes.RediscoveryInterval AS node_RediscoveryInterval, Nodes.NextPoll AS node_NextPoll, Nodes.NextRediscovery AS node_NextRediscovery, Nodes.StatCollection AS node_StatCollection, Nodes.External AS node_External, Nodes.Community AS node_Community, Nodes.RWCommunity AS node_RWCommunity, Nodes.IP AS node_IP, Nodes.IPAddressGUID AS node_IPAddressGUID, Nodes.BlockUntil AS node_BlockUntil, Nodes.OrionIdPrefix AS node_OrionIdPrefix, Nodes.OrionIdColumn AS node_OrionIdColumn, Nodes.EntityType AS node_EntityType, Nodes.DetailsUrl AS node_DetailsUrl, Nodes.DisplayName AS node_DisplayName, Nodes.Category AS node_Category, Nodes.IsOrionServer AS node_IsOrionServer, Nodes.UnManaged AS node_UnManaged, Nodes.UnManageFrom AS node_UnManageFrom, Nodes.UnManageUntil AS node_UnManageUntil, Nodes.Image AS node_Image, Nodes.InstanceType AS node_InstanceType, Nodes.Uri AS node_Uri, Nodes.InstanceSiteId AS node_InstanceSiteId, HardwareInfo.Manufacturer AS hardware_Manufacturer, HardwareInfo.Model AS hardware_Model, HardwareInfo.ServiceTag AS hardware_ServiceTag, HardwareInfo.IsDisabled AS hardware_IsDisabled, HardwareInfo.CategoriesWithProblems AS hardware_CategoriesWithProblems, HardwareInfo.CategoriesWithStatus AS hardware_CategoriesWithStatus, HardwareInfo.FullyQualifiedName AS hardware_FullyQualifiedName, HardwareInfo.UnManaged AS hardware_UnManaged, HardwareInfo.StatusDescription AS hardware_StatusDescription, HardwareInfo.Description AS hardware_Description, HardwareInfo.StatusLED AS hardware_StatusLED, HardwareInfo.DisplayName AS hardware_DisplayName, ActiveAlerts.AlertID AS alert_AlertID, ActiveAlerts.AlertTime AS alert_AlertTime, ActiveAlerts.ObjectType AS alert_ObjectType, ActiveAlerts.ObjectID AS alert_ObjectID, ActiveAlerts.ObjectName AS alert_ObjectName, ActiveAlerts.EventMessage AS alert_EventMessage, ActiveAlerts.PropertyID AS alert_PropertyID, ActiveAlerts.Monitoredproperty AS alert_Monitoredproperty, ActiveAlerts.CurrentValue AS alert_CurrentValue, ActiveAlerts.TriggerValue AS alert_TriggerValue, ActiveAlerts.ResetValue AS alert_ResetValue, ActiveAlerts.EngineID AS alert_EngineID, ActiveAlerts.AlertNotes AS alert_AlertNotes, ActiveAlerts.ExpireTime AS alert_ExpireTime, ActiveAlerts.Description AS alert_Description, ActiveAlerts.InstanceType AS alert_InstanceType, ActiveAlerts.Uri AS alert_Uri, ActiveAlerts.InstanceSiteId AS alert_InstanceSiteId, Interfaces.InterfaceID AS interface_InterfaceID, Interfaces.ObjectSubType AS interface_ObjectSubType, Interfaces.Name AS interface_Name, Interfaces.TypeName AS interface_TypeName, Interfaces.TypeDescription AS interface_TypeDescription, Interfaces.Speed AS interface_Speed, Interfaces.MTU AS interface_MTU, Interfaces.PhysicalAddress AS interface_PhysicalAddress, Interfaces.AdminStatus AS interface_AdminStatus, Interfaces.OperStatus AS interface_OperStatus, Interfaces.StatusIcon AS interface_StatusIcon, Interfaces.InBandwidth AS interface_InBandwidth, Interfaces.OutBandwidth AS interface_OutBandwidth, Interfaces.Caption AS interface_Caption, Interfaces.FullName AS interface_FullName, Interfaces.Alias AS interface_Alias, Interfaces.IfName AS interface_IfName, Interfaces.CustomBandwidth AS interface_CustomBandwidth, Interfaces.InterfaceSpeed AS interface_InterfaceSpeed, Interfaces.InterfaceCaption AS interface_InterfaceCaption, Interfaces.MAC AS interface_MAC, Interfaces.InterfaceName AS interface_InterfaceName, Interfaces.InterfaceTypeName AS interface_InterfaceTypeName, Interfaces.InterfaceAlias AS interface_InterfaceAlias, Interfaces.InterfaceMTU AS interface_InterfaceMTU, Interfaces.InterfaceTypeDescription AS interface_InterfaceTypeDescription, Interfaces.DuplexMode AS interface_DuplexMode, Interfaces.Status AS interface_Status, Interfaces.Description AS interface_Description, Interfaces.StatusDescription AS interface_StatusDescription, Interfaces.UnManaged AS interface_UnManaged, Interfaces.DisplayName AS interface_DisplayName, NodesCustomProperties.CIDN AS custom_CIDN, NodesCustomProperties.CarriageType AS custom_CarriageType, NodesCustomProperties.AddressId AS custom_AddressId, NodesCustomProperties.DevAlias AS custom_DevAlias, NodesCustomProperties.DevClass AS custom_DevClass, NodesCustomProperties.DevStatus AS custom_DevStatus, NodesCustomProperties.SiteName AS custom_SiteName, NodesCustomProperties.SiteType AS custom_SiteType, NodesCustomProperties._3LC AS custom_3LC, NodesCustomProperties._3PPConnected AS custom_3PPConnected, NodesCustomProperties.IOM_BAU_Status AS custom_IOM_BAU_Status, NodesCustomProperties.CPUThreshold AS custom_MgmtClass, NodesCustomProperties.IndependenceStatus AS custom_IndependenceStatus, NodesCustomProperties.swCustomer AS custom_swCustomer, NodesCustomProperties.RoomNumber AS custom_RoomNumber, NodesCustomProperties.FloorNumber AS custom_FloorNumber, NodesCustomProperties.StreetNumber AS custom_StreetNumber, NodesCustomProperties.StreetName AS custom_StreetName, NodesCustomProperties.StreetType AS custom_StreetType, NodesCustomProperties.StreetSuffix AS custom_StreetSuffix, NodesCustomProperties.City AS custom_City, NodesCustomProperties.PostCode AS custom_PostCode, NodesCustomProperties.State AS custom_State, NodesCustomProperties.Country AS custom_Country, NodesCustomProperties.Latitude AS custom_Latitude, NodesCustomProperties.Longitude AS custom_Longitude, InterfacesCustomProperties.InterfaceAlertEnable AS custom_InterfaceAlertEnable, InterfacesCustomProperties.InterfaceCategory AS custom_InterfaceCategory, InterfacesCustomProperties.InterfaceRole AS custom_InterfaceRole FROM Orion.Nodes AS Nodes LEFT JOIN Orion.HardwareHealth.HardwareInfo AS HardwareInfo ON Nodes.NodeID = HardwareInfo.NodeID LEFT JOIN Orion.ActiveAlerts AS ActiveAlerts ON Nodes.NodeID = ActiveAlerts.NodeID LEFT JOIN Orion.NPM.Interfaces AS Interfaces ON Nodes.NodeID = Interfaces.NodeID LEFT JOIN Orion.NodesCustomProperties AS NodesCustomProperties ON Nodes.NodeID = NodesCustomProperties.NodeID LEFT JOIN Orion.NPM.InterfacesCustomProperties AS InterfacesCustomProperties ON Interfaces.InterfaceID = InterfacesCustomProperties.InterfaceID WHERE Nodes.NodeName = '${parameters.deviceName}'`\n});", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.SolarWindsUserName}:${config.SolarWindsPassword}`)}`\n});", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "SolarWindsOriginClayton"}, {"name": "SolarWindsOT", "active": true, "description": "SolarWinds Instance for OT/Shared", "apiType": "rest", "method": "get", "parameters": ["deviceName"], "baseUrl": "config.SolarWinds.otBaseURI", "uri": "'SolarWinds/InformationService/v3/Json/Query'", "queryParams": "new Object({\n query: `SELECT Nodes.NodeID AS node_NodeID, Nodes.ObjectSubType AS node_ObjectSubType, Nodes.IPAddress AS node_IPAddress, Nodes.IPAddressType AS node_IPAddressType, Nodes.DynamicIP AS node_DynamicIP, Nodes.Caption AS node_Caption, Nodes.NodeDescription AS node_NodeDescription, Nodes.Description AS node_Description, Nodes.DNS AS node_DNS, Nodes.SysName AS node_SysName, Nodes.Vendor AS node_Vendor, Nodes.SysObjectID AS node_SysObjectID, Nodes.Location AS node_Location, Nodes.Contact AS node_Contact, Nodes.VendorIcon AS node_VendorIcon, Nodes.Icon AS node_Icon, Nodes.Status AS node_Status, Nodes.StatusLED AS node_StatusLED, Nodes.StatusDescription AS node_StatusDescription, Nodes.CustomStatus AS node_CustomStatus, Nodes.IOSImage AS node_IOSImage, Nodes.IOSVersion AS node_IOSVersion, Nodes.GroupStatus AS node_GroupStatus, Nodes.LastBoot AS node_LastBoot, Nodes.SystemUpTime AS node_SystemUpTime, Nodes.ResponseTime AS node_ResponseTime, Nodes.PercentLoss AS node_PercentLoss, Nodes.AvgResponseTime AS node_AvgResponseTime, Nodes.MinResponseTime AS node_MinResponseTime, Nodes.MaxResponseTime AS node_MaxResponseTime, Nodes.CPUCount AS node_CPUCount, Nodes.CPULoad AS node_CPULoad, Nodes.MemoryUsed AS node_MemoryUsed, Nodes.MemoryAvailable AS node_MemoryAvailable, Nodes.PercentMemoryUsed AS node_PercentMemoryUsed, Nodes.PercentMemoryAvailable AS node_PercentMemoryAvailable, Nodes.LastSync AS node_LastSync, Nodes.LastSystemUpTimePollUtc AS node_LastSystemUpTimePollUtc, Nodes.MachineType AS node_MachineType, Nodes.IsServer AS node_IsServer, Nodes.Severity AS node_Severity, Nodes.UiSeverity AS node_UiSeverity, Nodes.ChildStatus AS node_ChildStatus, Nodes.Allow64BitCounters AS node_Allow64BitCounters, Nodes.AgentPort AS node_AgentPort, Nodes.TotalMemory AS node_TotalMemory, Nodes.CMTS AS node_CMTS, Nodes.CustomPollerLastStatisticsPoll AS node_CustomPollerLastStatisticsPoll, Nodes.CustomPollerLastStatisticsPollSuccess AS node_CustomPollerLastStatisticsPollSuccess, Nodes.SNMPVersion AS node_SNMPVersion, Nodes.PollInterval AS node_PollInterval, Nodes.EngineID AS node_EngineID, Nodes.RediscoveryInterval AS node_RediscoveryInterval, Nodes.NextPoll AS node_NextPoll, Nodes.NextRediscovery AS node_NextRediscovery, Nodes.StatCollection AS node_StatCollection, Nodes.External AS node_External, Nodes.Community AS node_Community, Nodes.RWCommunity AS node_RWCommunity, Nodes.IP AS node_IP, Nodes.IPAddressGUID AS node_IPAddressGUID, Nodes.BlockUntil AS node_BlockUntil, Nodes.OrionIdPrefix AS node_OrionIdPrefix, Nodes.OrionIdColumn AS node_OrionIdColumn, Nodes.EntityType AS node_EntityType, Nodes.DetailsUrl AS node_DetailsUrl, Nodes.DisplayName AS node_DisplayName, Nodes.Category AS node_Category, Nodes.IsOrionServer AS node_IsOrionServer, Nodes.UnManaged AS node_UnManaged, Nodes.UnManageFrom AS node_UnManageFrom, Nodes.UnManageUntil AS node_UnManageUntil, Nodes.Image AS node_Image, Nodes.InstanceType AS node_InstanceType, Nodes.Uri AS node_Uri, Nodes.InstanceSiteId AS node_InstanceSiteId, HardwareInfo.Manufacturer AS hardware_Manufacturer, HardwareInfo.Model AS hardware_Model, HardwareInfo.ServiceTag AS hardware_ServiceTag, HardwareInfo.IsDisabled AS hardware_IsDisabled, HardwareInfo.CategoriesWithProblems AS hardware_CategoriesWithProblems, HardwareInfo.CategoriesWithStatus AS hardware_CategoriesWithStatus, HardwareInfo.FullyQualifiedName AS hardware_FullyQualifiedName, HardwareInfo.UnManaged AS hardware_UnManaged, HardwareInfo.StatusDescription AS hardware_StatusDescription, HardwareInfo.Description AS hardware_Description, HardwareInfo.StatusLED AS hardware_StatusLED, HardwareInfo.DisplayName AS hardware_DisplayName, ActiveAlerts.AlertID AS alert_AlertID, ActiveAlerts.AlertTime AS alert_AlertTime, ActiveAlerts.ObjectType AS alert_ObjectType, ActiveAlerts.ObjectID AS alert_ObjectID, ActiveAlerts.ObjectName AS alert_ObjectName, ActiveAlerts.EventMessage AS alert_EventMessage, ActiveAlerts.PropertyID AS alert_PropertyID, ActiveAlerts.Monitoredproperty AS alert_Monitoredproperty, ActiveAlerts.CurrentValue AS alert_CurrentValue, ActiveAlerts.TriggerValue AS alert_TriggerValue, ActiveAlerts.ResetValue AS alert_ResetValue, ActiveAlerts.EngineID AS alert_EngineID, ActiveAlerts.AlertNotes AS alert_AlertNotes, ActiveAlerts.ExpireTime AS alert_ExpireTime, ActiveAlerts.Description AS alert_Description, ActiveAlerts.InstanceType AS alert_InstanceType, ActiveAlerts.Uri AS alert_Uri, ActiveAlerts.InstanceSiteId AS alert_InstanceSiteId, Interfaces.InterfaceID AS interface_InterfaceID, Interfaces.ObjectSubType AS interface_ObjectSubType, Interfaces.Name AS interface_Name, Interfaces.TypeName AS interface_TypeName, Interfaces.TypeDescription AS interface_TypeDescription, Interfaces.Speed AS interface_Speed, Interfaces.MTU AS interface_MTU, Interfaces.PhysicalAddress AS interface_PhysicalAddress, Interfaces.AdminStatus AS interface_AdminStatus, Interfaces.OperStatus AS interface_OperStatus, Interfaces.StatusIcon AS interface_StatusIcon, Interfaces.InBandwidth AS interface_InBandwidth, Interfaces.OutBandwidth AS interface_OutBandwidth, Interfaces.Caption AS interface_Caption, Interfaces.FullName AS interface_FullName, Interfaces.Alias AS interface_Alias, Interfaces.IfName AS interface_IfName, Interfaces.CustomBandwidth AS interface_CustomBandwidth, Interfaces.InterfaceSpeed AS interface_InterfaceSpeed, Interfaces.InterfaceCaption AS interface_InterfaceCaption, Interfaces.MAC AS interface_MAC, Interfaces.InterfaceName AS interface_InterfaceName, Interfaces.InterfaceTypeName AS interface_InterfaceTypeName, Interfaces.InterfaceAlias AS interface_InterfaceAlias, Interfaces.InterfaceMTU AS interface_InterfaceMTU, Interfaces.InterfaceTypeDescription AS interface_InterfaceTypeDescription, Interfaces.DuplexMode AS interface_DuplexMode, Interfaces.Status AS interface_Status, Interfaces.Description AS interface_Description, Interfaces.StatusDescription AS interface_StatusDescription, Interfaces.UnManaged AS interface_UnManaged, Interfaces.DisplayName AS interface_DisplayName, NodesCustomProperties.CIDN AS custom_CIDN, NodesCustomProperties.CarriageType AS custom_CarriageType, NodesCustomProperties.AddressId AS custom_AddressId, NodesCustomProperties.DevAlias AS custom_DevAlias, NodesCustomProperties.DevClass AS custom_DevClass, NodesCustomProperties.DevStatus AS custom_DevStatus, NodesCustomProperties.SiteName AS custom_SiteName, NodesCustomProperties.SiteType AS custom_SiteType, NodesCustomProperties._3LC AS custom_3LC, NodesCustomProperties._3PPConnected AS custom_3PPConnected, NodesCustomProperties.IOM_BAU_Status AS custom_IOM_BAU_Status, NodesCustomProperties.CPUThreshold AS custom_MgmtClass, NodesCustomProperties.IndependenceStatus AS custom_IndependenceStatus, NodesCustomProperties.swCustomer AS custom_swCustomer, NodesCustomProperties.RoomNumber AS custom_RoomNumber, NodesCustomProperties.FloorNumber AS custom_FloorNumber, NodesCustomProperties.StreetNumber AS custom_StreetNumber, NodesCustomProperties.StreetName AS custom_StreetName, NodesCustomProperties.StreetType AS custom_StreetType, NodesCustomProperties.StreetSuffix AS custom_StreetSuffix, NodesCustomProperties.City AS custom_City, NodesCustomProperties.PostCode AS custom_PostCode, NodesCustomProperties.State AS custom_State, NodesCustomProperties.Country AS custom_Country, NodesCustomProperties.Latitude AS custom_Latitude, NodesCustomProperties.Longitude AS custom_Longitude, InterfacesCustomProperties.InterfaceAlertEnable AS custom_InterfaceAlertEnable, InterfacesCustomProperties.InterfaceCategory AS custom_InterfaceCategory, InterfacesCustomProperties.InterfaceRole AS custom_InterfaceRole FROM Orion.Nodes AS Nodes LEFT JOIN Orion.HardwareHealth.HardwareInfo AS HardwareInfo ON Nodes.NodeID = HardwareInfo.NodeID LEFT JOIN Orion.ActiveAlerts AS ActiveAlerts ON Nodes.NodeID = ActiveAlerts.NodeID LEFT JOIN Orion.NPM.Interfaces AS Interfaces ON Nodes.NodeID = Interfaces.NodeID LEFT JOIN Orion.NodesCustomProperties AS NodesCustomProperties ON Nodes.NodeID = NodesCustomProperties.NodeID LEFT JOIN Orion.NPM.InterfacesCustomProperties AS InterfacesCustomProperties ON Interfaces.InterfaceID = InterfacesCustomProperties.InterfaceID WHERE Nodes.NodeName = '${parameters.deviceName}'`\n});", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.SolarWindsUserName}:${config.SolarWindsPassword}`)}`\n});", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "SolarWindsOT"}, {"name": "SolarWindsSADfe", "active": true, "description": "SolarWinds Instance for SADfe", "apiType": "rest", "method": "get", "parameters": ["deviceName"], "baseUrl": "config.SolarWinds.sadfeBaseURI", "uri": "'SolarWinds/InformationService/v3/Json/Query'", "queryParams": "new Object({\n query: `SELECT Nodes.NodeID AS node_NodeID, Nodes.ObjectSubType AS node_ObjectSubType, Nodes.IPAddress AS node_IPAddress, Nodes.IPAddressType AS node_IPAddressType, Nodes.DynamicIP AS node_DynamicIP, Nodes.Caption AS node_Caption, Nodes.NodeDescription AS node_NodeDescription, Nodes.Description AS node_Description, Nodes.DNS AS node_DNS, Nodes.SysName AS node_SysName, Nodes.Vendor AS node_Vendor, Nodes.SysObjectID AS node_SysObjectID, Nodes.Location AS node_Location, Nodes.Contact AS node_Contact, Nodes.VendorIcon AS node_VendorIcon, Nodes.Icon AS node_Icon, Nodes.Status AS node_Status, Nodes.StatusLED AS node_StatusLED, Nodes.StatusDescription AS node_StatusDescription, Nodes.CustomStatus AS node_CustomStatus, Nodes.IOSImage AS node_IOSImage, Nodes.IOSVersion AS node_IOSVersion, Nodes.GroupStatus AS node_GroupStatus, Nodes.LastBoot AS node_LastBoot, Nodes.SystemUpTime AS node_SystemUpTime, Nodes.ResponseTime AS node_ResponseTime, Nodes.PercentLoss AS node_PercentLoss, Nodes.AvgResponseTime AS node_AvgResponseTime, Nodes.MinResponseTime AS node_MinResponseTime, Nodes.MaxResponseTime AS node_MaxResponseTime, Nodes.CPUCount AS node_CPUCount, Nodes.CPULoad AS node_CPULoad, Nodes.MemoryUsed AS node_MemoryUsed, Nodes.MemoryAvailable AS node_MemoryAvailable, Nodes.PercentMemoryUsed AS node_PercentMemoryUsed, Nodes.PercentMemoryAvailable AS node_PercentMemoryAvailable, Nodes.LastSync AS node_LastSync, Nodes.LastSystemUpTimePollUtc AS node_LastSystemUpTimePollUtc, Nodes.MachineType AS node_MachineType, Nodes.IsServer AS node_IsServer, Nodes.Severity AS node_Severity, Nodes.UiSeverity AS node_UiSeverity, Nodes.ChildStatus AS node_ChildStatus, Nodes.Allow64BitCounters AS node_Allow64BitCounters, Nodes.AgentPort AS node_AgentPort, Nodes.TotalMemory AS node_TotalMemory, Nodes.CMTS AS node_CMTS, Nodes.CustomPollerLastStatisticsPoll AS node_CustomPollerLastStatisticsPoll, Nodes.CustomPollerLastStatisticsPollSuccess AS node_CustomPollerLastStatisticsPollSuccess, Nodes.SNMPVersion AS node_SNMPVersion, Nodes.PollInterval AS node_PollInterval, Nodes.EngineID AS node_EngineID, Nodes.RediscoveryInterval AS node_RediscoveryInterval, Nodes.NextPoll AS node_NextPoll, Nodes.NextRediscovery AS node_NextRediscovery, Nodes.StatCollection AS node_StatCollection, Nodes.External AS node_External, Nodes.Community AS node_Community, Nodes.RWCommunity AS node_RWCommunity, Nodes.IP AS node_IP, Nodes.IPAddressGUID AS node_IPAddressGUID, Nodes.BlockUntil AS node_BlockUntil, Nodes.OrionIdPrefix AS node_OrionIdPrefix, Nodes.OrionIdColumn AS node_OrionIdColumn, Nodes.EntityType AS node_EntityType, Nodes.DetailsUrl AS node_DetailsUrl, Nodes.DisplayName AS node_DisplayName, Nodes.Category AS node_Category, Nodes.IsOrionServer AS node_IsOrionServer, Nodes.UnManaged AS node_UnManaged, Nodes.UnManageFrom AS node_UnManageFrom, Nodes.UnManageUntil AS node_UnManageUntil, Nodes.Image AS node_Image, Nodes.InstanceType AS node_InstanceType, Nodes.Uri AS node_Uri, Nodes.InstanceSiteId AS node_InstanceSiteId, HardwareInfo.Manufacturer AS hardware_Manufacturer, HardwareInfo.Model AS hardware_Model, HardwareInfo.ServiceTag AS hardware_ServiceTag, HardwareInfo.IsDisabled AS hardware_IsDisabled, HardwareInfo.CategoriesWithProblems AS hardware_CategoriesWithProblems, HardwareInfo.CategoriesWithStatus AS hardware_CategoriesWithStatus, HardwareInfo.FullyQualifiedName AS hardware_FullyQualifiedName, HardwareInfo.UnManaged AS hardware_UnManaged, HardwareInfo.StatusDescription AS hardware_StatusDescription, HardwareInfo.Description AS hardware_Description, HardwareInfo.StatusLED AS hardware_StatusLED, HardwareInfo.DisplayName AS hardware_DisplayName, ActiveAlerts.AlertID AS alert_AlertID, ActiveAlerts.AlertTime AS alert_AlertTime, ActiveAlerts.ObjectType AS alert_ObjectType, ActiveAlerts.ObjectID AS alert_ObjectID, ActiveAlerts.ObjectName AS alert_ObjectName, ActiveAlerts.EventMessage AS alert_EventMessage, ActiveAlerts.PropertyID AS alert_PropertyID, ActiveAlerts.Monitoredproperty AS alert_Monitoredproperty, ActiveAlerts.CurrentValue AS alert_CurrentValue, ActiveAlerts.TriggerValue AS alert_TriggerValue, ActiveAlerts.ResetValue AS alert_ResetValue, ActiveAlerts.EngineID AS alert_EngineID, ActiveAlerts.AlertNotes AS alert_AlertNotes, ActiveAlerts.ExpireTime AS alert_ExpireTime, ActiveAlerts.Description AS alert_Description, ActiveAlerts.InstanceType AS alert_InstanceType, ActiveAlerts.Uri AS alert_Uri, ActiveAlerts.InstanceSiteId AS alert_InstanceSiteId, Interfaces.InterfaceID AS interface_InterfaceID, Interfaces.ObjectSubType AS interface_ObjectSubType, Interfaces.Name AS interface_Name, Interfaces.TypeName AS interface_TypeName, Interfaces.TypeDescription AS interface_TypeDescription, Interfaces.Speed AS interface_Speed, Interfaces.MTU AS interface_MTU, Interfaces.PhysicalAddress AS interface_PhysicalAddress, Interfaces.AdminStatus AS interface_AdminStatus, Interfaces.OperStatus AS interface_OperStatus, Interfaces.StatusIcon AS interface_StatusIcon, Interfaces.InBandwidth AS interface_InBandwidth, Interfaces.OutBandwidth AS interface_OutBandwidth, Interfaces.Caption AS interface_Caption, Interfaces.FullName AS interface_FullName, Interfaces.Alias AS interface_Alias, Interfaces.IfName AS interface_IfName, Interfaces.CustomBandwidth AS interface_CustomBandwidth, Interfaces.InterfaceSpeed AS interface_InterfaceSpeed, Interfaces.InterfaceCaption AS interface_InterfaceCaption, Interfaces.MAC AS interface_MAC, Interfaces.InterfaceName AS interface_InterfaceName, Interfaces.InterfaceTypeName AS interface_InterfaceTypeName, Interfaces.InterfaceAlias AS interface_InterfaceAlias, Interfaces.InterfaceMTU AS interface_InterfaceMTU, Interfaces.InterfaceTypeDescription AS interface_InterfaceTypeDescription, Interfaces.DuplexMode AS interface_DuplexMode, Interfaces.Status AS interface_Status, Interfaces.Description AS interface_Description, Interfaces.StatusDescription AS interface_StatusDescription, Interfaces.UnManaged AS interface_UnManaged, Interfaces.DisplayName AS interface_DisplayName, NodesCustomProperties.CIDN AS custom_CIDN, NodesCustomProperties.CarriageType AS custom_CarriageType, NodesCustomProperties.AddressId AS custom_AddressId, NodesCustomProperties.DevAlias AS custom_DevAlias, NodesCustomProperties.DevClass AS custom_DevClass, NodesCustomProperties.DevStatus AS custom_DevStatus, NodesCustomProperties.SiteName AS custom_SiteName, NodesCustomProperties.SiteType AS custom_SiteType, NodesCustomProperties._3LC AS custom_3LC, NodesCustomProperties._3PPConnected AS custom_3PPConnected, NodesCustomProperties.IOM_BAU_Status AS custom_IOM_BAU_Status, NodesCustomProperties.CPUThreshold AS custom_MgmtClass, NodesCustomProperties.IndependenceStatus AS custom_IndependenceStatus, NodesCustomProperties.swCustomer AS custom_swCustomer, NodesCustomProperties.RoomNumber AS custom_RoomNumber, NodesCustomProperties.FloorNumber AS custom_FloorNumber, NodesCustomProperties.StreetNumber AS custom_StreetNumber, NodesCustomProperties.StreetName AS custom_StreetName, NodesCustomProperties.StreetType AS custom_StreetType, NodesCustomProperties.StreetSuffix AS custom_StreetSuffix, NodesCustomProperties.City AS custom_City, NodesCustomProperties.PostCode AS custom_PostCode, NodesCustomProperties.State AS custom_State, NodesCustomProperties.Country AS custom_Country, NodesCustomProperties.Latitude AS custom_Latitude, NodesCustomProperties.Longitude AS custom_Longitude, InterfacesCustomProperties.InterfaceAlertEnable AS custom_InterfaceAlertEnable, InterfacesCustomProperties.InterfaceCategory AS custom_InterfaceCategory, InterfacesCustomProperties.InterfaceRole AS custom_InterfaceRole FROM Orion.Nodes AS Nodes LEFT JOIN Orion.HardwareHealth.HardwareInfo AS HardwareInfo ON Nodes.NodeID = HardwareInfo.NodeID LEFT JOIN Orion.ActiveAlerts AS ActiveAlerts ON Nodes.NodeID = ActiveAlerts.NodeID LEFT JOIN Orion.NPM.Interfaces AS Interfaces ON Nodes.NodeID = Interfaces.NodeID LEFT JOIN Orion.NodesCustomProperties AS NodesCustomProperties ON Nodes.NodeID = NodesCustomProperties.NodeID LEFT JOIN Orion.NPM.InterfacesCustomProperties AS InterfacesCustomProperties ON Interfaces.InterfaceID = InterfacesCustomProperties.InterfaceID WHERE Nodes.NodeName = '${parameters.deviceName}'`\n});", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.SolarWindsUserName}:${config.SolarWindsPassword}`)}`\n});", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "SolarWindsSADfe"}, {"name": "SolarWindsTOLL", "active": true, "description": "SolarWinds Instance for TOLL", "apiType": "rest", "method": "get", "parameters": ["deviceName"], "baseUrl": "config.SolarWinds.tollBaseURI", "uri": "'SolarWinds/InformationService/v3/Json/Query'", "queryParams": "new Object({\n query: `SELECT Nodes.NodeID AS node_NodeID, Nodes.ObjectSubType AS node_ObjectSubType, Nodes.IPAddress AS node_IPAddress, Nodes.IPAddressType AS node_IPAddressType, Nodes.DynamicIP AS node_DynamicIP, Nodes.Caption AS node_Caption, Nodes.NodeDescription AS node_NodeDescription, Nodes.Description AS node_Description, Nodes.DNS AS node_DNS, Nodes.SysName AS node_SysName, Nodes.Vendor AS node_Vendor, Nodes.SysObjectID AS node_SysObjectID, Nodes.Location AS node_Location, Nodes.Contact AS node_Contact, Nodes.VendorIcon AS node_VendorIcon, Nodes.Icon AS node_Icon, Nodes.Status AS node_Status, Nodes.StatusLED AS node_StatusLED, Nodes.StatusDescription AS node_StatusDescription, Nodes.CustomStatus AS node_CustomStatus, Nodes.IOSImage AS node_IOSImage, Nodes.IOSVersion AS node_IOSVersion, Nodes.GroupStatus AS node_GroupStatus, Nodes.LastBoot AS node_LastBoot, Nodes.SystemUpTime AS node_SystemUpTime, Nodes.ResponseTime AS node_ResponseTime, Nodes.PercentLoss AS node_PercentLoss, Nodes.AvgResponseTime AS node_AvgResponseTime, Nodes.MinResponseTime AS node_MinResponseTime, Nodes.MaxResponseTime AS node_MaxResponseTime, Nodes.CPUCount AS node_CPUCount, Nodes.CPULoad AS node_CPULoad, Nodes.MemoryUsed AS node_MemoryUsed, Nodes.MemoryAvailable AS node_MemoryAvailable, Nodes.PercentMemoryUsed AS node_PercentMemoryUsed, Nodes.PercentMemoryAvailable AS node_PercentMemoryAvailable, Nodes.LastSync AS node_LastSync, Nodes.LastSystemUpTimePollUtc AS node_LastSystemUpTimePollUtc, Nodes.MachineType AS node_MachineType, Nodes.IsServer AS node_IsServer, Nodes.Severity AS node_Severity, Nodes.UiSeverity AS node_UiSeverity, Nodes.ChildStatus AS node_ChildStatus, Nodes.Allow64BitCounters AS node_Allow64BitCounters, Nodes.AgentPort AS node_AgentPort, Nodes.TotalMemory AS node_TotalMemory, Nodes.CMTS AS node_CMTS, Nodes.CustomPollerLastStatisticsPoll AS node_CustomPollerLastStatisticsPoll, Nodes.CustomPollerLastStatisticsPollSuccess AS node_CustomPollerLastStatisticsPollSuccess, Nodes.SNMPVersion AS node_SNMPVersion, Nodes.PollInterval AS node_PollInterval, Nodes.EngineID AS node_EngineID, Nodes.RediscoveryInterval AS node_RediscoveryInterval, Nodes.NextPoll AS node_NextPoll, Nodes.NextRediscovery AS node_NextRediscovery, Nodes.StatCollection AS node_StatCollection, Nodes.External AS node_External, Nodes.Community AS node_Community, Nodes.RWCommunity AS node_RWCommunity, Nodes.IP AS node_IP, Nodes.IPAddressGUID AS node_IPAddressGUID, Nodes.BlockUntil AS node_BlockUntil, Nodes.OrionIdPrefix AS node_OrionIdPrefix, Nodes.OrionIdColumn AS node_OrionIdColumn, Nodes.EntityType AS node_EntityType, Nodes.DetailsUrl AS node_DetailsUrl, Nodes.DisplayName AS node_DisplayName, Nodes.Category AS node_Category, Nodes.IsOrionServer AS node_IsOrionServer, Nodes.UnManaged AS node_UnManaged, Nodes.UnManageFrom AS node_UnManageFrom, Nodes.UnManageUntil AS node_UnManageUntil, Nodes.Image AS node_Image, Nodes.InstanceType AS node_InstanceType, Nodes.Uri AS node_Uri, Nodes.InstanceSiteId AS node_InstanceSiteId, HardwareInfo.Manufacturer AS hardware_Manufacturer, HardwareInfo.Model AS hardware_Model, HardwareInfo.ServiceTag AS hardware_ServiceTag, HardwareInfo.IsDisabled AS hardware_IsDisabled, HardwareInfo.CategoriesWithProblems AS hardware_CategoriesWithProblems, HardwareInfo.CategoriesWithStatus AS hardware_CategoriesWithStatus, HardwareInfo.FullyQualifiedName AS hardware_FullyQualifiedName, HardwareInfo.UnManaged AS hardware_UnManaged, HardwareInfo.StatusDescription AS hardware_StatusDescription, HardwareInfo.Description AS hardware_Description, HardwareInfo.StatusLED AS hardware_StatusLED, HardwareInfo.DisplayName AS hardware_DisplayName, ActiveAlerts.AlertID AS alert_AlertID, ActiveAlerts.AlertTime AS alert_AlertTime, ActiveAlerts.ObjectType AS alert_ObjectType, ActiveAlerts.ObjectID AS alert_ObjectID, ActiveAlerts.ObjectName AS alert_ObjectName, ActiveAlerts.EventMessage AS alert_EventMessage, ActiveAlerts.PropertyID AS alert_PropertyID, ActiveAlerts.Monitoredproperty AS alert_Monitoredproperty, ActiveAlerts.CurrentValue AS alert_CurrentValue, ActiveAlerts.TriggerValue AS alert_TriggerValue, ActiveAlerts.ResetValue AS alert_ResetValue, ActiveAlerts.EngineID AS alert_EngineID, ActiveAlerts.AlertNotes AS alert_AlertNotes, ActiveAlerts.ExpireTime AS alert_ExpireTime, ActiveAlerts.Description AS alert_Description, ActiveAlerts.InstanceType AS alert_InstanceType, ActiveAlerts.Uri AS alert_Uri, ActiveAlerts.InstanceSiteId AS alert_InstanceSiteId, Interfaces.InterfaceID AS interface_InterfaceID, Interfaces.ObjectSubType AS interface_ObjectSubType, Interfaces.Name AS interface_Name, Interfaces.TypeName AS interface_TypeName, Interfaces.TypeDescription AS interface_TypeDescription, Interfaces.Speed AS interface_Speed, Interfaces.MTU AS interface_MTU, Interfaces.PhysicalAddress AS interface_PhysicalAddress, Interfaces.AdminStatus AS interface_AdminStatus, Interfaces.OperStatus AS interface_OperStatus, Interfaces.StatusIcon AS interface_StatusIcon, Interfaces.InBandwidth AS interface_InBandwidth, Interfaces.OutBandwidth AS interface_OutBandwidth, Interfaces.Caption AS interface_Caption, Interfaces.FullName AS interface_FullName, Interfaces.Alias AS interface_Alias, Interfaces.IfName AS interface_IfName, Interfaces.CustomBandwidth AS interface_CustomBandwidth, Interfaces.InterfaceSpeed AS interface_InterfaceSpeed, Interfaces.InterfaceCaption AS interface_InterfaceCaption, Interfaces.MAC AS interface_MAC, Interfaces.InterfaceName AS interface_InterfaceName, Interfaces.InterfaceTypeName AS interface_InterfaceTypeName, Interfaces.InterfaceAlias AS interface_InterfaceAlias, Interfaces.InterfaceMTU AS interface_InterfaceMTU, Interfaces.InterfaceTypeDescription AS interface_InterfaceTypeDescription, Interfaces.DuplexMode AS interface_DuplexMode, Interfaces.Status AS interface_Status, Interfaces.Description AS interface_Description, Interfaces.StatusDescription AS interface_StatusDescription, Interfaces.UnManaged AS interface_UnManaged, Interfaces.DisplayName AS interface_DisplayName, NodesCustomProperties.CIDN AS custom_CIDN,  NodesCustomProperties.AddressId AS AddressId, NodesCustomProperties.DevAlias AS custom_DevAlias, NodesCustomProperties.DevClass AS custom_DevClass, NodesCustomProperties.DevStatus AS custom_DevStatus, NodesCustomProperties.SiteName AS custom_SiteName, NodesCustomProperties.SiteType AS custom_SiteType, NodesCustomProperties._3LC AS custom_3LC, NodesCustomProperties._3PPConnected AS custom_3PPConnected, NodesCustomProperties.IOM_BAU_Status AS custom_IOM_BAU_Status, NodesCustomProperties.CPUThreshold AS custom_MgmtClass, NodesCustomProperties.swCustomer AS custom_swCustomer, NodesCustomProperties.RoomNumber AS custom_RoomNumber, NodesCustomProperties.FloorNumber AS custom_FloorNumber, NodesCustomProperties.StreetNumber AS custom_StreetNumber, NodesCustomProperties.StreetName AS custom_StreetName, NodesCustomProperties.StreetType AS custom_StreetType, NodesCustomProperties.StreetSuffix AS custom_StreetSuffix, NodesCustomProperties.City AS custom_City, NodesCustomProperties.PostCode AS custom_PostCode, NodesCustomProperties.State AS custom_State, NodesCustomProperties.Country AS custom_Country, NodesCustomProperties.Latitude AS custom_Latitude, NodesCustomProperties.Longitude AS custom_Longitude, InterfacesCustomProperties.InterfaceAlertEnable  AS custom_InterfaceAlertEnable\\nFROM Orion.Nodes AS Nodes \\nLEFT JOIN Orion.HardwareHealth.HardwareInfo AS HardwareInfo ON Nodes.NodeID = HardwareInfo.NodeID \\nLEFT JOIN Orion.ActiveAlerts AS ActiveAlerts ON Nodes.NodeID = ActiveAlerts.NodeID \\nLEFT JOIN Orion.NPM.Interfaces AS Interfaces ON Nodes.NodeID = Interfaces.NodeID LEFT JOIN Orion.NodesCustomProperties AS NodesCustomProperties ON Nodes.NodeID = NodesCustomProperties.NodeID LEFT JOIN Orion.NPM.InterfacesCustomProperties AS InterfacesCustomProperties ON Interfaces.InterfaceID = InterfacesCustomProperties.InterfaceID WHERE Nodes.NodeName = '${parameters.deviceName}'`\n});", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.SolarWindsUserName}:${config.SolarWindsPassword}`)}`\n});", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "SolarWindsTOLL"}, {"name": "SolarWindsVTS", "active": true, "description": "SolarWinds Instance for VTS", "apiType": "rest", "method": "get", "parameters": ["deviceName"], "baseUrl": "config.SolarWinds.vtsBaseURI", "uri": "'SolarWinds/InformationService/v3/Json/Query'", "queryParams": "new Object({\n query: `SELECT Nodes.NodeID AS node_NodeID, Nodes.ObjectSubType AS node_ObjectSubType, Nodes.IPAddress AS node_IPAddress, Nodes.IPAddressType AS node_IPAddressType, Nodes.DynamicIP AS node_DynamicIP, Nodes.Caption AS node_Caption, Nodes.NodeDescription AS node_NodeDescription, Nodes.Description AS node_Description, Nodes.DNS AS node_DNS, Nodes.SysName AS node_SysName, Nodes.Vendor AS node_Vendor, Nodes.SysObjectID AS node_SysObjectID, Nodes.Location AS node_Location, Nodes.Contact AS node_Contact, Nodes.VendorIcon AS node_VendorIcon, Nodes.Icon AS node_Icon, Nodes.Status AS node_Status, Nodes.StatusLED AS node_StatusLED, Nodes.StatusDescription AS node_StatusDescription, Nodes.CustomStatus AS node_CustomStatus, Nodes.IOSImage AS node_IOSImage, Nodes.IOSVersion AS node_IOSVersion, Nodes.GroupStatus AS node_GroupStatus, Nodes.LastBoot AS node_LastBoot, Nodes.SystemUpTime AS node_SystemUpTime, Nodes.ResponseTime AS node_ResponseTime, Nodes.PercentLoss AS node_PercentLoss, Nodes.AvgResponseTime AS node_AvgResponseTime, Nodes.MinResponseTime AS node_MinResponseTime, Nodes.MaxResponseTime AS node_MaxResponseTime, Nodes.CPUCount AS node_CPUCount, Nodes.CPULoad AS node_CPULoad, Nodes.MemoryUsed AS node_MemoryUsed, Nodes.MemoryAvailable AS node_MemoryAvailable, Nodes.PercentMemoryUsed AS node_PercentMemoryUsed, Nodes.PercentMemoryAvailable AS node_PercentMemoryAvailable, Nodes.LastSync AS node_LastSync, Nodes.LastSystemUpTimePollUtc AS node_LastSystemUpTimePollUtc, Nodes.MachineType AS node_MachineType, Nodes.IsServer AS node_IsServer, Nodes.Severity AS node_Severity, Nodes.UiSeverity AS node_UiSeverity, Nodes.ChildStatus AS node_ChildStatus, Nodes.Allow64BitCounters AS node_Allow64BitCounters, Nodes.AgentPort AS node_AgentPort, Nodes.TotalMemory AS node_TotalMemory, Nodes.CMTS AS node_CMTS, Nodes.CustomPollerLastStatisticsPoll AS node_CustomPollerLastStatisticsPoll, Nodes.CustomPollerLastStatisticsPollSuccess AS node_CustomPollerLastStatisticsPollSuccess, Nodes.SNMPVersion AS node_SNMPVersion, Nodes.PollInterval AS node_PollInterval, Nodes.EngineID AS node_EngineID, Nodes.RediscoveryInterval AS node_RediscoveryInterval, Nodes.NextPoll AS node_NextPoll, Nodes.NextRediscovery AS node_NextRediscovery, Nodes.StatCollection AS node_StatCollection, Nodes.External AS node_External, Nodes.Community AS node_Community, Nodes.RWCommunity AS node_RWCommunity, Nodes.IP AS node_IP, Nodes.IPAddressGUID AS node_IPAddressGUID, Nodes.BlockUntil AS node_BlockUntil, Nodes.OrionIdPrefix AS node_OrionIdPrefix, Nodes.OrionIdColumn AS node_OrionIdColumn, Nodes.EntityType AS node_EntityType, Nodes.DetailsUrl AS node_DetailsUrl, Nodes.DisplayName AS node_DisplayName, Nodes.Category AS node_Category, Nodes.IsOrionServer AS node_IsOrionServer, Nodes.UnManaged AS node_UnManaged, Nodes.UnManageFrom AS node_UnManageFrom, Nodes.UnManageUntil AS node_UnManageUntil, Nodes.Image AS node_Image, Nodes.InstanceType AS node_InstanceType, Nodes.Uri AS node_Uri, Nodes.InstanceSiteId AS node_InstanceSiteId, HardwareInfo.Manufacturer AS hardware_Manufacturer, HardwareInfo.Model AS hardware_Model, HardwareInfo.ServiceTag AS hardware_ServiceTag, HardwareInfo.IsDisabled AS hardware_IsDisabled, HardwareInfo.CategoriesWithProblems AS hardware_CategoriesWithProblems, HardwareInfo.CategoriesWithStatus AS hardware_CategoriesWithStatus, HardwareInfo.FullyQualifiedName AS hardware_FullyQualifiedName, HardwareInfo.UnManaged AS hardware_UnManaged, HardwareInfo.StatusDescription AS hardware_StatusDescription, HardwareInfo.Description AS hardware_Description, HardwareInfo.StatusLED AS hardware_StatusLED, HardwareInfo.DisplayName AS hardware_DisplayName, ActiveAlerts.AlertID AS alert_AlertID, ActiveAlerts.AlertTime AS alert_AlertTime, ActiveAlerts.ObjectType AS alert_ObjectType, ActiveAlerts.ObjectID AS alert_ObjectID, ActiveAlerts.ObjectName AS alert_ObjectName, ActiveAlerts.EventMessage AS alert_EventMessage, ActiveAlerts.PropertyID AS alert_PropertyID, ActiveAlerts.Monitoredproperty AS alert_Monitoredproperty, ActiveAlerts.CurrentValue AS alert_CurrentValue, ActiveAlerts.TriggerValue AS alert_TriggerValue, ActiveAlerts.ResetValue AS alert_ResetValue, ActiveAlerts.EngineID AS alert_EngineID, ActiveAlerts.AlertNotes AS alert_AlertNotes, ActiveAlerts.ExpireTime AS alert_ExpireTime, ActiveAlerts.Description AS alert_Description, ActiveAlerts.InstanceType AS alert_InstanceType, ActiveAlerts.Uri AS alert_Uri, ActiveAlerts.InstanceSiteId AS alert_InstanceSiteId, Interfaces.InterfaceID AS interface_InterfaceID, Interfaces.ObjectSubType AS interface_ObjectSubType, Interfaces.Name AS interface_Name, Interfaces.TypeName AS interface_TypeName, Interfaces.TypeDescription AS interface_TypeDescription, Interfaces.Speed AS interface_Speed, Interfaces.MTU AS interface_MTU, Interfaces.PhysicalAddress AS interface_PhysicalAddress, Interfaces.AdminStatus AS interface_AdminStatus, Interfaces.OperStatus AS interface_OperStatus, Interfaces.StatusIcon AS interface_StatusIcon, Interfaces.InBandwidth AS interface_InBandwidth, Interfaces.OutBandwidth AS interface_OutBandwidth, Interfaces.Caption AS interface_Caption, Interfaces.FullName AS interface_FullName, Interfaces.Alias AS interface_Alias, Interfaces.IfName AS interface_IfName, Interfaces.CustomBandwidth AS interface_CustomBandwidth, Interfaces.InterfaceSpeed AS interface_InterfaceSpeed, Interfaces.InterfaceCaption AS interface_InterfaceCaption, Interfaces.MAC AS interface_MAC, Interfaces.InterfaceName AS interface_InterfaceName, Interfaces.InterfaceTypeName AS interface_InterfaceTypeName, Interfaces.InterfaceAlias AS interface_InterfaceAlias, Interfaces.InterfaceMTU AS interface_InterfaceMTU, Interfaces.InterfaceTypeDescription AS interface_InterfaceTypeDescription, Interfaces.DuplexMode AS interface_DuplexMode, Interfaces.Status AS interface_Status, Interfaces.Description AS interface_Description, Interfaces.StatusDescription AS interface_StatusDescription, Interfaces.UnManaged AS interface_UnManaged, Interfaces.DisplayName AS interface_DisplayName, NodesCustomProperties.CIDN AS custom_CIDN, NodesCustomProperties.CarriageType AS custom_CarriageType, NodesCustomProperties.AddressId AS custom_AddressId, NodesCustomProperties.DevAlias AS custom_DevAlias, NodesCustomProperties.DevClass AS custom_DevClass, NodesCustomProperties.DevStatus AS custom_DevStatus, NodesCustomProperties.SiteName AS custom_SiteName, NodesCustomProperties.SiteType AS custom_SiteType, NodesCustomProperties._3LC AS custom_3LC, NodesCustomProperties._3PPConnected AS custom_3PPConnected, NodesCustomProperties.IOM_BAU_Status AS custom_IOM_BAU_Status, NodesCustomProperties.CPUThreshold AS custom_MgmtClass, NodesCustomProperties.IndependenceStatus AS custom_IndependenceStatus, NodesCustomProperties.swCustomer AS custom_swCustomer, NodesCustomProperties.RoomNumber AS custom_RoomNumber, NodesCustomProperties.FloorNumber AS custom_FloorNumber, NodesCustomProperties.StreetNumber AS custom_StreetNumber, NodesCustomProperties.StreetName AS custom_StreetName, NodesCustomProperties.StreetType AS custom_StreetType, NodesCustomProperties.StreetSuffix AS custom_StreetSuffix, NodesCustomProperties.City AS custom_City, NodesCustomProperties.PostCode AS custom_PostCode, NodesCustomProperties.State AS custom_State, NodesCustomProperties.Country AS custom_Country, NodesCustomProperties.Latitude AS custom_Latitude, NodesCustomProperties.Longitude AS custom_Longitude, InterfacesCustomProperties.InterfaceAlertEnable AS custom_InterfaceAlertEnable, InterfacesCustomProperties.InterfaceCategory AS custom_InterfaceCategory, InterfacesCustomProperties.InterfaceRole AS custom_InterfaceRole FROM Orion.Nodes AS Nodes LEFT JOIN Orion.HardwareHealth.HardwareInfo AS HardwareInfo ON Nodes.NodeID = HardwareInfo.NodeID LEFT JOIN Orion.ActiveAlerts AS ActiveAlerts ON Nodes.NodeID = ActiveAlerts.NodeID LEFT JOIN Orion.NPM.Interfaces AS Interfaces ON Nodes.NodeID = Interfaces.NodeID LEFT JOIN Orion.NodesCustomProperties AS NodesCustomProperties ON Nodes.NodeID = NodesCustomProperties.NodeID LEFT JOIN Orion.NPM.InterfacesCustomProperties AS InterfacesCustomProperties ON Interfaces.InterfaceID = InterfacesCustomProperties.InterfaceID WHERE Nodes.NodeName = '${parameters.deviceName}'`\n});", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.SolarWindsUserName}:${config.SolarWindsPassword}`)}`\n});", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "SolarWindsVTS"}, {"name": "SplunkAAASC", "active": true, "description": "Search Splunk for index AAASC in syslog", "apiType": "rest", "method": "post", "parameters": ["fnn"], "baseUrl": "config.SplunkAAACS.diagURI", "uri": "", "queryParams": "", "body": "`\"search=search sourcetype=syslog index=aaasc '${parameters.fnn}' START OR STOP OR auth | head 500&earliest_time=-7d&output_mode=json_rows\"`", "header": "`{\"Authorization\": \"Basic ${base64(`${config.robotAccount01Username}:${config.robotAccount01Password}`)}\", \"Content-Type\": \"application/x-www-url-formencoded\"}`", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "SplunkAAASC"}, {"name": "SplunkDMSLog", "active": true, "description": "Search Splunk in DMS Log for last 7 days", "apiType": "rest", "method": "post", "parameters": ["versionMAC", "dateFrom", "dateTo"], "baseUrl": "config.SplunkAAACS.diagURI", "uri": "", "queryParams": "", "body": "// Getting top 500 of past 7 days of event data\n`\"search=search+index%3Dtipt*-app-n+sourcetype%3Daccess+host%3Dlxxsp10*+GET+%28%28%5C.cfg+NOT+%28web+OR+license%29%29+OR+%28%5C.xml+NOT+%28calls+OR+directory%29%29+OR+%5C.conf%29+*${parameters.versionMAC}*+%0A` +\n`%7C+head+500%0A`+ \n`%7C+rex+field%3D_raw+%22%5E%28%3F%3CSourceIP%3E%5CS%2B%29+%5B-%5Cd%5D%2B+%5B-%5Cd%2B%5D%2B+%5C%5B%28%3F%3CTime%3E%5B%5E%5C%5D%5D%2B%29%5C%5D+%5C%22%28%3F%3C`+ \n`Method%3E%5CS%2B%29+%28%3F%3CResource%3E%5CS%2B%29+%5CS%2B%5C%22+%28%3F%3CStatus_Code%3E%5Cd%2B%29+%28%3F%3CPUT_Size%3E%5B%5Cd%5C-%5D%2B%29%5Cs%28%3F%3C`+\n`GET_Size%3E%5B%5Cd%5C-%5D%2B%29%5Cs%28%3F%3CServe_Secs%3E%5B%5Cd%5C-%5C.%5D%2B%29.*%3F%28%3F%3CUserAgent%3E%5Cw%5B%5E%5C%22%5D*Type%2F%5Cw%2B%29%5C%22%22%0A` + \n`%7C+eval+Time%3Dstrftime%28strptime%28Time%2C+%22%25d%2F%25b%2F%25Y%3A%25H%3A%25M%3A%25S+%25z%22%29%2C+%22%25Y%2F%25m%2F%25d+%25H%3A%25M%3A%25S%22%29%0A` + \n`%7C+rename+SourceIP+as+SrcIP%2C+Resource+as+File%2C+Status_Code+as+Code%2C+Serve_Secs+as+Secs%2C+GET_Size+as+%22DOWN%22%2C+PUT_Size+as+%22UP%22%2C+host+as+Host%0A` + \n`%7C+table+Time%2C+SrcIP%2C+Method%2C+File%2C+Code%2C+Secs%2C+%22DOWN%22%2C+%22UP%22%2C+UserAgent%2C+Host%2C%0A`+\n`%7C+sort+-Time&output_mode=json&preview=false&earliest_time=${parameters.dateFrom}&latest_time=${parameters.dateTo}\"`", "header": "`{\"Authorization\": \"Basic ${base64(`${config.robotAccount01Username}:${config.robotAccount01Password}`)}\", \"Content-Type\": \"application/x-www-url-formencoded\"}`", "authKeyDb": [], "timeout": 300000, "parseResponse": "// convert into proper json data for displaying\nlet resultJson = \"\";\nif(response.data && typeof response.data === \"string\" && response.data.includes(\"result\"))\n{\n    const fixedData = `[${response.data.replace(/}\\n{/g, '},{')}]`;\n    const dataArray = JSON.parse(fixedData);\n    resultJson = dataArray.reduce((acc, item, index) => {\n      acc.push(item.result);\n      return acc;\n    }, []);\n}", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": ""}, {"name": "SSP", "active": true, "description": "Server status page (SSP) gives outages details of most of the products support by Merge - using this to get reliable conen data", "apiType": "rest", "method": "get", "parameters": ["fnn"], "baseUrl": "config.MergeExternalCollectorAPI.baseURI", "uri": "`service-status/search-fnn`", "queryParams": "new Object({\n    fnn: parameters.fnn,\n    content_type:'application/json'\n});", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.mergeExternalCollectorUsername}:${config.mergeExternalCollectorPassword}`)}`\n});", "authKeyDb": [], "timeout": 300000, "parseResponse": "if (response.data) {\n    let keyNames = ['current', 'events', 'past', 'future', 'near_future', 'far_future'];\n    for (let keyName of keyNames) {\n        if (Array.isArray(response.data[keyName])) {\n            for (let i = 0; i < response.data[keyName].length; i++) {\n                delete response.data[keyName][i].area;\n            }\n        }\n    }\n}\nresponse.data;", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "typeof response.events !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": ""}, {"name": "TID", "active": true, "description": "", "apiType": "rest", "method": "get", "parameters": ["TIDsid", "TIDcommands", "tidIPv4Address", "customerIPv4Address", "routerName", "interfaceName"], "baseUrl": "config.MergeExternalCollectorAPI.baseURI", "uri": "'tid';", "queryParams": "new Object({\n    fnn: parameters.TIDsid,\n    commands: parameters.TIDcommands,\n    tidIPv4Address: parameters.tidIPv4Address,\n    customerIPv4Address: parameters.customerIPv4Address,\n    routerName: parameters.routerName,\n    interfaceName: parameters.interfaceName,\n    // Note: The routing prefix number is currently hardcoded as it\n    // is consistent across all of TID (currently)\n    // May need to be revised / collected from another source if it is dynamic\n    routingPrefix: '*********'\n});", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.mergeExternalCollectorUsername}:${config.mergeExternalCollectorPassword}`)}`\n});", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "TID"}, {"name": "TIPTConfluence", "active": false, "description": "TIPT Confluence Data", "apiType": "rest", "method": "get", "parameters": ["<PERSON><PERSON>"], "baseUrl": "config.ConfluenceURL", "uri": "`TIPT+Known+Issues`", "queryParams": "new Object({\n    expand: \"body.view\"\n});", "body": "", "header": "`{ \"Authorization\": \"Bearer ${config.ConfluenceToken}\"}`", "authKeyDb": [], "timeout": 300000, "parseResponse": "if (response && response.data && response.data.body && response.data.body.view && response.data.body.view.value) { response.data.body.view.value.replace(/\\n/g,'') } else { response.data }", "pollCondition": "", "proxyRequired": true, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "TIPT"}, {"name": "TLIVE", "active": true, "description": "TLIVE API V3", "apiType": "rest", "method": "get", "parameters": ["deviceId", "category1", "instance1", "variable1", "start1", "stop1", "step1", "start_test", "stop_test", "step_test"], "baseUrl": "config.TLIVE.diagURI", "uri": "`${parameters.deviceId}/trends`", "queryParams": "new Object({\n\tcategory: parameters.category1,\n\tinstance: parameters.instance1,\n\tvariable: parameters.variable1,\n    start: parameters.start1,\n    stop: parameters.stop1,\n    step: parameters.step1\n});", "body": "", "header": "`{ \"accept\": \"application/json\", \"Authorization\": \"${config.TLiveAP<PERSON><PERSON>ey}\"}`", "authKeyDb": [], "timeout": 60000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "T-Live"}, {"name": "TLIVEPK", "active": true, "description": "TLIVE API V3 Primary Key", "apiType": "rest", "method": "post", "parameters": ["device_name"], "baseUrl": "config.TLIVE.diagURI", "uri": "", "queryParams": "", "body": "`{ \"filters\": [ { \"value\": \"${parameters.device_name}\", \"op\": \"eq\", \"field\": \"hostname\" } ]}`", "header": "`{\"Authorization\": \"${config.TLiveAP<PERSON><PERSON>ey}\", \"Content-Type\": \"application/json\"}`", "authKeyDb": [], "timeout": 60000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "T-Live"}, {"name": "TLIVESplunk", "active": true, "description": "TLIVE ALL SPLUNK SOURCES API", "apiType": "rest", "method": "post", "parameters": ["TLIVESplunkHighSpeedBSNTU", "TLIVESplunkHighSpeedFAE1", "TLIVESplunkHighSpeedFAE2", "TLIVESplunkHighSpeedFAE3", "TLIVESplunkHighSpeedFAE4", "TLIVESplunkHighSpeedPOPSEP1", "TLIVESplunkHighSpeedPOPSEP2", "TLIVESplunkInDiscardBSNTU", "TLIVESplunkInDiscardFAE1", "TLIVESplunkInDiscardFAE2", "TLIVESplunkInDiscardFAE3", "TLIVESplunkInDiscardFAE4", "TLIVESplunkInDiscardPOPSEP1", "TLIVESplunkInDiscardPOPSEP2", "TLIVESplunkInErrorBSNTU", "TLIVESplunkInErrorFAE1", "TLIVESplunkInErrorFAE2", "TLIVESplunkInErrorFAE3", "TLIVESplunkInErrorFAE4", "TLIVESplunkInErrorPOPSEP1", "TLIVESplunkInErrorPOPSEP2", "TLIVESplunkInOctetBSNTU", "**********************", "**********************", "**********************", "**********************", "TLIVESplunkInOctetPOPSEP1", "TLIVESplunkInOctetPOPSEP2", "TLIVESplunkInPacketBSNTU", "TLIVESplunkInPacketFAE1", "TLIVESplunkInPacketFAE2", "TLIVESplunkInPacketFAE3", "TLIVESplunkInPacketFAE4", "TLIVESplunkInPacketPOPSEP1", "TLIVESplunkInPacketPOPSEP2", "TLIVESplunkOutDiscardBSNTU", "TLIVESplunkOutDiscardFAE1", "TLIVESplunkOutDiscardFAE2", "TLIVESplunkOutDiscardFAE3", "TLIVESplunkOutDiscardFAE4", "TLIVESplunkOutDiscardPOPSEP1", "TLIVESplunkOutDiscardPOPSEP2", "TLIVESplunkOutErrorBSNTU", "TLIVESplunkOutErrorFAE1", "TLIVESplunkOutErrorFAE2", "TLIVESplunkOutErrorFAE3", "TLIVESplunkOutErrorFAE4", "TLIVESplunkOutErrorPOPSEP1", "TLIVESplunkOutErrorPOPSEP2", "TLIVESplunkOutPacketBSNTU", "TLIVESplunkOutPacketFAE1", "TLIVESplunkOutPacketFAE2", "TLIVESplunkOutPacketFAE3", "TLIVESplunkOutPacketFAE4", "TLIVESplunkOutPacketPOPSEP1", "TLIVESplunkOutPacketPOPSEP2", "TLIVESplunkOutOctetBSNTU", "TLIVESplunkOutOctetFAE1", "TLIVESplunkOutOctetFAE2", "TLIVESplunkOutOctetFAE3", "TLIVESplunkOutOctetFAE4", "TLIVESplunkOutOctetPOPSEP1", "TLIVESplunkOutOctetPOPSEP2"], "baseUrl": "config.HAPIURI", "uri": "\"rTLiveSplunk.php\"", "queryParams": "", "body": "`{\"TLIVESplunkHighSpeedBSNTU\":${parameters.TLIVESplunkHighSpeedBSNTU} ,\"TLIVESplunkHighSpeedFAE1\":${parameters.TLIVESplunkHighSpeedFAE1} ,\"TLIVESplunkHighSpeedFAE2\":${parameters.TLIVESplunkHighSpeedFAE2} ,\"TLIVESplunkHighSpeedFAE3\":${parameters.TLIVESplunkHighSpeedFAE3} ,\"TLIVESplunkHighSpeedFAE4\":${parameters.TLIVESplunkHighSpeedFAE4} ,\"TLIVESplunkHighSpeedPOPSEP1\":${parameters.TLIVESplunkHighSpeedPOPSEP1} ,\"TLIVESplunkHighSpeedPOPSEP2\":${parameters.TLIVESplunkHighSpeedPOPSEP2} ,\"TLIVESplunkInDiscardBSNTU\":${parameters.TLIVESplunkInDiscardBSNTU} ,\"TLIVESplunkInDiscardFAE1\":${parameters.TLIVESplunkInDiscardFAE1} ,\"TLIVESplunkInDiscardFAE2\":${parameters.TLIVESplunkInDiscardFAE2} ,\"TLIVESplunkInDiscardFAE3\":${parameters.TLIVESplunkInDiscardFAE3} ,\"TLIVESplunkInDiscardFAE4\":${parameters.TLIVESplunkInDiscardFAE4} ,\"TLIVESplunkInDiscardPOPSEP1\":${parameters.TLIVESplunkInDiscardPOPSEP1} ,\"TLIVESplunkInDiscardPOPSEP2\":${parameters.TLIVESplunkInDiscardPOPSEP2} , \"TLIVESplunkInErrorBSNTU\":${parameters.TLIVESplunkInErrorBSNTU} ,\"TLIVESplunkInErrorFAE1\":${parameters.TLIVESplunkInErrorFAE1} ,\"TLIVESplunkInErrorFAE2\":${parameters.TLIVESplunkInErrorFAE2} ,\"TLIVESplunkInErrorFAE3\":${parameters.TLIVESplunkInErrorFAE3} ,\"TLIVESplunkInErrorFAE4\":${parameters.TLIVESplunkInErrorFAE4} ,\"TLIVESplunkInErrorPOPSEP1\":${parameters.TLIVESplunkInErrorPOPSEP1} ,\"TLIVESplunkInErrorPOPSEP2\":${parameters.TLIVESplunkInErrorPOPSEP2} ,\"TLIVESplunkInOctetBSNTU\":${parameters.TLIVESplunkInOctetBSNTU} ,\"**********************\":${parameters.**********************} ,\"**********************\":${parameters.**********************} ,\"**********************\":${parameters.**********************} ,\"**********************\":${parameters.**********************} ,\"TLIVESplunkInOctetPOPSEP1\":${parameters.TLIVESplunkInOctetPOPSEP1} ,\"TLIVESplunkInOctetPOPSEP2\":${parameters.TLIVESplunkInOctetPOPSEP2} ,\"TLIVESplunkInPacketBSNTU\":${parameters.TLIVESplunkInPacketBSNTU} ,\"TLIVESplunkInPacketFAE1\":${parameters.TLIVESplunkInPacketFAE1} ,\"TLIVESplunkInPacketFAE2\":${parameters.TLIVESplunkInPacketFAE2} ,\"TLIVESplunkInPacketFAE3\":${parameters.TLIVESplunkInPacketFAE3} ,\"TLIVESplunkInPacketFAE4\":${parameters.TLIVESplunkInPacketFAE4} ,\"TLIVESplunkInPacketPOPSEP1\":${parameters.TLIVESplunkInPacketPOPSEP1} ,\"TLIVESplunkInPacketPOPSEP2\":${parameters.TLIVESplunkInPacketPOPSEP2} ,\"TLIVESplunkOutDiscardBSNTU\":${parameters.TLIVESplunkOutDiscardBSNTU} ,\"TLIVESplunkOutDiscardFAE1\":${parameters.TLIVESplunkOutDiscardFAE1} ,\"TLIVESplunkOutDiscardFAE2\":${parameters.TLIVESplunkOutDiscardFAE2} ,\"TLIVESplunkOutDiscardFAE3\":${parameters.TLIVESplunkOutDiscardFAE3} ,\"TLIVESplunkOutDiscardFAE4\":${parameters.TLIVESplunkOutDiscardFAE4} ,\"TLIVESplunkOutDiscardPOPSEP1\":${parameters.TLIVESplunkOutDiscardPOPSEP1} ,\"TLIVESplunkOutDiscardPOPSEP2\":${parameters.TLIVESplunkOutDiscardPOPSEP2} ,\"TLIVESplunkOutErrorBSNTU\":${parameters.TLIVESplunkOutErrorBSNTU} ,\"TLIVESplunkOutErrorFAE1\":${parameters.TLIVESplunkOutErrorFAE1} ,\"TLIVESplunkOutErrorFAE2\":${parameters.TLIVESplunkOutErrorFAE2} ,\"TLIVESplunkOutErrorFAE3\":${parameters.TLIVESplunkOutErrorFAE3} ,\"TLIVESplunkOutErrorFAE4\":${parameters.TLIVESplunkOutErrorFAE4} ,\"TLIVESplunkOutErrorPOPSEP1\":${parameters.TLIVESplunkOutErrorPOPSEP1} ,\"TLIVESplunkOutErrorPOPSEP2\":${parameters.TLIVESplunkOutErrorPOPSEP2} ,\"TLIVESplunkOutPacketBSNTU\":${parameters.TLIVESplunkOutPacketBSNTU} ,\"TLIVESplunkOutPacketFAE1\":${parameters.TLIVESplunkOutPacketFAE1} ,\"TLIVESplunkOutPacketFAE2\":${parameters.TLIVESplunkOutPacketFAE2} ,\"TLIVESplunkOutPacketFAE3\":${parameters.TLIVESplunkOutPacketFAE3} ,\"TLIVESplunkOutPacketFAE4\":${parameters.TLIVESplunkOutPacketFAE4} ,\"TLIVESplunkOutPacketPOPSEP1\":${parameters.TLIVESplunkOutPacketPOPSEP1} ,\"TLIVESplunkOutPacketPOPSEP2\":${parameters.TLIVESplunkOutPacketPOPSEP2} ,\"TLIVESplunkOutOctetBSNTU\":${parameters.TLIVESplunkOutOctetBSNTU} ,\"TLIVESplunkOutOctetFAE1\":${parameters.TLIVESplunkOutOctetFAE1} ,\"TLIVESplunkOutOctetFAE2\":${parameters.TLIVESplunkOutOctetFAE2} ,\"TLIVESplunkOutOctetFAE3\":${parameters.TLIVESplunkOutOctetFAE3} ,\"TLIVESplunkOutOctetFAE4\":${parameters.TLIVESplunkOutOctetFAE4} ,\"TLIVESplunkOutOctetPOPSEP1\":${parameters.TLIVESplunkOutOctetPOPSEP1} ,\"TLIVESplunkOutOctetPOPSEP2\":${parameters.TLIVESplunkOutOctetPOPSEP2}}`", "header": "`{ \"accept\": \"application/json\", \"Authorization\": \"${config.TLiveAP<PERSON><PERSON>ey}\"}`", "authKeyDb": [], "timeout": 60000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "T-Live"}, {"name": "TRAD", "active": true, "description": "Trad to Network Elements", "apiType": "rest", "method": "get", "parameters": ["tradDevice", "tradInterfaces", "tradCommands", "tradVPNFNN", "tradPingIP", "tradVLAN"], "baseUrl": "config.HAPIURI", "uri": "\"rTRADn\"", "queryParams": "new Object({\n    device: parameters.tradDevice,\n    VLAN: parameters.tradVLAN,\n    interfaces: parameters.tradInterfaces,\n    commands: parameters.tradCommands,\n    pingIP: parameters.tradPingIP,\n    VPNFNN: parameters.tradVPNFNN\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 300000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "TRAD"}, {"name": "WECAdbor", "active": true, "description": "WEC - Adbor Mobile Service Experience Information", "apiType": "rest", "method": "post", "parameters": ["id", "<PERSON><PERSON><PERSON>", "number"], "baseUrl": "config.WEC.diagnosticsURI", "uri": "", "queryParams": "", "body": "`{\"adborid\": \"${parameters.adborid}\", \"number\": \"${parameters.number}\" }`", "header": "`{\"apikey\": \"${config.WECClientId}\", \"Content-Type\": \"application/json\"}`", "authKeyDb": [], "timeout": 180000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data.serverError", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "Telstra Mobile Assurance-V2"}, {"name": "WECLongLat", "active": true, "description": "WEC - Long Lat Mobile Service Experience Information", "apiType": "rest", "method": "post", "parameters": ["id", "latitude", "longitude"], "baseUrl": "config.WEC.diagnosticsURI", "uri": "", "queryParams": "", "body": "`{\"latitude\": \"${parameters.latitude}\", \"longitude\": \"${parameters.longitude}\" }`", "header": "`{\"apikey\": \"${config.WECClientId}\", \"Content-Type\": \"application/json\"}`", "authKeyDb": [], "timeout": 180000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data.serverError", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": "OKAPIClient", "wikiPage": "Telstra Mobile Assurance-V2"}, {"name": "<PERSON><PERSON>", "active": true, "description": "", "apiType": "rest", "method": "get", "parameters": ["ip<PERSON><PERSON><PERSON>"], "baseUrl": "config.MergeExternalCollectorAPI.baseURI", "uri": "'whois'", "queryParams": "new Object({\n    ipAddress: parameters.ipAddress\n});", "body": "", "header": "JSON.stringify({\n    'Authorization': `Basic ${base64(`${config.mergeExternalCollectorUsername}:${config.mergeExternalCollectorPassword}`)}`\n});", "authKeyDb": [], "timeout": 60000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "<PERSON><PERSON>"}, {"name": "WOLF", "active": true, "description": "", "apiType": "rest", "method": "get", "parameters": ["address", "type"], "baseUrl": "config.HAPIURI", "uri": "\"rWOLF\"", "queryParams": "new Object({\n    address: parameters.address,\n    type: parameters.type\n});", "body": "", "header": "", "authKeyDb": [], "timeout": 120000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": ""}, {"name": "XDIANDIG", "active": true, "description": "XDI API Call for ANDig", "apiType": "rest", "method": "post", "parameters": ["search_id", "owner", "type", "key", "depth"], "baseUrl": "config.HAPIURI", "uri": "'rXDILookdown'", "queryParams": "", "body": "JSON.stringify([{\n    search_id: parameters.search_id,\n    search_criteria: {\n        owner: parameters.owner,\n        type: parameters.type,\n        key: parameters.key,\n        depth: parameters.depth\n    }\n}]);", "header": "", "authKeyDb": [], "timeout": 180000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": ""}, {"name": "XDIANDIGDigiServices", "active": true, "description": "XDI API Call for ANDIG using domID, cfsID, or UUID", "apiType": "rest", "method": "post", "parameters": ["search_id", "inputType", "inputValue", "depth", "includeActiveEvents"], "baseUrl": "config.HAPIURI", "uri": "'rXDIDigiServices'", "queryParams": "", "body": "JSON.stringify([{\n    search_id: parameters.search_id,\n    search_criteria: {\n        [parameters.inputType]: parameters.inputValue,\n        depth: parameters.depth,\n        includeActiveEvents: parameters.includeActiveEvents\n    }\n}]);", "header": "", "authKeyDb": [], "timeout": 180000, "parseResponse": "", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": ""}, {"name": "XDIAVC", "active": true, "description": "XDI API Call for AVCs", "apiType": "rest", "method": "post", "parameters": ["search_id", "owner", "type", "key", "depth", "includeActiveEvents"], "baseUrl": "config.HAPIURI", "uri": "'rXDILookdown'", "queryParams": "", "body": "JSON.stringify([{\n    search_id: parameters.search_id,\n    search_criteria: {\n        owner: parameters.owner,\n        type: parameters.type,\n        key: parameters.key,\n        depth: parameters.depth,\n        includeActiveEvents: parameters.IncludeActiveEvents\n    }\n}]);", "header": "JSON.stringify({\n    Authorization: `Bearer ${config.XDIAuthToken}`,\n    'Content-Type': 'application/json'\n});", "authKeyDb": [], "timeout": 180000, "parseResponse": "if (response && response.data && response.data[0] && response.data[0].result) { response.data[0].result } else { response.data }", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "XDI"}, {"name": "XDIEventImpact", "active": true, "description": "XDI API Call for iTAM Events", "apiType": "rest", "method": "post", "parameters": ["eventId"], "baseUrl": "config.HAPIURI", "uri": "'rXDIEventImpact'", "queryParams": "", "body": "JSON.stringify({\n  eventId: parameters.eventId,\n  depth: 0,\n  includeActiveEvents: true,\n  deltaResultsOnly: true\n});", "header": "JSON.stringify({\n    Authorization: `Bearer ${config.XDIAuthToken}`,\n    'Content-Type': 'application/json'\n});", "authKeyDb": [], "timeout": 180000, "parseResponse": "if (response && response.data && response.data[0] && response.data[0].result) { response.data[0].result } else { response.data }", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "XDI"}, {"name": "XDILRD", "active": true, "description": "XDI API Call for Address using LRD", "apiType": "rest", "method": "post", "parameters": ["search_id", "owner", "type", "key", "depth"], "baseUrl": "config.HAPIURI", "uri": "'rXDILookdown'", "queryParams": "", "body": "JSON.stringify([{\n    search_id: parameters.search_id,\n    search_criteria: {\n        owner: parameters.owner,\n        type: parameters.type,\n        key: parameters.key,\n        depth: parameters.depth,\n        includeActiveEvents: false,\n        includePlannedEvents: false,\n        includeiTAMIncidents: false,\n        includeSplunkInsights: false,\n        returnPath: true,\n        timePoint: 0\n    }\n}]);", "header": "JSON.stringify({\n    Authorization: `Bearer ${config.XDIAuthToken}`,\n    'Content-Type': 'application/json'\n});", "authKeyDb": [], "timeout": 180000, "parseResponse": "if (response && response.data && response.data[0] && response.data[0].result) { response.data[0].result } else { response.data }", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "XDI"}, {"name": "XDIN2R", "active": true, "description": "XDI API Call for N2Rs", "apiType": "rest", "method": "post", "parameters": ["search_id", "id", "depth", "includeActiveEvents"], "baseUrl": "config.HAPIURI", "uri": "'rXDILookdown'", "queryParams": "", "body": "JSON.stringify([{\n    search_id: parameters.search_id,\n    search_criteria: {\n        id: parameters.id,\n        depth: parameters.depth,\n        includeActiveEvents: parameters.IncludeActiveEvents\n    }\n}]);", "header": "JSON.stringify({\n    Authorization: `Bearer ${config.XDIAuthToken}`,\n    'Content-Type': 'application/json'\n});", "authKeyDb": [], "timeout": 180000, "parseResponse": "if (response && response.data && response.data[0] && response.data[0].result) { response.data[0].result } else { response.data }", "pollCondition": "", "proxyRequired": false, "useCookies": false, "tlsMinVersion": null, "errorCondition": "response.data && typeof response.data.error !== 'undefined'", "asyncPoll": null, "asyncCallback": null, "masslCertificateName": null, "wikiPage": "XDI"}]}