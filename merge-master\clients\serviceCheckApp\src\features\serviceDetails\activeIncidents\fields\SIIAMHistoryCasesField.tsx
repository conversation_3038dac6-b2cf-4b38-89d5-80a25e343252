import { CollapsablePanel } from '../../../../components/collapsablePanel/CollapsablePanel'
import { DetailField } from '../../../../components/detailsPanel/DetailField'
import { Panel } from '../../../../components/panel/Panel'
import { formatDate } from '../../../../helpers/formatDate'
import { HistoryIncident } from '../../../../infrastructure/models'
import styles from './SIIAMHistoryCasesField.module.scss'
interface SIIAMHistoryCasesFieldProps {
    value: HistoryIncident[] | null | undefined
}

export const SIIAMHistoryCasesField = ({
    value,
}: SIIAMHistoryCasesFieldProps) => {
    if (value === null || value === undefined) {
        return (
            <Panel>
                <DetailField label="SIIAM History Cases" value="Unknown" />
            </Panel>
        )
    }

    if (value.length === 0) {
        return (
            <Panel>
                <DetailField label="SIIAM History Cases" value="None" />
            </Panel>
        )
    }

    const sorted = [...value].sort((a, b) => {
        const date1 = new Date(a.creationTime).getTime()
        const date2 = new Date(b.creationTime).getTime()
        return date2 - date1
    })

    return (
        <CollapsablePanel
            headerElement={
                <DetailField label="SIIAM History Cases" value={''} />
            }
            itemCount={value.length}
            canOpen={value.length > 0}
        >
            <div className={styles.historyField}>
                <div className={styles.incidentRow}>
                    {sorted.map((incident) => (
                        <div key={incident.id}>
                            <DetailField
                                label="Incident"
                                value={incident.id}
                                inline={true}
                            />
                            <DetailField
                                label="Created On"
                                value={formatDate(incident.creationTime)}
                                inline={true}
                            />

                            <DetailField
                                label="Status"
                                value={incident.status}
                                inline={true}
                            />
                        </div>
                    ))}
                </div>
            </div>
        </CollapsablePanel>
    )
}
