
import czConventionalChangelog from 'cz-conventional-changelog';


// Same implementation of filterSubject in cz-conventional-commits
const filterSubject = function(subject, disableSubjectLowerCase) {
    subject = subject.trim();
    if (!disableSubjectLowerCase && subject.charAt(0).toLowerCase() !== subject.charAt(0)) {
        subject = subject.charAt(0).toLowerCase() + subject.slice(1, subject.length);
    }
    while (subject.endsWith('.')) {
        subject = subject.slice(0, subject.length - 1);
    }
    return subject;
};


// Basic class to pass in an object with the prompt method that sets some fields
// for the commit (type, scope and subject)
class CommitizenMockPrompter {
    constructor(type, scope, subject) {
        this.type = type;
        this.scope = scope;
        this.subject = filterSubject(subject, true);
    }

    prompt() {
        return new Promise((resolve, reject) => {
            resolve({
                type: this.type,
                scope: this.scope ? this.scope : null,
                subject: this.subject,
                body: null,
                breaking: null,
                issues: null
            });
        });
    }
}


/**
 *
 * @param {String} type type of commit
 * @param {String} scope scope of commit (optional)
 * @param {String} subject submect message of commit
 * Note: this function uses
 */
export function conventionalCommit(type, scope, subject) {
    let mockPrompter = new CommitizenMockPrompter(type, scope, subject);

    let commitMessagePromise = new Promise((resolve, reject) => {
        czConventionalChangelog.prompter(mockPrompter, (message) => {
            resolve(message);
        });
    })

    return commitMessagePromise;
}
