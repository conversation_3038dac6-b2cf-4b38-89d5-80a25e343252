
import passport from 'passport';

import ldap from 'ldapjs';
import stringSanitizer from 'string-sanitizer';
import unixcrypt from 'unixcrypt';
import jsonwebtoken from 'jsonwebtoken';

import { BasicStrategy } from 'passport-http';
import passportLocal from 'passport-local';
import passportJwt from 'passport-jwt';
import fs from 'fs';
import readline from 'readline';
import debug from 'debug';
import _ from 'lodash';

import '../config/config.js';
import { AuthorizationRoles } from './enumerations.js';
import logger from './logger.js';
import customEncrypt from './customEncrypt.js';
import authEmitter from './authEmitter.js';
import { AuthenticationError } from './error.js';

const debugMsg = debug('merge:auth');

const localStrategy = passportLocal.Strategy;
const JwtStrategy = passportJwt.Strategy
const ExtractJwt = passportJwt.ExtractJwt;

// Can be moved to config file in the future
const LDAP_SERVERS = Object.freeze([
    'ldaps://ldap-account-01.puma.corp.telstra.com',
    'ldaps://ldap-account-01-puex.puma.corp.telstra.com'
]);


// Loads static users from configuration files
const usersFromConfig = {};
for (let i in global.gConfig.userConfig) {
    let userConfigFile = global.gConfig.userConfig[i];
    try {
        let userContents = JSON.parse(await fs.promises.readFile(userConfigFile));
        Object.assign(usersFromConfig, userContents);
    } catch(error) {
        logger.error(`Error in reading user config file ${userConfigFile}: ${error}`);
    }
}


class User {
    constructor(obj) {
        if (!_.isPlainObject(obj)) {
            throw new TypeError("User only accepts plain object in constructor");
        }

        this.username = null;
        this.name = null;
        this.department = null;
        this.businessUnit = null;
        this.isRobot = false;
        this.mergeGroups = [];
        this.groups = [];
        this.mail = null;

        // User roles
        this.readOnly = false;
        this.level0 = false;
        this.level1 = false;
        this.level2 = false;
        this.level3 = false;
        this.apiAccess = false;
        this.isAdmin = false;
        this.isDeveloper = false;
        this.levelLead = false;
        this.commandAccess = false;
        this.offshoreAccess = false;

        // Additional user roles (not in a group in LDAP)
        this.sourceAPIAccess = false;

        // Additional flags
        this.isAAAUser = false;
        this.outsideAustralia = false;
        this.SSU = null;
        this.isWholesaleUser = false;

        // Only assigns properties from constructor parameter
        // declared above this line
        _.assign(this, _.pick(obj, Object.keys(this)));

        this.encryptedUsername = customEncrypt.encryptedUsername(this.username);
        this.login = new Date();
    }
}


passport.use(new localStrategy({ usernameField: 'username', passReqToCallback: true }, async(req, username, password, done) => {
    try {
        username = stringSanitizer.sanitize(username);

        let authenticatedUser = null;
        let userIP = req.headers['x-real-ip'] || req.socket.remoteAddress;

        if (username) {
            if (username in usersFromConfig) {
                if (unixcrypt.verify(password, usersFromConfig[username].passHash)) {
                    logger.info(`User authentication (local) successful for ${username} with IP ${userIP}`);
                    authenticatedUser = new User(usersFromConfig[username]);
                } else {
                    logger.info(`User authentication (local) failed for ${username} with IP ${userIP}`);
                    throw new AuthenticationError(`Username or password is incorrect`);
                }
            } else {
                // Verify username / password with LDAP server
                let ldapClient = await ldapClientConnect();
                try {
                    let ldapUser = await ldapSearchUser(ldapClient, username);
                    if (ldapUser && ldapUser.dn) {
                        let userDN = ldapUser.dn;
                        let ldapAuthenticated = await ldapVerifyUser(ldapClient, userDN, password);

                        if (ldapAuthenticated) {
                            logger.info(`User authentication successful for ${username} with IP ${userIP}`);
                            let user = createUserFromLdap(ldapUser);

                            // if (!global.gConfig.permittedSSU.includes(user.SSU)) {
                            //     throw new AuthenticationError(`Username ${username} is not authorized to access Merge due to SSU permissions`);
                            // }

                            // Checks user has access to at least 1 entitlement
                            if (!(user.readOnly ||
                                user.level0 ||
                                user.level1 ||
                                user.level2 ||
                                user.level3 ||
                                user.commandAccess ||
                                user.isDeveloper ||
                                user.isAdmin ||
                                user.levelLead ||
                                user.apiAccess ||
                                user.offshoreAccess)) {
                                throw new AuthenticationError(`Username ${username} does not have the correct entitlement to access Merge`);
                            }

                            authenticatedUser = user;
                        } else {
                            logger.info(`User authentication failed for ${username} with IP ${userIP}`);
                            throw new AuthenticationError(`Username or password is incorrect`);
                        }
                    } else {
                        logger.info(`User not found for ${username} with IP ${userIP}`);
                        throw new AuthenticationError(`Username ${username} could not be found`);
                    }
                } finally {
                    // Ensures ldapClient will run unbind() after all other operations
                    ldapClient.unbind();
                }
            }
        }

        if (!(authenticatedUser instanceof User)) {
            throw new TypeError('User not set in authentication');
        }

        done(null, _.toPlainObject(authenticatedUser));
    } catch(error) {
        // Authentication errors which can occur if the user password is incorrect
        // or the user could not be found in LDAP, skip logging these
        if (!(error instanceof AuthenticationError)) {
            logger.error(`Authentication failed with error, ${error.toString()}`);
        }
        done(error, null);
    }
}));


async function ldapClientConnect() {
    return new Promise((resolve, reject) => {
        let ldapClient = ldap.createClient({
            url: LDAP_SERVERS,
            tlsOptions: { rejectUnauthorized: false },
            timeout: 5000,
            connectTimeout: 5000
        }).on('connect', () => {
            resolve(ldapClient);
        }).on('error', (error) => {
            reject(error);
        });
    });
}


async function ldapSearchUser(ldapClient, username) {
    return new Promise((resolve, reject) => {
        try {
            let searchOptions = {
                filter: new ldap.RDN({ cn: username }).toString(),
                scope: 'sub',
                timeLimit: 10,
                sizeLimit: 1,
                attributes: [
                    'cn',
                    'title',
                    'personalTitle',
                    'givenName',
                    'displayName',
                    'c',
                    'co',
                    'department',
                    'division',
                    'telephoneNumber',
                    'mail',
                    'telstraCostAccount',
                    'telstraJobKey',
                    'telstraSSUStatus',
                    'memberOf',
                    'extensionAttribute3'
                ]
            }

            let robotDN = ldap.parseDN(global.gConfig.robotDNBase);
            let robotAccountRDN = new ldap.RDN({ cn: global.gConfig.robotAccount01Username });
            robotDN.unshift(robotAccountRDN);

            ldapClient.bind(robotDN.toString(), global.gConfig.robotAccount01Password, function(error) {
                if (error) {
                    authEmitter.emit('AuthError');
                    reject(error);
                } else {
                    let userInfoLdap = null;
                    ldapClient.search(global.gConfig.userSearchBase, searchOptions, function(error, res) {
                        if (error) {
                            reject(error);
                        } else {
                            res.on('searchEntry', function(entry) {
                                userInfoLdap = entry;
                            });

                            res.on('end', function() {
                                resolve(userInfoLdap);
                            });

                            res.on('error', function(error) {
                                reject(error);
                            });
                        }
                    });
                }
            });
        } catch(error) {
            reject(error);
        }
    });
}


async function ldapVerifyUser(ldapClient, userDN, password) {
    return new Promise((resolve, reject) => {
        ldapClient.bind(userDN.toString(), password, function(error) {
            if (error) {
                resolve(false);
            } else {
                resolve(true);
            }
        });
    });
}


function createUserFromLdap(userLdap) {
    let mergeGroups = [];
    let groups = [];

    let isRobot = userLdap.dn.childOf(global.gConfig.robotDNBase);
    let ldapGroups = [];

    let ldapAttributes = userLdap.pojo.attributes;
    let userMemberOf = findAttributeValue(ldapAttributes, 'memberOf', false);
    let userCN = findAttributeValue(ldapAttributes, 'cn', true);

    // memberOf field could have been a string in previous iterations,
    // accounts for it here as well, in case
    if (Array.isArray(userMemberOf)) {
        ldapGroups = userMemberOf;
    } else if (typeof userMemberOf === 'string') {
        ldapGroups = [userMemberOf];
    }

    for (let group of ldapGroups) {
        try {
            let groupDN = ldap.parseDN(group);
            let agsOrgUnitDN = ldap.parseDN(global.gConfig.agsOrgUnitDNBase);

            let groupNameRDN = groupDN.rdnAt(0);
            let groupName = groupNameRDN && groupNameRDN.has('CN') ? groupNameRDN.getValue('CN') : null;

            if (groupName) {
                // Just ensures group unit exists within the AGS org unit
                // and starts with 'aMerge' to be included as a Merge group
                if (groupDN.childOf(agsOrgUnitDN) && global.gConfig.agsOrgGroupNames.includes(groupName)) {
                    mergeGroups.push(groupName);
                }
                groups.push(groupName);
            }
        } catch(error) {
            logger.warn(`Could not parse LDAP group entry ${group} for username ${userCN}, ${error.toString()}`);
        }
    }

    let attributeCountryShort = findAttributeValue(ldapAttributes, 'c', true);
    let attributeCountryFull = findAttributeValue(ldapAttributes, 'co', true);

    let outsideAustralia = (typeof attributeCountryShort === 'string' && attributeCountryShort.toLowerCase() !== 'au') ||
                           (typeof attributeCountryFull === 'string' && attributeCountryFull.toLowerCase() !== 'australia');

    let SSU = findAttributeValue(ldapAttributes, 'telstraSSUStatus', true);
    let isWholesaleUser = ['NONSP', 'WBU'].includes(SSU);

    let userDisplayName = findAttributeValue(ldapAttributes, 'displayName', true);
    let userDepartment = findAttributeValue(ldapAttributes, 'department', true);
    let userMail = findAttributeValue(ldapAttributes, 'mail', true);
    let userBusinessUnit = findAttributeValue(ldapAttributes, 'extensionAttribute3', true);

    return new User({
        username: userCN,
        name: userDisplayName,
        department: userDepartment,
        businessUnit: userBusinessUnit,
        isRobot: isRobot,
        mergeGroups: mergeGroups,
        groups: groups,
        mail: userMail,
        readOnly: mergeGroups.includes('aMergeRead'),
        apiAccess: mergeGroups.includes('aMergeAPI'),
        isAdmin: mergeGroups.includes('aMergeAdmin'),
        isDeveloper: mergeGroups.includes('aMergeDeveloper'),
        levelLead: mergeGroups.includes('aMergeLead'),
        commandAccess: mergeGroups.includes('aMergeCommandG1'),
        level0: mergeGroups.includes('aMergeFOH'),
        level1: mergeGroups.includes('aMergeL1'),
        level2: mergeGroups.includes('aMergeL2'),
        level3: mergeGroups.includes('aMergeL3'),
        offshoreAccess: mergeGroups.includes('aMergeOffshoreConcent'),
        isAAAUser: ['aMergeFOH', 'aMergeL1', 'aMergeL2', 'aMergeL3', 'aMergeAdmin', 'aMergeLead'].some(group => mergeGroups.includes(group)),
        outsideAustralia: outsideAustralia,
        SSU: SSU,
        isWholesaleUser: isWholesaleUser
    });
}


function findAttributeValue(ldapAttributes, key, useFirstValue) {
    let attributeEntry = ldapAttributes.find((attribute) => attribute.type === key);

    if (!attributeEntry) {
        return null;
    }

    if (useFirstValue) {
        return Array.isArray(attributeEntry.values) && attributeEntry.values.length ? attributeEntry.values[0] : null;
    } else {
        return attributeEntry.values ? attributeEntry.values : null;
    }
}


passport.serializeUser(function(user, done) {
    debugMsg('#> Serialize User: ', user);
    done(null, user);
});


passport.deserializeUser(function(user, done) {
    debugMsg('#> Deserialize User: ', user.username);
    done(null, user);
});


/**
 *
 * @param {object} roles Array of AuthorizationRole strings that are checked and if the user
 * is part of any of the roles listed, the authorization will be successful
 * @param {boolean} redirectLogin Flag to indicate whether to redirect the HTTP request to
 * the login URI, if not set, an HTTP 401 status code will be sent instead
 * @returns {function} middleware function
 */
function authorizeForRoles(roles, redirectLogin) {
    return function(req, res, next) {
        if (req.isAuthenticated() && req.user) {
            let authorized = false;
            for (let role of roles) {
                if (req.user[role] === true) {
                    authorized = true;
                }
            }

            if (authorized) {
                next();
            } else {
                res.sendStatus(403);
            }
        } else {
            if (redirectLogin) {
                req.session.redirectUrl = req.originalUrl;
                res.redirect('/auth/login');
            } else {
                res.sendStatus(401);
            }
        }
    }
}


function authorizeForRolesSocket(roles, user) {
    let authorized = false;
    for (let role of roles) {
        if (user[role] === true) {
            authorized = true;
        }
    }

    return authorized;
}


passport.use('jwt', new JwtStrategy({
    jwtFromRequest: ExtractJwt.fromExtractors([
        ExtractJwt.fromHeader('authenticate'),
        ExtractJwt.fromAuthHeaderAsBearerToken()
    ]),
    secretOrKey: global.gConfig.apiSecret,
    algorithms: [global.gConfig.apiEncrytionAlg]
}, function(payload, done) {
    try {
        if (payload && payload.username && typeof payload.username === 'string') {
            done(null, new User(payload));
        } else {
            done(null, null);
        }
    } catch(error) {
        done(error, null);
    }
}));

passport.use('basic-prometheus', new BasicStrategy(async (username, password, done) => {
    const prometheusUsername = global.gConfig?.prometheusAuth?.username;
    const prometheusPassword = global.gConfig?.prometheusAuth?.password;

    let userAuthenticated = null;

    if (username === prometheusUsername) {
        let passwordMatched = unixcrypt.verify(password, prometheusPassword);

        if (passwordMatched) {
            userAuthenticated = {
                user: username
            };
        }
    }

    done(null, userAuthenticated);
}));

function makeApiToken(user, expiry) {
    // Token consists of username, any roles from authorization
    let userAcl = {
        username: user.username,
        businessUnit: user.businessUnit,
        ..._.pick(user, Object.keys(AuthorizationRoles)),
        isWholesaleUser: user.isWholesaleUser
    };

    return jsonwebtoken.sign(
        userAcl,
        global.gConfig.apiSecret,
        { expiresIn: expiry, algorithm: global.gConfig.apiEncrytionAlg }
    );
}


function checkUserAuthorizedForServiceCheckLevel(user, level) {
    let isAuthorizedForLevel = false;

    switch (level) {
        case 0:
            isAuthorizedForLevel = true;
            break;
        case 1:
            isAuthorizedForLevel = user && (
                user.level1 ||
                user.level2 ||
                user.level3 ||
                user.levelLead ||
                user.isDeveloper ||
                user.isAdmin
            );
            break;
        case 2:
            isAuthorizedForLevel = user && (
                user.level2 ||
                user.level3 ||
                user.levelLead ||
                user.isDeveloper ||
                user.isAdmin
            );
            break;
        case 3:
            isAuthorizedForLevel = user && (
                user.level3 ||
                user.levelLead ||
                user.isDeveloper ||
                user.isAdmin
            );
            break;
        case 4:
            isAuthorizedForLevel = user && (user.levelLead || user.isDeveloper || user.isAdmin);
            break;
        case 5:
            isAuthorizedForLevel = user && (user.isDeveloper || user.isAdmin);
            break;
        case 6:
            isAuthorizedForLevel = user && user.isAdmin;
            break;
        default:
            throw new Error(`Unrecognised level input ${level}`);
    }

    return isAuthorizedForLevel;
}


/**
 *
 * @param {boolean} passthrough Flag to indicate whether to proceed to the next
 * middleware if the user is already authenticated
 * @returns {function} Returns a middleware function to use before any endpoints
 * to authenticate the user via a JWT in the HTTP header
 */
function authenticateWithJWT(passthrough=false) {
    return function(req, res, next) {
        if (passthrough === true && req.isAuthenticated() && req.user) {
            // If the user is currently authenticated in sessions and passthrough is set,
            // then proceed
            next();
        } else {
            req.headers.authenticate = req.headers.authenticate ? req.headers.authenticate.replace(/^Bearer\s/i,'') : null;
            passport.authenticate('jwt', { session: false }, function(error, user) {
                if (error) {
                    res.sendStatus(401);
                } else {
                    if (user) {
                        req.user = _.toPlainObject(user);
                        next();
                    } else {
                        res.sendStatus(401);
                    }
                }
            })(req, res, next);
        }
    }
}


async function ensureAuthenticatedLdap(username, password) {
    return new Promise((resolve, reject) => {
        let robotDN = ldap.parseDN(global.gConfig.robotDNBase);
        let robotAccountRDN = new ldap.RDN({ cn: username });
        robotDN.unshift(robotAccountRDN);

        let ldapClient = ldap.createClient({
            url: LDAP_SERVERS,
            tlsOptions: { 'rejectUnauthorized': false },
            timeout: 5000,
            connectTimeout: 5000
        }).on('connect', function() {

            ldapClient.bind(robotDN.toString(), password, function(error) {
                let loginSuccessful = error ? false : true;

                // Unbind Client
                ldapClient.unbind();
                resolve(loginSuccessful);
            });
        }).on('connectTimeout', function(error) {
            reject(error);
        }).on('error', function(error) {
            reject(error);
        });
    });
}


async function readCredentialsFromFile(path) {
    return new Promise((resolve, reject) => {
        let credentialList = [];
        let credentialFileStream = fs.createReadStream(path).on('open', function() {
            let rl = readline.createInterface({
                input: credentialFileStream
            });

            rl.on('line', async (line) => {
                let credentials = line.split(":");
                if (credentials.length == 2) {
                    credentialList.push({
                        username: credentials[0],
                        password: credentials[1],
                        fromCredentialsFile: true
                    });
                }
            });

            rl.on('close', function() {
                resolve(credentialList);
            });
        }).on('error', (error) => {
            reject(error);
        });
    });
}


// Helper function to read file containing ACCOUNT-01 credentials
// and set config values
async function updateRobotAccountCredentials(path) {
    let credentialList = [{
        username: global.gConfig.robotAccount01Username,
        password: global.gConfig.robotAccount01Password,
        fromCredentialsFile: false
    }];

    if (path) {
        try {
            // Adds credentials from credentials file to attempt to authenticate with LDAP first
            credentialList.unshift(...await readCredentialsFromFile(path));
        } catch (error) {
            logger.error(`Error loading account-01 credentials file ${path}: ${error.message}`);
        }
    }

    let foundAuthenticatedAccount = false;

    // Goes through ACCOUNT-01 credentials and attempts to authenticate it with LDAP
    for (var i in credentialList) {
        let username = credentialList[i].username;
        let password = credentialList[i].password;
        let fromCredentialsFile = credentialList[i].fromCredentialsFile;

        try {
            let isAuthenticatedLdap = await ensureAuthenticatedLdap(username, password);
            if (isAuthenticatedLdap) {
                foundAuthenticatedAccount = true;

                if (fromCredentialsFile) {
                    logger.info(`Set account-01 credentials for robot user ${username} from credentials file ${path}`);
                } else {
                    logger.info(`Set account-01 credentials for robot user ${username} from config file`);
                }

                global.gConfig.robotAccount01Username = username;
                global.gConfig.robotAccount01Password = password;
                break;
            } else {
                logger.warn(`Could not authenticate user ${username} via LDAP`);
            }
        } catch (error) {
            logger.error(`Error when checking ${username} is authenticated via LDAP: ${error.message}`);
        }
    }

    if (!foundAuthenticatedAccount) {
        if (path) {
            logger.error(`All users from credentials file ${path} failed to authenticate via LDAP, using empty credentials`);
        } else {
            logger.error('Credentials from config file failed to authenticate via LDAP, using empty credentials');
        }

        global.gConfig.robotAccount01Username = "";
        global.gConfig.robotAccount01Password = "";
    }
}


function setUser(req, res, next) {
    res.locals.user = req.user;
    next();
}


// Handles auth emitter "AuthError" event by reading the robot account credentials again on failure
authEmitter.on('AuthError', async function() {
    if (global.gConfig.robotAccount01CredentialFile) {
        logger.info('AuthError event emitted: updating robot account credentials');
        await updateRobotAccountCredentials(global.gConfig.robotAccount01CredentialFile);
    } else {
        logger.warn('AuthError event emitted: no robot account credentials file configured, skip updating credentials');
    }
});


export default {
    initialize: passport.initialize(),
    session: passport.session(),
    authenticateWithJWT: authenticateWithJWT,
    authorizeForRoles: authorizeForRoles,
    authorizeForRolesSocket: authorizeForRolesSocket,
    updateRobotAccountCredentials: updateRobotAccountCredentials,
    makeApiToken: makeApiToken,
    checkUserAuthorizedForServiceCheckLevel: checkUserAuthorizedForServiceCheckLevel,
    setUser: setUser
};
