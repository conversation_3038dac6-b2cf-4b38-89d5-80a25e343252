
/**
 * @param {string}  last        Time interval expressed in ${length}${unit} format
 * @return {Date}   The start date that is the specified number of time before the current date
 */
export function calculateStartDate(last) {
    let startDate;

    startDate = new Date();
    // Extract time unit and length from "last" parameter
    let lastTags = last.match(/^(\d+)([d|w|m|y])$/);

    let timeLength = lastTags[1];
    let timeUnit = lastTags[2];

    // Subtract the appropriate time unit and length to get the start time to filter from
    switch (timeUnit) {
        case "d":
            startDate.setDate(startDate.getDate() - timeLength);
            break;
        case "w":
            startDate.setDate(startDate.getDate() - 7*timeLength);
            break;
        case "m":
            startDate.setMonth(startDate.getMonth() - timeLength);
            break;
        case "y":
            startDate.setFullYear(startDate.getFullYear() - timeLength);
            break;
    }

    return startDate;
}

// Function to calculate the expiry date (72h plus next 3AM)
export function calculateSessionExpiry() {
    let currDate = new Date();
    let expiryDate = new Date(currDate);

    expiryDate.setDate(expiryDate.getDate() + 3);

    if (currDate.getHours() > 2) {
        expiryDate.setDate(expiryDate.getDate() + 1);
    }

    expiryDate.setHours(3, 0, 0, 0);

    return expiryDate;
}
