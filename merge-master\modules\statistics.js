
import dedent from 'dedent';

import hangarDatabase from './hangarDatabase.js';


function getAAAUsage(startDate, interval, limit, offset, userFilter, group, callback) {
    let query;
    let parameters;

    let timeGroup = '?';
    switch (interval) {
        case "d":
            timeGroup = dedent`DATE_FORMAT(CONVERT_TZ(DATE(timestamp), \
                               "Australia/Victoria", "+00:00"), "%Y-%m-%dT%TZ")`;
            break;
        case "w":
            timeGroup = dedent`DATE_FORMAT(CONVERT_TZ(DATE_SUB(DATE(timestamp), \
                               INTERVAL WEEKDAY(timestamp) DAY), \
                               "Australia/Victoria", "+00:00"), "%Y-%m-%dT%TZ")`;
            break;
        case "m":
            timeGroup = dedent`DATE_FORMAT(CONVERT_TZ(DATE_FORMAT(DATE(timestamp), "%Y-%m-01"), \
                               "Australia/Victoria", "+00:00"), "%Y-%m-%dT%TZ")`;
            break;
        case "y":
            timeGroup = dedent`DATE_FORMAT(CONVERT_TZ(DATE_FORMAT(DATE(timestamp), "%Y-01-01"), \
                               "Australia/Victoria", "+00:00"), "%Y-%m-%dT%TZ")`;
            break;
    }

    let columns = `${timeGroup} as start, COUNT(*) as count`;
    let conditions = 'timestamp > ?';
    let groups = [];
    let orders = ['count DESC'];

    if (userFilter.length > 0) {
        let userListTokens = new Array(userFilter.length).fill('?').join(',');
        conditions = `timestamp > ? AND userid in (${userListTokens})`;
    }

    if (group == "user") {
        columns = `${timeGroup} as start, COUNT(*) as count, userid as user`;
        groups.push('userid');
        orders.push('user ASC');
    }

    if (interval === undefined) {
        parameters = [startDate.toISOString(), startDate, ...userFilter, limit, offset];
    } else {
        groups.push(timeGroup);
        orders.unshift('start DESC');
        parameters = [startDate, ...userFilter, limit, offset];
    }

    // Creates group by and order by section of query from lists
    let groupBy = '';
    let orderBy = '';
    if (groups.length > 0) {
        groupBy = `GROUP BY ${groups.join(",")}`;
    }

    if (orders.length > 0) {
        orderBy = `ORDER BY ${orders.join(",")}`;
    }

    query = dedent`SELECT SQL_CALC_FOUND_ROWS ${columns} \
                   FROM ${global.gConfig.hangarDb.databaseTableUsage} \
                   WHERE ${conditions} \
                   ${groupBy} \
                   ${orderBy} LIMIT ? OFFSET ?`;

    hangarDatabase.execute(query, parameters,
        function(err, results, _) {
            if (err) {
                callback(err, null);
                return;
            }

            hangarDatabase.execute('SELECT FOUND_ROWS() as total',
                function(err, total, _) {
                    if (err) {
                        callback(err, null);
                    }

                    callback(null, results, total[0].total);
                }
            );
        }
    );
}


export default {
    getAAAUsage: getAAAUsage
};
