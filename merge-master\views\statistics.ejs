<!-- v1 -->
<%- include('header', {}); %>
<%- include('menu' , {currentTab: 'Home' }); %>
<link rel="stylesheet" type="text/css" href="/public/stylesheets/jsgrid.min.css">
<link rel="stylesheet" type="text/css" href="/public/stylesheets/jsgrid-theme.min.css">
<link rel="stylesheet" type="text/css" href="/public/stylesheets/select2.min.css">
<script type="text/javascript" src="/public/javascripts/jsgrid.min.js"></script>
<script src="/public/javascripts/select2.min.js"></script>
<style>
    .select2-drop {
        display: none !important;
    }
</style>

<div class="container">
    <br>
    <div class="jumbotron py-1 px-4 text-white rounded bg-secondary">
        <h3 class="display-6 font-weight-normal">
            <%= title %>
        </h3>
    </div>

    <div class="row">
        <div class="col-sm-12">
            <p>
                Note: Each separate filter field has a logical AND applied over it, whilst multiple filters in the same field have a logical OR applied.
            </p>
        </div>
    </div>
    <div class="row">
        <div class="col-1">
            <label class="col-form-label" for="statsCarriageTypeFilter">Carriage type</label>
        </div>
        <div class="col-11">
            <select id="statsCarriageTypeFilter" style="width: 100%" multiple="multiple" placeholder="none"></select>
        </div>
    </div>
    <hr/>
    <div class="row">
        <div class="col-1">
            <label class="col-form-label" for="statsRuleFilter">Rule names</label>
        </div>
        <div class="col-7">
            <select id="statsRuleFilter" style="width: 100%" multiple="multiple"></select>
        </div>
        <div class="col-1">
            <label class="col-form-label" for="statsRuleStatusFilter">Status</label>
        </div>
        <div class="col-3">
            <select id="statsRuleStatusFilter" style="width: 100%" multiple="multiple" disabled>
                <option value="codeError">Code Error</option>
                <option value="done">Done</option>
                <option value="preCondError">Pre-Condition Error</option>
                <option value="preConError">Pre-Condition OK</option>
                <option value="preConReject">Pre-Condition Reject</option>
                <option value="running">Running</option>
            </select>
        </div>
    </div>
    <br>
    <div class="row">
        <div class="col-1">
            <label class="col-form-label" for="statsRuleTriggerSourceFilter">Trigger source</label>
        </div>
        <div class="col-7">
            <select id="statsRuleTriggerSourceFilter" style="width: 100%" multiple="multiple" disabled></select>
        </div>
        <div class="col-1">
            <label class="col-form-label" for="statsRuleResultFilter">Result</label>
        </div>
        <div class="col-3">
            <select id="statsRuleResultFilter" style="width: 100%" multiple="multiple" disabled>
                <option value="Actionable">Actionable</option>
                <option value="Actioned">Actioned</option>
                <option value="Error">Error</option>
                <option value="Failed">Failed</option>
                <option value="OK">OK</option>
                <option value="Reject">Reject</option>
                <option value="Warning">Warning</option>
            </select>
        </div>
    </div>
    <hr/>
    <div class="row">
        <div class="col-1">
            <label class="col-form-label" for="statsSourceFilter">Source names</label>
        </div>
        <div class="col-7">
            <select id="statsSourceFilter" style="width: 100%" multiple="multiple"></select>
        </div>
        <div class="col-1">
            <label class="col-form-label" for="statsSourceStatusFilter">Status</label>
        </div>
        <div class="col-3">
            <select id="statsSourceStatusFilter" style="width: 100%" multiple="multiple" disabled>
                <option value="API URI error">API URI Error</option>
                <option value="Collected">Collected</option>
                <option value="error">Error</option>
                <option value="Parameter Error">Parameter Error</option>
                <option value="Pre-Condition Error">Pre-Condition Error</option>
                <option value="Pre-Condition False">Pre-Condition False</option>
                <option value="running">Running</option>
            </select>
        </div>
    </div>
    <hr/>
    <div class="row">
        <div class="col-sm-8">
            <label class="col-form-label" for="statsType">Statistics type</label>
            <select id="statsType" style="width: 10em">
                <option value="scsUsers" selected>User</option>
                <option value="scsCarriageTypes">Carriage Type</option>
                <option value="scsFnn">FNN</option>
                <option value="scsRules">Rule</option>
                <option value="scsSources">Source</option>
            </select>
            <label class="col-form-label" for="lastQuantity">Last</label>
            <input id="lastQuantity" type="number" style="width: 4em" value=1 min=1>
            <select id="lastUnit" style="width: 6em">
                <option value="d">days</option>
                <option value="w">weeks</option>
                <option value="m" selected>months</option>
                <option value="y">years</option>
            </select>
            <button id="lastTimeApply" class="btn btn-primary">Apply</button>
        </div>
        <div class="col-sm-4">
            <div class="float-right">
                <p id="displayUpdatedAt"></p>
            </div>
        </div>
    </div>
    <br>
    <div id="statsGrid" class="flex-grow-1"></div>
</div>

<script>
    $(document).ready(function () {
        $('#lastQuantity').keypress("keypress", function (e) {
            if (e.which < "0".charCodeAt(0) || e.which > "9".charCodeAt(0)) {
                e.preventDefault();
            }
        });

        $('#lastQuantity').on("paste", function (e) {
            e.preventDefault();
        });

        $("#statsCarriageTypeFilter").select2({
            tags: true
        });

        $("#statsRuleFilter").select2();
        $("#statsRuleStatusFilter").select2();
        $("#statsRuleResultFilter").select2();
        $("#statsRuleTriggerSourceFilter").select2();

        $("#statsSourceFilter").select2();
        $("#statsSourceStatusFilter").select2();
        $("#lastUnit").select2({
            minimumResultsForSearch: -1,
            width: "resolve"
        });

        $("#statsType").select2({
            minimumResultsForSearch: -1,
            width: "resolve"
        });

        httpApiGetAllResults("/rules", null, [], updateRuleList);
        httpApiGetAllResults("/sources", null, [], updateSourceList);

        function updateRuleList(err, rules) {
            if (err) {
                console.error(err);
                return;
            }

            let ruleItemList = $.map(rules, function(item) {
                return {
                    id: item.name,
                    text: item.name
                }
            });

            $("#statsRuleFilter").select2({
                data: ruleItemList
            });
        }

        function updateSourceList(err, sources) {
            if (err) {
                console.error(err);
                return;
            }

            let sourceItemList = $.map(sources, function(item) {
                return {
                    id: item.name,
                    text: item.name
                }
            });

            $("#statsRuleTriggerSourceFilter").select2({
                data: sourceItemList
            });

            $("#statsSourceFilter").select2({
                data: sourceItemList
            });
        }

        $("#statsRuleFilter").on('select2:select', function (e) {
            updateRuleFilters();
        });

        $("#statsRuleFilter").on('select2:unselect', function (e) {
            updateRuleFilters();
        });

        $("#statsSourceFilter").on('select2:select', function (e) {
            updateSourceFilters();
        });

        $("#statsSourceFilter").on('select2:unselect', function (e) {
            updateSourceFilters();
        });

        $("#lastTimeApply").click(function () {
            let statsType = $("#statsType").val();
            let gridFields = [
                { name: "count", title: "Number of Service Checks", type: "text" },
                {
                    name: "start", title: "From", type: "text", sorting: false, itemTemplate: function (value) {
                        return new Date(value).toLocaleString('en-GB');
                    }
                }
            ];
            let groupField;

            switch (statsType) {
                case "scsUsers":
                    groupField = { name: "user", title: "Username", type: "text", sorting: false };
                    break;
                case "scsCarriageTypes":
                    groupField = { name: "carriageType", title: "Carriage Type", type: "text", sorting: false, itemTemplate: function(value) {
                        if (value === "") {
                            return "\"\"";
                        } else {
                            return value;
                        }
                    }};
                    break;
                case "scsFnn":
                    groupField = { name: "fnn", title: "FNN", type: "text", sorting: false };
                    break;
                case "scsRules":
                    groupField = { name: "rule", title: "Rule", type: "text", sorting: false };
                    break;
                case "scsSources":
                    groupField = { name: "source", title: "Source", type: "text", sorting: false };
                    break;
            }

            gridFields = [groupField, ...gridFields];
            $("#statsGrid").jsGrid("option", "fields", gridFields);
        });

        $("#statsGrid").jsGrid({
            width: "100%",
            height: "100%",
            filtering: false,
            inserting: false,
            editing: false,
            sorting: true,
            autoload: true,
            paging: true,
            pageLoading: true,
            pageSize: 1000,
            pageIndex: 1,
            editButton: false,
            deleteButton: false,
            controller: {
                loadData: function (filter) {
                    var data = $.Deferred();

                    let limit = filter.pageSize;
                    let offset = (filter.pageIndex - 1) * filter.pageSize;
                    let lastQuantity = $("#lastQuantity").val();
                    let lastUnit = $("#lastUnit").val();
                    let statsType = $("#statsType").val();

                    let carriageTypeFilter = $("#statsCarriageTypeFilter").val();
                    let ruleFilter = $("#statsRuleFilter").val();
                    let ruleStatusFilter = $("#statsRuleStatusFilter").val();
                    let ruleResultFilter = $("#statsRuleResultFilter").val();
                    let ruleTriggerSourceFilter = $("#statsRuleTriggerSourceFilter").val();
                    let sourceFilter = $("#statsSourceFilter").val();
                    let sourceStatusFilter = $("#statsSourceStatusFilter").val();

                    let parameters = {
                        limit: limit,
                        offset: offset,
                        carriageType: carriageTypeFilter,
                        rule: ruleFilter,
                        ruleStatus: ruleStatusFilter,
                        ruleResult: ruleResultFilter,
                        ruleTriggerSource: ruleTriggerSourceFilter,
                        source: sourceFilter,
                        sourceStatus: sourceStatusFilter
                    };

                    if (filter.sortField) {
                        parameters.order = filter.sortOrder;
                    }

                    if (lastQuantity && lastUnit) {
                        parameters.last = `${lastQuantity}${lastUnit}`;
                    }

                    setButtonSpinner($("#lastTimeApply"), "Apply", true);
                    $.ajax({
                        cache: false,
                        type: "GET",
                        url: `/stats/${statsType}`,
                        data: parameters,
                        traditional: true,
                        success: function (response) {
                            data.resolve({
                                data: response.results,
                                itemsCount: response.metadata.pagination.total
                            });

                            let currDate = new Date().toLocaleString('en-GB');
                            $("#displayUpdatedAt").text(`Updated at: ${currDate}`);
                        },
                        error: function (xhr, status, e) {
                            // Not the best solution, puts nothing in the table
                            data.resolve({
                                data: [],
                                itemsCount: 0
                            });

                            $("#displayUpdatedAt").text("Error obtaining statistics");
                        },
                        complete: function(err) {
                            setButtonSpinner($("#lastTimeApply"), "Apply", false);
                        }
                    });

                    return data.promise();
                }
            },
            fields: [
                { name: "user", title: "Username", type: "text", sorting: false },
                { name: "count", title: "Number of Service Checks", type: "text" },
                {
                    name: "start", title: "From", type: "text", sorting: false, itemTemplate: function (value) {
                        return new Date(value).toLocaleString('en-GB');
                    }
                }
            ]
        });

        $("#statsGrid").jsGrid("sort", { field: "count", order: "desc" });
    });


    function updateRuleFilters() {
        let ruleFilterIsEmpty = $("#statsRuleFilter").val().length == 0;
        $("#statsRuleStatusFilter").prop('disabled', ruleFilterIsEmpty);
        $("#statsRuleResultFilter").prop('disabled', ruleFilterIsEmpty);
        $("#statsRuleTriggerSourceFilter").prop('disabled', ruleFilterIsEmpty);
        if (ruleFilterIsEmpty) {
            $("#statsRuleStatusFilter").val(null).trigger("change");
            $("#statsRuleResultFilter").val(null).trigger("change");
            $("#statsRuleTriggerSourceFilter").val(null).trigger("change");
        }
    }


    function updateSourceFilters() {
        let sourceFilterIsEmpty = $("#statsSourceFilter").val().length == 0;
        $("#statsSourceStatusFilter").prop('disabled', sourceFilterIsEmpty);
        if (sourceFilterIsEmpty) {
            $("#statsSourceStatusFilter").val(null).trigger("change");
        }
    }


    function httpApiGetAllResults(url, parameters, resultList, callback) {
        // Goes through the response from a REST API and continues to the next pagination link if available
        $.ajax({
            url: url,
            type: "GET",
            data: parameters,
            cache: false,
            success: function(response) {
                try {
                    resultList.push(...response.results);

                    if (response.metadata.pagination.next === null) {
                        callback(null, resultList);
                    } else {
                        httpApiGetAllResults(response.metadata.pagination.next, null, resultList, callback);
                    }
                } catch(e) {
                    callback(e, null);
                }
            },
            error: function (xhr, status, e) {
                callback(e, null);
            }
        });
    }
</script>

<%- include('footer', {}); %>