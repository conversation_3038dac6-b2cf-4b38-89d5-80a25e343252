export const formatDate = (
    dateString: string,
    invalidDateFallback: string = 'Invalid Date'
): string => {
    const date = new Date(dateString)

    // Check if the date is valid
    if (isNaN(date.getTime())) {
        return invalidDateFallback
    }

    // Retrieve the user's preferred locale from the browser
    const userLocale = navigator.languages?.[0] || navigator.language

    // Define formatting options
    const options: Intl.DateTimeFormatOptions = {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true,
    }

    // Format the date using the user's locale
    return new Intl.DateTimeFormat(userLocale, options).format(date)
}
