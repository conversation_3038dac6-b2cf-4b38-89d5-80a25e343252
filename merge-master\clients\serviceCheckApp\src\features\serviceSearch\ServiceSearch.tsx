import {
    ActionButton,
    ActionButtonValidation,
    Spacing,
    TextField,
} from '@able/react'
import { ValidationState } from '@able/react/dist/ActionButtonValidation'
import { useNavigate } from 'react-router'
import { useAppState } from '../../hooks/useAppState'
import { useServiceSearch } from '../../hooks/useServiceSearch'
import { InitialAppState } from '../../infrastructure/models'
import { MergeClassicButton } from '../mergeClassicButton/MergeClassicButton'
import styles from './ServiceSearch.module.scss'

export const ServiceSearch = () => {
    const { appState, setAppState } = useAppState()
    const { isPending, isError, error, onSearch } = useServiceSearch()
    const navigate = useNavigate()
    const isLoadingService = appState.isLoadingService

    const getDisplayState = (): ValidationState => {
        if (isError) return 'Error'
        if (isPending || isLoadingService) return 'Loading'
        return 'Normal'
    }

    const getValidationMessage = (): string => {
        if (isPending || isLoadingService) {
            return 'Retrieving details...'
        }
        if (isError) {
            return error?.message ?? 'An error occurred.'
        }
        return ''
    }

    return (
        <div className={styles.serviceSearch}>
            <TextField
                id="searchInput"
                size="Auto"
                name="searchInput"
                value={appState.serviceNumber}
                events={{
                    onChange: (e: React.FormEvent<HTMLInputElement>) => {
                        if (!appState.id) {
                            setAppState({
                                ...appState,
                                serviceNumber: e.currentTarget.value.trim(),
                                serviceNumberErrorMessage: '',
                            })
                        }
                    },
                    onKeyDown: (e: React.KeyboardEvent<HTMLInputElement>) => {
                        if (e.key === 'Enter') {
                            onSearch()
                        }
                    },
                }}
                label="Product / Service ID"
                helpText="Enter FNN, Device Name, or Phone Number"
                invalid={!!appState.serviceNumberErrorMessage}
                invalidInputText={appState.serviceNumberErrorMessage}
                developmentUrl="/public/images/able-sprites.svg"
            />

            <Spacing top="spacing2x" />

            {appState.id && !isLoadingService ? (
                <>
                    <ActionButton
                        variant="LowEmphasis"
                        label="Not the details you are looking for? Start again."
                        element="button"
                        events={{
                            onClick: () => {
                                // To update: New UI link
                                navigate('/serviceCheck/new')
                                setAppState(InitialAppState)
                            },
                        }}
                        developmentUrl="/public/images/able-sprites.svg"
                    />
                    <MergeClassicButton viewType="view" />
                </>
            ) : (
                <>
                    <ActionButtonValidation
                        actionButtonEvents={{
                            onClick: onSearch,
                        }}
                        label="Find Product"
                        state={getDisplayState()}
                        validationMessage={getValidationMessage()}
                        developmentUrl="/public/images/able-sprites.svg"
                    />
                    <MergeClassicButton viewType="search" />
                </>
            )}
        </div>
    )
}
