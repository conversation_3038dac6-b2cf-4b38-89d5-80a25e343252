import { ActionButtonValidation } from '@able/react'
import { useCallback } from 'react'
import { useTestPackageRunner } from '../../../../api/sockets/useTestPackageRunner'
import { CheckboxGroup } from '../../../../components/checkboxGroup/CheckboxGroup'
import { DateRange } from '../../../../components/dateRange/DateRange'
import { DetailsPanel } from '../../../../components/detailsPanel/DetailsPanel'
import { Panel } from '../../../../components/panel/Panel'
import { useUser } from '../../../../context/UserContext'
import { useAppState } from '../../../../hooks/useAppState'
import { ConnectionStatus } from '../../../connectionStatus/ConnectionStatus'
import styles from './TestRunForm.module.scss'
import { allDatesValid, validateDateRange } from './validation'

export const TestRunForm = () => {
    const { appState, setAppState } = useAppState()
    const { canRunTests, isLoading, hasAttemptedRun, runTestPackages } =
        useTestPackageRunner()
    const selectedTestPackages = appState.selectedTestPackages
    const { canModify } = useUser()
    const serviceTestState = appState.serviceDetails.status

    const allSelected = selectedTestPackages.every((a) => a.isSelected)
    // Handle individual checkbox change
    const toggleSelectedPackage = useCallback(
        (packageName: string, isChecked: boolean) => {
            const updatedTestPackages = appState.selectedTestPackages.map(
                (pkg) =>
                    pkg.name === packageName
                        ? { ...pkg, isSelected: isChecked }
                        : pkg
            )
            setAppState({
                ...appState,
                selectedTestPackages: updatedTestPackages,
            })
        },
        [appState, setAppState]
    )

    // Handle "Select All" functionality
    const updateAllSelected = useCallback(() => {
        setAppState({
            ...appState,
            selectedTestPackages: allSelected
                ? selectedTestPackages.map((tp) => ({
                      ...tp,
                      isSelected: false,
                  }))
                : selectedTestPackages.map((tp) => ({
                      ...tp,
                      isSelected: true,
                  })),
        })
    }, [setAppState, appState, allSelected, selectedTestPackages])

    const getState = () => {
        if (serviceTestState === 'running') {
            return 'Error'
        }

        if (!allDatesValid(appState.selectedTestPackages)) {
            return 'Error'
        }

        if (!canModify(appState.createdBy)) {
            return 'Attention'
        }

        if (!canRunTests && hasAttemptedRun) {
            return 'Error'
        }
        if (isLoading) {
            return 'Loading'
        }

        return 'Normal'
    }

    const getValidationMessage = () => {
        if (serviceTestState === 'running') {
            return 'ServiceCheck is currently running. Please wait for it to complete.'
        }

        if (!allDatesValid(appState.selectedTestPackages)) {
            return 'A selected test package has invalid date values.'
        }

        if (!canModify(appState.createdBy)) {
            return 'Tests can only be run by create user of the service check.'
        }

        if (isLoading) {
            return 'Tests currently running. Please wait for them to complete.'
        }
        if (hasAttemptedRun && !canRunTests) {
            return 'Please select test packages to run.'
        }

        return ''
    }

    const onStartDateChange = (packageName: string, startDate: string) => {
        if (isNaN(Date.parse(startDate))) {
            return
        }

        const updatedTestPackages = appState.selectedTestPackages.map(
            (selected) => {
                if (selected.name === packageName) {
                    return {
                        ...selected,
                        parameters: {
                            ...selected.parameters,
                            startDate,
                        },
                    }
                }
                return selected
            }
        )

        setAppState({
            ...appState,
            selectedTestPackages: updatedTestPackages,
        })
    }

    const onEndDateChange = (packageName: string, endDate: string) => {
        if (isNaN(Date.parse(endDate))) {
            return
        }

        const updatedTestPackages = appState.selectedTestPackages.map(
            (selected) => {
                if (selected.name === packageName) {
                    return {
                        ...selected,
                        parameters: {
                            ...selected.parameters,
                            endDate,
                        },
                    }
                }
                return selected
            }
        )

        setAppState({
            ...appState,
            selectedTestPackages: updatedTestPackages,
        })
    }

    return (
        <DetailsPanel label="Choose Tests" panelElements={<ConnectionStatus />}>
            <div className={styles.runTestsForm}>
                <CheckboxGroup
                    selectAllLabel="Select All"
                    isSelectAll
                    selectAllChecked={allSelected}
                    items={selectedTestPackages.map((pkg) => ({
                        name: pkg.name,
                        label: pkg.title,
                        checked: pkg.isSelected,
                        customContent:
                            pkg.hasDateRangeFilter && pkg.isSelected ? (
                                <Panel>
                                    <DateRange
                                        startDate={
                                            pkg.parameters.startDate ?? ''
                                        }
                                        endDate={pkg.parameters.endDate ?? ''}
                                        errorMessage={validateDateRange(
                                            pkg.parameters.startDate ?? '',
                                            pkg.parameters.endDate ?? ''
                                        )}
                                        onStartDateChange={(startDate) =>
                                            onStartDateChange(
                                                pkg.name,
                                                startDate
                                            )
                                        }
                                        onEndDateChange={(endDate) =>
                                            onEndDateChange(pkg.name, endDate)
                                        }
                                    />
                                </Panel>
                            ) : (
                                <></>
                            ),
                    }))}
                    onItemChange={toggleSelectedPackage}
                    onSelectAllChange={updateAllSelected}
                />
                <div className={styles.runTestButton}>
                    <ActionButtonValidation
                        actionButtonEvents={{
                            onClick: runTestPackages,
                        }}
                        className={styles.testRunButton}
                        label="Run Tests"
                        state={getState()}
                        validationMessage={getValidationMessage()}
                        developmentUrl="/public/images/able-sprites.svg"
                    />
                </div>
            </div>
        </DetailsPanel>
    )
}
