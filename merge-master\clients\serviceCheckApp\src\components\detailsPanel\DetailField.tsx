import { TextStyle } from '@able/react'
import classNames from 'classnames'
import styles from './DetailsPanel.module.scss'

interface DetailFieldProps {
    label: string
    value: string | null
    inline?: boolean
    rightAlign?: boolean
    className?: string
    isLoading?: boolean
    linkValue?: string
}

export const DetailField = ({
    label,
    value,
    inline = true,
    rightAlign = false,
    className,
    isLoading,
    linkValue,
}: DetailFieldProps) => {
    return value !== null ? (
        <div
            className={classNames(styles.detailRow, className, {
                [styles.inline]: !rightAlign && inline,
                [styles.rightAlign]: rightAlign,
            })}
        >
            <TextStyle alias="LabelA1">{label}</TextStyle>
            {isLoading ? (
                <span>...</span>
            ) : (
                <TextStyle>
                    {linkValue ? (
                        <a href={linkValue}>{value}</a>
                    ) : (
                        value ?? 'N/A'
                    )}
                </TextStyle>
            )}
        </div>
    ) : null
}
