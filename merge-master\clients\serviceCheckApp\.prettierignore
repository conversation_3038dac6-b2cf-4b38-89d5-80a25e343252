# Ignore dependencies and build outputs
node_modules/
dist/
build/
coverage/
.out/
.vscode/
.DS_Store

# Ignore package manager lock files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Ignore configuration and environment files
*.config.js
*.config.cjs
*.config.mjs
*.config.ts
.env
.env.local
.env.*.local

# Ignore logs
*.log

# Ignore minified files
*.min.js
*.min.css

# Ignore specific project files
public/
temp/
tmp/
.cache/

# Ignore auto-generated files
*.generated.*
*.snap
*.spec.js

# Ignore assets
*.png
*.jpg
*.jpeg
*.gif
*.svg
*.ico

# Ignore pre-processed files
*.compiled.*
*.bundle.*
*.map

# Ignore markdown files (optional)
*.md
