'use strict';

const { connect, disconnect } = require('./config');

async function up() {
    await connect();

    const ServiceCheckModel = (await import('../db/model/serviceCheck.js')).default;

    await ServiceCheckModel.updateMany({
        inputType: {
            $exists: false
        }
    }, {
        $set: {
            inputType: null
        }
    }, { multi: true, strict: false });

    const GeneratedServiceCheckModel = (await import('../db/model/generatedServiceCheck.js')).default;

    await GeneratedServiceCheckModel.updateMany({
        inputType: {
            $exists: false
        }
    }, {
        $set: {
            inputType: null
        }
    }, { multi: true, strict: false });

    await disconnect();
}


async function down() {
    await connect();

    const ServiceCheckModel = (await import('../db/model/serviceCheck.js')).default;

    await ServiceCheckModel.updateMany({}, {
        $unset: {
            inputType: 1
        }
    }, { multi: true, strict: false });

    const GeneratedServiceCheckModel = (await import('../db/model/generatedServiceCheck.js')).default;

    await GeneratedServiceCheckModel.updateMany({}, {
        $unset: {
            inputType: 1
        }
    }, { multi: true, strict: false });

    await disconnect();
}


module.exports = { up, down };
