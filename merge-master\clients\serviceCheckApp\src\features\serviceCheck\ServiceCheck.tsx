import { TextStyle } from '@able/react'
import classNames from 'classnames'
import { useSearchParams } from 'react-router'
import { ErrorPanel } from '../../components/errorPanel/ErrorPanel'
import { useAppState } from '../../hooks/useAppState'
import { ServiceCheckStatus } from '../serviceCheckStatus/ServiceCheckStatus'
import { ServiceCheckStatusInformationPanel } from '../serviceCheckStatusInformationPanel/ServiceCheckStatusInformationPanel'
import { CustomerDetailsPanel } from '../serviceDetails/customerDetails/CustomerDetailsPanel'
import { ServiceSearch } from '../serviceSearch/ServiceSearch'
import { OverviewTab } from './components/overview/OverviewTab'
import { ServiceCheckDetailsPanel } from './components/serviceCheckDetails/ServiceCheckDetailsPanel'
import { TestsTab } from './components/tests/TestsTab'
import styles from './ServiceCheck.module.scss'

export const ServiceCheck = () => {
    const { appState, setAppState } = useAppState()
    const [searchParams] = useSearchParams()
    const id = searchParams.get('id')
    const isLoadingService = appState.isLoadingService

    const isNew = !id
    const productIdentified = !!appState.serviceDetails.productTypes?.length

    const handleTabChange = (tab: 'overview' | 'tests') => {
        setAppState({
            ...appState,
            selectedTab: tab,
        })
    }

    return (
        <div className={styles.serviceCheck}>
            <div className={styles.searchRow}>
                <div>
                    <TextStyle element="h1" alias="HeadingA">
                        Service Check
                    </TextStyle>
                    <ServiceSearch />
                    {productIdentified && <ServiceCheckStatusInformationPanel className={styles.infoPanel}/>}

                    {productIdentified && <ServiceCheckDetailsPanel />}

                </div>
                {appState.id &&
                    !isNew &&
                    !isLoadingService &&
                    productIdentified && <CustomerDetailsPanel />}
            </div>
            

            {productIdentified ? (
                <>
                    {appState.id && !isNew && !isLoadingService && (
                        <div className={styles.content}>
                            <div className={styles.tabNavigation}>
                                <div className={styles.tabs}>
                                    <button
                                        onClick={() =>
                                            handleTabChange('overview')
                                        }
                                        className={classNames(
                                            styles.tabButton,
                                            {
                                                [styles.activeTab]:
                                                    appState.selectedTab ===
                                                    'overview',
                                            }
                                        )}
                                    >
                                        <TextStyle
                                            element="h3"
                                            alias="HeadingC"
                                        >
                                            Overview
                                        </TextStyle>
                                    </button>
                                    <button
                                        onClick={() => handleTabChange('tests')}
                                        className={classNames(
                                            styles.tabButton,
                                            {
                                                [styles.activeTab]:
                                                    appState.selectedTab ===
                                                    'tests',
                                            }
                                        )}
                                    >
                                        <TextStyle
                                            element="h3"
                                            alias="HeadingC"
                                        >
                                            Tests
                                        </TextStyle>
                                    </button>
                                </div>
                                <ServiceCheckStatus
                                    className={styles.serviceCheckStatus}
                                />
                            </div>

                            <div className={styles.tabContent}>
                                {appState.selectedTab === 'overview' && (
                                    <OverviewTab />
                                )}
                                {appState.selectedTab === 'tests' && (
                                    <TestsTab />
                                )}
                            </div>
                        </div>
                    )}
                </>
            ) : (
                <>
                    {appState.id && !isNew && !isLoadingService && (
                        <ErrorPanel
                            errorMessage={
                                'Service Number was not valid. Please try searching for another.'
                            }
                        />
                    )}
                </>
            )}
        </div>
    )
}
