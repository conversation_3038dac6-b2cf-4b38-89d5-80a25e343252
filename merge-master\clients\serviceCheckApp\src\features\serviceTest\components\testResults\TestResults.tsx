import { Switch, TextStyle } from '@able/react'
import React, { useMemo } from 'react'
import { useAppState } from '../../../../hooks/useAppState'
import { TestResultItem } from './TestResultItem'
import styles from './TestResults.module.scss'

export const TestResults = () => {
    const { appState, setAppState } = useAppState()
    const { testResults, showOkResults, showUndeterminedResults } = appState

    const filteredTestResults = useMemo(() => {
        return testResults.map((testResultModel) => {
            let filteredResults = testResultModel.results || []
            if (!showOkResults) {
                filteredResults = filteredResults.filter(
                    (resultItem) => resultItem.data?.result !== 'OK'
                )
            }
            if (!showUndeterminedResults) {
                filteredResults = filteredResults.filter(
                    (resultItem) => resultItem.data
                )
            }
            return {
                ...testResultModel,
                results: filteredResults.length ? filteredResults : null,
            }
        })
    }, [testResults, showOkResults, showUndeterminedResults])

    return (
        <div className={styles.testResults}>
            <div className={styles.testResultHeader}>
                <TextStyle element="h3" alias="HeadingC">
                    Test Results
                </TextStyle>

                <div className={styles.toggles}>
                    <Switch
                        events={{
                            onClick: () =>
                                setAppState({
                                    ...appState,
                                    showUndeterminedResults:
                                        !appState.showUndeterminedResults,
                                }),
                        }}
                        checked={appState.showUndeterminedResults}
                        variant="Compact"
                        label="Show Undetermined Results"
                        className={styles.toggle}
                    />
                    <Switch
                        events={{
                            onClick: () =>
                                setAppState({
                                    ...appState,
                                    showOkResults: !appState.showOkResults,
                                }),
                        }}
                        checked={appState.showOkResults}
                        variant="Compact"
                        label="Show OK Results"
                        className={styles.toggle}
                    />
                </div>
            </div>

            <div className={styles.testResultsGrid}>
                {Object.entries(filteredTestResults).map(
                    ([packageName, packageData]) => (
                        <React.Fragment key={packageName}>
                            {packageData.results?.map((result, index) => (
                                <TestResultItem
                                    key={`${packageName}-result-${index}`}
                                    packageData={packageData}
                                    result={result}
                                />
                            ))}
                        </React.Fragment>
                    )
                )}
            </div>
        </div>
    )
}
