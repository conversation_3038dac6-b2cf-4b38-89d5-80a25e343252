'use strict';

import chai from 'chai';
import _ from 'lodash';

import apiFunctions from '../../modules/apiFunctions.js';
import config from '../config.js';
import Sdwan from '../../db/model/sdwan.js';

describe('API Functions', () => {
    describe('getSDWANDocument()', () => {
        before(() => {
            global.gConfig = _.cloneDeep(config.default);
        });

        afterEach(async() => {
            await Sdwan.deleteMany({});
        });

        it('Empty object', async() => {
            chai.expect(await apiFunctions.getSDWANDocument({})).to.deep.equal(null);
        });

        it('FNN filter, no SDWAN documents', async() => {
            chai.expect(await apiFunctions.getSDWANDocument({ fnn: 'N1234567R' })).to.deep.equal({
                message: 'SDWAN document for FNN / device name N1234567R not found'
            });
        });

        it('Device name filter, no SDWAN documents', async() => {
            chai.expect(await apiFunctions.getSDWANDocument({ deviceName: 'abcdefgr01c01' })).to.deep.equal({
                message: 'SDWAN document for FNN / device name abcdefgr01c01 not found'
            });
        });

        it('FNN and device name filter, no SDWAN documents', async() => {
            chai.expect(await apiFunctions.getSDWANDocument({ deviceName: 'abcdefgr01c01', fnn: 'N1234567R' })).to.deep.equal({
                message: 'SDWAN document for FNN / device name N1234567R not found'
            });
        });

        it('FNN filter, matching SDWAN document', async() => {
            await new Sdwan({
                sdwanFnn: 'N1234567R',
                deviceName: 'deviceName'
            }).save();

            let sdwanDocument = await apiFunctions.getSDWANDocument({ fnn: 'N1234567R' });

            chai.expect(sdwanDocument).to.deep.equal({
                carriageFnn: [],
                deviceName: 'deviceName',
                deviceTimestamp: 'Invalid Date',
                deviceUptimeDate: 'Invalid Date',
                interfaceData: [],
                itamIncident: [],
                networkTopology: [],
                sdwanFnn: 'N1234567R'
            });
        });

        it('FNN filter, non-matching SDWAN document', async() => {
            await new Sdwan({
                sdwanFnn: 'N1111111R',
                deviceName: 'deviceName'
            }).save();

            let sdwanDocument = await apiFunctions.getSDWANDocument({ fnn: 'N1234567R' });

            chai.expect(sdwanDocument).to.deep.equal({
                message: 'SDWAN document for FNN / device name N1234567R not found'
            });
        });

        it('FNN filter, matching SDWAN document with all fields', async() => {
            await new Sdwan({
                carriageFnn: 'A0123456789',
                deviceName: 'deviceName',
                deviceStatus: 'reachable',
                deviceUptimeDate: new Date('2022-07-01T12:00:00Z'),
                deviceIP: '*******',
                latitude: null,
                longitude: null,
                deviceModel: 'vedge-cloud',
                siteID: '1234',
                deviceTimestamp: new Date('2022-07-02T12:00:00Z'),
                serviceType: 'serviceType',
                interfaceData: [{
                    bwUp : null,
                    bwDown : null,
                    rxKbps : 0,
                    txKbps : 0,
                    rxDrops : 0,
                    txDrops : 0,
                    rxOctets : 1000,
                    txOctets : 2000,
                    rxPackets : 36,
                    txPackets : 36,
                    interfaceIP : '-',
                    interfaceName : 'eth0',
                    interfaceType : null,
                    interfaceStatus : 'Up',
                    interfaceTimestamp: new Date('2022-07-03T12:00:00Z')
                },
                {
                    bwUp : null,
                    bwDown : null,
                    rxKbps : 0,
                    txKbps : 0,
                    rxDrops : 0,
                    txDrops : 0,
                    rxOctets : 2000,
                    txOctets : 1000,
                    rxPackets : 36,
                    txPackets : 36,
                    interfaceIP : '-',
                    interfaceName : 'eth1',
                    interfaceType : null,
                    interfaceStatus : 'Up',
                    interfaceTimestamp: new Date('2022-07-04T12:00:00Z')
                }],
                sdwanFnn: 'N1234567R',
                cidn: '5678',
                customerName: 'CUSTOMER LTD',
                customerAddress: '123 Address',
                networkTopology: null,
                imsi: '12345678',
                flexiPlanName: null,
                flexiPlanLimit: null,
                itamIncident: null,
                planUsage: null,
                createdAt: new Date('2022-07-05T12:00:00Z'),
                updatedAt: new Date('2022-07-05T12:00:00Z')
            }).save();

            let sdwanDocument = await apiFunctions.getSDWANDocument({ fnn: 'N1234567R' });

            let expectedResult = {
                carriageFnn: [
                    'A0123456789'
                ],
                cidn: '5678',
                customerAddress: '123 Address',
                customerName: 'CUSTOMER LTD',
                deviceIP: '*******',
                deviceModel: 'vedge-cloud',
                deviceName: 'deviceName',
                deviceStatus: 'reachable',
                deviceTimestamp: '02/07/2022, 10:00:00 pm',
                deviceUptimeDate: '01/07/2022, 10:00:00 pm',
                flexiPlanLimit: null,
                flexiPlanName: null,
                imsi: '12345678',
                interfaceData: [{
                    bwUp : null,
                    bwDown : null,
                    rxKbps : 0,
                    txKbps : 0,
                    rxDrops : 0,
                    txDrops : 0,
                    rxOctets : 1000,
                    txOctets : 2000,
                    rxPackets : 36,
                    txPackets : 36,
                    interfaceIP : '-',
                    interfaceName : 'eth0',
                    interfaceType : null,
                    interfaceStatus : 'Up',
                    interfaceTimestamp: '03/07/2022, 10:00:00 pm'
                },
                {
                    bwUp : null,
                    bwDown : null,
                    rxKbps : 0,
                    txKbps : 0,
                    rxDrops : 0,
                    txDrops : 0,
                    rxOctets : 2000,
                    txOctets : 1000,
                    rxPackets : 36,
                    txPackets : 36,
                    interfaceIP : '-',
                    interfaceName : 'eth1',
                    interfaceType : null,
                    interfaceStatus : 'Up',
                    interfaceTimestamp: '04/07/2022, 10:00:00 pm'
                }],
                itamIncident: null,
                latitude: null,
                longitude: null,
                networkTopology: null,
                planUsage: null,
                sdwanFnn: 'N1234567R',
                serviceType: 'serviceType',
                siteID: '1234'
            };

            // Tests key order is sorted as well
            chai.expect(sdwanDocument).to.deep.equal(expectedResult);
            chai.expect(Object.keys(sdwanDocument)).to.deep.equal(Object.keys(expectedResult));
        });

        it('FNN filter, matching SDWAN document with all fields, null IMSI data', async() => {
            await new Sdwan({
                carriageFnn: 'A0123456789',
                deviceName: 'deviceName',
                deviceStatus: 'reachable',
                deviceUptimeDate: new Date('2022-07-01T12:00:00Z'),
                deviceIP: '*******',
                latitude: null,
                longitude: null,
                deviceModel: 'vedge-cloud',
                siteID: '1234',
                deviceTimestamp: new Date('2022-07-02T12:00:00Z'),
                serviceType: 'serviceType',
                interfaceData: [{
                    bwUp : null,
                    bwDown : null,
                    rxKbps : 0,
                    txKbps : 0,
                    rxDrops : 0,
                    txDrops : 0,
                    rxOctets : 1000,
                    txOctets : 2000,
                    rxPackets : 36,
                    txPackets : 36,
                    interfaceIP : '-',
                    interfaceName : 'eth0',
                    interfaceType : null,
                    interfaceStatus : 'Up',
                    interfaceTimestamp: new Date('2022-07-03T12:00:00Z')
                },
                {
                    bwUp : null,
                    bwDown : null,
                    rxKbps : 0,
                    txKbps : 0,
                    rxDrops : 0,
                    txDrops : 0,
                    rxOctets : 2000,
                    txOctets : 1000,
                    rxPackets : 36,
                    txPackets : 36,
                    interfaceIP : '-',
                    interfaceName : 'eth1',
                    interfaceType : null,
                    interfaceStatus : 'Up',
                    interfaceTimestamp: new Date('2022-07-04T12:00:00Z')
                }],
                sdwanFnn: 'N1234567R',
                cidn: '5678',
                customerName: 'CUSTOMER LTD',
                customerAddress: '123 Address',
                networkTopology: null,
                imsi: null,
                flexiPlanName: null,
                flexiPlanLimit: null,
                itamIncident: null,
                planUsage: null,
                createdAt: new Date('2022-07-05T12:00:00Z'),
                updatedAt: new Date('2022-07-05T12:00:00Z')
            }).save();

            let sdwanDocument = await apiFunctions.getSDWANDocument({ fnn: 'N1234567R' });

            let expectedResult = {
                carriageFnn: [
                    'A0123456789'
                ],
                cidn: '5678',
                customerAddress: '123 Address',
                customerName: 'CUSTOMER LTD',
                deviceIP: '*******',
                deviceModel: 'vedge-cloud',
                deviceName: 'deviceName',
                deviceStatus: 'reachable',
                deviceTimestamp: '02/07/2022, 10:00:00 pm',
                deviceUptimeDate: '01/07/2022, 10:00:00 pm',
                flexiPlanLimit: null,
                flexiPlanName: null,
                interfaceData: [{
                    bwUp : null,
                    bwDown : null,
                    rxKbps : 0,
                    txKbps : 0,
                    rxDrops : 0,
                    txDrops : 0,
                    rxOctets : 1000,
                    txOctets : 2000,
                    rxPackets : 36,
                    txPackets : 36,
                    interfaceIP : '-',
                    interfaceName : 'eth0',
                    interfaceType : null,
                    interfaceStatus : 'Up',
                    interfaceTimestamp: '03/07/2022, 10:00:00 pm'
                },
                {
                    bwUp : null,
                    bwDown : null,
                    rxKbps : 0,
                    txKbps : 0,
                    rxDrops : 0,
                    txDrops : 0,
                    rxOctets : 2000,
                    txOctets : 1000,
                    rxPackets : 36,
                    txPackets : 36,
                    interfaceIP : '-',
                    interfaceName : 'eth1',
                    interfaceType : null,
                    interfaceStatus : 'Up',
                    interfaceTimestamp: '04/07/2022, 10:00:00 pm'
                }],
                itamIncident: null,
                latitude: null,
                longitude: null,
                networkTopology: null,
                planUsage: null,
                sdwanFnn: 'N1234567R',
                serviceType: 'serviceType',
                siteID: '1234'
            };

            // Tests key order is sorted as well
            chai.expect(sdwanDocument).to.deep.equal(expectedResult);
            chai.expect(Object.keys(sdwanDocument)).to.deep.equal(Object.keys(expectedResult));
        });


        it('Device name filter, matching SDWAN document', async() => {
            await new Sdwan({
                sdwanFnn: 'N1234567R',
                deviceName: 'abcdefgr01c01'
            }).save();

            let sdwanDocument = await apiFunctions.getSDWANDocument({ deviceName: 'abcdefgr01c01' });

            chai.expect(sdwanDocument).to.deep.equal({
                carriageFnn: [],
                deviceName: 'abcdefgr01c01',
                deviceTimestamp: 'Invalid Date',
                deviceUptimeDate: 'Invalid Date',
                interfaceData: [],
                itamIncident: [],
                networkTopology: [],
                sdwanFnn: 'N1234567R'
            });
        });

        it('Device name filter, non-matching SDWAN document', async() => {
            await new Sdwan({
                sdwanFnn: 'N1234567R',
                deviceName: 'abcdefgr01c02'
            }).save();

            let sdwanDocument = await apiFunctions.getSDWANDocument({ deviceName: 'abcdefgr01c01' });

            chai.expect(sdwanDocument).to.deep.equal({
                message: 'SDWAN document for FNN / device name abcdefgr01c01 not found'
            });
        });

        it('Device name filter, matching SDWAN document with all fields', async() => {
            await new Sdwan({
                carriageFnn: 'A0123456789',
                deviceName: 'abcdefgr01c01',
                deviceStatus: 'reachable',
                deviceUptimeDate: new Date('2022-07-01T12:00:00Z'),
                deviceIP: '*******',
                latitude: null,
                longitude: null,
                deviceModel: 'vedge-cloud',
                siteID: '1234',
                deviceTimestamp: new Date('2022-07-02T12:00:00Z'),
                serviceType: 'serviceType',
                interfaceData: [{
                    bwUp : null,
                    bwDown : null,
                    rxKbps : 0,
                    txKbps : 0,
                    rxDrops : 0,
                    txDrops : 0,
                    rxOctets : 1000,
                    txOctets : 2000,
                    rxPackets : 36,
                    txPackets : 36,
                    interfaceIP : '-',
                    interfaceName : 'eth0',
                    interfaceType : null,
                    interfaceStatus : 'Up',
                    interfaceTimestamp: new Date('2022-07-03T12:00:00Z')
                },
                {
                    bwUp : null,
                    bwDown : null,
                    rxKbps : 0,
                    txKbps : 0,
                    rxDrops : 0,
                    txDrops : 0,
                    rxOctets : 2000,
                    txOctets : 1000,
                    rxPackets : 36,
                    txPackets : 36,
                    interfaceIP : '-',
                    interfaceName : 'eth1',
                    interfaceType : null,
                    interfaceStatus : 'Up',
                    interfaceTimestamp: new Date('2022-07-04T12:00:00Z')
                }],
                sdwanFnn: 'N1234567R',
                cidn: '5678',
                customerName: 'CUSTOMER LTD',
                customerAddress: '123 Address',
                networkTopology: null,
                imsi: '12345678',
                flexiPlanName: null,
                flexiPlanLimit: null,
                itamIncident: null,
                planUsage: null,
                createdAt: new Date('2022-07-05T12:00:00Z'),
                updatedAt: new Date('2022-07-05T12:00:00Z')
            }).save();

            let sdwanDocument = await apiFunctions.getSDWANDocument({ deviceName: 'abcdefgr01c01' });

            let expectedResult = {
                carriageFnn: [
                    'A0123456789'
                ],
                cidn: '5678',
                customerAddress: '123 Address',
                customerName: 'CUSTOMER LTD',
                deviceIP: '*******',
                deviceModel: 'vedge-cloud',
                deviceName: 'abcdefgr01c01',
                deviceStatus: 'reachable',
                deviceTimestamp: '02/07/2022, 10:00:00 pm',
                deviceUptimeDate: '01/07/2022, 10:00:00 pm',
                flexiPlanLimit: null,
                flexiPlanName: null,
                imsi: '12345678',
                interfaceData: [{
                    bwUp : null,
                    bwDown : null,
                    rxKbps : 0,
                    txKbps : 0,
                    rxDrops : 0,
                    txDrops : 0,
                    rxOctets : 1000,
                    txOctets : 2000,
                    rxPackets : 36,
                    txPackets : 36,
                    interfaceIP : '-',
                    interfaceName : 'eth0',
                    interfaceType : null,
                    interfaceStatus : 'Up',
                    interfaceTimestamp: '03/07/2022, 10:00:00 pm'
                },
                {
                    bwUp : null,
                    bwDown : null,
                    rxKbps : 0,
                    txKbps : 0,
                    rxDrops : 0,
                    txDrops : 0,
                    rxOctets : 2000,
                    txOctets : 1000,
                    rxPackets : 36,
                    txPackets : 36,
                    interfaceIP : '-',
                    interfaceName : 'eth1',
                    interfaceType : null,
                    interfaceStatus : 'Up',
                    interfaceTimestamp: '04/07/2022, 10:00:00 pm'
                }],
                itamIncident: null,
                latitude: null,
                longitude: null,
                networkTopology: null,
                planUsage: null,
                sdwanFnn: 'N1234567R',
                serviceType: 'serviceType',
                siteID: '1234'
            };

            // Tests key order is sorted as well
            chai.expect(sdwanDocument).to.deep.equal(expectedResult);
            chai.expect(Object.keys(sdwanDocument)).to.deep.equal(Object.keys(expectedResult));
        });

        it('Device name filter, matching SDWAN document with all fields, null IMSI data', async() => {
            await new Sdwan({
                carriageFnn: 'A0123456789',
                deviceName: 'abcdefgr01c01',
                deviceStatus: 'reachable',
                deviceUptimeDate: new Date('2022-07-01T12:00:00Z'),
                deviceIP: '*******',
                latitude: null,
                longitude: null,
                deviceModel: 'vedge-cloud',
                siteID: '1234',
                deviceTimestamp: new Date('2022-07-02T12:00:00Z'),
                serviceType: 'serviceType',
                interfaceData: [{
                    bwUp : null,
                    bwDown : null,
                    rxKbps : 0,
                    txKbps : 0,
                    rxDrops : 0,
                    txDrops : 0,
                    rxOctets : 1000,
                    txOctets : 2000,
                    rxPackets : 36,
                    txPackets : 36,
                    interfaceIP : '-',
                    interfaceName : 'eth0',
                    interfaceType : null,
                    interfaceStatus : 'Up',
                    interfaceTimestamp: new Date('2022-07-03T12:00:00Z')
                },
                {
                    bwUp : null,
                    bwDown : null,
                    rxKbps : 0,
                    txKbps : 0,
                    rxDrops : 0,
                    txDrops : 0,
                    rxOctets : 2000,
                    txOctets : 1000,
                    rxPackets : 36,
                    txPackets : 36,
                    interfaceIP : '-',
                    interfaceName : 'eth1',
                    interfaceType : null,
                    interfaceStatus : 'Up',
                    interfaceTimestamp: new Date('2022-07-04T12:00:00Z')
                }],
                sdwanFnn: 'N1234567R',
                cidn: '5678',
                customerName: 'CUSTOMER LTD',
                customerAddress: '123 Address',
                networkTopology: null,
                imsi: null,
                flexiPlanName: null,
                flexiPlanLimit: null,
                itamIncident: null,
                planUsage: null,
                createdAt: new Date('2022-07-05T12:00:00Z'),
                updatedAt: new Date('2022-07-05T12:00:00Z')
            }).save();

            let sdwanDocument = await apiFunctions.getSDWANDocument({ deviceName: 'abcdefgr01c01' });

            let expectedResult = {
                carriageFnn: [
                    'A0123456789'
                ],
                cidn: '5678',
                customerAddress: '123 Address',
                customerName: 'CUSTOMER LTD',
                deviceIP: '*******',
                deviceModel: 'vedge-cloud',
                deviceName: 'abcdefgr01c01',
                deviceStatus: 'reachable',
                deviceTimestamp: '02/07/2022, 10:00:00 pm',
                deviceUptimeDate: '01/07/2022, 10:00:00 pm',
                flexiPlanLimit: null,
                flexiPlanName: null,
                interfaceData: [{
                    bwUp : null,
                    bwDown : null,
                    rxKbps : 0,
                    txKbps : 0,
                    rxDrops : 0,
                    txDrops : 0,
                    rxOctets : 1000,
                    txOctets : 2000,
                    rxPackets : 36,
                    txPackets : 36,
                    interfaceIP : '-',
                    interfaceName : 'eth0',
                    interfaceType : null,
                    interfaceStatus : 'Up',
                    interfaceTimestamp: '03/07/2022, 10:00:00 pm'
                },
                {
                    bwUp : null,
                    bwDown : null,
                    rxKbps : 0,
                    txKbps : 0,
                    rxDrops : 0,
                    txDrops : 0,
                    rxOctets : 2000,
                    txOctets : 1000,
                    rxPackets : 36,
                    txPackets : 36,
                    interfaceIP : '-',
                    interfaceName : 'eth1',
                    interfaceType : null,
                    interfaceStatus : 'Up',
                    interfaceTimestamp: '04/07/2022, 10:00:00 pm'
                }],
                itamIncident: null,
                latitude: null,
                longitude: null,
                networkTopology: null,
                planUsage: null,
                sdwanFnn: 'N1234567R',
                serviceType: 'serviceType',
                siteID: '1234'
            };

            // Tests key order is sorted as well
            chai.expect(sdwanDocument).to.deep.equal(expectedResult);
            chai.expect(Object.keys(sdwanDocument)).to.deep.equal(Object.keys(expectedResult));
        });

        it('Input FNN and device name, ensure FNN is used with higher priority test 1', async() => {
            await new Sdwan({
                sdwanFnn: 'N1234567R',
                deviceName: 'deviceName'
            }).save();

            let sdwanDocument = await apiFunctions.getSDWANDocument({ deviceName: 'abcdefgr01c01', fnn: 'N1234567R' });

            chai.expect(sdwanDocument).to.deep.equal({
                carriageFnn: [],
                deviceName: 'deviceName',
                deviceTimestamp: 'Invalid Date',
                deviceUptimeDate: 'Invalid Date',
                interfaceData: [],
                itamIncident: [],
                networkTopology: [],
                sdwanFnn: 'N1234567R'
            });
        });

        it('Input FNN and device name, ensure FNN is used with higher priority test 2', async() => {
            await new Sdwan({
                sdwanFnn: 'N1234567R',
                deviceName: 'deviceName'
            }).save();

            let sdwanDocument = await apiFunctions.getSDWANDocument({ deviceName: 'abcdefgr01c01', fnn: 'N1111111R' });

            chai.expect(sdwanDocument).to.deep.equal({
                message: 'SDWAN document for FNN / device name N1111111R not found'
            });
        });

        it('Input FNN and device name, ensure FNN is used with higher priority test 3', async() => {
            await new Sdwan({
                sdwanFnn: 'N1111111R',
                deviceName: 'deviceName'
            }).save();

            await new Sdwan({
                sdwanFnn: 'N1234567R',
                deviceName: 'defxyzar01c01'
            }).save();

            let sdwanDocument = await apiFunctions.getSDWANDocument({ deviceName: 'defxyzar01c01', fnn: 'N1111111R' });

            chai.expect(sdwanDocument).to.deep.equal({
                carriageFnn: [],
                deviceName: 'deviceName',
                deviceTimestamp: 'Invalid Date',
                deviceUptimeDate: 'Invalid Date',
                interfaceData: [],
                itamIncident: [],
                networkTopology: [],
                sdwanFnn: 'N1111111R'
            });
        });
    });
});
