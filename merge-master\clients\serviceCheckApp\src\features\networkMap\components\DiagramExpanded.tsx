import DOMPurify from 'dompurify'
import { useCallback, useEffect, useRef, useState } from 'react'
import { UncontrolledReactSVGPanZoom } from 'react-svg-pan-zoom'
import { Diagram } from '../../../infrastructure/models'
import styles from '../NetworkMap.module.scss'

interface DiagramExpandedProps {
    diagram: Diagram
    width: number
    height: number
}

export const DiagramExpanded = ({
    diagram,
    width,
    height,
}: DiagramExpandedProps) => {
    const [svgInnerHTML, setSvgInnerHTML] = useState<string>('')
    // Use a container ref on the wrapping div.
    const containerRef = useRef<HTMLDivElement | null>(null)
    const [svgWidth, setSvgWidth] = useState<number>(1800)
    const [svgHeight, setSvgHeight] = useState<number>(800)
    // Parse and sanitize the SVG.
    useEffect(() => {
        if (diagram.xml) {
            const parser = new DOMParser()
            const xmlDoc = parser.parseFromString(
                diagram.xml,
                'application/xml'
            )
            const svgElement = xmlDoc.querySelector('svg')
            if (svgElement) {
                svgElement.style.transformOrigin = '0 0'
                const serializedSvg = new XMLSerializer().serializeToString(
                    svgElement
                )
                const sanitizedSvg = DOMPurify.sanitize(serializedSvg, {
                    USE_PROFILES: { svg: true },
                })
                const tempDoc = parser.parseFromString(
                    sanitizedSvg,
                    'application/xml'
                )
                const sanitizedSvgElement = tempDoc.querySelector('svg')
                if (sanitizedSvgElement) {
                    // Try to extract width and height attributes.
                    const widthAttr = sanitizedSvgElement.getAttribute('width')
                    const heightAttr =
                        sanitizedSvgElement.getAttribute('height')
                    let w = parseFloat(widthAttr || '')
                    let h = parseFloat(heightAttr || '')
                    if (!isNaN(w) && !isNaN(h)) {
                        setSvgWidth(w)
                        setSvgHeight(h)
                    } else if (sanitizedSvgElement.getAttribute('viewBox')) {
                        // If width/height are missing, derive from viewBox.
                        const parts = sanitizedSvgElement
                            .getAttribute('viewBox')!
                            .split(' ')
                        if (parts.length === 4) {
                            const vbWidth = parseFloat(parts[2])
                            const vbHeight = parseFloat(parts[3])
                            if (!isNaN(vbWidth) && !isNaN(vbHeight)) {
                                setSvgWidth(vbWidth)
                                setSvgHeight(vbHeight)
                            }
                        }
                    }
                    setSvgInnerHTML(sanitizedSvgElement.innerHTML)
                }
            }
        }
    }, [diagram.xml])

    // Functions for node highlighting.
    const highlightNode = useCallback(
        (targetId: string, svg: SVGSVGElement) => {
            const targetElement = svg.getElementById(targetId) as SVGGElement
            if (targetElement) {
                let transformElement =
                    targetElement.firstElementChild as SVGGElement
                if (
                    targetElement.lastElementChild?.tagName.toLowerCase() ===
                    'a'
                ) {
                    const childElement =
                        targetElement.lastElementChild as SVGAElement
                    transformElement =
                        childElement.firstElementChild as SVGGElement
                }
                if (transformElement) {
                    const toBeHiddenImage =
                        transformElement.firstElementChild?.getAttribute(
                            'xlink:href'
                        )
                    if (toBeHiddenImage) {
                        const highlightElement = svg.getElementById(
                            `${targetId}_highlight`
                        ) as SVGGElement
                        if (highlightElement) {
                            const highlightChild =
                                highlightElement.firstElementChild as SVGGElement
                            if (highlightChild) {
                                const toBeShownImage =
                                    highlightChild.firstElementChild?.getAttribute(
                                        'xlink:href'
                                    )
                                if (toBeShownImage) {
                                    transformElement.firstElementChild?.setAttribute(
                                        'xlink:href',
                                        toBeShownImage
                                    )
                                    highlightChild.firstElementChild?.setAttribute(
                                        'xlink:href',
                                        toBeHiddenImage
                                    )
                                }
                            }
                        }
                    }
                }
            }
        },
        []
    )

    const unhighlightNode = useCallback(
        (targetId: string, svg: SVGSVGElement) => {
            const targetElement = svg.getElementById(targetId) as SVGGElement
            if (targetElement) {
                let transformElement =
                    targetElement.firstElementChild as SVGGElement
                if (
                    targetElement.lastElementChild?.tagName.toLowerCase() ===
                    'a'
                ) {
                    const childElement =
                        targetElement.lastElementChild as SVGAElement
                    transformElement =
                        childElement.firstElementChild as SVGGElement
                }
                if (transformElement) {
                    const toBeShownImage =
                        transformElement.firstElementChild?.getAttribute(
                            'xlink:href'
                        )
                    if (toBeShownImage) {
                        const highlightElement = svg.getElementById(
                            `${targetId}_highlight`
                        ) as SVGGElement
                        if (highlightElement) {
                            const highlightChild =
                                highlightElement.firstElementChild as SVGGElement
                            if (highlightChild) {
                                const toBeHiddenImage =
                                    highlightChild.firstElementChild?.getAttribute(
                                        'xlink:href'
                                    )
                                if (toBeHiddenImage) {
                                    transformElement.firstElementChild?.setAttribute(
                                        'xlink:href',
                                        toBeHiddenImage
                                    )
                                    highlightChild.firstElementChild?.setAttribute(
                                        'xlink:href',
                                        toBeShownImage
                                    )
                                }
                            }
                        }
                    }
                }
            }
        },
        []
    )

    // Event handlers attached via React on the inner <g>.
    const handleMouseOver = useCallback(
        (event: React.MouseEvent<SVGGElement, MouseEvent>) => {
            const target = event.target as SVGElement
            const nodeId = target?.closest("[id^='y.node.']")?.id
            if (!nodeId || !containerRef.current) return

            // Query for the underlying <svg> element from the container.
            const svg = containerRef.current.querySelector('svg')
            if (!svg) return

            const tooltip = svg.querySelector(`[id="tooltip.${nodeId}"]`)
            if (tooltip) {
                const rect = svg.getBoundingClientRect()
                const x = event.clientX - rect.left + 10
                const y = event.clientY - rect.top + 10
                tooltip.setAttribute('visibility', 'visible')
                tooltip.setAttribute('transform', `translate(${x}, ${y})`)
                svg.appendChild(tooltip)
            }
            highlightNode(nodeId, svg)
        },
        [highlightNode]
    )

    const handleMouseOut = useCallback(
        (event: React.MouseEvent<SVGGElement, MouseEvent>) => {
            if (!containerRef.current) return
            const svg = containerRef.current.querySelector('svg')
            if (!svg) return

            const target = event.target as SVGElement
            const nodeId = target?.closest("[id^='y.node.']")?.id
            if (!nodeId) return

            const tooltip = svg.querySelector(`[id="tooltip.${nodeId}"]`)
            tooltip?.setAttribute('visibility', 'hidden')
            unhighlightNode(nodeId, svg)
        },
        [unhighlightNode]
    )

    return svgInnerHTML ? (
        <div className={styles.diagramContainer} ref={containerRef}>
            <UncontrolledReactSVGPanZoom
                width={width}
                height={height}
                scaleFactorOnWheel={1.1}
            >
                <svg width={svgWidth} height={svgHeight}>
                    <g
                        onMouseOver={handleMouseOver}
                        onMouseOut={handleMouseOut}
                        dangerouslySetInnerHTML={{ __html: svgInnerHTML }}
                    />
                </svg>
            </UncontrolledReactSVGPanZoom>
        </div>
    ) : null
}
