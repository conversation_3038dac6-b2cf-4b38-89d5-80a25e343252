<%- include('header') %>
<%- include('jsonEditInclude', {}) %>
<%- include('menu', {currentTab: 'Form'}); %>
<link rel="stylesheet" type="text/css" href="/public/stylesheets/jsgrid.min.css">
<link rel="stylesheet" type="text/css" href="/public/stylesheets/jsgrid-theme.min.css">
<link rel="stylesheet" type="text/css" href="/public/stylesheets/select2.min.css">
<link rel="stylesheet" href="/public/stylesheets/flatpickr.min.css">
<script src="/socket.io/socket.io.js"></script>
<script src="/public/javascripts/flatpickr.min.js"></script>
<script src="/public/javascripts/ace.js"></script>
<script src="/public/javascripts/select2.min.js"></script>
<script src="/public/javascripts/json2csv.umd.min.js"></script>
<script src="/public/javascripts/jsgrid.min.js"></script>
<br>
<div id="searchContainer" class="container">
    <div class="border border-secondary rounded p-4">
        <h3>Service Check Advanced Search</h3>
        <div class="row">
            <div class="col-12">
                <label for="maxNumRecords">Max Number Records Returned:</label>
                <select id="maxNumRecords" style="width: 6em">
                    <option value=10>10</option>
                    <option value=25>25</option>
                    <option value=50 selected>50</option>
                    <option value=100>100</option>
                    <option value=200>200</option>
                    <option value=500>500</option>
                    <option value=1000>1000</option>
                </select>
            </div>
        </div>
        <br />
        <div class="row">
            <div class="col-12">
                <button id="resetDates" class="btn btn-sm btn-secondary" title="Default time range (1 month)"><span class="fas fa-undo"></span></button>&nbsp;
                <label for="startDate" class="col-form-label">Start:</label>&nbsp;
                <input type="text" id="startDate" />&nbsp;
                <label for="endDate" class="col-form-label">End:</label>&nbsp;
                <input type="text" id="endDate" />&nbsp;
            </div>
        </div>
        <br />
        <div class="row">
            <div class="col-12">
                <label class="col-form-label">Columns for the service check history table. FNN and Time columns will always be displayed.</label>
                <button id="columnSelectReset" class="btn btn-sm btn-secondary" title="Reset columns to default"><span class="fas fa-undo"></span></button>
            </div>
        </div>
        <br />
        <div class="row">
            <div class="col-12">
                <select id="columnSelect" multiple="multiple" style="width:100%;min-height:500px;">
                    <option value="status" selected>Status</option>
                    <option value="suite">Suite</option>
                    <option value="startMethod">Start Method</option>
                    <option value="carriageType" selected>Carriage Type</option>
                    <option value="carriageFNN" selected>Carriage FNN</option>
                    <option value="deviceName" selected>Device Name</option>
                    <option value="billingFNN">Billing FNN</option>
                    <option value="MDNFNN">MDN FNN</option>
                    <option value="CIDN">CIDN</option>
                    <option value="nbnAccessType">NBN Access Type</option>
                    <option value="nbnId">NBN ID</option>
                    <option value="createdBy" selected>User</option>
                    <option value="serviceType" selected>Service Type</option>
                    <option value="siiamCases" selected>SIIAM Cases</option>
                    <option value="serviceCentralIncidents" selected>Service Central Incidents</option>
                </select>
            </div>
        </div>
        <br />
        <label for="serviceCheckSearchExpression">Search Expression</label>
        <div id="serviceCheckSearchExpression" class="border border-secondary" style="height:100%;width:100%;"></div>
        <br />
        <div class="row">
            <div class="col-6">
                <button id="startSearch" class="btn btn-primary mr-1">Search</button>
                <button id="cancelSearch" disabled class="btn btn-secondary mx-1">Cancel</button>
                <button id="downloadCsv" class="btn btn-dark mx-1"><span class="fas fa-download"></span> CSV</button>
            </div>
            <div class="col-6">
                <div class="float-right">
                    <p id="serviceCheckSearchCount"></p>
                </div>
            </div>
        </div>
        <br />
        <div class="row">
            <div class="col-12">
                <div id="serviceCheckSearchInputAlert" class="alert alert-warning" style="display:none;"></div>
                <div id="serviceCheckSearchErrorAlert" class="alert alert-danger" style="display:none;"></div>
            </div>
        </div>
        <hr />
        <div id="serviceCheckSearchStatusAlert" class="alert alert-info" style="display:none;"></div>
        <div id="serviceCheckRecordsGrid"></div>
    </div>
</div>


<script>
const socket = io();

$(document).ready(function() {
    const serviceCheckColumnsDefault = Object.freeze([
        'status',
        'carriageType',
        'carriageFNN',
        'deviceName',
        'serviceType',
        'createdBy',
        'siiamCases',
        'serviceCentralIncidents'
    ]);

    const serviceCheckColumnFields = Object.freeze({
        fnn: { name: "fnn", title: "FNN", type: "text", itemTemplate: function(value, item) {
            // To update: New UI link
            if (item?.startMethod === 'UI') {
                return $("<a>").attr("href", `/serviceCheck/new?id=${item.id}`).attr("target", "blank").text(value);
            } else {
                return $("<a>").attr("href", `/serviceCheck/view/html/${item.id}`).attr("target", "blank").text(value);
            }
        }},
        status: { name: "status", title: "Status", width: "8em", type: "select", textField: "name", valueField: "value",
            items: [
                {
                    name: "",
                    value: null
                },
                {
                    name: "abortedInitial",
                    value: "abortedInitial"
                },
                {
                    name: "completedWithError",
                    value: "completedWithError"
                },
                {
                    name: "completedWithErrorPartial",
                    value: "completedWithErrorPartial"
                },
                {
                    name: "done",
                    value: "done"
                },
                {
                    name: "donePartial",
                    value: "donePartial"
                },
                {
                    name: "error",
                    value: "error"
                },
                {
                    name: "running",
                    value: "running"
                }
            ]
        },
        startMethod: { name: "startMethod", title: "Start Method", type: "select", textField: "name", valueField: "value", width: "6em",
            items: [
                {
                    name: "",
                    value: null
                },
                {
                    name: "API",
                    value: "API"
                },
                {
                    name: "UI",
                    value: "UI"
                },
                {
                    name: "UI Classic",
                    value: "UI Classic"
                }
            ]
        },
        suite: { name: "suite", title: "Suite", type: "select", textField: "name", valueField: "value", width: "6em",
            items: [
                {
                    name: "",
                    value: null
                },
                {
                    name: "standard",
                    value: "standard"
                },
                {
                    name: "andig",
                    value: "andig"
                },
                {
                    name: "ipvoice",
                    value: "ipvoice"
                },
                {
                    name: "outageInfo",
                    value: "outageInfo"
                },
                {
                    name: "wholesale",
                    value: "wholesale"
                }
            ]
        },
        carriageType: { name: "carriageType", title: "Carriage Type", type: "text" },
        carriageFNN: { name: "carriageFNN", title: "Carriage FNN", type: "text" },
        deviceName: { name: "deviceName", title: "Device Name", type: "text", width: "12em" },
        billingFNN: { name: "billingFNN", title: "Billing FNN", type: "text" },
        MDNFNN: { name: "MDNFNN", title: "MDN FNN", type: "text" },
        CIDN: { name: "CIDN", title: "CIDN", type: "text" },
        nbnAccessType: { name: "nbnAccessType", title: "NBN Access Type", type: "text" },
        nbnId: { name: "nbnId", title: "NBN ID", type: "text" },
        createdBy: { name: "createdBy", title: "User", type: "text" },
        serviceType: { name: "serviceType", title: "Service Type", type: "text" },
        siiamCases: { name: "siiamCases", title: "SIIAM Cases", type: "checkbox", sorting: false, width: "6em", itemTemplate: function(value, item) {
            if (value.length == 0) {
                return "None";
            } else {
                let caseList = value.join("<br>");
                let elementId =  "siiam_cases" + item.id;
                let cases = (value.length == 1) ? "case" : "cases";
                let expandElement = `<a>${value.length} open ${cases} </a><a href="#" data-toggle="collapse" data-target="#${elementId}">view</a>\
                                        <div class="collapse" id="${elementId}"><p>${caseList}</p></div>`;
                return expandElement;
            }
        }},
        serviceCentralIncidents: { name: "serviceCentralIncidents", title: "Service Central Incidents", type: "checkbox", sorting: false, width: "8em", itemTemplate: function(value, item) {
            if (value.length == 0) {
                return "None";
            } else {
                let caseList = value.join("<br>");
                let elementId =  "service_central_incidents" + item.id;
                let incidents = (value.length == 1) ? "incident" : "incidents";
                let expandElement = `<a>${value.length} active ${incidents} </a><a href="#" data-toggle="collapse" data-target="#${elementId}">view</a>\
                                        <div class="collapse" id="${elementId}"><p>${caseList}</p></div>`;
                return expandElement;
            }
        }},
        createdOn: { name: "createdOn", title: "Time", type: "text", width: "8em", filtering: false, itemTemplate: function(value) {
            return new Date(value).toLocaleString('en-GB');
        }},
        searchExpressionResult: { name: "searchExpressionResult", title: "Expression Result", type: "text", width: "12em", filtering: false, itemTemplate: function(value) {
            if (typeof value === 'object') {
                return $("<pre>").text('Object (not displayed)');
            } else {
                return value;
            }
        }}
    });

    const downloadFile = (content) => {
        const link = document.createElement('a');
        const file = new Blob([content], { type: 'text/plain' });
        link.href = URL.createObjectURL(file);
        let dateString = new Date().toISOString().replace(/[^0-9]/g, '');
        link.download = `Service_check_advanced_search_${dateString}.csv`;
        link.click();
        URL.revokeObjectURL(link.href);
    };

    let serviceCheckRecords = [];
    let recordCountTotal = 0;

    let columnSelectServiceCheck = localStorage.getItem('columnSelectServiceCheckAdvanceSearch');
    let initialColumns = serviceCheckColumnsDefault;
    let initialFields = [];

    if (columnSelectServiceCheck) {
        try {
            let columns = JSON.parse(columnSelectServiceCheck);
            if (Array.isArray(columns)) {
                initialColumns = columns;
            }
        } catch(error) {
            // Failed to parse column list from local storage, will continue
        }
    }

    initialFields.push(serviceCheckColumnFields.fnn);
    for (let column of initialColumns) {
        if (serviceCheckColumnFields[column]) {
            initialFields.push(serviceCheckColumnFields[column]);
        }
    }
    initialFields.push(serviceCheckColumnFields.createdOn);
    initialFields.push(serviceCheckColumnFields.searchExpressionResult);

    $('#maxNumRecords').select2({
        minimumResultsForSearch: -1,
        width: "resolve"
    });

    $('#columnSelect').select2();
    $('#columnSelect').val(initialColumns);
    $('#columnSelect').trigger('change');

    $('#serviceCheckRecordsGrid').jsGrid({
        height: '60vh',
        width: '100%',
        sorting: false,
        paging: false,
        filtering: true,
        data: serviceCheckRecords,
        fields: initialFields
    });

    let startDateStored = localStorage.getItem('startDateServiceCheckAdvancedSearch');
    let endDateStored = localStorage.getItem('endDateServiceCheckAdvancedSearch');
    let startDateDefault = null;
    let endDateDefault = null;

    if (isNaN(Date.parse(startDateStored))) {
        startDateDefault = new Date();
        startDateDefault.setMonth(startDateDefault.getMonth() - 1);
    } else {
        startDateDefault = new Date(startDateStored);
    }

    if (!isNaN(Date.parse(endDateStored))) {
        endDateDefault = new Date(endDateStored);
    }

    const startDatePicker = flatpickr('#startDate', {
        defaultDate: startDateDefault,
        enableTime: true,
        enableSeconds: true,
        dateFormat: 'd/m/Y h:i:S K',
        position: 'below',
        onChange: function(selectedDates, dateStr, instance) {
            if (Array.isArray(selectedDates) && selectedDates[0] instanceof Date) {
                localStorage.setItem('startDateServiceCheckAdvancedSearch', selectedDates[0].toISOString());
            }
        }
    });

    const endDatePicker = flatpickr('#endDate', {
        defaultDate: endDateDefault,
        enableTime: true,
        enableSeconds: true,
        dateFormat: 'd/m/Y h:i:S K',
        position: 'below',
        onChange: function(selectedDates, dateStr, instance) {
            if (Array.isArray(selectedDates) && selectedDates[0] instanceof Date) {
                localStorage.setItem('endDateServiceCheckAdvancedSearch', selectedDates[0].toISOString());
            }
        }
    });

    const searchExpressionEditor = ace.edit('serviceCheckSearchExpression', {
        mode: 'ace/mode/javascript',
        minLines: 5,
        maxLines: 50
    });

    socket.on('serviceCheckRecordSearch:started', () => {
        setButtonSpinner($('#startSearch'), 'Search', true);
        $('#cancelSearch').prop('disabled', false);

        $('#serviceCheckSearchInputAlert').hide();
        $('#serviceCheckSearchErrorAlert').hide();
        $('#serviceCheckSearchStatusAlert').hide();

        serviceCheckRecords = [];
        $('#serviceCheckRecordsGrid').jsGrid('option', 'data', serviceCheckRecords);

        recordCountIteration = 0;
        recordCountTotal = 0;
    });

    socket.on('serviceCheckRecordSearch:recordCountTotal', (count) => {
        recordCountIteration = 0;
        recordCountTotal = count;
        $('#serviceCheckSearchCount').text('');
    });

    socket.on('serviceCheckRecordSearch:recordCountIteration', (count) => {
        recordCountIteration = count;
        $('#serviceCheckSearchCount').text(`Searching ${recordCountIteration} of ${recordCountTotal}`);
    });

    socket.on('serviceCheckRecordSearch:match', (data) => {
        serviceCheckRecords.push(data);
        $('#serviceCheckRecordsGrid').jsGrid('option', 'data', serviceCheckRecords);
    });

    socket.on('serviceCheckRecordSearch:complete', (message) => {
        setButtonSpinner($('#startSearch'), 'Search', false);
        $('#cancelSearch').prop('disabled', true);

        if (message) {
            $('#serviceCheckSearchStatusAlert').text(message);
            $('#serviceCheckSearchStatusAlert').show();
        }
    });

    socket.on('serviceCheckRecordSearch:inputError', (error) => {
        if (typeof error === 'string') {
            $('#serviceCheckSearchInputAlert').text(error);
            $('#serviceCheckSearchInputAlert').show();
        }
    });

    socket.on('serviceCheckRecordSearch:error', (error) => {
        if (typeof error === 'string') {
            $('#serviceCheckSearchErrorAlert').text(error);
            $('#serviceCheckSearchErrorAlert').show();
        }
    });

    $('#resetDates').click(function() {
        let defaultDate = new Date();
        defaultDate.setMonth(defaultDate.getMonth() - 1);

        startDatePicker.setDate(defaultDate);
        endDatePicker.clear();

        localStorage.removeItem('startDateServiceCheckAdvancedSearch');
        localStorage.removeItem('endDateServiceCheckAdvancedSearch');
    });

    $('#columnSelectReset').click(function() {
        $('#columnSelect').val(serviceCheckColumnsDefault);
        $('#columnSelect').trigger('change');
    });

    $('#columnSelect').on('change', function() {
        let currColumns = $('#columnSelect').val();
        localStorage.setItem('columnSelectServiceCheckAdvanceSearch', JSON.stringify(currColumns));

        let fields = [];
        fields.push(serviceCheckColumnFields.fnn);
        for (let column of currColumns) {
            if (serviceCheckColumnFields[column]) {
                fields.push(serviceCheckColumnFields[column]);
            }
        }
        fields.push(serviceCheckColumnFields.createdOn);
        fields.push(serviceCheckColumnFields.searchExpressionResult);

        $('#serviceCheckRecordsGrid').jsGrid('option', 'fields', fields);
    });

    $('#startSearch').click(function() {
        const filters = {};
        if (startDatePicker && Array.isArray(startDatePicker.selectedDates) && startDatePicker.selectedDates[0] instanceof Date) {
            filters.startDate = startDatePicker.selectedDates[0].toISOString();
        }

        if (endDatePicker && Array.isArray(endDatePicker.selectedDates) && endDatePicker.selectedDates[0] instanceof Date) {
            filters.endDate = endDatePicker.selectedDates[0].toISOString();
        }

        const searchExpression = searchExpressionEditor.getValue();
        const gridFilters = $('#serviceCheckRecordsGrid').jsGrid('getFilter');

        let filterEqualFields = [
            'fnn',
            'status',
            'suite',
            'startMethod',
            'carriageType',
            'carriageFNN',
            'billingFNN',
            'MDNFNN',
            'CIDN',
            'nbnAccessType',
            'nbnId',
            'createdBy',
            'serviceType',
            'siiamCases',
            'serviceCentralIncidents'
        ];

        let filterLikeFields = [
            'deviceName'
        ];


        for (let [filterName, filterValue] of Object.entries(gridFilters)) {
            if (filterValue !== undefined && filterValue !== '') {
                if (filterEqualFields.includes(filterName)) {
                    filters[filterName] = filterValue;
                } else if (filterLikeFields.includes(filterName)) {
                    filters[filterName] = {
                        like: filterValue
                    };
                }
            }
        }

        socket.emit('serviceCheckRecordSearch:start', {
            searchExpression: searchExpression,
            maxRecords: parseInt($('#maxNumRecords').val()),
            filters: filters
        });
    });

    $('#cancelSearch').click(function() {
        socket.emit('serviceCheckRecordSearch:cancel');
    });

    $('#downloadCsv').click(function() {
        const gridData = $('#serviceCheckRecordsGrid').jsGrid('option', 'data');
        if (gridData.length === 0) {
            alert('No results to export');
            return;
        }

        const gridFields = $('#serviceCheckRecordsGrid').jsGrid('option', 'fields');
        const csvParserFields = gridFields.map((field) => {
            return {
                label: field.title,
                value: field.name
            };
        });

        const parser = new json2csv.Parser({
            fields: csvParserFields,
            header: true
        });
        const outputCsv = parser.parse(gridData);

        downloadFile(outputCsv);
    });
});
</script>
</body>
</html>
