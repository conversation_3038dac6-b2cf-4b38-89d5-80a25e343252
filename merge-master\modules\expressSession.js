
import session from 'express-session';
import mongoose from 'mongoose';
import MongoStore from 'connect-mongo';

import '../config/config.js';


// Sets boolean value flag when mongoose connection receives an 'open'
// event, as the express-session middleware should not be called before
// mongoose connecs to the MongoDB instance for the first time (may cause exceptions, see below comments)
var initialConnectionSuccessful = false;
const mongooseConnectionPromise = new Promise((resolve, reject) => {
    // Checks for ready state because if Merge unit tests are run, the database connection is set up before
    // the rest of the application, including the importing of this module, so the 'open'
    // event will never be emitted for mongoose.connection in this instance
    // TODO: Not the most ideal solution, please refactor if there is a better / cleaner solution
    if (mongoose.connection.readyState == 1) {
        initialConnectionSuccessful = true;
        resolve(mongoose.connection.getClient());
    } else {
        mongoose.connection.on('open', function() {
            initialConnectionSuccessful = true;
            resolve(mongoose.connection.getClient());
        });
    }
});


// express-session middleware may throw an exception when the autoremove option for
// MongoStore.create is the default value 'native', to prevent this,
// added autoremove: 'disabled' to the options for MongoStore.create()
// Sometimes the application will also crash when autoremove: 'disabled' is set, cause is currently unknown
// The session() middleware will only be called when the mongoose connection state is ready
// Issue for error: https://github.com/jdesboeufs/connect-mongo/issues/413
let expressSession = session({
    secret: global.gConfig.sessionCookieSecret,
    resave: false,
    saveUninitialized: false,
    cookie: {
        path: '/',
        domain: global.gConfig.cookieDomain,
        secure: global.gConfig.cookieSecure,
        httpOnly: true
    },
    store: MongoStore.create({ client: mongooseConnectionPromise })
});


export default function(req, res, next) {
    if (initialConnectionSuccessful) {
        expressSession(req, res, next);
    } else {
        next(new Error('MongoDB initial connection has not resolved, express session middleware has not been established'));
    }
}
