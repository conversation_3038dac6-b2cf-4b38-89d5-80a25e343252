
function addQCCommand(QC) {
    $("#QCResults").prepend(`
        <div class="text-left border rounded">
            <div class="p-2">
                <a class='expand btn-sm' data-toggle='collapse' data-target='#result-${QC.id}'><span style='color:blue' class='fas fa-minus-square' title='Expand/Collapse'></span></a>
                <span style='color:blue' class='fas fa-link copyToClipboardLink' data-original-title='' title='Click to copy link of this command result with ID ${QC.id}' href="${window.location.origin}/command/view/html/${QC.id}"></span>
                <span id='command-${QC.id}' class='h3'>${QC.command}</span>
                <span id='status-${QC.id}'></span>
            </div>
            <div id='result-${QC.id}' class='text-left border rounded p-2 collapse show'>
            </div>
        </div><br>`);

    $(`#status-${QC.id}`).text(`By: ${QC.createdBy}, On: ${new Date(QC.createdOn).toLocaleString('en-GB')}, Status: Running`);
}


function updateQCResult(QC) {
    let existResultBox = $(`#result-${QC.id}`);
    let quickCommandResultParsed;

    try {
        quickCommandResultParsed = JSON.parse(QC.result);
    } catch(error) {
        // If parsing quick command result as JSON fails, use quick command result directly
        quickCommandResultParsed = QC.result;
    }

    let status = QC.status ? QC.status : 'Unknown';
    $(`#status-${QC.id}`).text(`By: ${QC.createdBy}, On: ${new Date(QC.createdOn).toLocaleString('en-GB')}, Status: ${status}${QC.duration ? ` (in ${QC.duration}s)` : ''}`);

    existResultBox.append(QC.statusCode ? $('<div>').addClass('text-left text-monospace').html(`HTTP status code: <span class="badge badge-light">${QC.statusCode}</span><br>`) : '')
    existResultBox.append(QC.error ? $('<div>').addClass('alert alert-danger').text(QC.error) : '');

    appendObjectTable(existResultBox, quickCommandResultParsed);
    appendObjectTextbox(existResultBox, `command-output-${QC.id}`, quickCommandResultParsed);
}

//Add Debug info
function addDebug(title, data) {
    var debugID = idGen('MCD');
    var tNow = new Date();
    var tTime = tNow.toLocaleTimeString('en-GB');

    var debugBox = $(`#QCDebug`);
    let debugJson = JSON.stringify(data, null, 4);

    debugBox.append(`
        <div class='debugLog'>
            <span id='title-${debugID}' class='h3' data-toggle='collapse' data-target='#debugR-${debugID}'>${title}</span>
            <span id='status-${debugID}' class=''>${tTime}</span>
            <a class='expand btn' data-toggle='collapse' data-target='#debugR-${debugID}'><span style='color:blue' class='fas fa-plus-square' title='Expand/Collapse'/></a>
        </div>
        <div id='debugR-${debugID}' class='text-left text-monospace border rounded collapse in'>
            <pre>${debugJson}</pre>
        </div>`);
}
