import { CollapsablePanel } from '../../../../components/collapsablePanel/CollapsablePanel'
import { DetailField } from '../../../../components/detailsPanel/DetailField'
import { Panel } from '../../../../components/panel/Panel'
import { formatDate } from '../../../../helpers/formatDate'
import { ServiceCentralIncident } from '../../../../infrastructure/models'
import styles from './ServiceCentralIncidentsField.module.scss'

interface ServiceCentralIncidentsFieldProps {
    incidents: ServiceCentralIncident[]
}

export const ServiceCentralIncidentsField = ({
    incidents,
}: ServiceCentralIncidentsFieldProps) => {
    if (incidents.length === 0) {
        return (
            <Panel>
                <DetailField label="Service Central Incidents" value="None" />
            </Panel>
        )
    }

    const sorted = [...incidents].sort((a, b) => {
        const date1 = new Date(a.openedAt).getTime()
        const date2 = new Date(b.openedAt).getTime()
        return date2 - date1
    })

    return (
        <CollapsablePanel
            headerElement={
                <DetailField label="Service Central Incidents" value={''} />
            }
            canOpen={incidents.length > 0}
            itemCount={incidents.length}
        >
            <div className={styles.serviceCentralIncidentsField}>
                <div className={styles.incidentRow}>
                    {sorted.map((incident) => (
                        <div key={incident.id}>
                            <DetailField
                                label={'Incident'}
                                value={incident.id}
                                inline={true}
                            />
                            <DetailField
                                label={'Opened On'}
                                value={formatDate(incident.openedAt)}
                                inline={true}
                            />
                            <DetailField
                                label={'State'}
                                value={incident.state}
                                inline={true}
                            />
                        </div>
                    ))}
                </div>
            </div>
        </CollapsablePanel>
    )
}
