
import mongoose from 'mongoose';
import chai from 'chai';
import chaiHttp from 'chai-http';
import chaiLike from 'chai-like';
import chaiDateTime from 'chai-datetime';
import sinon from 'sinon';
import dedent from 'dedent';
import superagentDefaults from 'superagent-defaults';
import supertest from 'supertest';
import moment from 'moment';
import 'moment-timezone';
import _ from 'lodash';
import assert from 'assert';

var app;
var request;
var mockMySQL;
import config from '../config.js';
import ServiceCheckModel from '../../db/model/serviceCheck.js';
import helpers from '../helpers.js';
import { UNIT_TEST_ALL_ACCESS_USERNAME, UNIT_TEST_USERS, UNIT_TEST_API_SERVICE_CHECK_USERS } from '../users.js';

const should = chai.should();

chaiLike.extend(helpers.dateFieldCheck);

chai.use(chaiLike);
chai.use(chaiHttp);
chai.use(chaiDateTime);

const TIME_ZONE = 'Australia/Victoria';


describe('Statistics', () => {
    // Setup and connect to mongodb memory server, start merge app instance
    // Set request variable with a configured supertest agent with test JWT and app instance
    before(async function() {
        global.gConfig = _.cloneDeep(config.default);
        app = (await import('../../app.js')).default;
        request = superagentDefaults(supertest(app));

        let token = await helpers.authenticateApi(request, UNIT_TEST_ALL_ACCESS_USERNAME);
        request.set('Authorization', `Bearer ${token}`);
    });

    // Drop all contents of database before each test
    beforeEach(async function() {
        global.gConfig = _.cloneDeep(config.default);
        // Uses deleteMany instead of mongoose.connection.db.dropDatabase() because
        // custom indexes appear to be deleted if dropDatabase() is used
        for (let collectionName in mongoose.connection.collections) {
            await mongoose.connection.collections[collectionName].deleteMany({});
        }

        const pool = (await import('../../modules/hangarDatabase.js')).default;
        mockMySQL = sinon.mock(pool);
    });

    afterEach(async function() {
        mockMySQL.restore();
    });

    describe('Service check count grouped by users', () => {
        it('Unauthenticated request', async() => {
            let res = await request.get('/stats/scsUsers').unset('Authorization');
            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                let token = await helpers.authenticateApi(request, username);
                let res = await request.get('/stats/scsUsers').set('Authorization', `Bearer ${token}`);

                switch(username) {
                    case 'apiAccess':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('No service checks', (done) => {
            request.get('/stats/scsUsers')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);
                chai.expect(res.body.results).to.eql([]);
                done();
            });
        });

        it('One service check in last week no parameters', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            var serviceCheck = new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            });

            serviceCheck.save().then(() => {
                request.get('/stats/scsUsers')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('One service check outside of last week no parameters', (done) => {
            let currDate = new Date();
            currDate.setDate(currDate.getDate() - 8);

            var serviceCheck = new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            });

            serviceCheck.save().then(() => {
                request.get('/stats/scsUsers?last=1w')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);
                    chai.expect(res.body.results).to.eql([]);

                    done();
                });
            });
        });

        it('One service check in last day', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            var serviceCheck = new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            });

            serviceCheck.save().then(() => {
                request.get('/stats/scsUsers?last=1d')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('One service check outside of last day', (done) => {
            let currDate = new Date();
            currDate.setDate(currDate.getDate() - 2);

            var serviceCheck = new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            });

            serviceCheck.save().then(() => {
                request.get('/stats/scsUsers?last=1d')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);
                    chai.expect(res.body.results).to.eql([]);

                    done();
                });
            });
        });

        it('One service check outside of last week', (done) => {
            let currDate = new Date();
            currDate.setDate(currDate.getDate() - 8);

            var serviceCheck = new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            });

            serviceCheck.save().then(() => {
                request.get('/stats/scsUsers?last=1w')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);
                    chai.expect(res.body.results).to.eql([]);

                    done();
                });
            });
        });

        it('One service check within last month', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            currDate.setDate(currDate.getDate() - 25);
            expectedDate.setMonth(expectedDate.getMonth() - 1);

            var serviceCheck = new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            });

            serviceCheck.save().then(() => {
                request.get('/stats/scsUsers?last=1m')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('One service check outside of last month', (done) => {
            let currDate = new Date();
            currDate.setDate(currDate.getDate() - 32);

            var serviceCheck = new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            });

            serviceCheck.save().then(() => {
                request.get('/stats/scsUsers?last=1m')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);
                    chai.expect(res.body.results).to.eql([]);

                    done();
                });
            });
        });

        it('One service check within last year', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            currDate.setDate(currDate.getDate() - 364);
            expectedDate.setFullYear(expectedDate.getFullYear() - 1);

            var serviceCheck = new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            });

            serviceCheck.save().then(() => {
                request.get('/stats/scsUsers?last=1y')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('One service check outside of last year', (done) => {
            let currDate = new Date();
            currDate.setDate(currDate.getDate() - 366);

            var serviceCheck = new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            });

            serviceCheck.save().then(() => {
                request.get('/stats/scsUsers?last=1y')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);
                    chai.expect(res.body.results).to.eql([]);

                    done();
                });
            });
        });

        it('One service check within last 3 days', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            currDate.setDate(currDate.getDate() - 3);
            currDate.setMinutes(currDate.getMinutes() + 1);

            expectedDate.setDate(expectedDate.getDate() - 3);

            var serviceCheck = new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            });

            serviceCheck.save().then(() => {
                request.get('/stats/scsUsers?last=3d')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('One service check outside of last 3 days', (done) => {
            let currDate = new Date();
            currDate.setDate(currDate.getDate() - 3);
            currDate.setMinutes(currDate.getMinutes() - 1);

            var serviceCheck = new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            });

            serviceCheck.save().then(() => {
                request.get('/stats/scsUsers?last=3d')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);
                    chai.expect(res.body.results).to.eql([]);

                    done();
                });
            });
        });

        it('Two service checks in last week', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: expectedDate,
                        count: 2
                    }]);

                    done();
                });
            });
        });

        it('Two service checks in last week with separate users', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: expectedDate,
                        count: 1
                    },
                    {
                        user: 'd222222',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Service checks sorted by count (descending order) test 1', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd333333',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd222222',
                        start: expectedDate,
                        count: 4
                    },
                    {
                        user: 'd111111',
                        start: expectedDate,
                        count: 2
                    },
                    {
                        user: 'd333333',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Service checks sorted by count (descending order) test 2', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N8355722R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N685143R',
                createdBy: 'd333333',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2837541R',
                createdBy: 'd444444',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N812384R',
                createdBy: 'd555555',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'Y00000078352N',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'Y00000034352N',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'Y00000076312N',
                createdBy: 'd333333',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N8162384R',
                createdBy: 'd555555',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N7125125R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'Y00000074141N',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N3125884R',
                createdBy: 'd555555',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'Y00000079343N',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1012387R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N6827413R',
                createdBy: 'd555555',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2735481R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'Y00000079851N',
                createdBy: 'd555555',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N6358331R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N9345728R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5329381R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd222222',
                        start: expectedDate,
                        count: 8
                    },
                    {
                        user: 'd555555',
                        start: expectedDate,
                        count: 5
                    },
                    {
                        user: 'd111111',
                        start: expectedDate,
                        count: 4
                    },
                    {
                        user: 'd333333',
                        start: expectedDate,
                        count: 2
                    },
                    {
                        user: 'd444444',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Service checks sorted by count (descending order) and user (ascending order) test 1', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd333333',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd333333',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd333333',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd333333',
                        start: expectedDate,
                        count: 3
                    },
                    {
                        user: 'd111111',
                        start: expectedDate,
                        count: 2
                    },
                    {
                        user: 'd222222',
                        start: expectedDate,
                        count: 2
                    }]);

                    done();
                });
            });
        });

        it('Service checks sorted by count (descending order) and user (ascending order) test 2', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N8355722R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N685143R',
                createdBy: 'd333333',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2837541R',
                createdBy: 'd444444',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N812384R',
                createdBy: 'd555555',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'Y00000078352N',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'Y00000034352N',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'Y00000076312N',
                createdBy: 'd333333',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2493341R',
                createdBy: 'd444444',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N8162384R',
                createdBy: 'd555555',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N7125125R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'Y00000074141N',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N3125884R',
                createdBy: 'd555555',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'Y00000079343N',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1012387R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N6827413R',
                createdBy: 'd555555',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'Y00000058902N',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2735481R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'Y00000079851N',
                createdBy: 'd555555',
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([{
                            user: 'd111111',
                            start: expectedDate,
                            count: 5
                        },
                        {
                            user: 'd222222',
                            start: expectedDate,
                            count: 5
                        },
                        {
                            user: 'd555555',
                            start: expectedDate,
                            count: 5
                        },
                        {
                            user: 'd333333',
                            start: expectedDate,
                            count: 2
                        },
                        {
                            user: 'd444444',
                            start: expectedDate,
                            count: 2
                        }]);

                        done();
                });
            });
        });

        it('Service checks in day interval test 1', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date('2020-10-10T04:00:00.000Z')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?last=20y&interval=d')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: new Date('2020-10-09T13:00:00.000Z'),
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Service checks in day interval test 2', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N3851235R',
                createdBy: 'd123456',
                createdOn: new Date('2020-05-20T00:00:00.000Z')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?last=20y&interval=d')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd123456',
                        start: new Date('2020-05-19T14:00:00.000Z'),
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Service checks in day interval test 3', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N837521R',
                createdBy: 'd654321',
                createdOn: new Date('2020-05-20T23:59:59.999Z')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?last=20y&interval=d')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd654321',
                        start: new Date('2020-05-20T14:00:00.000Z'),
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Service checks in week interval test 1', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date('2020-08-28T11:22:00.000Z')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?last=20y&interval=w')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: new Date('2020-08-23T14:00:00.000Z'),
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Service checks in week interval test 2', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N8735483R',
                createdBy: 'd888111',
                createdOn: new Date('2020-04-26T14:00:00.001Z')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?last=20y&interval=w')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd888111',
                        start: new Date('2020-04-26T14:00:00.000Z'),
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Service checks in week interval test 3', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N3475683R',
                createdBy: 'd578713',
                createdOn: new Date('2020-06-27T13:59:59.999Z')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?last=20y&interval=w')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd578713',
                        start: new Date('2020-06-21T14:00:00.000Z'),
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Service checks in month interval test 1', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date('2020-03-16T13:37:00.000Z')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?last=20y&interval=m')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: new Date('2020-02-29T13:00:00.000Z'),
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Service checks in month interval test 2', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N8735483R',
                createdBy: 'd915123',
                createdOn: new Date('2018-08-31T14:00:00.001Z')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?last=20y&interval=m')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd915123',
                        start: new Date('2018-08-31T14:00:00.000Z'),
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Service checks in month interval test 3', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N9123512R',
                createdBy: 'd741122',
                createdOn: new Date('2019-07-31T13:59:59.999Z')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?last=20y&interval=m')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd741122',
                        start: new Date('2019-06-30T14:00:00.000Z'),
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Service checks in month interval test 4', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N9123512R',
                createdBy: 'd111111',
                createdOn: new Date('2019-07-31T23:59:59.999+10:00')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N9123512R',
                createdBy: 'd111111',
                createdOn: new Date('2019-08-01T00:00:00.000+10:00')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?last=20y&interval=m')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: new Date('2019-07-31T14:00:00.000Z'),
                        count: 1
                    },
                    {
                        user: 'd111111',
                        start: new Date('2019-06-30T14:00:00.000Z'),
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Service checks in month interval test 5', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N9123512R',
                createdBy: 'd111111',
                createdOn: new Date('2019-04-28T13:50:50Z')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N9123512R',
                createdBy: 'd111111',
                createdOn: new Date('2019-04-01T13:50:50Z')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?last=20y&interval=m')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: new Date('2019-03-31T13:00:00.000Z'),
                        count: 2
                    }]);

                    done();
                });
            });
        });

        it('Service checks in year interval test 1', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date('2020-09-29T17:57:22.323Z')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?last=20y&interval=y')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: new Date('2019-12-31T13:00:00.000Z'),
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Service checks in year interval test 2', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1357832R',
                createdBy: 'd723511',
                createdOn: new Date('2015-12-31T13:00:00.000Z')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?last=20y&interval=y')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd723511',
                        start: new Date('2015-12-31T13:00:00.000Z'),
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Service checks in year interval test 3', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N753221R',
                createdBy: 'd662266',
                createdOn: new Date('2017-12-31T12:59:59.999Z')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?last=20y&interval=y')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd662266',
                        start: new Date('2016-12-31T13:00:00.000Z'),
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Service checks in year interval test 4', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N753221R',
                createdBy: 'd662266',
                createdOn: new Date('2017-12-31T23:59:59.999+11:00')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N753221R',
                createdBy: 'd662266',
                createdOn: new Date('2018-01-01T00:00:00.001+11:00')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?last=20y&interval=y')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd662266',
                        start: new Date('2017-12-31T13:00:00.000Z'),
                        count: 1
                    },{
                        user: 'd662266',
                        start: new Date('2016-12-31T13:00:00.000Z'),
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Service checks in year interval test 5', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N7532281R',
                createdBy: 'd111111',
                createdOn: new Date('2021-07-26T13:31:31Z')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N8123572R',
                createdBy: 'd111111',
                createdOn: new Date('2021-01-01T00:00:01Z')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1893751R',
                createdBy: 'd111111',
                createdOn: new Date('2020-01-01T12:59:59Z')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?last=20y&interval=y')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: new Date('2020-12-31T13:00:00.000Z'),
                        count: 2
                    },{
                        user: 'd111111',
                        start: new Date('2019-12-31T13:00:00.000Z'),
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Service checks in hour interval test 1', async() => {
            let date1 = new Date();
            let date1Group = moment(date1).tz(TIME_ZONE).startOf('hour').toDate();
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: date1
            }).save());

            await Promise.all(promises);

            let res = await request.get('/stats/scsUsers?last=1m&interval=h');
            res.should.have.status(200);

            chai.expect(res.body.results).to.be.like([{
                user: 'd111111',
                start: date1Group,
                count: 1
            }]);
        });

        it('Service checks in hour interval test 2', async() => {
            let date1 = new Date();
            date1.setHours(date1.getHours() - 156);
            let date1Group = moment(date1).tz(TIME_ZONE).startOf('hour').toDate();
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: date1
            }).save());

            await Promise.all(promises);

            let res = await request.get('/stats/scsUsers?last=1m&interval=h');
            res.should.have.status(200);

            chai.expect(res.body.results).to.be.like([{
                user: 'd111111',
                start: date1Group,
                count: 1
            }]);
        });

        it('Service checks in hour interval test 3', async() => {
            let date1 = new Date();
            let date2 = new Date();
            let date3 = new Date();
            let date4 = new Date();
            date1.setHours(date1.getHours() - 238);
            date2.setHours(date2.getHours() - 15);
            date3.setHours(date3.getHours() - 15);
            date4.setHours(date4.getHours() - 480);
            let date1Group = moment(date1).tz(TIME_ZONE).startOf('hour').toDate();
            let date2Group = moment(date2).tz(TIME_ZONE).startOf('hour').toDate();
            let date4Group = moment(date4).tz(TIME_ZONE).startOf('hour').toDate();
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: date1
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: date2
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: date3
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: date4
            }).save());

            await Promise.all(promises);

            let res = await request.get('/stats/scsUsers?last=1m&interval=h');
            res.should.have.status(200);

            chai.expect(res.body.results).to.be.like([{
                user: 'd111111',
                start: date2Group,
                count: 2
            },
            {
                user: 'd111111',
                start: date1Group,
                count: 1
            },
            {
                user: 'd111111',
                start: date4Group,
                count: 1
            }]);
        });

        it('Two service checks separated by day interval test 1', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date('2020-12-08T02:00:00.000Z')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                createdOn: new Date('2020-12-07T12:00:00.000Z')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?last=20y&interval=d')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: new Date('2020-12-07T13:00:00.000Z'),
                        count: 1
                    },
                    {
                        user: 'd111111',
                        start: new Date('2020-12-06T13:00:00.000Z'),
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Two service checks separated by day interval test 2', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date('2020-06-03T23:59:59.999+10:00')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                createdOn: new Date('2020-06-04T00:00:00.001+10:00')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?last=20y&interval=d')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: new Date('2020-06-03T14:00:00.000Z'),
                        count: 1
                    },
                    {
                        user: 'd111111',
                        start: new Date('2020-06-02T14:00:00.000Z'),
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Multiple service checks and users separated by day interval', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date('2020-12-08T00:00:00.001Z')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd222222',
                createdOn: new Date('2020-12-07T23:59:59.999Z')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N8735482R',
                createdBy: 'd111111',
                createdOn: new Date('2020-12-08T08:50:50.000Z')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N7623572R',
                createdBy: 'd222222',
                createdOn: new Date('2020-12-07T00:00:00.001Z')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N9823752R',
                createdBy: 'd111111',
                createdOn: new Date('2020-12-07T12:59:59.999Z')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd222222',
                createdOn: new Date('2020-12-07T08:16:24.592Z')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?last=20y&interval=d')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: new Date('2020-12-07T13:00:00.000Z'),
                        count: 2
                    },
                    {
                        user: 'd222222',
                        start: new Date('2020-12-07T13:00:00.000Z'),
                        count: 1
                    },
                    {
                        user: 'd222222',
                        start: new Date('2020-12-06T13:00:00.000Z'),
                        count: 2
                    },
                    {
                        user: 'd111111',
                        start: new Date('2020-12-06T13:00:00.000Z'),
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Two service checks separated by week interval test 1', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date('2020-10-08T12:33:16.000Z')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                createdOn: new Date('2020-05-12T12:33:16.000Z')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?last=20y&interval=w')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: new Date('2020-10-04T13:00:00.000Z'),
                        count: 1
                    },
                    {
                        user: 'd111111',
                        start: new Date('2020-05-10T14:00:00.000Z'),
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Two service checks separated by week interval test 2', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date('2020-10-18T23:59:59.999+11:00')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                createdOn: new Date('2020-10-19T00:00:00.001+11:00')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?last=20y&interval=w')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: new Date('2020-10-18T13:00:00.000Z'),
                        count: 1
                    },
                    {
                        user: 'd111111',
                        start: new Date('2020-10-11T13:00:00.000Z'),
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Two service checks separated by week interval test 3', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date('2021-01-01T11:20:28.382+11:00')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                createdOn: new Date('2021-01-03T03:22:46.823+11:00')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?last=20y&interval=w')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: new Date('2020-12-27T13:00:00.000Z'),
                        count: 2
                    }]);

                    done();
                });
            });
        });

        it('Multiple service checks and users separated by week interval', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date('2020-06-16T15:00:00.000Z')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N8937252R',
                createdBy: 'd111111',
                createdOn: new Date('2020-06-19T23:20:00.000Z')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N8735482R',
                createdBy: 'd111111',
                createdOn: new Date('2020-10-04T16:50:50.000Z')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5782383R',
                createdBy: 'd111111',
                createdOn: new Date('2020-04-23T18:23:33.000Z')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N8731511R',
                createdBy: 'd111111',
                createdOn: new Date('2020-11-31T02:04:06.000Z')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1263121R',
                createdBy: 'd222222',
                createdOn: new Date('2020-06-16T23:01:32.000Z')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N666666R',
                createdBy: 'd222222',
                createdOn: new Date('2020-06-17T21:42:42.999Z')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N9875251R',
                createdBy: 'd222222',
                createdOn: new Date('2020-12-05T16:50:50.000Z')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N6721231R',
                createdBy: 'd222222',
                createdOn: new Date('2020-12-03T08:16:24.592Z')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N4723815R',
                createdBy: 'd333333',
                createdOn: new Date('2020-12-04T13:11:58.000Z')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd333333',
                createdOn: new Date('2020-11-20T00:00:00.000Z')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd333333',
                createdOn: new Date('2019-12-26T00:00:01.000Z')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?last=20y&interval=w')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([{
                            user: 'd222222',
                            start: new Date('2020-11-29T13:00:00.000Z'),
                            count: 2
                        },
                        {
                            user: 'd111111',
                            start: new Date('2020-11-29T13:00:00.000Z'),
                            count: 1
                        },
                        {
                            user: 'd333333',
                            start: new Date('2020-11-29T13:00:00.000Z'),
                            count: 1
                        },
                        {
                            user: 'd333333',
                            start: new Date('2020-11-15T13:00:00.000Z'),
                            count: 1
                        },
                        {
                            user: 'd111111',
                            start: new Date('2020-10-04T13:00:00.000Z'),
                            count: 1
                        },
                        {
                            user: 'd111111',
                            start: new Date('2020-06-14T14:00:00.000Z'),
                            count: 2
                        },
                        {
                            user: 'd222222',
                            start: new Date('2020-06-14T14:00:00.000Z'),
                            count: 2
                        },
                        {
                            user: 'd111111',
                            start: new Date('2020-04-19T14:00:00.000Z'),
                            count: 1
                        },
                        {
                            user: 'd333333',
                            start: new Date('2019-12-22T13:00:00.000Z'),
                            count: 1
                        }]);

                        done();
                });
            });
        });

        it('One service check with one matching carriageType filter', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'ADSL',
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?carriageType=ADSL')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('One service check with no matching carriageType filter', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'ADSL',
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?carriageType=NBN')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([]);

                    done();
                });
            });
        });

        it('One service check with multiple carriageType filters, one matching', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'ADSL',
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?carriageType=NBN&carriageType=ADSL')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('One service check with multiple carriageType filters, none matching', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'ADSL',
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?carriageType=VOIP&carriageType=VDSL&carriageType=BSDL')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([]);

                    done();
                });
            });
        });

        it('Multiple service checks with one carriageType filter', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'ADSL',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                carriageType: 'MOBILE',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                carriageType: 'MOBILE',
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?carriageType=MOBILE')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: expectedDate,
                        count: 2
                    }]);

                    done();
                });
            });
        });

        it('Multiple service checks with multiple carriageType filters', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'ADSL',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'ADSL',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'ADSL',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                carriageType: 'MOBILE',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                carriageType: 'MOBILE',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                carriageType: 'MOBILE',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                carriageType: 'MOBILE',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                carriageType: 'MOBILE',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd111111',
                carriageType: 'INTERNATIONAL',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd111111',
                carriageType: 'INTERNATIONAL',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2222222R',
                createdBy: 'd111111',
                carriageType: 'NBN',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2222222R',
                createdBy: 'd111111',
                carriageType: 'NBN',
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?carriageType=ADSL&carriageType=MOBILE')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: expectedDate,
                        count: 8
                    }]);

                    done();
                });
            });
        });

        it('One service check with one matching user filter', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?user=d111111')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('One service check with no matching user filter', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?user=d222222')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([]);

                    done();
                });
            });
        });

        it('One service check with multiple user filters, one matching', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?user=d111111&user=d222222')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('One service check with multiple user filters, none matching', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd123456',
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?user=d111111&user=d222222&user=d333333')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([]);

                    done();
                });
            });
        });

        it('Multiple service checks with one user filter', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd222222',
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?user=d111111')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: expectedDate,
                        count: 2
                    }]);

                    done();
                });
            });
        });

        it('Multiple service checks with multiple user filters', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd222222',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd222222',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2222222R',
                createdBy: 'd222222',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2222222R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?user=d111111&user=d222222')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: expectedDate,
                        count: 9
                    },
                    {
                        user: 'd222222',
                        start: expectedDate,
                        count: 3
                    }]);

                    done();
                });
            });
        });

        it('One service check with one matching FNN filter', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?fnn=N1234567R')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('One service check with no matching FNN filter', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?fnn=N1111111R')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([]);

                    done();
                });
            });
        });

        it('One service check with multiple FNN filters, one matching', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?fnn=N1234567R&fnn=N1111111R')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('One service check with multiple FNN filters, none matching', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N9876543R',
                createdBy: 'd123456',
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?fnn=N1234567R&fnn=N1111111R&fnn=N2222222R')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([]);

                    done();
                });
            });
        });

        it('One service check with one matching FNN filter phone number local format', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: '0411111111',
                phoneNumber: '+61411111111',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?fnn=0411111111')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('One service check with one matching FNN filter phone number E.164', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: '0411111111',
                phoneNumber: '+61411111111',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?fnn=+61411111111')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Multiple service checks with one FNN filter', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd222222',
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?fnn=N5555555R')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: expectedDate,
                        count: 1
                    },
                    {
                        user: 'd222222',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Multiple service checks with multiple FNN filters', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd222222',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd222222',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2222222R',
                createdBy: 'd222222',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?fnn=N1234567R&fnn=N1111111R')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: expectedDate,
                        count: 7
                    },
                    {
                        user: 'd222222',
                        start: expectedDate,
                        count: 2
                    }]);

                    done();
                });
            });
        });

        it('Multiple service checks with multiple FNN filters with phone numbers', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                phoneNumber: null,
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                phoneNumber: null,
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                phoneNumber: null,
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                phoneNumber: null,
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                phoneNumber: null,
                createdBy: 'd222222',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                phoneNumber: null,
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                phoneNumber: null,
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                phoneNumber: null,
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                phoneNumber: null,
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                phoneNumber: null,
                createdBy: 'd222222',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2222222R',
                phoneNumber: null,
                createdBy: 'd222222',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                phoneNumber: null,
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                phoneNumber: '+61422334455',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                phoneNumber: '+61422334455',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                phoneNumber: '+61422334455',
                createdBy: 'd222222',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                phoneNumber: '+61422334455',
                createdBy: 'd222222',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                phoneNumber: '+61234567890',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                phoneNumber: '+61234567890',
                createdBy: 'd222222',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                phoneNumber: '+61388881111',
                createdBy: 'd222222',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                phoneNumber: '+61388881111',
                createdBy: 'd222222',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                phoneNumber: '+61388881111',
                createdBy: 'd222222',
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?fnn=N1234567R&fnn=N1111111R&fnn=61422334455&fnn=0234567890')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: expectedDate,
                        count: 10
                    },
                    {
                        user: 'd222222',
                        start: expectedDate,
                        count: 5
                    }]);

                    done();
                });
            });
        });

        it('Multiple service checks with multiple FNN filters all phone numbers', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                phoneNumber: null,
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                phoneNumber: null,
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                phoneNumber: null,
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                phoneNumber: null,
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                phoneNumber: null,
                createdBy: 'd222222',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                phoneNumber: null,
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                phoneNumber: null,
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                phoneNumber: null,
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                phoneNumber: null,
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                phoneNumber: null,
                createdBy: 'd222222',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2222222R',
                phoneNumber: null,
                createdBy: 'd222222',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                phoneNumber: null,
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                phoneNumber: '+61422334455',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                phoneNumber: '+61422334455',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                phoneNumber: '+61422334455',
                createdBy: 'd222222',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                phoneNumber: '+61422334455',
                createdBy: 'd222222',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                phoneNumber: '+61234567890',
                createdBy: 'd111111',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                phoneNumber: '+61234567890',
                createdBy: 'd222222',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                phoneNumber: '+61388881111',
                createdBy: 'd222222',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                phoneNumber: '+61388881111',
                createdBy: 'd222222',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                phoneNumber: '+61388881111',
                createdBy: 'd222222',
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?fnn=61422334455&fnn=0388881111')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd222222',
                        start: expectedDate,
                        count: 5
                    },
                    {
                        user: 'd111111',
                        start: expectedDate,
                        count: 2
                    }]);

                    done();
                });
            });
        });

        it('Multiple service checks with multiple carriageType filters and multiple users', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'ADSL',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'ADSL',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'ADSL',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd222222',
                carriageType: 'ADSL',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                carriageType: 'MOBILE',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                carriageType: 'MOBILE',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                carriageType: 'MOBILE',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                carriageType: 'MOBILE',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                carriageType: 'MOBILE',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd333333',
                carriageType: 'MOBILE',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd333333',
                carriageType: 'MOBILE',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd111111',
                carriageType: 'INTERNATIONAL',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd111111',
                carriageType: 'INTERNATIONAL',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd222222',
                carriageType: 'INTERNATIONAL',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd222222',
                carriageType: 'INTERNATIONAL',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2222222R',
                createdBy: 'd111111',
                carriageType: 'NBN',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2222222R',
                createdBy: 'd111111',
                carriageType: 'NBN',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2222222R',
                createdBy: 'd222222',
                carriageType: 'NBN',
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?carriageType=ADSL&carriageType=NBN')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: expectedDate,
                        count: 5
                    },
                    {
                        user: 'd222222',
                        start: expectedDate,
                        count: 2
                    }]);

                    done();
                });
            });
        });

        it('Multiple service checks with multiple carriageType filters and multiple users and intervals', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd111111',
                carriageType: 'NBN',
                createdOn: new Date('2020-10-10T04:00:00.000Z')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2222222R',
                createdBy: 'd222222',
                carriageType: 'INTERNATIONAL',
                createdOn: new Date('2020-10-10T04:00:00.000Z')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N3333333R',
                createdBy: 'd333333',
                carriageType: 'MOBILE',
                createdOn: new Date('2020-10-10T04:00:00.000Z')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd333333',
                carriageType: 'NBN',
                createdOn: new Date('2020-10-05T04:00:00.000Z')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2222222R',
                createdBy: 'd111111',
                carriageType: 'INTERNATIONAL',
                createdOn: new Date('2020-10-05T04:00:00.000Z')
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N3333333R',
                createdBy: 'd222222',
                carriageType: 'MOBILE',
                createdOn: new Date('2020-10-05T04:00:00.000Z')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?last=20y&interval=d&carriageType=NBN&carriageType=INTERNATIONAL')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: new Date('2020-10-09T13:00:00.000Z'),
                        count: 1
                    },
                    {
                        user: 'd222222',
                        start: new Date('2020-10-09T13:00:00.000Z'),
                        count: 1
                    },
                    {
                        user: 'd111111',
                        start: new Date('2020-10-04T13:00:00.000Z'),
                        count: 1
                    },
                    {
                        user: 'd333333',
                        start: new Date('2020-10-04T13:00:00.000Z'),
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('One service check with matching rule', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?rule=MDR001')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([{
                            user: 'd111111',
                            start: expectedDate,
                            count: 1
                        }]);

                        done();
                });
            });
        });

        it('One service check with matching rule, status not done', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                rulesData: {
                    MDR001: {
                        status: 'preCondReject'
                    }
                },
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?rule=MDR001')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([{
                            user: 'd111111',
                            start: expectedDate,
                            count: 1
                        }]);

                        done();
                });
            });
        });

        it('One service check with non matching rule', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                rulesData: {
                    MDR000: {
                        status: 'done'
                    },
                    MDR002: {
                        status: 'preCondReject'
                    }
                },
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?rule=MDR001')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([]);

                        done();
                });
            });
        });

        it('One service check with matching rule, matching status', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?rule=MDR001&ruleStatus=done')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([{
                            user: 'd111111',
                            start: expectedDate,
                            count: 1
                        }]);

                        done();
                });
            });
        });

        it('One service check with matching rule, non matching status', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                rulesData: {
                    MDR001: {
                        status: 'preCondReject'
                    }
                },
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?rule=MDR001&ruleStatus=done')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([]);

                        done();
                });
            });
        });

        it('One service check with matching source', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                sourcesMetadata: {
                    MAGPIE: {}
                },
                sourcesMetadata: {
                    MAGPIE: {
                        status: 'Collected'
                    }
                },
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?source=MAGPIE')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([{
                            user: 'd111111',
                            start: expectedDate,
                            count: 1
                        }]);

                        done();
                });
            });
        });

        it('One service check with matching source, status not Collected', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                sourcesMetadata: {
                    MAGPIE: {
                        status: 'error'
                    }
                },
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?source=MAGPIE')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([{
                            user: 'd111111',
                            start: expectedDate,
                            count: 1
                        }]);

                        done();
                });
            });
        });

        it('One service check with non matching source', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                sourcesMetadata: {
                    MAGPIE: {
                        status: 'Collected'
                    },
                    ODIN: {
                        status: 'Collected'
                    }
                },
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?source=TRADPE')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([]);

                        done();
                });
            });
        });

        it('Multiple service checks with no base rule filter and rule status filter', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                rulesData: {
                    MDR001: {
                        status: 'preConReject'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                rulesData: {
                    MDR002: {
                        status: 'done'
                    }
                },
                createdOn: new Date()
            }).save());

            // In this test case, both service checks are counted as filters on rules only work if
            // there is an existing filter on the rule name
            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?ruleStatus=done')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([{
                            user: 'd111111',
                            start: expectedDate,
                            count: 2
                        }]);

                        done();
                });
            });
        });

        it('Multiple service checks with different status, result, trigger sources and filters', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                rulesData: {
                    MDR001: {
                        result: 'OK',
                        status: 'done',
                        TrgSource: 'Source1'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                rulesData: {
                    MDR001: {
                        result: 'Error',
                        status: 'done',
                        TrgSource: 'Source2'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                rulesData: {
                    MDR001: {
                        result: 'OK',
                        status: 'preConReject',
                        TrgSource: 'Source2'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                rulesData: {
                    MDR001: {
                        result: 'OK',
                        status: 'done',
                        TrgSource: 'Source2'
                    }
                },
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?rule=MDR001&ruleTriggerSource=Source1&ruleResult=OK&ruleStatus=done')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([{
                            user: 'd111111',
                            start: expectedDate,
                            count: 1
                        }]);

                        done();
                });
            });
        });

        it('Service check with filters on fields that are not present', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                rulesData: {
                    MDR001: {
                    }
                },
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?rule=MDR001&ruleResult=OK&ruleStatus=done&ruleTriggerSource=Source1')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([]);

                        done();
                });
            });
        });

        it('Multiple service checks with filters for multiple rule results', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                rulesData: {
                    MDR001: {
                        result: 'OK'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                rulesData: {
                    MDR001: {
                        result: 'Error'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                rulesData: {
                    MDR001: {
                        result: 'OK'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                rulesData: {
                    MDR001: {
                        result: 'Actionable'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                rulesData: {
                    MDR001: {
                        result: 'Warning'
                    }
                },
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?rule=MDR001&ruleResult=OK&ruleResult=Actionable')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([{
                            user: 'd111111',
                            start: expectedDate,
                            count: 3
                        }]);

                        done();
                });
            });
        });

        it('Multiple service checks with filters for multiple source statuses', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                sourcesMetadata: {
                    Source2: {
                        status: 'Collected'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                sourcesMetadata: {
                    Source1: {
                        status: 'error'
                    }
                },
                createdOn: new Date()
            }).save());


            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                sourcesMetadata: {
                    Source1: {
                        status: 'running'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                rulesData: {},
                sourcesMetadata: {
                    Source2: {
                        status: 'Collected'
                    }
                },
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?source=Source1&sourceStatus=Collected&sourceStatus=error')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([{
                            user: 'd111111',
                            start: expectedDate,
                            count: 2
                        }]);

                        done();
                });
            });
        });

        it('Multiple service checks with no base source filter and source status filter', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                rulesData: {},
                sourcesMetadata: {
                    Source1: {
                        status: 'error'
                    }
                },
                createdOn: new Date()
            }).save());


            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                rulesData: {},
                sourcesMetadata: {
                    Source2: {
                        status: 'running'
                    }
                },
                createdOn: new Date()
            }).save());

            // In this test case, both service checks are counted as filters on sources only work if
            // there is an existing filter on the source name
            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?sourceStatus=Collected')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([{
                            user: 'd111111',
                            start: expectedDate,
                            count: 2
                        }]);

                        done();
                });
            });
        });

        it('Multiple service checks with multiple carriageType filters, rule filters, source filters and multiple users, none matching', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'ADSL',
                rulesData: {},
                sourcesMetadata: {
                    MAGPIE: {
                        status: 'Completed'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'ADSL',
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'ADSL',
                rulesData: {
                    MDR004: {
                        status: 'done'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd222222',
                carriageType: 'ADSL',
                rulesData: {},
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                carriageType: 'MOBILE',
                rulesData: {
                    MDR004: {
                        status: 'done'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                carriageType: 'MOBILE',
                rulesData: {},
                sourcesMetadata: {
                    MAGPIE: {
                        status: 'Completed'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                carriageType: 'MOBILE',
                rulesData: {},
                sourcesMetadata: {
                    ODIN: {
                        status: 'Completed'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                carriageType: 'MOBILE',
                rulesData: {
                    MTR001: {
                        status: 'done'
                    },
                    MTR002: {
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    RASSP: {
                        status: 'Completed'
                    },
                    TRADPE: {
                        status: 'Completed'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                carriageType: 'MOBILE',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd333333',
                carriageType: 'MOBILE',
                rulesData: {
                    MTR001: {
                        status: 'done'
                    },
                    MTR002: {
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    RASSP: {
                        status: 'Completed'
                    },
                    TRADPE: {
                        status: 'Completed'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd333333',
                carriageType: 'MOBILE',
                rulesData: {
                    MDR001: {
                        status: 'done'
                    },
                    MTR002: {
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    RASSP: {
                        status: 'Completed'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd111111',
                carriageType: 'INTERNATIONAL',
                rulesData: {
                    MTR001: {
                        status: 'done'
                    },
                    MTR002: {
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    RASSP: {
                        status: 'Completed'
                    },
                    TRADPE: {
                        status: 'Completed'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd111111',
                carriageType: 'INTERNATIONAL',
                rulesData: {
                    MTR001: {
                        status: 'done'
                    },
                    MTR002: {
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    RASSP: {
                        status: 'Completed'
                    },
                    TRADPE: {
                        status: 'Completed'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd222222',
                carriageType: 'INTERNATIONAL',
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    MAGPIE: {
                        status: 'Completed'
                    },
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd222222',
                carriageType: 'INTERNATIONAL',
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2222222R',
                createdBy: 'd111111',
                carriageType: 'NBN',
                rulesData: {
                    MTR001: {
                        status: 'done'
                    },
                    MTR002: {
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    RASSP: {
                        status: 'Completed'
                    },
                    TRADPE: {
                        status: 'Completed'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2222222R',
                createdBy: 'd111111',
                carriageType: 'NBN',
                rulesData: {},
                sourcesMetadata: {
                    MAGPIE: {
                        status: 'Completed'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2222222R',
                createdBy: 'd222222',
                carriageType: 'NBN',
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?carriageType=ADSL&carriageType=NBN&rule=MDR001&rule=MDR002&source=MAGPIE&source=ODIN')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([]);

                        done();
                });
            });
        });

        it('Multiple service checks with multiple carriageType filters, rule filters, source filters and multiple users, one matching', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'ADSL',
                rulesData: {},
                sourcesMetadata: {
                    MAGPIE: {
                        status: 'Completed'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'ADSL',
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'ADSL',
                rulesData: {
                    MDR004: {
                        status: 'done'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd222222',
                carriageType: 'ADSL',
                rulesData: {},
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                carriageType: 'MOBILE',
                rulesData: {
                    MDR004: {
                        status: 'done'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                carriageType: 'MOBILE',
                rulesData: {},
                sourcesMetadata: {
                    MAGPIE: {
                        status: 'Completed'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                carriageType: 'MOBILE',
                rulesData: {},
                sourcesMetadata: {
                    ODIN: {
                        status: 'Completed'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                carriageType: 'MOBILE',
                rulesData: {
                    MTR001: {
                        status: 'done'
                    },
                    MTR002: {
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    RASSP: {
                        status: 'Completed'
                    },
                    TRADPE: {
                        status: 'Completed'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd111111',
                carriageType: 'MOBILE',
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd333333',
                carriageType: 'MOBILE',
                rulesData: {
                    MTR001: {
                        status: 'done'
                    },
                    MTR002: {
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    RASSP: {
                        status: 'Completed'
                    },
                    TRADPE: {
                        status: 'Completed'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N5555555R',
                createdBy: 'd333333',
                carriageType: 'MOBILE',
                rulesData: {
                    MDR001: {
                        status: 'done'
                    },
                    MTR002: {
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    RASSP: {
                        status: 'Completed'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd111111',
                carriageType: 'INTERNATIONAL',
                rulesData: {
                    MTR001: {
                        status: 'done'
                    },
                    MTR002: {
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    RASSP: {
                        status: 'Completed'
                    },
                    TRADPE: {
                        status: 'Completed'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd111111',
                carriageType: 'INTERNATIONAL',
                rulesData: {
                    MTR001: {
                        status: 'done'
                    },
                    MTR002: {
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    RASSP: {
                        status: 'Completed'
                    },
                    TRADPE: {
                        status: 'Completed'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd222222',
                carriageType: 'INTERNATIONAL',
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    MAGPIE: {
                        status: 'Completed'
                    },
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd222222',
                carriageType: 'INTERNATIONAL',
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2222222R',
                createdBy: 'd111111',
                carriageType: 'NBN',
                rulesData: {
                    MTR001: {
                        status: 'done'
                    },
                    MTR002: {
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    RASSP: {
                        status: 'Completed'
                    },
                    TRADPE: {
                        status: 'Completed'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2222222R',
                createdBy: 'd111111',
                carriageType: 'NBN',
                rulesData: {},
                sourcesMetadata: {
                    MAGPIE: {
                        status: 'Completed'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2222222R',
                createdBy: 'd222222',
                carriageType: 'NBN',
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    MAGPIE: {
                        status: 'Completed'
                    }
                },
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?carriageType=ADSL&carriageType=NBN&rule=MDR001&rule=MDR002&source=MAGPIE&source=ODIN')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([{
                            user: 'd222222',
                            start: expectedDate,
                            count: 1
                        }]);

                        done();
                });
            });
        });

        it('Multiple service checks test logical AND between filters for different fields', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N3333333R',
                createdBy: 'd111111',
                carriageType: 'INTERNATIONAL',
                rulesData: {
                    MDR001: {
                        TrgSource: 'Source1',
                        result: 'OK',
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    Source2: {
                        status: 'Collected'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd222222',
                carriageType: 'INTERNATIONAL',
                rulesData: {
                    MDR001: {
                        TrgSource: 'Source1',
                        result: 'OK',
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    Source2: {
                        status: 'Collected'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd111111',
                carriageType: 'ADSL',
                rulesData: {
                    MDR001: {
                        TrgSource: 'Source1',
                        result: 'OK',
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    Source2: {
                        status: 'Collected'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd111111',
                carriageType: 'INTERNATIONAL',
                rulesData: {
                    MDR002: {
                        TrgSource: 'Source1',
                        result: 'OK',
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    Source2: {
                        status: 'Collected'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd111111',
                carriageType: 'INTERNATIONAL',
                rulesData: {
                    MDR001: {
                        TrgSource: 'Source1',
                        result: 'OK',
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N3333333R',
                createdBy: 'd222222',
                carriageType: 'ADSL',
                rulesData: {
                    MDR002: {
                        TrgSource: 'Source1',
                        result: 'OK',
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd222222',
                carriageType: 'ADSL',
                rulesData: {
                    MDR002: {
                        TrgSource: 'Source1',
                        result: 'OK',
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N3333333R',
                createdBy: 'd111111',
                carriageType: 'ADSL',
                rulesData: {
                    MDR002: {
                        TrgSource: 'Source1',
                        result: 'OK',
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N3333333R',
                createdBy: 'd222222',
                carriageType: 'MOBILE',
                rulesData: {
                    MDR002: {
                        TrgSource: 'Source1',
                        result: 'OK',
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N3333333R',
                createdBy: 'd222222',
                carriageType: 'ADSL',
                rulesData: {
                    MDR001: {
                        TrgSource: 'Source1',
                        result: 'OK',
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N3333333R',
                createdBy: 'd222222',
                carriageType: 'ADSL',
                rulesData: {
                    MDR002: {
                        TrgSource: 'Source1',
                        result: 'OK',
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    Source2: {
                        status: 'Collected'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N3333333R',
                createdBy: 'd222222',
                carriageType: 'ADSL',
                rulesData: {
                    MDR002: {
                        TrgSource: 'Source2',
                        result: 'OK',
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N3333333R',
                createdBy: 'd222222',
                carriageType: 'ADSL',
                rulesData: {
                    MDR002: {
                        TrgSource: 'Source1',
                        result: 'Error',
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N3333333R',
                createdBy: 'd222222',
                carriageType: 'ADSL',
                rulesData: {
                    MDR002: {
                        TrgSource: 'Source1',
                        result: 'OK',
                        status: 'error'
                    }
                },
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N3333333R',
                createdBy: 'd222222',
                carriageType: 'ADSL',
                rulesData: {
                    MDR002: {
                        TrgSource: 'Source1',
                        result: 'OK',
                        status: 'done'
                    }
                },
                sourcesMetadata: {
                    Source1: {
                        status: 'Error'
                    }
                },
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?carriageType=ADSL&user=d222222&fnn=N3333333R&rule=MDR002&ruleStatus=done&ruleResult=OK&ruleTriggerSource=Source1&source=Source1&sourceStatus=Collected')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([{
                            user: 'd222222',
                            start: expectedDate,
                            count: 1
                        }]);

                        done();
                });
            });
        });

        it('Multiple service checks test logical OR for multiple carriageType filters', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'NBN',
                rulesData: {},
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'ADSL',
                rulesData: {},
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'MOBILE',
                rulesData: {},
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?carriageType=MOBILE&carriageType=ADSL')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([{
                            user: 'd111111',
                            start: expectedDate,
                            count: 2
                        }]);

                        done();
                });
            });
        });

        it('Multiple service checks test logical OR for multiple user filters', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'NBN',
                rulesData: {},
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd222222',
                carriageType: 'NBN',
                rulesData: {},
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd333333',
                carriageType: 'NBN',
                rulesData: {},
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?user=d111111&user=d333333')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([{
                            user: 'd111111',
                            start: expectedDate,
                            count: 1
                        },
                        {
                            user: 'd333333',
                            start: expectedDate,
                            count: 1
                        }]);

                        done();
                });
            });
        });

        it('Multiple service checks test logical OR for multiple FNN filters', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd111111',
                carriageType: 'NBN',
                rulesData: {},
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2222222R',
                createdBy: 'd111111',
                carriageType: 'NBN',
                rulesData: {},
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N3333333R',
                createdBy: 'd111111',
                carriageType: 'NBN',
                rulesData: {},
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?fnn=N2222222R&fnn=N1111111R')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([{
                            user: 'd111111',
                            start: expectedDate,
                            count: 2
                        }]);

                        done();
                });
            });
        });

        it('Multiple service checks test logical OR for multiple rule filters', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'NBN',
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'NBN',
                rulesData: {
                    MDR002: {
                        status: 'done'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'NBN',
                rulesData: {
                    MDR003: {
                        status: 'done'
                    }
                },
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?rule=MDR001&rule=MDR003')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([{
                            user: 'd111111',
                            start: expectedDate,
                            count: 2
                        }]);

                        done();
                });
            });
        });

        it('Multiple service checks test logical OR for multiple rule filters with subfilters', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'NBN',
                rulesData: {
                    MDR001: {
                        TrgSource: 'Source1',
                        result: 'OK',
                        status: 'done'
                    },
                    MDR002: {
                        TrgSource: 'Source3',
                        result: 'Failed',
                        status: 'error'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'NBN',
                rulesData: {
                    MDR001: {
                        TrgSource: 'Source3',
                        result: 'OK',
                        status: 'done'
                    },
                    MDR002: {
                        TrgSource: 'Source1',
                        result: 'Failed',
                        status: 'error'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'NBN',
                rulesData: {
                    MDR003: {
                        TrgSource: 'Source1',
                        result: 'OK',
                        status: 'done'
                    },
                    MDR004: {
                        TrgSource: 'Source2',
                        result: 'Actioned',
                        status: 'Pre-Condition Reject'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd222222',
                carriageType: 'NBN',
                rulesData: {
                    MDR002: {
                        TrgSource: 'Source2',
                        result: 'Actioned',
                        status: 'done'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd222222',
                carriageType: 'NBN',
                rulesData: {
                    MDR001: {
                        TrgSource: 'Source1',
                        result: 'Warning',
                        status: 'done'
                    },
                    MDR002: {
                        TrgSource: 'Source2',
                        result: 'Actioned',
                        status: 'error'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd222222',
                carriageType: 'NBN',
                rulesData: {
                    MDR001: {
                        TrgSource: 'Source2',
                        result: 'Actioned',
                        status: 'done'
                    },
                    MDR002: {
                        TrgSource: 'Source1',
                        result: 'OK',
                        status: 'Pre-Condition Reject'
                    }
                },
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?rule=MDR001&rule=MDR002&ruleResult=OK&ruleResult=Actioned&ruleStatus=done&ruleStatus=Pre-Condition Reject&ruleTriggerSource=Source1&ruleTriggerSource=Source2')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([{
                            user: 'd222222',
                            start: expectedDate,
                            count: 2
                        },
                        {
                            user: 'd111111',
                            start: expectedDate,
                            count: 1
                        }]);

                        done();
                });
            });
        });

        it('Multiple service checks test logical OR for multiple source filters', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'NBN',
                rulesData: {},
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'NBN',
                rulesData: {},
                sourcesMetadata: {
                    Source2: {
                        status: 'Collected'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'NBN',
                rulesData: {},
                sourcesMetadata: {
                    Source3: {
                        status: 'Collected'
                    }
                },
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?source=Source2&source=Source3')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([{
                            user: 'd111111',
                            start: expectedDate,
                            count: 2
                        }]);

                        done();
                });
            });
        });

        it('Multiple service checks test logical OR for multiple source filters with subfilters', (done) => {
            let promises = [];
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'NBN',
                rulesData: {},
                sourcesMetadata: {
                    Source1: {
                        status: 'Error'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'NBN',
                rulesData: {},
                sourcesMetadata: {
                    Source2: {
                        status: 'Collected'
                    }
                },
                createdOn: new Date()
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                carriageType: 'NBN',
                rulesData: {},
                sourcesMetadata: {
                    Source3: {
                        status: 'Pre-Condition Error'
                    }
                },
                createdOn: new Date()
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?source=Source1&source=Source3&sourceStatus=Collected&sourceStatus=Error')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([{
                            user: 'd111111',
                            start: expectedDate,
                            count: 1
                        }]);

                        done();
                });
            });
        });

        it('Service checks ordered by count ascending', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd333333',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd333333',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd333333',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd333333',
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?order=desc')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);
                        chai.expect(res.body.results).to.be.like([{
                            user: 'd333333',
                            start: expectedDate,
                            count: 4
                        },
                        {
                            user: 'd222222',
                            start: expectedDate,
                            count: 2
                        },
                        {
                            user: 'd111111',
                            start: expectedDate,
                            count: 1
                        }]);

                        done();
                });
            });
        });

        it('Service checks ordered by count descending', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd333333',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd333333',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd333333',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd333333',
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?order=asc')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);
                        chai.expect(res.body.results).to.be.like([{
                            user: 'd111111',
                            start: expectedDate,
                            count: 1
                        },
                        {
                            user: 'd222222',
                            start: expectedDate,
                            count: 2
                        },
                        {
                            user: 'd333333',
                            start: expectedDate,
                            count: 4
                        }]);

                        done();
                });
            });
        });

        it('Get stats grouped by users limit test 1', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?limit=1')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: expectedDate,
                        count: 2
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by users limit test 2', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd333333',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd444444',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd333333',
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?limit=3')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        user: 'd111111',
                        start: expectedDate,
                        count: 4
                    },
                    {
                        user: 'd333333',
                        start: expectedDate,
                        count: 2
                    },
                    {
                        user: 'd222222',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by users offset test 1', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?offset=1')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        user: 'd222222',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by users offset test 2', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd333333',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd444444',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd333333',
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsUsers?offset=2')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        user: 'd222222',
                        start: expectedDate,
                        count: 1
                    },
                    {
                        user: 'd444444',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by users invalid limit (non-integer)', (done) => {
            request.get('/stats/scsUsers?limit=notalimit')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get stats grouped by users invalid limit (integer equals 0)', (done) => {
            request.get('/stats/scsUsers?limit=0')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get stats grouped by users invalid limit (negative integer)', (done) => {
            request.get('/stats/scsUsers?limit=-1')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get stats grouped by users invalid offset (non-integer)', (done) => {
            request.get('/stats/scsUsers?offset=notanoffset')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get stats grouped by users invalid offset (negative integer)', (done) => {
            request.get('/stats/scsUsers?offset=-1')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });
    });


    describe('Service check count grouped by carriage type', () => {
        it('Unauthenticated request', async() => {
            let res = await request.get('/stats/scsCarriageTypes').unset('Authorization');
            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                let token = await helpers.authenticateApi(request, username);
                let res = await request.get('/stats/scsCarriageTypes').set('Authorization', `Bearer ${token}`);

                switch(username) {
                    case 'apiAccess':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('No service checks', (done) => {
            request.get('/stats/scsCarriageTypes')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);
                chai.expect(res.body.results).to.eql([]);
                done();
            });
        });

        it('One service check in last week no parameters', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            var serviceCheck = new ServiceCheckModel({
                fnn: 'N1234567R',
                carriageType: 'ADSL',
                createdOn: currDate
            });

            serviceCheck.save().then(() => {
                request.get('/stats/scsCarriageTypes')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([{
                            carriageType: 'ADSL',
                            start: expectedDate,
                            count: 1
                        }]);

                        done();
                });
            });
        });

        it('Get stats grouped by carriage type limit test 1', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                carriageType: 'ADSL',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                carriageType: 'ADSL',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                carriageType: 'NBN',
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsCarriageTypes?limit=1')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        carriageType: 'ADSL',
                        start: expectedDate,
                        count: 2
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by carriage type limit test 2', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                carriageType: 'ADSL',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                carriageType: 'NBN',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                carriageType: 'MOBILE',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                carriageType: 'ADSL',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                carriageType: 'ADSL',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                carriageType: 'VOIP',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                carriageType: 'ADSL',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                carriageType: 'MOBILE',
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsCarriageTypes?limit=3')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        carriageType: 'ADSL',
                        start: expectedDate,
                        count: 4
                    },
                    {
                        carriageType: 'MOBILE',
                        start: expectedDate,
                        count: 2
                    },
                    {
                        carriageType: 'NBN',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by carriage type offset test 1', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                carriageType: 'ADSL',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                carriageType: 'ADSL',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                carriageType: 'NBN',
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsCarriageTypes?offset=1')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        carriageType: 'NBN',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by carriage type offset test 2', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                carriageType: 'ADSL',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                carriageType: 'NBN',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                carriageType: 'MOBILE',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                carriageType: 'ADSL',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                carriageType: 'ADSL',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                carriageType: 'VOIP',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                carriageType: 'ADSL',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                carriageType: 'MOBILE',
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsCarriageTypes?offset=2')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        carriageType: 'NBN',
                        start: expectedDate,
                        count: 1
                    },
                    {
                        carriageType: 'VOIP',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by carriage type invalid limit (non-integer)', (done) => {
            request.get('/stats/scsCarriageTypes?limit=notalimit')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get stats grouped by carriage type invalid limit (integer equals 0)', (done) => {
            request.get('/stats/scsCarriageTypes?limit=0')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get stats grouped by carriage type invalid limit (negative integer)', (done) => {
            request.get('/stats/scsCarriageTypes?limit=-1')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get stats grouped by carriage type invalid offset (non-integer)', (done) => {
            request.get('/stats/scsCarriageTypes?offset=notanoffset')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get stats grouped by carriage type invalid offset (negative integer)', (done) => {
            request.get('/stats/scsCarriageTypes?offset=-1')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });
    });


    describe('Service check count grouped by FNN', () => {
        it('Unauthenticated request', async() => {
            let res = await request.get('/stats/scsFnn').unset('Authorization');
            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                let token = await helpers.authenticateApi(request, username);
                let res = await request.get('/stats/scsFnn').set('Authorization', `Bearer ${token}`);

                switch(username) {
                    case 'apiAccess':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('No service checks', (done) => {
            request.get('/stats/scsFnn')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);
                chai.expect(res.body.results).to.eql([]);
                done();
            });
        });

        it('One service check in last week no parameters', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            var serviceCheck = new ServiceCheckModel({
                fnn: 'N1234567R',
                createdOn: currDate
            });

            serviceCheck.save().then(() => {
                request.get('/stats/scsFnn')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        fnn: 'N1234567R',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by FNN limit test 1', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsFnn?limit=1')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        fnn: 'N1111111R',
                        start: expectedDate,
                        count: 2
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by FNN limit test 2', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2222222R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N3333333R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd333333',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N4444444R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd444444',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N3333333R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsFnn?limit=3')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        fnn: 'N1111111R',
                        start: expectedDate,
                        count: 4
                    },
                    {
                        fnn: 'N3333333R',
                        start: expectedDate,
                        count: 2
                    },
                    {
                        fnn: 'N2222222R',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by FNN offset test 2', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2222222R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N3333333R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd333333',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N4444444R',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                createdBy: 'd444444',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N3333333R',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsFnn?offset=2')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        fnn: 'N2222222R',
                        start: expectedDate,
                        count: 1
                    },
                    {
                        fnn: 'N4444444R',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by FNN phone numbers test 1', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: '0411222333',
                phoneNumber: '+61411222333',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: '0411222333',
                phoneNumber: '+61411222333',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                phoneNumber: null,
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                phoneNumber: null,
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: '61422224444',
                phoneNumber: '+61422224444',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: '0422 224 444',
                phoneNumber: '+61422224444',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'display FNN',
                phoneNumber: '+61422224444',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsFnn')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        fnn: '+61422224444',
                        start: expectedDate,
                        count: 3
                    },
                    {
                        fnn: '+61411222333',
                        start: expectedDate,
                        count: 2
                    },
                    {
                        fnn: 'N1234567R',
                        start: expectedDate,
                        count: 2
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by FNN phone numbers test 2', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: '0411222333',
                phoneNumber: null,
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: '0411222333',
                phoneNumber: null,
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                phoneNumber: null,
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                phoneNumber: null,
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsFnn')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        fnn: '0411222333',
                        start: expectedDate,
                        count: 2
                    },
                    {
                        fnn: 'N1234567R',
                        start: expectedDate,
                        count: 2
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by FNN filter by FNN', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                phoneNumber: null,
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N2222222R',
                phoneNumber: null,
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                phoneNumber: null,
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: 'N1111111R',
                phoneNumber: null,
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsFnn?fnn=N1111111R&fnn=N1234567R')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        fnn: 'N1111111R',
                        start: expectedDate,
                        count: 2
                    },
                    {
                        fnn: 'N1234567R',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by FNN filter by FNN with phone number', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: '0404111222',
                phoneNumber: '+61404111222',
                createdBy: 'd111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: '0404 111 222',
                phoneNumber: '+61404111222',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: '61422334444',
                phoneNumber: '+61422334444',
                createdBy: 'd1111111',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: '0422334444',
                phoneNumber: '+61422334444',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: '0422334444',
                phoneNumber: '+61422334444',
                createdBy: 'd333333',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: '0422334444',
                phoneNumber: '+61422334444',
                createdBy: 'd444444',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: '94221111',
                phoneNumber: '+61294221111',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                fnn: '(02) 9422 1111',
                phoneNumber: '+61294221111',
                createdBy: 'd222222',
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsFnn?fnn=+61404111222&fnn=61422334444')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        fnn: '+61422334444',
                        start: expectedDate,
                        count: 4
                    },
                    {
                        fnn: '+61404111222',
                        start: expectedDate,
                        count: 2
                    }]);

                    done();
                });
            });
        });

        it('Service checks in day interval', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: 'N1234567R',
                phoneNumber: null,
                createdBy: 'd111111',
                createdOn: new Date('2020-10-10T04:00:00.000Z')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsFnn?last=20y&interval=d')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        fnn: 'N1234567R',
                        start: new Date('2020-10-09T13:00:00.000Z'),
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Service checks in day interval with phone number', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                fnn: '0411222333',
                phoneNumber: '+61411222333',
                createdBy: 'd111111',
                createdOn: new Date('2020-10-10T04:00:00.000Z')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsFnn?last=20y&interval=d')
                    .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body.results).to.be.like([{
                        fnn: '+61411222333',
                        start: new Date('2020-10-09T13:00:00.000Z'),
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by FNN invalid limit (non-integer)', (done) => {
            request.get('/stats/scsFnn?limit=notalimit')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get stats grouped by FNN invalid limit (integer equals 0)', (done) => {
            request.get('/stats/scsFnn?limit=0')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get stats grouped by FNN invalid limit (negative integer)', (done) => {
            request.get('/stats/scsFnn?limit=-1')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get stats grouped by FNN invalid offset (non-integer)', (done) => {
            request.get('/stats/scsFnn?offset=notanoffset')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get stats grouped by FNN invalid offset (negative integer)', (done) => {
            request.get('/stats/scsFnn?offset=-1')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });
    });


    describe('Service check count grouped by rules', () => {
        it('Unauthenticated request', async() => {
            let res = await request.get('/stats/scsRules').unset('Authorization');
            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                let token = await helpers.authenticateApi(request, username);
                let res = await request.get('/stats/scsRules').set('Authorization', `Bearer ${token}`);

                switch(username) {
                    case 'apiAccess':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('No service checks', (done) => {
            request.get('/stats/scsRules')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);
                chai.expect(res.body.results).to.eql([]);
                done();
            });
        });

        it('One service check in last week no parameters', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            var serviceCheck = new ServiceCheckModel({
                fnn: 'N1234567R',
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            });

            serviceCheck.save().then(() => {
                request.get('/stats/scsRules')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([{
                            rule: 'MDR001',
                            start: expectedDate,
                            count: 1
                        }]);

                        done();
                });
            });
        });

        it('One service check in last week', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setMonth(currDate.getMonth() - 1);

            var serviceCheck = new ServiceCheckModel({
                fnn: 'N1234567R',
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            });

            serviceCheck.save().then(() => {
                request.get('/stats/scsRules?last=1m')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([{
                            rule: 'MDR001',
                            start: expectedDate,
                            count: 1
                        }]);

                        done();
                });
            });
        });

        it('Multiple service checks and rules separated by day interval', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR001: {
                        status: 'done'
                    },
                    MDR002: {
                        status: 'done'
                    }
                },
                createdOn: new Date('2020-12-07T13:00:00.001Z')
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MTR001: {
                        status: 'done'
                    },
                    MTR002: {
                        status: 'done'
                    }
                },
                createdOn: new Date('2020-12-08T12:59:59.999Z')
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR001: {
                        status: 'done'
                    },
                    MTR001: {
                        status: 'done'
                    }
                },
                createdOn: new Date('2020-12-08T02:50:50.000Z')
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MSR001: {
                        status: 'done'
                    },
                    MTR001: {
                        status: 'done'
                    },
                    MTR002: {
                        status: 'done'
                    }
                },
                createdOn: new Date('2020-12-06T13:00:00.001Z')
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR001: {
                        status: 'done'
                    },
                    MDR002: {
                        status: 'done'
                    },
                    MTR001: {
                        status: 'done'
                    },
                    MTR002: {
                        status: 'done'
                    }
                },
                createdOn: new Date('2020-12-07T12:59:59.999Z')
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {},
                createdOn: new Date('2020-12-07T08:16:24.592Z')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsRules?last=20y&interval=d')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.metadata.pagination.total).to.equal(9);

                        chai.expect(res.body.results).to.be.like([{
                            rule: 'MDR001',
                            start: new Date('2020-12-07T13:00:00.000Z'),
                            count: 2
                        },
                        {
                            rule: 'MTR001',
                            start: new Date('2020-12-07T13:00:00.000Z'),
                            count: 2
                        },
                        {
                            rule: 'MDR002',
                            start: new Date('2020-12-07T13:00:00.000Z'),
                            count: 1
                        },
                        {
                            rule: 'MTR002',
                            start: new Date('2020-12-07T13:00:00.000Z'),
                            count: 1
                        },
                        {
                            rule: 'MTR001',
                            start: new Date('2020-12-06T13:00:00.000Z'),
                            count: 2
                        },
                        {
                            rule: 'MTR002',
                            start: new Date('2020-12-06T13:00:00.000Z'),
                            count: 2
                        },
                        {
                            rule: 'MDR001',
                            start: new Date('2020-12-06T13:00:00.000Z'),
                            count: 1
                        },
                        {
                            rule: 'MDR002',
                            start: new Date('2020-12-06T13:00:00.000Z'),
                            count: 1
                        },
                        {
                            rule: 'MSR001',
                            start: new Date('2020-12-06T13:00:00.000Z'),
                            count: 1
                        }]);

                        done();
                });
            });
        });

        it('Multiple service checks and rules separated by week interval', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR001: {
                        status: 'done'
                    },
                    MDR002: {
                        status: 'done'
                    }
                },
                createdOn: new Date('2020-03-02T13:00:00.000+11:00')
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MTR001: {
                        status: 'done'
                    },
                    MTR002: {
                        status: 'done'
                    }
                },
                createdOn: new Date('2020-04-18T05:00:00.000+10:00')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsRules?last=20y&interval=w')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.metadata.pagination.total).to.equal(4);

                        chai.expect(res.body.results).to.be.like([{
                            rule: 'MTR001',
                            start: new Date('2020-04-12T14:00:00.000Z'),
                            count: 1
                        },
                        {
                            rule: 'MTR002',
                            start: new Date('2020-04-12T14:00:00.000Z'),
                            count: 1
                        },
                        {
                            rule: 'MDR001',
                            start: new Date('2020-03-01T13:00:00.000Z'),
                            count: 1
                        },
                        {
                            rule: 'MDR002',
                            start: new Date('2020-03-01T13:00:00.000Z'),
                            count: 1
                        }]);

                        done();
                });
            });
        });

        it('Multiple service checks and rules separated by month interval', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR001: {
                        status: 'done'
                    },
                    MDR002: {
                        status: 'done'
                    }
                },
                createdOn: new Date('2020-03-01T13:00:00.000+11:00')
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MTR001: {
                        status: 'done'
                    },
                    MTR002: {
                        status: 'done'
                    }
                },
                createdOn: new Date('2020-04-18T05:00:00.000+10:00')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsRules?last=20y&interval=m')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.metadata.pagination.total).to.equal(4);

                        chai.expect(res.body.results).to.be.like([{
                            rule: 'MTR001',
                            start: new Date('2020-03-31T13:00:00.000Z'),
                            count: 1
                        },
                        {
                            rule: 'MTR002',
                            start: new Date('2020-03-31T13:00:00.000Z'),
                            count: 1
                        },
                        {
                            rule: 'MDR001',
                            start: new Date('2020-02-29T13:00:00.000Z'),
                            count: 1
                        },
                        {
                            rule: 'MDR002',
                            start: new Date('2020-02-29T13:00:00.000Z'),
                            count: 1
                        }]);

                        done();
                });
            });
        });

        it('Multiple service checks and rules separated by year interval', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR001: {
                        status: 'done'
                    },
                    MDR002: {
                        status: 'done'
                    }
                },
                createdOn: new Date('2020-03-01T13:00:00.000+11:00')
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MTR001: {
                        status: 'done'
                    },
                    MTR002: {
                        status: 'done'
                    }
                },
                createdOn: new Date('2021-04-18T05:00:00.000+10:00')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsRules?last=20y&interval=y')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.metadata.pagination.total).to.equal(4);

                        chai.expect(res.body.results).to.be.like([{
                            rule: 'MTR001',
                            start: new Date('2020-12-31T13:00:00.000Z'),
                            count: 1
                        },
                        {
                            rule: 'MTR002',
                            start: new Date('2020-12-31T13:00:00.000Z'),
                            count: 1
                        },
                        {
                            rule: 'MDR001',
                            start: new Date('2019-12-31T13:00:00.000Z'),
                            count: 1
                        },
                        {
                            rule: 'MDR002',
                            start: new Date('2019-12-31T13:00:00.000Z'),
                            count: 1
                        }]);

                        done();
                });
            });
        });

        it('Get stats grouped by rules filter test 1', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR002: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR003: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR001: {
                        status: 'Code Error'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR004: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR001: {
                        status: 'Code Error'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR003: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsRules?rule=MDR001')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        rule: 'MDR001',
                        start: expectedDate,
                        count: 4
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by rules filter test 2', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR002: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR003: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR001: {
                        status: 'Code Error'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR004: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR001: {
                        status: 'Code Error'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR003: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsRules?rule=MDR001&ruleStatus=done')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        rule: 'MDR001',
                        start: expectedDate,
                        count: 2
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by rules limit test 1', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR002: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsRules?limit=1')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        rule: 'MDR001',
                        start: expectedDate,
                        count: 2
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by rules limit test 2', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR002: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR003: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR004: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR003: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsRules?limit=3')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        rule: 'MDR001',
                        start: expectedDate,
                        count: 4
                    },
                    {
                        rule: 'MDR003',
                        start: expectedDate,
                        count: 2
                    },
                    {
                        rule: 'MDR002',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by rules offset test 1', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR002: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsRules?offset=1')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        rule: 'MDR002',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by rules offset test 2', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR002: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR003: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR004: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR001: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                rulesData: {
                    MDR003: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsRules?offset=2')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        rule: 'MDR002',
                        start: expectedDate,
                        count: 1
                    },
                    {
                        rule: 'MDR004',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by rules invalid limit (non-integer)', (done) => {
            request.get('/stats/scsRules?limit=notalimit')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get stats grouped by rules invalid limit (integer equals 0)', (done) => {
            request.get('/stats/scsRules?limit=0')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get stats grouped by rules invalid limit (negative integer)', (done) => {
            request.get('/stats/scsRules?limit=-1')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get stats grouped by rules invalid offset (non-integer)', (done) => {
            request.get('/stats/scsRules?offset=notanoffset')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get stats grouped by rules invalid offset (negative integer)', (done) => {
            request.get('/stats/scsRules?offset=-1')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });
    });


    describe('Service check count grouped by sources', () => {
        it('Unauthenticated request', async() => {
            let res = await request.get('/stats/scsSources').unset('Authorization');
            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                let token = await helpers.authenticateApi(request, username);
                let res = await request.get('/stats/scsSources').set('Authorization', `Bearer ${token}`);

                switch(username) {
                    case 'apiAccess':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('No service checks', (done) => {
            request.get('/stats/scsSources')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);
                chai.expect(res.body.results).to.eql([]);
                done();
            });
        });

        it('One service check in last week no parameters', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            var serviceCheck = new ServiceCheckModel({
                fnn: 'N1234567R',
                sourcesMetadata: {
                    MAGPIE: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            });

            serviceCheck.save().then(() => {
                request.get('/stats/scsSources')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([{
                            source: 'MAGPIE',
                            start: expectedDate,
                            count: 1
                        }]);

                        done();
                });
            });
        });

        it('One service check in last week', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setMonth(currDate.getMonth() - 1);

            var serviceCheck = new ServiceCheckModel({
                fnn: 'N1234567R',
                sourcesMetadata: {
                    MAGPIE: {
                        status: 'done'
                    }
                },
                createdOn: currDate
            });

            serviceCheck.save().then(() => {
                request.get('/stats/scsSources?last=1m')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.results).to.be.like([{
                            source: 'MAGPIE',
                            start: expectedDate,
                            count: 1
                        }]);

                        done();
                });
            });
        });

        it('Multiple service checks and sources separated by day interval', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    MAGPIE: {
                        status: 'done'
                    },
                    ODIN: {
                        status: 'done'
                    }
                },
                createdOn: new Date('2020-12-07T13:00:00.001Z')
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    RASSP: {
                        status: 'done'
                    },
                    CMI: {
                        status: 'done'
                    }
                },
                createdOn: new Date('2020-12-08T12:59:59.999Z')
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    MAGPIE: {
                        status: 'done'
                    },
                    RASSP: {
                        status: 'done'
                    }
                },
                createdOn: new Date('2020-12-08T06:50:50.000Z')
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    GMACS: {
                        status: 'done'
                    },
                    RASSP: {
                        status: 'done'
                    },
                    CMI: {
                        status: 'done'
                    }
                },
                createdOn: new Date('2020-12-06T13:00:00.001Z')
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    MAGPIE: {
                        status: 'done'
                    },
                    ODIN: {
                        status: 'done'
                    },
                    RASSP: {
                        status: 'done'
                    },
                    CMI: {
                        status: 'done'
                    }
                },
                createdOn: new Date('2020-12-07T12:59:59.999Z')
            }).save());

            promises.push(new ServiceCheckModel({
                createdOn: new Date('2020-12-07T08:16:24.592Z')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsSources?last=20y&interval=d')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.metadata.pagination.total).to.equal(9);

                        chai.expect(res.body.results).to.be.like([{
                            source: 'MAGPIE',
                            start: new Date('2020-12-07T13:00:00.000Z'),
                            count: 2
                        },
                        {
                            source: 'RASSP',
                            start: new Date('2020-12-07T13:00:00.000Z'),
                            count: 2
                        },
                        {
                            source: 'CMI',
                            start: new Date('2020-12-07T13:00:00.000Z'),
                            count: 1
                        },
                        {
                            source: 'ODIN',
                            start: new Date('2020-12-07T13:00:00.000Z'),
                            count: 1
                        },
                        {
                            source: 'CMI',
                            start: new Date('2020-12-06T13:00:00.000Z'),
                            count: 2
                        },
                        {
                            source: 'RASSP',
                            start: new Date('2020-12-06T13:00:00.000Z'),
                            count: 2
                        },
                        {
                            source: 'GMACS',
                            start: new Date('2020-12-06T13:00:00.000Z'),
                            count: 1
                        },
                        {
                            source: 'MAGPIE',
                            start: new Date('2020-12-06T13:00:00.000Z'),
                            count: 1
                        },
                        {
                            source: 'ODIN',
                            start: new Date('2020-12-06T13:00:00.000Z'),
                            count: 1
                        }]);

                        done();
                });
            });
        });

        it('Multiple service checks and sources separated by week interval', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    MAGPIE: {
                        status: 'done'
                    },
                    ODIN: {
                        status: 'done'
                    }
                },
                createdOn: new Date('2020-03-02T13:00:00.000+11:00')
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    RASSP: {
                        status: 'done'
                    },
                    CMI: {
                        status: 'done'
                    }
                },
                createdOn: new Date('2020-04-18T05:00:00.000+10:00')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsSources?last=20y&interval=w')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.metadata.pagination.total).to.equal(4);

                        chai.expect(res.body.results).to.be.like([{
                            source: 'CMI',
                            start: new Date('2020-04-12T14:00:00.000Z'),
                            count: 1
                        },
                        {
                            source: 'RASSP',
                            start: new Date('2020-04-12T14:00:00.000Z'),
                            count: 1
                        },
                        {
                            source: 'MAGPIE',
                            start: new Date('2020-03-01T13:00:00.000Z'),
                            count: 1
                        },
                        {
                            source: 'ODIN',
                            start: new Date('2020-03-01T13:00:00.000Z'),
                            count: 1
                        }]);

                        done();
                });
            });
        });

        it('Multiple service checks and sources separated by month interval', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    MAGPIE: {
                        status: 'done'
                    },
                    ODIN: {
                        status: 'done'
                    }
                },
                createdOn: new Date('2020-03-01T13:00:00.000+11:00')
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    RASSP: {
                        status: 'done'
                    },
                    CMI: {
                        status: 'done'
                    }
                },
                createdOn: new Date('2020-04-18T05:00:00.000+10:00')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsSources?last=20y&interval=m')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.metadata.pagination.total).to.equal(4);

                        chai.expect(res.body.results).to.be.like([{
                            source: 'CMI',
                            start: new Date('2020-03-31T13:00:00.000Z'),
                            count: 1
                        },
                        {
                            source: 'RASSP',
                            start: new Date('2020-03-31T13:00:00.000Z'),
                            count: 1
                        },
                        {
                            source: 'MAGPIE',
                            start: new Date('2020-02-29T13:00:00.000Z'),
                            count: 1
                        },
                        {
                            source: 'ODIN',
                            start: new Date('2020-02-29T13:00:00.000Z'),
                            count: 1
                        }]);

                        done();
                });
            });
        });

        it('Multiple service checks and sources separated by year interval', (done) => {
            let promises = [];

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    MAGPIE: {
                        status: 'done'
                    },
                    ODIN: {
                        status: 'done'
                    }
                },
                createdOn: new Date('2020-03-01T13:00:00.000+11:00')
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    RASSP: {
                        status: 'done'
                    },
                    CMI: {
                        status: 'done'
                    }
                },
                createdOn: new Date('2021-04-18T05:00:00.000+10:00')
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsSources?last=20y&interval=y')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }
                        res.should.have.status(200);

                        chai.expect(res.body.metadata.pagination.total).to.equal(4);

                        chai.expect(res.body.results).to.be.like([{
                            source: 'CMI',
                            start: new Date('2020-12-31T13:00:00.000Z'),
                            count: 1
                        },
                        {
                            source: 'RASSP',
                            start: new Date('2020-12-31T13:00:00.000Z'),
                            count: 1
                        },
                        {
                            source: 'MAGPIE',
                            start: new Date('2019-12-31T13:00:00.000Z'),
                            count: 1
                        },
                        {
                            source: 'ODIN',
                            start: new Date('2019-12-31T13:00:00.000Z'),
                            count: 1
                        }]);

                        done();
                });
            });
        });

        it('Get stats grouped by sources limit test 1', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source2: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsSources?limit=1')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        source: 'Source1',
                        start: expectedDate,
                        count: 2
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by sources limit test 2', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source2: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source3: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source4: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source3: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsSources?limit=3')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        source: 'Source1',
                        start: expectedDate,
                        count: 4
                    },
                    {
                        source: 'Source3',
                        start: expectedDate,
                        count: 2
                    },
                    {
                        source: 'Source2',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by sources with sources filter test 1', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source2: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source3: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source1: {
                        status: 'Error'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source4: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source1: {
                        status: 'Error'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source3: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsSources?source=Source1')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        source: 'Source1',
                        start: expectedDate,
                        count: 4
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by sources with sources filter test 2', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source2: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source3: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source1: {
                        status: 'Error'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source4: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source1: {
                        status: 'Error'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source3: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsSources?source=Source1&sourceStatus=Collected')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        source: 'Source1',
                        start: expectedDate,
                        count: 2
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by sources offset test 1', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source2: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsSources?offset=1')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        source: 'Source2',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by sources offset test 2', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setDate(currDate.getDate() - 1);

            let promises = [];

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source2: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source3: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source4: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source1: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            promises.push(new ServiceCheckModel({
                sourcesMetadata: {
                    Source3: {
                        status: 'Collected'
                    }
                },
                createdOn: currDate
            }).save());

            Promise.all(promises).then(() => {
                request.get('/stats/scsSources?offset=2')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);
                    chai.expect(res.body.results).to.be.like([{
                        source: 'Source2',
                        start: expectedDate,
                        count: 1
                    },
                    {
                        source: 'Source4',
                        start: expectedDate,
                        count: 1
                    }]);

                    done();
                });
            });
        });

        it('Get stats grouped by sources invalid limit (non-integer)', (done) => {
            request.get('/stats/scsSources?limit=notalimit')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get stats grouped by sources invalid limit (integer equals 0)', (done) => {
            request.get('/stats/scsSources?limit=0')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get stats grouped by sources invalid limit (negative integer)', (done) => {
            request.get('/stats/scsSources?limit=-1')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get stats grouped by sources invalid offset (non-integer)', (done) => {
            request.get('/stats/scsSources?offset=notanoffset')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get stats grouped by sources invalid offset (negative integer)', (done) => {
            request.get('/stats/scsSources?offset=-1')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });
    });


    describe('Last service check for each user', () => {
        it('Unauthenticated request', async() => {
            let res = await request.get('/stats/scsLastCheckPerUser').unset('Authorization');
            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                let token = await helpers.authenticateApi(request, username);
                let res = await request.get('/stats/scsLastCheckPerUser').set('Authorization', `Bearer ${token}`);

                switch(username) {
                    case 'apiAccess':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('No service checks', async() => {
            let startDate = new Date();
            startDate.setMonth(startDate.getMonth() - 1);

            let res = await request.get('/stats/scsLastCheckPerUser');

            res.should.have.status(200);

            chai.expect(res.body.metadata).to.be.like({
                pagination: {
                    limit: 100,
                    offset: 0,
                    total: 0,
                    prev: null,
                    next: null
                },
                startDate: startDate
            });
            chai.expect(res.body.results).to.eql([]);
        });

        it('One service check no parameters', async() => {
            let currDate = new Date();
            let startDate = new Date();
            startDate.setMonth(startDate.getMonth() - 1);

            let serviceCheck = new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            });

            await serviceCheck.save();

            let res = await request.get('/stats/scsLastCheckPerUser');

            res.should.have.status(200);

            chai.expect(res.body.metadata).to.be.like({
                pagination: {
                    limit: 100,
                    offset: 0,
                    total: 1,
                    prev: null,
                    next: null
                },
                startDate: startDate
            });

            chai.expect(res.body.results).to.be.like([{
                user: 'd111111',
                lastServiceCheck: currDate
            }]);
        });

        it('One service check in last year no parameters', async() => {
            let currDate = new Date();
            currDate.setFullYear(currDate.getFullYear() - 1);
            let startDate = new Date();
            startDate.setMonth(startDate.getMonth() - 1);

            let serviceCheck = new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            });

            await serviceCheck.save();

            let res = await request.get('/stats/scsLastCheckPerUser');

            res.should.have.status(200);

            chai.expect(res.body.metadata).to.be.like({
                pagination: {
                    limit: 100,
                    offset: 0,
                    total: 0,
                    prev: null,
                    next: null
                },
                startDate: startDate
            });

            chai.expect(res.body.results).to.be.like([]);
        });

        it('One service check in last year with last parameter', async() => {
            let currDate = new Date();
            currDate.setDate(currDate.getDate() - 360);
            let startDate = new Date();
            startDate.setFullYear(startDate.getFullYear() - 1);

            let serviceCheck = new ServiceCheckModel({
                fnn: 'N1234567R',
                createdBy: 'd111111',
                createdOn: currDate
            });

            await serviceCheck.save();

            let res = await request.get('/stats/scsLastCheckPerUser?last=1y');

            res.should.have.status(200);

            chai.expect(res.body.metadata).to.be.like({
                pagination: {
                    limit: 100,
                    offset: 0,
                    total: 1,
                    prev: null,
                    next: null
                },
                startDate: startDate
            });

            chai.expect(res.body.results).to.be.like([{
                user: 'd111111',
                lastServiceCheck: currDate
            }]);
        });
    });


    describe('AAA tool usage by user', () => {
        it('Unauthenticated request', async() => {
            let res = await request.get('/stats/aaaToolUsers').unset('Authorization');
            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                const pool = (await import('../../modules/hangarDatabase.js')).default;
                mockMySQL = sinon.mock(pool);

                switch(username) {
                    case 'apiAccess':
                    case 'admin':
                    case 'levelLead':
                        mockMySQL.expects('execute').once().yields(null, []);
                        mockMySQL.expects('execute').withArgs('SELECT FOUND_ROWS() as total').once().yields(null, [{ total: 0 }]);
                        break;
                }


                let token = await helpers.authenticateApi(request, username);
                let res = await request.get('/stats/aaaToolUsers').set('Authorization', `Bearer ${token}`);

                switch(username) {
                    case 'apiAccess':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }

                mockMySQL.verify();
                mockMySQL.restore();
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Invalid query parameters 1', (done) => {
            request.get('/stats/aaaToolUsers?last=y')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);

                done();
            });
        });

        it('Invalid query parameters 2', (done) => {
            request.get('/stats/aaaToolUsers?last=1y&interval=day')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);

                done();
            });
        });

        it('No AAA tool records (MySQL mock)', (done) => {
            // Sets response of executing SQL query in mock object
            mockMySQL.expects('execute').once().yields(null, []);
            mockMySQL.expects('execute').withArgs('SELECT FOUND_ROWS() as total').once().yields(null, [{ total: 0 }]);

            request.get('/stats/aaaToolUsers')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(200);

                chai.expect(res.body).to.eql({
                    metadata: {
                        pagination: {
                            limit: 100,
                            offset: 0,
                            total: 0,
                            prev: null,
                            next: null
                        }
                    },
                    results: []
                });

                mockMySQL.verify();

                done();
            });
        });

        it('Single AAA tool record (MySQL mock)', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setMonth(expectedDate.getMonth() - 1);

            mockMySQL.expects('execute').withArgs(
                dedent`SELECT SQL_CALC_FOUND_ROWS ? as start, COUNT(*) as count, userid as user \
                       FROM AAA_usage \
                       WHERE timestamp > ? \
                       GROUP BY userid \
                       ORDER BY count DESC,user ASC LIMIT ? OFFSET ?`
            ).once().yields(null, [{
                user: 'd111111',
                start: expectedDate,
                count: 1
            }]);
            mockMySQL.expects('execute').withArgs('SELECT FOUND_ROWS() as total').once().yields(null, [{ total: 1 }]);

            request.get('/stats/aaaToolUsers')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(200);

                chai.expect(res.body.metadata.pagination.total).to.equal(1);

                chai.expect(res.body.results).to.like([{
                    user: 'd111111',
                    start: expectedDate,
                    count: 1
                }]);

                mockMySQL.verify();

                done();
            });
        });

        it('Multiple AAA tool records (MySQL mock)', (done) => {
            let currDate = new Date();
            let expectedDate = new Date(currDate);
            expectedDate.setMonth(expectedDate.getMonth() - 1);

            mockMySQL.expects('execute').withArgs(
                dedent`SELECT SQL_CALC_FOUND_ROWS DATE_FORMAT(CONVERT_TZ(DATE(timestamp), "Australia/Victoria", \
                       "+00:00"), "%Y-%m-%dT%TZ") as start, COUNT(*) as count, userid as user \
                       FROM AAA_usage WHERE timestamp > ? \
                       GROUP BY userid,DATE_FORMAT(CONVERT_TZ(DATE(timestamp), "Australia/Victoria", "+00:00"), \
                       "%Y-%m-%dT%TZ") \
                       ORDER BY start DESC,count DESC,user ASC LIMIT ? OFFSET ?`
            ).once().yields(null, [{
                user: 'd111111',
                start: '2021-05-16T14:00:00Z',
                count: 5
            },
            {
                user: 'd222222',
                start: '2021-05-16T14:00:00Z',
                count: 3
            },
            {
                user: 'd333333',
                start: '2021-05-15T14:00:00Z',
                count: 2
            },
            {
                user: 'd444444',
                start: '2021-05-15T14:00:00Z',
                count: 2
            }]);
            mockMySQL.expects('execute').withArgs('SELECT FOUND_ROWS() as total').once().yields(null, [{ total: 4 }]);

            request.get('/stats/aaaToolUsers?last=30y&interval=d')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(200);

                chai.expect(res.body.metadata.pagination.total).to.equal(4);

                chai.expect(res.body.results).to.like([{
                    user: 'd111111',
                    start: '2021-05-16T14:00:00Z',
                    count: 5
                },
                {
                    user: 'd222222',
                    start: '2021-05-16T14:00:00Z',
                    count: 3
                },
                {
                    user: 'd333333',
                    start: '2021-05-15T14:00:00Z',
                    count: 2
                },
                {
                    user: 'd444444',
                    start: '2021-05-15T14:00:00Z',
                    count: 2
                }]);

                mockMySQL.verify();

                done();
            });
        });
    });


    describe('AAA tool usage total', () => {
        it('Unauthenticated request', async() => {
            let res = await request.get('/stats/aaaToolTotal').unset('Authorization');
            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                const pool = (await import('../../modules/hangarDatabase.js')).default;
                mockMySQL = sinon.mock(pool);

                switch(username) {
                    case 'apiAccess':
                    case 'admin':
                    case 'levelLead':
                        mockMySQL.expects('execute').once().yields(null, []);
                        mockMySQL.expects('execute').withArgs('SELECT FOUND_ROWS() as total').once().yields(null, [{ total: 0 }]);
                        break;
                }


                let token = await helpers.authenticateApi(request, username);
                let res = await request.get('/stats/aaaToolTotal').set('Authorization', `Bearer ${token}`);

                switch(username) {
                    case 'apiAccess':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }

                mockMySQL.verify();
                mockMySQL.restore();
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });


    describe('Quick commands today', () => {
        it('Unauthenticated request', async() => {
            let res = await request.get('/stats/qcsToday').unset('Authorization');
            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                let token = await helpers.authenticateApi(request, username);
                let res = await request.get('/stats/qcsToday').set('Authorization', `Bearer ${token}`);

                switch(username) {
                    case 'apiAccess':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });


    describe('Quick commands top users', () => {
        it('Unauthenticated request', async() => {
            let res = await request.get('/stats/qcsTopUsers').unset('Authorization');
            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                let token = await helpers.authenticateApi(request, username);
                let res = await request.get('/stats/qcsTopUsers').set('Authorization', `Bearer ${token}`);

                switch(username) {
                    case 'apiAccess':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });


    describe('Quick commands week', () => {
        it('Unauthenticated request', async() => {
            let res = await request.get('/stats/qcsWeek').unset('Authorization');
            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                let token = await helpers.authenticateApi(request, username);
                let res = await request.get('/stats/qcsWeek').set('Authorization', `Bearer ${token}`);

                switch(username) {
                    case 'apiAccess':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });


    describe('Quick commands month', () => {
        it('Unauthenticated request', async() => {
            let res = await request.get('/stats/qcsMonth').unset('Authorization');
            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                let token = await helpers.authenticateApi(request, username);
                let res = await request.get('/stats/qcsMonth').set('Authorization', `Bearer ${token}`);

                switch(username) {
                    case 'apiAccess':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });


    describe('Users on', () => {
        it('Unauthenticated request', async() => {
            let res = await request.get('/stats/usersOn').unset('Authorization');
            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                let token = await helpers.authenticateApi(request, username);
                let res = await request.get('/stats/usersOn').set('Authorization', `Bearer ${token}`);

                switch(username) {
                    case 'apiAccess':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });
});
