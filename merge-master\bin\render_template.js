/**
 * Script that is spawned as a child process to render the ejs template
 */

import fs from 'fs';
import ivm from 'isolated-vm';


const RENDER_TEMPLATE_CODE = `
// Helper class to create formatted text rows in templates
class RowCreator {
    constructor(padLength=48, rowLength=16) {
        this.PAD_LENGTH = padLength;
        this.ROW_LENGTH = rowLength;
    }

    format(fieldName, fieldValue, isRow=false) {
        if (typeof fieldValue === 'undefined' || fieldValue === null) {
            // If the field value is null / undefined, return an empty string
            return '';
        } else {
            if (Array.isArray(fieldValue)) {
                if (fieldValue.length) {
                    if (isRow) {
                        let rowString = \`\${fieldName == null ? '' : fieldName}\`.padEnd(this.PAD_LENGTH);
                        for (let i = 0; i < fieldValue.length; i++) {
                            if (i < fieldValue.length - 1) {
                                rowString += \`\${fieldValue[i] == null ? '' : fieldValue[i]}\`.padEnd(this.ROW_LENGTH);
                            } else {
                                rowString += \`\${fieldValue[i] == null ? '' : fieldValue[i]}\`;
                            }
                        }
                        rowString += "\\n";
                        return rowString;
                    } else {
                        let columnString = (fieldName + ":").padEnd(this.PAD_LENGTH) + fieldValue[0] + "\\n";
                        for (let i = 1; i < fieldValue.length; i++) {
                            columnString += "".padEnd(this.PAD_LENGTH) + fieldValue[i] + "\\n";
                        }
                        return columnString;
                    }

                } else {
                    return (fieldName + ":").padEnd(this.PAD_LENGTH) + "None\\n";
                }
            } else {
                return (fieldName + ":").padEnd(this.PAD_LENGTH) + fieldValue + "\\n";
            }
        }
    }
}

const includeOverwriteFunction = () => {
    throw new Error("include() is not an available method for text templates");
};

const noEscapeFunction = (text) => {
    return text;
};

const renderTemplate = (templateName, parameters) => {
    let moduleTemplate = moduleTemplates.find(template => template.name === templateName);
    if (!moduleTemplate) {
        throw new Error("Template module " + templateName + " does not exist or is not active");
    }

    return render(moduleTemplate.template, {
        ...parameters,
        RowCreator: RowCreator,
        data: serviceCheckRecord,
        r: serviceCheckRecord.rulesData,
        s: serviceCheckRecord.sourcesData,
        showRulesData: showRulesData,
        mergeUrl: mergeUrl,
        include: includeOverwriteFunction,
        renderTemplate: null
    }, { escape: noEscapeFunction });
};

let templateData = {
    RowCreator: RowCreator,
    data: serviceCheckRecord,
    r: serviceCheckRecord.rulesData,
    s: serviceCheckRecord.sourcesData,
    showRulesData: showRulesData,
    mergeUrl: mergeUrl
};

let templateDataInput = {};

if (enableTemplateModules) {
    templateDataInput.renderTemplate = renderTemplate;
} else {
    templateDataInput = {
        ...templateDataInput,
        ...templateParameters
    };
    templateDataInput.renderTemplate = null;
}

templateDataInput = {
    ...templateDataInput,
    ...templateData,
    include: includeOverwriteFunction
};

render(templateCode, templateDataInput, { escape: noEscapeFunction });
`;


process.on('message', async(data) => {
    const isolate = new ivm.Isolate({
        memoryLimit: 64
    });
    let renderContext = await isolate.createContext();

    try {
        const ejsSourceCode = (await fs.promises.readFile('./bin/ejs_modified.js')).toString();

        await renderContext.eval(ejsSourceCode, { timeout: 2000 });

        let {
            templateCode,
            serviceCheckRecord,
            moduleTemplates,
            showRulesData,
            mergeUrl,
            enableTemplateModules,
            templateParameters
        } = data;

        await renderContext.global.set('templateCode', templateCode);
        await renderContext.global.set('serviceCheckRecord', new ivm.ExternalCopy(serviceCheckRecord).copyInto());
        await renderContext.global.set('moduleTemplates', new ivm.ExternalCopy(moduleTemplates).copyInto());
        await renderContext.global.set('showRulesData', showRulesData);
        await renderContext.global.set('mergeUrl', mergeUrl);
        await renderContext.global.set('enableTemplateModules', enableTemplateModules);
        await renderContext.global.set('templateParameters', new ivm.ExternalCopy(templateParameters).copyInto());

        let text = await renderContext.eval(RENDER_TEMPLATE_CODE, {
            timeout: 10000
        });

        process.send({
            error: null,
            text: text
        });
    } catch(error) {
        // Error objects cannot be serialized easily to send back to parent process
        // for now it will just send the error string
        process.send({
            error: error.toString(),
            text: null
        });
    } finally {
        renderContext.release();
        if (!isolate.isDisposed) {
            isolate.dispose();
        }

        process.exit(0);
    }
});
