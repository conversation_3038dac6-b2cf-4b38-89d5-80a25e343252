import { useSocketContext } from '../context/SocketContext'

export const useEmit = () => {
    const { socket } = useSocketContext()

    const emit = (event: string, data: any) => {
        if (!socket) {
            console.error(
                `Cannot emit event "${event}": Socket is not connected`
            )
            return
        }
        socket.emit(event, data)
    }

    return { emit }
}
