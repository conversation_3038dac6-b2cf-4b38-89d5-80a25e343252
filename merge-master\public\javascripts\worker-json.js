!function(s){var i,r;void 0!==s.window&&s.document||s.require&&s.define||(s.console||(s.console=function(){var t=Array.prototype.slice.call(arguments,0);postMessage({type:"log",data:t})},s.console.error=s.console.warn=s.console.log=s.console.trace=s.console),((s.window=s).ace=s).onerror=function(t,e,n,i,r){postMessage({type:"error",data:{message:t,data:r.data,file:e,line:n,col:i,stack:r.stack}})},s.normalizeModule=function(t,e){if(-1!==e.indexOf("!")){var n=e.split("!");return s.normalizeModule(t,n[0])+"!"+s.normalizeModule(t,n[1])}if("."==e.charAt(0)){var i=t.split("/").slice(0,-1).join("/");for(e=(i?i+"/":"")+e;-1!==e.indexOf(".")&&r!=e;){var r=e;e=e.replace(/^\.\//,"").replace(/\/\.\//,"/").replace(/[^\/]+\/\.\.\//,"")}}return e},s.require=function(t,e){if(e||(e=t,t=null),!e.charAt)throw new Error("worker.js require() accepts only (parentId, id) as arguments");e=s.normalizeModule(t,e);var n=s.require.modules[e];if(n)return n.initialized||(n.initialized=!0,n.exports=n.factory().exports),n.exports;if(!s.require.tlns)return console.log("unable to load "+e);var i=function(t,e){for(var n=t,i="";n;){var r=e[n];if("string"==typeof r)return r+i;if(r)return r.location.replace(/\/*$/,"/")+(i||r.main||r.name);if(!1===r)return"";var s=n.lastIndexOf("/");if(-1===s)break;i=n.substr(s)+i,n=n.slice(0,s)}return t}(e,s.require.tlns);return".js"!=i.slice(-3)&&(i+=".js"),s.require.id=e,s.require.modules[e]={},importScripts(i),s.require(t,e)},s.require.modules={},s.require.tlns={},s.define=function(e,n,i){var r;2==arguments.length?(i=n,"string"!=typeof e&&(n=e,e=s.require.id)):1==arguments.length&&(i=e,n=[],e=s.require.id),"function"==typeof i?(n.length||(n=["require","exports","module"]),r=function(t){return s.require(e,t)},s.require.modules[e]={exports:{},factory:function(){var e=this,t=i.apply(this,n.slice(0,i.length).map(function(t){switch(t){case"require":return r;case"exports":return e.exports;case"module":return e;default:return r(t)}}));return t&&(e.exports=t),e}}):s.require.modules[e]={exports:i,initialized:!0}},s.define.amd={},require.tlns={},s.initBaseUrls=function(t){for(var e in t)require.tlns[e]=t[e]},s.initSender=function(){function t(){}var e=s.require("ace/lib/event_emitter").EventEmitter,n=s.require("ace/lib/oop");return function(){n.implement(this,e),this.callback=function(t,e){postMessage({type:"call",id:e,data:t})},this.emit=function(t,e){postMessage({type:"event",name:t,data:e})}}.call(t.prototype),new t},i=s.main=null,r=s.sender=null,s.onmessage=function(t){var e,n=t.data;if(n.event&&r)r._signal(n.event,n.data);else if(n.command)if(i[n.command])i[n.command].apply(i,n.args);else{if(!s[n.command])throw new Error("Unknown command:"+n.command);s[n.command].apply(s,n.args)}else{n.init&&(s.initBaseUrls(n.tlns),r=s.sender=s.initSender(),e=require(n.module)[n.classname],i=s.main=new e(r))}})}(this),define("ace/lib/oop",[],function(t,n,e){"use strict";n.inherits=function(t,e){t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}})},n.mixin=function(t,e){for(var n in e)t[n]=e[n];return t},n.implement=function(t,e){n.mixin(t,e)}}),define("ace/range",[],function(t,e,n){"use strict";function s(t,e,n,i){this.start={row:t,column:e},this.end={row:n,column:i}}(function(){this.isEqual=function(t){return this.start.row===t.start.row&&this.end.row===t.end.row&&this.start.column===t.start.column&&this.end.column===t.end.column},this.toString=function(){return"Range: ["+this.start.row+"/"+this.start.column+"] -> ["+this.end.row+"/"+this.end.column+"]"},this.contains=function(t,e){return 0==this.compare(t,e)},this.compareRange=function(t){var e=t.end,n=t.start,i=this.compare(e.row,e.column);return 1==i?1==(i=this.compare(n.row,n.column))?2:0==i?1:0:-1==i?-2:-1==(i=this.compare(n.row,n.column))?-1:1==i?42:0},this.comparePoint=function(t){return this.compare(t.row,t.column)},this.containsRange=function(t){return 0==this.comparePoint(t.start)&&0==this.comparePoint(t.end)},this.intersects=function(t){var e=this.compareRange(t);return-1==e||0==e||1==e},this.isEnd=function(t,e){return this.end.row==t&&this.end.column==e},this.isStart=function(t,e){return this.start.row==t&&this.start.column==e},this.setStart=function(t,e){"object"==typeof t?(this.start.column=t.column,this.start.row=t.row):(this.start.row=t,this.start.column=e)},this.setEnd=function(t,e){"object"==typeof t?(this.end.column=t.column,this.end.row=t.row):(this.end.row=t,this.end.column=e)},this.inside=function(t,e){return 0==this.compare(t,e)&&(!this.isEnd(t,e)&&!this.isStart(t,e))},this.insideStart=function(t,e){return 0==this.compare(t,e)&&!this.isEnd(t,e)},this.insideEnd=function(t,e){return 0==this.compare(t,e)&&!this.isStart(t,e)},this.compare=function(t,e){return this.isMultiLine()||t!==this.start.row?t<this.start.row?-1:t>this.end.row?1:this.start.row===t?e>=this.start.column?0:-1:this.end.row!==t||e<=this.end.column?0:1:e<this.start.column?-1:e>this.end.column?1:0},this.compareStart=function(t,e){return this.start.row==t&&this.start.column==e?-1:this.compare(t,e)},this.compareEnd=function(t,e){return this.end.row==t&&this.end.column==e?1:this.compare(t,e)},this.compareInside=function(t,e){return this.end.row==t&&this.end.column==e?1:this.start.row==t&&this.start.column==e?-1:this.compare(t,e)},this.clipRows=function(t,e){var n,i;return this.end.row>e?n={row:e+1,column:0}:this.end.row<t&&(n={row:t,column:0}),this.start.row>e?i={row:e+1,column:0}:this.start.row<t&&(i={row:t,column:0}),s.fromPoints(i||this.start,n||this.end)},this.extend=function(t,e){var n,i,r=this.compare(t,e);return 0==r?this:(-1==r?n={row:t,column:e}:i={row:t,column:e},s.fromPoints(n||this.start,i||this.end))},this.isEmpty=function(){return this.start.row===this.end.row&&this.start.column===this.end.column},this.isMultiLine=function(){return this.start.row!==this.end.row},this.clone=function(){return s.fromPoints(this.start,this.end)},this.collapseRows=function(){return 0==this.end.column?new s(this.start.row,0,Math.max(this.start.row,this.end.row-1),0):new s(this.start.row,0,this.end.row,0)},this.toScreenRange=function(t){var e=t.documentToScreenPosition(this.start),n=t.documentToScreenPosition(this.end);return new s(e.row,e.column,n.row,n.column)},this.moveBy=function(t,e){this.start.row+=t,this.start.column+=e,this.end.row+=t,this.end.column+=e}}).call(s.prototype),s.fromPoints=function(t,e){return new s(t.row,t.column,e.row,e.column)},s.comparePoints=function(t,e){return t.row-e.row||t.column-e.column},s.comparePoints=function(t,e){return t.row-e.row||t.column-e.column},e.Range=s}),define("ace/apply_delta",[],function(t,e,n){"use strict";e.applyDelta=function(t,e,n){var i,r=e.start.row,s=e.start.column,o=t[r]||"";switch(e.action){case"insert":1===e.lines.length?t[r]=o.substring(0,s)+e.lines[0]+o.substring(s):(i=[r,1].concat(e.lines),t.splice.apply(t,i),t[r]=o.substring(0,s)+t[r],t[r+e.lines.length-1]+=o.substring(s));break;case"remove":var a=e.end.column,c=e.end.row;r===c?t[r]=o.substring(0,s)+o.substring(a):t.splice(r,c-r+1,o.substring(0,s)+t[c].substring(a))}}}),define("ace/lib/event_emitter",[],function(t,e,n){"use strict";function s(){this.propagationStopped=!0}function o(){this.defaultPrevented=!0}var i={};i._emit=i._dispatchEvent=function(t,e){this._eventRegistry||(this._eventRegistry={}),this._defaultHandlers||(this._defaultHandlers={});var n=this._eventRegistry[t]||[],i=this._defaultHandlers[t];if(n.length||i){"object"==typeof e&&e||(e={}),e.type||(e.type=t),e.stopPropagation||(e.stopPropagation=s),e.preventDefault||(e.preventDefault=o),n=n.slice();for(var r=0;r<n.length&&(n[r](e,this),!e.propagationStopped);r++);return i&&!e.defaultPrevented?i(e,this):void 0}},i._signal=function(t,e){var n=(this._eventRegistry||{})[t];if(n){n=n.slice();for(var i=0;i<n.length;i++)n[i](e,this)}},i.once=function(e,n){var i=this;if(this.on(e,function t(){i.off(e,t),n.apply(null,arguments)}),!n)return new Promise(function(t){n=t})},i.setDefaultHandler=function(t,e){var n,i,r,s=this._defaultHandlers;(s=s||(this._defaultHandlers={_disabled_:{}}))[t]&&(n=s[t],(i=s._disabled_[t])||(s._disabled_[t]=i=[]),i.push(n),-1!=(r=i.indexOf(e))&&i.splice(r,1)),s[t]=e},i.removeDefaultHandler=function(t,e){var n,i,r=this._defaultHandlers;r&&(n=r._disabled_[t],r[t]==e?n&&this.setDefaultHandler(t,n.pop()):!n||-1!=(i=n.indexOf(e))&&n.splice(i,1))},i.on=i.addEventListener=function(t,e,n){this._eventRegistry=this._eventRegistry||{};var i=this._eventRegistry[t];return-1==(i=i||(this._eventRegistry[t]=[])).indexOf(e)&&i[n?"unshift":"push"](e),e},i.off=i.removeListener=i.removeEventListener=function(t,e){this._eventRegistry=this._eventRegistry||{};var n,i=this._eventRegistry[t];!i||-1!==(n=i.indexOf(e))&&i.splice(n,1)},i.removeAllListeners=function(t){t||(this._eventRegistry=this._defaultHandlers=void 0),this._eventRegistry&&(this._eventRegistry[t]=void 0),this._defaultHandlers&&(this._defaultHandlers[t]=void 0)},e.EventEmitter=i}),define("ace/anchor",[],function(t,e,n){"use strict";var i=t("./lib/oop"),r=t("./lib/event_emitter").EventEmitter,s=e.Anchor=function(t,e,n){this.$onChange=this.onChange.bind(this),this.attach(t),void 0===n?this.setPosition(e.row,e.column):this.setPosition(e,n)};(function(){function l(t,e,n){var i=n?t.column<=e.column:t.column<e.column;return t.row<e.row||t.row==e.row&&i}i.implement(this,r),this.getPosition=function(){return this.$clipPositionToDocument(this.row,this.column)},this.getDocument=function(){return this.document},this.$insertRight=!1,this.onChange=function(t){var e,n,i,r,s,o,a,c,u;t.start.row==t.end.row&&t.start.row!=this.row||t.start.row>this.row||(n=t,i={row:this.row,column:this.column},r=this.$insertRight,s="insert"==n.action,o=(s?1:-1)*(n.end.row-n.start.row),a=(s?1:-1)*(n.end.column-n.start.column),c=n.start,u=s?c:n.end,e=l(i,c,r)?{row:i.row,column:i.column}:l(u,i,!r)?{row:i.row+o,column:i.column+(i.row==u.row?a:0)}:{row:c.row,column:c.column},this.setPosition(e.row,e.column,!0))},this.setPosition=function(t,e,n){var i,r=n?{row:t,column:e}:this.$clipPositionToDocument(t,e);this.row==r.row&&this.column==r.column||(i={row:this.row,column:this.column},this.row=r.row,this.column=r.column,this._signal("change",{old:i,value:r}))},this.detach=function(){this.document.off("change",this.$onChange)},this.attach=function(t){this.document=t||this.document,this.document.on("change",this.$onChange)},this.$clipPositionToDocument=function(t,e){var n={};return t>=this.document.getLength()?(n.row=Math.max(0,this.document.getLength()-1),n.column=this.document.getLine(n.row).length):t<0?(n.row=0,n.column=0):(n.row=t,n.column=Math.min(this.document.getLine(n.row).length,Math.max(0,e))),e<0&&(n.column=0),n}}).call(s.prototype)}),define("ace/document",[],function(t,e,n){"use strict";function i(t){this.$lines=[""],0===t.length?this.$lines=[""]:Array.isArray(t)?this.insertMergedLines({row:0,column:0},t):this.insert({row:0,column:0},t)}var r=t("./lib/oop"),s=t("./apply_delta").applyDelta,o=t("./lib/event_emitter").EventEmitter,l=t("./range").Range,a=t("./anchor").Anchor;(function(){r.implement(this,o),this.setValue=function(t){var e=this.getLength()-1;this.remove(new l(0,0,e,this.getLine(e).length)),this.insert({row:0,column:0},t)},this.getValue=function(){return this.getAllLines().join(this.getNewLineCharacter())},this.createAnchor=function(t,e){return new a(this,t,e)},0==="aaa".split(/a/).length?this.$split=function(t){return t.replace(/\r\n|\r/g,"\n").split("\n")}:this.$split=function(t){return t.split(/\r\n|\r|\n/)},this.$detectNewLine=function(t){var e=t.match(/^.*?(\r\n|\r|\n)/m);this.$autoNewLine=e?e[1]:"\n",this._signal("changeNewLineMode")},this.getNewLineCharacter=function(){switch(this.$newLineMode){case"windows":return"\r\n";case"unix":return"\n";default:return this.$autoNewLine||"\n"}},this.$autoNewLine="",this.$newLineMode="auto",this.setNewLineMode=function(t){this.$newLineMode!==t&&(this.$newLineMode=t,this._signal("changeNewLineMode"))},this.getNewLineMode=function(){return this.$newLineMode},this.isNewLine=function(t){return"\r\n"==t||"\r"==t||"\n"==t},this.getLine=function(t){return this.$lines[t]||""},this.getLines=function(t,e){return this.$lines.slice(t,e+1)},this.getAllLines=function(){return this.getLines(0,this.getLength())},this.getLength=function(){return this.$lines.length},this.getTextRange=function(t){return this.getLinesForRange(t).join(this.getNewLineCharacter())},this.getLinesForRange=function(t){var e,n;return t.start.row===t.end.row?e=[this.getLine(t.start.row).substring(t.start.column,t.end.column)]:((e=this.getLines(t.start.row,t.end.row))[0]=(e[0]||"").substring(t.start.column),n=e.length-1,t.end.row-t.start.row==n&&(e[n]=e[n].substring(0,t.end.column))),e},this.insertLines=function(t,e){return console.warn("Use of document.insertLines is deprecated. Use the insertFullLines method instead."),this.insertFullLines(t,e)},this.removeLines=function(t,e){return console.warn("Use of document.removeLines is deprecated. Use the removeFullLines method instead."),this.removeFullLines(t,e)},this.insertNewLine=function(t){return console.warn("Use of document.insertNewLine is deprecated. Use insertMergedLines(position, ['', '']) instead."),this.insertMergedLines(t,["",""])},this.insert=function(t,e){return this.getLength()<=1&&this.$detectNewLine(e),this.insertMergedLines(t,this.$split(e))},this.insertInLine=function(t,e){var n=this.clippedPos(t.row,t.column),i=this.pos(t.row,t.column+e.length);return this.applyDelta({start:n,end:i,action:"insert",lines:[e]},!0),this.clonePos(i)},this.clippedPos=function(t,e){var n=this.getLength();void 0===t?t=n:t<0?t=0:n<=t&&(t=n-1,e=void 0);var i=this.getLine(t);return null==e&&(e=i.length),{row:t,column:e=Math.min(Math.max(e,0),i.length)}},this.clonePos=function(t){return{row:t.row,column:t.column}},this.pos=function(t,e){return{row:t,column:e}},this.$clipPosition=function(t){var e=this.getLength();return t.row>=e?(t.row=Math.max(0,e-1),t.column=this.getLine(e-1).length):(t.row=Math.max(0,t.row),t.column=Math.min(Math.max(t.column,0),this.getLine(t.row).length)),t},this.insertFullLines=function(t,e){var n=0,n=(t=Math.min(Math.max(t,0),this.getLength()))<this.getLength()?(e=e.concat([""]),0):(e=[""].concat(e),t--,this.$lines[t].length);this.insertMergedLines({row:t,column:n},e)},this.insertMergedLines=function(t,e){var n=this.clippedPos(t.row,t.column),i={row:n.row+e.length-1,column:(1==e.length?n.column:0)+e[e.length-1].length};return this.applyDelta({start:n,end:i,action:"insert",lines:e}),this.clonePos(i)},this.remove=function(t){var e=this.clippedPos(t.start.row,t.start.column),n=this.clippedPos(t.end.row,t.end.column);return this.applyDelta({start:e,end:n,action:"remove",lines:this.getLinesForRange({start:e,end:n})}),this.clonePos(e)},this.removeInLine=function(t,e,n){var i=this.clippedPos(t,e),r=this.clippedPos(t,n);return this.applyDelta({start:i,end:r,action:"remove",lines:this.getLinesForRange({start:i,end:r})},!0),this.clonePos(i)},this.removeFullLines=function(t,e){t=Math.min(Math.max(0,t),this.getLength()-1);var n=(e=Math.min(Math.max(0,e),this.getLength()-1))==this.getLength()-1&&0<t,i=e<this.getLength()-1,r=n?t-1:t,s=n?this.getLine(r).length:0,o=i?e+1:e,a=i?0:this.getLine(o).length,c=new l(r,s,o,a),u=this.$lines.slice(t,e+1);return this.applyDelta({start:c.start,end:c.end,action:"remove",lines:this.getLinesForRange(c)}),u},this.removeNewLine=function(t){t<this.getLength()-1&&0<=t&&this.applyDelta({start:this.pos(t,this.getLine(t).length),end:this.pos(t+1,0),action:"remove",lines:["",""]})},this.replace=function(t,e){return t instanceof l||(t=l.fromPoints(t.start,t.end)),0===e.length&&t.isEmpty()?t.start:e==this.getTextRange(t)?t.end:(this.remove(t),e?this.insert(t.start,e):t.start)},this.applyDeltas=function(t){for(var e=0;e<t.length;e++)this.applyDelta(t[e])},this.revertDeltas=function(t){for(var e=t.length-1;0<=e;e--)this.revertDelta(t[e])},this.applyDelta=function(t,e){var n="insert"==t.action;(n?t.lines.length<=1&&!t.lines[0]:!l.comparePoints(t.start,t.end))||(n&&2e4<t.lines.length?this.$splitAndapplyLargeDelta(t,2e4):(s(this.$lines,t,e),this._signal("change",t)))},this.$safeApplyDelta=function(t){var e=this.$lines.length;("remove"==t.action&&t.start.row<e&&t.end.row<e||"insert"==t.action&&t.start.row<=e)&&this.applyDelta(t)},this.$splitAndapplyLargeDelta=function(t,e){for(var n=t.lines,i=n.length-e+1,r=t.start.row,s=t.start.column,o=0,a=0;o<i;o=a){a+=e-1;var c=n.slice(o,a);c.push(""),this.applyDelta({start:this.pos(r+o,s),end:this.pos(r+a,s=0),action:t.action,lines:c},!0)}t.lines=n.slice(o),t.start.row=r+o,t.start.column=s,this.applyDelta(t,!0)},this.revertDelta=function(t){this.$safeApplyDelta({start:this.clonePos(t.start),end:this.clonePos(t.end),action:"insert"==t.action?"remove":"insert",lines:t.lines.slice()})},this.indexToPosition=function(t,e){for(var n=this.$lines||this.getAllLines(),i=this.getNewLineCharacter().length,r=e||0,s=n.length;r<s;r++)if((t-=n[r].length+i)<0)return{row:r,column:t+n[r].length+i};return{row:s-1,column:t+n[s-1].length+i}},this.positionToIndex=function(t,e){for(var n=this.$lines||this.getAllLines(),i=this.getNewLineCharacter().length,r=0,s=Math.min(t.row,n.length),o=e||0;o<s;++o)r+=n[o].length+i;return r+t.column}}).call(i.prototype),e.Document=i}),define("ace/lib/lang",[],function(t,e,n){"use strict";e.last=function(t){return t[t.length-1]},e.stringReverse=function(t){return t.split("").reverse().join("")},e.stringRepeat=function(t,e){for(var n="";0<e;)1&e&&(n+=t),(e>>=1)&&(t+=t);return n};var i=/^\s\s*/,r=/\s\s*$/;e.stringTrimLeft=function(t){return t.replace(i,"")},e.stringTrimRight=function(t){return t.replace(r,"")},e.copyObject=function(t){var e={};for(var n in t)e[n]=t[n];return e},e.copyArray=function(t){for(var e=[],n=0,i=t.length;n<i;n++)t[n]&&"object"==typeof t[n]?e[n]=this.copyObject(t[n]):e[n]=t[n];return e},e.deepCopy=function t(e){if("object"!=typeof e||!e)return e;var n;if(Array.isArray(e)){n=[];for(var i=0;i<e.length;i++)n[i]=t(e[i]);return n}if("[object Object]"!==Object.prototype.toString.call(e))return e;for(var i in n={},e)n[i]=t(e[i]);return n},e.arrayToMap=function(t){for(var e={},n=0;n<t.length;n++)e[t[n]]=1;return e},e.createMap=function(t){var e=Object.create(null);for(var n in t)e[n]=t[n];return e},e.arrayRemove=function(t,e){for(var n=0;n<=t.length;n++)e===t[n]&&t.splice(n,1)},e.escapeRegExp=function(t){return t.replace(/([.*+?^${}()|[\]\/\\])/g,"\\$1")},e.escapeHTML=function(t){return(""+t).replace(/&/g,"&#38;").replace(/"/g,"&#34;").replace(/'/g,"&#39;").replace(/</g,"&#60;")},e.getMatchOffsets=function(t,e){var n=[];return t.replace(e,function(t){n.push({offset:arguments[arguments.length-2],length:t.length})}),n},e.deferredCall=function(t){function e(){n=null,t()}var n=null,i=function(t){return i.cancel(),n=setTimeout(e,t||0),i};return(i.schedule=i).call=function(){return this.cancel(),t(),i},i.cancel=function(){return clearTimeout(n),n=null,i},i.isPending=function(){return n},i},e.delayedCall=function(t,e){function n(){r=null,t()}function i(t){null==r&&(r=setTimeout(n,t||e))}var r=null;return i.delay=function(t){r&&clearTimeout(r),r=setTimeout(n,t||e)},(i.schedule=i).call=function(){this.cancel(),t()},i.cancel=function(){r&&clearTimeout(r),r=null},i.isPending=function(){return r},i}}),define("ace/worker/mirror",[],function(t,e,n){"use strict";t("../range").Range;var i=t("../document").Document,a=t("../lib/lang"),r=e.Mirror=function(t){this.sender=t;var r=this.doc=new i(""),s=this.deferredUpdate=a.delayedCall(this.onUpdate.bind(this)),o=this;t.on("change",function(t){var e,n=t.data;if(n[0].start)r.applyDeltas(n);else for(var i=0;i<n.length;i+=2){e=Array.isArray(n[i+1])?{action:"insert",start:n[i],lines:n[i+1]}:{action:"remove",start:n[i],end:n[i+1]},r.applyDelta(e,!0)}if(o.$timeout)return s.schedule(o.$timeout);o.onUpdate()})};(function(){this.$timeout=500,this.setTimeout=function(t){this.$timeout=t},this.setValue=function(t){this.doc.setValue(t),this.deferredUpdate.schedule(this.$timeout)},this.getValue=function(t){this.sender.callback(this.doc.getValue(),t)},this.onUpdate=function(){},this.isPending=function(){return this.deferredUpdate.isPending()}}).call(r.prototype)}),define("ace/mode/json/json_parse",[],function(t,e,n){"use strict";function r(t){throw{name:"SyntaxError",message:t,at:c,text:l}}function s(t){return t&&t!==u&&r("Expected '"+t+"' instead of '"+u+"'"),u=l.charAt(c),c+=1,u}function i(){var t,e="";for("-"===u&&s(e="-");"0"<=u&&u<="9";)e+=u,s();if("."===u)for(e+=".";s()&&"0"<=u&&u<="9";)e+=u;if("e"===u||"E"===u)for(e+=u,s(),"-"!==u&&"+"!==u||(e+=u,s());"0"<=u&&u<="9";)e+=u,s();if(t=+e,!isNaN(t))return t;r("Bad number")}function o(){var t,e,n,i="";if('"'===u)for(;s();){if('"'===u)return s(),i;if("\\"===u)if(s(),"u"===u){for(e=n=0;e<4&&(t=parseInt(s(),16),isFinite(t));e+=1)n=16*n+t;i+=String.fromCharCode(n)}else{if("string"!=typeof h[u])break;i+=h[u]}else{if("\n"==u||"\r"==u)break;i+=u}}r("Bad string")}function a(){for(;u&&u<=" ";)s()}var c,u,l,h={'"':'"',"\\":"\\","/":"/",b:"\b",f:"\f",n:"\n",r:"\r",t:"\t"},f=function(){switch(a(),u){case"{":return function(){var t,e={};if("{"===u){if(s("{"),a(),"}"===u)return s("}"),e;for(;u;){if(t=o(),a(),s(":"),Object.hasOwnProperty.call(e,t)&&r('Duplicate key "'+t+'"'),e[t]=f(),a(),"}"===u)return s("}"),e;s(","),a()}}r("Bad object")}();case"[":return function(){var t=[];if("["===u){if(s("["),a(),"]"===u)return s("]"),t;for(;u;){if(t.push(f()),a(),"]"===u)return s("]"),t;s(","),a()}}r("Bad array")}();case'"':return o();case"-":return i();default:return("0"<=u&&u<="9"?i:function(){switch(u){case"t":return s("t"),s("r"),s("u"),s("e"),!0;case"f":return s("f"),s("a"),s("l"),s("s"),s("e"),!1;case"n":return s("n"),s("u"),s("l"),s("l"),null}r("Unexpected '"+u+"'")})()}};return function(t,o){var e;return l=t,c=0,u=" ",e=f(),a(),u&&r("Syntax error"),"function"==typeof o?function t(e,n){var i,r,s=e[n];if(s&&"object"==typeof s)for(i in s)Object.hasOwnProperty.call(s,i)&&(void 0!==(r=t(s,i))?s[i]=r:delete s[i]);return o.call(e,n,s)}({"":e},""):e}}),define("ace/mode/json_worker",[],function(t,e,n){"use strict";var i=t("../lib/oop"),r=t("../worker/mirror").Mirror,s=t("./json/json_parse"),o=e.JsonWorker=function(t){r.call(this,t),this.setTimeout(200)};i.inherits(o,r),function(){this.onUpdate=function(){var t=this.doc.getValue(),e=[];try{t&&s(t)}catch(t){var n=this.doc.indexToPosition(t.at-1);e.push({row:n.row,column:n.column,text:t.message,type:"error"})}this.sender.emit("annotate",e)}}.call(o.prototype)});