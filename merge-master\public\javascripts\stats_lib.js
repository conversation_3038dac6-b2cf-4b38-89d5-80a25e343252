
    //--------------------------------
    $(document).ready(function () {
        loadReports();
    });

    //--------------------------------
    function loadReports() {
        getUsersOn();
        getScsToday();
        getScsWeek();
        getScsMonth();
        getQcsToday();
        getQcsWeek();
        getQcsMonth();
    }

    //--------------------------------
    function nicely(_what) {
        return _what?_what.toLocaleString():_what;
    }

    //--------------------------------
    function renderReport(_reportId, _dataHeading, _dataSummaryValue, _dataDetailArray, error) {
        // _reportId: existing html report ID to append html
        // _dataSummaryValue: shown are the summary value
        // _dataDetailArray: array of json { head: "abc", val: "123"}
        let jqMain = `${_reportId}`;
        let jqInfoID = jqMain + "Info";
        let jqInfoListID = jqInfoID + "List";

        if (error) {
            $(`#${jqMain}`).html("ERROR: " + error);
            $(`#${jqMain}`).addClass("badge badge-danger");
            return;
        }

        $(`#${jqMain}`).html(
            `<div class="row">
                <a data-toggle="collapse" aria-expanded="false" data-target='#${jqMain}Info'>
                    <span style="color:blue" class="fas fa-plus-square" title="Expand/Collapse"></span>
                    <span>&nbsp;${_dataHeading}:&nbsp;</span>
                    <span class="badge badge-success">${nicely(_dataSummaryValue)}</span>
                </a>
            `
        );

        $(`#${jqMain}`).append(
            `<div id="${jqInfoID}" class="border rounded collapse in">
                <ul id="${jqInfoListID}" class="list-group">
                </ul>
             </div>
            `
        );

        for (let detail of _dataDetailArray) {
            $(`#${jqInfoListID}`).append(
                `<li class="list-group-item d-flex justify-content-between align-items-center">
                    ${detail.head}
                    <span class="badge badge-primary badge-pill">
                    ${nicely(detail.val)}
                    </span>
                </li>
                `
            );
        }

    }

    //--------------------------------
    function getUsersOn() {
        $.ajax({
            cache: false,
            type: "GET",
            url: "/stats/usersOn",
            data: {},
            dataType: "json",
            success: function (sessionsArray) {
                let arr = []
                for (let sess of sessionsArray) {
                    arr.push({ head: sess.userInfo, val: sess.userLogin});
                }
                renderReport("usersOn", "Users Currently Logged On",
                                sessionsArray.length, arr, null)
            },
            error: function(xhr, status, e) {
                console.error(e);
                renderReport("usersOn", "Users Currently Logged On", null, [], e);
            }
        });
    }

    //--------------------------------
    function getScsToday() {
        $.ajax({
            cache: false,
            type: "GET",
            url: "/stats/scsTotal",
            data: {
                last: "1d"
            },
            dataType: "json",
            success: function (response) {
                let serviceCheckCount = response.results[0] ? response.results[0].count : 0;
                $("#scsToday").text(serviceCheckCount);
                $("#scsToday").addClass("badge badge-success");
            },
            error: function (xhr, status, e) {
                console.error(e);
                $("#scsToday").html("ERROR: " + e.message);
                $("#scsToday").addClass("badge badge-danger")
            }
        });
    }

    //--------------------------------
    function getScsWeek() {
        $.ajax({
            cache: false,
            type: "GET",
            url: "/stats/scsUsers",
            data: {
                last: "1w"
            },
            dataType: "json",
            success: function (response) {
                let arr = [];
                let serviceCheckCount = 0;

                for (var i = 0; i < response.results.length; i++) {
                    arr.push({ head: response.results[i].user, val: response.results[i].count });
                    serviceCheckCount += response.results[i].count;
                }
                renderReport("scsWeek", "Top Users this Week",
                                serviceCheckCount, arr, null);
            },
            error: function (xhr, status, e) {
                console.error(e);
                renderReport("scsWeek", "Top Users this Week", null, [], e);
            }
        });
    }

    //--------------------------------
    function getScsMonth() {
        $.ajax({
            cache: false,
            type: "GET",
            url: "/stats/scsUsers",
            data: {
                last: "1m"
            },
            dataType: "json",
            success: function (response) {
                let arr = [];
                let serviceCheckCount = 0;

                for (var i = 0; i < response.results.length; i++) {
                    arr.push({head: response.results[i].user, val: response.results[i].count});
                    serviceCheckCount += response.results[i].count;
                }
                renderReport("scsMonth", "Top Users this Month",
                                serviceCheckCount, arr, null);
            },
            error: function (xhr, status, e) {
                console.error(e);
                renderReport("scsMonth", "Top Users this Month", null, [], e);
            }
        });
    }

    //--------------------------------
    function getQcsToday() {
        $.ajax({
            cache: false,
            type: "GET",
            url: "/stats/qcsToday",
            data: {},
            dataType: "json",
            success: function (qcArray) {
                $("#qcsToday").html(qcArray.length);
                $("#qcsToday").addClass("badge badge-success");
            },
            error: function (xhr, status, e) {
                console.error(e);
                $("#qcsToday").html("ERROR: " + e.message);
                $("#qcsToday").addClass("badge badge-danger");
            }
        });
    }

    //--------------------------------
    function getQcsWeek() {
        $.ajax({
            cache: false,
            type: "GET",
            url: "/stats/qcsWeek",
            data: {},
            dataType: "json",
            success: function (weeksArray) {
                // console.log(weeksArray)
                let arr = []
                weeksArray.forEach(function (week, i) {
                    // console.log('%d: %s', i, week);
                    if (i>0) arr.push({head:(i==1?week._id.weekStart+" (Week Starting)":week._id.weekStart), val:week.documentCount})
                });
                if (weeksArray.length > 0) {
                    renderReport("qcsWeek", "Total Quick Commands this Week",
                        weeksArray[0].documentCount, arr, null);
                } else {
                    renderReport("qcsWeek", "Total Quick Commands this Week",
                                    0, arr, null);
                }
            },
            error: function (xhr, status, e) {
                console.error(e);
                renderReport("qcsWeek", "Total Quick Commands this Week", null, [], e);
            }
        });
    }

    //--------------------------------
    function getQcsMonth() {
        $.ajax({
            cache: false,
            type: "GET",
            url: "/stats/qcsMonth",
            data: {},
            dataType: "json",
            success: function (monthsArray) {
                // console.log(monthsArray)
                const MONTH = [, 'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
                let arr = []
                monthsArray.forEach(function (month, i) {
                    if (i>0) arr.push({head:MONTH[month._id], val:month.documentCount})
                });
                if (monthsArray.length > 0) {
                    renderReport("qcsMonth", "Total Quick Commands this Month",
                                    monthsArray[0].documentCount, arr, null);
                } else {
                    renderReport("qcsMonth", "Total Quick Commands this Month",
                                    0, arr, null);
                }
            },
            error: function (xhr, status, e) {
                console.error(e);
                renderReport("scsMonth", "Total Quick Commands this Month", null, [], e);
            }
        });
    }

