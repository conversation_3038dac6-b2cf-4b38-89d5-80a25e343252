import { CollapsablePanel } from '../../../../components/collapsablePanel/CollapsablePanel'
import { DetailField } from '../../../../components/detailsPanel/DetailField'
import { Panel } from '../../../../components/panel/Panel'
import { NBNOutage } from '../../../../infrastructure/models'
import styles from './PlannedOutagesField.module.scss'
interface NBNOutagesFieldProps {
    value: NBNOutage[]
}

export const NBNOutagesField = ({ value }: NBNOutagesFieldProps) => {
    if (value === null || value === undefined) {
        return (
            <Panel>
                <DetailField label="NBN Service Health Check Outages" value="Unknown" />
            </Panel>
        )
    }

    if (value.length === 0) {
        return (
            <Panel>
                <DetailField label="NBN Service Health Check Outages" value="None" />
            </Panel>
        )
    }

    return (
        <CollapsablePanel
            headerElement={<DetailField label="NBN Service Health Check Outages" value="" />}
            canOpen={value.length > 0}
            itemCount={value.length > 1 ? value.length - 1 : value.length}
        >
            <div className={styles.plannedOutages}>
                <div className={styles.outageRow}>
                    {value.slice(1).map((outage, index) => (
                        <div key={`${outage.outage_type || 'unknown'}-${index}`}>
                            <DetailField
                                label={outage.outage_type || 'Unknown Outage'}
                                value={outage.value || 'Unknown ID'}
                                inline={true}
                            />
                        </div>
                    ))}
                </div>
            </div>
        </CollapsablePanel>
    )
}
