@use '@able/web/src/index' as able;
.dateRange {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  
    .inputs {
      display: flex;
      gap: 1rem;
      align-items: center;
  
      input[type="datetime-local"] {
        padding: 0.5rem 0.75rem;
        border: 0.125rem solid able.color(materialBaseBrandPrimary);
        border-radius: 4px;
        background-color: #fff;
        color: able.color(materialBaseBrandPrimary);
        font-size: 1rem;
        font-weight: 800; // Makes the text a bit bolder

        transition: border-color 0.2s, box-shadow 0.2s;
  
        &:focus {
          border-color: #0070f3;
          box-shadow: 0 0 0 2px rgba(0, 112, 243, 0.2);
          outline: none;
        }
      }
    }

    .lastButtons {
        display: flex;
        align-items: center;
        gap: 1rem;
    }
  
    .error {
      color: #ff0033;
      font-size: 0.875rem;
    }
  }
  