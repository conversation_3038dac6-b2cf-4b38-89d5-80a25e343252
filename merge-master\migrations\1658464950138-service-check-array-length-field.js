/**
 * Note: This migration file was created when 'migrate-mongoose' was the npm package
 * used to perform MongoDB migrations, since the 'migrate' npm package is now used
 * the implementation may not be compatible and all functionality is commented out
 */

// const mongoose = require('mongoose');

// const migrate = require('./migrate');

// const serviceCheckSchema = new mongoose.Schema({
//     id: { type: String, index: true, immutable: true },
//     fnn: { type: String, index: true },
//     phoneNumber: { type: String, default: null, index: true },
//     billingFNN: { type: String, default: null },
//     carriageFNN: { type: String, default: null, index: true },
//     MDNFNN: { type: String, default: null },
//     carriageType: { type: String, default: null, index: true },
//     nbnServiceType: { type: String, default: null },
//     nbnAccessType: { type: String, default: null },
//     nbnSubAccessType: { type: String, default: null },
//     deviceName: { type: String, default: null, index: true },
//     CIDN: { type: String, default: null },
//     address: { type: String, default: null },
//     OffshoreResources: { type: String, default: null },
//     serviceType: { type: String, default: null, index: true },
//     siiamCases: {
//         type: [{
//             type: String
//         }],
//         default: []
//     },
//     siiamCasesLength: { type: Number, default: 0, select: false, index: true },
//     serviceCentralIncidents: {
//         type: [{
//             type: String
//         }],
//         default: []
//     },
//     serviceCentralIncidentsLength: { type: Number, default: 0, select: false, index: true },
//     status: { type: String, default: null, index: true },
//     endedOn: { type: Date, default: null },
//     durationMilliSec: { type: Number, default: 0 },
//     input: {
//         _id: false,
//         searchFNN: { type: String, default: null },
//         suite: { type: String, default: "standard" },
//         level: { type: Number, min: 0, max: 6, default: 0 },
//         carriageFNN: { type: String, default: null },
//         carriageType: { type: String, default: null },
//         deviceIP: { type: String, default: null },
//         deviceName: { type: String, default: null },
//         idType: { type: String, default: null }
//     },
//     additionalParameters: { type: Object, default: {} },
//     rulesData: { type: Object, default: {} },
//     sourcesData: { type: Object, default: {} },
//     sourcesMetadata: { type: Object, default: {} },
//     errorMessage: { type: String, default: null },
//     createdBy: { type: String, required: true, immutable: true, default: "unknown", index: true },
//     createdOn: { type: Date, required: true, immutable: true, default: Date.now, index: true }
// }, { minimize: false });

// const ServiceCheckModel = mongoose.model('servicechecks', serviceCheckSchema);


/**
 * Make any changes you need to make to the database here
 */
async function up () {
//     // Write migration here
//     await migrate.connect();

//     for await (const record of ServiceCheckModel.find({})) {
//         if (Array.isArray(record.siiamCases)) {
//             record.siiamCasesLength = record.siiamCases.length;
//         }

//         if (Array.isArray(record.serviceCentralIncidents)) {
//             record.serviceCentralIncidentsLength = record.serviceCentralIncidents.length;
//         }

//         await record.save();
//     }
}

/**
 * Make any changes that UNDO the up function side effects here (if possible)
 */
async function down () {
    // Write migration here
    // await migrate.connect();

    // for await (const record of ServiceCheckModel.find({})) {
    //     record.siiamCasesLength = undefined;
    //     record.serviceCentralIncidentsLength = undefined;

    //     await record.save();
    // }
}

module.exports = { up, down };
