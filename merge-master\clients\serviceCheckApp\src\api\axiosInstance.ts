import axios from 'axios'
import { API_BASE_URL } from './apiUrl'

const axiosInstance = axios.create({
    baseURL: API_BASE_URL,
    timeout: 600000, // Set timeout for requests
    headers: {
        'Content-Type': 'application/json',
    },
})

// Interceptors for request/response handling
axiosInstance.interceptors.request.use(
    (config) => config,
    (error) =>
        Promise.reject(
            error instanceof Error ? error : new Error(String(error))
        )
)

axiosInstance.interceptors.response.use(
    (response) => response,
    (error) =>
        Promise.reject(
            error instanceof Error ? error : new Error(String(error))
        )
)

export default axiosInstance
