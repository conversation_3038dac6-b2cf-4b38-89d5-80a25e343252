
import express from 'express';
import fs from 'fs';
import _ from 'lodash';
import swaggerUi from 'swagger-ui-express';
import swaggerJSDoc from 'swagger-jsdoc';

import auth from '../modules/auth.js';
import { AuthorizationRoles } from '../modules/enumerations.js';
import logger from '../modules/logger.js';

const jsdocApiOptions = JSON.parse(await fs.promises.readFile('./config/swagger.json'));
const router = express.Router();

let swaggerSpec;
let swaggerSpecOkapi;

try {
    swaggerSpec = swaggerJSDoc(jsdocApiOptions);

    swaggerSpecOkapi = _.cloneDeep(swaggerSpec);
    swaggerSpecOkapi.servers = _.get(global, ['gConfig', 'swaggerOkapiEndpoints'], []);
    swaggerSpecOkapi.components.securitySchemes = {
        MergeToken: {
            type: "apiKey",
            in: "header",
            name: "Authenticate"
        }
    };
    swaggerSpecOkapi.info.title = "Merge (OKAPI)";
    swaggerSpecOkapi.info.description = "[Download swagger specification](/api-docs-okapi/download)";

    let okapiPaths = {};
    // Adds all paths starting with /api/ to the OKAPI swagger paths, but removes /api/ from the start so the URI is accurate
    // Not a perfect implementation as it requires the documented paths to start with exactly "/api/"
    for (let path in swaggerSpec.paths) {
        if (path.match(/^\/api\//)) {
            okapiPaths[path.replace(/^\/api\//, '/')] = swaggerSpec.paths[path];
        }
    }
    swaggerSpecOkapi.paths = okapiPaths;
} catch (error) {
    logger.error(error);
}


function useSchema(swaggerSchema, swaggerOptions) {
    return function(...args) {
        swaggerUi.setup(swaggerSchema, swaggerOptions)(...args);
    }
}


router.use('/api-docs', swaggerUi.serve);
router.get('/api-docs', auth.authorizeForRoles([
    AuthorizationRoles.readOnly,
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead,
    AuthorizationRoles.apiAccess
], true), useSchema(swaggerSpec, { explorer: false }));


// Endpoint to download
router.get('/api-docs/download', auth.authorizeForRoles([
    AuthorizationRoles.readOnly,
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead,
    AuthorizationRoles.apiAccess
], true), function(req, res) {
    let dateStr = new Date().toISOString().replace(/[^0-9]/g, '');
    res.set("Content-Disposition", `attachment;filename=Merge_swagger_${dateStr}.json`);
    res.send(JSON.stringify(swaggerSpec, null, 4));
});

router.use('/api-docs-okapi', swaggerUi.serve);
router.get('/api-docs-okapi', auth.authorizeForRoles([
    AuthorizationRoles.readOnly,
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead,
    AuthorizationRoles.apiAccess
], true), useSchema(swaggerSpecOkapi, { explorer: false, swaggerOptions: { supportedSubmitMethods: [] }}));


router.get('/api-docs-okapi/download', auth.authorizeForRoles([
    AuthorizationRoles.readOnly,
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead,
    AuthorizationRoles.apiAccess
], true), function(req, res) {
    let dateStr = new Date().toISOString().replace(/[^0-9]/g, '');
    res.set("Content-Disposition", `attachment;filename=Merge_swagger_okapi_${dateStr}.json`);
    res.send(JSON.stringify(swaggerSpecOkapi, null, 4));
});


export default router;
