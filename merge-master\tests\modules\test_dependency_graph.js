'use strict';

import chai from 'chai';
import chaiHttp from 'chai-http';
import chaiLike from 'chai-like';
import chaiDateTime from 'chai-datetime';
import chaiAsPromised from 'chai-as-promised';
import _ from 'lodash';
import util from 'util';

import config from '../config.js';
import helpers from '../helpers.js';
import Rule from '../../db/model/rule.js';
import Source from '../../db/model/source.js';
import { DependencyGraph } from '../../modules/dependencyGraph.js';

chaiLike.extend(helpers.dateFieldCheck);
chaiLike.extend(helpers.regexCheck);
chai.use(chaiLike);
chai.use(chaiHttp);
chai.use(chaiDateTime);
chai.use(chaiAsPromised);


describe('Create dependency graph', () => {
    it('Create empty graph', () => {
        let graph = new DependencyGraph([], []);

        chai.expect(graph.edges).to.eql([]);
        chai.expect(graph.vertices).to.eql([]);
    });

    it('Create graph with 1 node, 0 edges', () => {
        let ruleList = [
            new Rule({
                name: "MDR001"
            })
        ];
        let sourceList = [];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.edges).to.eql([
            []
        ]);
        chai.expect(graph.vertices).to.eql([
            ...ruleList,
            ...sourceList
        ]);
    });

    it('Create graph with 1 rule node, 1 source node, 0 edges', () => {
        let ruleList = [
            new Rule({
                name: "MDR001"
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName"
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.edges).to.eql([
            [],
            []
        ]);
        chai.expect(graph.vertices).to.eql([
            ...ruleList,
            ...sourceList
        ]);
    });

    it('Create graph with 2 rule nodes, 0 edges', () => {
        let ruleList = [
            new Rule({
                name: "MDR001"
            }),
            new Rule({
                name: "MDR002"
            })
        ];
        let sourceList = [];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.edges).to.eql([
            [],
            []
        ]);
        chai.expect(graph.vertices).to.eql([
            ...ruleList,
            ...sourceList
        ]);
    });

    it('Create graph with 2 rule nodes, 1 edge from rule to rule', () => {
        let ruleList = [
            new Rule({
                name: "MDR001"
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ]
            })
        ];
        let sourceList = [];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.edges).to.eql([
            [1],
            []
        ]);
        chai.expect(graph.vertices).to.eql([
            ...ruleList,
            ...sourceList
        ]);
    });

    it('Create graph with 2 source nodes, 1 edge from source to source', () => {
        let ruleList = [];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName"
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.edges).to.eql([
            [1],
            []
        ]);
        chai.expect(graph.vertices).to.eql([
            ...ruleList,
            ...sourceList
        ]);
    });

    it('Create graph with 1 rule node, 1 source node, 1 edge from source to rule', () => {
        let ruleList = [
            new Rule({
                name: "MDR001"
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR001"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.edges).to.eql([
            [1],
            []
        ]);
        chai.expect(graph.vertices).to.eql([
            ...ruleList,
            ...sourceList
        ]);
    });

    it('Create graph with 1 rule node, 1 source node, 1 edge from rule to source', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preSources: [
                    "SourceName1"
                ]
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName"
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.edges).to.eql([
            [],
            [0]
        ]);
        chai.expect(graph.vertices).to.eql([
            ...ruleList,
            ...sourceList
        ]);
    });

    it('Create graph with 1 rule node, 1 source node, edges to non-existant rules / sources', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [
                    "MDR002",
                ],
                preSources: [
                    "SourceName2"
                ]
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR002",
                ],
                preSources: [
                    "SourceName2"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.edges).to.eql([
            [],
            []
        ]);
        chai.expect(graph.vertices).to.eql([
            ...ruleList,
            ...sourceList
        ]);
    });

    it('Create graph with 1 rule node, 1 source node, self directed edge', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [
                    "MDR001",
                ],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        // Self directed edges should be ignored
        chai.expect(graph.edges).to.eql([
            [],
            []
        ]);
        chai.expect(graph.vertices).to.eql([
            ...ruleList,
            ...sourceList
        ]);
    });

    it('Create graph with multiple nodes, test graph creation 1', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
            }),
            new Rule({
                name: "MDR002",
            }),
            new Rule({
                name: "MDR003",
            }),
            new Rule({
                name: "MDR004",
            }),
            new Rule({
                name: "MDR005",
            }),
            new Rule({
                name: "MDR006"
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName"
            }),
            new Source({
                name: "SourceName2",
                api: "APIName"
            }),
            new Source({
                name: "SourceName3",
                api: "APIName"
            }),
            new Source({
                name: "SourceName4",
                api: "APIName"
            }),
            new Source({
                name: "SourceName5",
                api: "APIName"
            }),
            new Source({
                name: "SourceName6",
                api: "APIName"
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.edges).to.eql([
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            []
        ]);
        chai.expect(graph.vertices).to.eql([
            ...ruleList,
            ...sourceList
        ]);
    });

    it('Create graph with multiple nodes, test graph creation 2', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [
                    "MDR006"
                ],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: [
                    "SourceName6",
                ]
            }),
            new Rule({
                name: "MDR004",
                preRules: [
                    "MDR003"
                ],
                preSources: [
                    "SourceName2",
                    "SourceName3"
                ]
            }),
            new Rule({
                name: "MDR005",
                preRules: [
                    "MDR001",
                    "MDR003"
                ],
                preSources: [
                    "SourceName1",
                    "SourceName2",
                    "SourceName4"
                ]
            }),
            new Rule({
                name: "MDR006",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR006"
                ],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [
                    "MDR001",
                    "MDR006"
                ],
                preSources: []
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName1",
                    "SourceName2"
                ]
            }),
            new Source({
                name: "SourceName4",
                api: "APIName",
                preRules: [
                    "MDR003",
                    "MDR004",
                    "MDR001"
                ],
                preSources: [
                    "SourceName2"
                ]
            }),
            new Source({
                name: "SourceName5",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName6",
                api: "APIName",
                preRules: [
                    "MDR005"
                ],
                preSources: [
                    "SourceName4"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.edges).to.eql([
            [4, 7, 9],
            [],
            [3, 4, 9],
            [9],
            [11],
            [0, 6, 7],
            [4, 8],
            [3, 4, 8, 9],
            [3],
            [4, 11],
            [],
            [2]
        ]);
        chai.expect(graph.vertices).to.eql([
            ...ruleList,
            ...sourceList
        ]);
    });

    it('Create graph with multiple nodes, test graph creation 3', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [
                    "MDR001",
                    "MDR002",
                    "MDR003"
                ],
                preSources: [
                    "SourceName1",
                    "SourceName2",
                    "SourceName3"
                ]
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001",
                    "MDR002",
                    "MDR003"
                ],
                preSources: [
                    "SourceName1",
                    "SourceName2",
                    "SourceName3"
                ]
            }),
            new Rule({
                name: "MDR003",
                preRules: [
                    "MDR001",
                    "MDR002",
                    "MDR003"
                ],
                preSources: [
                    "SourceName1",
                    "SourceName2",
                    "SourceName3"
                ]
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR001",
                    "MDR002",
                    "MDR003"
                ],
                preSources: [
                    "SourceName1",
                    "SourceName2",
                    "SourceName3"
                ]
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [
                    "MDR001",
                    "MDR002",
                    "MDR003"
                ],
                preSources: [
                    "SourceName1",
                    "SourceName2",
                    "SourceName3"
                ]
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [
                    "MDR001",
                    "MDR002",
                    "MDR003"
                ],
                preSources: [
                    "SourceName1",
                    "SourceName2",
                    "SourceName3"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.edges).to.eql([
            [1, 2, 3, 4, 5],
            [0, 2, 3, 4, 5],
            [0, 1, 3, 4, 5],
            [0, 1, 2, 4, 5],
            [0, 1, 2, 3, 5],
            [0, 1, 2, 3, 4]
        ]);
        chai.expect(graph.vertices).to.eql([
            ...ruleList,
            ...sourceList
        ]);
    });

    it('Create graph with multiple nodes, test graph creation 4', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [
                    "MDR006"
                ],
                preSources: [
                    "SourceName7"
                ]
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR007",
                    "MDR008"
                ],
                preSources: [
                    "SourceName9"
                ]
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: [
                    "SourceName6",
                    "SourceName7"
                ]
            }),
            new Rule({
                name: "MDR004",
                preRules: [
                    "MDR003",
                    "MDR007"
                ],
                preSources: [
                    "SourceName2",
                    "SourceName3"
                ]
            }),
            new Rule({
                name: "MDR005",
                preRules: [
                    "MDR001",
                    "MDR003",
                    "MTR001"
                ],
                preSources: [
                    "SourceName1",
                    "SourceName2",
                    "SourceName4",
                    "SourceName8"
                ]
            }),
            new Rule({
                name: "MDR006",
                preRules: [
                    "MDR007",
                    "MDR008"
                ],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR006",
                    "MDR007",
                    "MDR008"
                ],
                preSources: [
                    "SourceName7",
                    "SourceName8"
                ]
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [
                    "MDR001",
                    "MDR006"
                ],
                preSources: []
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [
                    "MDR007",
                ],
                preSources: [
                    "SourceName1",
                    "SourceName2"
                ]
            }),
            new Source({
                name: "SourceName4",
                api: "APIName",
                preRules: [
                    "MDR003",
                    "MDR004",
                    "MDR001"
                ],
                preSources: [
                    "SourceName8",
                    "SourceName9",
                    "SourceName2"
                ]
            }),
            new Source({
                name: "SourceName5",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName8"
                ]
            }),
            new Source({
                name: "SourceName6",
                api: "APIName",
                preRules: [
                    "MDR005"
                ],
                preSources: [
                    "SourceName4"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.edges).to.eql([
            [4, 7, 9],
            [],
            [3, 4, 9],
            [9],
            [11],
            [0, 6, 7],
            [4, 8],
            [3, 4, 8, 9],
            [3],
            [4, 11],
            [],
            [2]
        ]);
        chai.expect(graph.vertices).to.eql([
            ...ruleList,
            ...sourceList
        ]);
    });
});


describe('Topological sort', () => {
    it('Graph with no vertices', () => {
        let ruleList = [];
        let sourceList = [];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([
            ...ruleList,
            ...sourceList
        ]);
    });

    it('Graph with rules and sources with no edges test 1', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([
            ...ruleList,
            ...sourceList
        ]);
    });

    it('Graph with rules and sources with no edges test 2', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([
            ...ruleList,
            ...sourceList
        ]);
    });

    it('Graph with rules and sources with no edges test 3', () => {
        let ruleList = [];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([
            ...ruleList,
            ...sourceList
        ]);
    });

    it('Graph with rules and sources with no edges test 4', () => {
        let ruleList = [];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([
            ...ruleList,
            ...sourceList
        ]);
    });

    it('Graph with rules and sources with no edges test 5', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([
            ...ruleList,
            ...sourceList
        ]);
    });

    it('Graph with 2 rules with dependency test 1', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            })
        ];
        let sourceList = [];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([
            ruleList[0],
            ruleList[1]
        ]);
    });

    it('Graph with 2 rules with dependency test 2', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [
                    "MDR002"
                ],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([
            ruleList[1],
            ruleList[0]
        ]);
    });

    it('Graph with 2 rules with circular dependency (cyclic graph)', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [
                    "MDR002"
                ],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            })
        ];
        let sourceList = [];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([]);
    });

    it('Graph with 2 sources with dependency test 1', () => {
        let ruleList = [];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([
            sourceList[0],
            sourceList[1]
        ]);
    });

    it('Graph with 2 sources with dependency test 2', () => {
        let ruleList = [];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName2"
                ]
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([
            sourceList[1],
            sourceList[0]
        ]);
    });

    it('Graph with 2 sources with circular dependency (cyclic graph)', () => {
        let ruleList = [];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName2"
                ]
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([]);
    });

    it('Graph with 1 rule and 1 source with dependency test 1', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([
            ruleList[0],
            sourceList[0]
        ]);
    });

    it('Graph with 1 rule and 1 source with dependency test 2', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([
            sourceList[0],
            ruleList[0]
        ]);
    });

    it('Graph with 1 rule and 1 source with circular dependency (cyclic graph)', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([]);
    });

    it('Graph with 1 rule and 1 source with self dependency', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR001"
                ],
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([
            ruleList[0],
            sourceList[0]
        ]);
    });

    it('Graph with multiple vertices and is cyclic test 1', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [
                    "MDR002"
                ],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([]);
    });

    it('Graph with multiple vertices and is cyclic test 2', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: [
                    "SourceName1"
                ]
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR002"
                ],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([
            ruleList[0]
        ]);
    });

    it('Graph with multiple vertices and is cyclic test 3', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            }),
            new Rule({
                name: "MDR003",
                preRules: [
                    "MDR002"
                ],
                preSources: [
                    "SourceName2"
                ]
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR002",
                    "MDR003"
                ],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([
            ruleList[0],
            ruleList[1]
        ]);
    });

    it('Graph with multiple vertices and is cyclic test 4', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [
                    "MDR003"
                ],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Rule({
                name: "MDR004",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR001",
                    "MDR002",
                    "MDR004"
                ],
                preSources: [
                    "SourceName2",
                    "SourceName3"
                ]
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName4",
                api: "APIName",
                preRules: [],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([
            ruleList[1],
            ruleList[3],
            sourceList[1],
            sourceList[2],
            sourceList[3]
        ]);
    });

    it('Graph with multiple vertices and is cyclic test 5', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: [
                    "SourceName1",
                    "SourceName4"
                ]
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: [
                    "SourceName2"
                ]
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Rule({
                name: "MDR004",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [
                    "MDR004"
                ],
                preSources: []
            }),
            new Source({
                name: "SourceName4",
                api: "APIName",
                preRules: [
                    "MDR002"
                ],
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([
            ruleList[3],
            sourceList[0],
            sourceList[2],
            ruleList[2],
            sourceList[1]
        ]);
    });

    it('Graph with multiple vertices and is cyclic test 6', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: [
                    "SourceName1",
                    "SourceName4"
                ]
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: [
                    "SourceName2"
                ]
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Rule({
                name: "MDR004",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [
                    "MDR001"
                ],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [
                    "MDR004"
                ],
                preSources: []
            }),
            new Source({
                name: "SourceName4",
                api: "APIName",
                preRules: [
                    "MDR002"
                ],
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([
            ruleList[3],
            sourceList[0],
            sourceList[2],
            ruleList[2]
        ]);
    });

    it('Graph with multiple vertices and is acyclic test 1', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: [
                    "SourceName1"
                ]
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([
            ruleList[0],
            sourceList[0],
            ruleList[1]
        ]);
    });

    it('Graph with multiple vertices and is acyclic test 2', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR002"
                ],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([
            ruleList[0],
            ruleList[1],
            sourceList[0]
        ]);
    });

    it('Graph with multiple vertices and is acyclic test 3', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            }),
            new Rule({
                name: "MDR003",
                preRules: [
                    "MDR002"
                ],
                preSources: [
                    "SourceName2"
                ]
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR002"
                ],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([
            ruleList[0],
            ruleList[1],
            sourceList[0],
            sourceList[1],
            ruleList[2]
        ]);
    });

    it('Graph with multiple vertices and is acyclic test 4', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            }),
            new Rule({
                name: "MDR003",
                preRules: [
                    "MDR002"
                ],
                preSources: [
                    "SourceName2"
                ]
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR002",
                    "MDR003"
                ],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([
            ruleList[0],
            sourceList[1],
            ruleList[1],
            ruleList[2],
            sourceList[0]
        ]);
    });

    it('Graph with multiple vertices and is acyclic test 5', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [
                    "MDR003"
                ],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Rule({
                name: "MDR002",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Rule({
                name: "MDR004",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR002",
                    "MDR004"
                ],
                preSources: [
                    "SourceName2",
                    "SourceName3"
                ]
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName4",
                api: "APIName",
                preRules: [],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([
            ruleList[1],
            ruleList[3],
            sourceList[1],
            sourceList[2],
            sourceList[3],
            sourceList[0],
            ruleList[2],
            ruleList[0]
        ]);
    });

    it('Graph with multiple vertices and is acyclic test 6', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Rule({
                name: "MDR002",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Rule({
                name: "MDR004",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR002",
                    "MDR004"
                ],
                preSources: [
                    "SourceName2",
                    "SourceName3"
                ]
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName4",
                api: "APIName",
                preRules: [],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([
            ruleList[1],
            ruleList[3],
            sourceList[1],
            sourceList[2],
            sourceList[3],
            sourceList[0],
            ruleList[0],
            ruleList[2]
        ]);
    });

    it('Graph with multiple vertices and is acyclic test 7', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: [
                    "SourceName1",
                    "SourceName4"
                ]
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: [
                    "SourceName2"
                ]
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Rule({
                name: "MDR004",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [
                    "MDR004"
                ],
                preSources: []
            }),
            new Source({
                name: "SourceName4",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([
            ruleList[3],
            sourceList[0],
            sourceList[2],
            ruleList[2],
            sourceList[1],
            sourceList[3],
            ruleList[0],
            ruleList[1]
        ]);
    });

    it('Graph with multiple vertices and is acyclic test 8', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: [
                    "SourceName1",
                    "SourceName4"
                ]
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: [
                    "SourceName2"
                ]
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Rule({
                name: "MDR004",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [
                    "MDR001"
                ],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [
                    "MDR004"
                ],
                preSources: []
            }),
            new Source({
                name: "SourceName4",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.topologicalSort()).to.eql([
            ruleList[3],
            sourceList[0],
            sourceList[2],
            ruleList[2],
            sourceList[3],
            ruleList[0],
            sourceList[1],
            ruleList[1]
        ]);
    });
});

describe('Acyclic graph check', () => {
    it('Graph with no vertices', () => {
        let ruleList = [];
        let sourceList = [];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(true);
    });

    it('Graph with rules and sources with no edges test 1', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(true);
    });

    it('Graph with rules and sources with no edges test 2', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(true);
    });

    it('Graph with rules and sources with no edges test 3', () => {
        let ruleList = [];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(true);
    });

    it('Graph with rules and sources with no edges test 4', () => {
        let ruleList = [];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(true);
    });

    it('Graph with rules and sources with no edges test 5', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(true);
    });

    it('Graph with 2 rules with dependency test 1', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            })
        ];
        let sourceList = [];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(true);
    });

    it('Graph with 2 rules with dependency test 2', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [
                    "MDR002"
                ],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            })
        ];
        let sourceList = [];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(false);
    });

    it('Graph with 2 sources with dependency test 1', () => {
        let ruleList = [];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(true);
    });

    it('Graph with 2 sources with circular dependency test 2', () => {
        let ruleList = [];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName2"
                ]
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(false);
    });

    it('Graph with 1 rule and 1 source with dependency test 1', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(true);
    });

    it('Graph with 1 rule and 1 source with dependency test 2', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(false);
    });

    it('Graph with 1 rule and 1 source with self dependency', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR001"
                ],
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(true);
    });

    it('Graph with multiple vertices and is cyclic test 1', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [
                    "MDR002"
                ],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(false);
    });

    it('Graph with multiple vertices and is cyclic test 2', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: [
                    "SourceName1"
                ]
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR002"
                ],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(false);
    });

    it('Graph with multiple vertices and is cyclic test 3', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            }),
            new Rule({
                name: "MDR003",
                preRules: [
                    "MDR002"
                ],
                preSources: [
                    "SourceName2"
                ]
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR002",
                    "MDR003"
                ],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(false);
    });

    it('Graph with multiple vertices and is cyclic test 4', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [
                    "MDR003"
                ],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Rule({
                name: "MDR004",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR001",
                    "MDR002",
                    "MDR004"
                ],
                preSources: [
                    "SourceName2",
                    "SourceName3"
                ]
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName4",
                api: "APIName",
                preRules: [],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(false);
    });

    it('Graph with multiple vertices and is cyclic test 5', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: [
                    "SourceName1",
                    "SourceName4"
                ]
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: [
                    "SourceName2"
                ]
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Rule({
                name: "MDR004",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [
                    "MDR004"
                ],
                preSources: []
            }),
            new Source({
                name: "SourceName4",
                api: "APIName",
                preRules: [
                    "MDR002"
                ],
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(false);
    });

    it('Graph with multiple vertices and is cyclic test 6', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: [
                    "SourceName1",
                    "SourceName4"
                ]
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: [
                    "SourceName2"
                ]
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Rule({
                name: "MDR004",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [
                    "MDR001"
                ],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [
                    "MDR004"
                ],
                preSources: []
            }),
            new Source({
                name: "SourceName4",
                api: "APIName",
                preRules: [
                    "MDR002"
                ],
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(false);
    });

    it('Graph with multiple vertices and is acyclic test 1', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: [
                    "SourceName1"
                ]
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(true);
    });

    it('Graph with multiple vertices and is acyclic test 2', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR002"
                ],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(true);
    });

    it('Graph with multiple vertices and is acyclic test 3', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            }),
            new Rule({
                name: "MDR003",
                preRules: [
                    "MDR002"
                ],
                preSources: [
                    "SourceName2"
                ]
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR002"
                ],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(true);
    });

    it('Graph with multiple vertices and is acyclic test 4', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            }),
            new Rule({
                name: "MDR003",
                preRules: [
                    "MDR002"
                ],
                preSources: [
                    "SourceName2"
                ]
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR002",
                    "MDR003"
                ],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(true);
    });

    it('Graph with multiple vertices and is acyclic test 5', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [
                    "MDR003"
                ],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Rule({
                name: "MDR002",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Rule({
                name: "MDR004",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR002",
                    "MDR004"
                ],
                preSources: [
                    "SourceName2",
                    "SourceName3"
                ]
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName4",
                api: "APIName",
                preRules: [],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(true);
    });

    it('Graph with multiple vertices and is acyclic test 6', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Rule({
                name: "MDR002",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Rule({
                name: "MDR004",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR002",
                    "MDR004"
                ],
                preSources: [
                    "SourceName2",
                    "SourceName3"
                ]
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName4",
                api: "APIName",
                preRules: [],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(true);
    });

    it('Graph with multiple vertices and is acyclic test 7', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: [
                    "SourceName1",
                    "SourceName4"
                ]
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: [
                    "SourceName2"
                ]
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Rule({
                name: "MDR004",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [
                    "MDR004"
                ],
                preSources: []
            }),
            new Source({
                name: "SourceName4",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(true);
    });

    it('Graph with multiple vertices and is acyclic test 8', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: [
                    "SourceName1",
                    "SourceName4"
                ]
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: [
                    "SourceName2"
                ]
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Rule({
                name: "MDR004",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [
                    "MDR001"
                ],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [
                    "MDR004"
                ],
                preSources: []
            }),
            new Source({
                name: "SourceName4",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);

        chai.expect(graph.isAcyclic()).to.eql(true);
    });
});

describe('Filter vertices', () => {
    it('Graph with no vertices', () => {
        let ruleList = [];
        let sourceList = [];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices([], []);

        chai.expect(graph.edges).to.eql([]);
        chai.expect(graph.vertices).to.eql([]);
    });

    it('Graph with rules and sources with no edges test 1', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices([], []);

        chai.expect(graph.edges).to.eql([]);
        chai.expect(graph.vertices).to.eql([]);
    });

    it('Graph with rules and sources with no edges test 2', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices(["MDR002"], []);

        chai.expect(graph.edges).to.eql([
            []
        ]);
        chai.expect(graph.vertices).to.eql([
            ruleList[1]
        ]);
    });

    it('Graph with rules and sources with no edges test 3', () => {
        let ruleList = [];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices([], []);

        chai.expect(graph.edges).to.eql([]);
        chai.expect(graph.vertices).to.eql([]);
    });

    it('Graph with rules and sources with no edges test 4', () => {
        let ruleList = [];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices([], ["SourceName3"]);

        chai.expect(graph.edges).to.eql([
            []
        ]);
        chai.expect(graph.vertices).to.eql([
            sourceList[2]
        ]);
    });

    it('Graph with rules and sources with no edges test 5', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices(["MDR001", "MDR003", "MDR004"], ["SourceName1"]);

        chai.expect(graph.edges).to.eql([
            [],
            [],
            []
        ]);
        chai.expect(graph.vertices).to.eql([
            ruleList[0],
            ruleList[2],
            sourceList[0]
        ]);
    });

    it('Graph with 2 rules with dependency test 1', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            })
        ];
        let sourceList = [];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices([], []);

        chai.expect(graph.edges).to.eql([]);
        chai.expect(graph.vertices).to.eql([]);
    });

    it('Graph with 2 rules with dependency test 2', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [
                    "MDR002"
                ],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices(["MDR002"], []);

        chai.expect(graph.edges).to.eql([
            []
        ]);
        chai.expect(graph.vertices).to.eql([
            ruleList[1]
        ]);
    });

    it('Graph with 2 rules with dependency test 3', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [
                    "MDR002"
                ],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices(["MDR001"], []);

        chai.expect(graph.edges).to.eql([
            [],
            [0]
        ]);
        chai.expect(graph.vertices).to.eql([
            ...ruleList
        ]);
    });


    it('Graph with 2 rules with circular dependency (cyclic graph)', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [
                    "MDR002"
                ],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            })
        ];
        let sourceList = [];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices(["MDR002"], []);

        chai.expect(graph.edges).to.eql([
            [1],
            [0]
        ]);
        chai.expect(graph.vertices).to.eql([
            ...ruleList
        ]);
    });

    it('Graph with 2 sources with dependency test 1', () => {
        let ruleList = [];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices([], []);

        chai.expect(graph.edges).to.eql([]);
        chai.expect(graph.vertices).to.eql([]);
    });

    it('Graph with 2 sources with dependency test 2', () => {
        let ruleList = [];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName2"
                ]
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices([], ["SourceName2"]);

        chai.expect(graph.edges).to.eql([
            []
        ]);
        chai.expect(graph.vertices).to.eql([
            sourceList[1]
        ]);
    });

    it('Graph with 2 sources with dependency test 3', () => {
        let ruleList = [];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName2"
                ]
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices([], ["SourceName1", "SourceName2"]);

        chai.expect(graph.edges).to.eql([
            [],
            [0]
        ]);
        chai.expect(graph.vertices).to.eql([
            ...sourceList
        ]);
    });

    it('Graph with 2 sources with circular dependency (cyclic graph)', () => {
        let ruleList = [];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName2"
                ]
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices(["SourceName1", "SourceName2"], []);

        chai.expect(graph.edges).to.eql([]);
        chai.expect(graph.vertices).to.eql([]);
    });

    it('Graph with 1 rule and 1 source with dependency test 1', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices(["MDR001"], []);

        chai.expect(graph.edges).to.eql([
            []
        ]);
        chai.expect(graph.vertices).to.eql([
            ...ruleList
        ]);
    });

    it('Graph with 1 rule and 1 source with dependency test 2', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices(["MDR001"], ["SourceName1"]);

        chai.expect(graph.edges).to.eql([
            [],
            [0]
        ]);
        chai.expect(graph.vertices).to.eql([
            ...ruleList,
            ...sourceList
        ]);
    });

    it('Graph with 1 rule and 1 source with circular dependency (cyclic graph)', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices([], ["SourceName1"]);

        chai.expect(graph.edges).to.eql([
            [1],
            [0]
        ]);
        chai.expect(graph.vertices).to.eql([
            ...ruleList,
            ...sourceList
        ]);
    });

    it('Graph with 1 rule and 1 source with self dependency', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR001"
                ],
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices([], ["SourceName1"]);

        chai.expect(graph.edges).to.eql([
            [1],
            []
        ]);
        chai.expect(graph.vertices).to.eql([
            ...ruleList,
            ...sourceList
        ]);
    });

    it('Graph with multiple vertices and is cyclic test 1', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [
                    "MDR002"
                ],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices([], []);

        chai.expect(graph.edges).to.eql([]);
        chai.expect(graph.vertices).to.eql([]);
    });

    it('Graph with multiple vertices and is cyclic test 2', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: [
                    "SourceName1"
                ]
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR002"
                ],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices([], ["SourceName1"]);

        chai.expect(graph.edges).to.eql([
            [1],
            [2],
            [1]
        ]);
        chai.expect(graph.vertices).to.eql([
            ...ruleList,
            ...sourceList
        ]);
    });

    it('Graph with multiple vertices and is cyclic test 3', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            }),
            new Rule({
                name: "MDR003",
                preRules: [
                    "MDR002"
                ],
                preSources: [
                    "SourceName2"
                ]
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR002",
                    "MDR003"
                ],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices(["MDR001"], ["SourceName3"]);

        chai.expect(graph.edges).to.eql([
            []
        ]);
        chai.expect(graph.vertices).to.eql([
            ruleList[0]
        ]);
    });

    it('Graph with multiple vertices and is cyclic test 4', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [
                    "MDR003"
                ],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Rule({
                name: "MDR004",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR001",
                    "MDR002",
                    "MDR004"
                ],
                preSources: [
                    "SourceName2",
                    "SourceName3"
                ]
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName4",
                api: "APIName",
                preRules: [],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices(["MDR001"], ["SourceName1", "SourceName3"]);

        chai.expect(graph.edges).to.eql([
            [4],
            [4],
            [0],
            [4],
            [2],
            [4],
            [4]
        ]);
        chai.expect(graph.vertices).to.eql([
            ...ruleList,
            sourceList[0],
            sourceList[1],
            sourceList[2]
        ]);
    });

    it('Graph with multiple vertices and is acyclic test 1', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: [
                    "SourceName1"
                ]
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices([], ["SourceName1"]);

        chai.expect(graph.edges).to.eql([
            [1],
            []
        ]);
        chai.expect(graph.vertices).to.eql([
            ruleList[0],
            sourceList[0]
        ]);
    });

    it('Graph with multiple vertices and is acyclic test 2', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR002"
                ],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices([], ["SourceName1"]);

        chai.expect(graph.edges).to.eql([
            [1],
            [2],
            []
        ]);
        chai.expect(graph.vertices).to.eql([
            ruleList[0],
            ruleList[1],
            sourceList[0]
        ]);
    });

    it('Graph with multiple vertices and is acyclic test 3', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            }),
            new Rule({
                name: "MDR003",
                preRules: [
                    "MDR002"
                ],
                preSources: [
                    "SourceName2"
                ]
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR002"
                ],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices([], ["SourceName1"]);

        chai.expect(graph.edges).to.eql([
            [1],
            [2],
            []
        ]);
        chai.expect(graph.vertices).to.eql([
            ruleList[0],
            ruleList[1],
            sourceList[0]
        ]);
    });

    it('Graph with multiple vertices and is acyclic test 4', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: []
            }),
            new Rule({
                name: "MDR003",
                preRules: [
                    "MDR002"
                ],
                preSources: [
                    "SourceName2"
                ]
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR002",
                    "MDR003"
                ],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices(["MDR003"], []);

        chai.expect(graph.edges).to.eql([
            [1],
            [2],
            [],
            [2]
        ]);
        chai.expect(graph.vertices).to.eql([
            ...ruleList,
            sourceList[1]
        ]);
    });

    it('Graph with multiple vertices and is acyclic test 5', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [
                    "MDR003"
                ],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Rule({
                name: "MDR002",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Rule({
                name: "MDR004",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR002",
                    "MDR004"
                ],
                preSources: [
                    "SourceName2",
                    "SourceName3"
                ]
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName4",
                api: "APIName",
                preRules: [],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices(["MDR001"], ["SourceName4"]);

        chai.expect(graph.edges).to.eql([
            [],
            [4],
            [0],
            [4],
            [0,2],
            [4],
            [4],
            []
        ]);
        chai.expect(graph.vertices).to.eql([
            ...ruleList,
            ...sourceList
        ]);
    });

    it('Graph with multiple vertices and is acyclic test 6', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [
                    "MDR003"
                ],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Rule({
                name: "MDR002",
                preRules: [],
                preSources: []
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Rule({
                name: "MDR004",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [
                    "MDR002",
                    "MDR004"
                ],
                preSources: [
                    "SourceName2",
                    "SourceName3"
                ]
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName4",
                api: "APIName",
                preRules: [],
                preSources: []
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices(["MDR001"], []);

        chai.expect(graph.edges).to.eql([
            [],
            [4],
            [0],
            [4],
            [0,2],
            [4],
            [4]
        ]);
        chai.expect(graph.vertices).to.eql([
            ...ruleList,
            sourceList[0],
            sourceList[1],
            sourceList[2]
        ]);
    });

    it('Graph with multiple vertices and is acyclic test 7', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: [
                    "SourceName1",
                    "SourceName4"
                ]
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: [
                    "SourceName2"
                ]
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Rule({
                name: "MDR004",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [
                    "MDR004"
                ],
                preSources: []
            }),
            new Source({
                name: "SourceName4",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices([], ["SourceName2", "SourceName4"]);

        chai.expect(graph.edges).to.eql([
            [1,2],
            [],
            []
        ]);
        chai.expect(graph.vertices).to.eql([
            sourceList[0],
            sourceList[1],
            sourceList[3]
        ]);
    });

    it('Graph with multiple vertices and is acyclic test 8', () => {
        let ruleList = [
            new Rule({
                name: "MDR001",
                preRules: [],
                preSources: [
                    "SourceName1",
                    "SourceName4"
                ]
            }),
            new Rule({
                name: "MDR002",
                preRules: [
                    "MDR001"
                ],
                preSources: [
                    "SourceName2"
                ]
            }),
            new Rule({
                name: "MDR003",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Rule({
                name: "MDR004",
                preRules: [],
                preSources: []
            })
        ];
        let sourceList = [
            new Source({
                name: "SourceName1",
                api: "APIName",
                preRules: [],
                preSources: []
            }),
            new Source({
                name: "SourceName2",
                api: "APIName",
                preRules: [
                    "MDR001"
                ],
                preSources: [
                    "SourceName1"
                ]
            }),
            new Source({
                name: "SourceName3",
                api: "APIName",
                preRules: [
                    "MDR004"
                ],
                preSources: []
            }),
            new Source({
                name: "SourceName4",
                api: "APIName",
                preRules: [],
                preSources: [
                    "SourceName1"
                ]
            })
        ];

        let graph = new DependencyGraph(ruleList, sourceList);
        graph.filterVertices(["MDR002"], ["SourceName4"]);

        chai.expect(graph.edges).to.eql([
            [1,3],
            [],
            [0,3,4],
            [1],
            [0]
        ]);
        chai.expect(graph.vertices).to.eql([
            ruleList[0],
            ruleList[1],
            sourceList[0],
            sourceList[1],
            sourceList[3]
        ]);
    });
});
