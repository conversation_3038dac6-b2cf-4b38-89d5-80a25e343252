import { DetailsPanel } from '../../../components/detailsPanel/DetailsPanel'
import { Panel } from '../../../components/panel/Panel'
import { useAppState } from '../../../hooks/useAppState'
import { CIDNField } from './fields/CIDNField'
import { CustomerConsentField } from './fields/CustomerConsentField'
import { CustomerField } from './fields/CustomerField'
import { LocationField } from './fields/LocationField'

export const CustomerDetailsPanel = () => {
    const { appState } = useAppState()
    const customerDetails = appState.serviceDetails.customerDetails

    const customerConsentString = () => {
        const consentValue = customerDetails.customerConsent

        if (consentValue == undefined) {
            return 'Unknown'
        }

        return consentValue ? 'Yes' : 'No'
    }
    return (
        <DetailsPanel label="Customer Details">
            <Panel>
                <>
                    <CustomerField value={customerDetails.customer} />
                    <LocationField value={customerDetails.location} />
                    <CIDNField value={customerDetails.cidn} />
                    <CustomerConsentField value={customerConsentString()} />
                </>
            </Panel>
        </DetailsPanel>
    )
}
