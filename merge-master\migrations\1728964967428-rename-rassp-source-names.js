'use strict';

const { connect, disconnect } = require('./config');

async function up() {
    await connect();

    const ServiceCheckModel = (await import('../db/model/serviceCheck.js')).default;

    for await (const record of ServiceCheckModel.find({})) {
        if (record.sourcesMetadata?.RASSPB) {
            record.sourcesMetadata['RASSP-B'] = record.sourcesMetadata.RASSPB;
            delete record.sourcesMetadata.RASSPB;
        }

        if (record.sourcesMetadata?.RASSPC) {
            record.sourcesMetadata['RASSP-C'] = record.sourcesMetadata.RASSPC;
            delete record.sourcesMetadata.RASSPC;
        }

        record.markModified('sourcesMetadata');

        await record.save({ validateBeforeSave: false });
    }

    const ServiceCheckSourceData = (await import('../db/model/serviceCheckSourceData.js')).default;

    await ServiceCheckSourceData.updateMany({
        name: 'RASSP<PERSON>'
    }, {
        $set: {
            'name': 'RASSP-B'
        }
    }, { multi: true, strict: false });

    await ServiceCheckSourceData.updateMany({
        name: 'RASSPC'
    }, {
        $set: {
            'name': 'RASSP-C'
        }
    }, { multi: true, strict: false });

    await disconnect();
}


async function down() {
    await connect();

    await disconnect();
}


module.exports = { up, down };
