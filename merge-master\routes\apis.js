'use strict';

import express from 'express';
import { query, validationResult } from 'express-validator';
import _ from 'lodash';
import escapeStringRegexp from 'escape-string-regexp';

import auth from '../modules/auth.js';
import { AuthorizationRoles } from '../modules/enumerations.js';
import logger from '../modules/logger.js';
import pagination from '../modules/pagination.js';
import validators from '../modules/validators.js';
import Api from '../db/model/api.js';

const router = express.Router();


/* Main API list page */
router.get('/', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], false), [
    query('limit').default(100).isInt({ min: 1 }).toInt(),
    query('offset').default(0).isInt({ min: 0 }).toInt(),
    query('sort').default('name').isString(),
    query('order').default('asc').isString().isIn(['asc', 'desc'])
        .withMessage('Order must be either "asc" or "desc"'),
    query('id').optional().custom(validators.createFilterQueryValidator(["like", "equal"]))
        .customSanitizer(validators.createFilterQuerySanitizer())
        .withMessage("ID filter must contain 'like' or 'equal' operator"),
    query('name').optional().custom(validators.createFilterQueryValidator(["like", "equal"]))
        .customSanitizer(validators.createFilterQuerySanitizer())
        .withMessage("Name filter must contain 'like' or 'equal' operator"),
    query('description').optional().custom(validators.createFilterQueryValidator(["like", "equal"]))
        .customSanitizer(validators.createFilterQuerySanitizer())
        .withMessage("Description filter must contain 'like' or 'equal' operator")
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let limit = req.query.limit;
    let offset = req.query.offset;
    let sort = req.query.sort;
    let orderBy = req.query.order;
    let orderByParam = (orderBy === 'asc') ? 1 : -1;

    let querySort = { [sort]: orderByParam };

    let condition = {};
    let filters = [];

    let queryFilters = {
        id: req.query.id,
        name: req.query.name,
        description: req.query.description
    };

    // Goes through the query filters and applies an equality or regex match depending on the operation
    for (let filter in queryFilters) {
        if (queryFilters[filter] != undefined) {
            for (let operation in queryFilters[filter]) {
                switch (operation) {
                    case "equal":
                        filters.push({ [filter]: req.query[filter][operation] });
                        break;
                    case "like":
                        filters.push({ [filter]: { $regex: new RegExp(escapeStringRegexp(req.query[filter][operation]), 'i') }});
                        break;
                }
            }
        }
    }

    if (filters.length > 0) {
        condition = { $and: filters };
    }

    try {
        let [apis, countResult] = await Promise.all([
            Api.aggregate([
                { $match: condition },
                { $sort: querySort },
                { $skip: offset },
                { $limit: limit },
                { $project: {
                    __v: 0,
                    _id: 0
                }}
            ]).collation({ locale: 'en' }),
            Api.aggregate([
                { $match: condition },
                { $count: 'total' }
            ]).collation({ locale: 'en' }),
        ]);
        let count = _.get(countResult, [0, 'total'], 0);

        let paginationMetadata = pagination.createPaginationMetadata(req, limit, offset, count);

        let response = {
            metadata: {
                pagination: paginationMetadata,
            },
            results: apis
        };

        res.send(response);
    } catch(error) {
        logger.error(`Get all APIs error: ${error.toString()}`);
        res.sendStatus(500);
    }
});


/*Individual api view page*/
router.get('/:name', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], false), async function(req, res) {
    try {
        let api = await Api.findOne({ name: req.params.name });

        if (api) {
            res.send(api.toJSON());
        } else {
            res.sendStatus(404);
        }
    } catch(error) {
        logger.error(`Get one API error: ${error.toString()}`);
        res.sendStatus(500);
    }
});

/* Save new API*/
router.post('/', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], false), async function(req, res) {
    // Removes createdOn from keys if present
    if (req.body) {
        delete req.body.createdOn;
    }

    if (!req.user.isAdmin && global.gConfig.disableRuleSourceEditing) {
        res.status(405).send({
            message: "API modification is not allowed"
        });
        return;
    }

    const newApi = new Api(req.body);

    // Set createdBy from session username
    if (req.user) {
        newApi.createdBy = req.user.username;
    }

    try {
        let api = await newApi.save();
        res.status(201).send(api.toJSON());
    } catch(error) {
        if (error.name == "MongoServerError" && error.code == 11000) {
            res.status(409).send({
                error: error.toString()
            });
        } else if (error.name == "ValidationError") {
            res.status(400).send({
                error: error.toString(),
                validation: error.errors
            });
        } else if (error.name == "CastError") {
            res.status(400).send({
                error: error.toString()
            });
        } else {
            logger.error(`Create API unexpected error, ${error.toString()}`);
            res.status(500).send({
                error: error.toString()
            });
        }
    }
});

/* Individual api edit save*/
router.put('/:name', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], false), async function(req, res) {
    // Removes createdOn from keys if present
    if (req.body) {
        delete req.body.createdOn;
    }

    if (!req.user.isAdmin && global.gConfig.disableRuleSourceEditing) {
        res.status(405).send({
            message: "API modification is not allowed"
        });
        return;
    }

    try {
        let api = await Api.findOneAndUpdate({ name: req.params.name }, req.body, {
            runValidators: true,
            strict: true,
            new: true,
            useFindAndModify: false
        });

        if (api) {
            res.send(api.toJSON());
        } else {
            res.sendStatus(404);
        }
    } catch(error) {
        if (error.name == "MongoServerError" && error.code == 11000) {
            res.status(409).send({
                error: error.toString()
            });
        } else if (error.name == "ValidationError") {
            res.status(400).send({
                error: error.toString(),
                validation: error.errors
            });
        } else if (error.name == "CastError") {
            res.status(400).send({
                error: error.toString()
            });
        } else {
            logger.error(`Update API unexpected error, ${error.toString()}`);
            res.status(500).send({
                error: error.toString()
            });
        }
    }
});


router.delete('/:name', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], false), async function(req, res) {
    if (!req.user.isAdmin && global.gConfig.disableRuleSourceEditing) {
        res.status(405).send({
            message: "API modification is not allowed"
        });
        return;
    }

    try {
        let result = await Api.deleteOne({ name: req.params.name });
        if (result.deletedCount) {
            res.sendStatus(200);
        } else {
            res.sendStatus(404);
        }
    } catch(error) {
        logger.error(`Delete API error: ${error.toString()}`);
        res.sendStatus(500);
    }
});


export default router;
