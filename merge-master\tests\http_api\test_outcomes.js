'use strict';

import mongoose from 'mongoose';
import chai from 'chai';
import chaiHttp from 'chai-http';
import chaiLike from 'chai-like';
import chaiDateTime from 'chai-datetime';
import superagentDefaults from 'superagent-defaults';
import supertest from 'supertest';
import cookie from 'cookie';
import _ from 'lodash';
import sinon from 'sinon';
import assert from 'assert';

var app;
var request;
import config from '../config.js';
import Outcome from '../../db/model/outcome.js';
import helpers from '../helpers.js';
import { UNIT_TEST_ALL_ACCESS_USERNAME, UNIT_TEST_USERS } from '../users.js';

const should = chai.should();

chaiLike.extend(helpers.dateFieldCheck);
chaiLike.extend(helpers.regexCheck);
chai.use(chaiLike);
chai.use(chaiHttp);
chai.use(chaiDateTime);


const OUTCOME_KEYS = Object.freeze(Object.keys(Outcome.schema.obj));


describe('Merge outcomes REST endpoints', () => {
    // Setup and connect to mongodb memory server, start merge app instance
    // Set request variable with a configured supertest agent with test JWT and app instance
    before(async function() {
        global.gConfig = _.cloneDeep(config.default);
        app = (await import('../../app.js')).default;
        request = superagentDefaults(supertest(app));
    });

    // Drop all contents of database before each test
    beforeEach(async function() {
        global.gConfig = _.cloneDeep(config.default);

        // Uses deleteMany instead of mongoose.connection.db.dropDatabase() because
        // custom indexes appear to be deleted if dropDatabase() is used
        for (let collectionName in mongoose.connection.collections) {
            await mongoose.connection.collections[collectionName].deleteMany({});
        }

        await helpers.authenticateSession(request, UNIT_TEST_ALL_ACCESS_USERNAME);
    });

    afterEach(async function() {
        sinon.restore();
    });

    describe('Read list of outcomes', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/outcomes');

            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/outcomes');

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Get empty list of outcomes', async() => {
            let res = await request.get('/outcomes');

            res.should.have.status(200);
            chai.expect(res.body).to.eql({
                metadata: {
                    pagination: {
                        limit: 100,
                        offset: 0,
                        total: 0,
                        prev: null,
                        next: null
                    }
                },
                results: []
            });
        });

        it('Get list of outcomes with one outcome (default values)', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'OutcomeName1',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);

            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get list of outcomes with three outcomes (default values)', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName2'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'OutcomeName1',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName2',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName3',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get list of outcomes with one outcome', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1',
                title: 'Outcome Title 1',
                description: 'Outcome Description 1',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'OutcomeName1',
                title: 'Outcome Title 1',
                description: 'Outcome Description 1',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get list of outcomes with two outcomes', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1',
                title: 'Outcome Title 1',
                description: 'Outcome Description 1',
                header: '',
                displayCondition: "Outcome Display Condition 1",
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName2',
                title: 'Outcome Title 2',
                description: 'Outcome Description 2',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'merge'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes');

            res.should.have.status(200);

            chai.expect(res.body.results).to.like([{
                name: 'OutcomeName1',
                title: 'Outcome Title 1',
                description: 'Outcome Description 1',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            },
            {
                name: 'OutcomeName2',
                title: 'Outcome Title 2',
                description: 'Outcome Description 2',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'merge'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get one outcome with limit', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName2'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName3'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName4'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName5'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?limit=1');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'OutcomeName1',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get one outcome with limit and offset', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName2'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName3'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName4'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName5'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?limit=2&offset=2');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'OutcomeName3',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName4',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get outcome list sorted by name ascending', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName2'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?sort=name&order=asc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'OutcomeName1',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName2',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName3',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get outcome list sorted by name descending', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName2'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?sort=name&order=desc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'OutcomeName3',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName2',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName1',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get outcome list sorted by name ascending case insensitive', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName2'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?sort=name&order=asc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'OutcomeName1',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName2',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName3',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get outcome list sorted by name descending case insensitive', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName2'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?sort=name&order=desc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'OutcomeName3',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName2',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName1',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get outcome list sorted by title ascending', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1',
                title: 'Bravo'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName2',
                title: 'Charlie'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName3',
                title: 'Alpha'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?sort=title&order=asc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'OutcomeName3',
                title: 'Alpha',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName1',
                title: 'Bravo',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName2',
                title: 'Charlie',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get outcome list sorted by title descending', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1',
                title: 'Bravo'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName2',
                title: 'Charlie'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName3',
                title: 'Alpha'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?sort=title&order=desc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'OutcomeName2',
                title: 'Charlie',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName1',
                title: 'Bravo',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName3',
                title: 'Alpha',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get outcome list sorted by title ascending case insensitive', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1',
                title: 'Test Title 1'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName2',
                title: 'test title 2'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName3',
                title: 'TEST TITLE 3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?sort=title&order=asc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'OutcomeName1',
                title: 'Test Title 1',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName2',
                title: 'test title 2',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName3',
                title: 'TEST TITLE 3',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get outcome list sorted by title descending case insensitive', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1',
                title: 'Test Title 1'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName2',
                title: 'test title 2'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName3',
                title: 'TEST TITLE 3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?sort=title&order=desc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'OutcomeName3',
                title: 'TEST TITLE 3',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName2',
                title: 'test title 2',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName1',
                title: 'Test Title 1',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get outcome list sorted by description ascending', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1',
                description: 'Bravo'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName2',
                description: 'Charlie'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName3',
                description: 'Alpha'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?sort=description&order=asc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'OutcomeName3',
                title: '',
                description: 'Alpha',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName1',
                title: '',
                description: 'Bravo',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName2',
                title: '',
                description: 'Charlie',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get outcome list sorted by description descending', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1',
                description: 'Bravo'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName2',
                description: 'Charlie'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName3',
                description: 'Alpha'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?sort=description&order=desc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'OutcomeName2',
                title: '',
                description: 'Charlie',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName1',
                title: '',
                description: 'Bravo',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName3',
                title: '',
                description: 'Alpha',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get outcome list sorted by description ascending case insensitive', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1',
                description: 'Test Description 1'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName2',
                description: 'test description 2'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName3',
                description: 'TEST DESCRIPTION 3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?sort=description&order=asc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'OutcomeName1',
                title: '',
                description: 'Test Description 1',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName2',
                title: '',
                description: 'test description 2',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName3',
                title: '',
                description: 'TEST DESCRIPTION 3',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get outcome list sorted by description descending case insensitive', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1',
                description: 'Test Description 1'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName2',
                description: 'test description 2'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName3',
                description: 'TEST DESCRIPTION 3'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?sort=description&order=desc');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'OutcomeName3',
                title: '',
                description: 'TEST DESCRIPTION 3',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName2',
                title: '',
                description: 'test description 2',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName1',
                title: '',
                description: 'Test Description 1',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get outcome list filter by name equality 1', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'TestOutcome1'
            }).save());

            promises.push(new Outcome({
                name: 'TestOutcome2'
            }).save());

            promises.push(new Outcome({
                name: 'AnotherOutcome1'
            }).save());

            promises.push(new Outcome({
                name: 'AnotherOutcome2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?name=TestOutcome1');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'TestOutcome1',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get outcome list filter by name equality 2', async() => {
            let promises = [];

            promises.push(new Outcome({
                name: 'TestOutcome1'
            }).save());

            promises.push(new Outcome({
                name: 'TestOutcome2'
            }).save());

            promises.push(new Outcome({
                name: 'AnotherOutcome1'
            }).save());

            promises.push(new Outcome({
                name: 'AnotherOutcome2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?name=Test');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([]);
        });

        it('Get outcome list filter by name equality 3', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'TestOutcome1'
            }).save());

            promises.push(new Outcome({
                name: 'TestOutcome2'
            }).save());

            promises.push(new Outcome({
                name: 'AnotherOutcome1'
            }).save());

            promises.push(new Outcome({
                name: 'AnotherOutcome2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?name[equal]=AnotherOutcome2');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'AnotherOutcome2',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get outcome list filter by name equality 4', async() => {
            let promises = [];

            promises.push(new Outcome({
                name: 'TestOutcome1'
            }).save());

            promises.push(new Outcome({
                name: 'TestOutcome2'
            }).save());

            promises.push(new Outcome({
                name: 'AnotherOutcome1'
            }).save());

            promises.push(new Outcome({
                name: 'AnotherOutcome2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?name[equal]=Outcome');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([]);
        });

        it('Get outcome list filter by name like 1', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'TestOutcome1'
            }).save());

            promises.push(new Outcome({
                name: 'TestOutcome2'
            }).save());

            promises.push(new Outcome({
                name: 'AnotherOutcome1'
            }).save());

            promises.push(new Outcome({
                name: 'AnotherOutcome2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?name[like]=Test');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'TestOutcome1',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'TestOutcome2',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get outcome list filter by name like 2', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'TestOutcome1'
            }).save());

            promises.push(new Outcome({
                name: 'TestOutcome2'
            }).save());

            promises.push(new Outcome({
                name: 'AnotherOutcome1'
            }).save());

            promises.push(new Outcome({
                name: 'AnotherOutcome2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?name[like]=Outcome1');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'AnotherOutcome1',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'TestOutcome1',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get outcome list filter by name like 3', async() => {
            let promises = [];

            promises.push(new Outcome({
                name: 'TestOutcome1'
            }).save());

            promises.push(new Outcome({
                name: 'TestOutcome2'
            }).save());

            promises.push(new Outcome({
                name: 'AnotherOutcome1'
            }).save());

            promises.push(new Outcome({
                name: 'AnotherOutcome2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?name[like]=Text');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([]);
        });

        it('Get outcome list filter by name like 4 (case insensitive)', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'TestOutcome1'
            }).save());

            promises.push(new Outcome({
                name: 'TestOutcome2'
            }).save());

            promises.push(new Outcome({
                name: 'AnotherOutcome1'
            }).save());

            promises.push(new Outcome({
                name: 'AnotherOutcome2'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?name[like]=AnOtHer');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'AnotherOutcome1',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'AnotherOutcome2',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get outcome list filter by title equality 1', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1',
                title: 'A outcome title'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName2',
                title: 'Another outcome title'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName3',
                title: 'Outcome title here'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName4',
                title: 'Yet another outcome title'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?title=Outcome%20title%20here');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'OutcomeName3',
                title: 'Outcome title here',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get outcome list filter by title equality 2', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1',
                title: 'A outcome title'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName2',
                title: 'Another outcome title'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName3',
                title: 'Outcome title here'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName4',
                title: 'Yet another outcome title'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?title[equal]=Outcome%20title%20here');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'OutcomeName3',
                title: 'Outcome title here',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get outcome list filter by title like 1', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1',
                title: 'Extract field here'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName2',
                title: 'Extract field there'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName3',
                title: 'Summarise field here'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName4',
                title: 'Summarise field there'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?title[like]=Extract');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'OutcomeName1',
                title: 'Extract field here',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName2',
                title: 'Extract field there',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get outcome list filter by title like 2 (case insensitive)', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1',
                title: 'Extract field here'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName2',
                title: 'Extract field there'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName3',
                title: 'Summarise field here'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName4',
                title: 'Summarise field there'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?title[like]=THeRE');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'OutcomeName2',
                title: 'Extract field there',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }, {
                name: 'OutcomeName4',
                title: 'Summarise field there',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get outcome list filter by description equality 1', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1',
                description: 'A outcome description'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName2',
                description: 'Another outcome description'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName3',
                description: 'Outcome description here'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName4',
                description: 'Yet another outcome description'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?description=Outcome%20description%20here');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'OutcomeName3',
                title: '',
                description: 'Outcome description here',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get outcome list filter by description equality 2', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1',
                description: 'A outcome description'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName2',
                description: 'Another outcome description'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName3',
                description: 'Outcome description here'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName4',
                description: 'Yet another outcome description'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?description[equal]=Outcome%20description%20here');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'OutcomeName3',
                title: '',
                description: 'Outcome description here',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get outcome list filter by description like 1', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1',
                description: 'Extract field here'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName2',
                description: 'Extract field there'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName3',
                description: 'Summarise field here'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName4',
                description: 'Summarise field there'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?description[like]=Extract');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'OutcomeName1',
                title: '',
                description: 'Extract field here',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            },
            {
                name: 'OutcomeName2',
                title: '',
                description: 'Extract field there',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get outcome list filter by description like 2 (case insensitive)', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1',
                description: 'Extract field here'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName2',
                description: 'Extract field there'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName3',
                description: 'Summarise field here'
            }).save());

            promises.push(new Outcome({
                name: 'OutcomeName4',
                description: 'Summarise field there'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes?description[like]=THeRE');

            res.should.have.status(200);
            chai.expect(res.body.results).to.like([{
                name: 'OutcomeName2',
                title: '',
                description: 'Extract field there',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }, {
                name: 'OutcomeName4',
                title: '',
                description: 'Summarise field there',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            }]);
            res.body.results.forEach((item) => {
                chai.expect(item).to.have.all.keys(OUTCOME_KEYS);
            });
        });

        it('Get outcome list invalid limit (non-integer)', async() => {
            let res = await request.get('/outcomes?limit=notalimit');

            res.should.have.status(400);
        });

        it('Get outcome list invalid limit (integer equals 0)', async() => {
            let res = await request.get('/outcomes?limit=0');

            res.should.have.status(400);
        });

        it('Get outcome list invalid limit (negative integer)', async() => {
            let res = await request.get('/outcomes?limit=-1');

            res.should.have.status(400);
        });

        it('Get outcome list invalid offset (non-integer)', async() => {
            let res = await request.get('/outcomes?offset=notanoffset');

            res.should.have.status(400);
        });

        it('Get outcome list invalid offset (negative integer)', async() => {
            let res = await request.get('/outcomes?offset=-1');

            res.should.have.status(400);
        });

        it('Get outcome list invalid order parameter', async() => {
            let res = await request.get('/outcomes?order=ascending');

            res.should.have.status(400);
        });

        it('Get outcome list invalid name filter operator', async() => {
            let res = await request.get('/outcomes?name[ophere]=test');

            res.should.have.status(400);
        });

        it('Get outcome list empty title filter operator', async() => {
            let res = await request.get('/outcomes?title[]=no-op');

            res.should.have.status(400);
        });

        it('Get outcome list with HTTP GET with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            let res = await request.get('/outcomes');

            res.should.have.status(200);
            chai.expect(res.body).to.eql({
                metadata: {
                    pagination: {
                        limit: 100,
                        offset: 0,
                        total: 0,
                        prev: null,
                        next: null
                    }
                },
                results: []
            });
        });
    });

    describe('Read outcome', () => {
        it('Unauthenticated request', async() => {
            await new Outcome({
                name: 'OutcomeName'
            }).save();

            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/outcomes/OutcomeName');

            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            await new Outcome({
                name: 'OutcomeName'
            }).save();

            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/outcomes/OutcomeName');

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Get non-existant outcome', async() => {
            let res = await request.get('/outcomes/OutcomeName1');

            res.should.have.status(404);
        });

        it('Get one existing outcome (default values)', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes/OutcomeName1');

            res.should.have.status(200);
            chai.expect(res.body).to.like({
                name: 'OutcomeName1',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(res.body).to.have.all.keys(OUTCOME_KEYS);
        });

        it('Get one existing outcome', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1',
                title: 'Outcome Title 1',
                description: 'Outcome Description 1',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes/OutcomeName1');

            res.should.have.status(200);

            chai.expect(res.body).to.like({
                name: 'OutcomeName1',
                title: 'Outcome Title 1',
                description: 'Outcome Description 1',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });

            chai.expect(res.body).to.have.all.keys(OUTCOME_KEYS);
        });

        it('Get existing outcome with HTTP GET with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1'
            }).save());

            await Promise.all(promises);
            let res = await request.get('/outcomes/OutcomeName1');

            res.should.have.status(200);
            chai.expect(res.body).to.like({
                name: 'OutcomeName1',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(res.body).to.have.all.keys(OUTCOME_KEYS);
        });
    });

    describe('Create outcome', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.post('/outcomes').send({
                name: 'OutcomeName1'
            });

            res.should.have.status(401);
            chai.expect(await Outcome.find({ name: 'OutcomeName1' }).countDocuments()).to.eql(0);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.post('/outcomes').send({
                    name: 'OutcomeName1'
                });

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(201);
                            chai.expect(await Outcome.find({ name: 'OutcomeName1' }).countDocuments()).to.eql(1);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                            chai.expect(await Outcome.find({ name: 'OutcomeName1' }).countDocuments()).to.eql(0);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }

                await Outcome.deleteMany({});
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Create outcome with HTTP POST (default values)', async() => {
            let currDate = new Date();

            let postRes = await request.post('/outcomes').send({
                name: 'OutcomeName1'
            });

            postRes.should.have.status(201);
            chai.expect(postRes.body).to.like({
                name: 'OutcomeName1',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });

            let getRes = await request.get('/outcomes/OutcomeName1');

            getRes.should.have.status(200);
            chai.expect(getRes.body).to.like({
                name: 'OutcomeName1',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(getRes.body).to.have.all.keys(OUTCOME_KEYS);
        });

        it('Create outcome with HTTP POST', async() => {
            let currDate = new Date();

            let postRes = await request.post('/outcomes').send({
                name: 'OutcomeName1',
                title: 'Title Name Here',
                description: 'Description Here',
                header: ''
            });

            postRes.should.have.status(201);
            chai.expect(postRes.body).to.like({
                name: 'OutcomeName1',
                title: 'Title Name Here',
                description: 'Description Here',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(postRes.body).to.have.all.keys(OUTCOME_KEYS);

            let getRes = await request.get('/outcomes/OutcomeName1');

            getRes.should.have.status(200);
            chai.expect(getRes.body).to.like({
                name: 'OutcomeName1',
                title: 'Title Name Here',
                description: 'Description Here',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(getRes.body).to.have.all.keys(OUTCOME_KEYS);
        });

        it('Create duplicate outcome with HTTP POST', async() => {
            let postRes1 = await request.post('/outcomes').send({
                name: 'OutcomeName1'
            });

            postRes1.should.have.status(201);

            let postRes2 = await request.post('/outcomes').send({
                name: 'OutcomeName1',
            });

            postRes2.should.have.status(409);
        });

        it('Create outcome with HTTP POST test createdBy immutable', async() => {
            let currDate = new Date();

            let res = await request.post('/outcomes').send({
                name: 'OutcomeName1',
                createdBy: 'anotheruser'
            });

            res.should.have.status(201);
            chai.expect(res.body).to.like({
                name: 'OutcomeName1',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(res.body).to.have.all.keys(OUTCOME_KEYS);
        });

        it('Create outcome with HTTP POST test createdOn immutable', async() => {
            let customDate = new Date('2020-12-15T16:00:00.000Z');
            let currDate = new Date();

            let res = await request.post('/outcomes').send({
                name: 'OutcomeName1',
                createdOn: customDate
            });

            res.should.have.status(201);
            chai.expect(res.body).to.like({
                name: 'OutcomeName1',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(res.body).to.have.all.keys(OUTCOME_KEYS);
        });

        it('Create outcome with HTTP POST unrecognised fields', async() => {
            let currDate = new Date();

            let postRes = await request.post('/outcomes').send({
                name: 'OutcomeName1',
                customField: 'should not show up',
                unrecognisedField: 550
            });

            postRes.should.have.status(201);
            chai.expect(postRes.body).to.like({
                name: 'OutcomeName1',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });

            chai.expect(postRes.body).to.have.all.keys(OUTCOME_KEYS);
            chai.expect(postRes.body).to.not.have.any.keys('customField', 'unrecognisedField');

            let getRes = await request.get('/outcomes/OutcomeName1');

            getRes.should.have.status(200);
            chai.expect(getRes.body).to.like({
                name: 'OutcomeName1',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            });
            chai.expect(getRes.body).to.have.all.keys(OUTCOME_KEYS);
            chai.expect(getRes.body).to.not.have.any.keys('customField', 'unrecognisedField');
        });

        it('Create outcome with HTTP POST with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            let postRes = await request.post('/outcomes').send({
                name: 'OutcomeName1'
            });

            postRes.should.have.status(405);

            let getRes = await request.get('/outcomes/OutcomeName1');

            getRes.should.have.status(404);
        });

        it('Create outcome with HTTP POST with disableRuleSourceEditing enabled and admin user', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'admin');

            let postRes = await request.post('/outcomes').send({
                name: 'OutcomeName1'
            });

            postRes.should.have.status(201);

            let getRes = await request.get('/outcomes/OutcomeName1');

            getRes.should.have.status(200);
        });
    });

    describe('Update outcome', () => {
        it('Unauthenticated request', async() => {
            await new Outcome({
                name: 'OutcomeName1',
                title: 'test title'
            }).save();

            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.put('/outcomes/OutcomeName1').send({
                title: 'new title'
            });

            res.should.have.status(401);
            chai.expect((await Outcome.findOne({ name: 'OutcomeName1' })).title).to.eql('test title');
        });

        it('Authorization tests', async() => {
            await new Outcome({
                name: 'OutcomeName1',
                title: 'test title'
            }).save();

            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.put('/outcomes/OutcomeName1').send({
                    title: 'new title'
                });

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                            chai.expect((await Outcome.findOne({ name: 'OutcomeName1' })).title).to.eql('new title');
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                            chai.expect((await Outcome.findOne({ name: 'OutcomeName1' })).title).to.eql('test title');
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
                await Outcome.updateOne({ name: 'OutcomeName1' }, { $set: { title: 'test title' }});
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Update outcome with HTTP PUT', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/outcomes/OutcomeName1').send({
                name: 'OutcomeName1',
                title: 'Outcome 1',
                description: 'For unit tests',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null
            });

            res.should.have.status(200);
            chai.expect(res.body).to.like({
                name: 'OutcomeName1',
                title: 'Outcome 1',
                description: 'For unit tests',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(res.body).to.have.all.keys(OUTCOME_KEYS);
        });

        it('Update outcome with HTTP PUT test non-existant outcome', async() => {
            let res = await request.put('/outcomes/OutcomeName1');

            res.should.have.status(404);
        });

        it('Update outcome with HTTP PUT test name immutable', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1'
            }).save());

            await Promise.all(promises);
            let putRes = await request.put('/outcomes/OutcomeName1').send({
                name: 'OutcomeName2'
            });

            putRes.should.have.status(200);

            let getRes1 = await request.get('/outcomes/OutcomeName1');

            getRes1.should.have.status(200);
            chai.expect(getRes1.body).to.like({
                name: 'OutcomeName1',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(getRes1.body).to.have.all.keys(OUTCOME_KEYS);

            let getRes2 = await request.get('/outcomes/OutcomeName2');

            getRes2.should.have.status(404);
        });

        it('Update outcome with HTTP PUT test createdBy immutable', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1',
                createdBy: 'testuser'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/outcomes/OutcomeName1').send({
                createdBy: 'anotheruser'
            });

            res.should.have.status(200);
            chai.expect(res.body).to.like({
                name: 'OutcomeName1',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'testuser'
            });
            chai.expect(res.body).to.have.all.keys(OUTCOME_KEYS);
        });

        it('Update outcome with HTTP PUT test createdOn immutable', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/outcomes/OutcomeName1').send({
                createdOn: '2010-01-15T16:00:00.000Z'
            });

            res.should.have.status(200);
            chai.expect(res.body).to.like({
                name: 'OutcomeName1',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(res.body).to.have.all.keys(OUTCOME_KEYS);
        });

        it('Update outcome with HTTP PUT unrecognised fields', async() => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1'
            }).save());

            await Promise.all(promises);
            let res = await request.put('/outcomes/OutcomeName1').send({
                badField: 'badValue'
            });

            res.should.have.status(200);

            chai.expect(res.body).to.like({
                name: 'OutcomeName1',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            });

            chai.expect(res.body).to.have.all.keys(OUTCOME_KEYS);
            chai.expect(res.body).to.not.have.any.keys('badField');
        });

        it('Update outcome with HTTP PUT with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1'
            }).save());

            await Promise.all(promises);
            let putRes = await request.put('/outcomes/OutcomeName1').send({
                title: 'new title'
            });

            putRes.should.have.status(405);

            let getRes = await request.get('/outcomes/OutcomeName1');

            getRes.should.have.status(200);
            getRes.body.should.like({
                name: 'OutcomeName1',
                title: '',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(getRes.body).to.have.all.keys(OUTCOME_KEYS);
        });

        it('Create outcome with HTTP PUT with disableRuleSourceEditing enabled and admin user', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'admin');

            let currDate = new Date();
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1'
            }).save());

            await Promise.all(promises);
            let putRes = await request.put('/outcomes/OutcomeName1').send({
                title: 'new title'
            });

            putRes.should.have.status(200);

            let getRes = await request.get('/outcomes/OutcomeName1');

            getRes.should.have.status(200);
            getRes.body.should.like({
                name: 'OutcomeName1',
                title: 'new title',
                description: '',
                header: '',
                display: false,
                tier: 7,
                weight: 0,
                messageBucketName: null,
                createdOn: currDate,
                createdBy: 'unknown'
            });
            chai.expect(getRes.body).to.have.all.keys(OUTCOME_KEYS);
        });
    });

    describe('Delete outcome', () => {
        it('Unauthenticated request', async() => {
            await new Outcome({
                name: 'OutcomeName1'
            }).save();

            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.delete('/outcomes/OutcomeName1');

            res.should.have.status(401);
            chai.expect(await Outcome.find({ name: 'OutcomeName1' }).countDocuments()).to.eql(1);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                if (await Outcome.find({ name: 'OutcomeName1' }).countDocuments() === 0) {
                    await new Outcome({
                        name: 'OutcomeName1'
                    }).save();
                }

                await helpers.authenticateSession(request, username);
                let res = await request.delete('/outcomes/OutcomeName1');

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                            chai.expect(await Outcome.find({ name: 'OutcomeName1' }).countDocuments()).to.eql(0);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                            chai.expect(await Outcome.find({ name: 'OutcomeName1' }).countDocuments()).to.eql(1);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Delete outcome with HTTP DELETE', async() => {
            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1'
            }).save());

            await Promise.all(promises);
            let deleteRes = await request.delete('/outcomes/OutcomeName1');

            deleteRes.should.have.status(200);

            let getRes = await request.get('/outcomes/OutcomeName1');

            getRes.should.have.status(404);
        });

        it('Delete outcome with HTTP DELETE test non-existant outcome', async() => {
            let res = await request.delete('/outcomes/OutcomeName1');

            res.should.have.status(404);
        });

        it('Delete outcome with HTTP DELETE with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1'
            }).save());

            await Promise.all(promises);
            let deleteRes = await request.delete('/outcomes/OutcomeName1');

            deleteRes.should.have.status(405);

            let getRes = await request.get('/outcomes/OutcomeName1');

            getRes.should.have.status(200);
        });

        it('Delete outcome with HTTP DELETE with disableRuleSourceEditing enabled and admin user', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'admin');

            let promises = [];

            promises.push(new Outcome({
                name: 'OutcomeName1'
            }).save());

            await Promise.all(promises);
            let deleteRes = await request.delete('/outcomes/OutcomeName1');

            deleteRes.should.have.status(200);

            let getRes = await request.get('/outcomes/OutcomeName1');

            getRes.should.have.status(404);
        });
    });
});