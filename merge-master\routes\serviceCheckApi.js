'use strict';

import express from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { isValidPhoneNumber, parsePhoneNumber } from 'libphonenumber-js';
import _ from 'lodash';

import ServiceCheckModel from '../db/model/serviceCheck.js';

import auth from '../modules/auth.js';
import { AuthorizationRoles, InputTypes, ServiceCheckStartMethod, ServiceCheckStatus } from '../modules/enumerations.js';
import logger from '../modules/logger.js';
import serviceCheck, { transformServiceCheck }  from '../modules/serviceCheck.js';
import { identifyInputType } from '../modules/inputType.js';
import { generateId } from '../modules/helpers/id.js';


const router = express.Router();


router.get('/validateSearchInput', auth.authorizeForRoles([
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), [
    query('fnn').isString().isLength({ min: 1 })
], async function(req, res) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    const inputType = identifyInputType(req.query.fnn);
    res.send({
        isValid: inputType !== InputTypes.other
    });
});


router.post('/start', auth.authorizeForRoles([
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), [
    body('fnn').isString().isLength({ min: 1 }).trim()
], async function(req, res) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    try {
        let serviceCheckRecord = new ServiceCheckModel();

        serviceCheckRecord.id = generateId('MSC');
        serviceCheckRecord.input.searchFNN = req.body.fnn;

        serviceCheckRecord.startMethod = ServiceCheckStartMethod.userInterface;
        serviceCheckRecord.createdBy = req.user.username;
        serviceCheckRecord.createdOn = new Date();

        serviceCheckRecord.fnn = serviceCheckRecord.input.searchFNN.trim().toUpperCase();

        // Store valid phone number inputs in E.164 format
        if (isValidPhoneNumber(serviceCheckRecord.fnn, 'AU')) {
            serviceCheckRecord.phoneNumber = parsePhoneNumber(serviceCheckRecord.fnn, 'AU').format('E.164');
        }

        await serviceCheck.runServiceCheckInitial(serviceCheckRecord, req.user, null);

        let disableDisplayMessage = serviceCheck.disableDisplayCondition(serviceCheckRecord, req.user);
        let level = _.get(serviceCheckRecord, ['input', 'level'], 0);

        if (disableDisplayMessage || !auth.checkUserAuthorizedForServiceCheckLevel(req.user, level)) {
            let errorMessage;

            if (disableDisplayMessage) {
                errorMessage = disableDisplayMessage.message;
            } else {
                errorMessage = `You do not have access to view a service check record with level: ${level}`;
            }

            res.status(403).send({
                error: errorMessage
            });
        } else {
            let serviceCheckResponse = await transformServiceCheck(serviceCheckRecord, req.user, false);

            if (!serviceCheckResponse.productTypes.length) {
                res.sendStatus(422);
                return;
            }

            res.send(serviceCheckResponse);
        }
    } catch (error) {
        res.sendStatus(500);
        logger.error(`Service check app API, error starting service check, ${error.toString()}`);
    }
});


router.get('/records/:id', auth.authorizeForRoles([
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), [
    param('id').isString().isLength({ min: 1 }).trim()
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let id = req.params.id;

    try {
        let serviceCheckRecord = await serviceCheck.findAndPopulate(id);

        if (serviceCheckRecord) {
            let disableDisplayMessage = serviceCheck.disableDisplayCondition(serviceCheckRecord, req.user);
            let level = _.get(serviceCheckRecord, ['input', 'level'], 0);

            if (disableDisplayMessage || !auth.checkUserAuthorizedForServiceCheckLevel(req.user, level)) {
                let errorMessage;

                if (disableDisplayMessage) {
                    errorMessage = disableDisplayMessage.message;
                } else {
                    errorMessage = `You do not have access to view a service check record with level: ${level}`;
                }

                res.status(403).send({
                    error: errorMessage
                });
            } else {
                let serviceCheckRecordResponse = await transformServiceCheck(serviceCheckRecord, req.user, true);

                res.send(serviceCheckRecordResponse);
            }
        } else {
            res.sendStatus(404);
        }
    } catch(error) {
        logger.error(`Service check API get record, could not obtain service check with ID: ${id}, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.patch('/records/:id/retryStart', auth.authorizeForRoles([
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), [
    param('id').isString().isLength({ min: 1 }).trim()
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let id = req.params.id;

    try {
        let serviceCheckRecord = await serviceCheck.findAndPopulate(id);

        if (serviceCheckRecord === null) {
            res.sendStatus(404);
            return;
        }

        // Retrying a service check initial search not owned by the current user
        // is forbidden
        if (serviceCheckRecord.createdBy !== req.user.username) {
            res.sendStatus(403);
            return;
        }

        if (serviceCheckRecord.status !== ServiceCheckStatus.abortedInitial) {
            res.sendStatus(422);
            return;
        }

        let disableDisplayMessage = serviceCheck.disableDisplayCondition(serviceCheckRecord, req.user);
        let level = _.get(serviceCheckRecord, ['input', 'level'], 0);

        if (disableDisplayMessage || !auth.checkUserAuthorizedForServiceCheckLevel(req.user, level)) {
            let errorMessage;

            if (disableDisplayMessage) {
                errorMessage = disableDisplayMessage.message;
            } else {
                errorMessage = `You do not have access to view a service check record with level: ${level}`;
            }

            res.status(403).send({
                error: errorMessage
            });
        } else {
            await serviceCheck.runServiceCheckInitial(serviceCheckRecord, req.user, null);

            // Check for forbidden service check is repeated here
            let disableDisplayMessage = serviceCheck.disableDisplayCondition(serviceCheckRecord, req.user);
            let level = _.get(serviceCheckRecord, ['input', 'level'], 0);

            if (disableDisplayMessage || !auth.checkUserAuthorizedForServiceCheckLevel(req.user, level)) {
                let errorMessage;

                if (disableDisplayMessage) {
                    errorMessage = disableDisplayMessage.message;
                } else {
                    errorMessage = `You do not have access to view a service check record with level: ${level}`;
                }

                res.status(403).send({
                    error: errorMessage
                });
            } else {
                let serviceCheckResponse = await transformServiceCheck(serviceCheckRecord, req.user, false);

                if (!serviceCheckResponse.productTypes.length) {
                    res.sendStatus(422);
                    return;
                }

                res.send(serviceCheckResponse);
            }
        }
    } catch(error) {
        logger.error(`Service check API get record, could not retry initial search for service check with ID: ${id}, ${error.toString()}`);
        res.sendStatus(500);
    }
});


export default router;
