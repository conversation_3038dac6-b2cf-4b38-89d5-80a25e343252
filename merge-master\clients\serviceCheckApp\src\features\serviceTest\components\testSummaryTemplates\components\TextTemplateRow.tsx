import { TextStyle } from '@able/react'
import { useState } from 'react'

import { useGetTextTemplateQuery } from '../../../../../api/queries/useGetTextTemplateQuery'
import { ContentModal } from '../../../../../components/contentModal/ContentModal'
import { TextTemplate } from '../../../../../infrastructure/models'
import { PopupButton } from '../../popupButton/PopupButton'
import styles from '../TestSummaryTemplates.module.scss'

interface TextTemplateRowProps {
  template: TextTemplate
  serviceId: string
}

export const TextTemplateRow = ({ template, serviceId }: TextTemplateRowProps) => {
  const [showModal, setShowModal] = useState(false)
  const [modalContent, setModalContent] = useState<string>('')

  // Initialize query with the appId and the current template name.
  const { refetch: getTextTemplate, isFetching } = useGetTextTemplateQuery(
      serviceId,
      template.name
  )

  const handleClick = async () => {
      setShowModal(true)
      const response = await getTextTemplate()

      setModalContent(response.data as string)
  }

  return (
      <>
          <div className={styles.summaryRow}>
              <TextStyle>{template.title}</TextStyle>
              <PopupButton onClick={handleClick} />
          </div>

          {showModal && (
              <ContentModal
                  isOpen={showModal}
                  title={template.title}
                  isLoading={isFetching}
                  onClose={() => {
                      setShowModal(false)
                      setModalContent('')
                  }}
              >
                  <pre>{modalContent}</pre>
              </ContentModal>
          )}
      </>
  )
}
