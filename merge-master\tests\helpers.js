
import _ from 'lodash';
import chai from 'chai';
import cookie from 'cookie';
import moment from 'moment';

import { UNIT_TEST_PASSWORD } from './users.js';

const dateFieldCheck = {
    // Match the field that can be parsed as an ISO datetime string
    match: function(object) {
        return object instanceof Date || (Date.parse(object) && new Date(Date.parse(object)).toISOString() === object);
    },
    // Check that parsed datetime is within 1 minute of the expected datetime
    assert: function(object, expected) {
        return chai.expect(new Date(Date.parse(object))).to.closeToTime(expected, 60);
    }
};

const regexCheck = {
    match: function(object, expected) {
        return typeof object === 'string' && expected instanceof RegExp;
    },
    assert: function(object, expected) {
        return expected.test(object);
    }
};


async function authenticateApi(request, username) {
    let res = await request.post('/api/authenticate').send({ username: username, password: UNIT_TEST_PASSWORD });
    return res.body.token;
}


async function authenticateSession(request, username) {
    let res = await request.post('/auth/login').send({
        username: username,
        password: UNIT_TEST_PASSWORD
    });

    if (res.headers['set-cookie']) {
        res.headers['set-cookie'].forEach(setCookie => {
            let setCookieObj = cookie.parse(setCookie);
            if ('connect.sid' in setCookieObj) {
                request.set('Cookie', cookie.serialize('connect.sid', setCookieObj['connect.sid']));
            }
        });
    }
}


// Custom deep comparison function for objects to check if the key order is the same
function compareObjectWithKeyOrder(instance1, instance2) {
    if (instance1 === instance2) {
        return true;
    }

    if (_.isPlainObject(instance1) && _.isPlainObject(instance2)) {
        for (const [key, value] of Object.entries(instance1)) {
            if (Object.keys(instance1).indexOf(key) != Object.keys(instance2).indexOf(key)) {
                return false;
            }

            if (!compareObjectWithKeyOrder(value, instance2[key])) {
                return false;
            }
        }

        return true;
    } else {
        return _.isEqual(instance1, instance2);
    }
}

// Should be the same logic as dateTransform in modules/csv.js
function formatDateForCsv(date) {
    return moment(date).format(global.gConfig.dateTimeFormat);
}


// Parsing function for download buffer passed into supertest.parse()
function parseDownloadBuffer(response, callback) {
    response.data = '';
    response.on('data', (chunk) => {
        response.data += chunk;
    });

    response.on('end', () => {
        callback(null, response.data);
    });
}


export default {
    authenticateApi: authenticateApi,
    authenticateSession: authenticateSession,
    compareObjectWithKeyOrder: compareObjectWithKeyOrder,
    dateFieldCheck: dateFieldCheck,
    formatDateForCsv: formatDateForCsv,
    parseDownloadBuffer: parseDownloadBuffer,
    regexCheck: regexCheck
};
