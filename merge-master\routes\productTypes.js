'use strict';

import express from 'express';
import fs from 'fs';

import auth from '../modules/auth.js';
import { AuthorizationRoles } from '../modules/enumerations.js';

const productTypes = JSON.parse(await fs.promises.readFile('./config/productTypes.json'));
const router = express.Router();


router.get('/', auth.authorizeForRoles([
    AuthorizationRoles.readOnly,
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), async function(req, res) {
    try {
        let productTypesBase64 = Buffer.from(JSON.stringify(productTypes)).toString('base64');
        res.render('productTypes', { user: req.user, productTypes: productTypesBase64, title: 'Product Types' });
    } catch(error) {
        res.sendStatus(500);
    }
});


export default router;
