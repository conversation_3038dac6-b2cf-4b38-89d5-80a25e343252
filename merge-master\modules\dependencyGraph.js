
import _ from 'lodash';

import Rule from '../db/model/rule.js';
import Source from '../db/model/source.js';


export class DependencyGraph {
    /**
     * Creates a directional graph represented by an array of arrays containing
     * numbers which are indexes for the destination of a graph edge, with the
     * source of the graph edge being the index of the array.
     * Example:
     *   graphEdges = [
     *       [1, 2],
     *       [2],
     *       []
     *   ]
     * This represents a graph with 3 vertices where there are directed edges
     * from node 0 to 1, node 0 to 2 and node 1 to 2.
     * The graph created will have 2 types of nodes, 1 for rules and 1 for sources,
     * this will change how the search function behaves.
     * In this graph, the source vertex represents a rule or source that
     * the destination vertex depends on.
     * @param {Array[Rule]} rules Array of rules to add to the graph
     * @param {Array[Source]} sources Array of sources to add to the graph
     */
    constructor(rules, sources) {
        if (!(rules && Array.isArray(rules)) || !(sources && Array.isArray(sources))) {
            throw new TypeError('Expected arrays for inputs to DependencyGraph constructor');
        }

        let vertexCount = rules.length + sources.length;

        this.edges = Array.from(Array(vertexCount), () => []);
        this.vertices = [];
        this.rulesIndexByName = {};
        this.sourcesIndexByName = {};

        let vertexIndex = 0;

        rules.forEach(rule => {
            this.vertices.push(rule);
            this.rulesIndexByName[rule.name] = vertexIndex;
            vertexIndex++;
        });

        sources.forEach(source => {
            this.vertices.push(source);
            this.sourcesIndexByName[source.name] = vertexIndex;
            vertexIndex++;
        });

        rules.forEach(rule => {
            let ruleIndex = this.rulesIndexByName[rule.name];

            for (let preRule of rule.preRules) {
                let preRuleIndex = _.get(this.rulesIndexByName, [preRule]);
                // Ignore edges from rule if it depends on itself
                if (preRuleIndex !== undefined && ruleIndex !== preRuleIndex) {
                    this.edges[preRuleIndex].push(ruleIndex);
                }
            }

            for (let preSource of rule.preSources) {
                let preSourceIndex = _.get(this.sourcesIndexByName, [preSource]);
                if (preSourceIndex !== undefined) {
                    this.edges[preSourceIndex].push(ruleIndex);
                }
            }
        });

        sources.forEach(source => {
            let sourceIndex = this.sourcesIndexByName[source.name];

            for (let preRule of source.preRules) {
                let preRuleIndex = _.get(this.rulesIndexByName, [preRule]);
                if (preRuleIndex !== undefined) {
                    this.edges[preRuleIndex].push(sourceIndex);
                }
            }

            for (let preSource of source.preSources) {
                let preSourceIndex = _.get(this.sourcesIndexByName, [preSource]);
                // Ignore edges from source if it depends on itself
                if (preSourceIndex !== undefined && sourceIndex !== preSourceIndex) {
                    this.edges[preSourceIndex].push(sourceIndex);
                }
            }
        });
    }

     /**
     * Implements Kahn's algorithm to sort the rules and sources in a topological order to ensure each
     * rule / source in the array has all its dependencies resolved in earlier elements.
     * Note: This only works on directed acyclic graphs.
     * @returns {Array} Array with rules and sources from initial vertices, sorted in topological order
     */
    topologicalSort() {
        let sortedVertices = [];
        let visitQueue = [];

        // Creates a reverse directed edge list
        let inDegree = Array(this.vertices.length).fill(0);

        for (let node in this.edges) {
            for (let i = 0; i < this.edges[node].length; i++) {
                let destination = this.edges[node][i];
                inDegree[destination]++;
            }
        }

        for (let node in inDegree) {
            if (inDegree[node] === 0) {
                visitQueue.push(node);
            }
        };

        while (visitQueue.length) {
            let node = visitQueue.shift();
            sortedVertices.push(this.vertices[node]);
            for (let i = 0; i < this.edges[node].length; i++) {
                let destination = this.edges[node][i];
                inDegree[destination]--;
                if (inDegree[destination] === 0) {
                    visitQueue.push(destination);
                }
            }
        }

        return sortedVertices;
    }

    /**
     * Performs a topological sort on the graph and determines if it is acyclic if the topologically sorted
     * array contains the same number of vertices as the entire graph
     * @returns {Boolean} Boolean value representing whether the dependency graph is acyclic
     */
    isAcyclic() {
        let sortedVertices = this.topologicalSort();
        return sortedVertices.length === this.vertices.length;
    }

    /**
     * Reduces the graph to include only the specified rule names and source names and the dependencies required
     * to run the specified rules and sources
     * Implementation is a breadth-first search on the directed graph with the edge direction reversed
     * (where an edge from A to B means A depends on B)
     */
    filterVertices(ruleNamesSet, sourceNamesSet) {
        let ruleNames = Array.from(ruleNamesSet);
        let sourceNames = Array.from(sourceNamesSet);

        // Creates a reversed edges array to perform BFS on the filtered rule / source names
        let reversedEdges = Array.from(Array(this.vertices.length), () => []);

        for (let i = 0; i < this.edges.length; i++) {
            for (let j = 0; j < this.edges[i].length; j++) {
                let target = this.edges[i][j];
                reversedEdges[target].push(i);
            }
        }

        let visitedVertices = Array(this.vertices.length).fill(false);
        let visitQueue = [];

        for (let i = 0; i < ruleNames.length; i++) {
            let ruleIndex = _.get(this.rulesIndexByName, [ruleNames[i]]);
            if (ruleIndex !== undefined) {
                visitQueue.push(ruleIndex);
                visitedVertices[ruleIndex] = true;
            }
        }

        for (let i = 0; i < sourceNames.length; i++) {
            let sourceIndex = _.get(this.sourcesIndexByName, [sourceNames[i]]);
            if (sourceIndex !== undefined) {
                visitQueue.push(sourceIndex);
                visitedVertices[sourceIndex] = true;
            }
        }

        while (visitQueue.length) {
            let node = visitQueue.shift();
            for (let i = 0; i < reversedEdges[node].length; i++) {
                let destination = reversedEdges[node][i];
                if (!visitedVertices[destination]) {
                    visitedVertices[destination] = true;
                    visitQueue.push(destination);
                }
            }
        }

        // Recreates the graph and its edges from the visited vertices
        let vertices = [];
        let rules = [];
        let sources = [];
        let vertexIndex = 0;

        this.rulesIndexByName = {};
        this.sourcesIndexByName = {};
        for (let i = 0; i < visitedVertices.length; i++) {
            if (visitedVertices[i]) {
                if (this.vertices[i] instanceof Rule) {
                    this.rulesIndexByName[this.vertices[i].name] = vertexIndex;
                    rules.push(this.vertices[i]);
                } else if (this.vertices[i] instanceof Source) {
                    this.sourcesIndexByName[this.vertices[i].name] = vertexIndex;
                    sources.push(this.vertices[i]);
                }
                vertices.push(this.vertices[i]);
                vertexIndex++;
            }
        }

        // Sets vertices for Graph instance to new array
        this.vertices = vertices;
        this.edges = Array.from(Array(this.vertices.length), () => []);

        rules.forEach(rule => {
            let ruleIndex = this.rulesIndexByName[rule.name];

            for (let preRule of rule.preRules) {
                let preRuleIndex = _.get(this.rulesIndexByName, [preRule]);
                // Ignore edges from rule if it depends on itself
                if (preRuleIndex !== undefined && ruleIndex !== preRuleIndex) {
                    this.edges[preRuleIndex].push(ruleIndex);
                }
            }

            for (let preSource of rule.preSources) {
                let preSourceIndex = _.get(this.sourcesIndexByName, [preSource]);
                if (preSourceIndex !== undefined) {
                    this.edges[preSourceIndex].push(ruleIndex);
                }
            }
        });

        sources.forEach(source => {
            let sourceIndex = this.sourcesIndexByName[source.name];

            for (let preRule of source.preRules) {
                let preRuleIndex = _.get(this.rulesIndexByName, [preRule]);
                if (preRuleIndex !== undefined) {
                    this.edges[preRuleIndex].push(sourceIndex);
                }
            }

            for (let preSource of source.preSources) {
                let preSourceIndex = _.get(this.sourcesIndexByName, [preSource]);
                // Ignore edges from source if it depends on itself
                if (preSourceIndex !== undefined && sourceIndex !== preSourceIndex) {
                    this.edges[preSourceIndex].push(sourceIndex);
                }
            }
        });
    }

    /**
     * Finds if the start vertex depends on the dependency vertex
     * @param {Rule|Source} startVertex
     * @param {Rule|Source} dependencyVertex
     * @returns {boolean}
     */
    findIfVertexIsDependency(startVertex, dependencyVertex) {
        let startVertexIndex = this.vertices.indexOf(startVertex);
        let dependencyVertexIndex = this.vertices.indexOf(dependencyVertex);

        if (startVertexIndex === -1 || dependencyVertexIndex === -1) {
            return false;
        }

        // Note: Could be optimised as this is run everytime this function is called
        let reversedEdges = Array.from(Array(this.vertices.length), () => []);

        for (let i = 0; i < this.edges.length; i++) {
            for (let j = 0; j < this.edges[i].length; j++) {
                let target = this.edges[i][j];
                reversedEdges[target].push(i);
            }
        }

        let visitedVertices = Array(this.vertices.length).fill(false);
        let visitQueue = [];
        visitQueue.push(startVertexIndex);

        while (visitQueue.length) {
            let node = visitQueue.shift();
            for (let i = 0; i < reversedEdges[node].length; i++) {
                let destination = reversedEdges[node][i];
                if (!visitedVertices[destination]) {
                    visitedVertices[destination] = true;
                    visitQueue.push(destination);
                }
            }
        }

        return visitedVertices[dependencyVertexIndex];
    }
}
