@use '@able/web/src/index' as able;

.paginatedTable {
    margin-top: 1rem;

    .clickableRow {
        cursor: pointer;
    }
    table {
        width: 100%;
        border-collapse: collapse;
    }

    th,
    td {
        padding: able.spacing(spacing1x) able.spacing(spacing2x);
        border: 1px solid #ddd;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .headerCellContent {
        display: flex;
        align-items: center;
        gap: 1rem;
        justify-content: space-between;
        cursor: pointer;
    }

    .sortIconUp {
        margin-top: 0.5rem;
    }

    .sortIconDown {
        margin-bottom: 0.5rem;
    }

    .pageActions {
        display: flex;
        gap: 1rem;
        margin-top: 1rem;
    }

    .copyToClipboardButton {
        padding: able.spacing(spacing1x) able.spacing(spacing2x);
        background-color: white;
        color: able.color(textOnPrimary);
        border: 1px solid able.color(mat);
        border-radius: 10px;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
    }

    .downloadButton {
        padding: able.spacing(spacing1x) able.spacing(spacing2x);
        background-color: white;
        color: able.color(textOnPrimary);
        border: 1px solid able.color(mat);
        border-radius: 10px;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
    }

    .buttonGroup {
        display: flex;
        gap: 1rem;
    }
}
