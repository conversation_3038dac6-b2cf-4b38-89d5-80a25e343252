<%- include('header', {}); %>
<%- include('menu', {currentTab: 'command'}); %>
<script src="/public/javascripts/jquery-dateformat.js" crossorigin="anonymous"></script>
<script src="/public/javascripts/merge_QC_lib.js" crossorigin="anonymous"></script>
<div class="container">
    <br>
    <div class="jumbotron py-1">
        <h2 class="display-4"><%= title  %></h2>
        <div>
            <b>User</b>: <%= user.username%> <br />
            <b>Name</b>: <%= user.name %> <br />
            <b>Email</b>: <%= user.mail %> <br />
            <b>Department</b>: <%= user.department %> <br />
            <b>Business Unit</b>: <%= user.businessUnit %> <br />
            <b>SSU</b>: <%= user.SSU %> <br />
            <b>Merge Permissions</b>: <%= user.mergeGroups %> <br />
            <b>All Permissions</b>: <%= user.groups %> <br />
            <b>Is Robot</b>: <%= user.isRobot %> <br />
        </div>
        <br />
        <h2>Permissions</h2>
        <div>
            <b>Read Only</b>: <%= user.readOnly %> <br />
            <b>Level 0 (FOH)</b>: <%= user.level0 %> <br />
            <b>Level 1</b>: <%= user.level1 %> <br />
            <b>Level 2</b>: <%= user.level2 %> <br />
            <b>Level 3</b>: <%= user.level3 %> <br />
            <b>Command Access</b>: <%= user.commandAccess %> <br />
            <b>Offshore Access</b>: <%= user.offshoreAccess %> <br />
            <b>OutsideAustralia</b>: <%= user.outsideAustralia %> <br />
            <b>Is AAA User</b>: <%= user.isAAAUser %> <br />
            <b>Administration Accesses</b>:<br />
            <div class="ml-2">
            <b>Level Lead</b>: <%= user.levelLead %> <br />
            <b>Is Admin</b>: <%= user.isAdmin %> <br />
            <b>Is Developer</b>: <%= user.isDeveloper %> <br />
            <b>Is Wholesale User</b>: <%= user.isWholesaleUser %> <br />
            </div>
        </div>
    </div>
</div>
<%- include('footer', {}); %>