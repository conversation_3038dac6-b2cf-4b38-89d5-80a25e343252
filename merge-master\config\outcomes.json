{"version": 30, "updatedOn": "2023-10-30T04:22:39.439Z", "updatedBy": "d888090", "outcomes": [{"name": "comb001", "title": "Active Service Central / SIIAM / Promise ticket and create NBN incident", "description": "Derived outcome if results are create NBN incident but there is an active Assurance ticket", "header": "", "display": false, "tier": 1, "weight": 550, "messageBucketName": "NBN_CheckActiveTicket"}, {"name": "comb002", "title": "Active Service Central / SIIAM / Promise ticket and GMACS issue for BDSL", "description": "Derived outcome if results a GMACS issue for BDSL but there is an active Assurance ticket", "header": "", "display": false, "tier": 1, "weight": 555, "messageBucketName": "BDSL_CheckActiveTicket"}, {"name": "comb003", "title": "Active Service Central / SIIAM / Promise ticket and MDN issue", "description": "", "header": "", "display": false, "tier": 1, "weight": 540, "messageBucketName": "MDN_CheckActiveTicket"}, {"name": "comb004", "title": "INC/CRQ active outage", "description": "Derived outcome if NBN INC needs to be created, but INC/CRQ already exists", "header": "", "display": false, "tier": 1, "weight": 600, "messageBucketName": "NBN_ActiveOutageINC"}, {"name": "comb005", "title": "Active Service Central / SIIAM / Promise ticket and IPMAN issue", "description": "Derived outcome if results IPMAN issue but there is an active Assurance ticket", "header": "", "display": false, "tier": 1, "weight": 560, "messageBucketName": "IPMAN_CheckActiveTicket"}, {"name": "comb006", "title": "Power Outage present and no successful ping", "description": "A power outage may be affecting this service, determined from the national power outage data, and no successful VPN ping or ping to FAE", "header": "There is currently a power issue that may be impacting your service", "display": true, "tier": 1, "weight": 24, "messageBucketName": "Furth_Inv_Outage"}, {"name": "gma000", "title": "BDSL service test run", "description": "A test was run to check BDSL service", "header": "--Testing of your BDSL service indicates--", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "gma001", "title": "BDSL NTU type test run", "description": "BDSL NTU type NTU54R was found", "header": "The NTU type is NTU54R", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "gma002", "title": "BDSL NTU type test run", "description": "BDSL NTU type EFM was found", "header": "The NTU type is EFM", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "gma003", "title": "BDSL NTU loop test run", "description": "BDSL NTU loop test was successful", "header": "We can successfully loop to the NTU", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "gma004", "title": "BDSL NTU loop test run", "description": "BDSL NTU loop test was unsuccessful", "header": "Can't loop NTU", "display": true, "tier": 1, "weight": 280, "messageBucketName": "BDSL_Reload_NTU"}, {"name": "gma005", "title": "BDSL Ethernet 1/0 interface test run", "description": "BDSL Ethernet 1/0 interface test was successful", "header": "Ethernet 1/0 (Customer facing interface) status is Up", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "gma006", "title": "BDSL Ethernet 1/0 interface test run", "description": "BDSL Ethernet 1/0 interface test was unsuccessful", "header": "Ethernet 1/0 (Customer facing interface) status is Down", "display": true, "tier": 1, "weight": 300, "messageBucketName": "BDSL_Check_connection_CPE_Adtran"}, {"name": "gma007", "title": "BDSL input and output traffic monitored", "description": "BDSL input and output traffic found", "header": "The tx and rx incrementing", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "gma008", "title": "BDSL input and output traffic monitored", "description": "BDSL input and output traffic not found", "header": "The tx and rx not incrementing", "display": true, "tier": 1, "weight": 320, "messageBucketName": "BDSL_Pass_to_CT"}, {"name": "inv000", "title": "NBN Product Info Collected", "description": "The NBNSearch source was run and a response was obtained", "header": "--NBN Product Instance Record Info--", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "inv001", "title": "NBN Product Info service is active", "description": "The NBNSearch identified the AVC as an active service", "header": "This is an active NBN service", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "inv002", "title": "NBN Product Info service is deleted", "description": "The NBNSearch identified the AVC as a disabled service", "header": "This service is not an active service", "display": true, "tier": 0, "weight": 500, "messageBucketName": "Account_team"}, {"name": "inv003", "title": "NBN Product Info service status is unknown", "description": "The NBNSearch could not identify the status of the service (AVC not found or other status)", "header": "The status of this NBN service is unknown, there is a possible issue with Telstra records", "display": true, "tier": 1, "weight": 490, "messageBucketName": "Account_team"}, {"name": "inv004", "title": "NBN Product Info service is SFP", "description": "The NBNSearch shows that this NBN service's NTD type is SFP (Smart Places)", "header": "This NBN FTTP is a Smart Places Service.", "display": true, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "ipm000", "title": "IPMAN Demarc point (customer facing) MAC address of the device is connected", "description": "The CPE MAC address is present", "header": "Determined the CPE MAC address of Basement Switch / NTU (customer facing interface)", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "ipm001", "title": "IPMAN Demarc point (customer facing) MAC address of the device is not connected", "description": "The CPE MAC address is not present", "header": "CPE MAC address of Basement Switch / NTU (customer facing interface) not found", "display": true, "tier": 1, "weight": 330, "messageBucketName": "Furth_Inv_IPMAN"}, {"name": "ipm002", "title": "IPMAN Demarc point (customer facing) Interface state is up", "description": "The Demarc point Interface state is up", "header": "Basement Switch / NTU (customer facing interface) is up", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "ipm003", "title": "IPMAN Demarc point (customer facing) Interface state is down", "description": "The Demarc point Interface state is down", "header": "Basement Switch / NTU (customer facing interface) is down", "display": true, "tier": 1, "weight": 360, "messageBucketName": "Create_IPMAN_INC"}, {"name": "ipm004", "title": "IPMAN Demarc point (customer facing) Interface state is unknown", "description": "The Demarc point Interface state is unknown", "header": "Basement Switch / NTU (customer facing interface) is Unknown", "display": true, "tier": 1, "weight": 380, "messageBucketName": "Furth_Inv_IPMAN"}, {"name": "ipm005", "title": "IPMAN Demarc point (customer facing) current speed and duplex set", "description": "The current speed and duplex is set", "header": "Found current speed and duplex settings of device, ensure the duplex settings of your device is configured correctly (Basement Switch / NTU - customer facing interface)", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "ipm006", "title": "IPMAN Demarc point (customer facing) packetMetricsIncreasedAllInterfaces incrementing", "description": "IPMAN Demarc point packets  incrementing", "header": "IPMAN Basement Switch / NTU (customer facing interface) packets  incrementing", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "ipm007", "title": "IPMAN Demarc point (customer facing) packetMetricsIncreasedAllInterfaces not incrementing", "description": "IPMAN Demarc point packets not incrementing", "header": "IPMAN Basement Switch / NTU (customer facing interface) packets not incrementing", "display": true, "tier": 3, "weight": 75, "messageBucketName": "Furth_Inv_IPMAN"}, {"name": "ipm008", "title": "IPMAN Demarc point (customer facing) errorMetricsNotIncreasedAllInterfaces incrementing", "description": "IPMAN Demarc point errors incrementing", "header": "IPMAN Basement Switch / NTU (customer facing interface) errors incrementing", "display": true, "tier": 3, "weight": 75, "messageBucketName": "Furth_Inv_IPMAN"}, {"name": "ipm009", "title": "IPMAN Demarc point (customer facing) errorMetricsNotIncreasedAllInterfaces not incrementing", "description": "IPMAN Demarc point errors not incrementing", "header": "IPMAN Basement Switch / NTU (customer facing interface) errors not incrementing", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "ipm010", "title": "IPMAN Demarc point (network facing) MAC address of the device is connected", "description": "The CPE MAC address is present", "header": "Determined the CPE MAC address of Basement Switch / NTU (network facing interface)", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "ipm011", "title": "IPMAN Demarc point (network facing) MAC address of the device is not connected", "description": "The CPE MAC address is not present", "header": "CPE MAC address of Basement Switch / NTU (network facing interface) not found", "display": true, "tier": 1, "weight": 330, "messageBucketName": "Furth_Inv_IPMAN"}, {"name": "ipm012", "title": "IPMAN Demarc point (network facing) Interface state is up", "description": "The Demarc point Interface state is up", "header": "Basement Switch / NTU (network facing interface) is up", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "ipm013", "title": "IPMAN Demarc point (network facing) Interface state is down", "description": "The Demarc point Interface state is down", "header": "Basement Switch / NTU (network facing interface) is down", "display": true, "tier": 1, "weight": 360, "messageBucketName": "Create_IPMAN_INC"}, {"name": "ipm014", "title": "IPMAN Demarc point (network facing) Interface state is unknown", "description": "The Demarc point Interface state is unknown", "header": "Basement Swicth / NTU (network facing interface) state is unknown", "display": true, "tier": 1, "weight": 380, "messageBucketName": "Furth_Inv_IPMAN"}, {"name": "ipm015", "title": "IPMAN Demarc point (network facing) current speed and duplex set", "description": "The current speed and duplex is set", "header": "Found current speed and duplex settings of device, ensure the duplex settings of your device is configured correctly (Basement Switch / NTU - network facing interface)", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "ipm016", "title": "IPMAN Demarc point (network facing) packetMetricsIncreasedAllInterfaces incrementing", "description": "IPMAN Demarc point packets  incrementing", "header": "IPMAN Basement Switch / NTU (network facing interface) packets  incrementing", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "ipm017", "title": "IPMAN Demarc point (network facing) packetMetricsIncreasedAllInterfaces not incrementing", "description": "IPMAN Demarc point packets not incrementing", "header": "IPMAN Basement Switch / NTU (network facing interface) packets not incrementing", "display": true, "tier": 3, "weight": 75, "messageBucketName": "Furth_Inv_IPMAN"}, {"name": "ipm018", "title": "IPMAN Demarc point (network facing) errorMetricsNotIncreasedAllInterfaces incrementing", "description": "IPMAN Demarc point errors incrementing", "header": "IPMAN Basement Switch / NTU (network facing interface) errors incrementing", "display": true, "tier": 3, "weight": 75, "messageBucketName": "Furth_Inv_IPMAN"}, {"name": "ipm019", "title": "IPMAN Demarc point (network facing) errorMetricsNotIncreasedAllInterfaces not incrementing", "description": "IPMAN Demarc point errors not incrementing", "header": "IPMAN Basement Switch / NTU (network facing interface) errors not incrementing", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "ipm020", "title": "IPMAN Next Upstream device Interface state is up", "description": "IPMAN Next Upstream device Interface state is up", "header": "IPMAN Next Upstream device (POP) Interface state is up", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "ipm021", "title": "IPMAN Next Upstream device Interface state is down", "description": "IPMAN Next Upstream device Interface state is down", "header": "IPMAN Next Upstream device (POP) Interface state is down", "display": true, "tier": 1, "weight": 360, "messageBucketName": "Create_IPMAN_INC"}, {"name": "ipm022", "title": "IPMAN Next Upstream device Interface state is unknown", "description": "IPMAN Next Upstream device Interface state is unknown", "header": "IPMAN Next Upstream device (POP) Interface state is unknown", "display": true, "tier": 1, "weight": 380, "messageBucketName": "Furth_Inv_IPMAN"}, {"name": "ipm023", "title": "IPMAN Next Upstream device current speed and duplex set", "description": "IPMAN Next Upstream device current speed and duplex set", "header": "IPMAN Next Upstream device (POP) current speed and duplex set", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "map000", "title": "Primary interface test run", "description": "A test was run to check interface available", "header": "--Access status--", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "map001", "title": "Primary interface test successful", "description": "A primary interface was found", "header": "Primary Interface 'Found'", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "map002", "title": "Primary interface test unsuccessful", "description": "A primary interface was not found", "header": "Primary Interface 'Not Found'", "display": true, "tier": 3, "weight": 80, "messageBucketName": "Furth_Inv_MDN_L3"}, {"name": "map003", "title": "Primary interface available test successful", "description": "A primary interface is available", "header": "Primary access tested as OK / up", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "map004", "title": "Primary interface available test unsuccessful", "description": "A primary interface is not available", "header": "Primary access tested as failed / not found", "display": true, "tier": 3, "weight": 100, "messageBucketName": "Furth_Inv_MDN_L3"}, {"name": "mas005", "title": "Secondary interface test run", "description": "A test was run to check interface available", "header": "--Secondary interface--", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "mas006", "title": "Secondary interface test successful", "description": "A secondary interface was found", "header": "Secondary interface has been found", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "mas007", "title": "Secondary interface test unsuccessful", "description": "A secondary interface was not found", "header": "Secondary interface has not been found", "display": true, "tier": 3, "weight": 65, "messageBucketName": "Furth_Inv_MDN_L3"}, {"name": "mas008", "title": "Secondary interface available test successful", "description": "A secondary interface is available", "header": "and secondary access tested as 'IN USE'", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "mas009", "title": "Secondary interface available test unsuccessful", "description": "A secondary interface is not available", "header": "Secondary access tested as 'NOT IN USE'", "display": true, "tier": 3, "weight": 70, "messageBucketName": "Furth_Inv_MDN_L3"}, {"name": "mdn000", "title": "Device power and status check", "description": "A check was run for device power and status", "header": "--Device power and status checks shows--", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "mdn001", "title": "Device power check successful", "description": "Device power was found", "header": "There is power on your device", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "mdn002", "title": "Device power check unsuccessful", "description": "Device power check was not found", "header": "Unable to confirm the device power status", "display": true, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "mdn004", "title": "Device status test successful", "description": "Device status available", "header": "Named device is up", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "mdn005", "title": "Device status test unsuccessful", "description": "Device status unavailable", "header": "[UpTime] Unable to determine device status", "display": true, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "mdn006", "title": "Packets drop check", "description": "A packets drop check", "header": "Intermittent Comms and latency / packet loss templates includes 'pkts dropped from input and output queue'", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "mdn007", "title": "No packets drop", "description": "No packets drop were detected", "header": "There are no detected packets drops", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "mdn008", "title": "Packets drop", "description": "Packets drop on managed device were detected", "header": "Packet drops on managed device were detected", "display": true, "tier": 3, "weight": 75, "messageBucketName": "Furth_Inv_MDN_L3"}, {"name": "net000", "title": "Ping test run", "description": "A ping test was run with 5 packets with the VPNPING source", "header": "--A ping test was initiated from within your network to the WAN IP of your device--", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "net001", "title": "Ping test successful", "description": "All packets sent to device from the ping test were received", "header": "Ping to WAN IP successful", "display": true, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "net002", "title": "Ping test failed", "description": "No packets sent to device from the ping test were received", "header": "Ping to WAN IP failed", "display": true, "tier": 2, "weight": 40, "messageBucketName": "Furth_Inv_Layer3"}, {"name": "net003", "title": "Ping test partially successful", "description": "At least 1 packet sent to the device was received, but some failed", "header": "<PERSON> Fails Intermittently", "display": true, "tier": 2, "weight": 40, "messageBucketName": "Furth_Inv_Perf_More_Info"}, {"name": "net004", "title": "Loopback test successful", "description": "Loopback packets sent to device from the ping test were received", "header": "Able to ping the loopback address, ping the device", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "net005", "title": "Loopback test failed", "description": "Loopback packets sent to device from the ping test were not received", "header": "Not able to ping the loopback address, ping the device", "display": true, "tier": 3, "weight": 35, "messageBucketName": "Furth_Inv_MDN_L3"}, {"name": "net006", "title": "Primary access connection test successful", "description": "Primary access connection packets sent to device from the ping test were received", "header": "Able to ping the primary access connection on the device", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "net007", "title": "Primary access connection test failed", "description": "Primary access connection packets sent to device from the ping test were not received", "header": "Not able to ping the primary access connection on the device", "display": true, "tier": 3, "weight": 30, "messageBucketName": "Furth_Inv_MDN_L3"}, {"name": "net008", "title": "Ping test successful on FAE IP address", "description": "All packets sent to device (FAE IP address) from the ping test were received", "header": "Ping to WAN IP successful (FAE IP)", "display": true, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "oc000", "title": "Outage checks run", "description": "The \"CMART\", \"CONEN\" or \"Hangar - National Power Outage Data\" sources were collected and information on planned / unplanned / power outages has been obtained", "header": "--Outage checks indicate--", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "oc001", "title": "No planned outages", "description": "No planned outages for the service as identified by CMART", "header": "No planned outages", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "oc002", "title": "Planned outages present", "description": "1 or more planned outages for the service as identified by CMART", "header": "Planned event may be impacting the service", "display": true, "tier": 1, "weight": 28, "messageBucketName": "Furth_Inv_Outage"}, {"name": "oc003", "title": "No unplanned outages", "description": "No planned outages for the service as identified by CONEN", "header": "No unplanned outages", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "oc004", "title": "Unplanned outages present", "description": "1 or more unplanned outages for the service as identified by CONEN", "header": "Unplanned event may be impacting the service", "display": true, "tier": 1, "weight": 26, "messageBucketName": "Furth_Inv_Outage"}, {"name": "oc005", "title": "No power outages", "description": "No power outage for the service, determined from the national power outage data", "header": "No current power issues in the area that may be impacting your service", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "oc006", "title": "Power outage in area", "description": "A power outage may be affecting this service, determined from the national power outage data", "header": "There is currently a power issue that may be impacting your service", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs000", "title": "NBN Service Health check run", "description": "NBN Service Health check was performed on the NBN AVC", "header": "--Further testing of your NBN service indicates--", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs001", "title": "NBN Service Health connectivity OK", "description": "The connectivity status of the NBN service according to the Service Health API is good", "header": "There is connectivity between your equipment and the NBN", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs002", "title": "NBN Service Health connectivity not OK", "description": "The connectivity status of the NBN service according to the Service Health API is not good", "header": "There is no connectivity between Telstra/Customer CPE and NBN", "display": true, "tier": 1, "weight": 460, "messageBucketName": "Create_NBN_INC_HardDwn"}, {"name": "shs003", "title": "NBN Service Health performance OK", "description": "The performance of the NBN service according to the Service Health API is good", "header": "Checks indicate the performance of this link is within NBN specification", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs004", "title": "NBN Service Health performance not OK", "description": "The performance of the NBN service according to the Service Health API is not good", "header": "Checks indicate the performance of this link may experience reduced performance", "display": true, "tier": 1, "weight": 170, "messageBucketName": "Create_NBN_INC_PI"}, {"name": "shs005", "title": "NBN Service Health stability OK", "description": "The stability of the NBN service according to the Service Health API is good", "header": "The stability indicator returns an OK indicating the service is stable", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs006", "title": "NBN Service Health stability not OK", "description": "The stability of the NBN service according to the Service Health API is not good", "header": "Stability Impacted", "display": true, "tier": 1, "weight": 168, "messageBucketName": "Create_NBN_INC_PI"}, {"name": "shs007", "title": "NBN Service Health operational status OK", "description": "The operational status of the NBN service is up", "header": "The operational status indicator shows the status as OK", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs008", "title": "NBN Service Health operational status not OK", "description": "The operational status of the NBN service is not up", "header": "Operational Status RED (Not OK)", "display": true, "tier": 1, "weight": 470, "messageBucketName": "Create_NBN_INC_HardDwn"}, {"name": "shs009", "title": "NBN Service Health downstream seamless rate adaption is dynamic", "description": "The downstream seamless rate adaption for this service is dynamic", "header": "Downstream Seamless Rate Adaption is set to dynamic", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs010", "title": "NBN Service Health downstream seamless rate adaption is not dynamic", "description": "The downstream seamless rate adaption for this service is not dynamic", "header": "Downstream SRA not set to dynamic", "display": true, "tier": 4, "weight": 10, "messageBucketName": "INFO_SRA"}, {"name": "shs011", "title": "NBN Service Health upstream seamless rate adaption is dynamic", "description": "The upstream seamless rate adaption for this service is dynamic", "header": "Upstream Seamless Rate Adaption is set to dynamic", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs012", "title": "NBN Service Health upstream seamless rate adaption is not dynamic", "description": "The upstream seamless rate adaption for this service is not dynamic", "header": "Upstream SRA not set to dynamic", "display": true, "tier": 4, "weight": 10, "messageBucketName": "INFO_SRA"}, {"name": "shs014", "title": "NBN Service Health sync rate is above or equal to assured line rate", "description": "The actual line rate from NBN Service Health is above or equal to the assured line rate", "header": "The access is able to sync to above the assured line rate", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs015", "title": "NBN Service Health sync rate is below assured line rate", "description": "The actual line rate from NBN Service Health is below the assured line rate", "header": "Sync below assured line rate", "display": true, "tier": 1, "weight": 200, "messageBucketName": "Create_NBN_INC_PI"}, {"name": "shs016", "title": "NBN Service Health no observed service drops", "description": "NBN Service Drops are observed when there are at least 3 dropouts today or yesterday, 6 in the last 2 days, 21 in the last 7 days and 30 in a month", "header": "There are no observed service drops", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs017", "title": "NBN Service Health observed service drops", "description": "NBN Service Drops are observed when there are at least 3 dropouts today or yesterday, 6 in the last 2 days, 21 in the last 7 days and 30 in a month", "header": "There are observed service drops", "display": true, "tier": 1, "weight": 160, "messageBucketName": "Create_NBN_INC_PI"}, {"name": "shs018", "title": "NBN Service Health Reverse Power DPU OK", "description": "Reverse power state is Powered", "header": "The reverse power check indicates power to the DPU is OK", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs019", "title": "NBN Service Health Reverse Power DPU Down", "description": "Reverse power state is not Powered", "header": "Reverse power to DPU is down", "display": true, "tier": 1, "weight": 340, "messageBucketName": "Customer_or_Telstra_CT"}, {"name": "shs020", "title": "NBN Service Health last status change date found", "description": "There is a datetime value for the last status change for this service", "header": "Found record of when service last changed status (connected to NBN network)", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs021", "title": "NBN Service Health last status change date not found", "description": "There is no value for the last status change for this service", "header": "There is no record of when the service last changed status", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs022", "title": "NBN Service Health MAC address of the device is connected to the UNI-D port (FTTC / FTTP / HFC)", "description": "The CPE MAC address is present in the service health category metrics (FTTC / FTTP / HFC)", "header": "Found the MAC address of the device connected to the UNI-D port, indicating CPE is connected", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs023", "title": "NBN Service Health MAC address of the device is not connected to the UNI-D port (FTTC / FTTP / HFC)", "description": "The CPE MAC address is not present in the service health category metrics (FTTC / FTTP / HFC)", "header": "No MAC address present, no CPE Connected", "display": true, "tier": 1, "weight": 330, "messageBucketName": "Create_NBN_INC_HardDwn"}, {"name": "shs024", "title": "NBN Service Health NCD Port ID found", "description": "The port ID exists in the NCD metrics", "header": "Found the port ID from the NDD device, ensure that your CPE GW / Router / Switch is connected to the correct UNI-D port", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs025", "title": "NBN Service Health NCD MAC Address found", "description": "The MAC address exists in the NCD metrics", "header": "NDD MAC address, NDD connected to DPU", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs026", "title": "NBN Service Health NCD MAC Address not found", "description": "The MAC address does not exist in the NCD metrics", "header": "No MAC address. No Layer 2 NDD<->DPU", "display": true, "tier": 1, "weight": 335, "messageBucketName": "Create_NBN_INC_HardDwn"}, {"name": "shs027", "title": "NBN Service Health NCD make / model name found", "description": "The make / model name exists in the NCD metrics", "header": "Found the model's name for the NDD device, please refer to the template", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs028", "title": "NBN Service Health in home wiring no bridge taps", "description": "In home wiring status is Green, no bridge tap detected", "header": "No bridge tap (in home unused parallel cabling) that could reduce the performance of the access service", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs029", "title": "NBN Service Health in home wiring bridge taps", "description": "In home wiring status is not Green, possible bridge tap detected", "header": "Bridge tap detected, may impact speed & performance", "display": true, "tier": 1, "weight": 172, "messageBucketName": "Create_NBN_INC_PI"}, {"name": "shs030", "title": "NBN Service Health no line impairments", "description": "Line impairments status is Green, no issues detected", "header": "No indication of the line being impaired", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs031", "title": "NBN Service Health line impairments", "description": "Line impairments status is not Green, possible issues detected", "header": "Line impaired indicator not Green", "display": true, "tier": 1, "weight": 171, "messageBucketName": "Create_NBN_INC_HardDwn"}, {"name": "shs032", "title": "NBN Service Health NTD Port ID Found", "description": "The port ID exists in the NTD metrics", "header": "Ensure the CPE GW/R/SW is connected to the correct UNI-D port", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs033", "title": "NBN Service Health NTD MAC Address found", "description": "The MAC address exists in the NTD metrics", "header": "We can see the MAC address of the NTD device connected", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs034", "title": "NBN Service Health NTD MAC Address not found", "description": "The MAC address does not exist in the NTD metrics", "header": "No MAC address found for NTD", "display": true, "tier": 1, "weight": 310, "messageBucketName": "Create_NBN_INC_HardDwn"}, {"name": "shs035", "title": "NBN Service Health NTD make / model name found", "description": "The make / model name exists in the NTD metrics", "header": "Found the model's name for the NTD device, please refer to the template", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs036", "title": "NBN Service Health HFC signal within specification", "description": "HFC main signal has no issue detected, status is Green", "header": "HFC Signal is within specification", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs037", "title": "NBN Service Health HFC signal not within specification", "description": "HFC main signal has possible issue detected, status is not Green", "header": "HFC Signal impaired or down", "display": true, "tier": 1, "weight": 300, "messageBucketName": "Create_NBN_INC_PI"}, {"name": "shs038", "title": "NBN Service Health line status change date found", "description": "There is a datetime value for the line status change for this service", "header": "Found record of when service last changed status (connected to NBN network)", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs039", "title": "NBN Service Health line status change date not found", "description": "There is no value for the line status change for this service", "header": "There is no record of when the service last changed status", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs040", "title": "NBN Service Health NTD Version Found", "description": "The NTD Version exists in the NTD metrics", "header": "Found the NTD Version from the device", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs041", "title": "NBN Service Health cell busy hour performance unknown", "description": "The cell busyHourPerformance metric has value null", "header": "[NBN Network Cell Info] The cell performance during the busy period (7PM - 11PM) is unknown", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs042", "title": "NBN Service Health cell busy hour performance OK", "description": "The cell busyHourPerformance metric has value \"6 Mbps and above\"", "header": "[NBN Network Cell Info] During the typical busy period (7PM - 11PM), there are no observed cell performance issues detected (above 6 Mbps)", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs043", "title": "NBN Service Health cell busy hour performance not OK", "description": "The cell busyHourPerformance metric has value \"Below 6 Mbps\"", "header": "[NBN Network Cell Info] Busy period cell performance issue", "display": true, "tier": 1, "weight": 120, "messageBucketName": "Create_NBN_INC_PI"}, {"name": "shs044", "title": "NBN Service Health cell forecast upgrade date present", "description": "The forecastUpgradeDate metric for cell type is set", "header": "[NBN Network Cell Info] There is a date forecasted for an upgrade to improve cell performance", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs045", "title": "NBN Service Health cell forecast upgrade date not present", "description": "The forecastUpgradeDate metric for cell type is not set", "header": "[NBN Network Cell Info] There is no date forecasted for an upgrade to improve cell performance", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs046", "title": "NBN Service Health cell planned activity date present", "description": "The plannedActivity metric for cell type is set", "header": "[NBN Network Cell Info] There is planned activity date to upgrade and improve cell performance", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs047", "title": "NBN Service Health cell planned activity date not present", "description": "The plannedActivity metric for cell type is not set", "header": "[NBN Network Cell Info] There is no planned NBN activity to improve cell performance", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs048", "title": "NBN Service Health backhaul busy hour performance unknown", "description": "The backhaul busyHourPerformance metric has value null", "header": "[NBN Network Backhaul Info] The network congestion status for this service is unknown", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs049", "title": "NBN Service Health backhaul not experiencing congestion", "description": "The backhaul busyHourPerformance metric has value \"Link is not experiencing network congestion\"", "header": "[NBN Network Backhaul Info] This service is not impacted by network congestion based on 28 day average", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs050", "title": "NBN Service Health backhaul ugpraded in last 28 days", "description": "The backhaul busyHourPerformance metric has value \"Link has been upgraded within the last 28 days\"", "header": "[NBN Network Backhaul Info] This service has had its link upgraded within the last 28 days", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs051", "title": "NBN Service Health backhaul experiencing congestion", "description": "The backhaul busyHourPerformance metric has value \"Link is experiencing network congestion\"", "header": "[NBN Network Backhaul Info] 28 Day avg: Potentially impacted by network congestion", "display": true, "tier": 1, "weight": 125, "messageBucketName": "Create_NBN_INC_PI"}, {"name": "shs052", "title": "NBN Service Health backhaul forecast upgrade date present", "description": "The forecastUpgradeDate metric for backhaul type is set", "header": "[NBN Network Backhaul Info] There is a date forecasted for an upgrade to improve backhaul performance", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs053", "title": "NBN Service Health backhaul forecast upgrade date not present", "description": "The forecastUpgradeDate metric for backhaul type is not set", "header": "[NBN Network Backhaul Info] There is no date forecasted for an upgrade to improve backhaul performance", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs054", "title": "NBN Service Health backhaul planned activity date present", "description": "The plannedActivity metric for backhaul type is set", "header": "[NBN Network Backhaul Info] Planned activity date to upgrade/improve backhaul performance", "display": true, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs055", "title": "NBN Service Health backhaul planned activity date not present", "description": "The plannedActivity metric for backhaul type is not set", "header": "[NBN Network Backhaul Info] There is no planned NBN activity to improve backhaul performance", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs056", "title": "NBN Service Health service has no current outage", "description": "The currentOutage value from Service Health not \"true\" (can be \"false\" or null)", "header": "This service is currently not impacted by an NBN outage", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs057", "title": "NBN Service Health service has current outage", "description": "The currentOutage value from Service Health is \"true\"", "header": "The service may currently be impacted by an NBN outage", "display": true, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs058", "title": "NBN Service Health planned outage present", "description": "The Outage metrics contain a planned outage ID (CRQ)", "header": "A planned outage may impact your service check the SHS results for CRQ#", "display": true, "tier": 1, "weight": 590, "messageBucketName": "NBN_ActiveOutageINC"}, {"name": "shs060", "title": "NBN Service Health unplanned outage present", "description": "The Outage metrics contain an unplanned outage ID (INC)", "header": "Unplanned NBN outage. Check the SHS results for INC#", "display": true, "tier": 1, "weight": 590, "messageBucketName": "NBN_ActiveOutageINC"}, {"name": "shs062", "title": "NBN Service Health network activity event present", "description": "The Outage metrics contain a network event ID (INC)", "header": "NBN network activity. Check the SHS for INC#", "display": true, "tier": 1, "weight": 590, "messageBucketName": "NBN_ActiveOutageINC"}, {"name": "shs064", "title": "NBN Service Health fixed wireless signal no issue", "description": "The status value from Service Health (WirelessSignal) is \"No Issue Detected\" or null", "header": "There are no issues with the Fixed Wireless Signal", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs065", "title": "NBN Service Health fixed wireless signal has issue", "description": "The status value from Service Health (WirelessSignal) is \"Issue Detected\"", "header": "Fixed Wireless Signal impacted", "display": true, "tier": 1, "weight": 210, "messageBucketName": "Create_NBN_INC_PI"}, {"name": "shs066", "title": "NBN Service Health downstream heavy user not flagged", "description": "The fair use policy metrics have not flagged this service as a heavy user on the downstream", "header": "[Fair Use Policy] This service is not flagged as heavy user on the downstream", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs067", "title": "NBN Service Health downstream heavy user flagged", "description": "The fair use policy metrics have flagged this service as a heavy user on the downstream", "header": "[Fair Use Policy] Downstream heavy user flagged, could be impacted by NBN throttling", "display": true, "tier": 1, "weight": 166, "messageBucketName": "Customer"}, {"name": "shs068", "title": "NBN Service Health upstream heavy user not flagged", "description": "The fair use policy metrics have not flagged this service as a heavy user on the upstream", "header": "[Fair Use Policy] This service is not flagged as heavy user on the upstream", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs069", "title": "NBN Service Health upstream heavy user flagged", "description": "The fair use policy metrics have flagged this service as a heavy user on the upstream", "header": "[Fair Use Policy] Upstream heavy user flagged, could be impacted by NBN throttling", "display": true, "tier": 1, "weight": 164, "messageBucketName": "Customer"}, {"name": "shs070", "title": "NBN Service Health connectivity unknown", "description": "The connectivity status of the NBN service according to the Service Health API is unknown", "header": "[SHS] Connectivity between CPE > NBN is unknown", "display": true, "tier": 1, "weight": 450, "messageBucketName": "Furth_Inv_Layer1"}, {"name": "shs071", "title": "NBN Service Health performance unknown", "description": "NBN service performance according to the Service Health API is unknown", "header": "[SHS] Performance of this service is unknown", "display": true, "tier": 1, "weight": 164, "messageBucketName": "Create_NBN_INC_PI"}, {"name": "shs072", "title": "NBN Service Health stability unknown", "description": "The stability of the NBN service according to the Service Health API is unknown", "header": "[SHS] Stability of this service is unknown", "display": true, "tier": 1, "weight": 162, "messageBucketName": "Create_NBN_INC_PI"}, {"name": "shs073", "title": "NBN Service Health MAC address of the device is connected (FTTB / FTTN)", "description": "The CPE MAC address is present in the service health category metrics (FTTB / FTTN)", "header": "Determined the CPE MAC address for this service", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs074", "title": "NBN Service Health MAC address of the device is not connected (FTTB / FTTN)", "description": "The CPE MAC address is not present in the service health category metrics (FTTB / FTTN)", "header": "CPE MAC address not found", "display": true, "tier": 1, "weight": 330, "messageBucketName": "Create_NBN_INC_HardDwn"}, {"name": "shs075", "title": "NBN Service Health operational status last change reason found", "description": "The last change reason of the NBN service is set", "header": "Check service health summary for reason of last change", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs076", "title": "NBN Service Health NTD port state up", "description": "The port state is up in the NTD metrics", "header": "The UNI-D (WAN) is up", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs077", "title": "NBN Service Health NTD port state down", "description": "The port state is down in the NTD metrics", "header": "The UNI-D (WAN) is down", "display": true, "tier": 1, "weight": 360, "messageBucketName": "Create_NBN_INC_HardDwn"}, {"name": "shs078", "title": "NBN Service Health NTD port state unknown", "description": "The port state is unknown in the NTD metrics", "header": "The state of the UNI-D port is unknown", "display": true, "tier": 1, "weight": 380, "messageBucketName": "Create_NBN_INC_HardDwn"}, {"name": "shs079", "title": "NBN Service Health NTD current speed and duplex set", "description": "The current speed and duplex is set in the NTD metrics", "header": "Found current speed and duplex of device, ensure the duplex settings of your device are configured correctly", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs080", "title": "NBN Service Health optical signal status OK", "description": "The status for the optical signal metrics is \"No Issue Detected\"", "header": "No issues detected with the strength of the optical signal, optical is up", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "shs081", "title": "NBN Service Health optical signal status not OK", "description": "The status for the optical signal metrics is \"Issue Detected\" or \"Potential Issue Detected\"", "header": "Optical is down or below operational threshold", "display": true, "tier": 1, "weight": 375, "messageBucketName": "Create_NBN_INC_HardDwn"}, {"name": "shs082", "title": "NBN Service Health optical signal status unknown", "description": "The status for the optical signal metrics none of the expected values (\"No Issue Detected\", \"Issue Detected\", \"Potential Issue Detected\")", "header": "The state of the optical signal is unknown", "display": true, "tier": 1, "weight": 370, "messageBucketName": "Furth_Inv_Layer1"}, {"name": "tkt000", "title": "Ticketing sources run", "description": "The \"RASSP\", \"SIIAMfnn\" / \"SIIAMCarriageFnn\" or \"Hangar - Service central incident API\" / \"Hangar - Service central incident API Carriage FNN\" sources were collected and ticketing information has been obtained", "header": "--Database and ticketing system checks show--", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "tkt001", "title": "RASS record found", "description": "There exists a RASS record for either the main FNN or carriage FNN for this service", "header": "There is a record in RASS for this service", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "tkt002", "title": "RASS record not found", "description": "The RASS record for this service could not be found", "header": "There are no records in RASS for this service", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "tkt003", "title": "No RASS orders", "description": "No existing orders are listed in RASS", "header": "There are no references to existing orders in our system", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "tkt004", "title": "RASS orders found", "description": "1 or more existing orders are listed in RASS", "header": "There are currently references to existing orders in our system", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "tkt005", "title": "No active SIIAM tickets", "description": "No active tickets for this service in SIIAM", "header": "There are no active SIIAM tickets", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "tkt006", "title": "Active SIIAM tickets", "description": "1 or more active tickets for this service in SIIAM", "header": "Active SIIAM ticket", "display": true, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "tkt007", "title": "No active Service Central tickets", "description": "No active tickets for this service in Service Central", "header": "There are no active Service Central incidents", "display": false, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "tkt008", "title": "Active Service Central tickets", "description": "1 or more active tickets for this service in Service Central", "header": "Active Service Central ticket", "display": true, "tier": 7, "weight": 0, "messageBucketName": null}, {"name": "tkt009", "title": "RASS CAN order found", "description": "1 or more CAN orders are listed in RASS", "header": "RASS CAN order found", "display": true, "tier": 0, "weight": 100, "messageBucketName": "Furth_Inv_Records"}, {"name": "tkt010", "title": "RASS MOD order found", "description": "1 or more active MOD orders are listed in RASS", "header": "RASS MOD order found", "display": true, "tier": 4, "weight": 80, "messageBucketName": "Furth_Inv_Records"}, {"name": "tkt011", "title": "RASS NEW order found", "description": "1 or more active NEW orders are listed in RASS", "header": "RASS NEW order found", "display": true, "tier": 4, "weight": 80, "messageBucketName": "Furth_Inv_Records"}, {"name": "tkt012", "title": "Active Promise Task", "description": "1 or more active Promise Task", "header": "Active Promise Task", "display": true, "tier": 7, "weight": 0, "messageBucketName": null}]}