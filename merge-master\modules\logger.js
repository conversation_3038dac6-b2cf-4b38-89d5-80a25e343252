
import pino from 'pino';
import onFinished from 'on-finished';
import onHeaders from 'on-headers';
import os from 'os';

import '../config/config.js';

let pinoOptions;
let logDestination = global.gConfig ? global.gConfig.logDestination : undefined;
let hostname = process.env.MERGE_HOSTNAME ? process.env.MERGE_HOSTNAME : os.hostname();


if (!process.env.LOG_LEVEL) {
    pinoOptions = {
        level: 'info',
        base: { pid: process.pid, hostname: hostname },
        transport: {
            target: 'pino-pretty',
            options: {
                colorize: false,
                translateTime: 'SYS:yyyy-mm-dd HH:MM:ss.l',
                destination: logDestination
            }
        }
    };
} else {
    pinoOptions = {
        level: process.env.LOG_LEVEL,
        base: null,
        timestamp: false,
        transport: {
            target: 'pino-pretty',
            options: {
                colorize: true,
                ignore: 'time',
                destination: logDestination
            }
        }
    };
}

const logger = pino(
    pinoOptions
);

export default logger;

export function httpLogger(req, res, next) {
    req._startAt = process.hrtime();

    onHeaders(res, function() {
        res._startAt = process.hrtime();
    });

    onFinished(res, function() {
        let contentLength = res.getHeader('content-length');
        if (!contentLength) {
            contentLength = '-';
        }

        let responseTime = undefined;
        if (req._startAt && res._startAt) {
            responseTime = ((res._startAt[0] - req._startAt[0]) * 1e3 + (res._startAt[1] - req._startAt[1]) * 1e-6).toFixed(3);
        }

        logger.info(`${req.method} ${req.originalUrl} ${res.statusCode} ${responseTime} ms - ${contentLength}`);
    });
    next();
}
