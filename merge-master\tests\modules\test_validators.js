
import assert from 'assert';
import validators from '../../modules/validators.js';

describe('isTimePeriod()', () => {
    it('null', () => {
        assert.strictEqual(validators.isTimePeriod(null), false);
    });

    it('undefined', () => {
        assert.strictEqual(validators.isTimePeriod(undefined), false);
    });

    it('empty string', () => {
        assert.strictEqual(validators.isTimePeriod(""), false);
    });

    it('integer value', () => {
        assert.strictEqual(validators.isTimePeriod(1), false);
    });

    it('boolean value true', () => {
        assert.strictEqual(validators.isTimePeriod(true), false);
    });

    it('boolean value false', () => {
        assert.strictEqual(validators.isTimePeriod(false), false);
    });

    it('time period 1 day', () => {
        assert.strictEqual(validators.isTimePeriod("1d"), true);
    });

    it('time period 100 days', () => {
        assert.strictEqual(validators.isTimePeriod("100d"), true);
    });

    it('time period with leading 0', () => {
        assert.strictEqual(validators.isTimePeriod("012345678d"), true);
    });

    it('time period negative 92 days', () => {
        assert.strictEqual(validators.isTimePeriod("-92d"), false);
    });

    it('time period negative 9 years', () => {
        assert.strictEqual(validators.isTimePeriod("-9y"), false);
    });

    it('time period negative 892 months', () => {
        assert.strictEqual(validators.isTimePeriod("-892m"), false);
    });

    it('time period with only integer string 5', () => {
        assert.strictEqual(validators.isTimePeriod("5"), false);
    });

    it('time period with only integer string 82', () => {
        assert.strictEqual(validators.isTimePeriod("82"), false);
    });

    it('time period with only integer string 3825', () => {
        assert.strictEqual(validators.isTimePeriod("3825"), false);
    });

    it('time period decimals 1', () => {
        assert.strictEqual(validators.isTimePeriod("2.5m"), false);
    });

    it('time period decimals 2', () => {
        assert.strictEqual(validators.isTimePeriod("15.8239y"), false);
    });

    it('time period 5 weeks', () => {
        assert.strictEqual(validators.isTimePeriod("5w"), true);
    });

    it('time period 88 weeks', () => {
        assert.strictEqual(validators.isTimePeriod("88w"), true);
    });

    it('time period 3 months', () => {
        assert.strictEqual(validators.isTimePeriod("3m"), true);
    });

    it('time period 73 months', () => {
        assert.strictEqual(validators.isTimePeriod("73m"), true);
    });

    it('time period 2 years', () => {
        assert.strictEqual(validators.isTimePeriod("2y"), true);
    });

    it('time period 239 years', () => {
        assert.strictEqual(validators.isTimePeriod("239y"), true);
    });

    it('time period content after unit 1', () => {
        assert.strictEqual(validators.isTimePeriod("2d8"), false);
    });

    it('time period content after unit 2', () => {
        assert.strictEqual(validators.isTimePeriod("6ww"), false);
    });

    it('time period content after unit 3', () => {
        assert.strictEqual(validators.isTimePeriod("7y-1256f"), false);
    });

    it('time period content after unit 4', () => {
        assert.strictEqual(validators.isTimePeriod("5d3w"), false);
    });
});


describe('isTimeInterval()', () => {
    it('null', () => {
        assert.strictEqual(validators.isTimeInterval(null), false);
    });

    it('undefined', () => {
        assert.strictEqual(validators.isTimeInterval(undefined), false);
    });

    it('empty string', () => {
        assert.strictEqual(validators.isTimeInterval(""), false);
    });

    it('integer value', () => {
        assert.strictEqual(validators.isTimeInterval(1), false);
    });

    it('boolean value true', () => {
        assert.strictEqual(validators.isTimeInterval(true), false);
    });

    it('boolean value false', () => {
        assert.strictEqual(validators.isTimeInterval(false), false);
    });

    it('time interval hour', () => {
        assert.strictEqual(validators.isTimeInterval("h"), true);
    });

    it('time interval day', () => {
        assert.strictEqual(validators.isTimeInterval("d"), true);
    });

    it('time interval week', () => {
        assert.strictEqual(validators.isTimeInterval("w"), true);
    });

    it('time interval month', () => {
        assert.strictEqual(validators.isTimeInterval("m"), true);
    });

    it('time interval year', () => {
        assert.strictEqual(validators.isTimeInterval("y"), true);
    });

    it('time interval unrecognised 1', () => {
        assert.strictEqual(validators.isTimeInterval("b"), false);
    });

    it('time interval unrecognised 2', () => {
        assert.strictEqual(validators.isTimeInterval("x"), false);
    });

    it('time interval unrecognised 3', () => {
        assert.strictEqual(validators.isTimeInterval("z"), false);
    });

    it('time interval multiple length string 1', () => {
        assert.strictEqual(validators.isTimeInterval("day"), false);
    });

    it('time interval multiple length string 2', () => {
        assert.strictEqual(validators.isTimeInterval("m3jfioawejf"), false);
    });

    it('time interval multiple length string 3', () => {
        assert.strictEqual(validators.isTimeInterval("teststring"), false);
    });
});


describe('isIPv4Address()', () => {
    it('null', () => {
        assert.strictEqual(validators.isIPv4Address(null), false);
    });

    it('undefined', () => {
        assert.strictEqual(validators.isIPv4Address(undefined), false);
    });

    it('empty string', () => {
        assert.strictEqual(validators.isIPv4Address(""), false);
    });

    it('integer value', () => {
        assert.strictEqual(validators.isIPv4Address(1), false);
    });

    it('boolean value true', () => {
        assert.strictEqual(validators.isIPv4Address(true), false);
    });

    it('boolean value false', () => {
        assert.strictEqual(validators.isIPv4Address(false), false);
    });

    it('Valid IPv4 address test 1', () => {
        assert.strictEqual(validators.isIPv4Address("*******"), true);
    });

    it('Valid IPv4 address test 2', () => {
        assert.strictEqual(validators.isIPv4Address("***************"), true);
    });

    it('Valid IPv4 address test 3', () => {
        assert.strictEqual(validators.isIPv4Address("***********"), true);
    });

    it('Valid IPv4 address test 4', () => {
        assert.strictEqual(validators.isIPv4Address("**************"), true);
    });

    it('Invalid IPv4 address test 1', () => {
        assert.strictEqual(validators.isIPv4Address("256.80.80.80"), false);
    });

    it('Invalid IPv4 address test 2', () => {
        assert.strictEqual(validators.isIPv4Address("************"), false);
    });

    it('Invalid IPv4 address test 3', () => {
        assert.strictEqual(validators.isIPv4Address("255.0.254."), false);
    });

    it('Invalid IPv4 address test 4', () => {
        assert.strictEqual(validators.isIPv4Address("19216801"), false);
    });

    it('Invalid IPv4 address test 5', () => {
        assert.strictEqual(validators.isIPv4Address("1.2.3"), false);
    });

    it('Invalid IPv4 address test 6', () => {
        assert.strictEqual(validators.isIPv4Address("1.2"), false);
    });

    it('Invalid IPv4 address test 7', () => {
        assert.strictEqual(validators.isIPv4Address("1"), false);
    });

    it('Invalid IPv4 address test 8', () => {
        assert.strictEqual(validators.isIPv4Address("***********."), false);
    });

    it('Invalid IPv4 address test 9', () => {
        assert.strictEqual(validators.isIPv4Address(".***********"), false);
    });

    it('Invalid IPv4 address test 10', () => {
        assert.strictEqual(validators.isIPv4Address("string"), false);
    });
});


describe('isValidSourceData()', () => {
    it('null', () => {
        assert.strictEqual(validators.isValidSourceData(null), true);
    });

    it('undefined', () => {
        assert.strictEqual(validators.isValidSourceData(undefined), false);
    });

    it('empty string', () => {
        assert.strictEqual(validators.isValidSourceData(""), false);
    });

    it('integer value', () => {
        assert.strictEqual(validators.isValidSourceData(1), false);
    });

    it('boolean value true', () => {
        assert.strictEqual(validators.isValidSourceData(true), false);
    });

    it('boolean value false', () => {
        assert.strictEqual(validators.isValidSourceData(false), false);
    });

    it('empty object', () => {
        assert.strictEqual(validators.isValidSourceData({}), true);
    });

    it('array of objects', () => {
        assert.strictEqual(validators.isValidSourceData([{ a: null }, { b: null }]), false);
    });

    it('object with keys and null values', () => {
        assert.strictEqual(validators.isValidSourceData({ a: null, b: null }), false);
    });

    it('object with keys and string values', () => {
        assert.strictEqual(validators.isValidSourceData({ a: "stringA", b: "stringB" }), false);
    });

    it('object with keys and number values', () => {
        assert.strictEqual(validators.isValidSourceData({ a: 123, b: 456 }), false);
    });

    it('object with keys and object values', () => {
        assert.strictEqual(validators.isValidSourceData({ a: {}, b: { data: [1, 2, 3, 4] } }), true);
    });

    it('object with keys and one object value one null value', () => {
        assert.strictEqual(validators.isValidSourceData({ a: {}, b: null }), false);
    });

    it('object with keys and one object value one string value', () => {
        assert.strictEqual(validators.isValidSourceData({ a: {}, b: "test" }), false);
    });

    it('object with keys and one object value one number value', () => {
        assert.strictEqual(validators.isValidSourceData({ a: {}, b: 1 }), false);
    });

    it('string representing empty object', () => {
        assert.strictEqual(validators.isValidSourceData("{}"), true);
    });

    it('string representing array of objects', () => {
        assert.strictEqual(validators.isValidSourceData("[{ \"a\": null }, { \"b\": null }]"), false);
    });

    it('string representing object with keys and null values', () => {
        assert.strictEqual(validators.isValidSourceData("{ \"a\": null, \"b\": null }"), false);
    });

    it('string representing object with keys and string values', () => {
        assert.strictEqual(validators.isValidSourceData("{ \"a\": \"stringA\", \"b\": \"stringB\" }"), false);
    });

    it('string representing object with keys and number values', () => {
        assert.strictEqual(validators.isValidSourceData("{ \"a\": 123, \"b\": 456 }"), false);
    });

    it('string representing object with keys and object values', () => {
        assert.strictEqual(validators.isValidSourceData("{ \"a\": {}, \"b\": { \"data\": [1, 2, 3, 4] } }"), true);
    });

    it('string representing object with keys and one object value one null value', () => {
        assert.strictEqual(validators.isValidSourceData("{ \"a\": {}, \"b\": null }"), false);
    });

    it('string representing object with keys and one object value one string value', () => {
        assert.strictEqual(validators.isValidSourceData("{ \"a\": {}, \"b\": \"test\" }"), false);
    });

    it('string representing object with keys and one object value one number value', () => {
        assert.strictEqual(validators.isValidSourceData("{ \"a\": {}, \"b\": 1 }"), false);
    });
});


describe('isValidAdditionalParameters()', () => {
    it('null', () => {
        assert.strictEqual(validators.isValidAdditionalParameters(null), true);
    });

    it('undefined', () => {
        assert.strictEqual(validators.isValidAdditionalParameters(undefined), false);
    });

    it('empty string', () => {
        assert.strictEqual(validators.isValidAdditionalParameters(""), false);
    });

    it('integer value', () => {
        assert.strictEqual(validators.isValidAdditionalParameters(1), false);
    });

    it('boolean value true', () => {
        assert.strictEqual(validators.isValidAdditionalParameters(true), false);
    });

    it('boolean value false', () => {
        assert.strictEqual(validators.isValidAdditionalParameters(false), false);
    });

    it('empty object', () => {
        assert.strictEqual(validators.isValidAdditionalParameters({}), true);
    });

    it('array', () => {
        assert.strictEqual(validators.isValidAdditionalParameters([{ dateFrom: "2000-01-01T00:00:00Z" }]), false);
    });

    it('object with dateFrom in the future', () => {
        let futureDate = new Date();
        futureDate.setDate(futureDate.getDate() + 1);

        assert.strictEqual(validators.isValidAdditionalParameters({ dateFrom: futureDate.toISOString() }), false);
    });

    it('object with dateFrom ahead of dateTo', () => {
        assert.strictEqual(validators.isValidAdditionalParameters({ dateFrom: "2001-01-01T00:00:00Z", dateTo: "2000-01-01T01:00:00Z" }), false);
    });

    it('object with keys and null values', () => {
        assert.strictEqual(validators.isValidAdditionalParameters({ dateFrom: null, dateTo: null }), true);
    });

    it('object with keys and string values', () => {
        assert.strictEqual(validators.isValidAdditionalParameters({ dateFrom: "stringA", dateTo: "stringB" }), false);
    });

    it('object with keys and string values (valid)', () => {
        assert.strictEqual(validators.isValidAdditionalParameters({ dateFrom: "2000-01-01T00:00:00Z", dateTo: "2000-01-01T01:00:00Z" }), true);
    });

    it('object with keys and number values', () => {
        assert.strictEqual(validators.isValidAdditionalParameters({ dateFrom: 123, dateTo: 456 }), true);
    });

    it('object with keys and object values', () => {
        assert.strictEqual(validators.isValidAdditionalParameters({ dateFrom: {}, dateTo: { data: [1, 2, 3, 4] } }), false);
    });

    it('object with other keys', () => {
        assert.strictEqual(validators.isValidAdditionalParameters({ a: 1, b: "test" }), true);
    });

    it('string representing empty object', () => {
        assert.strictEqual(validators.isValidAdditionalParameters("{}"), true);
    });

    it('string representing array', () => {
        assert.strictEqual(validators.isValidAdditionalParameters("[{ dateFrom: \"2000-01-01T00:00:00Z\" }]"), false);
    });

    it('string representing object with keys and null values', () => {
        assert.strictEqual(validators.isValidAdditionalParameters("{ \"dateFrom\": null, \"dateTo\": null }"), true);
    });

    it('string representing object with keys and string values', () => {
        assert.strictEqual(validators.isValidAdditionalParameters("{ \"dateFrom\": \"stringA\", \"dateTo\": \"stringB\" }"), false);
    });

    it('string representing object with keys and string values (valid)', () => {
        assert.strictEqual(validators.isValidAdditionalParameters("{ \"dateFrom\": \"2000-01-01T00:00:00Z\", \"dateTo\": \"2000-01-01T01:00:00Z\" }"), true);
    });

    it('string representing object with keys and number values', () => {
        assert.strictEqual(validators.isValidAdditionalParameters("{ \"dateFrom\": 123, \"dateTo\": 456 }"), true);
    });

    it('string representing object with keys and object values', () => {
        assert.strictEqual(validators.isValidAdditionalParameters("{ \"dateFrom\": {}, \"dateTo\": { \"data\": [1, 2, 3, 4] } }"), false);
    });

    it('string representing object with other keys', () => {
        assert.strictEqual(validators.isValidAdditionalParameters("{ \"a\": 1, \"b\": \"test\" }"), true);
    });
});


describe('createFilterQueryValidator()', () => {
    it('null', () => {
        assert.strictEqual(validators.createFilterQueryValidator([])(null), false);
    });

    it('undefined', () => {
        assert.strictEqual(validators.createFilterQueryValidator([])(undefined), false);
    });

    it('empty string', () => {
        assert.strictEqual(validators.createFilterQueryValidator([])(""), true);
    });

    it('non-empty string', () => {
        assert.strictEqual(validators.createFilterQueryValidator([])("filtervalue"), true);
    });

    it('integer value', () => {
        assert.strictEqual(validators.createFilterQueryValidator([])(1), false);
    });

    it('boolean value true', () => {
        assert.strictEqual(validators.createFilterQueryValidator([])(true), false);
    });

    it('boolean value false', () => {
        assert.strictEqual(validators.createFilterQueryValidator([])(false), false);
    });

    it('object with empty valid keys', () => {
        assert.strictEqual(validators.createFilterQueryValidator([])({
            equal: "teststring"
        }), false);
    });

    it('object with empty valid keys and empty object', () => {
        assert.strictEqual(validators.createFilterQueryValidator([])({}), true);
    });

    it('object with non-empty valid keys and empty object', () => {
        assert.strictEqual(validators.createFilterQueryValidator([
            "equal"
        ])({}), true);
    });

    it('object with matching valid key', () => {
        assert.strictEqual(validators.createFilterQueryValidator([
            "equal"
        ])({
            equal: "teststring"
        }), true);
    });

    it('object with matching valid keys', () => {
        assert.strictEqual(validators.createFilterQueryValidator([
            "equal",
            "like",
            "gt",
            "lt"
        ])({
            equal: "teststring",
            like: "115"
        }), true);
    });

    it('object with non-matching valid key', () => {
        assert.strictEqual(validators.createFilterQueryValidator([
            "equal"
        ])({
            like: "likeword"
        }), false);
    });

    it('object with non-string value 1', () => {
        assert.strictEqual(validators.createFilterQueryValidator([
            "equal"
        ])({
            equal: 100
        }), false);
    });

    it('object with non-string value 2', () => {
        assert.strictEqual(validators.createFilterQueryValidator([
            "equal"
        ])({
            equal: true
        }), false);
    });

    it('object with non-string value 3', () => {
        assert.strictEqual(validators.createFilterQueryValidator([
            "equal"
        ])({
            equal: {
                another: "value"
            }
        }), false);
    });

    it('object with integer value and option', () => {
        assert.strictEqual(validators.createFilterQueryValidator([
            "equal"
        ], 'integer')({
            equal: '100'
        }), true);
    });

    it('object with string value and integer option', () => {
        assert.throws(() => {
            validators.createFilterQueryValidator([
                "equal"
            ], 'integer')({
                equal: 'stringhere'
            })
        }, {
            name: 'Error',
            message: 'Value stringhere could not be parsed as integer.'
        });
    });

    it('object with number value and option', () => {
        assert.strictEqual(validators.createFilterQueryValidator([
            "equal"
        ], 'number')({
            equal: '100.1'
        }), true);
    });

    it('object with string value and number option', () => {
        assert.throws(() => {
            validators.createFilterQueryValidator([
                "equal"
            ], 'number')({
                equal: 'stringhere'
            })
        }, {
            name: 'Error',
            message: 'Value stringhere could not be parsed as number.'
        });
    });
});


describe('createFilterQuerySanitizer()', () => {
    it('null', () => {
        assert.strictEqual(validators.createFilterQuerySanitizer()(null), null);
    });

    it('undefined', () => {
        assert.strictEqual(validators.createFilterQuerySanitizer()(undefined), undefined);
    });

    it('empty string', () => {
        assert.deepEqual(validators.createFilterQuerySanitizer()(""), {
            equal: ""
        });
    });

    it('non-empty string', () => {
        assert.deepEqual(validators.createFilterQuerySanitizer()("filtervalue"), {
            equal: "filtervalue"
        });
    });

    it('non-empty string with custom default key', () => {
        assert.deepEqual(validators.createFilterQuerySanitizer({ defaultOperator: 'like' })("filtervalue"), {
            like: "filtervalue"
        });
    });

    it('integer value', () => {
        assert.strictEqual(validators.createFilterQuerySanitizer()(1), 1);
    });

    it('boolean value true', () => {
        assert.strictEqual(validators.createFilterQuerySanitizer()(true), true);
    });

    it('boolean value false', () => {
        assert.strictEqual(validators.createFilterQuerySanitizer()(false), false);
    });

    it('object with no keys', () => {
        assert.deepEqual(validators.createFilterQuerySanitizer()({}), {});
    });

    it('object with a key', () => {
        assert.deepEqual(validators.createFilterQuerySanitizer()({
            equal: "teststring"
        }), {
            equal: "teststring"
        });
    });

    it('object with multiple keys', () => {
        assert.deepEqual(validators.createFilterQuerySanitizer()({
            equal: "teststring",
            like: "liketext",
            gt: "50",
            lt: "20"
        }), {
            equal: "teststring",
            like: "liketext",
            gt: "50",
            lt: "20"
        });
    });

    it('object with a key and valueType integer', () => {
        assert.deepEqual(validators.createFilterQuerySanitizer({ valueType: 'integer' })({
            equal: "100"
        }), {
            equal: 100
        });
    });

    it('object with multiple keys and valueType integer', () => {
        assert.deepEqual(validators.createFilterQuerySanitizer({ valueType: 'integer' })({
            gt: "50",
            lt: "20"
        }), {
            gt: 50,
            lt: 20
        });
    });

    it('object with a key and valueType number', () => {
        assert.deepEqual(validators.createFilterQuerySanitizer({ valueType: 'number' })({
            equal: "22.11"
        }), {
            equal: 22.11
        });
    });

    it('object with multiple keys and valueType number', () => {
        assert.deepEqual(validators.createFilterQuerySanitizer({ valueType: 'number' })({
            gt: "15.16",
            lt: "82.51"
        }), {
            gt: 15.16,
            lt: 82.51
        });
    });
});

