import { ActionButtonValidation } from '@able/react'
import { Panel } from '../panel/Panel'
import { RedCrossIcon } from '../statusIcon/RedCrossIcon'
import styles from './ErrorPanel.module.scss'

interface ErrorPanelProps {
    errorMessage: string
    onRefresh?: () => void
}

export const ErrorPanel = ({ errorMessage, onRefresh }: ErrorPanelProps) => {
    return (
        <Panel background="materialBaseTertiary">
            <div className={styles.errorPanel}>
                <div className={styles.error}>
                    <RedCrossIcon />
                    <span>{errorMessage}</span>
                </div>

                {onRefresh && (
                    <ActionButtonValidation
                        actionButtonEvents={{
                            onClick: onRefresh,
                        }}
                        state={'Normal'}
                        label={'Refresh'}
                        validationMessage={''}
                    ></ActionButtonValidation>
                )}
            </div>
        </Panel>
    )
}
