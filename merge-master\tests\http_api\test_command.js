'use strict';

import mongoose from 'mongoose';
import chai from 'chai';
import chaiHttp from 'chai-http';
import chaiLike from 'chai-like';
import chaiDateTime from 'chai-datetime';
import deepEqualInAnyOrder from 'deep-equal-in-any-order';
import sinon from 'sinon';
import superagentDefaults from 'superagent-defaults';
import supertest from 'supertest';
import cookie from 'cookie';
import _ from 'lodash';
import dedent from 'dedent';
import assert from 'assert';

var app;
var request;
import config from '../config.js';
import CommandRun from '../../db/model/commandRun.js';
import helpers from '../helpers.js';
import { UNIT_TEST_ALL_ACCESS_USERNAME, UNIT_TEST_USERS } from '../users.js';

const should = chai.should();

chaiLike.extend(helpers.dateFieldCheck);
chaiLike.extend(helpers.regexCheck);
chai.use(chaiLike);
chai.use(chaiHttp);
chai.use(chaiDateTime);
chai.use(deepEqualInAnyOrder);


const QUICK_COMMAND_RECORD_KEYS = Object.freeze([
    "id",
    "command",
    "status",
    "createdBy",
    "createdOn"
]);


// Note: tests here are incomplete and do not provide full coverage, just checks basic cases and permissions for now
describe('Merge Quick Command Endpoints', () => {
    // Setup and connect to mongodb memory server, start merge app instance
    // Set request variable with a configured supertest agent with test JWT and app instance
    before(async function() {
        global.gConfig = _.cloneDeep(config.default);
        app = (await import('../../app.js')).default;
        request = superagentDefaults(supertest(app));
    });

    // Drop all contents of database before each test
    beforeEach(async function() {
        global.gConfig = _.cloneDeep(config.default);

        // Uses deleteMany instead of mongoose.connection.db.dropDatabase() because
        // custom indexes appear to be deleted if dropDatabase() is used
        for (let collectionName in mongoose.connection.collections) {
            await mongoose.connection.collections[collectionName].deleteMany({});
        }

        await helpers.authenticateSession(request, UNIT_TEST_ALL_ACCESS_USERNAME);
    });

    afterEach(async function() {
        sinon.restore();
    });

    describe('List View', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/command/list');

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/command/list');

                switch(username) {
                    case 'commandAccess':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('Record History', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/command/history');

            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/command/history');
                switch(username) {
                    case 'commandAccess':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('No records', async() => {
            let res = await request.get('/command/history').buffer();

            res.should.have.status(200);

            chai.expect(res.body.metadata).to.like({
                pagination: {
                    limit: 100,
                    offset: 0,
                    total: 0
                }
            });

            chai.expect(res.body.results).to.eql([]);
        });

        it('One record', async() => {
            let currDate = new Date();
            let commandId = "60063c0ecde92315a6db59a2";
            let promises = [];

            promises.push(new CommandRun({
                id: commandId,
                command: "testcommand",
                duration: "5.15s",
                status: "Completed",
                error: null,
                createdBy: "username",
                createdOn: currDate,
                result: "commandresult",
                statusCode: null
            }).save());

            await Promise.all(promises);

            let res = await request.get('/command/history');

            res.should.have.status(200);

            chai.expect(res.body.metadata).to.like({
                pagination: {
                    limit: 100,
                    offset: 0,
                    total: 1
                }
            });

            chai.expect(res.body.results).to.be.like([{
                id: commandId,
                command: "testcommand",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            }]);

            res.body.results.forEach((quickCommandRecord) => {
                chai.expect(quickCommandRecord).to.have.all.keys(QUICK_COMMAND_RECORD_KEYS);
            });
        });

        it('One record, command filter matching', async() => {
            let currDate = new Date();
            let commandId = "60063c0ecde92315a6db59a2";
            let promises = [];

            promises.push(new CommandRun({
                id: commandId,
                command: "testcommand",
                duration: "5.15s",
                status: "Completed",
                error: null,
                createdBy: "username",
                createdOn: currDate,
                result: "commandresult",
                statusCode: null
            }).save());

            await Promise.all(promises);

            let res = await request.get('/command/history?command[like]=test');

            res.should.have.status(200);

            chai.expect(res.body.metadata).to.like({
                pagination: {
                    limit: 100,
                    offset: 0,
                    total: 1
                }
            });

            chai.expect(res.body.results).to.be.like([{
                id: commandId,
                command: "testcommand",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            }]);

            res.body.results.forEach((quickCommandRecord) => {
                chai.expect(quickCommandRecord).to.have.all.keys(QUICK_COMMAND_RECORD_KEYS);
            });
        });

        it('One record, command filter not matching', async() => {
            let currDate = new Date();
            let commandId = "60063c0ecde92315a6db59a2";
            let promises = [];

            promises.push(new CommandRun({
                id: commandId,
                command: "testcommand",
                duration: "5.15s",
                status: "Completed",
                error: null,
                createdBy: "username",
                createdOn: currDate,
                result: "commandresult",
                statusCode: null
            }).save());

            await Promise.all(promises);

            let res = await request.get('/command/history?command[like]=somethingelse');

            res.should.have.status(200);

            chai.expect(res.body.metadata).to.like({
                pagination: {
                    limit: 100,
                    offset: 0,
                    total: 0
                }
            });

            chai.expect(res.body.results).to.be.like([]);
        });

        it('One record, status filter matching', async() => {
            let currDate = new Date();
            let commandId = "60063c0ecde92315a6db59a2";
            let promises = [];

            promises.push(new CommandRun({
                id: commandId,
                command: "testcommand",
                duration: "5.15s",
                status: "Completed",
                error: null,
                createdBy: "username",
                createdOn: currDate,
                result: "commandresult",
                statusCode: null
            }).save());

            await Promise.all(promises);

            let res = await request.get('/command/history?status=Completed');

            res.should.have.status(200);

            chai.expect(res.body.metadata).to.like({
                pagination: {
                    limit: 100,
                    offset: 0,
                    total: 1
                }
            });

            chai.expect(res.body.results).to.be.like([{
                id: commandId,
                command: "testcommand",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            }]);

            res.body.results.forEach((quickCommandRecord) => {
                chai.expect(quickCommandRecord).to.have.all.keys(QUICK_COMMAND_RECORD_KEYS);
            });
        });

        it('One record, status filter not matching', async() => {
            let currDate = new Date();
            let commandId = "60063c0ecde92315a6db59a2";
            let promises = [];

            promises.push(new CommandRun({
                id: commandId,
                command: "testcommand",
                duration: "5.15s",
                status: "Completed",
                error: null,
                createdBy: "username",
                createdOn: currDate,
                result: "commandresult",
                statusCode: null
            }).save());

            await Promise.all(promises);

            let res = await request.get('/command/history?status=error');

            res.should.have.status(200);

            chai.expect(res.body.metadata).to.like({
                pagination: {
                    limit: 100,
                    offset: 0,
                    total: 0
                }
            });

            chai.expect(res.body.results).to.be.like([]);
        });

        it('One record, createdBy filter matching', async() => {
            let currDate = new Date();
            let commandId = "60063c0ecde92315a6db59a2";
            let promises = [];

            promises.push(new CommandRun({
                id: commandId,
                command: "testcommand",
                duration: "5.15s",
                status: "Completed",
                error: null,
                createdBy: "username",
                createdOn: currDate,
                result: "commandresult",
                statusCode: null
            }).save());

            await Promise.all(promises);

            let res = await request.get('/command/history?createdBy=username');

            res.should.have.status(200);

            chai.expect(res.body.metadata).to.like({
                pagination: {
                    limit: 100,
                    offset: 0,
                    total: 1
                }
            });

            chai.expect(res.body.results).to.be.like([{
                id: commandId,
                command: "testcommand",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            }]);

            res.body.results.forEach((quickCommandRecord) => {
                chai.expect(quickCommandRecord).to.have.all.keys(QUICK_COMMAND_RECORD_KEYS);
            });
        });

        it('One record, status filter not matching', async() => {
            let currDate = new Date();
            let commandId = "60063c0ecde92315a6db59a2";
            let promises = [];

            promises.push(new CommandRun({
                id: commandId,
                command: "testcommand",
                duration: "5.15s",
                status: "Completed",
                error: null,
                createdBy: "username",
                createdOn: currDate,
                result: "commandresult",
                statusCode: null
            }).save());

            await Promise.all(promises);

            let res = await request.get('/command/history?createdBy=username2');

            res.should.have.status(200);

            chai.expect(res.body.metadata).to.like({
                pagination: {
                    limit: 100,
                    offset: 0,
                    total: 0
                }
            });

            chai.expect(res.body.results).to.be.like([]);
        });

        it('Multiple records record sort by command ascending case insensitive', async() => {
            let currDate = new Date();
            let promises = [];

            let commands = [
                'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',
                'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
                'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm',
                'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'
            ];

            for (let i = 0; i < commands.length; i++) {
                let testCommand = commands[i];

                promises.push(new CommandRun({
                    id: `6000000000000000000000${i.toString().padStart(2, '0')}`,
                    command: testCommand,
                    duration: "1.00s",
                    status: "Completed",
                    error: null,
                    createdBy: "username",
                    createdOn: currDate,
                    result: "commandresult",
                    statusCode: null
                }).save());
            }

            await Promise.all(promises);

            let res = await request.get('/command/history?sort=command&order=asc');

            res.should.have.status(200);

            chai.expect(res.body.metadata).to.like({
                pagination: {
                    limit: 100,
                    offset: 0,
                    total: commands.length
                }
            });

            chai.expect(res.body.results).to.be.like([{
                id: "600000000000000000000026",
                command: "a",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000000",
                command: "A",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000027",
                command: "b",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000001",
                command: "B",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000028",
                command: "c",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000002",
                command: "C",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000029",
                command: "d",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000003",
                command: "D",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000030",
                command: "e",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000004",
                command: "E",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000031",
                command: "f",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000005",
                command: "F",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000032",
                command: "g",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000006",
                command: "G",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000033",
                command: "h",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000007",
                command: "H",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000034",
                command: "i",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000008",
                command: "I",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000035",
                command: "j",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000009",
                command: "J",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000036",
                command: "k",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000010",
                command: "K",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000037",
                command: "l",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000011",
                command: "L",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000038",
                command: "m",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000012",
                command: "M",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000039",
                command: "n",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000013",
                command: "N",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000040",
                command: "o",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000014",
                command: "O",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000041",
                command: "p",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000015",
                command: "P",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000042",
                command: "q",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000016",
                command: "Q",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000043",
                command: "r",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000017",
                command: "R",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000044",
                command: "s",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000018",
                command: "S",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000045",
                command: "t",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000019",
                command: "T",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000046",
                command: "u",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000020",
                command: "U",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000047",
                command: "v",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000021",
                command: "V",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000048",
                command: "w",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000022",
                command: "W",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000049",
                command: "x",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000023",
                command: "X",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000050",
                command: "y",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000024",
                command: "Y",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000051",
                command: "z",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000025",
                command: "Z",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            }]);

            res.body.results.forEach((quickCommandRecord) => {
                chai.expect(quickCommandRecord).to.have.all.keys(QUICK_COMMAND_RECORD_KEYS);
            });
        });

        it('Multiple records record sort by command descending case insensitive', async() => {
            let currDate = new Date();
            let promises = [];

            let commands = [
                'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',
                'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
                'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm',
                'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'
            ];

            for (let i = 0; i < commands.length; i++) {
                let testCommand = commands[i];

                promises.push(new CommandRun({
                    id: `6000000000000000000000${i.toString().padStart(2, '0')}`,
                    command: testCommand,
                    duration: "1.00s",
                    status: "Completed",
                    error: null,
                    createdBy: "username",
                    createdOn: currDate,
                    result: "commandresult",
                    statusCode: null
                }).save());
            }

            await Promise.all(promises);

            let res = await request.get('/command/history?sort=command&order=desc');

            res.should.have.status(200);

            chai.expect(res.body.metadata).to.like({
                pagination: {
                    limit: 100,
                    offset: 0,
                    total: commands.length
                }
            });

            chai.expect(res.body.results).to.be.like([{
                id: "600000000000000000000025",
                command: "Z",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000051",
                command: "z",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000024",
                command: "Y",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000050",
                command: "y",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000023",
                command: "X",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000049",
                command: "x",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000022",
                command: "W",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000048",
                command: "w",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000021",
                command: "V",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000047",
                command: "v",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000020",
                command: "U",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000046",
                command: "u",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000019",
                command: "T",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000045",
                command: "t",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000018",
                command: "S",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000044",
                command: "s",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000017",
                command: "R",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000043",
                command: "r",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000016",
                command: "Q",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000042",
                command: "q",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000015",
                command: "P",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000041",
                command: "p",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000014",
                command: "O",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000040",
                command: "o",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000013",
                command: "N",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000039",
                command: "n",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000012",
                command: "M",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000038",
                command: "m",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000011",
                command: "L",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000037",
                command: "l",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000010",
                command: "K",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000036",
                command: "k",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000009",
                command: "J",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000035",
                command: "j",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000008",
                command: "I",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000034",
                command: "i",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000007",
                command: "H",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000033",
                command: "h",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000006",
                command: "G",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000032",
                command: "g",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000005",
                command: "F",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000031",
                command: "f",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000004",
                command: "E",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000030",
                command: "e",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000003",
                command: "D",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000029",
                command: "d",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000002",
                command: "C",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000028",
                command: "c",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000001",
                command: "B",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000027",
                command: "b",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000000",
                command: "A",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            },
            {
                id: "600000000000000000000026",
                command: "a",
                status: "Completed",
                createdBy: "username",
                createdOn: currDate
            }]);

            res.body.results.forEach((quickCommandRecord) => {
                chai.expect(quickCommandRecord).to.have.all.keys(QUICK_COMMAND_RECORD_KEYS);
            });
        });

        it('Multiple records, to test the start date 1 and end date 1 filter', async() => {
            let promises = [];

            let date1 = new Date();
            let date2 = new Date();
            let date3 = new Date();
            let date4 = new Date();
            date1.setHours(date1.getHours() - 25);
            date2.setHours(date2.getHours() - 23);
            date3.setHours(date3.getHours() - 65);
            date4.setHours(date4.getHours() - 49);

            promises.push(new CommandRun({
                id: "60063c0ecde92315a6db59a2",
                command: "testCommand",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username",
                createdOn: date1,
                result: "commandresult",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "70063c0ecde92315a6db59a2",
                command: "testCommand1",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username1",
                createdOn: date2,
                result: "commandresult1",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "80063c0ecde92315a6db59a2",
                command: "testCommand2",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username2",
                createdOn: date3,
                result: "commandresult2",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "90063c0ecde92315a6db59a2",
                command: "testCommand3",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username3",
                createdOn: date4,
                result: "commandresult3",
                statusCode: null
            }).save());

            let expectedStartDate = new Date();
            expectedStartDate.setDate(expectedStartDate.getDate() - 3);

            let expectedEndDate = new Date();
            expectedEndDate.setDate(expectedEndDate.getDate() - 2);

            await Promise.all(promises);

            let res = await request.get(`/command/history?startDate=${expectedStartDate.toISOString()}&endDate=${expectedEndDate.toISOString()}`);
            res.should.have.status(200);

            chai.expect(res.body.metadata).to.like({
                pagination: {
                    limit: 100,
                    offset: 0,
                    total: 2
                }
            });

            chai.expect(res.body.results).to.be.like([{
                id: "90063c0ecde92315a6db59a2",
                command: "testCommand3",
                status: "Completed",
                createdBy: "username3",
                createdOn: date4
            },
            {
                id: "80063c0ecde92315a6db59a2",
                command: "testCommand2",
                status: "Completed",
                createdBy: "username2",
                createdOn: date3
            }]);

            res.body.results.forEach((quickCommandRecord) => {
                chai.expect(quickCommandRecord).to.have.all.keys(QUICK_COMMAND_RECORD_KEYS);
            });
        });

        it('Multiple records, to test the start date 2 and end date 2 filter', async() => {
            let promises = [];

            let date1 = new Date();
            let date2 = new Date();
            let date3 = new Date();
            let date4 = new Date();
            date1.setDate(date1.getDate() - 185);
            date2.setDate(date2.getDate() - 2);
            date3.setDate(date3.getDate() - 364);
            date4.setDate(date4.getDate() - 253);

            promises.push(new CommandRun({
                id: "60063c0ecde92315a6db59a2",
                command: "testCommand",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username",
                createdOn: date1,
                result: "commandresult",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "70063c0ecde92315a6db59a2",
                command: "testCommand1",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username1",
                createdOn: date2,
                result: "commandresult1",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "80063c0ecde92315a6db59a2",
                command: "testCommand2",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username2",
                createdOn: date3,
                result: "commandresult2",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "90063c0ecde92315a6db59a2",
                command: "testCommand3",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username3",
                createdOn: date4,
                result: "commandresult3",
                statusCode: null
            }).save());

            let expectedStartDate = new Date();
            expectedStartDate.setDate(expectedStartDate.getDate() - 400);

            let expectedEndDate = new Date();
            expectedEndDate.setDate(expectedEndDate.getDate() - 365);

            await Promise.all(promises);

            let res = await request.get(`/command/history?startDate=${expectedStartDate.toISOString()}&endDate=${expectedEndDate.toISOString()}`);
            res.should.have.status(200);

            chai.expect(res.body.metadata).to.like({
                pagination: {
                    limit: 100,
                    offset: 0,
                    total: 0
                },
                startDate: expectedStartDate,
                endDate: expectedEndDate
            });

            chai.expect(res.body.results).to.be.like([]);

            res.body.results.forEach((quickCommandRecord) => {
                chai.expect(quickCommandRecord).to.have.all.keys(QUICK_COMMAND_RECORD_KEYS);
            });
        });

        it('Multiple records, to test the start date 3 and end date 3 filter', async() => {
            let promises = [];

            let date1 = new Date();
            let date2 = new Date();
            let date3 = new Date();
            let date4 = new Date();
            date1.setDate(date1.getDate() - 185);
            date2.setDate(date2.getDate() - 2);
            date3.setDate(date3.getDate() - 364);
            date4.setDate(date4.getDate() - 253);

            promises.push(new CommandRun({
                id: "60063c0ecde92315a6db59a2",
                command: "testCommand",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username",
                createdOn: date1,
                result: "commandresult",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "70063c0ecde92315a6db59a2",
                command: "testCommand1",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username1",
                createdOn: date2,
                result: "commandresult1",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "80063c0ecde92315a6db59a2",
                command: "testCommand2",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username2",
                createdOn: date3,
                result: "commandresult2",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "90063c0ecde92315a6db59a2",
                command: "testCommand3",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username3",
                createdOn: date4,
                result: "commandresult3",
                statusCode: null
            }).save());

            let expectedStartDate = new Date();
            expectedStartDate.setDate(expectedStartDate.getDate() - 400);

            let expectedEndDate = new Date();
            expectedEndDate.setDate(expectedEndDate.getDate() - 265);

            await Promise.all(promises);

            let res = await request.get(`/command/history?startDate=${expectedStartDate.toISOString()}&endDate=${expectedEndDate.toISOString()}`);
            res.should.have.status(200);

            chai.expect(res.body.metadata).to.like({
                pagination: {
                    limit: 100,
                    offset: 0,
                    total: 1
                }
            });

            chai.expect(res.body.results).to.be.like([{
                id: "80063c0ecde92315a6db59a2",
                command: "testCommand2",
                status: "Completed",
                createdBy: "username2",
                createdOn: date3
            }]);

            res.body.results.forEach((quickCommandRecord) => {
                chai.expect(quickCommandRecord).to.have.all.keys(QUICK_COMMAND_RECORD_KEYS);
            });
        });

        it('Multiple records, to test the start date 1 filter alone', async() => {
            let promises = [];

            let date1 = new Date();
            let date2 = new Date();
            let date3 = new Date();
            let date4 = new Date();
            date1.setHours(date1.getHours() - 25);
            date2.setHours(date2.getHours() - 23);
            date3.setHours(date3.getHours() - 65);
            date4.setHours(date4.getHours() - 48);

            promises.push(new CommandRun({
                id: "60063c0ecde92315a6db59a2",
                command: "testCommand",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username",
                createdOn: date1,
                result: "commandresult",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "70063c0ecde92315a6db59a2",
                command: "testCommand1",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username1",
                createdOn: date2,
                result: "commandresult1",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "80063c0ecde92315a6db59a2",
                command: "testCommand2",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username2",
                createdOn: date3,
                result: "commandresult2",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "90063c0ecde92315a6db59a2",
                command: "testCommand3",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username3",
                createdOn: date4,
                result: "commandresult3",
                statusCode: null
            }).save());

            let expectedStartDate = new Date();
            expectedStartDate.setDate(expectedStartDate.getDate() - 3);

            await Promise.all(promises);

            let res = await request.get(`/command/history?startDate=${expectedStartDate.toISOString()}`);
            res.should.have.status(200);

            chai.expect(res.body.metadata).to.like({
                pagination: {
                    limit: 100,
                    offset: 0,
                    total: 4
                }
            });

            chai.expect(res.body.results).to.be.like([{
                id: "70063c0ecde92315a6db59a2",
                command: "testCommand1",
                status: "Completed",
                createdBy: "username1",
                createdOn: date2
            },
            {
                id: "60063c0ecde92315a6db59a2",
                command: "testCommand",
                status: "Completed",
                createdBy: "username",
                createdOn: date1
            },
            {
                id: "90063c0ecde92315a6db59a2",
                command: "testCommand3",
                status: "Completed",
                createdBy: "username3",
                createdOn: date4
            },
            {
                id: "80063c0ecde92315a6db59a2",
                command: "testCommand2",
                status: "Completed",
                createdBy: "username2",
                createdOn: date3
            }]);

            res.body.results.forEach((quickCommandRecord) => {
                chai.expect(quickCommandRecord).to.have.all.keys(QUICK_COMMAND_RECORD_KEYS);
            });
        });

        it('Multiple records, to test the start date 2 filter alone', async() => {
            let promises = [];

            let date1 = new Date();
            let date2 = new Date();
            let date3 = new Date();
            let date4 = new Date();
            date1.setHours(date1.getHours() - 25);
            date2.setHours(date2.getHours() - 23);
            date3.setHours(date3.getHours() - 65);
            date4.setHours(date4.getHours() - 48);

            promises.push(new CommandRun({
                id: "60063c0ecde92315a6db59a2",
                command: "testCommand",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username",
                createdOn: date1,
                result: "commandresult",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "70063c0ecde92315a6db59a2",
                command: "testCommand1",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username1",
                createdOn: date2,
                result: "commandresult1",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "80063c0ecde92315a6db59a2",
                command: "testCommand2",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username2",
                createdOn: date3,
                result: "commandresult2",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "90063c0ecde92315a6db59a2",
                command: "testCommand3",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username3",
                createdOn: date4,
                result: "commandresult3",
                statusCode: null
            }).save());

            let expectedStartDate = new Date();
            expectedStartDate.setDate(expectedStartDate.getDate() - 1);

            await Promise.all(promises);

            let res = await request.get(`/command/history?startDate=${expectedStartDate.toISOString()}`);
            res.should.have.status(200);

            chai.expect(res.body.metadata).to.like({
                pagination: {
                    limit: 100,
                    offset: 0,
                    total: 1
                }
            });

            chai.expect(res.body.results).to.be.like([{
                id: "70063c0ecde92315a6db59a2",
                command: "testCommand1",
                status: "Completed",
                createdBy: "username1",
                createdOn: date2
            }]);

            res.body.results.forEach((quickCommandRecord) => {
                chai.expect(quickCommandRecord).to.have.all.keys(QUICK_COMMAND_RECORD_KEYS);
            });
        });
    });

    describe('Record Download', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/command/history/download');

            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/command/history/download');

                switch(username) {
                    case 'commandAccess':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('No records', async() => {
            let res = await request.get('/command/history/download');

            res.should.have.status(200);
        });

        it('One record', async() => {
            let currDate = new Date();
            let formattedDate = helpers.formatDateForCsv(currDate);
            let commandId = "60063c0ecde92315a6db59a2";
            let promises = [];

            promises.push(new CommandRun({
                id: commandId,
                command: "testcommand",
                duration: "5.15s",
                status: "Completed",
                error: null,
                createdBy: "username",
                createdOn: currDate,
                result: "commandresult",
                statusCode: null
            }).save());

            await Promise.all(promises);

            let res = await request.get('/command/history/download').buffer().parse(helpers.parseDownloadBuffer);

            res.should.have.status(200);

            chai.expect(res.body).to.equal(
                dedent`"Command","Status","User","Time"
                       "testcommand","Completed","username","${formattedDate}"\n`
            );
        });

        it('One record, command filter matching', async() => {
            let currDate = new Date();
            let formattedDate = helpers.formatDateForCsv(currDate);
            let commandId = "60063c0ecde92315a6db59a2";
            let promises = [];

            promises.push(new CommandRun({
                id: commandId,
                command: "testcommand",
                duration: "5.15s",
                status: "Completed",
                error: null,
                createdBy: "username",
                createdOn: currDate,
                result: "commandresult",
                statusCode: null
            }).save());

            await Promise.all(promises);

            let res = await request.get('/command/history/download?command[like]=command').buffer().parse(helpers.parseDownloadBuffer);

            res.should.have.status(200);

            chai.expect(res.body).to.equal(
                dedent`"Command","Status","User","Time"
                       "testcommand","Completed","username","${formattedDate}"\n`
            );
        });

        it('One record, command filter not matching', async() => {
            let currDate = new Date();
            let commandId = "60063c0ecde92315a6db59a2";
            let promises = [];

            promises.push(new CommandRun({
                id: commandId,
                command: "testcommand",
                duration: "5.15s",
                status: "Completed",
                error: null,
                createdBy: "username",
                createdOn: currDate,
                result: "commandresult",
                statusCode: null
            }).save());

            await Promise.all(promises);

            let res = await request.get('/command/history/download?command[like]=somethingelse').buffer().parse(helpers.parseDownloadBuffer);

            res.should.have.status(200);

            chai.expect(res.body).to.equal('');
        });

        it('One record, status filter matching', async() => {
            let currDate = new Date();
            let formattedDate = helpers.formatDateForCsv(currDate);
            let commandId = "60063c0ecde92315a6db59a2";
            let promises = [];

            promises.push(new CommandRun({
                id: commandId,
                command: "testcommand",
                duration: "5.15s",
                status: "Completed",
                error: null,
                createdBy: "username",
                createdOn: currDate,
                result: "commandresult",
                statusCode: null
            }).save());

            await Promise.all(promises);

            let res = await request.get('/command/history/download?status=Completed').buffer().parse(helpers.parseDownloadBuffer);

            res.should.have.status(200);

            chai.expect(res.body).to.equal(
                dedent`"Command","Status","User","Time"
                       "testcommand","Completed","username","${formattedDate}"\n`
            );
        });

        it('One record, status filter not matching', async() => {
            let currDate = new Date();
            let commandId = "60063c0ecde92315a6db59a2";
            let promises = [];

            promises.push(new CommandRun({
                id: commandId,
                command: "testcommand",
                duration: "5.15s",
                status: "Completed",
                error: null,
                createdBy: "username",
                createdOn: currDate,
                result: "commandresult",
                statusCode: null
            }).save());

            await Promise.all(promises);

            let res = await request.get('/command/history/download?status=error').buffer().parse(helpers.parseDownloadBuffer);

            res.should.have.status(200);

            chai.expect(res.body).to.equal('');
        });

        it('One record, createdBy filter matching', async() => {
            let currDate = new Date();
            let formattedDate = helpers.formatDateForCsv(currDate);
            let commandId = "60063c0ecde92315a6db59a2";
            let promises = [];

            promises.push(new CommandRun({
                id: commandId,
                command: "testcommand",
                duration: "5.15s",
                status: "Completed",
                error: null,
                createdBy: "username",
                createdOn: currDate,
                result: "commandresult",
                statusCode: null
            }).save());

            await Promise.all(promises);

            let res = await request.get('/command/history/download?createdBy=username').buffer().parse(helpers.parseDownloadBuffer);

            res.should.have.status(200);

            chai.expect(res.body).to.equal(
                dedent`"Command","Status","User","Time"
                       "testcommand","Completed","username","${formattedDate}"\n`
            );
        });

        it('One record, createdBy filter not matching', async() => {
            let currDate = new Date();
            let commandId = "60063c0ecde92315a6db59a2";
            let promises = [];

            promises.push(new CommandRun({
                id: commandId,
                command: "testcommand",
                duration: "5.15s",
                status: "Completed",
                error: null,
                createdBy: "username",
                createdOn: currDate,
                result: "commandresult",
                statusCode: null
            }).save());

            await Promise.all(promises);

            let res = await request.get('/command/history/download?createdBy=user').buffer().parse(helpers.parseDownloadBuffer);

            res.should.have.status(200);

            chai.expect(res.body).to.equal('');
        });

        it('One record, to test the start date 1 and end date 1 filter', async() => {
            let commandId = "60063c0ecde92315a6db59a2";
            let promises = [];

            let date1 = new Date();
            date1.setHours(date1.getHours() - 65);

            let formattedDate = helpers.formatDateForCsv(date1);

            promises.push(new CommandRun({
                id: commandId,
                command: "testcommand",
                duration: "5.15s",
                status: "Completed",
                error: null,
                createdBy: "username",
                createdOn: date1,
                result: "commandresult",
                statusCode: null
            }).save());

            await Promise.all(promises);

            let expectedStartDate = new Date();
            expectedStartDate.setDate(expectedStartDate.getDate() - 3);

            let expectedEndDate = new Date();
            expectedEndDate.setDate(expectedEndDate.getDate() - 2);

            let res = await request.get(`/command/history/download?startDate=${expectedStartDate.toISOString()}&endDate=${expectedEndDate.toISOString()}`).buffer().parse(helpers.parseDownloadBuffer);

            res.should.have.status(200);

            chai.expect(res.body).to.equal(
                dedent`"Command","Status","User","Time"
                       "testcommand","Completed","username","${formattedDate}"\n`
            );
        });

        it('Multiple records, to test the start date 1 and end date 1 filter', async() => {
            let promises = [];

            let date1 = new Date();
            let date2 = new Date();
            let date3 = new Date();
            let date4 = new Date();

            date1.setHours(date1.getHours() - 28);
            date2.setHours(date2.getHours() - 25);
            date3.setHours(date3.getHours() - 65);
            date4.setHours(date4.getHours() - 50);

            let formattedDate1 = helpers.formatDateForCsv(date1);
            let formattedDate2 = helpers.formatDateForCsv(date2);
            let formattedDate3 = helpers.formatDateForCsv(date3);
            let formattedDate4 = helpers.formatDateForCsv(date4);

            promises.push(new CommandRun({
                id: "60063c0ecde92315a6db59a2",
                command: "testCommand1",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username1",
                createdOn: date1,
                result: "commandresult1",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "70063c0ecde92315a6db59a2",
                command: "testCommand2",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username2",
                createdOn: date2,
                result: "commandresult2",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "80063c0ecde92315a6db59a2",
                command: "testCommand3",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username3",
                createdOn: date3,
                result: "commandresult3",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "90063c0ecde92315a6db59a2",
                command: "testCommand4",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username4",
                createdOn: date4,
                result: "commandresult4",
                statusCode: null
            }).save());

            await Promise.all(promises);

            let expectedStartDate = new Date();
            expectedStartDate.setDate(expectedStartDate.getDate() - 2);

            let expectedEndDate = new Date();
            expectedEndDate.setDate(expectedEndDate.getDate() - 1);

            let res = await request.get(`/command/history/download?startDate=${expectedStartDate.toISOString()}&endDate=${expectedEndDate.toISOString()}`).buffer().parse(helpers.parseDownloadBuffer);

            res.should.have.status(200);

            chai.expect(res.body).to.equal(
                dedent`"Command","Status","User","Time"
                       "testCommand2","Completed","username2","${formattedDate2}"
                       "testCommand1","Completed","username1","${formattedDate1}"\n`
            );
        });

        it('Multiple records, to test the start date 2 and end date 2 filter', async() => {
            let promises = [];

            let date1 = new Date();
            let date2 = new Date();
            let date3 = new Date();
            let date4 = new Date();

            date1.setDate(date1.getDate() - 185);
            date2.setDate(date2.getDate() - 2);
            date3.setDate(date3.getDate() - 364);
            date4.setDate(date4.getDate() - 253);

            let formattedDate1 = helpers.formatDateForCsv(date1);
            let formattedDate2 = helpers.formatDateForCsv(date2);
            let formattedDate3 = helpers.formatDateForCsv(date3);
            let formattedDate4 = helpers.formatDateForCsv(date4);

            promises.push(new CommandRun({
                id: "60063c0ecde92315a6db59a2",
                command: "testCommand1",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username1",
                createdOn: date1,
                result: "commandresult1",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "70063c0ecde92315a6db59a2",
                command: "testCommand2",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username2",
                createdOn: date2,
                result: "commandresult2",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "80063c0ecde92315a6db59a2",
                command: "testCommand3",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username3",
                createdOn: date3,
                result: "commandresult3",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "90063c0ecde92315a6db59a2",
                command: "testCommand4",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username4",
                createdOn: date4,
                result: "commandresult4",
                statusCode: null
            }).save());

            await Promise.all(promises);

            let expectedStartDate = new Date();
            expectedStartDate.setDate(expectedStartDate.getDate() - 400);

            let expectedEndDate = new Date();
            expectedEndDate.setDate(expectedEndDate.getDate() - 365);

            let res = await request.get(`/command/history/download?startDate=${expectedStartDate.toISOString()}&endDate=${expectedEndDate.toISOString()}`).buffer().parse(helpers.parseDownloadBuffer);

            res.should.have.status(200);

            chai.expect(res.body).to.equal("");
        });

        it('Multiple records, to test the start date 3 and end date 3 filter', async() => {
            let promises = [];

            let date1 = new Date();
            let date2 = new Date();
            let date3 = new Date();
            let date4 = new Date();

            date1.setDate(date1.getDate() - 185);
            date2.setDate(date2.getDate() - 2);
            date3.setDate(date3.getDate() - 364);
            date4.setDate(date4.getDate() - 253);

            let formattedDate1 = helpers.formatDateForCsv(date1);
            let formattedDate2 = helpers.formatDateForCsv(date2);
            let formattedDate3 = helpers.formatDateForCsv(date3);
            let formattedDate4 = helpers.formatDateForCsv(date4);

            promises.push(new CommandRun({
                id: "60063c0ecde92315a6db59a2",
                command: "testCommand1",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username1",
                createdOn: date1,
                result: "commandresult1",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "70063c0ecde92315a6db59a2",
                command: "testCommand2",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username2",
                createdOn: date2,
                result: "commandresult2",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "80063c0ecde92315a6db59a2",
                command: "testCommand3",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username3",
                createdOn: date3,
                result: "commandresult3",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "90063c0ecde92315a6db59a2",
                command: "testCommand4",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username4",
                createdOn: date4,
                result: "commandresult4",
                statusCode: null
            }).save());

            await Promise.all(promises);

            let expectedStartDate = new Date();
            expectedStartDate.setDate(expectedStartDate.getDate() - 400);

            let expectedEndDate = new Date();
            expectedEndDate.setDate(expectedEndDate.getDate() - 265);

            let res = await request.get(`/command/history/download?startDate=${expectedStartDate.toISOString()}&endDate=${expectedEndDate.toISOString()}`).buffer().parse(helpers.parseDownloadBuffer);

            res.should.have.status(200);

            chai.expect(res.body).to.equal(
                dedent`"Command","Status","User","Time"
                       "testCommand3","Completed","username3","${formattedDate3}"\n`
            );
        });

        it('Multiple records, to test the start date 4 and end date 4 filter', async() => {
            let promises = [];

            let date1 = new Date();
            let date2 = new Date();
            let date3 = new Date();
            let date4 = new Date();

            date1.setDate(date1.getDate() - 185);
            date2.setDate(date2.getDate() - 2);
            date3.setDate(date3.getDate() - 364);
            date4.setDate(date4.getDate() - 253);

            let formattedDate1 = helpers.formatDateForCsv(date1);
            let formattedDate2 = helpers.formatDateForCsv(date2);
            let formattedDate3 = helpers.formatDateForCsv(date3);
            let formattedDate4 = helpers.formatDateForCsv(date4);

            promises.push(new CommandRun({
                id: "60063c0ecde92315a6db59a2",
                command: "testCommand1",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username1",
                createdOn: date1,
                result: "commandresult1",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "70063c0ecde92315a6db59a2",
                command: "testCommand2",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username2",
                createdOn: date2,
                result: "commandresult2",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "80063c0ecde92315a6db59a2",
                command: "testCommand3",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username3",
                createdOn: date3,
                result: "commandresult3",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "90063c0ecde92315a6db59a2",
                command: "testCommand4",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username4",
                createdOn: date4,
                result: "commandresult4",
                statusCode: null
            }).save());

            await Promise.all(promises);

            let expectedStartDate = new Date();
            expectedStartDate.setDate(expectedStartDate.getDate() - 500);

            let expectedEndDate = new Date();
            expectedEndDate.setDate(expectedEndDate.getDate() - 165);

            let res = await request.get(`/command/history/download?startDate=${expectedStartDate.toISOString()}&endDate=${expectedEndDate.toISOString()}`).buffer().parse(helpers.parseDownloadBuffer);

            res.should.have.status(200);

            chai.expect(res.body).to.equal(
                dedent`"Command","Status","User","Time"
                       "testCommand1","Completed","username1","${formattedDate1}"
                       "testCommand4","Completed","username4","${formattedDate4}"
                       "testCommand3","Completed","username3","${formattedDate3}"\n`
            );
        });

        it('Multiple records, to test the start date 1 filter only', async() => {
            let promises = [];

            let date1 = new Date();
            let date2 = new Date();
            let date3 = new Date();
            let date4 = new Date();

            date1.setDate(date1.getDate() - 185);
            date2.setDate(date2.getDate() - 2);
            date3.setDate(date3.getDate() - 364);
            date4.setDate(date4.getDate() - 253);

            let formattedDate1 = helpers.formatDateForCsv(date1);
            let formattedDate2 = helpers.formatDateForCsv(date2);
            let formattedDate3 = helpers.formatDateForCsv(date3);
            let formattedDate4 = helpers.formatDateForCsv(date4);

            promises.push(new CommandRun({
                id: "60063c0ecde92315a6db59a2",
                command: "testCommand1",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username1",
                createdOn: date1,
                result: "commandresult1",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "70063c0ecde92315a6db59a2",
                command: "testCommand2",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username2",
                createdOn: date2,
                result: "commandresult2",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "80063c0ecde92315a6db59a2",
                command: "testCommand3",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username3",
                createdOn: date3,
                result: "commandresult3",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "90063c0ecde92315a6db59a2",
                command: "testCommand4",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username4",
                createdOn: date4,
                result: "commandresult4",
                statusCode: null
            }).save());

            await Promise.all(promises);

            let expectedStartDate = new Date();
            expectedStartDate.setDate(expectedStartDate.getDate() - 500);

            let res = await request.get(`/command/history/download?startDate=${expectedStartDate.toISOString()}`).buffer().parse(helpers.parseDownloadBuffer);

            res.should.have.status(200);

            chai.expect(res.body).to.equal(
                dedent`"Command","Status","User","Time"
                       "testCommand2","Completed","username2","${formattedDate2}"
                       "testCommand1","Completed","username1","${formattedDate1}"
                       "testCommand4","Completed","username4","${formattedDate4}"
                       "testCommand3","Completed","username3","${formattedDate3}"\n`
            );
        });

        it('Multiple records, to test the start date 2 filter only', async() => {
            let promises = [];

            let date1 = new Date();
            let date2 = new Date();
            let date3 = new Date();
            let date4 = new Date();

            date1.setDate(date1.getDate() - 185);
            date2.setDate(date2.getDate() - 2);
            date3.setDate(date3.getDate() - 364);
            date4.setDate(date4.getDate() - 253);

            let formattedDate1 = helpers.formatDateForCsv(date1);
            let formattedDate2 = helpers.formatDateForCsv(date2);
            let formattedDate3 = helpers.formatDateForCsv(date3);
            let formattedDate4 = helpers.formatDateForCsv(date4);

            promises.push(new CommandRun({
                id: "60063c0ecde92315a6db59a2",
                command: "testCommand1",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username1",
                createdOn: date1,
                result: "commandresult1",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "70063c0ecde92315a6db59a2",
                command: "testCommand2",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username2",
                createdOn: date2,
                result: "commandresult2",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "80063c0ecde92315a6db59a2",
                command: "testCommand3",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username3",
                createdOn: date3,
                result: "commandresult3",
                statusCode: null
            }).save());

            promises.push(new CommandRun({
                id: "90063c0ecde92315a6db59a2",
                command: "testCommand4",
                duration: "1.00s",
                status: "Completed",
                error: null,
                createdBy: "username4",
                createdOn: date4,
                result: "commandresult4",
                statusCode: null
            }).save());

            await Promise.all(promises);

            let expectedStartDate = new Date();
            expectedStartDate.setDate(expectedStartDate.getDate() - 210);

            let res = await request.get(`/command/history/download?startDate=${expectedStartDate.toISOString()}`).buffer().parse(helpers.parseDownloadBuffer);

            res.should.have.status(200);

            chai.expect(res.body).to.equal(
                dedent`"Command","Status","User","Time"
                       "testCommand2","Completed","username2","${formattedDate2}"
                       "testCommand1","Completed","username1","${formattedDate1}"\n`
            );
        });
    });

    describe('Record View', () => {
        it('Unauthenticated request', async() => {
            let commandId = "60063c0ecde92315a6db59a2";
            await new CommandRun({
                id: commandId,
                command: "testcommand",
                duration: "5.15s",
                status: "Completed",
                error: null,
                createdBy: "username",
                createdOn: new Date(),
                result: "commandresult",
                statusCode: null
            }).save();

            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get(`/command/view/html/${commandId}`);

            res.should.have.status(302);
            res.should.redirectTo('/auth/login');
        });

        it('Authorization tests', async() => {
            let commandId = "60063c0ecde92315a6db59a2";
            await new CommandRun({
                id: commandId,
                command: "testcommand",
                duration: "5.15s",
                status: "Completed",
                error: null,
                createdBy: "username",
                createdOn: new Date(),
                result: "commandresult",
                statusCode: null
            }).save();

            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get(`/command/view/html/${commandId}`);

                switch(username) {
                    case 'commandAccess':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });
});