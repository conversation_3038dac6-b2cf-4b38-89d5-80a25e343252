#extraInfo {
  margin-top: 10px;
}

.card-body {
  padding: 0.5rem;
}

.color-bl {
  color: #007bff;
}

.highlight-sec {
  margin: 0 15px;
  padding: 0;
  width: 97%;
  background: #e5ebef;
}

.no-pad {
  padding: 0;
}

.mar-left-10 {
  margin-left: 10px;
}

.mar-04 button, .mar-04 input, .mar-04 select {
  overflow: visible;
  /*margin: 0 0.4rem;*/
}

.pad-rem25 td, .pad-rem25 th {
  padding: 0.25rem;
}

.pad-rem25 thead th {
  vertical-align: middle;
}

.toogle-info {
  display:none;
  font-size: 1.4rem;
}

.wid-6-rem {
  width: 6rem;
}

.wid-5-rem {
  width: 5rem;
}

#disabled-noted {
  line-height: 0px;
  position: relative;
  top: -28px;
  padding: 0.1rem 0.25rem;
  border: 1px solid transparent;
}

.ptb-3 {
  padding-top: 1rem!important;
  border: 1px solid #dee2e6;
  border-top: 0;
}

.ruleTable th {
  border-top: 0;
}

.p-pl-1 {
  margin-top: 1rem;
  margin-bottom: 0;
  background: aliceblue;
}

.nav-tabs .tab-nav {
  padding: .7rem .95rem;;
  background:#2ca8f5;
  color:#fff;
  border: 1px solid #fff;
  border-top-left-radius: .5rem;
  border-top-right-radius: .5rem;
  border-width: thin;
}

.alterPad {
  padding: .7rem .6rem !important;
}

.nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active {
  color: #495057!important;
  background-color: #fff!important;
  padding: .55rem .95rem;
  border-bottom: 6px solid;
}

.nav-tabs .nav-link.active i {
  color: var(--item-bg);
}

.mb-m-10 {
  margin-bottom: -10px;
}

span.circle-bg {
  display: inline-block;
  border-radius: 70%;
  padding: 4px;
  font-size: 14px;
  background: #fff;
}

.ssu-letter {
  position: relative;
  left: 13px;
  bottom: 2px;
  color: white;
  z-index: 999;
}

.ssu-circle {
  right: 5px;
  position: relative;
  font-size: 24px;
  top: 1px;
}

.dropdown-submenu {
  position: relative;
}

.dropdown-submenu a::after {
  transform: rotate(-90deg);
  position: absolute;
  right: 6px;
  top: .8em;
}

.dropdown-submenu .dropdown-menu {
  top: -11px;
  left: 100%;
  margin-left: .1rem;
  margin-right: .1rem;
}

.feedback-message-read {
  color: #808080
}

.feedback-message-read:hover {
  color: #202020
}

.feedback-message-unread {
  color: #4040ff
}

.feedback-message-unread:hover {
  color: #0000ff
}
