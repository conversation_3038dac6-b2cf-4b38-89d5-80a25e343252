import { Route, Routes } from 'react-router'
import { AppStateProvider } from './context/AppStateContext'
import { ServiceCheck } from './features/serviceCheck/ServiceCheck'
export const App = () => {
    return (
        <AppStateProvider>
            <Routes>
                {/* To update: New UI link */}
                <Route path="/serviceCheck/new" element={<ServiceCheck />} />
            </Routes>
        </AppStateProvider>
    )
}
