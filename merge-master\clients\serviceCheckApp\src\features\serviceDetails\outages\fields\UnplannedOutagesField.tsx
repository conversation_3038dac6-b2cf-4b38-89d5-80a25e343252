import { CollapsablePanel } from '../../../../components/collapsablePanel/CollapsablePanel'
import { DetailField } from '../../../../components/detailsPanel/DetailField'
import { Panel } from '../../../../components/panel/Panel'
import { formatDate } from '../../../../helpers/formatDate'
import { UnplannedOutage } from '../../../../infrastructure/models'
import styles from './UnplannedOutagesField.module.scss'

interface UnplannedOutagesFieldProps {
    value: UnplannedOutage[]
}

export const UnplannedOutagesField = ({
    value,
}: UnplannedOutagesFieldProps) => {
    if (value === null || value === undefined) {
        return (
            <Panel>
                <DetailField label="Unplanned Outages" value="Unknown" />
            </Panel>
        )
    }

    if (value.length === 0) {
        return (
            <Panel>
                <DetailField label="Unplanned Outages" value="None" />
            </Panel>
        )
    }

    return (
        <CollapsablePanel
            headerElement={<DetailField label="Unplanned Outages" value="" />}
            canOpen={value.length > 0}
            itemCount={value.length}
        >
            <div className={styles.unplannedOutages}>
                <div className={styles.outageRow}>
                    {value.map((outage, index) => (
                        <div key={`${outage.REC_ID}-${index}`}>
                            <DetailField
                                label="CONEN"
                                linkValue={`https://conen.tmof.in.telstra.com.au/conen/eventdetail.cgi?ID=${outage.REC_ID}`}
                                value={outage.REC_ID}
                                inline={true}
                            />
                            <DetailField
                                label="Start"
                                value={formatDate(outage.EVENT_DATE, 'Unknown')}
                                inline={true}
                            />
                            <DetailField
                                label="ETR"
                                value={formatDate(outage.ETR, 'Unknown')}
                                inline={true}
                            />
                        </div>
                    ))}
                </div>
            </div>
        </CollapsablePanel>
    )
}
