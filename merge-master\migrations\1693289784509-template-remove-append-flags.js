/**
 * Note: This migration file was created when 'migrate-mongoose' was the npm package
 * used to perform MongoDB migrations, since the 'migrate' npm package is now used
 * the implementation may not be compatible and all functionality is commented out
 */

// const mongoose = require('mongoose');
// const migrate = require('./migrate');

// const textTemplateSchema = new mongoose.Schema({
//   name: { type: String, default: null, minLength: 1, unique: true, required: true, immutable: true },
//   active: { type: Boolean, default: false },
//   title: { type: String, default: "" },
//   description: { type: String, default: "" },
//   allowedSystemsToAppend: {
//       type: [{
//           type: String,
//           enum: [
//               "SIIAM",
//               "ServiceCentral"
//           ]
//       }],
//       default: [],
//       validate: {
//           validator: function(allowedSystemsToAppend) {
//               return allowedSystemsToAppend.length === new Set(allowedSystemsToAppend).size;
//           },
//           message: "array should only contain unique string values"
//       }
//   },
//   suite: {
//       type: [{
//           type: String,
//           enum: [
//               "standard",
//               "wireless",
//               "ipvoice",
//               "outageInfo",
//               "wholesale"
//           ]
//       }],
//       default: [],
//       validate: {
//           validator: function(suite) {
//               return suite.length === new Set(suite).size;
//           },
//           message: "array should only contain unique string values"
//       }
//   },
//   templateType: { type: String, enum: [
//       "Service Check",
//       "Module"
//   ], default: "Service Check" },
//   formatType: { type: String, enum: [
//       "Text",
//       "HTML"
//   ], default: "Text" },
//   listCondition: { type: String, default: "" },
//   template: { type: String, default: "" },
//   createdBy: { type: String, required: true, immutable: true, default: "unknown" },
//   createdOn: { type: Date, required: true, immutable: true, default: Date.now }
// });


// textTemplateSchema.methods.toJSON = function() {
//   let obj = this.toObject();
//   delete obj._id;
//   delete obj.__v;
//   return obj;
// }


// const Template = mongoose.model("template", textTemplateSchema);

/**
 * Make any changes you need to make to the database here
 */
async function up () {
    // Write migration here
    await migrate.connect();

    await Template.updateMany({}, {
        $unset: {
            allowSIIAMAppend: 1,
            allowServiceCentralAppend: 1,
            allowServiceNowAppend: 1
        }
    }, { multi: true, strict: false });
}

/**
 * Make any changes that UNDO the up function side effects here (if possible)
 */
async function down () {
    // Write migration here
}

module.exports = { up, down };
