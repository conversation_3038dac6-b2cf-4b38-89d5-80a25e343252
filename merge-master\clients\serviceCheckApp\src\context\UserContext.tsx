import React, { createContext, useContext } from 'react';

export interface User {
    username: string
    canModify: (serviceCheckRecordUsername: string) => boolean;
}

const defaultUser: User = (() => {
    const username =
      typeof window !== 'undefined' && window.__USER__
        ? window.__USER__.username
        : '';
    return {
      username,
      canModify: (serviceCheckRecordUsername: string) => username === serviceCheckRecordUsername,
    };
  })();

const UserContext = createContext<User>(defaultUser)

export const UserProvider = ({ children }: { children: React.ReactNode }) => {
    return (
        <UserContext.Provider value={defaultUser}>
            {children}
        </UserContext.Provider>
    )
}

export const useUser = (): User => {
    return useContext(UserContext)
}
