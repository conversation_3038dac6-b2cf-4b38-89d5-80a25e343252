'use strict';

import debug from 'debug';
import express from 'express';
import { param, body, validationResult } from 'express-validator';
import { isValidPhoneNumber, parsePhoneNumber } from 'libphonenumber-js';
import moment from 'moment';


import { runCommand } from '../../modules/commandRun.js';
import { getCommandsList } from '../../routes/command.js';
import CommandRun from '../../db/model/commandRun.js';
import ValidateFnnCheck from '../../db/model/validateFnnCheck.js';
import { generateId } from '../../modules/helpers/id.js';
import logger from '../../modules/logger.js';
import { ValidateFnnCheckStatus } from '../../modules/enumerations.js';

const debugMsg = debug('merge:apiCommand');
const router = express.Router();


/**
 * Helper function to setup the quick command object to use in the runCommand() function
 * @param {String} fnn input FNN string
 * @returns {Array} with 2 elements, CommandRun instance and function to check whether FNN is valid depending on the command
 */
function setupQuickCommandValidateFnn(fnn) {
    let QCr = new CommandRun();
    let isValidFNNFunction;

    QCr.id = QCr._id;

    let phoneNumber = null;
    let phoneNumberIsMobile = null;
    if (isValidPhoneNumber(fnn, 'AU')) {
        phoneNumber = parsePhoneNumber(fnn, 'AU');
        phoneNumberIsMobile = phoneNumber.format('E.164')[3] === '4';
    }

    if ((phoneNumber && phoneNumberIsMobile) || fnn.match(/^\d{15}$/)) {
        let id = `validateFnn-${generateId('')}`;
        let inputFnn = phoneNumber && phoneNumberIsMobile ? phoneNumber.format('E.164').replace('+', '') : fnn;

        QCr.command = `BF -f ${inputFnn} --ID ${id}`;
        isValidFNNFunction = function(response) {
            let isValid = false;

            // Only a subset of carriers are considered valid as reported
            // in Bruno and Fluffy
            if (['Ericsson DCP', 'Jasper', 'Retail'].includes(response?.data?.results?.APIresponse?.Carrier)) {
                isValid = true;
            }

            return isValid;
        }
    } else if (phoneNumber && !phoneNumberIsMobile) {
        let inputFnn = `0${phoneNumber.nationalNumber}`
        QCr.command = `COMMPilot --ID ${inputFnn}`;

        isValidFNNFunction = function(response) {
            return response?.data?.user ? true : false;
        }
    } else {
        QCr.command = `magpie -f ${fnn}`;

        isValidFNNFunction = function(response) {
            return (response?.data?.FOUND === true) || (response?.data?.SUCCESS === true) ? true : false;
        }
    }

    return [QCr, isValidFNNFunction];
}


/**
 * @swagger
 * components:
 *   schemas:
 *     validateFnnCheckInput:
 *       type: object
 *       properties:
 *         fnn:
 *           type: string
 *           description: FNN (Service number to be verified)
 *           example: N2651597R
 */


/**
 * @swagger
 * /api/command/list:
 *   get:
 *     summary: Gets all the available commands which can be used.
 *     description: List of all the sources along with examples on how to query them
 *     security:
 *       - MergeToken: []
 *     responses:
 *       200:
 *         description: A list of all the available sources along with the exmple of how to use them
 *         content:
 *           application/json:
 *             schema:
 *                type: object
 *       401:
 *         description: Not authenticated
 *       500:
 *         description: Internal server error while trying to fetch commands list
 *     tags:
 *       - Quick Command
 */
router.get('/list', function (req, res, next) {
    try {
        let resJson = getCommandsList();
        res.json(resJson);
    } catch (error) {
        logger.error(error);
        res.sendStatus(500);
    } finally {
        debugMsg(`Quick command list called`);
    }
});


/**
 * @swagger
 * /api/command/run:
 *   post:
 *     summary: Run a quick command to fetch information from source.
 *     description: Quick command to fetch information from source
 *     security:
 *       - MergeToken: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/x-www-form-urlencoded:
 *           schema:
 *             type: object
 *             properties:
 *               command:
 *                 type: string
 *                 example: magpie -f N2651597R
 *                 description: "Command with appropriate params"
 *             required:
 *               - command
 *     responses:
 *       200:
 *         description: A JSON output of command
 *         content:
 *           application/json:
 *             schema:
 *                type: object
 *       401:
 *         description: Not authenticated
 *     tags:
 *       - Quick Command
 */
router.post('/run', [
    body('command').isString().trim().withMessage('command is required'),
    body('async').optional().isBoolean().toBoolean()
], async function (req, res) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    try {
        let startTime = moment();
        let QCr = new CommandRun({
            command: req.body.command
        });

        QCr.id = QCr._id;
        QCr.createdBy = req.user.username;
        QCr.status = 'Running';
        await QCr.save();

        runCommand(QCr, null, async function(error, response) {
            //Calculate duration
            QCr.duration = (moment().diff(startTime) / 1000).toFixed(2);

            if (error) {
                QCr.status = 'Error';
                QCr.error = error.toString();
            } else {
                QCr.status = 'Completed';
            }

            if (response) {
                QCr.statusCode = response.status;

                if (typeof response.data === 'object') {
                    QCr.result = JSON.stringify(response.data);
                } else {
                    QCr.result = response.data;
                }
            }

            try {
                await QCr.save();
                debugMsg(`Saved quick command record ${QCr.id}`);
            } catch(error) {
                debugMsg(`Could not save quick command record ${QCr.id}`);
                logger.error(`Could not save quick command record ${QCr.id}`);
            }

            // Converts commandRun model to object to remove _id key from response
            if(!asyncFetch) res.send(QCr.toJSON());
        });

        let asyncFetch = req.body.hasOwnProperty('async') || null;
        if(asyncFetch) res.send(QCr);

    } catch (error) {
        res.sendStatus(500);
        logger.error(error);
    }
});


/**
 * @swagger
 * /api/command/validateFnn/{fnn}:
 *   get:
 *     summary: Check whether a given FNN is valid or not.
 *     description: Verify if command is valid or not from FNN
 *     security:
 *       - MergeToken: []
 *     parameters:
 *     - in: path
 *       name: fnn
 *       description: Mandatory field. fnn (Service number to be verified)
 *       required: true
 *       example: N2651597R
 *       schema:
 *         type: string
 *         example: N2651597R
 *     responses:
 *       200:
 *         description: Object to specify if service number is valid or not
 *         content:
 *           application/json:
 *             schema:
 *                type: object
 *       401:
 *         description: Not authenticated
 *     tags:
 *       - Quick Command
 *       - Validate FNN
 */
router.get('/validateFnn/:fnn', [
    param('fnn').trim()
], async function (req, res) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let fnn = req.params.fnn;

    let [QCr, isValidFNNFunction] = setupQuickCommandValidateFnn(fnn);

    runCommand(QCr, null, function(error, response) {
        if (error) {
            // Just logs the error encountered while running command
            logger.error(`Validate FNN, error when running command, ${error.toString()}`);
        }

        try {
            let isValidFNN = isValidFNNFunction(response);

            debugMsg('Validate fnn for: ' + fnn);

            res.send({ isValid: isValidFNN });
        } catch(error) {
            logger.error(`Validate FNN, determining valid FNN function error, ${error.toString()}`);

            res.status(500).send({ error: error.toString() });
        }
    });
});


/**
 * @swagger
 * /api/command/validateFnnCheck:
 *   post:
 *     summary: Starts a check on whether a given FNN is valid or not
 *     description: Starts a check on validity of input FNN, returns an ID to use in a synchronous polling endpoint
 *     security:
 *       - MergeToken: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/x-www-form-urlencoded:
 *           schema:
 *             $ref: '#/components/schemas/validateFnnCheckInput'
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/validateFnnCheckInput'
 *           examples:
 *             Valid input FNN:
 *               value:
 *                 fnn: N2651597R
 *     responses:
 *       200:
 *         content:
 *           application/json:
 *             schema:
 *                type: object
 *                properties:
 *                  id:
 *                    type: string
 *                    description: ID of the validate FNN check, to be used to retrieve results
 *                    example: ae792fe29d99fb2cddacf8a21cb4bc7b
 *       401:
 *         description: Not authenticated
 *       403:
 *         description: Forbidden
 *       500:
 *         description: Internal Server Error
 *     tags:
 *       - Quick Command
 *       - Validate FNN
 */
router.post('/validateFnnCheck', [
    body('fnn').isString().isLength({ min: 1 })
], async function(req, res) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    try {
        let fnn = req.body.fnn;
        let id = generateId();

        let fnnCheck = new ValidateFnnCheck({
            id: id,
            fnn: fnn,
            status: ValidateFnnCheckStatus.running,
            createdBy: req.user.username
        });
        await fnnCheck.save();

        let [QCr, isValidFNNFunction] = setupQuickCommandValidateFnn(fnn);

        // Just wraps the run quick command call in a promise as it's
        // easier to work with, may not be required if quick command module is refactored
        let runCommandPromise = new Promise((resolve, reject) => {
            runCommand(QCr, null, async function(error, response) {
                if (error) {
                    reject(error);
                } else {
                    resolve(response);
                }
            });
        });

        runCommandPromise.then(async (response) => {
            try {
                let isValidFNN = isValidFNNFunction(response);

                fnnCheck.status = ValidateFnnCheckStatus.done;
                fnnCheck.isValid = isValidFNN;
                await fnnCheck.save();
            } catch(error) {
                logger.error(`Could not save validate FNN check, ${error.toString()}`);
            }
        }).catch(async (validateError) => {
            try {
                logger.warn(`Error running validate FNN check, ${validateError.toString()}`);
                fnnCheck.isValid = false;
                fnnCheck.status = ValidateFnnCheckStatus.error;

                await fnnCheck.save();
            } catch(error) {
                logger.error(`Could not save validate FNN check, ${error.toString()}`);
            }
        });

        res.send({
            id: id
        });
    } catch (error) {
        res.sendStatus(500);
    }
});


/**
 * @swagger
 * /api/command/validateFnnCheck/{id}:
 *   get:
 *     summary: Retrieves the status and results of a validate FNN check
 *     description: Requires input ID from starting a validate FNN check and retrieves the status of the check
 *     security:
 *       - MergeToken: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Mandatory field, job ID of the validate FNN check
 *         required: true
 *         schema:
 *           type: string
 *           example: ae792fe29d99fb2cddacf8a21cb4bc7b
 *     responses:
 *       200:
 *         content:
 *           application/json:
 *             schema:
 *                type: object
 *                properties:
 *                  id:
 *                    type: string
 *                    description: ID of the validate FNN check
 *                    example: ae792fe29d99fb2cddacf8a21cb4bc7b
 *                  fnn:
 *                    type: string
 *                    description: FNN that was validated
 *                    example: N2651597R
 *                  status:
 *                    type: string
 *                    description: Status of the validate FNN check
 *                    example: done
 *                  isValid:
 *                    type: boolean
 *                    description: Flag on whether the input FNN is valid. Can be null if the status is still "running"
 *                    example: true
 *                  createdBy:
 *                    type: string
 *                    description: User who initiated the validate FNN check
 *                    example: d123456
 *                  createdOn:
 *                    type: date
 *                    description: Date on when the validate FNN check was started
 *                    example: 2023-10-01T12:20:30Z
 *       401:
 *         description: Not authenticated
 *       403:
 *         description: Forbidden
 *       500:
 *         description: Internal Server Error
 *     tags:
 *       - Quick Command
 *       - Validate FNN
 */
router.get('/validateFnnCheck/:id', [
    param('id').isString().isLength({ min: 1 })
], async function(req, res) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    try {
        let id = req.params.id;
        let fnnCheck = await ValidateFnnCheck.findOne({ id: id }).select(['-_id', '-__v']);

        if (!fnnCheck) {
            res.sendStatus(404);
            return;
        }

        if (fnnCheck.createdBy !== req.user.username) {
            res.sendStatus(403);
            return;
        }

        res.send(fnnCheck);
    } catch(error) {
        res.sendStatus(500);
    }
});


/**
 * @swagger
 * /api/command/view/{id}:
 *   get:
 *     summary: Gets the details of a command which already ran.
 *     description: Get command output from ID
 *     security:
 *       - MergeToken: []
 *     parameters:
 *     - in: path
 *       name: id
 *       description: Mandatory field. id (unique identifier for the command)
 *       required: true
 *       example: 5fa3f523243eac04880d1a34
 *       schema:
 *         type: string
 *         example: 5fa3f523243eac04880d1a34
 *     responses:
 *       200:
 *         description: A JSON output of command
 *         content:
 *           application/json:
 *             schema:
 *                type: object
 *       401:
 *         description: Not authenticated
 *     tags:
 *       - Quick Command
 */
router.get('/view/:id', [
    param('id').isString().isLength({ min: 1 })
], async function (req, res, next) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let id = req.params.id;

    try {
        let commandRunRecord = await CommandRun.findOne({ id: id }).select(['-__v', '-_id']);

        if (!commandRunRecord) {
            res.sendStatus(404);
        } else {
            res.send(commandRunRecord);
        }
    } catch (error) {
        logger.error(`Error when finding command run record ${id}, ${errors.toString()}`);
        res.sendStatus(500);
    }
});

export default router;
