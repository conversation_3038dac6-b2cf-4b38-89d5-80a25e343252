<!-- v1 -->
<%- include('header', {}); %>
<%- include('menu', {currentTab: 'Home'}); %>
<div class="container">
    <br>
    <div class="jumbotron">
        <div>
            <div class="row">
                <div class="col-sm-8">
                    <h1 class="display-4">Quick Command</h1>
                </div>
                <div class="col-sm-4">
                    <div class="form-check form-check-inline <%= user && user.isDeveloper ? '' : 'invisible' %>">
                        <input class="form-check-input" type="checkbox" id="showDebugActive" checked>
                        <label class="form-check-label" for="showDebugActive">Show Debug Info</label>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <form id="commandForm" class="was-validated" novalidate>
                        <input id="command" type="text" class="form-control" placeholder="command" onkeydown="commandKeyDown(event)" required>
                        <div class="invalid-feedback">Command must not be empty</div>
                    </form>
                </div>
            </div>
            <div class="row">
                <div class="col-auto">
                    <small>Example: "siiam --fnn N2766194R", "siiam --ticket 148331960", "ping -a -d DPWHLNMTVR01C08". List of all commands and their switches are <a href="/command/list" target="Merge_command_list">here</a></small>
                    <br>
                    <small>Use the up &uarr; and down  &darr; arrow keys to select previously run commands.</small>
                </div>
            </div>
            <br>
            <div class="row">
                <div class="col-sm-6">
                    <button id="run" type="button" class="btn btn-success">Run</button>
                    <button id="clear" type="button" class="btn btn-secondary" onclick="clearResultInPage()">Clear</button>
                </div>
            </div>
        </div>
    </div>
    <div id="QCResults"></div>
    <br>
    <div class="border border-secondary rounded p-2 debugSection"><p class="h3" style="display: inline">Debug Section</p><a class="expand btn-sm" data-toggle="collapse" data-target="#QCDebug"><span style="color:blue" class="fas fa-minus-square" title="Expand/Collapse"></span></a><div id="QCDebug" class="collapse border show"></div></div>
    <br>
</div>
<script src="/socket.io/socket.io.js"></script>
<script src="/public/javascripts/ace.js" crossorigin="anonymous"></script>
<script src="/public/javascripts/merge_QC_lib.js" crossorigin="anonymous"></script>
<script>
    const socket = io();
    const currentUser = "<%- user.username %>";
    var commandOffset = 0;

    $(document).ready(function() {
        // Init config
        if (localStorage.getItem('showDebugActive') != null) {
            $('#showDebugActive').prop('checked', JSON.parse(localStorage.getItem('showDebugActive')));
        }

        if (!$('#showDebugActive').prop('checked')) {
            $('.debugSection').fadeOut('fast');
        }

        // Set last command
        if (localStorage.getItem('command') != null) {
            $('#command').val(localStorage.getItem('command'));
        }

        // Trigger when Run button clicked
        $('#run').click(function () {
            document.getElementById("command").checkValidity();

            let command = $("#command").val();
            if (command.length > 0) {
                localStorage.setItem('command', command);

                postQCResult(command);
                $("#command").val("");
            }
        })

        // Show and Hide Rules
        $('#showDebugActive').change(function(){
            if (this.checked) {
                $('.debugSection').fadeIn('fast');
                localStorage.setItem('showDebugActive', true);
            } else {
                $('.debugSection').fadeOut('fast');
                localStorage.setItem('showDebugActive', false);
            }
        });

        // Prevents form from directing when submitting
        $("#commandForm").submit(function() {
            return false;
        })
    });

    socket.on('QCStart', function (QCr) {
        addQCCommand(QCr);
    });

    // Main Command Update
    socket.on('QC', function (QCr) {
        updateQCResult(QCr);
        addDebug('QCr of ' + QCr.command, QCr);
    });

    //Debug info
    socket.on('QCDebug', function (title, data) {
        addDebug(title, data);
    });

    function clearResultInPage() {
        $("#QCResults").empty();
    }

    function postQCResult(QC) {
        let command = $("#command").val();
        socket.emit('commandRun', command);
    }

    //Set vaue of command field
    function setCommand(command) {
        $("#command").val(command);
    }

    //Trap arrow keys to act for previous and next command
    function commandKeyDown(e) {
        let key = e.which;
        let command = $("#command").val();

        if (key == 13) {
            if (command.length > 0) {
                localStorage.setItem('command', command);

                postQCResult(command);
                $("#command").val("");
            }
        } else if (key == 38) {
            // Up key code
            if (command.length === 0) {
                commandOffset = 0;
            } else {
                commandOffset += 1;
            }

            let promise = getCommandByOffset(currentUser, commandOffset);
            promise.done((commandRunRecord) => {
                if (commandRunRecord) {
                    setCommand(commandRunRecord.command);
                } else {
                    commandOffset -= 1;
                }
            });

        } else if (key == 40) {
            // Down key code
            if (command.length === 0) {
                commandOffset = 0;
            }

            if (commandOffset === 0) {
                $("#command").val("");
                return;
            } else {
                commandOffset -= 1;
            }

            let promise = getCommandByOffset(currentUser, commandOffset);
            promise.done((commandRunRecord) => {
                if (commandRunRecord) {
                    setCommand(commandRunRecord.command);
                } else {
                    commandOffset += 1;
                }
            });
        }
    };

    function getCommandByOffset(username, offset) {
        var data = $.Deferred();

        $.ajax({
            cache: false,
            type: "GET",
            url: "/command/history",
            data: {
                createdBy: username,
                limit: 1,
                offset: offset
            },
            success: function (response) {
                if (response.results) {
                    if (response.results.length === 0) {
                        // handles end of list
                        data.resolve(null);
                    } else {
                        data.resolve(response.results[0]);
                    }
                }
            },
            error: function (xhr, status, e) {
                console.error(`Could not obtain previous command for user ${username} and offset ${offset}`);
                data.resolve(null);
            }
        });

        return data.promise();
    }

</script>
<%- include('footer', {}); %>