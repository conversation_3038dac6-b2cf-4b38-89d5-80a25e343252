[{"name": "customerDetailsCustomerName", "responseKeyMap": ["customerDetails", "customer"], "productTypes": ["All"]}, {"name": "customerDetailsLocation", "responseKeyMap": ["customerDetails", "location"], "productTypes": ["All"]}, {"name": "customerDetailsCIDN", "responseKeyMap": ["customerDetails", "cidn"], "productTypes": ["All"]}, {"name": "customerDetailsCustomerConsent", "responseKeyMap": ["customerDetails", "customerConsent"], "productTypes": ["All"]}, {"name": "productDetailsProductName", "responseKeyMap": ["productDetails", "productName"], "productTypes": ["All"]}, {"name": "productDetailsFnn", "responseKeyMap": ["productDetails", "fnn"], "productTypes": ["All"]}, {"name": "productDetailsServiceType", "responseKeyMap": ["productDetails", "serviceType"], "productTypes": ["All"]}, {"name": "productDetailsServiceSpeed", "responseKeyMap": ["productDetails", "serviceSpeed"], "productTypes": ["ADSL", "BDSL", "IPMAN", "NBN", "NBN EE"]}, {"name": "productDetailsCarriageFnn", "responseKeyMap": ["productDetails", "carriageFNN"], "productTypes": ["ADSL", "BDSL", "FR", "INTERNATIONAL", "IPMAN", "MOBILE", "NBN", "NBN EE", "NextG IPWAN", "Telstra Fibre Adapt"]}, {"name": "productDetailsCarriageType", "responseKeyMap": ["productDetails", "carriageType"], "productTypes": ["All"]}, {"name": "productDetailsAccessType", "responseKeyMap": ["productDetails", "accessType"], "productTypes": ["NBN"]}, {"name": "productDetailsAvc", "responseKeyMap": ["productDetails", "avc"], "productTypes": ["NBN"]}, {"name": "productDetailsIpwanNetwork", "responseKeyMap": ["productDetails", "ipwanNetwork"], "productTypes": ["BDSL", "IPMAN", "NBN", "NBN EE"]}, {"name": "productDetailsIpwanPort", "responseKeyMap": ["productDetails", "ipwanPort"], "productTypes": ["BDSL", "IPMAN", "NBN"]}, {"name": "productDetailsCeIpAddress", "responseKeyMap": ["productDetails", "ceIpAddress"], "productTypes": ["BDSL", "IPMAN", "NBN"]}, {"name": "productDetailsRoutingProtocol", "responseKeyMap": ["productDetails", "routingProtocol"], "productTypes": ["BDSL", "IPMAN", "NBN"]}, {"name": "productDetailsAssociatedAccessService", "responseKeyMap": ["productDetails", "associatedAccessService"], "productTypes": ["NBN EE"]}, {"name": "productDetailsAssociatedAccessServiceFNN", "responseKeyMap": ["productDetails", "associatedAccessServiceFNN"], "productTypes": ["NBN EE"]}, {"name": "productDetailsMediaType", "responseKeyMap": ["productDetails", "mediaType"], "productTypes": ["NBN EE"]}, {"name": "productDetailsBpiId", "responseKeyMap": ["productDetails", "bpiId"], "productTypes": ["NBN EE"]}, {"name": "productDetailsAvcNBNEE", "responseKeyMap": ["productDetails", "avcNBNEE"], "productTypes": ["NBN EE"]}, {"name": "productDetailsInterfaceType", "responseKeyMap": ["productDetails", "interfaceType"], "productTypes": ["NBN EE"]}, {"name": "productDetailsOvcType", "responseKeyMap": ["productDetails", "ovcType"], "productTypes": ["NBN EE"]}, {"name": "productDetailsUniPort", "responseKeyMap": ["productDetails", "uniPort"], "productTypes": ["NBN EE"]}, {"name": "productDetailsCategoryOfService", "responseKeyMap": ["productDetails", "categoryOfService"], "productTypes": ["ADSL", "BDSL", "FR", "INTERNATIONAL", "IPMAN", "MOBILE", "NBN", "NBN EE", "NextG IPWAN", "Telstra Fibre Adapt"]}, {"name": "productDetailsMDNNetworkFnn", "responseKeyMap": ["productDetails", "mdnNetworkFnn"], "productTypes": ["MDN"]}, {"name": "productDetailsMDNService", "responseKeyMap": ["productDetails", "mdnService"], "productTypes": ["MDN"]}, {"name": "productDetailsOVCType", "responseKeyMap": ["productDetails", "ovcType"], "productTypes": ["NBN EE"]}, {"name": "productDetailsPhoneNumberRanges", "responseKeyMap": ["productDetails", "phoneNumberRanges"], "productTypes": ["VOIP"]}, {"name": "deviceDetailsDeviceName", "responseKeyMap": ["deviceDetails", "deviceName"], "productTypes": ["MDN"]}, {"name": "deviceDetailsDeviceType", "responseKeyMap": ["deviceDetails", "deviceType"], "productTypes": ["MDN"]}, {"name": "deviceDetailsSupplierCode", "responseKeyMap": ["deviceDetails", "supplierCode"], "productTypes": ["MDN"]}, {"name": "deviceDetailsProductCode", "responseKeyMap": ["deviceDetails", "productCode"], "productTypes": ["MDN"]}, {"name": "deviceDetailsDeviceSerialNumber", "responseKeyMap": ["deviceDetails", "deviceSerialNumber"], "productTypes": ["MDN"]}, {"name": "deviceDetailsBasementSwitch", "responseKeyMap": ["deviceDetails", "basementSwitch"], "productTypes": ["Basement Switch Device"]}, {"name": "deviceDetailsNTU", "responseKeyMap": ["deviceDetails", "ntu"], "productTypes": ["NTU Device"]}, {"name": "deviceDetailsNTUType", "responseKeyMap": ["deviceDetails", "ntuType"], "productTypes": ["BDSL"]}, {"name": "deviceDetailsDeviceStatusVoip", "responseKeyMap": ["deviceDetails", "deviceStatusVoip"], "productTypes": ["VOIP"]}, {"name": "deviceDetailsRegisterationStatusVoipField", "responseKeyMap": ["deviceDetails", "deviceRegisterationStatusVoip"], "productTypes": ["VOIP"]}, {"name": "deviceDetailsClusterVoipField", "responseKeyMap": ["deviceDetails", "deviceClusterVoip"], "productTypes": ["VOIP"]}, {"name": "deviceDetailsDeviceNameVoip", "responseKeyMap": ["deviceDetails", "deviceNameVoip"], "productTypes": ["VOIP"]}, {"name": "deviceDetailsDeviceVersionVoip", "responseKeyMap": ["deviceDetails", "deviceVersionVoip"], "productTypes": ["VOIP"]}, {"name": "deviceDetailsDeviceIPWorksDomainVoip", "responseKeyMap": ["deviceDetails", "deviceIPWorksDomainVoip"], "productTypes": ["VOIP"]}, {"name": "deviceDetailsDeviceUserFNNVoip", "responseKeyMap": ["deviceDetails", "deviceUserFNNVoip"], "productTypes": ["VOIP"]}, {"name": "deviceDetailsDeviceGroupFNNVoip", "responseKeyMap": ["deviceDetails", "deviceGroupFNNVoip"], "productTypes": ["VOIP"]}, {"name": "deviceDetailsDeviceTrunkFNNVoip", "responseKeyMap": ["deviceDetails", "deviceTrunkFNNVoip"], "productTypes": ["VOIP"]}, {"name": "deviceDetailsDeviceSiteFNNVoip", "responseKeyMap": ["deviceDetails", "deviceSiteFNNVoip"], "productTypes": ["VOIP"]}, {"name": "deviceDetailsDeviceNumberVoip", "responseKeyMap": ["deviceDetails", "deviceNumberVoip"], "productTypes": ["VOIP"]}, {"name": "deviceDetailsDeviceDetailsVoip", "responseKeyMap": ["deviceDetails", "deviceDetailsVoip"], "productTypes": ["VOIP"]}, {"name": "deviceDetailsDeviceRegistrationsVoip", "responseKeyMap": ["deviceDetails", "deviceRegistrationsVoip"], "productTypes": ["VOIP"]}, {"name": "deviceDetailsDeviceTypeSIPNTU", "responseKeyMap": ["deviceDetails", "deviceTypeSIPNTU"], "productTypes": ["VOIP"]}, {"name": "accountStatusRassStatus", "responseKeyMap": ["accountStatus", "rass<PERSON><PERSON><PERSON>"], "productTypes": ["All"]}, {"name": "accountStatusRassCancelledOrders", "responseKeyMap": ["accountStatus", "rassCancelledOrders"], "productTypes": ["All"]}, {"name": "accountStatusRassMiscellaneousOrders", "responseKeyMap": ["accountStatus", "rassModOrders"], "productTypes": ["All"]}, {"name": "outagesPlanned", "responseKeyMap": ["outages", "plannedOutages"], "productTypes": ["All"]}, {"name": "outagesUnplanned", "responseKeyMap": ["outages", "unplannedOutages"], "productTypes": ["All"]}, {"name": "outagesPower", "responseKeyMap": ["outages", "powerOutages"], "productTypes": ["All"]}, {"name": "NBNHealthCheckOutages", "responseKeyMap": ["outages", "nbnOutages"], "productTypes": ["NBN", "NBN EE"]}, {"name": "activeIncidentsSIIAMActiveCases", "responseKeyMap": ["activeIncidents", "siiamActiveCases"], "productTypes": ["All"]}, {"name": "activeIncidentsSIIAMHistoryCases", "responseKeyMap": ["activeIncidents", "siiamHistoryCases"], "productTypes": ["All"]}, {"name": "activeIncidentsServiceCentralIncidents", "responseKeyMap": ["activeIncidents", "serviceCentralIncidents"], "productTypes": ["All"]}, {"name": "activeIncidentsPromiseTasks", "responseKeyMap": ["activeIncidents", "promiseTasks"], "productTypes": ["All"]}]