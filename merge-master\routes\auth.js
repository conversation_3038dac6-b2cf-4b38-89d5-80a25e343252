import express from 'express';
import { query, validationResult } from 'express-validator';
import _ from 'lodash';
import passport from 'passport';

import logger from '../modules/logger.js';
import { calculateSessionExpiry } from '../modules/helpers/date.js';


const router = express.Router();

router.get('/login', [
    query('errorMessage').optional().isString()
], function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    res.render('login', { title: 'Log in to ' + global.gConfig.appName, errorMessage: req.query.errorMessage });
});


router.post('/login', function(req, res, next) {
    passport.authenticate('local', function(error, user) {
        if (error) {
            res.redirect('/auth/login?errorMessage=' + encodeURIComponent(error.message));
            return;
        }

        let redirectUrl = req.session?.redirectUrl || '/';

        req.logIn(user, function(error) {
            if (error) {
                logger.error(`Error with login, ${error.toString()}`);
                res.sendStatus(500);
                return;
            }

            req.session.cookie.expires = calculateSessionExpiry();

            try {
                res.redirect(redirectUrl);
            } catch(error) {
                res.sendStatus(500);
            }
        });
    })(req, res, next);
});


router.get('/logout', function(req, res, next) {
    req.logout((error) => {
        if (error) {
            next(error);
        }

        delete req.session.redirectUrl;
        res.redirect('/auth/login');
    });
});


export default router;
