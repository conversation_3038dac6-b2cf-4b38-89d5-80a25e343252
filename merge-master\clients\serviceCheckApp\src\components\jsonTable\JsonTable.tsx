import { TextStyle } from '@able/react'
import DOMPurify from 'dompurify'
import { useState } from 'react'
import styles from './JsonTable.module.scss'

interface JsonTableProps {
    data: any
}

export const JsonTable = ({ data }: JsonTableProps) => {
    // If data is a string, sanitize and render it.
    if (typeof data === 'string') {
        return (
            <div
                className={styles.sanitizedText}
                dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(data) }}
            />
        )
    }

    // For non-object types or null, just render as text.
    if (typeof data !== 'object' || data === null) {
        return <TextStyle>{String(data)}</TextStyle>
    }

    // Otherwise, render the object as a table.
    return (
        <table className={styles.jsonTable}>
            <thead className={styles.thead}>
                <tr>
                    <th className={styles.tableHeader} >
                        <TextStyle alias='HeadingD'>Key</TextStyle>
                    </th>
                    <th className={styles.tableHeader}>
                        <TextStyle alias='HeadingD'>Content</TextStyle>
                    </th>
                </tr>
            </thead>
            <tbody>
                {Object.entries(data).map(([key, value], index) => (
                    <JsonTableRow
                        key={`${key}_${index}`}
                        label={key}
                        value={value}
                    />
                ))}
            </tbody>
        </table>
    )
}

interface JsonTableRowProps {
    label: string
    value: any
}

const JsonTableRow = ({ label, value }: JsonTableRowProps) => {
    const [expanded, setExpanded] = useState(false)

    // Determine if this row should be expandable:
    // - Objects (and arrays) are expandable.
    // - Strings longer than 250 characters are expandable.
    let isExpandable = false
    if (typeof value === 'object' && value !== null) {
        isExpandable = true
    } else if (typeof value === 'string' && value.length > 250) {
        isExpandable = true
    }

    const toggleExpand = () => {
        setExpanded(!expanded)
    }

    // Render the content based on type.
    let content
    if (typeof value === 'object' && value !== null) {
        content = <JsonTable data={value} />
    } else if (typeof value === 'string') {
        // For long strings, render in a <pre> block.
        if (value.length > 250) {
            content = <pre className={styles.preformatted}>{value}</pre>
        } else {
            content = <TextStyle>{value}</TextStyle>
        }
    } else if (typeof value === 'boolean') {
        content = <TextStyle>{value ? 'true' : 'false'}</TextStyle>
    } else {
        content = <TextStyle>{String(value)}</TextStyle>
    }

    return (
        <tr className={styles.tableRow}>
            <td className={styles.tableCell}>
                {isExpandable ? (
                    <button
                        onClick={toggleExpand}
                        className={styles.expandableLabel}
                    >
                        <TextStyle>{label}</TextStyle>
                        <TextStyle className={styles.expandIcon}>
                            {expanded ? '−' : '+'}
                        </TextStyle>
                    </button>
                ) : (
                    <TextStyle>{label}</TextStyle>
                )}
            </td>
            <td className={styles.tableCell}>
                {isExpandable ? expanded && content : content}
            </td>
        </tr>
    )
}
