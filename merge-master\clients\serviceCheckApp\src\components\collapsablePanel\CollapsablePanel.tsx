import { TextStyle } from '@able/react'
import { useState } from 'react'
import { ChevronIcon } from '../chevronIcon/ChevronIcon'
import { Panel } from '../panel/Panel'
import styles from './CollapsablePanel.module.scss'

interface CollapsablePanelProps {
    headerElement: React.ReactNode
    itemCount?: number
    children: React.ReactNode
    canOpen: boolean
}

export const CollapsablePanel = ({
    headerElement,
    itemCount,
    children,
    canOpen,
}: CollapsablePanelProps) => {
    const [isOpen, setIsOpen] = useState(false)
    return (
        <Panel>
            <div className={styles.collapsablePanel}>
                <button
                    className={styles.header}
                    onClick={() => setIsOpen(!isOpen)}
                >
                    <div>{headerElement}</div>
                    <div className={styles.headerRight}>
                        {itemCount && (
                            <TextStyle>
                                <>{itemCount}</>
                            </TextStyle>
                        )}
                        {canOpen && <ChevronIcon isOpen={isOpen} />}
                    </div>
                </button>
                <div>{isOpen && children}</div>
            </div>
        </Panel>
    )
}
