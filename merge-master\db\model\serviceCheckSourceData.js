import mongoose from 'mongoose';

const serviceCheckSourceDataSchema = new mongoose.Schema({
    serviceCheckId: { type: mongoose.Schema.Types.ObjectId, required: true },
    name: { type: String, required: true },
    data: { type: mongoose.Schema.Types.Mixed, default: null }
});


serviceCheckSourceDataSchema.methods.toJSON = function() {
    let obj = this.toObject();
    delete obj._id;
    delete obj.__v;
    return obj;
};

serviceCheckSourceDataSchema.index({ serviceCheckId: 1, name: 1 }, { unique: true });


export default mongoose.model('serviceCheckSourceData', serviceCheckSourceDataSchema);
