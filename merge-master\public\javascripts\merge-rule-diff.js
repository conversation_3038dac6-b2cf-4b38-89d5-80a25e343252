
$(document).ready(function() {
    let diffEditor = new AceDiff({
        element: "#ruleSourceDiff",
        mode: "ace/mode/json",
        left: {
            content: "",
            editable: false,
            copyLinkEnabled: false
        },
        right: {
            content: "",
            editable: false,
            copyLinkEnabled: false
        }
    });

    $("#rulesDiffGrid").jsGrid({
        width: "100%",
        height: "100%",
        filtering: false,
        inserting: false,
        editing: false,
        sorting: true,
        autoload: true,
        paging: true,
        pageLoading: true,
        pageSize: 50,
        pageIndex: 1,
        editButton: false,
        deleteButton: false,
        controller: {
            loadData: function(filter) {
                var data = $.Deferred();

                let queryData = setupQueryDataFromFilter(filter);
                $.ajax({
                    cache: false,
                    type: "GET",
                    url: "/rules/diff",
                    data: queryData,
                    timeout: 60000,
                    success: function (response) {
                        data.resolve({
                            data: response.results,
                            itemsCount: response.metadata.pagination.total
                        });

                        if($(`#deployRules`).length > 0 && response.metadata.pagination.total > 0 && !disableRuleSourceEditing) {
                            $(`#deployRules`).prop("disabled", false);
                            $("#diffRuleDiv").css("visibility", "visible");
                            changeAlertBox("", false, true);
                        }

                        $("#displayDiffRulesTotal").text(`${response.metadata.pagination.total} total`);
                    },
                    error: function (xhr, status, e) {
                        if(changeAlertBox) changeAlertBox("Error in fetching source diff");
                        // Not the best solution, puts nothing in the table
                        console.error(e);

                        data.resolve({
                            data: [],
                            itemsCount: 0
                        });
                    },
                    complete: function() {
                        setButtonSpinner($("#rulesDiffRefresh"), $("<span>").addClass("fas fa-sync"), false);
                    }
                });

                return data.promise();
            }
        },
        fields:  [
            { name: "name", title: "Name", type: "text", itemTemplate: function(value) {
                return $("<a>").attr("href", `/edit/rules/${encodeURIComponent(value)}`).attr("target", "blank").text(value);
            }},
            { title: "View diff", type: "text", align: "center", width: "2em", sorting: false, filtering: false, itemTemplate: function(value, item) {
                return $("<button>").addClass("btn btn-secondary").html($("<span>").addClass("fas fa-external-link-alt")).click(function() {
                    let editors = diffEditor.getEditors();

                    let activeRule;
                    let configRule;

                    if (item.active) {
                        activeRule = JSON.stringify(sortObjectKeys(item.active), null, 4);
                    } else {
                        activeRule = `Rule ${item.name} could not be found in database`;
                    }

                    if (item.config) {
                        configRule = JSON.stringify(sortObjectKeys(item.config), null, 4);
                    } else {
                        configRule = `Rule ${item.name} could not be found in the config file`;
                    }

                    editors.left.setValue(activeRule);
                    editors.left.clearSelection();
                    editors.right.setValue(configRule);
                    editors.right.clearSelection();

                    $("#diffModalTitle").text(`Rule ${item.name} diff`);
                    $("#diffModal").modal("show");
                });
            }}
        ]
    });

    $("#sourcesDiffGrid").jsGrid({
        width: "100%",
        height: "100%",
        filtering: false,
        inserting: false,
        editing: false,
        sorting: true,
        autoload: true,
        paging: true,
        pageLoading: true,
        pageSize: 50,
        pageIndex: 1,
        editButton: false,
        deleteButton: false,
        controller: {
            loadData: function(filter) {
                var data = $.Deferred();

                let queryData = setupQueryDataFromFilter(filter);
                $.ajax({
                    cache: false,
                    type: "GET",
                    url: "/sources/diff",
                    data: queryData,
                    timeout: 60000,
                    success: function (response) {
                        data.resolve({
                            data: response.results,
                            itemsCount: response.metadata.pagination.total
                        });

                        if(typeof deployScript !== "undefined" && response.metadata.pagination.total > 0 && !disableRuleSourceEditing) {
                            $(`#deployRules`).prop("disabled", false);
                            $("#diffSourceDiv").css("visibility", "visible");
                            changeAlertBox("", false, true);
                        }

                        $("#displayDiffSourcesTotal").text(`${response.metadata.pagination.total} total`);
                    },
                    error: function (xhr, status, e) {
                        if(changeAlertBox) changeAlertBox("Error in fetching source diff");
                        // Not the best solution, puts nothing in the table
                        console.error(e);

                        data.resolve({
                            data: [],
                            itemsCount: 0
                        });
                    },
                    complete: function() {
                        setButtonSpinner($("#sourcesDiffRefresh"), $("<span>").addClass("fas fa-sync"), false);
                    }
                });

                return data.promise();
            }
        },
        fields:  [
            { name: "name", title: "Name", type: "text", width: "8em", itemTemplate: function(value) {
                return $("<a>").attr("href", `/edit/sources/${encodeURIComponent(value)}`).attr("target", "blank").text(value);
            }},
            { title: "View diff", type: "text", align: "center", width: "2em", sorting: false, filtering: false, itemTemplate: function(value, item) {
                return $("<button>").addClass("btn btn-secondary").html($("<span>").addClass("fas fa-external-link-alt")).click(function() {
                    let editors = diffEditor.getEditors();

                    let activeSource;
                    let configSource;

                    if (item.active) {
                        activeSource = JSON.stringify(sortObjectKeys(item.active), null, 4);
                    } else {
                        activeSource = `Source ${item.name} could not be found in database`;
                    }

                    if (item.config) {
                        configSource = JSON.stringify(sortObjectKeys(item.config), null, 4);
                    } else {
                        configSource = `Source ${item.name} could not be found in the config file`;
                    }

                    editors.left.setValue(activeSource);
                    editors.left.clearSelection();
                    editors.right.setValue(configSource);
                    editors.right.clearSelection();

                    $("#diffModalTitle").text(`Source ${item.name} diff`);
                    $("#diffModal").modal("show");
                });
            }}
        ]
    });

    $("#rulesDiffRefresh").click(function() {
        setButtonSpinner($("#rulesDiffRefresh"), $("<span>").addClass("fas fa-sync"), true);
        $("#rulesDiffGrid").jsGrid("loadData");
    });

    $("#sourcesDiffRefresh").click(function() {
        setButtonSpinner($("#sourcesDiffRefresh"), $("<span>").addClass("fas fa-sync"), true);
        $("#sourcesDiffGrid").jsGrid("loadData");
    });
});

// Helper function that returns an object where the keys are sorted
function sortObjectKeys(data) {
    return Object.keys(data).sort().reduce(
        (obj, key) => {
            if (typeof data[key] === "object" && data[key] != null) {
                // Recursively sorts keys for nested objects
                obj[key] = sortObjectKeys(data[key]);
            } else {
                obj[key] = data[key];
            }
            return obj;
        },
        {}
    );
}

function setupQueryDataFromFilter(filter) {
    let limit = filter.pageSize;
    let offset = (filter.pageIndex - 1)*filter.pageSize;

    let queryData = {};

    if (filter.sortField) {
        queryData.order = filter.sortOrder;
    }

    queryData.limit = limit;
    queryData.offset = offset;

    return queryData;
}