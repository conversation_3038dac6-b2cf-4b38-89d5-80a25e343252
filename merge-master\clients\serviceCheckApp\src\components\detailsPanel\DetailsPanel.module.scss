@use '@able/web/src/index' as able;

.panelRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.detailsPanel {
  width: 100%; 
  height: 100%;   
}

.detailRow {
  &.rightAlign {
    display: flex;
    justify-content: right;
    gap: 1rem;
    align-items: center;
  }
  
  &.inline {
      display: grid;
      grid-template-columns:  1fr auto; // Left column takes available space, right column adjusts to content
      justify-content: space-between; // Ensure proper spacing between the columns
      align-items: center; // Vertically align content
      gap: 1rem;
      // Ensure the right-side text starts from the very right
      > :nth-child(2) {
         text-align: right;
      }
  }
}

.header {
  position: sticky;
  top: 0;
  background-color: able.color(materialBaseTertiary);
  padding: able.spacing(spacing2x);
  z-index: 1;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  display: flex; 
  justify-content: space-between;
  align-items: center;
  min-height: 72px;


  .panelElements {
    display: flex;
    gap: 1rem;
    align-items: center
  }
}

.content {
    padding: able.spacing(spacing2x);
    display: flex; 
    flex-direction: column;
    gap: able.spacing(spacing1x); 
    height: 100%;   
    min-height:150px;
    max-height: 400px;
    overscroll-behavior: contain;
    
    &.fitContent {
      max-height: fit-content;
    }
    overflow-x: auto;
    overflow-y: auto;

    /* Webkit Browsers (Chrome, Edge, Safari) */
    &::-webkit-scrollbar {
      width: 10px; 
      height: 10px; 
    }
  
    &::-webkit-scrollbar-thumb {
      background-color: able.color(materialBaseBrandQuaternary); /* Scrollbar thumb color */
      border-radius: 6px; 
    }

    &::-webkit-scrollbar-track {
      background-color: able.color(materialBaseSecondary); /* Track color */
    }
  
    /* Firefox */
    scrollbar-width: thin; 
    scrollbar-color: able.color(materialBaseBrandTertiary) able.color(materialBaseSecondary); /* Thumb and track colors */
  }
  
  