
export const RuleResult = Object.freeze({
    ok: 'OK',
    failed: 'Failed',
    error: 'Error',
    reject: 'Reject',
    warning: 'Warning',
    actionable: 'Actionable',
    actioned: 'Actioned'
});

export const RuleStatus = Object.freeze({
    codeError: 'codeError',
    done: 'done',
    running: 'running'
});

export const SourceStatus = Object.freeze({
    apiUriError: 'API URI error',
    collected: 'Collected',
    error: 'error',
    parameterError: 'Parameter Error',
    preConditionError: 'Pre-Condition Error',
    preConditionFalse: 'Pre-Condition False',
    running: 'running'
});

export const ServiceCheckStatus = Object.freeze({
    abortedInitial: 'abortedInitial',
    completedWithError: 'completedWithError',
    completedWithErrorPartial: 'completedWithErrorPartial',
    done: 'done',
    donePartial: 'donePartial',
    error: 'error',
    running: 'running'
});

export const ServiceCheckStartMethod = Object.freeze({
    api: 'API',
    userInterface: 'UI',
    userInterfaceClassic: 'UI Classic'
});


export const CompareResult = Object.freeze({
    equal: 0,
    notEqual: 1,
    ruleDataMissing: 2,
    compareError: 3
});


export const AuthorizationRoles = Object.freeze({
    readOnly: 'readOnly',
    level0: 'level0',
    level1: 'level1',
    level2: 'level2',
    level3: 'level3',
    apiAccess: 'apiAccess',
    isAdmin: 'isAdmin',
    isDeveloper: 'isDeveloper',
    levelLead: 'levelLead',
    commandAccess: 'commandAccess',
    offshoreAccess: 'offshoreAccess',
    sourceAPIAccess: 'sourceAPIAccess'
});


export const ValidateFnnCheckStatus = Object.freeze({
    running: 'running',
    done: 'done',
    error: 'error'
});

export const InputTypes = Object.freeze({
    fnn: 'FNN',
    phoneNumber: 'Phone Number',
    mobilePhoneNumber: 'Mobile Phone Number',
    deviceName: 'Device Name',
    internationalLink: 'International Link',
    avc: 'AVC ID',
    ovc: 'OVC ID',
    bdslFnn: 'BDSL FNN',
    uuid: 'UUID',
    domId: 'DOM ID',
    cfsId: 'CFS ID',
    imsi: 'IMSI',
    other: 'Other'
});

export const ProductTypes = Object.freeze({
    ADSL: 'ADSL',
    BASEMENT_SWITCH_DEVICE: 'Basement Switch Device',
    BDSL: 'BDSL',
    FR: 'FR',
    INTERNATIONAL: 'INTERNATIONAL',
    IPMAN: 'IPMAN',
    MDN: 'MDN',
    MOBILE: 'MOBILE',
    NBN: 'NBN',
    NBN_EE: 'NBN EE',
    NBN_FTTB: 'NBN FTTB',
    NBN_FTTC: 'NBN FTTC',
    NBN_FTTN: 'NBN FTTN',
    NBN_FTTP: 'NBN FTTP',
    NBN_HFC: 'NBN HFC',
    NBN_WIRELESS: 'NBN Wireless',
    NEXTG_IPWAN: 'NextG IPWAN',
    NTU_DEVICE: 'NTU Device',
    TELSTRA_FIBRE_ADAPT: 'Telstra Fibre Adapt',
    VOIP: 'VOIP'
});
