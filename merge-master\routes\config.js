'use strict';

import axios from 'axios';
import express from 'express';
import { query, param, body, validationResult } from 'express-validator';
import _ from 'lodash';
import tunnel from 'tunnel';

import auth from '../modules/auth.js';
import logger from '../modules/logger.js';
import { conventionalCommit } from '../modules/helpers/commit.js';
import { MERGE_OBJECT_CONFIG_FILES } from '../modules/helpers/constants.js';
import { AuthorizationRoles } from '../modules/enumerations.js';
import pagination from '../modules/pagination.js';
import mergeConfigList from '../modules/mergeConfigList.js';
import mergeRule from '../modules/mergeRule.js';
import ApiMetadata from '../db/model/apiMetadata.js';
import MessageBucketMetadata from '../db/model/messageBucketMetadata.js';
import OutcomeMetadata from '../db/model/outcomeMetadata.js';
import RuleMetadata from '../db/model/ruleMetadata.js';
import TextTemplateMetadata from '../db/model/textTemplateMetadata.js';
import Api from '../db/model/api.js';
import MessageBucket from '../db/model/messageBucket.js';
import Outcome from '../db/model/outcome.js';
import Rule from '../db/model/rule.js';
import Source from '../db/model/source.js';
import Template from '../db/model/template.js';
import { DependencyGraph } from '../modules/dependencyGraph.js';

const router = express.Router();


// Proxy to use for HTTP requests to GitLab API
const axiosHttpsAgent = tunnel.httpsOverHttp({
    proxy: {
        host: global.gConfig.proxyHost,
        port: global.gConfig.proxyPort,
        proxyAuth: `${global.gConfig.robotAccount01Username}:${global.gConfig.robotAccount01Password}`
    }
});


router.get('/upload', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], true), async function(req, res) {
    res.render('configUpload', { username: req.user.username, user: req.user });
});


router.get('/download/:configName', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), [
    param('configName').isString().isIn(['api', 'rulesource', 'template', 'outcome', 'messagebucket']).withMessage('Config name must be one of: "api", "rulesource", "template", "outcome", "messagebucket"')
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let configName = req.params.configName;

    try {
        let attachmentNamePrefix;
        let configData;

        switch(configName) {
            case "api":
                // APIs should only be accessible by admin users
                if (req.user && req.user.isAdmin) {
                    configData = await mergeConfigList.getObjectsAndMetadataFromDatabase([Api], ["apis"], ApiMetadata);
                    attachmentNamePrefix = "APIs";
                } else {
                    res.sendStatus(403);
                    return;
                }
                break;
            case "rulesource":
                configData = await mergeConfigList.getObjectsAndMetadataFromDatabase([Rule, Source], ["rules", "sources"], RuleMetadata);
                attachmentNamePrefix = "SCRules";
                break;
            case "template":
                configData = await mergeConfigList.getObjectsAndMetadataFromDatabase([Template], ["textTemplates"], TextTemplateMetadata);
                attachmentNamePrefix = "textTemplates";
                break;
            case "outcome":
                configData = await mergeConfigList.getObjectsAndMetadataFromDatabase([Outcome], ["outcomes"], OutcomeMetadata);
                attachmentNamePrefix = "outcomes";
                break;
            case "messagebucket":
                configData = await mergeConfigList.getObjectsAndMetadataFromDatabase([MessageBucket], ["messageBuckets"], MessageBucketMetadata);
                attachmentNamePrefix = "messageBuckets";
                break;
        }
        let dateStr = new Date().toISOString().replace(/[^0-9]/g, '');
        let attachmentName = `Merge_${attachmentNamePrefix}_${global.gConfig.env}-v${global.gConfig.ver}-R${configData.version}_${dateStr}.json`;

        res.set("Content-Disposition", `attachment;filename=${attachmentName}`);
        res.send(JSON.stringify(configData, null, 4));
    } catch (error) {
        res.status(500).render('error', { error: error.toString() });
    }
});


router.get('/graph', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), function(req, res) {
    res.render('rulesSourcesGraph', { user: req.user });
});


router.get('/graphStatus', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], true), async function(req, res, next) {
    try {
        res.render('rulesSourcesGraphStatus', { user: req.user });
    } catch(error) {
        next(error);
    }
});


router.get('/differences', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], true), function(req, res) {
    res.render('configDiff', { user: req.user });
});


/**
 * Gets the differences between a config file for APIs / SCRules / textTemplates and the active instances in the database
 */
router.get('/diff', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], false), [
    query('instanceName').isString().isIn(['api', 'rule', 'source', 'outcome', 'template', 'messagebucket']).withMessage('Config name must be one of: "api", "rule", "source", "outcome", "template", "messagebucket"'),
    query('limit').optional().isInt({ min: 1 }).toInt(),
    query('offset').optional().isInt({ min: 0 }).toInt(),
    query('order').optional().isString().isIn(['asc', 'desc'])
        .withMessage('Order must be either "asc" or "desc"'),
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let instanceName = req.query.instanceName;
    let limit = (req.query.limit === undefined) ? 100: req.query.limit;
    let offset = (req.query.offset === undefined) ? 0: req.query.offset;
    let orderBy = (req.query.order === undefined) ? 'asc' : req.query.order;
    let orderByParam = (orderBy === 'asc') ? 1 : -1;

    let Model;
    let configList;

    try {
        switch (instanceName) {
            case "api":
                Model = Api;
                configList = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.api)).apis;
                break;
            case "rule":
                Model = Rule;
                configList = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.ruleSource)).rules;
                break;
            case "source":
                Model = Source;
                configList = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.ruleSource)).sources;
                break;
            case "outcome":
                Model = Outcome;
                configList = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.outcome)).outcomes;
                break;
            case "template":
                Model = Template;
                configList = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.textTemplate)).textTemplates;
                break;
            case "messagebucket":
                Model = MessageBucket;
                configList = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.messageBucket)).messageBuckets;
                break;
        }

        let instances = await Model.find({}).select(['-__v', '-_id', '-createdBy', '-createdOn']);

        let instancesConfig = Object.assign({}, ...configList.map(instance => ({ [instance.name]: instance })));
        let diffs = [];

        // Goes through instances in the database and
        // if any differ from config, add to the diff array
        for (let i = 0; i < instances.length; i++) {
            let currInstanceActive = instances[i];
            let currInstanceConfig = _.get(instancesConfig, [currInstanceActive.name], null);
            let currInstanceConfigObject = null;

            // Creates a Mongoose instance, as default values are sometimes not specified in the instances config
            // Assigns the object version of the instance back to populate the current config with any default values
            // may have been present as part of the schema, but not in the config file
            if (currInstanceConfig) {
                currInstanceConfigObject = _.omit(new Model(currInstanceConfig).toJSON(), ['createdBy', 'createdOn']);
                Object.assign(currInstanceConfig, currInstanceConfigObject);
            }

            if (!_.isEqual(currInstanceActive.toJSON(), currInstanceConfig)) {
                diffs.push({
                    name: currInstanceActive.name,
                    active: currInstanceActive,
                    config: currInstanceConfig
                });
            }

            // Removes instance from object storing instances from config file
            if (currInstanceConfig) {
                delete instancesConfig[currInstanceActive.name];
            }
        }

        // Adds any instances that are still present in the instancesConfig to results
        for (let instanceName in instancesConfig) {
            diffs.push({
                name: instanceName,
                active: null,
                config: instancesConfig[instanceName]
            });
        }

        let count = diffs.length;
        let diffSortFunction;

        // Determines sort function for results
        if (orderByParam === 1) {
            diffSortFunction = (result1, result2) => {
                if (result1.name > result2.name) {
                    return 1;
                }
                if (result1.name < result2.name) {
                    return -1;
                }
                return 0;
            };
        } else if (orderByParam === -1) {
            diffSortFunction = (result1, result2) => {
                if (result1.name > result2.name) {
                    return -1;
                }
                if (result1.name < result2.name) {
                    return 1;
                }
                return 0;
            };
        }

        diffs.sort(diffSortFunction);

        // Applies limit and offset to result array
        let results = diffs.slice(offset, offset + limit);
        let paginationMetadata = pagination.createPaginationMetadata(req, limit, offset, count);

        let response = {
            metadata: {
                pagination: paginationMetadata,
            },
            results: results
        };

        res.send(response);
    } catch(error) {
        logger.error(`Get diff error, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.post('/metadata', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], false), [
    body('configName').isString().isIn(['api', 'rulesource', 'template', 'outcome', 'messagebucket']).withMessage('Config name must be one of: "api", "rulesource", "template", "outcome", "messagebucket"'),
    body('version').isInt({ min: 1 }).toInt()
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let configName = req.body.configName;
    let version = req.body.version;

    let MetadataModel;

    try {
        switch(configName) {
            case "api":
                MetadataModel = ApiMetadata;
                break;
            case "rulesource":
                MetadataModel = RuleMetadata;
                break;
            case "template":
                MetadataModel = TextTemplateMetadata;
                break;
            case "outcome":
                MetadataModel = OutcomeMetadata;
                break;
            case "messagebucket":
                MetadataModel = MessageBucketMetadata;
                break;
        }

        await MetadataModel.findOneAndUpdate({}, { $set: {
            version: version,
            updatedBy: req.user.username,
            updatedOn: new Date()
        }}, {
            upsert: true,
            strict: "throw",
            runValidators: true,
            setDefaultsOnInsert: true,
            useFindAndModify: false
        });

        res.sendStatus(200);
    } catch (error) {
        res.sendStatus(500);
    }
});


router.post('/deploy', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], false), [
    body('commitMessage').isString().isLength({ min: 1, max: 100 }),
    body('configName').isString().isIn(['api', 'rulesource', 'outcome','template']).withMessage('Config name must be one of: "api", "rulesource", "outcome", "template"')
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let configName = req.body.configName;

    try {
        let configData;
        let metadataModel;
        let gitlabCommitUri;
        let gitlabCommitHistoryUri = `${global.gConfig.gitAPIUri}/repository/commits`;
        let currDate = new Date();

        let formattedCommitMessage = await conventionalCommit('refactor', configName, req.body.commitMessage);

        let payload = {
            branch: global.gConfig.gitConfigBranch,
            author_email: global.gConfig.gitUserEmail,
            author_name: global.gConfig.gitUserName,
            commit_message: formattedCommitMessage,
            encoding: "base64"
        };

        switch (configName) {
            case "api":
                configData = await mergeConfigList.getObjectsAndMetadataFromDatabase([Api], ["apis"], ApiMetadata);
                metadataModel = ApiMetadata;
                gitlabCommitUri = `${global.gConfig.gitAPIUri}/${global.gConfig.gitAPIConfigUri}`;
                break;
            case "rulesource":
                configData = await mergeConfigList.getObjectsAndMetadataFromDatabase([Rule, Source], ["rules", "sources"], RuleMetadata);
                metadataModel = RuleMetadata;
                gitlabCommitUri = `${global.gConfig.gitAPIUri}/${global.gConfig.gitConfigUri}`;
                break;
            case "outcome":
                configData = await mergeConfigList.getObjectsAndMetadataFromDatabase([Outcome], ["outcomes"], OutcomeMetadata);
                metadataModel = OutcomeMetadata;
                gitlabCommitUri = `${global.gConfig.gitAPIUri}/${global.gConfig.gitOutcomeConfigUri}`;
                break;
            case "template":
                configData = await mergeConfigList.getObjectsAndMetadataFromDatabase([Template], ["textTemplates"], TextTemplateMetadata);
                metadataModel = TextTemplateMetadata;
                gitlabCommitUri = `${global.gConfig.gitAPIUri}/${global.gConfig.gitTemplateConfigUri}`;
                break;
        }

        configData.version = configData.version ? configData.version + 1 : 1;
        configData.updatedBy = req.user.username;
        configData.updatedOn = currDate;

        try {
            payload.content = Buffer.from(JSON.stringify(configData, null, 4)).toString('base64');

            await axios.put(gitlabCommitUri, payload, {
                headers: {
                    "PRIVATE-TOKEN": global.gConfig.gitlabToken
                },
                httpsAgent: axiosHttpsAgent,
                proxy: false,
                timeout: 120000
            });
        } catch (error) {
            // Needs more investigation, not an ideal solution
            // The GitLab API appears to respond with HTTP 400 on a large payload with the response
            // { message: '9:Could not update refs/heads/edit-master-sources. Please refresh and try again..' }
            // This is present if the JSON for the config is large, in this case do not throw an exception
            // as the commit still appears to go through
            if (!(error && error.response && error.response.status === 400 &&
                error.response.data && error.response.data.message &&
                error.response.data.message.match(/Could not update refs/))) {
                throw error;
            }
        }

        // Updates version in metadata document
        await metadataModel.findOneAndUpdate({}, { $set: {
            version: configData.version,
            updatedBy: req.user.username,
            updatedOn: currDate
        }}, {
            upsert: true,
            strict: "throw",
            runValidators: true,
            setDefaultsOnInsert: true,
            useFindAndModify: false
        });

        // Manually retrieves the commit ID as it may not be returned in the
        // HTTP PUT response due to the above issue
        let response = await axios.get(gitlabCommitHistoryUri + "?ref_name=" + global.gConfig.gitConfigBranch, {
            params: {
                ref_name: global.gConfig.gitConfigBranch
            },
            headers: {
                "PRIVATE-TOKEN": global.gConfig.gitlabToken
            },
            httpsAgent: axiosHttpsAgent,
            proxy: false,
            timeout: 10000
        });

        let lastCommitId = _.get(response, ["data", 0, "id"], null);

        res.send({
            id: lastCommitId,
            version: configData.version
        });
    } catch (error) {
        logger.error(`Could not commit config for ${configName} to gitlab, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.get('/deploy/pipelineId', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], false), [
    query('commitId').isString().withMessage('Commit ID must be specified')
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let commitId = req.query.commitId;

    try {
        let pipelineUri = `${global.gConfig.gitAPIUri}/pipelines`;

        let response = await axios.get(pipelineUri, {
            headers: {
                "PRIVATE-TOKEN": global.gConfig.gitlabToken
            },
            params: {
                sha: commitId
            },
            httpsAgent: axiosHttpsAgent,
            proxy: false,
            timeout: 10000
        });

        let pipelineId = _.get(response, ["data", 0, "id"], null);
        let pipelineUrl = _.get(response, ["data", 0, "web_url"], null);

        if (pipelineId) {
            res.send({
                id: pipelineId,
                url: pipelineUrl
            });
        } else {
            res.sendStatus(404);
        }
    } catch(error) {
        logger.error(`Could not get pipeline ID for commit ${commitId}, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.post('/deploy/pipelineStartJob', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], false), [
    body('pipelineId').isInt({ min: 0 }).toInt().withMessage('Pipeline ID must be specified')
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let pipelineId = req.body.pipelineId;

    try {
        let jobsUri = `${global.gConfig.gitAPIUri}/pipelines/${pipelineId}/jobs?scope=manual`;
        let response = await axios.get(jobsUri, {
            headers: {
                "PRIVATE-TOKEN": global.gConfig.gitlabToken
            },
            httpsAgent: axiosHttpsAgent,
            proxy: false,
            timeout: 10000
        });

        if (Array.isArray(response.data) && response.data.length) {
            let jobId = response.data[0].id;
            let startJobUri = `${global.gConfig.gitAPIUri}/jobs/${jobId}/play`;

            await axios.post(startJobUri, {}, {
                headers: {
                    "PRIVATE-TOKEN": global.gConfig.gitlabToken
                },
                httpsAgent: axiosHttpsAgent,
                proxy: false,
                timeout: 10000
            });

            res.sendStatus(200);
        } else {
            // Occurs when there are no manual jobs in the pipeline
            res.sendStatus(422);
        }
    } catch (error) {
        logger.error(`Could not start manual jobs for pipeline ID ${pipelineId} from gitlab, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.get('/deploy/pipelines', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], false), [
    query('pipelineId').isInt({ min: 0 }).toInt().withMessage('Pipeline ID must be specified')
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let pipelineId = req.query.pipelineId;

    try {
        let pipelineUri = `${global.gConfig.gitAPIUri}/pipelines/${pipelineId}`;
        let response = await axios.get(pipelineUri, {
            headers: {
                "PRIVATE-TOKEN": global.gConfig.gitlabToken
            },
            httpsAgent: axiosHttpsAgent,
            proxy: false,
            timeout: 10000
        });

        // Just sends the response from the GitLab API for current pipeline
        res.send(response.data);
    } catch (error) {
        logger.error(`Could not obtain pipeline data for pipeline ID ${pipelineId} from gitlab, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.get('/deploy/pipelineJobs', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], false), [
    query('pipelineId').isInt({ min: 0 }).toInt().withMessage('Pipeline ID must be specified')
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let pipelineId = req.query.pipelineId;

    try {
        let jobsUri = `${global.gConfig.gitAPIUri}/pipelines/${pipelineId}/jobs`;
        let response = await axios.get(jobsUri, {
            headers: {
                "PRIVATE-TOKEN": global.gConfig.gitlabToken
            },
            httpsAgent: axiosHttpsAgent,
            proxy: false,
            timeout: 10000
        });

        // Just sends the response from the GitLab API for jobs
        res.send(response.data);
    } catch (error) {
        logger.error(`Could not get jobs for pipeline ID ${pipelineId} from gitlab, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.get('/deploy/pipelineRunning', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], false), async function(req, res) {
    try {
        // Retrieves CI/CD pipelines for the environment's branch with scope running or pending
        let runningPipelinesUri = `${global.gConfig.gitAPIUri}/pipelines?ref_name=${global.gConfig.gitConfigBranch}&scope=running`;
        let pendingPipelinesUri = `${global.gConfig.gitAPIUri}/pipelines?ref_name=${global.gConfig.gitConfigBranch}&scope=pending`;

        let [runningPipelines, pendingPipelines] = await Promise.all([
            axios.get(runningPipelinesUri, {
                headers: {
                    "PRIVATE-TOKEN": global.gConfig.gitlabToken
                },
                httpsAgent: axiosHttpsAgent,
                proxy: false,
                timeout: 10000
            }),
            axios.get(pendingPipelinesUri, {
                headers: {
                    "PRIVATE-TOKEN": global.gConfig.gitlabToken
                },
                httpsAgent: axiosHttpsAgent,
                proxy: false,
                timeout: 10000
            }),
        ]);

        let runningAndPendingPipelines = parseInt(_.get(runningPipelines, ['headers', 'x-total'], 0)) + parseInt(_.get(pendingPipelines, ['headers', 'x-total'], 0));

        res.send({
            pipelineRunningCount: runningAndPendingPipelines
        });
    } catch (error) {
        logger.error(`Could not get running / pending pipeline count from gitlab, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.get('/graphIsAcyclic', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], false), async function(req, res) {
    try {
        const [rules, sources] = await Promise.all([mergeRule.getRules(), mergeRule.getSources()]);
        const dependencyGraph = new DependencyGraph(rules, sources);

        res.send({
            isAcyclic: dependencyGraph.isAcyclic()
        });
    } catch(error) {
        res.sendStatus(500);
    }
});


router.get('/graphDisableDisplayConditionRuleStatus', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], false), async function(req, res) {
    try {
        const [rules, sources] = await Promise.all([mergeRule.getRules(), mergeRule.getSources()]);

        const dependencyGraph = new DependencyGraph(rules, sources);
        const status = {
            rules: [],
            sources: []
        };

        let displayDisplayConditionRule = rules.find((rule) => rule.name === global.gConfig.disableDisplayConditionRuleName);

        for (let rule of rules) {
            if (rule === displayDisplayConditionRule) {
                continue;
            }

            let isDependent = dependencyGraph.findIfVertexIsDependency(rule, displayDisplayConditionRule);

            status.rules.push({
                name: rule.name,
                isDependent: isDependent
            });
        }

        for (let source of sources) {
            let isDependent = dependencyGraph.findIfVertexIsDependency(source, displayDisplayConditionRule);

            status.sources.push({
                name: source.name,
                isDependent: isDependent
            });
        }

        res.send(status);
    } catch(error) {
        res.sendStatus(500);
    }
});


export default router;
