'use strict';

import chai from 'chai';
import fs from 'fs';
import _ from 'lodash';
import path, { dirname } from 'path';
import { fileURLToPath } from 'url';

import Rule from '../../db/model/rule.js';
import mergeRule from '../../modules/mergeRule.js';

const should = chai.should();

const __dirname = dirname(fileURLToPath(import.meta.url));

const SCRules = JSON.parse(await fs.promises.readFile(path.join(__dirname, '../../config/SCRules.json')));


describe('Rule unit tests', () => {
    let rules = SCRules.rules;

    rules.forEach(function(rule) {
        it(`Rule ${rule.name}: run unit tests`, async() => {
            let ruleInstance = new Rule(rule);

            let unitTestResult = await mergeRule.runUnitTests(ruleInstance);
            let failedTests = [];

            chai.expect(unitTestResult).to.be.an('array');
            unitTestResult.forEach(function(result) {
                chai.expect(result).to.be.an('object');

                if (result.status != 'pass') {
                    failedTests.push(result);
                }
            });

            if (failedTests.length) {
                chai.assert.fail(`Rule ${rule.name} has failed unit tests: ${failedTests.map(result => { return result.name; }).join()}`);
            }
        });
    });
});

