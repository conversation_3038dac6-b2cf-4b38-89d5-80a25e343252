# Unit testing
## Overview
This is a simple unit test suite that should be expanded upon to ensure that future functions / HTTP endpoints that are written for the Merge application work as intended and also so the behaviour of the code is recorded here as well.

## Test files
Javascript files in this directory that start with `test_` contain the Mocha BDD style tests that are run, other files are modules that setup the tests such as the MongoDB memory server for HTTP API endpoint testing and a module to override the config for Merge.

## Setup
To install the dependencies require for unit testing, run `npm install --save-dev` from the base of the repository. Then run `node tests/init_memory_server.js` to download the binaries for MongoDB. This step will only need to be run once, so long as you do not remove the local `node_modules` directory, the script should install the MongoDB binaries and exit. This step will need to be performed as tests will timeout upon initiating the memory based MongoDB instance as it will attempt to download MongoDB if it is not initialized beforehand.

After that, you may run all tests with this command (from the base directory of the repository), `npm test -- tests/*/*.js`.
To run a single test file, you can just substitute the last argument with a single test file, for example `npm test -- tests/http_api/test_stats.js`.
