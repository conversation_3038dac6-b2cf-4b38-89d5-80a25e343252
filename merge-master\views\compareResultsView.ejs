<%- include('header', {}); %>
<%- include('jsonEditInclude', {}) %>
<%- include('menu' , {currentTab: 'Form' }); %>
<script src="/public/javascripts/merge_SC_lib.js" crossorigin="anonymous"></script>
<div class="container">
    <br>
    <div class="jumbotron py-2">
        <div class="form-group container">
            <div class="row">
                <div class="col-sm-12" style="text-align:center">
                    <h5 id="compareResultsTitle" class="display-12">
                    </h5>
                </div>
            </div>
        </div>
    </div>

    <div id="Results">
        <div class='text-left border rounded'>
            <div class="card">
                <div class="card-header">
                    <span class="h4">Differences</span>
                    <span id="Differences-status" class=""></span>
                    <a class="btn expand" data-toggle="collapse" data-target="#Differences-table"><span style=" color:blue" class="fas fa-minus-square" title="Expand/Collapse"></span></a>
                    <span style="float:right; font-size:80%">* This section will only display all the differences between the Service Check records</span>
                </div>
                <div id="Differences-table" class="card-body text-left text-monospace border rounded collapse show">
                    <div id="Differences-none-alert" class="alert alert-info"  style="display:none;">
                        All the service check rules data are identical, no differences found.
                    </div>
                    <table class="table table-hover pad-rem25">
                        <thead id="Differences-tableHeader"></thead>
                        <tbody id="Differences-tableBody"></tbody>
                    </table>
                </div>
            </div>
            <div class="card">
                <div class="card-header">
                    <span class="h4">All Rules</span>
                    <span id="AllRules-status" class=""></span>
                    <a class="btn expand" data-toggle="collapse" data-target="#AllRules-table"><span style=" color:blue" class="fas fa-minus-square" title="Expand/Collapse"></span></a>
                    <span style="float:right; font-size:80%"></span>
                </div>
                <div id="AllRules-table" class="card-body text-left text-monospace border rounded collapse show">
                    <table class="table table-hover pad-rem25">
                        <thead id="AllRules-tableHeader"></thead>
                        <tbody id="AllRules-tableBody"></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<br>

<script>
    const serviceCheckRecord1 = Object.freeze(JSON.parse(atob("<%- serviceCheckRecord1 %>")));
    const serviceCheckRecord2 = Object.freeze(JSON.parse(atob("<%- serviceCheckRecord2 %>")));
    const serviceCheckRecord3 = Object.freeze(JSON.parse(atob("<%- serviceCheckRecord3 %>")));

    const serviceCheckAllRules = Object.freeze(JSON.parse(atob("<%- serviceCheckAllRules %>")));
    const serviceCheckDifferentRules = Object.freeze(JSON.parse(atob("<%- serviceCheckDifferentRules %>")));

    const CompareResult = Object.freeze(JSON.parse(atob("<%- CompareResult %>")));
    const CONFLUENCE_BASE_URL = "<%- global.gConfig.wikiBaseURL %>";

    $(document).ready(function() {
        $("#compareResultsTitle").html(`Comparing Service Check results of ${serviceCheckRecord1.fnn}<br> \
                                        ${serviceCheckRecord1.createdBy} - ${new Date(serviceCheckRecord1.createdOn).toLocaleString("en-GB")} with \
                                        ${serviceCheckRecord2.createdBy} - ${new Date(serviceCheckRecord2.createdOn).toLocaleString("en-GB")}`);

        if (!$.isEmptyObject(serviceCheckRecord3)) {
            $("#compareResultsTitle").append(` and ${serviceCheckRecord3.createdBy} - ${new Date(serviceCheckRecord3.createdOn).toLocaleString("en-GB")}`);
        }

        addRowsToTable(serviceCheckDifferentRules, $("#Differences-tableHeader"), $("#Differences-tableBody"), $("#Differences-none-alert"));
        addRowsToTable(serviceCheckAllRules, $("#AllRules-tableHeader"), $("#AllRules-tableBody"), $("#Differences-none-alert"));
    });

    function addRowsToTable(rowData, tableHeaderElement, tableBodyElement, tableEmptyAlert) {

        if (rowData.length === 0) {
            tableEmptyAlert.show();
        } else {
            let tableHeader = $("<tr>");
            tableHeader.append($("<th>").text(""));
            tableHeader.append($("<th>").text("Category"));
            tableHeader.append($("<th>").text("Result"));
            tableHeader.append($("<th>").html(`${serviceCheckRecord1.createdBy} - ${new Date(serviceCheckRecord1.createdOn).toLocaleString("en-GB")} `)
                                .append($("<i>").css({ color: "blue" }).addClass("fas fa-link copyToClipboardLink")
                                .attr("data-original-title", "").attr("title", `Click to copy link of this command result with ID ${serviceCheckRecord1.id}`)
                                .attr("href", `${window.location.origin}/serviceCheck/view/html/${serviceCheckRecord1.id}`)));
            tableHeader.append($("<th>").html(`${serviceCheckRecord2.createdBy} - ${new Date(serviceCheckRecord2.createdOn).toLocaleString("en-GB")} `)
                                .append($("<i>").css({ color: "blue" }).addClass("fas fa-link copyToClipboardLink")
                                .attr("data-original-title", "").attr("title", `Click to copy link of this command result with ID ${serviceCheckRecord2.id}`)
                                .attr("href", `${window.location.origin}/serviceCheck/view/html/${serviceCheckRecord2.id}`)));

            // The 3rd service check record is optional and may not always be present when comparing
            if (!$.isEmptyObject(serviceCheckRecord3)) {
                tableHeader.append($("<th>").html(`${serviceCheckRecord3.createdBy} - ${new Date(serviceCheckRecord3.createdOn).toLocaleString("en-GB")}`)
                                    .append($("<i>").css({ color: "blue" }).addClass("fas fa-link copyToClipboardLink")
                                    .attr("data-original-title", "").attr("title", `Click to copy link of this command result with ID ${serviceCheckRecord3.id}`)
                                    .attr("href", `${window.location.origin}/serviceCheck/view/html/${serviceCheckRecord3.id}`)));
            }

            tableHeader.append($("<th>").text("Rule"));
            tableHeaderElement.html(tableHeader);

            for (let i in rowData) {
                let differentRule = rowData[i];
                let compareStatus = differentRule[0];
                let ruleCategory = differentRule[1];
                let ruleMessage = differentRule[2];
                let rulesDataRecord1 = differentRule[3];
                let rulesDataRecord2 = differentRule[4];
                let rulesDataRecord3 = differentRule[5];
                let ruleMetadata = differentRule[6];

                let tableBodyRow = $("<tr>");

                let compareStatusHtml;
                let ruleCategoryHtml;
                let ruleDataRecord1Html;
                let ruleDataRecord2Html;
                let ruleDataRecord3Html;
                let ruleMetadataHtml;

                let highlightRow = false;

                switch (compareStatus) {
                    case CompareResult.equal:
                        compareStatusHtml = $("<span>").css({ color: "green" }).attr("title", "Equal").addClass("fas fa-equals");
                        break;
                    case CompareResult.notEqual:
                        compareStatusHtml = $("<span>").css({ color: "red" }).attr("title", "Not Equal").addClass("fas fa-not-equal");
                        highlightRow = true;
                        break;
                    case CompareResult.ruleDataMissing:
                        compareStatusHtml = $("<span>").css({ color: "red" }).attr("title", "Data Missing").addClass("fas fa-question");
                        highlightRow = true;
                        break;
                    case CompareResult.compareError:
                        compareStatusHtml = $("<span>").css({ color: "red" }).attr("title", "Comparison Error").addClass("fas fa-exclamation");
                        break;
                }

                let rgbColor = hexToRgb(getCategoryColourFromCategory(ruleCategory));
                let categoryColourStyle = `--ccred: ${rgbColor.r}; --ccgreen: ${rgbColor.g}; --ccblue: ${rgbColor.b}; --threshold: 0.5; background: rgb(var(--ccred), var(--ccgreen), var(--ccblue)); --r: calc(var(--ccred) * 0.299); --g: calc(var(--ccgreen) * 0.587); --b: calc(var(--ccblue) * 0.114); --sum: calc(var(--r) + var(--g) + var(--b)); --perceived-lightness: calc(var(--sum) / 255); color: hsl(0, 0%, calc((var(--perceived-lightness) - var(--threshold)) * -10000000%)); }`;
                ruleCategoryHtml = $("<span>").addClass("badge badge-info").attr("style", categoryColourStyle).append($("<span>").text(ruleCategory));

                tableBodyRow.append($("<td>").html(compareStatusHtml));
                tableBodyRow.append($("<td>").html(ruleCategoryHtml));
                tableBodyRow.append($("<td>").text(ruleMessage));

                if (rulesDataRecord1) {
                    ruleDataRecord1Html = $("<span>").addClass("small text-muted").html(rulesDataRecord1.valueMsg);
                } else {
                    ruleDataRecord1Html = $("<span>").css({ color: "red" }).attr("title", `Rule data for ${ruleMetadata.name} is not present in this record`).addClass("fas fa-question-circle");
                }

                if (rulesDataRecord2) {
                    ruleDataRecord2Html = $("<span>").addClass("small text-muted").html(rulesDataRecord2.valueMsg);
                } else {
                    ruleDataRecord2Html = $("<span>").css({ color: "red" }).attr("title", `Rule data for ${ruleMetadata.name} is not present in this record`).addClass("fas fa-question-circle");
                }

                let hightlightColor = highlightRow ? "#E8A7A7" : "";

                tableBodyRow.append($("<td>").attr("bgcolor", hightlightColor).html(ruleDataRecord1Html));
                tableBodyRow.append($("<td>").attr("bgcolor", hightlightColor).html(ruleDataRecord2Html));

                // The 3rd service check record is optional and may not always be present when comparing
                if (!$.isEmptyObject(serviceCheckRecord3)) {
                    if (rulesDataRecord3) {
                        ruleDataRecord3Html = $("<span>").addClass("small text-muted").html(rulesDataRecord3.valueMsg);
                    } else {
                        ruleDataRecord3Html = $("<span>").css({ color: "red" }).attr("title", `Rule data for ${ruleMetadata.name} is not present in this record`).addClass("fas fa-question-circle");
                    }
                    tableBodyRow.append($("<td>").attr("bgcolor", hightlightColor).html(ruleDataRecord3Html));
                }

                ruleMetadataHtml = $("<div>").html($("<a>").attr("target", "blank").attr("href", `${CONFLUENCE_BASE_URL}${ruleMetadata.name}`).text(ruleMetadata.name));
                if (ruleMetadata.overrideCompare) {
                    ruleMetadataHtml.append(" ");
                    ruleMetadataHtml.append($("<span>").addClass("fas fa-code").css({ color: "grey" }).attr("title", "View override compare function code"))
                                    .attr("onclick", `$('#override-compare-modal-${ruleMetadata.name}').modal('show')`);

                    let ruleOverrideCompareModal = `<div class="modal fade" id="override-compare-modal-${ruleMetadata.name}" tabindex="-1" role="dialog" aria-labelledby="Title" aria-hidden="true">
                        <div class="modal-dialog modal-xlg" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">${ruleMetadata.name} Override Compare Code</h5>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                                <div class="modal-body"><div id="override-compare-code-${ruleMetadata.name}"></div></div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-sm btn-secondary" data-dismiss="modal">Close</button>
                                    <button type="button" class="btn btn-sm btn-primary" onclick="copyToClipboardModal('override-compare-code-${ruleMetadata.name}');">Copy to Clipboard</button>
                                </div>
                            </div>
                        </div>
                    </div>`;

                    $(document.body).append(ruleOverrideCompareModal);

                    let editor = ace.edit(`override-compare-code-${ruleMetadata.name}`, {
                        mode: "ace/mode/javascript",
                        minLines: 5,
                        maxLines: 100,
                        printMargin: false
                    });
                    editor.setValue(ruleMetadata.overrideCompare);
                    editor.clearSelection();
                }

                if (!ruleMetadata.present) {
                    ruleMetadataHtml.append(" ");
                    ruleMetadataHtml.append($("<span>").addClass("fas fa-exclamation-triangle").css({ color: "orange" }).attr("title", `Rule ${ruleMetadata.name} does not exist in Merge anymore`));
                }

                if (ruleMetadata.error) {
                    ruleMetadataHtml.append(" ");
                    ruleMetadataHtml.append($("<span>").addClass("fas fa-exclamation-circle").css({ color: "red" }).attr("title", ruleMetadata.error));
                }

                tableBodyRow.append($("<td>").html(ruleMetadataHtml));
                tableBodyElement.append(tableBodyRow);
            }

        }
    }
</script>
<%- include('footer', {}); %>
