{"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"url": {"type": "string", "minLength": 1, "nullable": false}, "body": {"type": "string", "nullable": false}, "header": {"type": "string", "nullable": false}, "timeout": {"type": "integer", "minimum": 1, "nullable": false}, "apiAuthKey": {"type": "string", "minLength": 1, "nullable": false}, "authType": {"type": "string", "nullable": true}, "cronTime": {"type": "string", "minLength": 1, "nullable": false}, "active": {"type": "boolean", "nullable": false}, "masslCertificateName": {"type": "string", "nullable": false}, "proxyRequired": {"type": "boolean", "nullable": false}, "requiredTokens": {"type": "array", "nullable": false}}, "required": ["url", "body", "timeout", "api<PERSON><PERSON><PERSON><PERSON>", "authType", "cronTime", "active"], "additionalProperties": false}}, "additionalProperties": true}