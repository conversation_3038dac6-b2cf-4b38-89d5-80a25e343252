'use strict';

import express from 'express';
import { body, validationResult } from 'express-validator';
import passport from 'passport';

import auth from '../modules/auth.js';
import { AuthorizationRoles } from '../modules/enumerations.js';
import logger from '../modules/logger.js';

const router = express.Router();


/**
 * @swagger
 * /api/authenticate:
 *   post:
 *     summary: Authenticate to get JSON token
 *     description: JSON token required for subsequent api calls
 *     requestBody:
 *       required: true
 *       content:
 *         application/x-www-form-urlencoded:
 *           schema:
 *             type: object
 *             properties:
 *               username:
 *                 type: string
 *                 example: d802522
 *                 description: Mandatory field. Account-01 modified AGS number. ie, c or d number
 *               password:
 *                 type: string
 *                 format: password
 *                 example: YOUR_PASSWORD
 *                 description: Mandatory field. Account-01 Password
 *             required:
 *               - username
 *               - password
 *     responses:
 *       200:
 *         description: A JSON with JWT token and expiry time
 *         content:
 *           application/json:
 *             schema:
 *                type: object
 *                properties:
 *                  token:
 *                    type: string
 *                    description: JWT Token
 *                  expires:
 *                    type: string
 *                    description: Expiry time for the token. d- denotes day, m- denotes minutes, s- denotes secs
 *     tags:
 *       - Authenticate
 */
router.post('/authenticate', [
    body('username').isString().withMessage('username should be a string').isLength({ min: 1 }).withMessage('username should be non-empty'),
    body('password').isString().withMessage('password should be a string').isLength({ min: 1 }).withMessage('password should be non-empty')
], function(req, res, next) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    passport.authenticate('local', { session: false }, function(error, user) {
        if (error) {
            res.sendStatus(401);
            return;
        }

        try {
            if (user[AuthorizationRoles.apiAccess] === false && user[AuthorizationRoles.admin] === false && user[AuthorizationRoles.levelLead] === false) {
                res.sendStatus(403);
                return;
            }

            if (user) {
                let token = auth.makeApiToken(user, global.gConfig.apiTokenExpiry);

                res.send({
                    token: token,
                    expires: global.gConfig.apiTokenExpiry
                });
            } else {
                res.sendStatus(401);
            }
        } catch(error) {
            logger.error(`API authentication error generating token, ${error.toString()}`);
            res.sendStatus(500);
        }
    })(req, res, next);
});

export default router;
