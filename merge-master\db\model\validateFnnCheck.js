import mongoose from 'mongoose';


const validateFnnCheckSchema = new mongoose.Schema({
    id: { type: String, default: null, unique: true, required: true, immutable: true },
    fnn: { type: String, default: null },
    status: { type: String, default: null },
    isValid: { type: Boolean, default: null },
    createdBy: { type: String, required: true, immutable: true, default: 'unknown' },
    createdOn: { type: Date, required: true, immutable: true, default: Date.now }
});


export default mongoose.model('validate_fnn_check', validateFnnCheckSchema);
