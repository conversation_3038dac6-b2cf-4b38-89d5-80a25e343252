<!-- v1 -->
<%- include('header', {}); %>
<%- include('menu', { currentTab: 'Home' }); %>
<link rel="stylesheet" href="/public/stylesheets/flatpickr.min.css">
<script src="/public/javascripts/flatpickr.min.js" crossorigin="anonymous"></script>
<link rel="stylesheet" type="text/css" href="/public/stylesheets/jsgrid.min.css">
<link rel="stylesheet" type="text/css" href="/public/stylesheets/jsgrid-theme.min.css">
<link rel="stylesheet" type="text/css" href="/public/stylesheets/select2.min.css">
<script type="text/javascript" src="/public/javascripts/jsgrid.min.js"></script>
<script src="/public/javascripts/select2.min.js"></script>
<style>
    .jsgrid-cell {
        word-wrap: break-word;
    }
</style>

<div class="d-flex flex-grow-1 flex-column" style="padding:1rem;">
    <div class="d-inline">
        <br>
        <div class="jumbotron py-1 px-4 text-white rounded bg-secondary">
            <h3 class="display-6 font-weight-normal"><%= title %></h3>
        </div>

        <div class="row">
            <div class="col-6">
                <p id="displayCommandTotal"></p>
            </div>

            <div class="col-6">
                <div class="float-right">
                    <button id="resetDates" class="btn btn-sm btn-secondary" title="Default time range (1 month)"><span class="fas fa-undo"></span></button>&nbsp;
                    <label for="startDate" class="col-form-label">Start:</label>&nbsp;
                    <input type="text" id="startDate">&nbsp;
                    <label for="endDate" class="col-form-label">End:</label>&nbsp;
                    <input type="text" id="endDate">&nbsp;
                    <span id="endDateClear" class="fas fa-times" title="Clear end date"></span>&nbsp;
                    <button id="historyRefresh" class="btn btn-primary" title="Refresh"><span class="fas fa-sync"></span></button>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-6">
                <label for="numRecords">Records per page:</label>
                <select id="numRecords" style="width: 6em">
                    <option value=10>10</option>
                    <option value=25>25</option>
                    <option value=50 selected>50</option>
                    <option value=100>100</option>
                    <option value=500>500</option>
                    <option value=1000>1000</option>
                </select>
            </div>
            <div class="col-6">
                <div class="float-right">
                    <button id="downloadCsvCurrPage" class="btn btn-dark"><span class="fas fa-download"></span> CSV (current page)</button>
                    <button id="downloadCsvAll" class="btn btn-dark"><span class="fas fa-download"></span> CSV (all)</button>
                </div>
            </div>
        </div>
        <br>
    </div>
    <div id="commandGrid" class="flex-grow-1"></div>
</div>

<script>
    const currentUser = "<%- user.username %>";
    const enableUserFiltering = !<%- limitCommands %>;

    let startDatePicker;
    let endDatePicker;

    $(document).ready(function() {
        $('#lastQuantity').keypress("keypress", function(e) {
            if (e.which < "0".charCodeAt(0) || e.which > "9".charCodeAt(0)) {
                e.preventDefault();
            }
        });

        $('#lastQuantity').on("paste",function(e) {
            e.preventDefault();
        });

        $("#lastUnit").select2({
            minimumResultsForSearch: -1,
            width: "resolve"
        });

        $("#numRecords").select2({
            minimumResultsForSearch: -1,
            width: "resolve"
        });

        $("#historyRefresh").click(function() {
            setButtonSpinner($("#historyRefresh"), $("<span>").addClass("fas fa-sync"), true);
            $("#commandGrid").jsGrid("loadData");
        });

        $("#numRecords").on('select2:select', function (e) {
            $("#commandGrid").jsGrid("option", "pageSize", parseInt($("#numRecords").val()));
        });

        $("#downloadCsvCurrPage").click(function() {
            let filter = $("#commandGrid").jsGrid("getFilter");
            let sorting = $("#commandGrid").jsGrid("getFilter");
            let pageIndex = $("#commandGrid").jsGrid("option", "pageIndex");
            let pageSize = $("#commandGrid").jsGrid("option", "pageSize");

            let allFilters = Object.assign({}, filter, sorting, { pageIndex: pageIndex, pageSize: pageSize });

            let queryData = setupQueryDataFromFilter(allFilters);
            let queryDataString = Object.keys(queryData).map(key => key + '=' + queryData[key]).join('&');

            window.location.href = `/command/history/download?${queryDataString}`;
        });

        $("#downloadCsvAll").click(function() {
            let filter = $("#commandGrid").jsGrid("getFilter");
            let sorting = $("#commandGrid").jsGrid("getFilter");
            let pageIndex = 1;
            let pageSize = $("#commandGrid").jsGrid("_itemsCount");

            let allFilters = Object.assign({}, filter, sorting, { pageIndex: pageIndex, pageSize: pageSize });

            let queryData = setupQueryDataFromFilter(allFilters);
            let queryDataString = Object.keys(queryData).map(key => key + '=' + queryData[key]).join('&');

            window.location.href = `/command/history/download?${queryDataString}`;
        });

        let startDateCommandCheck = localStorage.getItem("startDateCommandCheck");
        let endDateCommandCheck = localStorage.getItem("endDateCommandCheck");
        let startDate = null;
        let endDate = null;

        if (isNaN(Date.parse(startDateCommandCheck))) {
            startDate = new Date();
            startDate.setMonth(startDate.getMonth() - 1);
        } else {
            startDate = new Date(startDateCommandCheck);
        }

        if (!isNaN(Date.parse(endDateCommandCheck))) {
            endDate = new Date(endDateCommandCheck);
        }

        startDatePicker = flatpickr("#startDate", {
            defaultDate: startDate,
            enableTime: true,
            enableSeconds: true,
            dateFormat: "d/m/Y h:i:S K",
            position: "below",
            onChange: function(selectedDates, dateStr, instance) {
                if (Array.isArray(selectedDates) && selectedDates[0] instanceof Date) {
                    localStorage.setItem("startDateCommandCheck", selectedDates[0].toISOString());
                }
            }
        });

        endDatePicker = flatpickr("#endDate", {
            defaultDate: endDate,
            enableTime: true,
            enableSeconds: true,
            dateFormat: "d/m/Y h:i:S K",
            position: "below",
            onChange: function(selectedDates, dateStr, instance) {
                if (Array.isArray(selectedDates) && selectedDates[0] instanceof Date) {
                    localStorage.setItem("endDateCommandCheck", selectedDates[0].toISOString());
                }
            }
        });

        $("#endDateClear").click(function() {
            endDatePicker.clear();
            localStorage.removeItem("endDateCommandCheck");
        });

        $("#resetDates").click(function() {
            let defaultDate = new Date();
            defaultDate.setMonth(defaultDate.getMonth() - 1);

            startDatePicker.setDate(defaultDate);
            endDatePicker.clear();

            localStorage.removeItem("startDateCommandCheck");
            localStorage.removeItem("endDateCommandCheck");
        });

        $("#commandGrid").jsGrid({
            width: "100%",
            height: "100%",
            filtering: true,
            inserting: false,
            editing: false,
            sorting: true,
            autoload: true,
            paging: true,
            pageLoading: true,
            pageSize: parseInt($("#numRecords").val()),
            pageIndex: 1,
            editButton: false,
            deleteButton: false,
            controller: {
                loadData: function(filter) {
                    let data = $.Deferred();

                    let queryData = setupQueryDataFromFilter(filter);
                    $.ajax({
                        cache: false,
                        type: "GET",
                        url: "/command/history",
                        data: queryData,
                        success: function (response) {
                            let total = response.metadata && response.metadata.pagination ? response.metadata.pagination.total : 0;

                            data.resolve({
                                data: response.results,
                                itemsCount: total
                            });

                            let startDate = "";
                            let endDate = "";

                            if (response.metadata) {
                                if (response.metadata.startDate) {
                                    startDate = new Date(response.metadata.startDate).toLocaleString('en-GB');
                                }
                                if (response.metadata.endDate) {
                                    endDate = ` To: ${new Date(response.metadata.endDate).toLocaleString('en-GB')}`;
                                }
                            }
                            $("#displayCommandTotal").text(total + ` records total (From: ${startDate}${endDate})`);

                            $("#downloadCsvCurrPage").prop("disabled", total == 0);
                            $("#downloadCsvAll").prop("disabled", total == 0);
                        },
                        error: function (xhr, status, e) {
                            // Not the best solution, puts nothing in the table
                            data.resolve({
                                data: [],
                                itemsCount: 0
                            });
                        },
                        complete: function(_) {
                            setButtonSpinner($("#historyRefresh"), $("<span>").addClass("fas fa-sync"), false);
                        }
                    });

                    return data.promise();
                }
            },
            fields: [
                { name: "command", title: "Command", type: "text", itemTemplate: function(value, item) {
                    return $("<a>").attr("href", `/command/view/html/${item.id}`).attr("target", "blank").text(value);
                }, width: "50%"},
                { name: "status", title: "Status", type: "text", width: "10em" },
                { name: "createdBy", title: "User", type: "text", width: "10em", filtering: enableUserFiltering },
                { name: "createdOn", title: "Time", type: "text", filtering: false, itemTemplate: function(value) {
                    return new Date(value).toLocaleString('en-GB');
                }}
            ]
        });
    });

    function setupQueryDataFromFilter(filter) {
        let limit = filter.pageSize;
        let offset = (filter.pageIndex - 1)*filter.pageSize;
        let lastQuantity = $("#lastQuantity").val();
        let lastUnit = $("#lastUnit").val();

        let queryData = {};

        queryData.limit = limit;
        queryData.offset = offset;

        if (filter.sortField) {
            queryData.sort = filter.sortField;
            queryData.order = filter.sortOrder;
        }

        if (lastQuantity && lastUnit) {
            queryData.last = `${lastQuantity}${lastUnit}`;
        }

        if (!enableUserFiltering) {
            filter.createdBy = currentUser;
        }

        if (startDatePicker && Array.isArray(startDatePicker.selectedDates) && startDatePicker.selectedDates[0] instanceof Date) {
            queryData.startDate = startDatePicker.selectedDates[0].toISOString();
        }

        if (endDatePicker && Array.isArray(endDatePicker.selectedDates) && endDatePicker.selectedDates[0] instanceof Date) {
            queryData.endDate = endDatePicker.selectedDates[0].toISOString();
        }

        ["command"].forEach(field => {
            if (filter[field]) {
                queryData[field] = {
                    like: filter[field]
                };
            }
        });

        ["status", "createdBy"].forEach(field => {
            if (filter[field]) {
                queryData[field] = filter[field];
            }
        });

        if (filter.siiamCases != undefined) {
            queryData.siiamCases = filter.siiamCases;
        }

        return queryData;
    }
</script>

<%- include('footer', {}); %>
