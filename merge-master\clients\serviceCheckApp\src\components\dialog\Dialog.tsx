import { TextStyle } from '@able/react'
import { faTimes } from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ReactNode } from 'react'
import ReactDOM from 'react-dom'
import styles from './Dialog.module.scss'

interface DialogProps {
    isOpen: boolean
    onClose: () => void
    title?: string
    children: ReactNode
}

const Dialog = ({ isOpen, onClose, title, children }: DialogProps) => {
    if (!isOpen) return null

    return ReactDOM.createPortal(
        <div className={styles.dialogOverlay} onClick={onClose}>
            <div
                className={styles.dialogContainer}
                onClick={(e) => e.stopPropagation()}
            >
                <header>
                    {title && <TextStyle alias="HeadingC">{title}</TextStyle>}
                    <button
                        onClick={onClose}
                        className={styles.dialogCloseButton}
                    >
                        <FontAwesomeIcon size={'lg'} icon={faTimes} />
                    </button>
                </header>

                <div className={styles.dialogContent}>{children}</div>
            </div>
        </div>,
        document.body
    )
}

export default Dialog
