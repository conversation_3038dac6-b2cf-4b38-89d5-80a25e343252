'use strict';

import debug from 'debug';
import express from 'express';
import { body, query, param, validationResult, oneOf } from 'express-validator';
import hal from 'hal';
import { isValidPhoneNumber, parsePhoneNumber } from 'libphonenumber-js';
import _ from 'lodash';

import pagination from '../../modules/pagination.js';
import textTemplate from '../../modules/textTemplate.js';

// Service check libaries
import auth from '../../modules/auth.js';
import { AuthorizationRoles, ServiceCheckStartMethod } from '../../modules/enumerations.js';
import ServiceCheckModel from '../../db/model/serviceCheck.js';
import Rule from '../../db/model/rule.js';
import logger from '../../modules/logger.js';

import serviceCheck, { runServiceCheckAction, serviceCheckHistoryByFnn } from '../../modules/serviceCheck.js';
import validators from '../../modules/validators.js';
import { RuleResult, ServiceCheckStatus } from '../../modules/enumerations.js';
import { NotFoundError, RuleCodeError, RuleNotActionableError, ServiceCheckLockError } from '../../modules/error.js';
import { calculateStartDate } from '../../modules/helpers/date.js';

const debugMsg = debug('merge:apiServiceCheck');
const router = express.Router();


/**
 * @swagger
 * components:
 *   parameters:
 *     limit:
 *       name: limit
 *       in: query
 *       description: Maximum number of entries in results
 *       required: false
 *       schema:
 *         type: integer
 *     offset:
 *       name: offset
 *       in: query
 *       description: Index to start search from
 *       required: false
 *       schema:
 *         type: integer
 *     fnn:
 *       name: fnn
 *       in: path
 *       description: Mandatory field. FNN
 *       required: true
 *       schema:
 *         type: string
 *         example: N1234567R
 *     serviceCheckId:
 *       name: serviceCheckId
 *       in: path
 *       description: Mandatory field, service check ID of the request you want to get result of
 *       required: true
 *       schema:
 *         type: string
 *         example: 5f437f9b39244a5e2a937b4a
 *     template:
 *       name: template
 *       in: query
 *       description: Name of template to render service check with
 *       required: false
 *       schema:
 *         type: string
 *     actionRuleName:
 *       name: rule
 *       in: query
 *       description: Name of rule to get action results for
 *       required: true
 *       schema:
 *         type: string
 *         example: MAR001
 *   schemas:
 *     id:
 *       description: Service check ID
 *       type: string
 *     createdOn:
 *       description: Time service check was run
 *       type: string
 *       format: date-time
 *     createdBy:
 *       description: User who created service check
 *       type: string
 *     status:
 *       description: Service check status
 *       type: string
 *     pagination:
 *       type: object
 *       properties:
 *         limit:
 *           description: Maximum number of results that are returned
 *           type: integer
 *         offset:
 *           description: Pagination offset index
 *           type: integer
 *         total:
 *           description: Count of all results for this query
 *           type: integer
 *         prev:
 *           description: Pagination previous page URL
 *           type: string
 *         next:
 *           description: Pagination next page URL
 *           type: string
 *     serviceCheckRecord:
 *       type: object
 *       properties:
 *         input:
 *           type: object
 *           description: Input submitted
 *           example: {
 *               "searchFNN": "N1234567R",
 *               "level": 0,
 *               "suite": "standard",
 *               "idType": null,
 *               "carriageFNN": "",
 *               "deviceName": "",
 *               "deviceIP": "",
 *               "carriageType": ""
 *           }
 *         id:
 *           type: string
 *           description: Service Check result ID. Should be used to fetch the service check results
 *           example: "MSC8a6d5a10f328cbe9aa0e348a9206f102"
 *         status:
 *           type: string
 *           description: status of the service check. started | running | completed | completedWithError
 *           example: "completed"
 *         fnn:
 *           type: string
 *           description: FNN of the service check
 *           example: "N1234567R"
 *         carriageFNN:
 *           type: string
 *           description: Carriage FNN of the service check
 *           example: "AVC111222333444"
 *         carriageType:
 *           type: string
 *           description: Carriage Type of the service check
 *           example: "NBN"
 *         deviceName:
 *           type: string
 *           description: Name of the device FNN is operating on
 *           example: null
 *         nbnServiceType:
 *           type: string
 *           description: NBN Service Type
 *           example: null
 *         nbnAccessType:
 *           type: string
 *           description: NBN Access Type
 *           example: null
 *         nbnSubAccessType:
 *           type: string
 *           description: NBN Sub Access Type
 *           example: null
 *         rulesData:
 *           type: object
 *           description: Rule data generated from running the service check
 *           example: {
 *               "MDR000": {
 *                   "status": "done",
 *                   "type": "Extract",
 *                   "result": "OK",
 *                   "extraInfo": null,
 *                   "rootCauseCategory": "Data Collection",
 *                   "msg": "Found MDN or Billing FNN",
 *               }
 *           }
 *         address:
 *           type: string
 *           description: Address of the service
 *           example: "123 Example St, Sydney NSW 2000"
 *         createdBy:
 *           type: string
 *           description: User Id of the user who created request
 *           example: "d123456"
 *         createdOn:
 *           type: string
 *           description: UTC date time when the request was created
 *           example: "2020-06-05T15:00:11.011Z"
 *         endedOn:
 *           type: string
 *           description: UTC date time when the request was ended
 *           example: "2020-06-05T15:02:33.142Z"
 *         durationMilliSec:
 *           type: integer
 *           description: Time taken in milliseconds to complete the request
 *           example: 65124
 *         summary:
 *           type: object
 *           description: Summary of rule results grouped by root cause category
 *           example: {
 *               "Data Collection": {
 *                   "result": "OK",
 *                   "rules": [{
 *                       "name": "MDR001",
 *                       "status": "done",
 *                       "result": "OK",
 *                       "message": "Carriage FNN is given!"
 *                   }]
 *               }
 *           }
 *         actions:
 *           type: object
 *           description: Rules which are actionable or have been actioned
 *           example: {
 *               "MAR001": {
 *                   "message": "Message for rule 1",
 *                   "result": "actionable"
 *               },
 *               "MAR002": {
 *                   "message": "Message for rule 2",
 *                   "result": "actioned"
 *               }
 *           }
 *         templateResult:
 *           type: string
 *           description: Output of the rendered text template, only present if template query parameter is specified
 *           example: "Mobile Number: N2766194R\n\n\n\nMerge Result Link: https://merge.in.telstra.com.au/serviceCheck/view/html/MSC8a6d5a10f328cbe9aa0e348a9206f102"
 *     template:
 *       type: object
 *       properties:
 *         name:
 *           description: Name of template, used to reference template when rendering with a service check
 *           type: string
 *         title:
 *           description: Title of template
 *           type: string
 *         description:
 *           description: Description of template
 *           type: string
 *         allowedSystemsToAppend:
 *           description: Name of ticketing systems this template can be appended to
 *           type: array
 *           items:
 *             type: string
 *         default:
 *           description: If this template is a default template
 *           type: boolean
 */


/**
 * @swagger
 * /api/serviceCheck:
 *   post:
 *     summary: Creates a request to get service status of an FNN.
 *     description: We register a request to process service status of an FNN
 *     security:
 *       - MergeToken: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/x-www-form-urlencoded:
 *           schema:
 *             type: object
 *             required:
 *             - fnn
 *             properties:
 *               fnn:
 *                 type: string
 *                 description: Mandatory field. Input FNN of the service. Must be a valid FNN number for example "N2758590R".
 *                 example: N2758590R
 *               inputSuite:
 *                 type: string
 *                 description: Service check testing suite to use.
 *                 enum: [standard, andig, ipvoice, outageInfo, wholesale]
 *                 default: standard
 *               inputLevel:
 *                 type: integer
 *                 minimum: 0
 *                 maximum: 6
 *                 description: Input level for service check, is a number between 0 to 6 (default 0).
 *                 default: 0
 *               carriageFNN:
 *                 type: string
 *                 description: Optional field. AVC / Carriage FNN of the service that can be empty.
 *               carriageType:
 *                 type: string
 *                 description: Optional field. Manual carriage type input to override defaults.
 *                 enum: [ADSL, BDSL, IPMAN, MOBILE, NBN, VOIP]
 *               deviceName:
 *                 type: string
 *                 description: Optional field. Device name that can be empty.
 *               deviceIP:
 *                 type: string
 *                 format: ipv4
 *                 description: Optional field. Device IPv4 address that can be empty.
 *               adborId:
 *                 type: string
 *                 description: Optional field. Adbor Id for search. If given it overrides longitude and latitude.
 *               latitude:
 *                 type: number
 *                 minimum: -90
 *                 maximum: 90
 *                 description: Optional field. Latitude along with longitude for search. If given it overrides Adbor id.
 *               longitude:
 *                 type: number
 *                 minimum: -180
 *                 maximum: 180
 *                 description: Optional field. Longitude along with latitude for search. If given it overrides Adbor id.
 *               fieldInterfaceIP:
 *                 type: string
 *                 format: ipv4
 *                 description: Optional field. Field interface IPv4 address.
 *               CIDN:
 *                 type: string
 *                 description: Optional field. CIDN field can be empty.
 *               sourceData:
 *                 type: object
 *                 description: Optional field. Source data that will be present with "InRequest-" prefix.
 *               additionalParameters:
 *                 type: object
 *                 description: Optional field. Contains 2 possible fields, dateFrom and dateTo.
 *                 properties:
 *                   dateFrom:
 *                     type: string
 *                     format: date-time
 *                   dateto:
 *                     type: string
 *                     format: date-time
 *               ruleNames:
 *                 type: array
 *                 description: Optional field. Filters service check to run only the specified rule names and their dependencies.
 *                 items:
 *                   type: string
 *               sourceNames:
 *                 type: array
 *                 description: Optional field. Filters service check to run only the specified source names and their dependencies.
 *                 items:
 *                   type: string
 *               resultCallbackURL:
 *                 type: string
 *                 description: Optional field. A valid URL, once the job is completed `post` the result to this call back url.
 *                 example: http://SomeValidCallbackURL.com
 *           encoding:
 *             ruleNames:
 *               style: form
 *               explode: true
 *             sourceNames:
 *               style: form
 *               explode: true
 *     responses:
 *       200:
 *         description: A JSON input submitted and estimated time for result fetch and SC result id
 *         content:
 *           application/json:
 *             schema:
 *                type: object
 *                properties:
 *                  input:
 *                    type: object
 *                    description: Input submitted
 *                  SCrId:
 *                    type: string
 *                    description: Service Check result ID. Should be used to fetch the service check results
 *                  status:
 *                    type: string
 *                    description: status of the service check. started | running | completed | completedWithError
 *                  estimatedDurationMs:
 *                    type: string
 *                    description: estimated time when the service check results would be ready
 *       401:
 *         description: Not authenticated
 *     tags:
 *       - Service Check
 */
router.post('/', [
    body('fnn').isString().isLength({ min: 1 }).trim().withMessage('FNN is required'),
    body('inputSuite').default('standard').isString().isIn(['standard', 'andig', 'ipvoice', 'outageInfo', 'wholesale']).withMessage("inputSuite should one of the values in ('standard', 'andig', 'ipvoice', 'outageInfo', 'wholesale')"),
    body('inputLevel').default(0).isInt({ min: 0, max: 6 }).withMessage('input level must be between 0 and 6 (inclusive)').toInt(),
    body('carriageFNN').optional().isString().trim(),
    body('carriageType').optional().isString().isIn(['', 'ADSL', 'BDSL', 'IPMAN', 'MOBILE', 'NBN', 'VOIP']).withMessage("carriageType should one of the values in ('ADSL', 'BDSL', 'IPMAN', 'MOBILE', 'NBN', 'VOIP')"),
    body('deviceIP').optional().isString().trim(),
    body('deviceName').optional().isString().toUpperCase().trim(),
    body('fieldInterfaceIP').optional().isString().trim().custom(validators.isIPv4Address),
    body('adborId').optional().isString().trim().custom(validators.isAdborId).withMessage('adborId must be a string with 8-9 digit characters'),
    body('latitude').optional().isFloat({ min: -90.0, max: 90.0 }).withMessage('Latitude must be between -90.0 and 90.0').toFloat(),
    body('longitude').optional().isFloat({ min: -180.0, max: 180.0 }).withMessage('Longitude must be between -180.0 and 180.0').toFloat(),
    oneOf(
        [
            [
                body('adborId').isEmpty(),
                body('latitude').isEmpty(),
                body('longitude').isEmpty()
            ],
            [
                body('adborId').isString().trim().custom(validators.isAdborId),
                body('latitude').isEmpty(),
                body('longitude').isEmpty()
            ],
            [
                body('adborId').isEmpty(),
                body('latitude').isFloat({ min: -90.0, max: 90.0 }).toFloat(),
                body('longitude').isFloat({ min: -180.0, max: 180.0 }).toFloat()
            ],
        ],
        { errorType: 'grouped' }
    ),
    body('CIDN').optional().isString().trim(),
    body('sourceData').optional().custom(validators.isValidSourceData).withMessage("sourceData should be an object or valid JSON string, with all values containing objects").customSanitizer(param => { try { return typeof param === 'string' ? JSON.parse(param) : param; } catch (error) { return null; } }),
    body('additionalParameters').default({}).custom(validators.isValidAdditionalParameters).withMessage("additionalParameters should contain fields \"dateFrom\" and \"dateTo\" with valid dates where \"dateFrom\" is not in the future and is before \"dateTo\"").customSanitizer(param => { try { return typeof param === 'string' ? JSON.parse(param) : param; } catch (error) { return null; } }),
    body('ruleNames').toArray().optional().isArray().custom(param => { return new Set(param).size === param.length; }).withMessage("ruleNames array should contain unique elements"),
    body('ruleNames.*').isString().isLength({ min: 1 }).withMessage("rule names elements should all be strings with a minimum length of 1"),
    body('sourceNames').toArray().optional().isArray().custom(param => { return new Set(param).size === param.length; }).withMessage("sourceNames array should contain unique elements"),
    body('sourceNames.*').isString().isLength({ min: 1 }).withMessage("source names elements should all be strings with a minimum length of 1"),
    body('resultCallbackURL').optional().isString().trim()
], async function (req, res) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    if (!auth.checkUserAuthorizedForServiceCheckLevel(req.user, req.body.inputLevel)) {
        res.status(403).json({
            error: `User ${req.user.username} is not allowed to start a service check with level ${req.body.inputLevel}`
        });
        return;
    }

    try {
        let SCr = new ServiceCheckModel();

        let output = {
            input: {}
        };

        output.SCrId = SCr._id;
        output.estimatedDurationMs = 180000;

        SCr.id = SCr._id;
        SCr.input.searchFNN = output.input.fnn = req.body.fnn;
        // Always sets the suite to standard except in the case of outageInfo
        SCr.input.suite = output.input.suite = 'standard';
        if (req.body.inputSuite === 'outageInfo') {
            SCr.input.suite = output.input.suite = req.body.inputSuite;
        }

        SCr.input.level = output.input.level = req.body.inputLevel;
        SCr.input.carriageFNN = output.input.carriageFNN = req.body.carriageFNN ? req.body.carriageFNN : null;
        SCr.input.carriageType = output.input.carriageType = req.body.carriageType ? req.body.carriageType : null;
        SCr.input.deviceIP = output.input.deviceIP = req.body.deviceIP ? req.body.deviceIP : null;
        SCr.input.deviceName = output.input.deviceName = req.body.deviceName ? req.body.deviceName : null;
        SCr.input.fieldInterfaceIP = output.input.fieldInterfaceIP = req.body.fieldInterfaceIP ? req.body.fieldInterfaceIP : null;
        SCr.input.adborId = output.input.adborId = req.body.adborId ? req.body.adborId : null;
        SCr.input.latitude = output.input.latitude = req.body.latitude ? req.body.latitude : null;
        SCr.input.longitude = output.input.longitude = req.body.longitude ? req.body.longitude : null;
        SCr.input.ruleNames = output.input.ruleNames = req.body.ruleNames ? req.body.ruleNames : [];
        SCr.input.sourceNames = output.input.sourceNames = req.body.sourceNames ? req.body.sourceNames : [];
        SCr.input.CIDN = output.input.CIDN = req.body.CIDN ? req.body.CIDN : null;

        SCr.resultCallbackURL = req.body.resultCallbackURL ? req.body.resultCallbackURL : null;
        SCr.additionalParameters = req.body.additionalParameters;

        SCr.status = output.status = 'started';
        SCr.carriageType = SCr.input.carriageType;
        SCr.deviceName = SCr.input.deviceName ? SCr.input.deviceName.toUpperCase() : null;

        SCr.startMethod = ServiceCheckStartMethod.api;
        SCr.createdBy = output.createdBy = req.user.username;
        SCr.createdOn = output.startTime = new Date();

        if (req.body.sourceData) {
            req.body.sourceData = Object.keys(req.body.sourceData).reduce(function(result, key) {
                result['InRequest-' + key] = req.body.sourceData[key];
                return result;
            }, {});
            SCr.sourcesData = Object.assign(SCr.sourcesData, req.body.sourceData);
        }

        SCr.fnn = SCr.input.searchFNN.trim().toUpperCase();

        // Store valid phone number inputs in E.164 format
        if (isValidPhoneNumber(SCr.fnn, "AU")) {
            SCr.phoneNumber = parsePhoneNumber(SCr.fnn, "AU").format("E.164");
        }

        debugMsg(`Received Service check for FNN '${SCr.fnn}' by SID '${req.sessionID}'`);

        Object.assign(output, createHALResourceServiceCheck(req.originalUrl, "startServiceCheck", SCr.id).toJSON());

        const runOutageInfo = req.body.inputSuite === 'outageInfo';
        // Call ServiceCheck Module to start check Service
        serviceCheck.start(SCr, req.user, null, runOutageInfo);

        res.setHeader('Content-Type', 'application/json');
        res.send(output);

        debugMsg(`Service Check post called for: '${SCr.fnn}'`);
    } catch (error) {
        res.sendStatus(500);
        logger.error(error);
    }
});


/**
 * @swagger
 * /api/serviceCheck/view/{serviceCheckId}:
 *   get:
 *     summary: Gets the details of a registered service check.
 *     description: We get the result of the registered service check request here
 *     security:
 *       - MergeToken: []
 *     parameters:
 *       - $ref: '#/components/parameters/serviceCheckId'
 *       - $ref: '#/components/parameters/template'
 *     responses:
 *       200:
 *         description: A JSON input submitted and estimated time for result fetch and SC result id
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/serviceCheckRecord'
 *       401:
 *         description: Not authenticated
 *       422:
 *         description: Unprocessable entity, the template name provided does not exist
 *       500:
 *         description: Unexpected server error, the template failed to render and may be contain runtime or syntax errors
 *     tags:
 *       - Service Check
 */
router.get('/view/:inputID', [
    param('inputID').isString().isLength({ min: 1 }).trim(),
    query('template').optional().isString().isLength({ min: 1 })
], async function (req, res) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let inputID = req.params.inputID;
    let templateName = req.query.template;

    if (inputID !== null) {
        debugMsg('#> request to view ' + inputID);

        try {
            let serviceCheckRecord = await serviceCheck.findAndPopulate(inputID, {
                errorMessage: 0,
                phoneNumber: 0,
                sourcesMetadata: 0
            });

            if (serviceCheckRecord) {
                let serviceCheckRecordData = serviceCheckRecord.toJSON();
                serviceCheckRecordData.actions = {};

                let disableDisplayMessage = serviceCheck.disableDisplayCondition(serviceCheckRecord, req.user);
                let level = _.get(serviceCheckRecord, ['input', 'level'], 0);

                if (disableDisplayMessage) {
                    res.status(403).send({
                        error: disableDisplayMessage.message
                    });
                    return;
                } else if (!auth.checkUserAuthorizedForServiceCheckLevel(req.user, level)) {
                    res.status(403).send({
                        error: `You do not have access to view service check record ${inputID} with level: ${level}`
                    });
                    return;
                }

                let summary = {};
                if (serviceCheckRecord.rulesData) {
                    Object.keys(serviceCheckRecord.rulesData).map(elm=> {
                        let e = serviceCheckRecord.rulesData[elm];
                        if (e.result == 'Reject') {
                            return false;
                        }
                        summary[e['rootCauseCategory']] = summary[e['rootCauseCategory']] || {};
                        summary[e['rootCauseCategory']].result = e.result !='OK' ? e.result : (summary[e['rootCauseCategory']].result || 'OK');
                        let tempRule = typeof summary[e['rootCauseCategory']].rules!=='undefined' ? summary[e['rootCauseCategory']].rules : [];
                        tempRule.push({name:elm, message: e.msg, status: e.status, result : e.result});
                        summary[e['rootCauseCategory']].rules = tempRule;

                        // Add any rules which are actionable or actioned to the action field
                        if (e.result == RuleResult.actionable || e.result == RuleResult.actioned) {
                            serviceCheckRecordData.actions[elm] = {
                                result: e.result,
                                message: e.message ? e.message : null,
                                actionCode: "curl -X POST "+req.protocol + "://" + req.get('host') + "/serviceCheck/"+inputID+"/action -d rule='"+elm+"'"
                            };
                        }
                    });
                }

                // If the template name is specified, render the template and store it in templateResult
                if (templateName !== undefined) {
                    try {
                        // Currently does not support showRules parameter in API, but can be added
                        serviceCheckRecordData.templateResult = await textTemplate.renderTemplateByName(serviceCheckRecord.toJSON(), templateName, false, req.user);
                    } catch(error) {
                        if (error.name == "NotFoundError") {
                            res.status(422).send({
                                error: error.message
                            });
                        } else {
                            res.status(500).send({
                                error: `Service check record ${inputID} failed to render with template ${templateName}`
                            });
                        }
                        return;
                    }
                }

                if (serviceCheckRecordData.status != ServiceCheckStatus.running) {
                    let halResource = createHALResourceServiceCheck(req.originalUrl, "getServiceCheckDetails", serviceCheckRecordData.id);

                    // Note: Currently creates a HAL resource for every rule in the service check,
                    // regardless of whether it is an action rule and is actionable / actioned
                    let halResourceActions = [];
                    if (_.isPlainObject(serviceCheckRecordData.rulesData)) {
                        for (let ruleName in serviceCheckRecordData.rulesData) {
                            let ruleActionUrl = `/api/serviceCheck/${serviceCheckRecordData.id}/action?` + new URLSearchParams({
                                rule: ruleName
                            }).toString();

                            halResourceActions.push(hal.Resource({}, ruleActionUrl));
                        }
                    }
                    halResource.embed("ruleActions", halResourceActions);

                    Object.assign(serviceCheckRecordData, halResource.toJSON());
                }

                // Returned service check record does not include sources data
                delete serviceCheckRecordData.sourcesData;
                delete serviceCheckRecordData.errorMessage;

                // Also removes sources data from generated service checks
                if (Array.isArray(serviceCheckRecordData.generatedServiceChecks)) {
                    serviceCheckRecordData.generatedServiceChecks = serviceCheckRecordData.generatedServiceChecks.map((record) => {
                        if (_.isPlainObject(record)) {
                            delete record.sourcesData;
                        }
                        return record;
                    });
                }

                Object.assign(serviceCheckRecordData, { 'summary': summary });
                res.setHeader('Content-Type', 'application/json');
                res.json(serviceCheckRecordData);
            } else {
                res.sendStatus(404);
            }
        } catch(error) {
            res.status(500).send({
                error: error.message
            });
            return;
        }
    } else {
        debugMsg('#> Request view service Check without ID');
        res.sendStatus(400);
    }
});


/**
 * @swagger
 * /api/serviceCheck/{serviceCheckId}/action:
 *   get:
 *     summary: View action result
 *     description: Views the response from executing an action
 *     security:
 *       - MergeToken: []
 *     parameters:
 *       - $ref: '#/components/parameters/serviceCheckId'
 *       - $ref: '#/components/parameters/actionRuleName'
 *     responses:
 *       200:
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *                title: Rule action response
 *                type: object
 *                properties:
 *                  createdOn:
 *                    type: string
 *                    description: The time the action was executed
 *                    example: "2021-01-01T04:40:00.000Z"
 *                  status:
 *                    type: integer
 *                    description: The status code of the action response
 *                    example: 200
 *                  response:
 *                    type: object
 *                    description: The response from executing the action
 *       400:
 *         description: Invalid parameters
 *       401:
 *         description: Not authenticated
 *       422:
 *         description: Unprocessible entity
 *       500:
 *         description: Server error
 *     tags:
 *       - Service Check
 */
router.get('/:id/action', [
    query('rule').isString().isLength({ min: 1 }).withMessage('Rule name is required')
], async function(req, res) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let ruleName = req.query.rule;
    let id = req.params.id;

    try {
        let serviceCheckRecord = await ServiceCheckModel.findOne({ id: id });

        if (serviceCheckRecord === null) {
            res.sendStatus(404);
            return;
        }

        let response;
        let status;

        if (serviceCheckRecord.rulesData &&
            serviceCheckRecord.rulesData[ruleName] &&
            serviceCheckRecord.rulesData[ruleName].result == RuleResult.actioned &&
            serviceCheckRecord.rulesData[ruleName].action) {
            response = serviceCheckRecord.rulesData[ruleName].action;
            status = 200;
        } else {
            response = {
                message: `Rule ${ruleName} has not been actioned`
            };
            status = 422;
        }

        Object.assign(response, createHALResourceServiceCheck(req.originalUrl, "serviceCheckAction", serviceCheckRecord.id).toJSON());

        res.status(status).send(response);
    } catch(error) {
        logger.error(`API: Could not retrieve action for service check record ${id} action rule ${ruleName}, ${error.toString()}`);
        res.sendStatus(500);
    }
});


/**
 * @swagger
 * /api/serviceCheck/{serviceCheckId}/action:
 *   post:
 *     summary: Execute rule action
 *     description: Executes an action from a rule which is actionable associated with a service check
 *     security:
 *       - MergeToken: []
 *     parameters:
 *       - $ref: '#/components/parameters/serviceCheckId'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               rule:
 *                 type: string
 *                 description: Action rule name
 *                 example: MAR001
 *     responses:
 *       200:
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *                title: Rule action response
 *                type: object
 *                properties:
 *                  createdOn:
 *                    type: string
 *                    description: The time the action was executed
 *                    example: "2021-01-01T04:40:00.000Z"
 *                  status:
 *                    type: integer
 *                    description: The status code of the action response
 *                    example: 200
 *                  response:
 *                    type: object
 *                    description: The response from executing the action
 *       400:
 *         description: Invalid parameters
 *       401:
 *         description: Not authenticated
 *       422:
 *         description: Unprocessible entity
 *       500:
 *         description: Server error
 *     tags:
 *       - Service Check
 */
router.post('/:id/action', [
    body('rule').isString().isLength({ min: 1 }).withMessage('Rule name is required'),
    body('userInputs').optional().isString()
], async function(req, res) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let ruleName = req.body.rule;
    let id = req.params.id;
    // any additional params to be injected in MAR
    let additionalParameters = typeof req.body.userInputs === 'string' ?  JSON.parse(req.body.userInputs) : null;

    let serviceCheckRecord = null;
    let rule = null;

    try {
        [serviceCheckRecord, rule] = await Promise.all([
            serviceCheck.findAndPopulate(id),
            Rule.findOne({ name: ruleName })
        ]);

        if (serviceCheckRecord === null) {
            res.sendStatus(404);
            return;
        }

        // Executing an action for a service check not owned by the current user
        // is forbidden
        if (serviceCheckRecord.createdBy !== req.user.username) {
            res.sendStatus(403);
            return;
        }
    } catch(error) {
        logger.error(`API: Could not read service check record ${id} or action rule ${ruleName}, ${error.toString()}`);
        res.sendStatus(500);
    }

    let halResource = null;

    try {
        halResource = createHALResourceServiceCheck(req.originalUrl, "serviceCheckAction", serviceCheckRecord.id).toJSON();

        let actionResult = await runServiceCheckAction(serviceCheckRecord, rule);

        let responseBody = {
            response: actionResult,
            updatedRule: serviceCheckRecord.rulesData[rule.name]
        };

        if (_.isPlainObject(halResource)) {
            Object.assign(responseBody, halResource);
        }

        res.send(responseBody);
    } catch(error) {
        logger.error(`API: Could not run action for service check record ${id} action rule ${ruleName}, ${error.toString()}`);

        if (error instanceof RuleCodeError) {
            let responseBody = {
                response: null,
                updatedRule: serviceCheckRecord.rulesData[rule.name]
            };

            if (_.isPlainObject(halResource)) {
                Object.assign(responseBody, halResource);
            }

            res.status(500).send(responseBody);
        } else if ((error instanceof RuleNotActionableError) || (error instanceof ServiceCheckLockError)) {
            res.status(422).send({
                message: `Rule ${ruleName} is not actionable for this service check.`
            });
        } else {
            res.sendStatus(500);
        }
    }
});


/**
 * @swagger
 * /api/serviceCheck/{serviceCheckId}/listTemplates:
 *   get:
 *     summary: List templates for service check
 *     description: Lists the available templates for the current service check
 *     security:
 *       - MergeToken: []
 *     parameters:
 *       - $ref: '#/components/parameters/serviceCheckId'
 *     responses:
 *       200:
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *                title: Template List
 *                type: array
 *                items:
 *                  "$ref": '#/components/schemas/template'
 *       400:
 *         description: Invalid parameters
 *       401:
 *         description: Not authenticated
 *       422:
 *         description: Unprocessible entity
 *       500:
 *         description: Server error
 *     tags:
 *       - Service Check
 */
router.get('/:id/listTemplates', [
    param('id').isString().isLength({ min: 1 }).trim()
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let id = req.params.id;

    try {
        let record = await serviceCheck.findAndPopulate(id);

        if (!record) {
            throw new NotFoundError(`Service check record with id ${id} not found`);
        }

        serviceCheck.validateAccessForUser(record, req.user);
        res.send(await textTemplate.listActiveTemplatesServiceCheck(record, req.user));
    } catch(error) {
        if (error.name === "NotFoundError") {
            res.status(404).send({
                error: error.message
            });
        } else if (error.name === "ServiceCheckAccessError") {
            res.status(403).send({
                error: error.message
            });
        } else {
            res.status(500).send(error.message);
        }
    }
});


/**
 * @swagger
 * /api/serviceCheck/{serviceCheckId}/feedback:
 *   put:
 *     summary: Sets feedback for a service check record
 *     description: Sets positive / negative feedback for a service check record with an optional message, can only be done on service checks run by the current user
 *     security:
 *       - MergeToken: []
 *     parameters:
 *       - $ref: '#/components/parameters/serviceCheckId'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               isPositive:
 *                 type: boolean
 *                 description: Value to represent whether the service check has positive (true) or negative (false) feedback.
 *                 example: true
 *               message:
 *                 type: string
 *                 description: Optional field. Message to include with negative feedback to describe what went wrong or could be improved with the service check. Ignored for positive feedback.
 *                 example: Feedback message here
 *     responses:
 *       200:
 *         description: OK
 *       400:
 *         description: Invalid parameters
 *       401:
 *         description: Not authenticated
 *       403:
 *         description: Forbidden
 *       422:
 *         description: Unprocessible entity
 *       500:
 *         description: Server error
 *     tags:
 *       - Service Check
 */
router.put('/:id/feedback', [
    body('isPositive').isBoolean().toBoolean(),
    body('message').optional().isString().isLength({ max: 4000 })
], async function(req, res) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let id = req.params.id;

    try {
        let record = await ServiceCheckModel.findOne({ id: id }, {
            createdBy: 1
        });

        if (!record) {
            throw new NotFoundError(`Service check record with id ${id} not found`);
        }

        if (record.createdBy !== req.user.username) {
            res.sendStatus(403);
            return;
        }

        await ServiceCheckModel.findOneAndUpdate(
            { id: id },
            { $set: {
                feedback: {
                    isPositive: req.body.isPositive,
                    messageExists: typeof req.body.message === 'string' ? true : false,
                    message: typeof req.body.message === 'string' ? req.body.message : null
                }
            }},
            { upsert: false, strict: "throw", runValidators: true, setDefaultsOnInsert: true, useFindAndModify: false }
        );

        res.sendStatus(200);
    } catch(error) {
        if (error.name === "NotFoundError") {
            res.status(404).send({ error: error.message });
        } else {
            res.sendStatus(500);
        }
    }
});


/**
 * @swagger
 * /api/serviceCheck/history/{fnn}:
 *   get:
 *     summary: History of service checks based on FNN
 *     description: Gets a list of the most recent service checks performed on an FNN
 *     security:
 *       - MergeToken: []
 *     parameters:
 *       - $ref: '#/components/parameters/fnn'
 *       - $ref: '#/components/parameters/last'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/offset'
 *     responses:
 *       200:
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *                title: Service Check History Results
 *                type: object
 *                properties:
 *                  metadata:
 *                    type: object
 *                    properties:
 *                      pagination:
 *                        "$ref": '#/components/schemas/pagination'
 *                  results:
 *                    type: array
 *                    items:
 *                      properties:
 *                        id:
 *                          "$ref": '#/components/schemas/id'
 *                        createdBy:
 *                          "$ref": '#/components/schemas/createdBy'
 *                        createdOn:
 *                          "$ref": '#/components/schemas/createdBy'
 *                        status:
 *                          "$ref": '#/components/schemas/status'
 *       400:
 *         description: Invalid parameters
 *       401:
 *         description: Not authenticated
 *       500:
 *         description: Server error
 *     tags:
 *       - Service Check
 */
router.get('/history/:fnn', [
    query('last').default('1m').custom(validators.isTimePeriod),
    query('limit').default(100).isInt({ min: 1 }).toInt(),
    query('offset').default(0).isInt({ min: 0 }).toInt()
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let fnn = req.params.fnn;
    let limit = req.query.limit;
    let offset = req.query.offset;
    let serviceCheckStartDate = calculateStartDate(req.query.last);

    try {
        let [serviceChecks, count] = await serviceCheckHistoryByFnn(fnn, null, limit, offset, serviceCheckStartDate);
        let paginationMetadata = pagination.createPaginationMetadata(req, limit, offset, count);

        let response = {
            metadata: {
                pagination: paginationMetadata,
                startDate: serviceCheckStartDate
            },
            results: serviceChecks
        };

        // Unsure of whether this is the best practice or not, includes links
        // for individual service checks in the _embedded field
        let halResource = hal.Resource({}, req.originalUrl);

        let halResourceServiceChecks = [];
        for (let serviceCheck of serviceChecks) {
            let serviceCheckUrl = `/api/serviceCheck/view/${serviceCheck.id}`;

            halResourceServiceChecks.push(hal.Resource({}, serviceCheckUrl));
        }
        halResource.embed("serviceCheckRecords", halResourceServiceChecks);

        Object.assign(response, halResource.toJSON());

        res.send(response);
    } catch(error) {
        res.status(500).send({ error: error.message });
    }
});


/**
 * @swagger
 * /api/serviceCheck/listTemplates:
 *   get:
 *     summary: Gets all the available templates which can be rendered with service check view.
 *     description: List of all the service check templates available
 *     security:
 *       - MergeToken: []
 *     responses:
 *       200:
 *         description: A list of all the available active templates along with description and title to be used in serviceCheck view templates
 *         content:
 *           application/json:
 *             schema:
 *                title: Template List
 *                type: array
 *                items:
 *                  "$ref": '#/components/schemas/template'
 *       401:
 *         description: Not authenticated
 *       500:
 *         description: Internal server error while trying to fetch commands list
 *     tags:
 *       - Service Check
 */
router.get('/listTemplates', async function (req, res) {
    try {
        res.send(await textTemplate.listActiveTemplates());
    } catch (error) {
        logger.error(error);
        res.sendStatus(500);
    }
});

/**
 * @swagger
 * /api/serviceCheck/view/sourceData/{serviceCheckId}:
 *   get:
 *     summary: Gets the details of a source data for a given SC.
 *     description: We get the result of source data for the registered service check request here
 *     security:
 *       - MergeToken: []
 *     parameters:
 *       - $ref: '#/components/parameters/serviceCheckId'
 *     responses:
 *       200:
 *         description: A JSON input submitted and source data object for given SC result id
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/serviceCheckRecord'
 *       401:
 *         description: Not authenticated
 *       422:
 *         description: Unprocessable entity, the template name provided does not exist
 *       500:
 *         description: Unexpected server error, the template failed to render and may be contain runtime or syntax errors
 *     tags:
 *       - Service Check
 */
 router.get('/view/sourceData/:inputID', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.sourceAPIAccess
], false), [
    param('inputID').isString().isLength({ min: 1 }).trim(),
], async function (req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let id = req.params.inputID;

    try {
        let serviceCheckRecord = await serviceCheck.findAndPopulate(id, {
            errorMessage: 0,
            phoneNumber: 0,
            sourcesMetadata: 0
        });

        if (serviceCheckRecord) {
            await serviceCheckRecord.populateSourcesData();

            let level = _.get(serviceCheckRecord, ['input', 'level'], 0);
            if (!auth.checkUserAuthorizedForServiceCheckLevel(req.user, level)) {
                res.status(403).send({
                    error: `You do not have access to view a service check record with level: ${level}`
                });
                return;
            }

            let serviceCheckRecordData = serviceCheckRecord.toJSON();
            delete serviceCheckRecordData.rulesData;
            delete serviceCheckRecordData.errorMessage;

            Object.assign(serviceCheckRecordData, createHALResourceServiceCheck(req.originalUrl, "serviceCheckSourceData", serviceCheckRecord.id).toJSON());

            res.send(serviceCheckRecordData);
        } else {
            res.sendStatus(404);
        }
    } catch(error) {
        res.status(500).send({ error: error.message });
    }
});


/**
 *
 * @param {string} selfUri Current URI that the hypertext application language should refer to
 * @param {string} selfUriName Name of URI key to exclude from links
 * @param {string} serviceCheckId Service check ID to include in URIs
 * @returns
 */
function createHALResourceServiceCheck(selfUri, selfUriName, serviceCheckId) {
    let halResource = new hal.Resource({}, selfUri);

    let serviceCheckRecordLinks = {
        startServiceCheck: `/api/serviceCheck`,
        getServiceCheckDetails: `/api/serviceCheck/view/${serviceCheckId}`,
        serviceCheckAction: `/api/serviceCheck/${serviceCheckId}/action`,
        serviceCheckSourceData: `/api/serviceCheck/view/sourceData/${serviceCheckId}`
    };

    serviceCheckRecordLinks = _.omit(serviceCheckRecordLinks, [selfUriName]);

    for (let linkName in serviceCheckRecordLinks) {
        halResource.link(new hal.Link(linkName, { href: serviceCheckRecordLinks[linkName] }));
    }

    return halResource;
}


export default router;