
import { isValidPhoneNumber, parsePhoneNumber } from 'libphonenumber-js';

import { InputTypes } from './enumerations.js';
import regex from './helpers/regex.js';


export function identifyInputType(fnn) {
    let inputType = InputTypes.other;

    if (typeof fnn === 'string') {
        if (isValidPhoneNumber(fnn, 'AU')) {
            if (parsePhoneNumber(fnn, 'AU').format('E.164')[3] === '4') {
                inputType = InputTypes.mobilePhoneNumber;
            } else {
                inputType = InputTypes.phoneNumber;
            }
        } else if (regex.fnn.test(fnn)) {
            inputType = InputTypes.fnn;
        } else if (regex.deviceName.test(fnn)) {
            inputType = InputTypes.deviceName;
        } else if (regex.internationalLink.test(fnn)) {
            inputType = InputTypes.internationalLink;
        } else if (regex.avc.test(fnn)) {
            inputType = InputTypes.avc;
        } else if (regex.ovc.test(fnn)) {
            inputType = InputTypes.ovc;
        } else if (regex.bdslFnn.test(fnn)) {
            inputType = InputTypes.bdslFnn;
        } else if (regex.imsi.test(fnn)) {
            inputType = InputTypes.imsi;
        } else if (regex.uuid.test(fnn)) {
            inputType = InputTypes.uuid;
        } else if (regex.domId.test(fnn)) {
            inputType = InputTypes.domId;
        } else if (regex.cfsId.test(fnn)) {
            inputType = InputTypes.cfsId;
        }
    }

    return inputType;
}