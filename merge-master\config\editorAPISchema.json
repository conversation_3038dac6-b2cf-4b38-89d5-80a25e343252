{"title": "API", "type": "object", "id": "API", "headerTemplate": "{{ self.name }}", "options": {"disable_collapse": true, "disable_edit_json": false, "disable_properties": true, "disable_array_add": true, "disable_array_delete": true, "disable_array_reorder": true}, "properties": {"name": {"title": "Name", "type": "string", "minLength": 1}, "active": {"title": "Active", "type": "boolean", "format": "checkbox", "default": false}, "description": {"title": "Description", "type": "string", "format": "string", "description": "", "minLength": 0}, "apiType": {"title": "API Type", "description": "", "type": "string", "enum": ["rest", "function"], "default": "rest"}, "method": {"title": "API Method", "description": "", "type": ["string", "null"], "format": "", "enum": [null, "get", "post"], "default": "get"}, "parameters": {"type": "array", "format": "table", "title": "Parameters", "description": "Parameters to be passed to URI or payload", "uniqueItems": true, "items": {"type": "string", "readonly": false}, "options": {"disable_array_delete": false, "disable_array_reorder": false}}, "baseUrl": {"title": "Base URL", "type": "string", "description": "Select \"Config URLs\" for pre-configured URLs that are in Merge or select \"Custom expression\" to determine a URL manually", "anyOf": [{"title": "Config URLs", "type": "string", "enum": [], "options": {"enum_titles": []}}, {"title": "Function names", "type": "string", "enum": [], "options": {"enum_titles": []}}, {"title": "Custom expression", "type": "string", "description": "Use `${config}` for global config, `${parameters}` for parameters", "minLength": 0, "format": "javascript", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 5}}}]}, "uri": {"title": "URI", "type": "string", "description": "URI to append to the base URL, should resolve as a string or be empty", "minLength": 0, "format": "javascript", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 5}}}, "queryParams": {"title": "URL query parameters", "type": "string", "description": "Query parameters in the URL, should resolve as a Javascript object or be empty", "minLength": 0, "format": "javascript", "readonly": false, "options": {"ace": {"minLines": 5, "maxLines": 20}}}, "body": {"title": "Payload", "type": "string", "description": "Use `${config}` for global config, `${parameters}` for parameters", "minLength": 0, "format": "javascript", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 5}}}, "header": {"title": "Headers", "type": "string", "description": "Headers to be sent, use `${config}` for global config, `${parameters}` for parameters", "minLength": 0, "format": "javascript", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 5}}}, "authKeyDb": {"type": "array", "title": "Authorization Key in headers from database", "uniqueItems": true, "items": {"$ref": "#/definitions/authKeyDb"}, "options": {"disable_array_delete": false, "disable_array_reorder": false}}, "timeout": {"title": "Timeout", "type": "number", "description": "Request timeout", "minimum": 1, "default": 300000}, "parseResponse": {"title": "Parse response", "type": "string", "description": "Parse the response needed to convert text response to json (use `${response}` for response returned from request)", "minLength": 0, "format": "javascript", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 5}}}, "pollCondition": {"title": "Poll condition", "type": "string", "description": "use only when async poll is used", "minLength": 0, "format": "javascript", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 5}}}, "proxyRequired": {"title": "Proxy Required (used for API usually outside intranet)", "type": "boolean", "format": "checkbox", "default": false}, "useCookies": {"title": "Use Cookies", "type": "boolean", "format": "checkbox", "default": false}, "tlsMinVersion": {"title": "Minimum TLS version", "description": "Minimum TLS version that can be used for HTTPS requests to the API", "type": ["string", "null"], "enum": [null, "TLSv1.3", "TLSv1.2", "TLSv1.1", "TLSv1"], "default": null}, "errorCondition": {"title": "Error condition", "type": "string", "description": "use `${response}` for response returned from request", "minLength": 0, "format": "javascript", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 5}}}, "asyncPoll": {"$ref": "#/definitions/asyncPoll"}, "asyncCallback": {"$ref": "#/definitions/asyncCallback"}, "masslCertificateName": {"title": "MASSL certificate name", "description": "Name of certificate / key pair to use for mutual auth SSL", "type": ["string", "null"], "enum": [null], "default": null}, "wikiPage": {"title": "Wiki Page", "type": "string", "minLength": 0}}, "definitions": {"asyncPoll": {"title": "Async Poll", "type": ["object", "null"], "default": null, "options": {"collapsed": false, "disable_edit_json": true, "disable_properties": true, "disable_array_add": true, "disable_array_delete": true, "disable_array_reorder": true}, "properties": {"name": {"title": "Name", "type": "string"}, "interval": {"title": "Interval", "type": ["number", "null"], "description": "Poll interval", "minimum": 1, "default": null}, "uri": {"title": "URI", "type": "string", "description": "URI to append to the base URL, should resolve as a string or be empty (async poll)", "minLength": 0, "format": "javascript", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 5}}}, "queryParams": {"title": "URL query parameters", "type": "string", "description": "Query parameters in the URL, should resolve as a Javascript object or be empty", "minLength": 0, "format": "javascript", "readonly": false, "options": {"ace": {"minLines": 5, "maxLines": 20}}}, "header": {"title": "Headers", "type": "string", "description": "Headers to be sent! Use `${config}` for global config, `${parameters}` for parameter, `response` for response from prvious request(useful for API's with async poll)", "minLength": 0, "format": "javascript", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 5}}}, "authKeyDb": {"type": "array", "title": "Authorization Key in headers from database", "uniqueItems": true, "items": {"$ref": "#/definitions/authKeyDb"}, "options": {"disable_array_delete": false, "disable_array_reorder": false}}, "timeout": {"title": "Timeout", "type": "number", "description": "Request timeout", "minimum": 1, "default": 60000}, "parseResponse": {"title": "Parse response", "type": "string", "description": "Parse Async the response needed to convert text response to json (use `${config}` for global config, `${parameters}` for parameter, `response` for response returned from request)", "minLength": 0, "format": "javascript", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 5}}}, "doneCondition": {"title": "Done condition", "type": "string", "description": "Condition to stop polling (use `${response}` for response returned from request)", "minLength": 0, "format": "javascript", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 5}}}, "errorCondition": {"title": "Error condition", "type": "string", "description": "use `${response}` for response returned from request", "minLength": 0, "format": "javascript", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 5}}}, "transform": {"type": ["object", "null"], "description": "Apply tranformations on objects inside response (use `${config}` for global config, `${parameters}` for parameter, `response` for response returned from request) . ", "title": "Transform async response", "minLength": 0, "default": null, "patternProperties": {"^[a-z0-9_-]+$": {"type": ["number", "string"]}}, "additionalProperties": true, "options": {"collapsed": true}}, "resultAPI": {"$ref": "#/definitions/resultAPI"}}}, "asyncCallback": {"title": "As<PERSON> Callback", "type": ["object", "null"], "default": null, "options": {"collapsed": false, "disable_edit_json": true, "disable_properties": true, "disable_array_add": true, "disable_array_delete": true, "disable_array_reorder": true}, "properties": {"timeout": {"title": "Timeout", "type": "number", "description": "Request timeout", "minimum": 1, "default": 60000}, "enabledEnvs": {"type": "array", "title": "Enabled environments", "uniqueItems": true, "items": {"type": "string", "enum": ["localDev", "dev", "testing", "stage", "prd"], "readonly": false}, "options": {"disable_array_delete": false, "disable_array_reorder": false}}, "idField": {"title": "ID field", "type": "string", "description": "Field to extract from the response that matches the ID of the initial response", "minLength": 0, "format": "javascript", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 5}}}, "parseResponse": {"title": "Parse response", "type": "string", "description": "Parse Async the response needed to convert text response to json (use `${config}` for global config, `${parameters}` for parameter, `response` for response returned from request)", "minLength": 0, "format": "javascript", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 5}}}, "doneCondition": {"title": "Done condition", "type": "string", "description": "Condition to stop polling (use `${response}` for response returned from request)", "minLength": 0, "format": "javascript", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 5}}}, "errorCondition": {"title": "Error condition", "type": "string", "description": "use `${response}` for response returned from request", "minLength": 0, "format": "javascript", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 5}}}}}, "authKeyDb": {"title": "Auth Key Entry in Header", "type": ["object"], "default": {"authKeyName": "SampleName", "authKeyHeader": "Authorization"}, "options": {"collapsed": false, "disable_edit_json": true, "disable_properties": true, "disable_array_add": true, "disable_array_delete": true, "disable_array_reorder": true}, "properties": {"authKeyName": {"title": "Key Name", "minLength": 1, "type": "string"}, "authKeyHeader": {"title": "Header Name", "minLength": 1, "type": "string"}}}, "resultAPI": {"title": "Result API", "type": ["object", "null"], "description": "End result extraction API", "default": null, "options": {"collapsed": false, "disable_edit_json": true, "disable_properties": true, "disable_array_add": true, "disable_array_delete": true, "disable_array_reorder": true}, "properties": {"name": {"title": "Name", "type": "string", "readonly": true}, "timeout": {"title": "Timeout", "type": "number", "description": "Request timeout", "minimum": 1, "default": 60000}, "uri": {"title": "URI", "type": "string", "description": "URI to append to the base URL, should resolve as a string or be empty (async poll result)", "minLength": 0, "format": "javascript", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 5}}}, "queryParams": {"title": "URL query parameters", "type": "string", "description": "Query parameters in the URL, should resolve as a Javascript object or be empty", "minLength": 0, "format": "javascript", "readonly": false, "options": {"ace": {"minLines": 5, "maxLines": 20}}}, "header": {"title": "Headers", "type": "string", "description": "Headers to be sent! use `${config}` for global config, `${parameters}` for parameter, `response` for response from prvious request(useful for API's with async poll)", "minLength": 0, "format": "javascript", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 5}}}, "authKeyDb": {"type": "array", "title": "Authorization Key in headers from database", "uniqueItems": true, "items": {"$ref": "#/definitions/authKeyDb"}, "options": {"disable_array_delete": false, "disable_array_reorder": false}}, "parseResponse": {"type": "string", "description": "Parse the Result response needed to convert text response to json (use `${config}` for global config, `${parameters}` for parameter, `response` for response returned from request)", "title": "Parse response", "minLength": 0, "format": "javascript", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 5}}}, "transform": {"type": ["object", "null"], "description": "Apply tranformations on objects inside response (use `${config}` for global config, `${parameters}` for parameter, `response` for response returned from request) . ", "title": "Transform result response", "minLength": 0, "default": null, "patternProperties": {"^[a-z0-9_-]+$": {"type": ["number", "string"]}}, "additionalProperties": true, "options": {"collapsed": true}}}}}}