{"version": 10, "messageBuckets": [{"name": "Account_team", "description": "", "messageFrontOfHouse": "Refer Customer to Account Team", "messageCustomer": ""}, {"name": "BDSL_Check_connection_CPE_Adtran", "description": "", "messageFrontOfHouse": "Check Connection between CPE & Adtran (NTU)", "messageCustomer": ""}, {"name": "BDSL_CheckActiveTicket", "description": "", "messageFrontOfHouse": "Check Active Assurance Ticket Before further investigation or passing to field", "messageCustomer": ""}, {"name": "BDSL_Pass_to_CT", "description": "", "messageFrontOfHouse": "Create field case, Field Service Technician to prove service between CPE & Exchange", "messageCustomer": ""}, {"name": "BDSL_Reload_NTU", "description": "", "messageFrontOfHouse": "GMACS Reload NTU", "messageCustomer": ""}, {"name": "Create_IPMAN_INC", "description": "", "messageFrontOfHouse": "Create IPMAN INC", "messageCustomer": ""}, {"name": "Create_NBN_INC_HardDwn", "description": "", "messageFrontOfHouse": "Create NBN INC for site down", "messageCustomer": ""}, {"name": "Create_NBN_INC_PI", "description": "", "messageFrontOfHouse": "Create NBN INC for a performance incident", "messageCustomer": ""}, {"name": "Customer", "description": "", "messageFrontOfHouse": "NBN Fair Use Policy Flagged - Advise Customer", "messageCustomer": ""}, {"name": "Customer_or_Telstra_CT", "description": "", "messageFrontOfHouse": "Customer Site Issue - Alternativly Offer Telstra CT Visit FFS", "messageCustomer": ""}, {"name": "Furth_Inv_Existing Assurance Ticket", "description": "", "messageFrontOfHouse": "Further Investigation Required - Existing Assurance Ticket", "messageCustomer": ""}, {"name": "Furth_Inv_IPMAN", "description": "", "messageFrontOfHouse": "Further Investigation Required - IPMAN", "messageCustomer": ""}, {"name": "Furth_Inv_Layer1", "description": "", "messageFrontOfHouse": "Further Investigation Required - Access / Layer 1/2", "messageCustomer": ""}, {"name": "Furth_Inv_Layer3", "description": "", "messageFrontOfHouse": "Further Investigation Required - Access / Layer 3", "messageCustomer": ""}, {"name": "Furth_Inv_MDN_L3", "description": "", "messageFrontOfHouse": "Further Investigation Required - MDN", "messageCustomer": ""}, {"name": "Furth_Inv_Outage", "description": "", "messageFrontOfHouse": "Further Investigation Required - Outage (Conen, CMART, Tomahawk)", "messageCustomer": ""}, {"name": "Furth_Inv_Perf_More_Info", "description": "", "messageFrontOfHouse": "Further Investigation Required - Performance (Layer 3)", "messageCustomer": ""}, {"name": "Furth_Inv_Records", "description": "", "messageFrontOfHouse": "Further Investigation Required - Records (RASS)", "messageCustomer": ""}, {"name": "INFO_SRA", "description": "", "messageFrontOfHouse": "Advise Customer SRA Not set to Dynamic", "messageCustomer": ""}, {"name": "IPMAN_ActiveOutageINC", "description": "", "messageFrontOfHouse": "There is an active Outage INC/CRQ, follow existing processes", "messageCustomer": ""}, {"name": "IPMAN_Check_connection_CPE", "description": "", "messageFrontOfHouse": "Check Connection between CPE & Basement Switch / NTU", "messageCustomer": ""}, {"name": "IPMAN_CheckActiveTicket", "description": "", "messageFrontOfHouse": "Check Active Assurance Ticket Before Creating SNI", "messageCustomer": ""}, {"name": "IPMAN_Pass_to_CT", "description": "", "messageFrontOfHouse": "Create field case, Field Service Technician to prove service between CPE & Exchange", "messageCustomer": ""}, {"name": "IPMAN_Pass_to_ITAM", "description": "", "messageFrontOfHouse": "Create an ITAM for further investigation", "messageCustomer": ""}, {"name": "IPMAN_Reload_NTU", "description": "", "messageFrontOfHouse": "Reload NTU", "messageCustomer": ""}, {"name": "MDN_CheckActiveTicket", "description": "", "messageFrontOfHouse": "Check Active Assurance Ticket Before further investigation or passing to field", "messageCustomer": ""}, {"name": "NBN_ActiveOutageINC", "description": "", "messageFrontOfHouse": "There is an active NBN Outage INC/CRQ, follow existing processes", "messageCustomer": ""}, {"name": "NBN_CheckActiveTicket", "description": "", "messageFrontOfHouse": "Check Active Assurance Ticket Before Creating NBN INC", "messageCustomer": ""}]}