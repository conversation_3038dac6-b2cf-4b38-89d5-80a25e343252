import { ModalContent } from '@able/react'
import { faCheck, faCopy, faDownload } from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import React, { useRef, useState } from 'react'
import { LoadingPanel } from '../../features/serviceTest/components/serviceCheckLoadingPanel/LoadingPanel'
import styles from './ContentModal.module.scss'

interface ContentModalProps {
    isOpen: boolean
    title: string
    isLoading: boolean
    children: React.ReactNode
    onClose: () => void
}

export const ContentModal = ({
    isOpen,
    title,
    isLoading,
    children,
    onClose,
}: ContentModalProps) => {
    const contentRef = useRef<HTMLDivElement | null>(null)

    const [hasCopied, setHasCopied] = useState<boolean>(false)

    const handleDownload = () => {
        if (contentRef.current) {
            // Get the plain text content from the modal
            const textContent = contentRef.current.innerText

            // Create a downloadable .txt file
            const element = document.createElement('a')
            const file = new Blob([textContent], { type: 'text/plain' })
            element.href = URL.createObjectURL(file)
            element.download = `${title}.txt`
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }
    }

    const handleCopyToClipboard = async () => {
        if (contentRef.current) {
            const textContent = contentRef.current.innerText
            try {
                await navigator.clipboard.writeText(textContent)
                setHasCopied(true)
            } catch {
                setHasCopied(false)
            }
        }
    }

    return (
        <ModalContent
            variant="Expansive"
            bodyContent={
                isLoading ? (
                    <LoadingPanel />
                ) : (
                    <div className={styles.modalContent}>
                        <div className={styles.modalActions}>
                            <button
                                className={styles.modalButton}
                                onClick={handleDownload}
                            >
                                <FontAwesomeIcon icon={faDownload} />
                                <span>Download</span>
                            </button>
                            <button
                                className={styles.modalButton}
                                onClick={handleCopyToClipboard}
                            >
                                {!hasCopied ? (
                                    <FontAwesomeIcon icon={faCopy} />
                                ) : (
                                    <FontAwesomeIcon icon={faCheck} />
                                )}
                                <span>Copy to Clipboard</span>
                            </button>
                        </div>

                        <div ref={contentRef} className={styles.summary}>
                            {children}
                        </div>
                    </div>
                )
            }
            footerContent={<></>}
            isShowing={isOpen}
            title={title}
            developmentUrl="/public/images/able-sprites.svg"
            setHideDialog={() => {
                setHasCopied(false)
                onClose()
            }}
        />
    )
}
