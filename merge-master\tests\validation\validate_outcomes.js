
import assert from 'assert';
import _ from 'lodash';

import Outcome from '../../db/model/outcome.js';
import mergeConfigList from '../../modules/mergeConfigList.js';
import { MERGE_OBJECT_CONFIG_FILES } from '../../modules/helpers/constants.js';
import helpers from '../helpers.js';


describe('Merge Outcomes', () => {
    it('Unique names', async() => {
        let outcomes = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.outcome)).outcomes;
        let outcomeNames = new Set();

        for (let i in outcomes) {
            if (outcomeNames.has(outcomes[i].name)) {
                assert.fail(`Duplicate outcome name ${outcomes[i].name} in config`);
            }
            outcomeNames.add(outcomes[i].name);
        };
    });

    it('Valid fields', async() => {
        let outcomes = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.outcome)).outcome;

        for (let i in outcomes) {
            let outcome = new Outcome(outcomes[i]);
            try {
                await outcome.validate();
            } catch(error) {
                let newError = new Error(`Error validating outcome ${outcome.name}`);
                newError.original_error = error;
                newError.stack = error.stack;
                throw newError;
            }
        }
    });

    it('Ordered by name ascending', async() => {
        let outcomes = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.outcome)).outcomes;

        for (let i = 1; i < outcomes.length; i++) {
            let prevOutcome = outcomes[i - 1];
            let currOutcome = outcomes[i];

            if (prevOutcome.name.localeCompare(currOutcome.name, 'en') > 0) {
                assert.fail(`Outcome name ${prevOutcome.name} is out of order, should be after outcome ${currOutcome.name}`);
            }
        }
    });

    it('Correct field order', async() => {
        let outcomes = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.outcome)).outcomes;
        let invalidNames = [];

        for (let i in outcomes) {
            let outcomeJson = new Outcome(outcomes[i]).toJSON();
            delete outcomeJson.createdBy;
            delete outcomeJson.createdOn;

            if (!_.isEqualWith(outcomes[i], outcomeJson, helpers.compareObjectWithKeyOrder)) {
                invalidNames.push(outcomes[i].name);
            };
        }

        if (invalidNames.length) {
            assert.fail(`Key order for outcome(s) ${invalidNames.join(',')} does not match database schema order`);
        }
    });
});

