'use strict';

import mongoose from 'mongoose';
import axios from 'axios';
import chai from 'chai';
import chaiHttp from 'chai-http';
import chaiLike from 'chai-like';
import chaiDateTime from 'chai-datetime';
import superagentDefaults from 'superagent-defaults';
import supertest from 'supertest';
import _ from 'lodash';
import sinon from 'sinon';
import assert from 'assert';

var app;
var request;
var clock;
import '../set_config.js';
import config from '../config.js';
import helpers from '../helpers.js';
import { UNIT_TEST_ALL_ACCESS_USERNAME, UNIT_TEST_USERS } from '../users.js';

const should = chai.should();

chaiLike.extend(helpers.dateFieldCheck);
chaiLike.extend(helpers.regexCheck);
chai.use(chaiLike);
chai.use(chaiHttp);
chai.use(chaiDateTime);


describe('Merge API: PAT', () => {
    // Setup and connect to mongodb memory server, start merge app instance
    // Set request variable with a configured supertest agent with test JWT and app instance
    before(async function() {
        global.gConfig = _.cloneDeep(config.default);
        app = (await import('../../app.js')).default;
        request = superagentDefaults(supertest(app));

        let token = await helpers.authenticateApi(request, UNIT_TEST_ALL_ACCESS_USERNAME);
        request.set('Authorization', `Bearer ${token}`);

        clock = sinon.useFakeTimers(new Date('2020-01-01T00:00:00Z').getTime());
    });

    // Drop all contents of database before each test
    beforeEach(async function() {
        global.gConfig = _.cloneDeep(config.default);
        for (let collectionName in mongoose.connection.collections) {
            await mongoose.connection.collections[collectionName].deleteMany({});
        }
    });

    afterEach(async function() {
        sinon.restore();
    });

    after(async function() {
        clock.restore();
    });

    describe('Schedule PAT job', () => {
        it('Unauthenticated request', async() => {
            let res = await request.post('/api/pat').unset('Authorization');
            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];
            sinon.stub(axios, 'post').callsFake(async function() {
                return {
                    data: {
                        jobId: 1
                    },
                    status: 202
                };
            });

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                let token = await helpers.authenticateApi(request, username);
                let res = await request.post('/api/pat').set('Authorization', `Bearer ${token}`).send({
                    scheduleType: 'CIDN',
                    cidn: 'A123467890',
                    auditTypes: [
                        'device'
                    ]
                });

                switch(username) {
                    case 'apiAccess':
                        try {
                            res.should.have.status(202);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Empty input', async() => {
            let res = await request.post('/api/pat').send({});
            res.should.have.status(400);
        });

        it('No schedule type', async() => {
            let res = await request.post('/api/pat').send({
                fnn: [
                    'N1234567R'
                ],
                cidn: 'A1234567890',
                auditTypes: [
                    'device'
                ]
            });
            res.should.have.status(400);
        });

        it('Invalid schedule type', async() => {
            let res = await request.post('/api/pat').send({
                fnn: [
                    'N1234567R'
                ],
                scheduleType: 'notascheduletype',
                cidn: 'A1234567890',
                auditTypes: [
                    'device'
                ]
            });
            res.should.have.status(400);
        });

        it('Empty fnn array with scheduleType FNNs', async() => {
            let res = await request.post('/api/pat').send({
                fnn: [],
                scheduleType: 'FNNs',
                auditTypes: [
                    'device'
                ]
            });
            res.should.have.status(400);
        });

        it('Single FNN', async() => {
            sinon.stub(axios, 'post').callsFake(async function() {
                return {
                    data: {
                        jobId: 1
                    },
                    status: 202
                };
            });

            let res = await request.post('/api/pat').send({
                fnn: [
                    'N1234567R'
                ],
                scheduleType: 'FNNs',
                auditTypes: [
                    'device'
                ]
            });
            res.should.have.status(202);
            chai.expect(res.body).to.eql({ jobId: 1 });
        });

        it('Single FNN, all fields set', async() => {
            sinon.stub(axios, 'post').callsFake(async function() {
                return {
                    data: {
                        jobId: 1
                    },
                    status: 202
                };
            });

            let res = await request.post('/api/pat').send({
                fnn: [
                    'N1234567R'
                ],
                scheduleType: 'FNNs',
                auditTypes: [
                    'device'
                ],
                priority: 1,
                amdocsUser: 'd123456',
                productName: 'MDN Managed Router',
                productGroupName: '01 - MDN and Devices',
                orderType: 'Provide',
                sdwanFeatures: 'Yes',
                amdocsDeviceName: [
                    'ABCDEFGR01C01'
                ],
                ncpId: 'NCP-ID1234',
                managementTier: 'IOM Dedicated',
                ncoId: 'NCO-12345678'
            });
            res.should.have.status(202);
            chai.expect(res.body).to.eql({ jobId: 1 });
        });

        it('Single FNN, all fields set with optional fields empty strings', async() => {
            sinon.stub(axios, 'post').callsFake(async function() {
                return {
                    data: {
                        jobId: 1
                    },
                    status: 202
                };
            });

            let res = await request.post('/api/pat').send({
                fnn: [
                    'N1234567R'
                ],
                scheduleType: 'FNNs',
                auditTypes: [
                    'device'
                ],
                priority: 1,
                amdocsUser: '',
                productName: '',
                productGroupName: '',
                orderType: '',
                sdwanFeatures: '',
                amdocsDeviceName: [
                    ''
                ],
                ncpId: '',
                managementTier: '',
                ncoId: ''
            });
            res.should.have.status(202);
            chai.expect(res.body).to.eql({ jobId: 1 });
        });

        it('Single FNN, all fields set with optional fields null', async() => {
            sinon.stub(axios, 'post').callsFake(async function() {
                return {
                    data: {
                        jobId: 1
                    },
                    status: 202
                };
            });

            let res = await request.post('/api/pat').send({
                fnn: [
                    'N1234567R'
                ],
                scheduleType: 'FNNs',
                auditTypes: [
                    'device'
                ],
                priority: 1,
                amdocsUser: null,
                productName: null,
                productGroupName: null,
                orderType: null,
                sdwanFeatures: null,
                amdocsDeviceName: [
                    null
                ],
                ncpId: null,
                managementTier: null,
                ncoId: null
            });
            res.should.have.status(202);
            chai.expect(res.body).to.eql({ jobId: 1 });
        });

        it('Single FNN, insertion into audit_jobs has error', async() => {
            sinon.stub(axios, 'post').callsFake(async function() {
                return {
                    data: 'Internal Server Error',
                    status: 500
                };
            });

            let res = await request.post('/api/pat').send({
                fnn: [
                    'N1234567R'
                ],
                scheduleType: 'FNNs',
                auditTypes: [
                    'device'
                ]
            });
            res.should.have.status(500);
        });

        it('Multiple FNNs', async() => {
            sinon.stub(axios, 'post').callsFake(async function() {
                return {
                    data: {
                        jobId: 1
                    },
                    status: 202
                };
            });

            let res = await request.post('/api/pat').send({
                fnn: [
                    'N1234567R',
                    'N7654321R  ',
                    'N1111111R'
                ],
                scheduleType: 'FNNs',
                auditTypes: [
                    'device'
                ]
            });
            res.should.have.status(202);
            chai.expect(res.body).to.eql({ jobId: 1 });
        });

        it('Multiple FNNs, all fields set', async() => {
            sinon.stub(axios, 'post').callsFake(async function() {
                return {
                    data: {
                        jobId: 1
                    },
                    status: 202
                };
            });

            let res = await request.post('/api/pat').send({
                fnn: [
                    'N1234567R',
                    'N7654321R',
                    'N1111111R'
                ],
                scheduleType: 'FNNs',
                auditTypes: [
                    'device'
                ],
                priority: 1,
                amdocsUser: 'd123456',
                productName: 'product name here',
                productGroupName: 'product group name here',
                orderType: 'Other',
                sdwanFeatures: 'No',
                amdocsDeviceName: [
                    'ABCDEFGR01C01',
                    'GHIMJKGR01C01'
                ],
                ncpId: 'NCP-ID4321',
                managementTier: 'IOM Standard',
                ncoId: 'NCO-87654321'
            });
            res.should.have.status(202);
            chai.expect(res.body).to.eql({ jobId: 1 });
        });

        it('Multiple FNNs, one audit_q insertion with error', async() => {
            sinon.stub(axios, 'post').callsFake(async function() {
                return {
                    data: 'Internal Server Error',
                    status: 500
                };
            });

            let res = await request.post('/api/pat').send({
                fnn: [
                    'N1234567R',
                    'N1111111R',
                    'N7654321R  '
                ],
                scheduleType: 'FNNs',
                auditTypes: [
                    'device'
                ]
            });
            res.should.have.status(500);
        });

        it('Duplicate FNN', async() => {
            let res = await request.post('/api/pat').send({
                fnn: [
                    'N1234567R',
                    'N1234567R'
                ],
                scheduleType: 'FNNs',
                auditTypes: [
                    'device'
                ]
            });
            res.should.have.status(400);
        });

        it('Duplicate audit type', async() => {
            let res = await request.post('/api/pat').send({
                fnn: [
                    'N1234567R'
                ],
                scheduleType: 'FNNs',
                auditTypes: [
                    'device',
                    'device'
                ]
            });
            res.should.have.status(400);
        });

        it('Invalid audit type', async() => {
            let res = await request.post('/api/pat').send({
                fnn: [
                    'N1234567R'
                ],
                scheduleType: 'FNNs',
                auditTypes: [
                    'notanaudittype'
                ]
            });
            res.should.have.status(400);
        });

        it('Empty auditTypes array', async() => {
            let res = await request.post('/api/pat').send({
                fnn: [
                    'N1234567R'
                ],
                scheduleType: 'FNNs',
                auditTypes: []
            });
            res.should.have.status(400);
        });

        it('Empty CIDN with scheduleType CIDN', async() => {
            let res = await request.post('/api/pat').send({
                scheduleType: 'CIDN',
                auditTypes: [
                    'config'
                ]
            });
            res.should.have.status(400);
        });

        it('Non-empty CIDN with scheduleType CIDN', async() => {
            sinon.stub(axios, 'post').callsFake(async function() {
                return {
                    data: {
                        jobId: 1
                    },
                    status: 202
                };
            });

            let res = await request.post('/api/pat').send({
                scheduleType: 'CIDN',
                cidn: 'A1234567890',
                auditTypes: [
                    'config',
                    'device'
                ]
            });
            res.should.have.status(202);
            chai.expect(res.body).to.eql({ jobId: 1 });
        });

        it('Customer config under 4000 characters', async() => {
            sinon.stub(axios, 'post').callsFake(async function() {
                return {
                    data: {
                        jobId: 1
                    },
                    status: 202
                };
            });

            let res = await request.post('/api/pat').send({
                scheduleType: 'CIDN',
                cidn: 'A1234567890',
                auditTypes: [
                    'config'
                ],
                customerConfig: 'config here'
            });
            res.should.have.status(202);
            chai.expect(res.body).to.eql({ jobId: 1 });
        });

        it('Customer config over 4000 characters', async() => {
            let res = await request.post('/api/pat').send({
                scheduleType: 'CIDN',
                cidn: 'A1234567890',
                auditTypes: [
                    'config'
                ],
                customerConfig: 'a'.repeat(4001)
            });
            res.should.have.status(400);
        });

        it('AMDOCS user under 50 characters', async() => {
            sinon.stub(axios, 'post').callsFake(async function() {
                return {
                    data: {
                        jobId: 1
                    },
                    status: 202
                };
            });

            let res = await request.post('/api/pat').send({
                scheduleType: 'FNNs',
                fnn: [
                    'N1234567R'
                ],
                auditTypes: [
                    'config'
                ],
                amdocsUser: 'AMDOCS user here'
            });
            res.should.have.status(202);
            chai.expect(res.body).to.eql({ jobId: 1 });
        });

        it('AMDOCS user over 50 characters', async() => {
            let res = await request.post('/api/pat').send({
                scheduleType: 'FNNs',
                fnn: [
                    'N1234567R'
                ],
                auditTypes: [
                    'config'
                ],
                amdocsUser: 'a'.repeat(51)
            });
            res.should.have.status(400);
        });

        it('No insertId returned', async() => {
            sinon.stub(axios, 'post').callsFake(async function() {
                return {
                    data: 'Internal Server Error',
                    status: 500
                };
            });

            let res = await request.post('/api/pat').send({
                scheduleType: 'CIDN',
                cidn: 'A1234567890',
                auditTypes: [
                    'config'
                ]
            });
            res.should.have.status(500);

        });

        it('Priority 0', async() => {
            sinon.stub(axios, 'post').callsFake(async function() {
                return {
                    data: {
                        jobId: 1
                    },
                    status: 202
                };
            });

            let res = await request.post('/api/pat').send({
                scheduleType: 'CIDN',
                cidn: 'A1234567890',
                auditTypes: [
                    'config'
                ],
                priority: 0
            });
            res.should.have.status(202);
            chai.expect(res.body).to.eql({ jobId: 1 });
        });

        it('Priority 1', async() => {
            sinon.stub(axios, 'post').callsFake(async function() {
                return {
                    data: {
                        jobId: 1
                    },
                    status: 202
                };
            });

            let res = await request.post('/api/pat').send({
                scheduleType: 'CIDN',
                cidn: 'A1234567890',
                auditTypes: [
                    'config'
                ],
                priority: 1
            });
            res.should.have.status(202);
            chai.expect(res.body).to.eql({ jobId: 1 });
        });

        it('Priority 2', async() => {
            sinon.stub(axios, 'post').callsFake(async function() {
                return {
                    data: {
                        jobId: 1
                    },
                    status: 202
                };
            });

            let res = await request.post('/api/pat').send({
                scheduleType: 'CIDN',
                cidn: 'A1234567890',
                auditTypes: [
                    'config'
                ],
                priority: 2
            });
            res.should.have.status(202);
            chai.expect(res.body).to.eql({ jobId: 1 });
        });

        it('Priority 3', async() => {
            let res = await request.post('/api/pat').send({
                scheduleType: 'CIDN',
                cidn: 'A1234567890',
                auditTypes: [
                    'config'
                ],
                priority: 3
            });
            res.should.have.status(400);
        });
    });

    describe('Get PAT job', () => {
        it('Unauthenticated request', async() => {
            let res = await request.get('/api/pat/1').unset('Authorization');
            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];
            sinon.stub(axios, 'get').callsFake(async function() {
                return {
                    data: {
                        jobId: 1
                    },
                    status: 200
                };
            });

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                let token = await helpers.authenticateApi(request, username);
                let res = await request.get('/api/pat/1').set('Authorization', `Bearer ${token}`);

                switch(username) {
                    case 'apiAccess':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Empty input', async() => {
            let res = await request.post('/api/pat').send({});
            res.should.have.status(400);
        });

        it('ID with no PAT job present', async() => {
            sinon.stub(axios, 'get').callsFake(async function() {
                return {
                    data: 'Not Found',
                    status: 404
                };
            });

            let res = await request.get('/api/pat/1');
            res.should.have.status(404);
        });

        it('ID with PAT job present (sched_type CIDN)', async() => {
            sinon.stub(axios, 'get').callsFake(async function() {
                return {
                    data: {
                        id: 1,
                        name: 'Merge Tests',
                        sched_type: 'CIDN',
                        cidn: 'A1234567890',
                        status: 'Scheduled',
                        dir: '',
                        server: '',
                        _inserted: '2022-01-01T00:00:00.000Z',
                        _runtime: null,
                        progress: 0,
                        num_fnn: 0,
                        audit_types: 'carriage,CNSDI Audit,config,device,EWS Dashboard,nonMDN,',
                        _ended_on: null,
                        scheduled_by: UNIT_TEST_ALL_ACCESS_USERNAME,
                        customer_config: ''
                    },
                    status: 200
                };
            });

            let res = await request.get('/api/pat/1');
            res.should.have.status(200);
            chai.expect(res.body).to.eql({
                id: 1,
                name: 'Merge Tests',
                sched_type: 'CIDN',
                cidn: 'A1234567890',
                status: 'Scheduled',
                dir: '',
                server: '',
                _inserted: '2022-01-01T00:00:00.000Z',
                _runtime: null,
                progress: 0,
                num_fnn: 0,
                audit_types: 'carriage,CNSDI Audit,config,device,EWS Dashboard,nonMDN,',
                _ended_on: null,
                scheduled_by: UNIT_TEST_ALL_ACCESS_USERNAME,
                customer_config: ''
            });
        });

        it('ID with negative integer job ID', async() => {
            let res = await request.get('/api/pat/-1');
            res.should.have.status(400);
        });

        it('ID with string job ID', async() => {
            let res = await request.get('/api/pat/one');
            res.should.have.status(400);
        });

        it('ID with PAT job present (sched_type FNNs)', async() => {
            sinon.stub(axios, 'get').callsFake(async function() {
                return {
                    data: {
                        id: 1,
                        name: 'Merge Tests',
                        sched_type: 'CIDN',
                        cidn: 'A1234567890',
                        status: 'Scheduled',
                        dir: '',
                        server: '',
                        _inserted: '2022-01-01T00:00:00.000Z',
                        _runtime: null,
                        progress: 0,
                        num_fnn: 0,
                        audit_types: 'carriage,CNSDI Audit,config,device,EWS Dashboard,nonMDN,',
                        _ended_on: null,
                        scheduled_by: UNIT_TEST_ALL_ACCESS_USERNAME,
                        customer_config: '',
                        audit_q: []
                    },
                    status: 200
                };
            });

            let res = await request.get('/api/pat/1');
            res.should.have.status(200);
            chai.expect(res.body).to.eql({
                id: 1,
                name: 'Merge Tests',
                sched_type: 'CIDN',
                cidn: 'A1234567890',
                status: 'Scheduled',
                dir: '',
                server: '',
                _inserted: '2022-01-01T00:00:00.000Z',
                _runtime: null,
                progress: 0,
                num_fnn: 0,
                audit_types: 'carriage,CNSDI Audit,config,device,EWS Dashboard,nonMDN,',
                _ended_on: null,
                scheduled_by: UNIT_TEST_ALL_ACCESS_USERNAME,
                customer_config: '',
                audit_q: []
            });
        });

        it('ID with PAT job present (sched_type FNNs) with FNNs in audit_q', async() => {
            sinon.stub(axios, 'get').callsFake(async function() {
                return {
                    data: {
                        id: 1,
                        name: 'Merge Tests',
                        sched_type: 'FNNs',
                        cidn: '',
                        status: 'Unknown',
                        dir: '',
                        server: '',
                        _inserted: '2022-01-01T00:00:00.000Z',
                        _runtime: null,
                        progress: 0,
                        num_fnn: 0,
                        audit_types: 'carriage,CNSDI Audit,config,device,EWS Dashboard,nonMDN,',
                        _ended_on: null,
                        scheduled_by: UNIT_TEST_ALL_ACCESS_USERNAME,
                        customer_config: '',
                        audit_q: [{
                            id: 1,
                            job_id: 1,
                            process_id: null,
                            child_id: null,
                            is_in_Q: 'Y',
                            FNN: 'N0000001R',
                            status: 'planned',
                            network: null,
                            source: 'USER',
                            running_server: null,
                            inserted_on: '2022-01-01T00:00:01.000Z',
                            started_on: null,
                            ended_on: null,
                            message: null,
                            amdocs_user: 'AMDOCS user string'
                        },
                        {
                            id: 1,
                            job_id: 1,
                            process_id: null,
                            child_id: null,
                            is_in_Q: 'Y',
                            FNN: 'N0000002R',
                            status: 'planned',
                            network: null,
                            source: 'USER',
                            running_server: null,
                            inserted_on: '2022-01-01T00:00:02.000Z',
                            started_on: null,
                            ended_on: null,
                            message: null,
                            amdocs_user: 'AMDOCS user string'
                        }]
                    },
                    status: 200
                };
            });

            let res = await request.get('/api/pat/1');
            res.should.have.status(200);
            chai.expect(res.body).to.eql({
                id: 1,
                name: 'Merge Tests',
                sched_type: 'FNNs',
                cidn: '',
                status: 'Unknown',
                dir: '',
                server: '',
                _inserted: '2022-01-01T00:00:00.000Z',
                _runtime: null,
                progress: 0,
                num_fnn: 0,
                audit_types: 'carriage,CNSDI Audit,config,device,EWS Dashboard,nonMDN,',
                _ended_on: null,
                scheduled_by: UNIT_TEST_ALL_ACCESS_USERNAME,
                customer_config: '',
                audit_q: [{
                    id: 1,
                    job_id: 1,
                    process_id: null,
                    child_id: null,
                    is_in_Q: 'Y',
                    FNN: 'N0000001R',
                    status: 'planned',
                    network: null,
                    source: 'USER',
                    running_server: null,
                    inserted_on: '2022-01-01T00:00:01.000Z',
                    started_on: null,
                    ended_on: null,
                    message: null,
                    amdocs_user: 'AMDOCS user string'
                },
                {
                    id: 1,
                    job_id: 1,
                    process_id: null,
                    child_id: null,
                    is_in_Q: 'Y',
                    FNN: 'N0000002R',
                    status: 'planned',
                    network: null,
                    source: 'USER',
                    running_server: null,
                    inserted_on: '2022-01-01T00:00:02.000Z',
                    started_on: null,
                    ended_on: null,
                    message: null,
                    amdocs_user: 'AMDOCS user string'
                }]
            });
        });
    });

    describe('Get PAT results', () => {
        it('Unauthenticated request', async() => {
            let res = await request.get('/api/pat/1/results').unset('Authorization');
            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];
            sinon.stub(axios, 'get').callsFake(async function() {
                return {
                    data: {
                        jobId: 1
                    },
                    status: 200
                };
            });

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                let token = await helpers.authenticateApi(request, username);
                let res = await request.get('/api/pat/1/results').set('Authorization', `Bearer ${token}`);

                switch(username) {
                    case 'apiAccess':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('ID with no PAT job present', async() => {
            sinon.stub(axios, 'get').callsFake(async function() {
                return {
                    data: 'Not Found',
                    status: 404
                };
            });

            let res = await request.get('/api/pat/1/results');
            res.should.have.status(404);
        });

        it('ID with PAT job present, no audit results', async() => {
            sinon.stub(axios, 'get').callsFake(async function() {
                return {
                    data: [],
                    status: 200
                };
            });


            let res = await request.get('/api/pat/1/results');
            res.should.have.status(200);
            chai.expect(res.body).to.eql([]);
        });

        it('ID with PAT job present, with audit results', async() => {
            sinon.stub(axios, 'get').callsFake(async function() {
                return {
                    data: [{
                        FNN: 'N1234567R',
                        started_on: '2020-01-01T00:00:01.000Z',
                        type: 'raw',
                        pat_ver: '*******',
                        file: '',
                        created_on: '2020-01-01T00:00:00.000Z',
                        overall_audit: 'FAIL',
                        carriage_audit: 'UNAUDITED',
                        wireless_backup_audit: 'FAIL',
                        backend_management_audit: 'PASS',
                        router_config_audit: 'PASS',
                        device_live: 'PASS',
                        speed_check: 'N/A',
                        policy_speed_check: 'N/A',
                        policy_check: 'N/A',
                        rcbu_backup: 'UNAUDITED',
                        framed_routes: 'N/A',
                        framed_routes_unused: 'N/A',
                        rmon_alarms: 'PASS',
                        rmon_ifindex: 'N/A',
                        snmp: 'N/A',
                        snmp_host: 'N/A',
                        manat: 'N/A',
                        error_summary: '',
                        commission_date: '0000-00-00',
                        carriage_type: 'UNKNOWN',
                        rcbu_add_date: '2020-01-01',
                        dns: '',
                        bgp: 'PASS',
                        overall_rmon: '',
                        overall_qos: '',
                        cisco_wireless_backup: '',
                        juniper_wireless_backup: 'N/A',
                        nbn_audit: '',
                        nbn_edge_audit: '',
                        nbn_device_audit: '',
                        magpie_data_quality: 'PASS',
                        device_name: '',
                        device_fnn: '',
                        wireless_type: 'N/A'
                    },
                    {
                        FNN: 'N7654321R',
                        started_on: '2020-01-01T00:00:01.000Z',
                        type: 'raw',
                        pat_ver: '*******',
                        file: '',
                        created_on: '2020-01-01T00:00:00.000Z',
                        overall_audit: 'FAIL',
                        carriage_audit: 'UNAUDITED',
                        wireless_backup_audit: 'FAIL',
                        backend_management_audit: 'PASS',
                        router_config_audit: 'PASS',
                        device_live: 'PASS',
                        speed_check: 'N/A',
                        policy_speed_check: 'N/A',
                        policy_check: 'N/A',
                        rcbu_backup: 'UNAUDITED',
                        framed_routes: 'N/A',
                        framed_routes_unused: 'N/A',
                        rmon_alarms: 'PASS',
                        rmon_ifindex: 'N/A',
                        snmp: 'N/A',
                        snmp_host: 'N/A',
                        manat: 'N/A',
                        error_summary: '',
                        commission_date: '0000-00-00',
                        carriage_type: 'UNKNOWN',
                        rcbu_add_date: '2020-01-01',
                        dns: '',
                        bgp: 'PASS',
                        overall_rmon: '',
                        overall_qos: '',
                        cisco_wireless_backup: '',
                        juniper_wireless_backup: 'N/A',
                        nbn_audit: '',
                        nbn_edge_audit: '',
                        nbn_device_audit: '',
                        magpie_data_quality: 'PASS',
                        device_name: '',
                        device_fnn: '',
                        wireless_type: 'N/A'
                    },
                    {
                        FNN: 'N1111111R',
                        started_on: '2020-01-01T00:00:01.000Z',
                        type: 'raw',
                        pat_ver: '*******',
                        file: '',
                        created_on: '2020-01-01T00:00:00.000Z',
                        overall_audit: 'FAIL',
                        carriage_audit: 'UNAUDITED',
                        wireless_backup_audit: 'FAIL',
                        backend_management_audit: 'PASS',
                        router_config_audit: 'PASS',
                        device_live: 'PASS',
                        speed_check: 'N/A',
                        policy_speed_check: 'N/A',
                        policy_check: 'N/A',
                        rcbu_backup: 'UNAUDITED',
                        framed_routes: 'N/A',
                        framed_routes_unused: 'N/A',
                        rmon_alarms: 'PASS',
                        rmon_ifindex: 'N/A',
                        snmp: 'N/A',
                        snmp_host: 'N/A',
                        manat: 'N/A',
                        error_summary: '',
                        commission_date: '0000-00-00',
                        carriage_type: 'UNKNOWN',
                        rcbu_add_date: '2020-01-01',
                        dns: '',
                        bgp: 'PASS',
                        overall_rmon: '',
                        overall_qos: '',
                        cisco_wireless_backup: '',
                        juniper_wireless_backup: 'N/A',
                        nbn_audit: '',
                        nbn_edge_audit: '',
                        nbn_device_audit: '',
                        magpie_data_quality: 'PASS',
                        device_name: '',
                        device_fnn: '',
                        wireless_type: 'N/A'
                    }],
                    status: 200
                };
            });

            let res = await request.get('/api/pat/1/results');
            res.should.have.status(200);
            chai.expect(res.body).to.eql([{
                FNN: 'N1234567R',
                started_on: '2020-01-01T00:00:01.000Z',
                type: 'raw',
                pat_ver: '*******',
                file: '',
                created_on: '2020-01-01T00:00:00.000Z',
                overall_audit: 'FAIL',
                carriage_audit: 'UNAUDITED',
                wireless_backup_audit: 'FAIL',
                backend_management_audit: 'PASS',
                router_config_audit: 'PASS',
                device_live: 'PASS',
                speed_check: 'N/A',
                policy_speed_check: 'N/A',
                policy_check: 'N/A',
                rcbu_backup: 'UNAUDITED',
                framed_routes: 'N/A',
                framed_routes_unused: 'N/A',
                rmon_alarms: 'PASS',
                rmon_ifindex: 'N/A',
                snmp: 'N/A',
                snmp_host: 'N/A',
                manat: 'N/A',
                error_summary: '',
                commission_date: '0000-00-00',
                carriage_type: 'UNKNOWN',
                rcbu_add_date: '2020-01-01',
                dns: '',
                bgp: 'PASS',
                overall_rmon: '',
                overall_qos: '',
                cisco_wireless_backup: '',
                juniper_wireless_backup: 'N/A',
                nbn_audit: '',
                nbn_edge_audit: '',
                nbn_device_audit: '',
                magpie_data_quality: 'PASS',
                device_name: '',
                device_fnn: '',
                wireless_type: 'N/A'
            },
            {
                FNN: 'N7654321R',
                started_on: '2020-01-01T00:00:01.000Z',
                type: 'raw',
                pat_ver: '*******',
                file: '',
                created_on: '2020-01-01T00:00:00.000Z',
                overall_audit: 'FAIL',
                carriage_audit: 'UNAUDITED',
                wireless_backup_audit: 'FAIL',
                backend_management_audit: 'PASS',
                router_config_audit: 'PASS',
                device_live: 'PASS',
                speed_check: 'N/A',
                policy_speed_check: 'N/A',
                policy_check: 'N/A',
                rcbu_backup: 'UNAUDITED',
                framed_routes: 'N/A',
                framed_routes_unused: 'N/A',
                rmon_alarms: 'PASS',
                rmon_ifindex: 'N/A',
                snmp: 'N/A',
                snmp_host: 'N/A',
                manat: 'N/A',
                error_summary: '',
                commission_date: '0000-00-00',
                carriage_type: 'UNKNOWN',
                rcbu_add_date: '2020-01-01',
                dns: '',
                bgp: 'PASS',
                overall_rmon: '',
                overall_qos: '',
                cisco_wireless_backup: '',
                juniper_wireless_backup: 'N/A',
                nbn_audit: '',
                nbn_edge_audit: '',
                nbn_device_audit: '',
                magpie_data_quality: 'PASS',
                device_name: '',
                device_fnn: '',
                wireless_type: 'N/A'
            },
            {
                FNN: 'N1111111R',
                started_on: '2020-01-01T00:00:01.000Z',
                type: 'raw',
                pat_ver: '*******',
                file: '',
                created_on: '2020-01-01T00:00:00.000Z',
                overall_audit: 'FAIL',
                carriage_audit: 'UNAUDITED',
                wireless_backup_audit: 'FAIL',
                backend_management_audit: 'PASS',
                router_config_audit: 'PASS',
                device_live: 'PASS',
                speed_check: 'N/A',
                policy_speed_check: 'N/A',
                policy_check: 'N/A',
                rcbu_backup: 'UNAUDITED',
                framed_routes: 'N/A',
                framed_routes_unused: 'N/A',
                rmon_alarms: 'PASS',
                rmon_ifindex: 'N/A',
                snmp: 'N/A',
                snmp_host: 'N/A',
                manat: 'N/A',
                error_summary: '',
                commission_date: '0000-00-00',
                carriage_type: 'UNKNOWN',
                rcbu_add_date: '2020-01-01',
                dns: '',
                bgp: 'PASS',
                overall_rmon: '',
                overall_qos: '',
                cisco_wireless_backup: '',
                juniper_wireless_backup: 'N/A',
                nbn_audit: '',
                nbn_edge_audit: '',
                nbn_device_audit: '',
                magpie_data_quality: 'PASS',
                device_name: '',
                device_fnn: '',
                wireless_type: 'N/A'
            }]);
        });

        it('ID with negative integer job ID', async() => {
            let res = await request.get('/api/pat/-1/results');
            res.should.have.status(400);
        });

        it('ID with string job ID', async() => {
            let res = await request.get('/api/pat/one/results');
            res.should.have.status(400);
        });
    });
});
