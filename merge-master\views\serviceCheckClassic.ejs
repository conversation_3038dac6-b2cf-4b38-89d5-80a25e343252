
<%
  const isDevelopment = process.env.MERGE_ENV === 'localDev';

  // Determine head content dynamically
  const generateHeadContent = () => {
    if (isDevelopment) {
      return `
        <script type="module" src="http://localhost:5173/clients/@vite/client"></script>
        <script type="module">
            import RefreshRuntime from 'http://localhost:5173/clients/@react-refresh';
            RefreshRuntime.injectIntoGlobalHook(window);
            window.$RefreshReg$ = () => {};
            window.$RefreshSig$ = () => (type) => type;
            window.__vite_plugin_react_preamble_installed__ = true;
        </script>
        <script type="module" src="http://localhost:5173/clients/src/main.tsx"></script>
      `;
    } else {
      try {
        if (manifest && manifest["index.html"]) {
          const cssLinks = manifest["index.html"].css
            ? manifest["index.html"].css
                .map((cssFile) => `<link rel="stylesheet" href="/clients/${cssFile}">`)
                .join('\n')
            : '';

          const jsScript = `<script type="module" src="/clients/${manifest["index.html"].file}"></script>`;

          return `${cssLinks}\n${jsScript}`;
        } else {
          return `
            <script>
              console.error("Manifest is missing or invalid.");
            </script>
          `;
        }
      } catch (error) {
        console.error('Error reading or parsing manifest:', error.message);
        return `
          <script>
            console.error('Error loading scripts. Check the server logs for more details.');
          </script>
        `;
      }
    }
  };

  const headContent = generateHeadContent();
%>
<%- include('header', {}); %>
<%- include('menu', { currentTab: 'Form' }); %>
<%- include('serviceCheckRuleHelpers'); %>
<%- headContent %> <!-- Dynamically injected head content -->
<link rel="stylesheet" type="text/css" href="/public/stylesheets/select2.min.css">
<script src="/public/javascripts/select2.min.js" crossorigin="anonymous"></script>
<script src="/public/javascripts/magpie-schematics.js" crossorigin="anonymous"></script>
<div class="container">
    <br>
    <div id="inputErrorAlert" class="alert alert-danger alert-dismissible" style="display: none;"><div id="inputErrorMessage"></div><button type="button" class="close" id="inputErrorAlertClose">&times;</button></div>
    <div class="no-pad jumbotron py-3">
        <div class="">
            <div class="row mar-left-10">
                <div class="mar-04">
                    <label for="fnn" class="col-form-label" id="fnnLabel">FNN/Device/Phone</label>
                    <span style='color:blue' class='fas fa-question-circle' data-toggle="tooltip" data-placement="top" title='You can provide N-R, Y-N, A, AVC FNNs or Device Name. Merge prefers an MDN or Carriage FNN but any FNN or device name that Magpie has a valid record for it, is acceptable.'></span>
                    <input id="fnn" type="text" class="" onKeyUp="predictCTypeAndSuite()" placeholder="FNN (N-R,Y-N)" style="width:24em;">
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="runOutageInfoCheck">
                        <label class="form-check-label" for="runOutageInfoCheck">Only run outage info check</label>
                    </div>
                    <input type="hidden" id="SCLevel" name="SCLevel" value="0">
                    <button id="check" type="button" class="btn btn-info">Check</button>
                    <button id="clear" type="button" class="btn btn-secondary">Clear</button>
                    <a class="expand toggle-info btn-sm" data-toggle='collapse' data-target='#extraInfo'><span style='color:blue' class='fas fa-minus-square' title='Expand/Collapse'></span></a>
                    <br />
                    <div id="extraInfo" class="collapse show">
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="showOKRules" checked>
                            <label class="form-check-label" for="showOKRules">Show OK Rules</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="showHiddenSources">
                            <label class="form-check-label" for="showHiddenSources">Show Hidden Sources</label>
                        </div>
                        <br>
                        <label class="col-form-label">Additional Inputs</label>
                        <a class='expand btn btn-sm' data-toggle='collapse' data-target='#extraField'>
                            <span style='color:blue' class='fas fa-plus-square' title='Expand/Collapse Advanced Input'></span>
                        </a>
                        <label class="col-form-label <%= user && user.isDeveloper ? '' : 'invisible' %>">Example FNN</label>
                        <a class="expand btn btn-sm <%= user && user.isDeveloper ? '' : 'invisible' %>" data-toggle='collapse' data-target='#examples'>
                            <span style='color:blue' class='fas fa-plus-square' title='Expand/Collapse example FNNs'></span>
                        </a>
                        <div id="extraField" class="collapse hide pl-5">
                            <div class="row">
                                <div class="col-12">
                                    <small>All are optional. Enter if you want to overwite them or Merge cannot find them.</small>
                                </div>
                            </div>
                            <br>
                            <div class="row">
                                <div class="col-7">
                                    <label for="cidn" class="col-form-label">CIDN</label>
                                    <input id="cidn" type="text" class="" placeholder="CIDN">
                                    <br>
                                    <label for="carriageType" class="col-form-label">Carriage Type</label>
                                    <select id="carriageType" type="text" class="" placeholder="Carriage Type">
                                        <option value="" selected></option>
                                        <option value="ADSL">ADSL</option>
                                        <option value="BDSL">BDSL</option>
                                        <option value="IPMAN">IPMAN</option>
                                        <option value="MOBILE">MOBILE</option>
                                        <option value="NBN">NBN</option>
                                        <option value="NBN EE">NBN EE</option>
                                        <option value="VOIP">VOIP</option>
                                    </select>
                                    <br>
                                    <label for="carriageFNN" class="col-form-label">Carriage FNN</label>
                                    <input id="carriageFNN" type="text" class="" placeholder="Carriage FNN (A#/AVC#)">
                                    <br>
                                    <label for="deviceName" class="col-form-label">Device Name</label>
                                    <input id="deviceName" type="text" class="" placeholder="Device Name">
                                    <br>
                                    <label for="deviceIP" class="col-form-label">Device IP</label>
                                    <input id="deviceIP" type="text" class="" placeholder="Device IP">
                                    <br>
                                    <label for="fieldInterfaceIP" class="col-form-label">Field Interface IP</label>
                                    <input id="fieldInterfaceIP" type="text" class="" placeholder="Field Interface IP">
                                    <br>
                                    <label for="latitude" class="col-form-label">Latitude</label>
                                    <input id="latitude" type="number" min="-90.0" max="90.0" step="any" oninput="clearAdborId()" placeholder="Latitude">
                                    <br>
                                    <label for="longitude" class="col-form-label">Longitude</label>
                                    <input id="longitude" type="number" min="-180.0" max="180.0" step="any" oninput="clearAdborId()" placeholder="Longitude">
                                    <br>
                                    <label for="adborId" class="col-form-label">Adbor Id</label>
                                    <input id="adborId" type="text" class="" oninput="clearCoordinates()" placeholder="Adbor Id">
                                </div>
                                <div class="col-5">
                                    <label for="dateFrom" class="col-form-label">Date From</label>
                                    <input type="text" id="dateFrom">
                                    <span id="dateFromClear" class="fas fa-times" title="Clear"></span>
                                    <br>
                                    <label for="dateTo" class="col-form-label">Date To</label>
                                    <input type="text" id="dateTo">
                                    <span id="dateToClear" class="fas fa-times" title="Clear"></span>
                                    <br>
                                    <label class="col-form-label" for="ruleNames">Rule names</label>
                                    <select id="ruleNames" style="width:16em" multiple="multiple"></select>
                                    <br>
                                    <label class="col-form-label" for="sourceNames">Source names</label>
                                    <select id="sourceNames" style="width:16em" multiple="multiple"></select>
                                </div>
                            </div>
                        </div>
                        <br>
                        <div id="examples" class="collapse pl-5">
                            <small>Example FNN: N2766194R [ADSL], N5502562R [ADSL+Outage], Y00000048107N [BDSL], N1005370R [IPMAN], N2651597R [Mobile], N1020375R[NBN Fiber],N1021144R[FTTN],0410522734[MOBILE]</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <h5><a id="linkNewUi">New UI &rarr;</a></h5>

    <div id="socketDisconnectAlert" class="alert alert-danger" style="display: none;">
        <h4 class="alert-heading d-inline">Socket.io disconnection error</h4>
        <button id="socketDisconnectAlertHide" type="button" class="close" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
        <hr>
        <p>The socket.io client received a disconnect event. Any running service check records may not receive further updates for data from rules or sources, to view the status of the service check or text templates, click the "record" link near the top of each service check that has not completed.</p>
    </div>

    <div class="modal fade" id="textTemplateModal" tabindex="-1" role="dialog" aria-labelledby="Title" aria-hidden="true">
        <div class="modal-dialog modal-xlg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <input type="hidden" id="textTemplateModal-ID" name="textTemplateModal-ID" value="">
                    <div class="row">
                        <div class="form-inline col-md-auto">
                            <h5 class="modal-title" id="textTemplateModal-Title"></h5>
                            <div class="form-group mr-2">
                                <label for="inputTemplateName" class="col-sm-auto col-form-label">Template:</label>
                                <select id="inputTemplateName" class="form-control" data-service-check-id=""></select>
                            </div>
                            <div class="form-group mr-2">
                                <span style="display:none;" id="templateListLoading" class="spinner-border spinner-border-sm"></span>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showRulesData"><label class="form-check-label" for="showRulesData">Show rules data</label>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="textTemplateModal-controls" class="form-row p-2" style="display:none;">
                        <span class="icons">
                            <button
                                class="btn btn-outline-secondary btn-sm copyToClipboardTemplate"
                                data-toggle="tooltip"
                                data-placement="top"
                                title="Copy to clipboard"
                            >
                                <span class="fa fa-copy"></span>
                            </button>
                            <button id="textTemplateModal-download" class="btn btn-dark btn-sm" title="Download template to text file">
                                <span class="fas fa-download"></span> Text
                            </button>
                        </span>
                    </div>
                    <div id="textTemplateModal-alert" style="display:none;" class="alert alert-danger" role="alert">
                        <pre id="textTemplateModal-alertText"></pre>
                    </div>
                    <div id="textTemplateModal-emptyWarning-alert" style="display:none;" class="alert alert-warning" role="alert">
                        <pre>No relevant text templates found for this service check.</pre>
                    </div>
                    <pre id="textTemplateModal-text" style="display:none;" class="border"></pre>
                    <div id="textTemplateModalAppendTicket-alert" style="display:none;" class="alert"></div>
                </div>
                <div class="modal-footer">
                    <div class="form-group mr-2">
                        <button id="textTemplateModal-siiambutton" type="button" class="btn btn-sm btn-secondary">Append SIIAM</button>
                        <select id="textTemplateModal-siiamdropdown"></select>
                        <button id="textTemplateModal-sibutton" type="button" class="btn btn-sm btn-secondary">Append ServiceCentral</button>
                        <select id="textTemplateModal-sidropdown"></select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="feedbackModal" tabindex="-1" role="dialog" aria-labelledby="Title" aria-hidden="true">
        <div class="modal-dialog modal-xlg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    Service Check Feedback
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    Please provide further details as to what can be improved for this service check
                    <textarea class="form-control" id="feedbackMessageInput" maxlength="4000"></textarea>
                    <small class="text-muted" id="feedbackMessageInputCharCount"></small><small class="text-muted"> characters remaining</small>
                </div>
                <div class="modal-footer">
                    <button id="feedbackRemove" type="button" class="btn btn-sm btn-danger">Remove feedback</button>
                    <button id="feedbackSend" type="button" class="btn btn-sm btn-primary">Send feedback</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="magpieSchematicsModal" tabindex="-1" role="dialog" aria-labelledby="Title" aria-hidden="true">
        <div class="modal-dialog modal-xlg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    Magpie Schematics
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div id="magpieSchematicModalBody" class="modal-body">
                    <div class="form-row">
                        <input type="range" class="custom-range col-4" id="magpieSchematicZoom" min="50" max="120" step="5"><label id="magpieSchematicZoomValue" for="magpieSchematicZoom" class="col-2">100%</label>
                    </div>
                    <div id="magpieSchematic" class="overflow-auto"></div>
                </div>
                <div class="modal-footer">
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="numberRangesModal" tabindex="-1" role="dialog" aria-labelledby="Title" aria-hidden="true">
        <div class="modal-dialog modal-xsml" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Full List of Number Ranges</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div id="numberRangesModalBody" class="modal-body">
                    <div id="numberRangesModalContent" class="overflow-auto"></div>
                </div>
                <div class="modal-footer">
                </div>
            </div>
        </div>
    </div>

    <!--span id="disabled-noted" class="alert alert-warning" role="alert">
        Update - ** IP `Wireless` suite is now collapsed in the `Data & IP` suite **
    </span-->
    <div id="Results"></div>
</div>
<link rel="stylesheet" href="/public/stylesheets/flatpickr.min.css">
<script src="/public/javascripts/flatpickr.min.js" crossorigin="anonymous"></script>
<script src="/public/javascripts/ace.js" crossorigin="anonymous"></script>
<script src="/public/javascripts/merge_SC_lib.js" crossorigin="anonymous"></script>
<script src="/socket.io/socket.io.js"></script>
<script>

    //date time
    var dateFromPickr = flatpickr("#dateFrom", { enableTime: true, dateFormat: "Z", position:"above" });
    var dateToPickr = flatpickr("#dateTo", { enableTime: true, dateFormat: "Z", position:"above" });

    wikiBaseURL = "<%= global.gConfig.wikiBaseURL %>";
    const socket = io();
    var user = <%- JSON.stringify(user) %>;
    // Only contains service check IDs that were started by the user, not generated service check IDs
    var serviceChecksCurrent = [];

    // Keys are service check IDs that were started by the user, values are an array generated service check IDs
    var serviceChecksGenerated = {};

    // Contains all service check IDs including generated service check IDs
    var serviceChecksCompleted = new Set();

    // Contains all service check IDs including generated service check IDs
    var serviceChecksHidden = [];

    // Keeps track of service check rules data as it is received from the "ruleCheckMsg" socket event
    // Required for the search rules functionality
    var serviceChecksRulesData = {};

    const MAX_SERVICE_CHECKS = 5;

    const ServiceCheckStatus = Object.freeze({
        completedWithError: 'completedWithError',
        done: 'done',
        error: 'error'
    });

    const AppendToTemplateStatus = Object.freeze({
        error: 0,
        success: 1,
        info: 2
    });

    const feedbackMessageCharacterLimit = 4000;

    function validateFNNs(fnn, carriageFNN, deviceName) {
        if (!fnn) {
            return "Merge need a MDN or Billing FNN for Service Check.";
        } else if (!(/^[0-9a-zA-Z\_-]+$/.test(fnn))) {
            return "Please enter a valid MDN or Billing FNN for Service Check.";
        } else if (fnn.length > 19) {
            if (fnn.length > 35) {
                if (!(/^[0-9a-f|A-F]{8}-[0-9a-f|A-F]{4}-[0-9a-f|A-F]{4}-[0-9a-f|A-F]{4}-[0-9a-f|A-F]{12}$/.test(fnn)))
                    return "Please enter a valid UUID as 32 hexadecimal characters with four hyphens: XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX.";
            } else {
                return "Please enter a valid MDN or Billing FNN for Service Check.";
            }
        } else if (carriageFNN && carriageFNN.length > 19) {
            return "Please enter a carriageFNN for Service Check.";
        } else if (deviceName && deviceName.length > 19) {
            return "Please enter a valid deviceName for Service Check.";
        } else if ((/^Y[0-9a-zA-Z\_-]+$/.test(fnn)) && suite != 'standard') {
            return "Please select Suite 'Data & IP' for BDSL or CIP type of FNNs starting with 'Y' for Service Check.";
        } else {
            return null;
        }
    }

    $(document).ready(function() {
        $("#ruleNames").select2({
            tags: true
        });

        $("#sourceNames").select2({
            tags: true
        });

        //Show and Hide Rules
        $('#showOKRules').change(function(){
            if (this.checked) {
                $('.rule-OK').fadeIn('fast');
                localStorage.setItem('showOKRules', true);
            } else {
                $('.rule-OK').fadeOut('fast');
                localStorage.setItem('showOKRules', false);
            }
        });

        // Show hidden sources in sources tab
        $('#showHiddenSources').change(function(){
            if (this.checked) {
                $('.sourceHideInResult').fadeIn('fast');
                localStorage.setItem('showHiddenSources', true);
            } else {
                $('.sourceHideInResult').fadeOut('fast');
                localStorage.setItem('showHiddenSources', false);
            }
        });

        let paramRepeatId = "<%= typeof serviceCheckRepeatId != 'undefined' ? serviceCheckRepeatId : null %>";

        if (paramRepeatId) {
            restartServiceCheck(paramRepeatId);
        } else {
            // Loads variables from localStorage into input elements
            // Init FNN
            if (localStorage.getItem('FNN') != null) {
                $("#fnn").val(localStorage.getItem('FNN'));
                $("#SCLevel").val(0);
            }
        }

        if (localStorage.getItem('showOKRules') != null) {
            $('#showOKRules').prop('checked', JSON.parse(localStorage.getItem('showOKRules')));
        }

        if (localStorage.getItem('showHiddenSources') != null) {
            $('#showHiddenSources').prop('checked', JSON.parse(localStorage.getItem('showHiddenSources')));
        }

        if (localStorage.getItem('showDebugActive') != null) {
            $('#showDebugActive').prop('checked', JSON.parse(localStorage.getItem('showDebugActive')));
        }

        socket.on('serviceCheckStarted', function(serviceCheckId, originServiceCheckId=null, originServiceCheckFnn=null) {
            if (!originServiceCheckId) {
                serviceChecksRulesData[serviceCheckId] = {};
                initNewServiceCheck(serviceCheckId, $("#fnn").val(), serviceChecksRulesData[serviceCheckId]);

                serviceChecksCurrent.push(serviceCheckId);
                serviceChecksGenerated[serviceCheckId] = [];

                // Gets the least recent service checks and removes it from the page
                while (serviceChecksCurrent.length > MAX_SERVICE_CHECKS) {
                    let removeServiceCheckId = serviceChecksCurrent.shift();
                    serviceChecksHidden.push(removeServiceCheckId);
                    serviceChecksHidden.push(...serviceChecksGenerated[removeServiceCheckId]);
                    delete serviceChecksGenerated[removeServiceCheckId];
                    delete serviceChecksRulesData[removeServiceCheckId];

                    $(`#service-check-group-${removeServiceCheckId}`).remove();
                    $(`#detail-${removeServiceCheckId}`).remove();
                    $(`#pills-tabContent-${removeServiceCheckId}`).remove();
                    $(`div[id^=${removeServiceCheckId}]`).remove();
                }

                // Sets the new URL link to the most recently started service check
                // To update: New UI link
                $('#linkNewUi').attr('href', `${location.protocol}//${location.host}/serviceCheck/new?${new URLSearchParams({ id: serviceCheckId }).toString()}`)
            } else {
                if (serviceChecksCurrent.includes(originServiceCheckId)) {
                    serviceChecksRulesData[serviceCheckId] = {};
                    serviceChecksGenerated[originServiceCheckId].push(serviceCheckId);
                    initNewServiceCheck(serviceCheckId, $("#fnn").val(), serviceChecksRulesData[serviceCheckId], originServiceCheckId, originServiceCheckFnn);
                } else {
                    // Original service check is already hidden, add generated service check ID to the hidden list as well
                    serviceChecksHidden.push(serviceCheckId);
                }
            }
        });

        $("#check").on('click', runServiceCheck);

        // Disable Check till there is a FNN
        $("#check").prop('disabled', $('#fnn').val().length <= 8);

        $("#clear").on('click', function() {
            $("#Results").empty();

            // Moves all current service checks to the array for hidden service checks
            serviceChecksHidden.push(...serviceChecksCurrent);
            for (let serviceCheckId in serviceChecksGenerated) {
                serviceChecksHidden.push(...serviceChecksGenerated[serviceCheckId]);
            }
            serviceChecksCurrent = [];
            serviceChecksGenerated = {};

            console.log('Cleared results');
        });

        // Validate input for FNN
        $("#fnn").keyup(function(e) {
            // Enter key
            if (e.keyCode === 13 && this.value.length >= 8) {
                runServiceCheck();
            }
        });

        $("#fnn").on('input', function(e) {
            this.value = this.value.trim().toUpperCase();
            $("#check").prop('disabled', this.value.length <= 8);
        });

        $("#dateFromClear").click(function() {
            dateFromPickr.clear();
        });

        $("#dateToClear").click(function() {
            dateToPickr.clear();
        });

        $("#inputErrorAlertClose").click(function() {
            $("#inputErrorAlert").hide();
        });

        $("#socketDisconnectAlertHide").click(function() {
            $("#socketDisconnectAlert").hide();
        });

        // Template name selection drop down listener
        $("#inputTemplateName").on('change', function() {
            resetAppendTemplateOptions();
        });

        // Rule data check box listener
        $("#inputTemplateName,#showRulesData").on('change', function() {
            let showRulesData = $("#showRulesData").prop("checked");
            let templateName = $('#inputTemplateName').val();
            let serviceCheckId = $('#inputTemplateName').attr('data-service-check-id');

            if (!templateName) {
                alert("Template not selected");
                return;
            }

            resetServiceCentralOptions();
            resetSIIAMOptions();

            $.ajax({
                cache: false,
                type: "GET",
                url: `/serviceCheck/${encodeURIComponent(serviceCheckId)}/renderTemplate`,
                data: {
                    name: templateName,
                    showRulesData: showRulesData
                },
                dataType: "text",
                success: function (response) {
                    console.log('Template rendered is: ', response);
                    $("#textTemplateModal-alert").hide();

                    $("#textTemplateModal-controls").show();
                    $("#textTemplateModal-text").text(response);
                    $("#textTemplateModal-text").show();
                },
                error: function (err) {
                    $("#textTemplateModal-alertText").text(err.responseText);
                    $("#textTemplateModal-alert").show();

                    $("#textTemplateModal-controls").hide();
                    $("#textTemplateModal-text").hide();
                }
            });
        });

        // Append SIIAM ticket drop down listener
        $("#textTemplateModal-siiamdropdown").click(function() {
            let incidentId = $('#textTemplateModal-siiamdropdown').val();
            if (incidentId) {
                resetServiceCentralOptions();
                setAppendToTemplateAlert(AppendToTemplateStatus.info, "Appending current template to SIIAM ticket number '" + incidentId + "'");
            } else {
                $("#textTemplateModalAppendTicket-alert").hide();
            }
        });

        // Append Service Central incident drop down listener
        $("#textTemplateModal-sidropdown").click(function() {
            let incidentId = $('#textTemplateModal-sidropdown').val();
            if (incidentId) {
                resetSIIAMOptions();
                setAppendToTemplateAlert(AppendToTemplateStatus.info, "Appending current template to Service Central incident number '" + incidentId + "'");
            } else {
                $("#textTemplateModalAppendTicket-alert").hide();
            }
        });


        // Append SIIAM button listener
        $("#textTemplateModal-siiambutton").click(function() {
            let serviceCheckId = $('#inputTemplateName').attr('data-service-check-id');
            let templateName = $('#inputTemplateName').val();
            let ticketId = $('#textTemplateModal-siiamdropdown').val();

            if (templateName && serviceCheckId && ticketId) {
                const confirmText = "Click either OK or Cancel";
                if (confirm(confirmText) == true) {
                    setButtonSpinner($("#textTemplateModal-siiambutton"), "Append SIIAM", true);
                    let appendTicketPromise = $.Deferred();

                    runAppendTemplateToTicket(serviceCheckId, templateName, ticketId, "SIIAM", appendTicketPromise);

                    appendTicketPromise.then((response) => {
                        setAppendToTemplateAlert(AppendToTemplateStatus.success, "Successfully appended template " + templateName + " to Service Central incident " + ticketId);
                        $("#textTemplateModal-siiamdropdown option[value=" + ticketId + "]").remove();
                    }).fail((error) => {
                        let errorMessage = null;
                        if (error && error.responseJSON && typeof error.responseJSON.error === 'string') {
                            errorMessage = error.responseJSON.error;
                        }

                        setAppendToTemplateAlert(AppendToTemplateStatus.error, "Error appending SIIAM ticket " + ticketId + (errorMessage ? ` error: ${errorMessage}` : ''));
                    }).always(() => {
                        resetSIIAMOptions();
                        setButtonSpinner($("#textTemplateModal-siiambutton"), "Append SIIAM", false);
                    });
                }
                else {
                    resetAppendTemplateOptions();
                }
            } else {
                resetServiceCentralOptions();
                setAppendToTemplateAlert(AppendToTemplateStatus.error, "SIIAM ticket not selected, please select a ticket number from the drop down");
            }
        });

        // Append Service Central button listener
        $("#textTemplateModal-sibutton").click(function() {
            let serviceCheckId = $('#inputTemplateName').attr('data-service-check-id');
            let templateName = $('#inputTemplateName').val();
            let incidentId = $('#textTemplateModal-sidropdown').val();

            if (templateName && serviceCheckId && incidentId) {
                const confirmText = "Click either OK or Cancel";
                if (confirm(confirmText) == true) {
                    setButtonSpinner($("#textTemplateModal-sibutton"), "Append Service Central", true);
                    let appendTicketPromise = $.Deferred();

                    runAppendTemplateToTicket(serviceCheckId, templateName, incidentId, "ServiceCentral", appendTicketPromise);

                    appendTicketPromise.then((response) => {
                        setAppendToTemplateAlert(AppendToTemplateStatus.success, "Successfully appended template " + templateName + " to Service Central incident " + incidentId);
                        $("#textTemplateModal-sidropdown option[value=" + incidentId + "]").remove();
                    }).fail((error) => {
                        let errorMessage = null;
                        if (error && error.responseJSON && typeof error.responseJSON.error === 'string') {
                            errorMessage = error.responseJSON.error;
                        }

                        setAppendToTemplateAlert(AppendToTemplateStatus.error, "Error appending Service Central Incident " + incidentId + (errorMessage ? ` error: ${errorMessage}` : ''));
                    }).always(() => {
                        resetServiceCentralOptions();
                        setButtonSpinner($("#textTemplateModal-sibutton"), "Append Service Central", false);
                    });
                }
                else {
                    resetAppendTemplateOptions();
                }
            } else {
                resetSIIAMOptions();
                setAppendToTemplateAlert(AppendToTemplateStatus.error, "Service Central Incident not selected, please select an incident number from the drop down");
            }

        });

        $("#feedbackMessageInputCharCount").text(`${feedbackMessageCharacterLimit - $("#feedbackMessageInput").val().length}/${feedbackMessageCharacterLimit}`);

        $("#feedbackMessageInput").bind('input propertychange', function() {
            $("#feedbackMessageInputCharCount").text(`${feedbackMessageCharacterLimit - $("#feedbackMessageInput").val().length}/${feedbackMessageCharacterLimit}`);
        });

        $("#textTemplateModal-download").click(function() {
            const templateText = $("#textTemplateModal-text").text();
            const templateName = $("#inputTemplateName").val().replace(/[^a-z0-9]/gi, '_');
            const dateString = new Date().toISOString().replace(/[^0-9]/g, '');
            const fileName = `Text_Template_${templateName}_${dateString}.txt`;

            downloadToFile(fileName, templateText);
        });

        // Prevents service check restart form from submitting and redirecting as
        // service checks can just be restarted on the current page
        $(document).on('submit', '.service-check-restart', function(event) {
            event.preventDefault();

            let id = $(this).find("input[name=\"id\"]").val();
            restartServiceCheck(id);
        });

        $('html').on('click', function(event) {
            if ($(event.target).closest('.popover').length === 0) {
                $('.feedback-popover').popover('hide');
            }
        });

        $("#magpieSchematicZoom").change(function() {
            $("#magpieSchematicZoomValue").text(`${$("#magpieSchematicZoom").val()}%`);
            const scaleValue = parseInt($("#magpieSchematicZoom").val()) / 100;
            $("#magpieSchematic svg").css('transform', `scale(${scaleValue})`);
            $("#magpieSchematic svg").css('transform-origin', "0 0");
        });

        $("#magpieSchematicZoom").val(100);

        $("#magpieSchematicModalBody").on("wheel", function(event) {
            let currVal = parseInt($("#magpieSchematicZoom").val());
            if (event?.originalEvent?.deltaY < 0) {
                $("#magpieSchematicZoom").val(currVal + 5);
            } else if (event?.originalEvent?.deltaY > 0) {
                $("#magpieSchematicZoom").val(currVal - 5);
            }
            $("#magpieSchematicZoom").trigger("change");
        });

        // To update: New UI link
        $('#linkNewUi').attr('href', `${location.protocol}//${location.host}/serviceCheck/new`);
    });

    socket.on('connect_error', function() {
        // Just prints this to console for now, used for debugging in case it ever occurs
        console.error('socket.io connect_error event received');
    });

    socket.on('disconnect', function(reason) {
        console.error(`socket.io disconnect event received, any running service checks may not receive further updates to its state, reason: ${reason}`);

        // The modal will only be displayed for a ping timeout, as disconnects are possible when the server is restarting for an update
        // The other actions such as updating the status with the record link will still be done on all running service checks, no matter the reason.
        if (reason == 'ping timeout') {
            $("#socketDisconnectAlert").show();
        }

        for (let serviceCheckId of serviceChecksCurrent) {
            if (!serviceChecksCompleted.has(serviceCheckId)) {
                $(`#status-${serviceCheckId}`).html("");
                $(`#status-${serviceCheckId}`).append("(Error: network disconnected, view ");
                $(`#status-${serviceCheckId}`).append($("<a>").attr("href", `/serviceCheck/view/html/${serviceCheckId}`).attr("target", "blank").text("record"));
                $(`#status-${serviceCheckId}`).append(")");
            }
        }
    });

    //For Update Status (Completed/error/completedWithError)
    socket.on('SCStatus', function (SCid, status) {
        if (serviceChecksHidden.includes(SCid)) {
            return;
        }

        console.log(`#> ${SCid} status : ${status}`);
        serviceChecksCompleted.add(SCid);

        let displayStatus;
        switch(status) {
            case ServiceCheckStatus.done:
                displayStatus = 'Completed';
                break;
            case ServiceCheckStatus.completedWithError:
                displayStatus = 'Completed With Error';
                break;
            case ServiceCheckStatus.error:
                displayStatus = 'Error';
                break;
            default:
                // For statuses that aren't in the enumerated instances
                // These currently exist to inform the user that the input given was invalid
                // (eg. empty or invalid values)
                // TODO: Handle differently in the future
                displayStatus = status;
                break;
        }

        $(`#status-${SCid}`).text(`(${displayStatus})`);

        if (status === ServiceCheckStatus.done || status === ServiceCheckStatus.completedWithError) {
            setBackgroundColourSCBlock('summary', SCid, true);

            $(`#textTemplate-${SCid}`).removeAttr("data-disable-select");
            $(`#textTemplateBSIP-${SCid}`).removeAttr("data-disable-select");

            // If the modal for a running service check that has finished is open, enable the
            // template select element, otherwise the user will need to close and open the modal again
            if ($(`#inputTemplateName`).attr('data-service-check-id') === SCid) {
                $('#inputTemplateName').removeAttr('disabled');
                $('#showRulesData').removeAttr('disabled');
            }
        }

        $(`#feedback-popover-${SCid}`).popover({
            html: true,
            title: `Provide Feedback<div class="float-right"><button type="button" class="close" onclick="$(&quot;#feedback-popover-${SCid}&quot;).popover(&quot;hide&quot;);"><span>&times;</span></button></div>`,
            placement: 'top',
            sanitize: false,
            trigger: 'click',
            content: 'Please provide feedback for this service check by clicking the thumbs up (service check performed as expected) or thumbs down button (service check results are inaccurate / incorrect)',
            container: 'body'
        });

        $(`#feedback-popover-${SCid}`).popover('show');
    });

    socket.on('CollectDataStart', function (category, name, SCid, hideInResult) {
        if (serviceChecksHidden.includes(SCid)) {
            return;
        }

        //Only add source to the Result-Sources tab if hideInResult
        console.log(`#> Received CollectDataStart Source "${name}" hide "${hideInResult}" for SCid "${SCid}"`);

        let hideSource = !$('#showHiddenSources').prop('checked');
        addCollectedData(category, name, SCid, hideInResult, false, hideSource, false);
    });

    let magpieSchematicsShown = false;

    socket.on('CollectDataUpdate', function(cd) {
        if (cd && serviceChecksHidden.includes(cd.SCid)) {
            return;
        }

        let sourceName = cd.name;
        let sourceData = cd.data;
        let id = cd.SCid;

        //Only update source to the Result-Sources tab if hideInResult
        console.log('#> Received CollectDataUpdate Source "' + cd.name + '" for SCid "' + cd.SCid + '"');

        let hideSource = !$('#showHiddenSources').prop('checked');
        updateCollectedData(cd, hideSource, false);
        console.log(cd);

        if (MagpieSchematicsSourceNames.includes(sourceName) && typeof sourceData?.schematics === 'string') {
            let fnn = cd.metadata?.parameters?.fnn;

            const schematicsElement = $(sourceData.schematics);

            const rowElement = $('<tr>').html(
                $('<td>').attr('colspan', '100%').text(
                    `Magpie Schematics (${fnn}) `
                ).append(
                    $('<button>').addClass('btn btn-sm btn-secondary').attr('title', 'View schematics').html($('<span>').addClass('fas fa-external-link-alt')).click(function() {
                        $('#magpieSchematic').html(schematicsElement);
                        $('#magpieSchematicsModal').modal('show');

                        // In the <script> tag in schematics, the ht and wh values are reduced each time at the start
                        // which may affect the schematics if set again with .html()
                        // This implementation multiplies the current value to set it to its original
                        if (magpieSchematicsShown) {
                            ht *= 3.8;
                            wh *= 2;
                            SVGRoot.setAttribute('height', ht);
                            SVGRoot.setAttribute('width',  wh);
                        }

                        magpieSchematicsShown = true;

                        ShowTooltip = overrideShowTooltip;
                    })
                )
            );

            addToSCBlock('Summary', id, rowElement, 'prepend');
        }
    });

    // ruleCheckMsg
    socket.on('ruleCheckMsg', function(SCid, rule, ruleData) {
        if (serviceChecksHidden.includes(SCid)) {
            return;
        }

        // Stores rules data once received, so that searching will work on incoming data
        serviceChecksRulesData[SCid][rule.name] = ruleData;

        let originServiceCheckId = null;
        // Gets the origin service check ID from a generated service check ID
        // Not the most efficient, iterates through all origin service check IDs
        if (!(SCid in serviceChecksGenerated)) {
            for (let serviceCheckId in serviceChecksGenerated) {
                if (serviceChecksGenerated[serviceCheckId].includes(SCid)) {
                    originServiceCheckId = serviceCheckId;
                }
            }
        }

        let isVisible = true;
        if (ruleData.result === RuleResult.ok) {
            isVisible = $('#showOKRules').prop('checked');
        }

        // Displays rules with errors in the summary tab
        if (ruleData.result === RuleResult.error) {
            let ruleErrorRow = $('<tr>').addClass('rule rule-Error').html(
                $('<td>').attr('colspan', '100%').append(
                    $('<span>').attr('style', 'color:red').attr('title', 'Error').addClass('fas fa-unlink')
                ).append(
                    ` Rule ${rule.name} error: ${ruleData.error}`
                )
            );
            addToSCBlock('Summary', SCid, ruleErrorRow, 'prepend');
        }

        ruleCheckMsg(SCid, rule, ruleData, isVisible, false, originServiceCheckId);
    });

    //Source collection error
    socket.on('sourceError', function(SCid, sourceName, errorMessage) {
        if (serviceChecksHidden.includes(SCid)) {
            return;
        }

        console.log('#> Received Error for Source "' + sourceName + '" Msg "' + errorMessage + '"');
        let sourceErrorRow = $('<tr>').attr('class', 'rule rule-Error')
            .html($('<td>').attr('colspan', '100%').html(
                $('<span>').attr('style', 'color:red').attr('class', 'fas fa-unlink').attr('title', 'Error').append(
                    $('<a>').attr('target', sourceName).attr('href', `${wikiBaseURL}${sourceName}`).text(sourceName)
                ).append(` : ${errorMessage}`)
            ));

        addToSCBlock('Summary', SCid, sourceErrorRow, 'prepend', false);
    });

    // The service check record received from the socket here does not contain the
    // sourcesData or sourcesMetadata fields (to reduce payload size)
    socket.on('SCRecordUpdate', function (SCr) {
        if (serviceChecksHidden.includes(SCr.id)) {
            return;
        }

        console.log('#> Got Record Update for FNN "' + SCr.fnn + '" SCid "' + SCr.id + '"');
        updateHeaderInfo( SCr.id, 'header-top','FNN', SCr.fnn );
        updateHeaderInfo( SCr.id, 'header-middle','Carriage FNN', SCr.carriageFNN );
        updateHeaderInfo( SCr.id, 'header-middle', 'Device Name', SCr.deviceName );
        updateHeaderInfo( SCr.id, 'header-middle', 'CIDN', SCr.CIDN );
        updateHeaderInfo( SCr.id, 'header-bottom', 'Carriage Type', SCr.carriageType );

        if(SCr.rulesData && SCr.rulesData.MDR012 && SCr.rulesData.MDR012.lastActiveCaseID){
            updateHeaderInfo( SCr.id, 'header-bottom', 'SIIAM', SCr.rulesData.MDR012.lastActiveCaseID );
        }
        if(SCr.nbnAccessType){
            updateHeaderInfo( SCr.id, 'header-bottom', 'Access Type', SCr.nbnAccessType );
        }
        if(SCr.nbnId){
            updateHeaderInfo( SCr.id, 'header-bottom', 'NBN ID', SCr.nbnId );
        }
        if(SCr.rulesData && SCr.rulesData.MDR106 && SCr.rulesData.MDR106.deviceType){
            updateHeaderInfo( SCr.id, 'header-bottom', 'Device Type', SCr.rulesData.MDR106.deviceType );
        }
        if (SCr.serviceType) {
            updateHeaderInfo( SCr.id, 'header-bottom', 'Service Type', SCr.serviceType );
        }

        //Cust Name
        if (SCr.rulesData && SCr.rulesData.MDR005 && SCr.rulesData.MDR005.customerName) {
            updateHeaderInfo(SCr.id, 'header-bottom', 'Customer', SCr.rulesData.MDR005.customerName);
        }
        //Address
        if (SCr.address) {
            updateHeaderInfo( SCr.id, 'header-top', 'Address', SCr.address );
        }

        //Consent Offshore
        if (SCr.OffshoreResources) {
            if (SCr.OffshoreResources === 'No' || SCr.OffshoreResources === 'Conditional') {
                updateHeaderInfo(SCr.id, 'header-middle', 'Offshore Consent', SCr.OffshoreResources, 'text-warning');
            } else {
                updateHeaderInfo(SCr.id, 'header-middle', 'Offshore Consent', SCr.OffshoreResources);
            }
        }
        //Customer Consent
        if (SCr.rulesData && SCr.rulesData.MDR068 && SCr.rulesData.MDR068.customerConsent) {
            updateHeaderInfo(SCr.id, 'header-middle', 'Overall Consent', SCr.rulesData.MDR068.customerConsent)
        }

        // Number Ranges
        if (SCr.serviceType === "GH" &&
            SCr.rulesData &&
            SCr.rulesData.MDR203 &&
            Array.isArray(SCr.rulesData.MDR203.numberRanges) &&
            SCr.rulesData.MDR203.numberRanges.length > 0
        ) {
            addNumberRanges(SCr.id, SCr.rulesData.MDR203.numberRanges);
        }

    });

    socket.on('serviceCheckError', function(id, message) {
        $(`#error-${id}`).html(message);
        $(`#error-${id}`).show();
    });

    socket.on('serviceCheckDisableDisplayCondition', function(id, message) {
        $(`#error-${id}`).html(message);
        $(`#error-${id}`).show();

        $(`#status-${id}`).text('(Error: no access to view service)');
        $(`#pills-tabContent-${id}`).remove();
    });

    //Active icon of textTemplate when job is completed
    function enableExtTemplate(SCid) {
        $(`#textTemplate-${SCid}`).removeClass("invisible");
        $(`#textTemplateBSIP-${SCid}`).removeClass("invisible");
    }

    function runServiceCheck() {
        var vFNN = $("#fnn").val();
        var vRunOutageInfoCheck = $("#runOutageInfoCheck").prop("checked");
        var vSCLevel = $("#SCLevel").val();
        var vCarriageType = $("#carriageType").val();
        var vCarriageFNN = $("#carriageFNN").val();
        var vDeviceName = $("#deviceName").val();
        var vCIDN = $("#cidn").val();
        var vDeviceIP = $("#deviceIP").val();
        var vFieldInterfaceIP = $("#fieldInterfaceIP").val();
        var vAdborId = $("#adborId").val();
        var vLatitude = $("#latitude").val() ? parseFloat($("#latitude").val()) : null;
        var vLongitude = $("#longitude").val() ? parseFloat($("#longitude").val()) : null;
        var vDateFrom = $("#dateFrom").val() ? $("#dateFrom").val().replace(/\.\d+/, '') : null;
        var vDateTo = $("#dateTo").val() ? $("#dateTo").val().replace(/\.\d+/, '') : null;
        var vRuleNames = $("#ruleNames").val();
        var vSourceNames = $("#sourceNames").val();

        console.log('#> Check Clicked for FNN : ' + vFNN);

        //Check correct FNNs provided
        let fnnError = validateFNNs(vFNN, vCarriageFNN, vDeviceName);
        if (fnnError) {
            $("#inputErrorMessage").text(fnnError);
            $("#inputErrorAlert").show();
        } else {
            $("#carriageFNN").val('');
            $("#deviceName").val('');
            $("#cidn").val('');
            $("#deviceIP").val('');
            $("#fieldInterfaceIP").val('');
            $("#adborId").val('');
            $("#latitude").val('');
            $("#longitude").val('');
            $("#carriageType").val('');
            $("#dateFrom").val('');
            $("#dateTo").val('');

            localStorage.setItem('FNN', vFNN);
            localStorage.setItem('SCLevel', vSCLevel);
            $(document).prop('title', vFNN);

            var SCr = {
                input: {
                    searchFNN: vFNN,
                    suite: vRunOutageInfoCheck ? 'outageInfo' : 'standard',
                    level: vSCLevel,
                    carriageType: vCarriageType,
                    carriageFNN: vCarriageFNN,
                    deviceName: vDeviceName,
                    deviceIP: vDeviceIP,
                    fieldInterfaceIP: vFieldInterfaceIP,
                    adborId: vAdborId,
                    latitude: vLatitude,
                    longitude: vLongitude,
                    CIDN: vCIDN,
                    ruleNames: vRuleNames,
                    sourceNames: vSourceNames
                },
                additionalParameters: {
                    dateFrom: vDateFrom,
                    dateTo: vDateTo
                }
            };

            console.log('#> initiated SCr : ', SCr);
            socket.emit('serviceCheckStart', SCr);
        }

        // Hides all other feedback popovers that are currently shown
        $('.feedback-popover').popover('hide');
    }

    //Reset Text Template Modal Append Options
    function resetAppendTemplateOptions() {
        resetServiceCentralOptions();
        resetSIIAMOptions();
        $("#textTemplateModalAppendTicket-alert").hide();
    }

    //Reset Text Template Modal Append Service Central Options
    function resetServiceCentralOptions() {
        let templateName = $('#inputTemplateName').val();
        if (appendAllowed(templateName, 'ServiceCentral') && $('#textTemplateModal-sidropdown option').length > 1) {
            $("#textTemplateModal-sidropdown").prop("selectedIndex", 0);
            $('#textTemplateModal-sidropdown').show();
            $('#textTemplateModal-sibutton').show();
        } else {
            $('#textTemplateModal-sidropdown').hide();
            $('#textTemplateModal-sibutton').hide();
        }
    }

    //Reset Text Template Modal Append SIIAM ticket options
    function resetSIIAMOptions() {
        let templateName = $('#inputTemplateName').val();
        if (appendAllowed(templateName, 'SIIAM') && $('#textTemplateModal-siiamdropdown option').length > 1) {
            $("#textTemplateModal-siiamdropdown").prop("selectedIndex", 0);
            $('#textTemplateModal-siiamdropdown').show();
            $('#textTemplateModal-siiambutton').show();
        } else {
            $('#textTemplateModal-siiamdropdown').hide();
            $('#textTemplateModal-siiambutton').hide();
        }
    }

    //Display Text Template Modal Append Ticket Message Area
    function setAppendToTemplateAlert(status, textMessage) {
        $("#textTemplateModalAppendTicket-alert").hide();
        switch(status) {
            case AppendToTemplateStatus.error:
                $("#textTemplateModalAppendTicket-alert").removeClass("alert-info");
                $("#textTemplateModalAppendTicket-alert").removeClass("alert-success");
                $("#textTemplateModalAppendTicket-alert").addClass("alert-danger");
                break;
            case AppendToTemplateStatus.success:
                $("#textTemplateModalAppendTicket-alert").removeClass("alert-info");
                $("#textTemplateModalAppendTicket-alert").removeClass("alert-danger");
                $("#textTemplateModalAppendTicket-alert").addClass("alert-success");
                break;
            case AppendToTemplateStatus.info:
                $("#textTemplateModalAppendTicket-alert").removeClass("alert-success");
                $("#textTemplateModalAppendTicket-alert").removeClass("alert-danger");
                $("#textTemplateModalAppendTicket-alert").addClass("alert-info");
                break;
        }
        $("#textTemplateModalAppendTicket-alert").text(textMessage);
        $("#textTemplateModalAppendTicket-alert").show();
    }

    function restartServiceCheck(serviceCheckId) {
        // Returns on empty / null / undefined service check ID
        if (!serviceCheckId) {
            return;
        }

        let serviceCheckPromise = $.Deferred();

        $.ajax({
            cache: false,
            type: "GET",
            url: `/serviceCheck/view/json/${encodeURIComponent(serviceCheckId)}`,
            success: function (response) {
                serviceCheckPromise.resolve(response);
            },
            error: function (xhr, status, e) {
                serviceCheckPromise.reject(e);
            }
        });


        serviceCheckPromise.done((serviceCheckRecord) => {
            try {
                $("#fnn").val(serviceCheckRecord.input.searchFNN);
                $("#SCLevel").val(serviceCheckRecord.input.level);
                $("#carriageType").val(serviceCheckRecord.input.carriageType);
                $("#carriageFNN").val(serviceCheckRecord.input.carriageFNN);
                $("#deviceName").val(serviceCheckRecord.input.deviceName);
                $("#deviceIP").val(serviceCheckRecord.input.deviceIP);
                $("#fieldInterfaceIP").val(serviceCheckRecord.input.fieldInterfaceIP);
                $("#adborId").val(serviceCheckRecord.input.adborId);
                $("#latitude").val(serviceCheckRecord.input.latitude);
                $("#longitude").val(serviceCheckRecord.input.longitude);

                $("#cidn").val(serviceCheckRecord.CIDN);

                if (serviceCheckRecord.additionalParameters) {
                    $("#dateFrom").val(serviceCheckRecord.additionalParameters.dateFrom);
                    $("#dateTo").val(serviceCheckRecord.additionalParameters.dateTo);
                }

                // Repeated code, enables check button if FNN length is appropriate
                // as the "input" event is not triggered upon changing the value programatically
                $('#check').prop('disabled', $('#fnn').val().length <= 8);
                runServiceCheck();
            } catch(error) {
                $("#inputErrorMessage").text(`Error when parsing service check inputs to run again: ${error.toString()}`);
                $("#inputErrorAlert").show();
            }
        }).fail((error) => {
            $("#inputErrorMessage").text(`Error when obtaining service check to run again: ${error.toString()}`);
            $("#inputErrorAlert").show();
        });
    }
</script>
<%- include('footer', {}); %>
