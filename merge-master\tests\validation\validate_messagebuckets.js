
import assert from 'assert';
import _ from 'lodash';

import MessageBucket from '../../db/model/messageBucket.js';
import mergeConfigList from '../../modules/mergeConfigList.js';
import { MERGE_OBJECT_CONFIG_FILES } from '../../modules/helpers/constants.js';
import helpers from '../helpers.js';


describe('Merge Message Buckets', () => {
    it('Unique names', async() => {
        let messageBuckets = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.messageBucket)).messageBuckets;
        let messageBucketNames = new Set();

        for (let i in messageBuckets) {
            if (messageBucketNames.has(messageBuckets[i].name)) {
                assert.fail(`Duplicate message bucket name ${messageBuckets[i].name} in config`);
            }
            messageBucketNames.add(messageBuckets[i].name);
        };
    });

    it('Valid fields', async() => {
        let messageBuckets = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.messageBucket)).messageBuckets;

        for (let i in messageBuckets) {
            let messageBucket = new MessageBucket(messageBuckets[i]);
            try {
                await messageBucket.validate();
            } catch(error) {
                let newError = new Error(`Error validating message bucket ${messageBucket.name}`);
                newError.original_error = error;
                newError.stack = error.stack;
                throw newError;
            }
        }
    });

    it('Ordered by name ascending', async() => {
        let messageBuckets = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.messageBucket)).messageBuckets;

        for (let i = 1; i < messageBuckets.length; i++) {
            let prevMessageBucket = messageBuckets[i - 1];
            let currMessageBucket = messageBuckets[i];

            if (prevMessageBucket.name.localeCompare(currMessageBucket.name, 'en') > 0) {
                assert.fail(`Message bucket name ${prevMessageBucket.name} is out of order, should be after message bucket ${currMessageBucket.name}`);
            }
        }
    });

    it('Correct field order', async() => {
        let messageBuckets = (await mergeConfigList.getObjectsAndMetadataFromFile(MERGE_OBJECT_CONFIG_FILES.messageBucket)).messageBuckets;
        let invalidNames = [];

        for (let i in messageBuckets) {
            let messageBucketJson = new MessageBucket(messageBuckets[i]).toJSON();
            delete messageBucketJson.createdBy;
            delete messageBucketJson.createdOn;

            if (!_.isEqualWith(messageBuckets[i], messageBucketJson, helpers.compareObjectWithKeyOrder)) {
                invalidNames.push(messageBuckets[i].name);
            };
        }

        if (invalidNames.length) {
            assert.fail(`Key order for message bucket(s) ${invalidNames.join(',')} does not match database schema order`);
        }
    });
});

