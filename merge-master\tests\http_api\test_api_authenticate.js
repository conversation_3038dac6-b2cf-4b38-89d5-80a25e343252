'use strict';

import mongoose from 'mongoose';
import chai from 'chai';
import chaiHttp from 'chai-http';
import chaiLike from 'chai-like';

import superagentDefaults from 'superagent-defaults';
import supertest from 'supertest';
import _ from 'lodash';


var app;
var request;
import config from '../config.js';
import { UNIT_TEST_ALL_ACCESS_USERNAME, UNIT_TEST_PASSWORD } from '../users.js';

const should = chai.should();

chai.use(chaiLike);
chai.use(chaiHttp);


describe('Merge API: Authenticate', () => {
    // Setup and connect to mongodb memory server, start merge app instance
    // Set request variable with a configured supertest agent with test JWT and app instance
    before(async function() {
        global.gConfig = _.cloneDeep(config.default);
        app = (await import('../../app.js')).default;
        request = superagentDefaults(supertest(app));
    });

    // Drop all contents of database before each test
    beforeEach(async function() {
        global.gConfig = _.cloneDeep(config.default);
        for (let collectionName in mongoose.connection.collections) {
            await mongoose.connection.collections[collectionName].deleteMany({});
        }
    });

    it('No credentials', async() => {
        let res = await request.post('/api/authenticate');

        res.should.have.status(400);
    });

    it('Incorrect credentials', async() => {
        let res = await request.post('/api/authenticate')
            .send(`username=${UNIT_TEST_ALL_ACCESS_USERNAME}&password=badpassword`)
            .type('form')

        res.should.have.status(401);
    });

    it('Correct credentials', async() => {
        let res = await request.post('/api/authenticate')
            .send(`username=${UNIT_TEST_ALL_ACCESS_USERNAME}&password=${UNIT_TEST_PASSWORD}`)
            .type('form')

        res.should.have.status(200);
    });

    it('Correct credentials (JSON)', async() => {
        let res = await request.post('/api/authenticate').send({
            username: UNIT_TEST_ALL_ACCESS_USERNAME,
            password: UNIT_TEST_PASSWORD
        });

        res.should.have.status(200);
    });

    it('Invalid credentials (empty strings)', async() => {
        let res = await request.post('/api/authenticate').send({
            username: '',
            password: ''
        });

        res.should.have.status(400);
    });

    it('Invalid credentials (incorrect types)', async() => {
        let res = await request.post('/api/authenticate').send({
            username: null,
            password: { Instance: 'Object' }
        });

        res.should.have.status(400);
    });

    it('Invalid JSON payload', async() => {
        let res = await request.post('/api/authenticate')
            .send('{\'username\': \'username\' \'password\': \'password\'}')
            .type('json')

        res.should.have.status(400);
    });
});
