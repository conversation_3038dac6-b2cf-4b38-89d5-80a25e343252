{"title": "Outcome", "type": "object", "id": "Outcome", "headerTemplate": "{{ self.name }}", "options": {"disable_collapse": true, "disable_edit_json": false, "disable_properties": true, "disable_array_add": true, "disable_array_delete": true, "disable_array_reorder": true}, "properties": {"name": {"title": "Name", "type": "string", "minLength": 1, "readonly": false}, "title": {"title": "Title", "type": "string", "description": "", "minLength": 0}, "description": {"title": "Description", "type": "string", "description": "", "minLength": 0}, "header": {"title": "Header", "type": "string", "description": "", "minLength": 0}, "display": {"title": "Display", "type": "boolean", "format": "checkbox", "default": false}, "tier": {"title": "Tier", "type": "integer", "description": "Outcome tier to determine message bucket to use (lower tiers are higher priority), take precendent over weight", "enum": [0, 1, 2, 3, 4, 5, 6, 7], "default": 7}, "weight": {"title": "Weight", "type": "integer", "description": "Outcome weight to determine message bucket to use (higher weights are higher priority)", "default": 0}, "messageBucketName": {"title": "Message Bucket Name", "type": ["string", "null"], "enum": [null], "default": null}}}