
import { useState } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faCheck, faCopy } from '@fortawesome/free-solid-svg-icons'

import { DetailField } from '../../../../components/detailsPanel/DetailField'
import { Panel } from '../../../../components/panel/Panel'
import { formatDate } from '../../../../helpers/formatDate'
import { useAppState } from '../../../../hooks/useAppState'
import { FeedbackButton } from '../../../feedbackForm/FeedbackButton'
import styles from './ServiceCheckDetailsPanel.module.scss'

export const ServiceCheckDetailsPanel = () => {
    const { appState } = useAppState()
    const [hasCopied, setHasCopied] = useState<boolean>(false)

    const handleCopyToClipboard = async () => {
        if (appState.id) {
            let serviceCheckUrl =`${location.protocol}//${location.host}${location.pathname}?${new URLSearchParams({ id: appState.id }).toString()}`;
            try {
                await navigator.clipboard.writeText(serviceCheckUrl)
                setHasCopied(true)
            } catch {
                setHasCopied(false)
            }
        }
    }

    return (
        <Panel>
            <>
                <div
                    style={{textAlign: 'right'}}
                >
                    <button
                        className={styles.copyToClipboardButton}
                        onClick={handleCopyToClipboard}
                    >
                        {!hasCopied ? (
                            <FontAwesomeIcon icon={faCopy} />
                        ) : (
                            <FontAwesomeIcon icon={faCheck} />
                        )}
                        <span>Copy Service Check Link</span>
                    </button>
                </div>

                <DetailField label={'Created By'} value={appState.createdBy} />
                <DetailField
                    label={'Created On'}
                    value={formatDate(appState.createdOn)}
                />
                <FeedbackButton />
            </>
        </Panel>
    )
}
