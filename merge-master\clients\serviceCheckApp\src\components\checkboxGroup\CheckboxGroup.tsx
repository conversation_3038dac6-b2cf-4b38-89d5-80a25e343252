import { Checkbox } from '@able/react'
import styles from './CheckboxGroup.module.scss'
interface CheckboxGroupItem {
    name: string
    label: string
    checked: boolean
    customContent?: React.ReactNode
}

interface CheckboxGroupProps {
    selectAllLabel?: string
    isSelectAll?: boolean
    items?: CheckboxGroupItem[]
    onItemChange: (name: string, checked: boolean) => void
    onSelectAllChange?: () => void
    selectAllChecked?: boolean
}

export const CheckboxGroup = ({
    selectAllLabel,
    isSelectAll = false,
    items = [],
    onItemChange,
    onSelectAllChange,
    selectAllChecked = false,
}: CheckboxGroupProps) => {
    const handleSelectAllChange = () => {
        if (isSelectAll && onSelectAllChange) {
             onSelectAllChange()
        }
    }

    return (
        <div className={styles.checkboxGroup}>
            {isSelectAll && (
                <Checkbox
                    label={selectAllLabel ?? 'Select All'}
                    name="select-all"
                    checked={selectAllChecked}
                    events={{
                        onChange: handleSelectAllChange,
                    }}
                />
            )}
            {items.map((item) => (
                <div className={styles.checkboxItem} key={item.name}>
                    <Checkbox
                        label={item.label}
                        name={item.name}
                        checked={item.checked}
                        events={{
                            onChange: () =>
                                onItemChange(item.name, !item.checked),
                        }}
                    />
                    {item.customContent}
                </div>
            ))}
        </div>
    )
}
