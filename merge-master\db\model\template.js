
import mongoose from 'mongoose';

const textTemplateSchema = new mongoose.Schema({
    name: { type: String, default: null, minLength: 1, unique: true, required: true, immutable: true },
    active: { type: Boolean, default: false },
    title: { type: String, default: '' },
    description: { type: String, default: '' },
    allowedSystemsToAppend: {
        type: [{
            type: String,
            enum: [
                'SIIAM',
                'ServiceCentral'
            ]
        }],
        default: [],
        validate: {
            validator: function(allowedSystemsToAppend) {
                return allowedSystemsToAppend.length === new Set(allowedSystemsToAppend).size;
            },
            message: 'array should only contain unique string values'
        }
    },
    suite: {
        type: [{
            type: String,
            enum: [
                'standard',
                'andig',
                'ipvoice',
                'outageInfo',
                'wholesale'
            ]
        }],
        default: [],
        validate: {
            validator: function(suite) {
                return suite.length === new Set(suite).size;
            },
            message: 'array should only contain unique string values'
        }
    },
    templateType: { type: String, enum: [
        'Service Check',
        'Module'
    ], default: 'Service Check' },
    formatType: { type: String, enum: [
        'Text',
        'HTML'
    ], default: 'Text' },
    listCondition: { type: String, default: '' },
    defaultPriority: { type: Number, default: 0, min: 0, validate: { validator: Number.isInteger }},
    template: { type: String, default: '' },
    createdBy: { type: String, required: true, immutable: true, default: 'unknown' },
    createdOn: { type: Date, required: true, immutable: true, default: Date.now }
});


textTemplateSchema.methods.toJSON = function() {
    let obj = this.toObject();
    delete obj._id;
    delete obj.__v;
    return obj;
}


export default mongoose.model('template', textTemplateSchema);
