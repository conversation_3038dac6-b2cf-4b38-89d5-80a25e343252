{"aoTemp": {"username": "aoTemp", "name": "API Test 1", "SSU": "NONSP", "department": "Rapid", "mail": "<EMAIL>", "readOnly": false, "level0": true, "level1": false, "level2": false, "level3": false, "isAdmin": false, "isDeveloper": true, "levelLead": false, "apiAccess": true, "commandAccess": true, "isRobot": false, "offshoreAccess": false, "outsideAustralia": false, "sourceAPIAccess": false, "isWholesaleUser": false, "passHash": "$6$k8jJH.fBUZ1QZ9TC$qh/rG2nW9Q2Rh7OJcX1kosWp1kIeLmEkuqhwl7Qk5lViOhnI/LRmHDOQXBYK2VyGS66EGIGoKRVRL4WVy741Q/"}, "tconnectRobot": {"username": "tconnectRobot", "name": "T-Connect", "SSU": "NONSP", "department": "Rapid", "mail": "<EMAIL>", "readOnly": false, "level0": true, "level1": false, "level2": false, "level3": false, "isAdmin": false, "isDeveloper": true, "levelLead": false, "apiAccess": true, "commandAccess": true, "isRobot": false, "offshoreAccess": true, "outsideAustralia": false, "sourceAPIAccess": true, "isWholesaleUser": false, "passHash": "$6$jayMqJaUEkebzfAw$DoDQRZx1ADRJWDWd0b.HnUpk9zzOE4vSEfiO1jEE3GO.HrXhei0M/3FJsIxNVkRw8StzDlQwNqUCQnTr6DzyH."}, "prometheusBlackbox": {"username": "prometheusBlackbox", "name": "Prometheus Blackbox API User", "SSU": "NONSP", "department": "Rapid", "mail": "", "readOnly": false, "level0": true, "level1": false, "level2": false, "level3": false, "isAdmin": false, "isDeveloper": false, "levelLead": false, "apiAccess": true, "commandAccess": false, "isRobot": false, "offshoreAccess": false, "outsideAustralia": false, "sourceAPIAccess": false, "isWholesaleUser": false, "passHash": "$6$mXh8akLaSeBk7scD$xtboL3JKTE918avecybnYwFFUhCaYMqcTgFmNJPziSpRwFNywPlbgDuqqiFHmMgsRQb4dKwL6RHPiCjgm7i15."}, "Fusion": {"username": "fusion", "name": "Fusion Robot", "SSU": "NONSP", "department": "Rapid", "mail": "<EMAIL>", "readOnly": false, "level0": true, "level1": false, "level2": false, "level3": false, "isAdmin": false, "isDeveloper": true, "levelLead": false, "apiAccess": true, "commandAccess": true, "isRobot": false, "offshoreAccess": true, "outsideAustralia": false, "sourceAPIAccess": true, "isWholesaleUser": false, "passHash": "$6$Ad2u.h94oZMNbjo9$60rlqomhlrjhBY/Ez1ieJmX9UFNqCsGpglgQnCnp19FuPw39oA32qdSm0AXbxxS.0e.y5T1p48XL/YdvJmW3o."}, "gbsivr": {"username": "gbsivr", "name": "GBS IVR Test", "SSU": "NONSP", "department": "Rapid", "mail": "<EMAIL>", "readOnly": false, "level0": true, "level1": false, "level2": false, "level3": false, "isAdmin": false, "isDeveloper": true, "levelLead": false, "apiAccess": true, "commandAccess": true, "isRobot": false, "offshoreAccess": false, "outsideAustralia": false, "sourceAPIAccess": false, "isWholesaleUser": false, "passHash": "$6$.4XygIbZZXbFN7qO$XztHq037MHd.i4Lgsuk8OccjpP2SlqECvHhISNZj7nWW2clZRKnKK8dMVHKA4eGKUK2NrTPpcj.Pi46L90t45/"}, "mergeExternalCollector": {"username": "mergeExternalCollector", "name": "Merge External Collector User", "SSU": "NONSP", "department": "Rapid", "mail": "", "readOnly": false, "level0": true, "level1": false, "level2": false, "level3": false, "isAdmin": false, "isDeveloper": true, "levelLead": false, "apiAccess": true, "commandAccess": true, "isRobot": false, "offshoreAccess": true, "outsideAustralia": false, "sourceAPIAccess": true, "isWholesaleUser": false, "passHash": "$6$79yLBX4tzpmLG19l$f2nU8o5Z0fUTEUqzOCZ2MCCV1FTySRpvMiBJppY5Kd4HmS5IFIhJIb2wQQVe.D0xQFMSEOVJw/BXzSlGFNUsb/"}, "virtualsme": {"username": "virtualsme", "name": "Virtual SME", "SSU": "NONSP", "department": "Rapid", "mail": "<EMAIL>", "readOnly": false, "level0": true, "level1": false, "level2": false, "level3": false, "isAdmin": false, "isDeveloper": true, "levelLead": false, "apiAccess": true, "commandAccess": true, "isRobot": false, "offshoreAccess": false, "outsideAustralia": false, "sourceAPIAccess": false, "isWholesaleUser": false, "passHash": "$6$XoR0SdcuiHe8dYil$aslbb7bvZJQqO9lIMCrh.6qOGq.TIVUWT7yqiHO7Oaj1g2VvhAmryhvfL1ss13zOZBn82aXkCXI.oKdLxPJoG."}}