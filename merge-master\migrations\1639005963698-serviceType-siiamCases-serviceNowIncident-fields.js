/**
 * Note: This migration file was created when 'migrate-mongoose' was the npm package
 * used to perform MongoDB migrations, since the 'migrate' npm package is now used
 * the implementation may not be compatible and all functionality is commented out
 */

// const mongoose = require('mongoose');

// const migrate = require('./migrate');

// const serviceCheckModel = new mongoose.Schema({
//     id: { type: String, index: true, immutable: true },
//     fnn: { type: String, index: true },
//     phoneNumber: { type: String, index: true, default: null },
//     billingFNN: String,
//     carriageFNN: String,
//     carriageFNNBackup: String,
//     MDNFNN: String,
//     carriageType: String,
//     nbnServiceType: String,
//     nbnAccessType: String,
//     nbnSubAccessType: String,
//     deviceName: String,
//     CIDN: String,
//     address: String,
//     OffshoreResources: String,
//     serviceType: { type: String, default: null },
//     siiamCases: {
//         type: [{
//             type: String
//         }],
//         default: []
//     },
//     serviceNowIncidents: {
//         type: [{
//             type: String
//         }],
//         default: []
//     },
//     status: String,
//     createdBy: { type: String, required: true, immutable: true, default: "unknown", index: true },
//     createdOn: { type: Date, required: true, immutable: true, default: Date.now, index: true },
//     endedOn: Date,
//     durationMilliSec: Number,
//     input: {
//         searchFNN: String,
//         carriageType: String,
//         carriageFNN: String,
//         deviceName: String,
//         deviceIP: String,
//         level: Number,
//         process: String,
//         suite : String,
//         idType : String
//     },
//     additionalParameters: Object,
//     rulesData: Object,
//     sourcesData: Object,
//     sourcesMetadata: Object,
//     saveError: String
// }, { minimize: false });

// const ServiceCheckModel = mongoose.model('servicechecks', serviceCheckModel);

/**
 * Make any changes you need to make to the database here
 */
async function up () {
    // Write migration here
    // await migrate.connect();
    // for await (const record of ServiceCheckModel.find({})) {
    //     if (record.rulesData && record.rulesData.MDR004 && record.rulesData.MDR004.serviceType) {
    //         record.serviceType = record.rulesData.MDR004.serviceType;
    //     }

    //     if (record.sourcesData) {
    //         if (record.sourcesData.SIIAMfnn && record.sourcesData.SIIAMfnn.activeCases) {
    //             let siiamCases = [];

    //             Object.keys(record.sourcesData.SIIAMfnn.activeCases).forEach(id => {
    //                 siiamCases.push(id);
    //             });

    //             record.siiamCases = siiamCases.sort();
    //         } else {
    //             record.siiamCases = [];
    //         }

    //         if (record.sourcesData['Hangar - Service central incident API']) {
    //             let serviceNowIncidents = [];

    //             Object.keys(record.sourcesData['Hangar - Service central incident API']).forEach(id => {
    //                 if (record.sourcesData['Hangar - Service central incident API'][id] &&
    //                     record.sourcesData['Hangar - Service central incident API'][id].active == '1') {
    //                     serviceNowIncidents.push(id);
    //                 }
    //             });

    //             record.serviceNowIncidents = serviceNowIncidents.sort();
    //         } else {
    //             record.serviceNowIncidents = [];
    //         }
    //     }

    //     await record.save();
    // }

    // // Sets serviceType fields to null explicitly
    // await ServiceCheckModel.updateMany({"serviceType": {$exists: false}}, {
    //     $set: { "serviceType": null }
    // }, { multi: true });
}

/**
 * Make any changes that UNDO the up function side effects here (if possible)
 */
async function down () {
    // await migrate.connect();
    // for await (const record of ServiceCheckModel.find({})) {
    //     record.serviceType = undefined;
    //     record.siiamCases = undefined;
    //     record.serviceNowIncidents = undefined;

    //     await record.save();
    // }
}

module.exports = { up, down };
