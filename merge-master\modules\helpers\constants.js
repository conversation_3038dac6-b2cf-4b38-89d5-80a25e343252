
export const PROXY_AUTH_ERROR_MESSAGE = 'tunneling socket could not be established, statusCode=407';


export const SOURCE_NAME_PREFIXES_MUTABLE = Object.freeze([
    "InRequest-"
]);


export const SOURCE_STATUS_ERROR_MESSAGE_EXCLUSIONS = Object.freeze([
    /^Error: Request failed with status code 404$/,
    /^Error: Poll condition not met$/,
]);


export const SOURCE_STATUS_NETWORK_ERROR_MESSAGE_EXCLUSIONS = Object.freeze([
    /^Error: Request failed with status code 3\d{2}/,
    /^Error: Request failed with status code 4\d{2}/,
    /^Error: Request failed with status code 5\d{2}/,
    /^Error: API error condition met$/,
    /^Error: read ECONNRESET/,
    /^Error: timeout of/,
    /^Error: Timeout of/,
    /^Error: Client network/,
    /^Error: socket/,
    /^Error: connect ECONNREFUSED/,
    /^Error: connect ETIMEDOUT/,
    /^Error: tunneling socket could not be established,/
]);


export const MERGE_OBJECT_CONFIG_FILES = Object.freeze({
    api: "./config/APIs.json",
    messageBucket: "./config/messageBuckets.json",
    outcome: "./config/outcomes.json",
    ruleSource: "./config/SCRules.json",
    textTemplate: "./config/textTemplates.json"
});


export default {
    MERGE_OBJECT_CONFIG_FILES: MERGE_OBJECT_CONFIG_FILES,
    PROXY_AUTH_ERROR_MESSAGE: PROXY_AUTH_ERROR_MESSAGE,
    SOURCE_NAME_PREFIXES_MUTABLE: SOURCE_NAME_PREFIXES_MUTABLE,
    SOURCE_STATUS_ERROR_MESSAGE_EXCLUSIONS: SOURCE_STATUS_ERROR_MESSAGE_EXCLUSIONS,
    SOURCE_STATUS_NETWORK_ERROR_MESSAGE_EXCLUSIONS: SOURCE_STATUS_NETWORK_ERROR_MESSAGE_EXCLUSIONS
};
