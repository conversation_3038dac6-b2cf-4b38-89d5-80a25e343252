/**
 * Note: This migration file was created when 'migrate-mongoose' was the npm package
 * used to perform MongoDB migrations, since the 'migrate' npm package is now used
 * the implementation may not be compatible and all functionality is commented out
 */

// const mongoose = require('mongoose');

// const migrate = require('./migrate');


// const actionSchema = new mongoose.Schema({
//     api: { type: String, required: true },
//     parameterCode: { type: String, default: "" },
//     codeCondition: { type: String, default: "" },
//     autoExecution: { type: Boolean, default: false }
// }, { _id: false });

// const RuleModel = new mongoose.Schema({
//     name: { type: String, validate: /^[A-Z]{3}[0-9]{3}$/, unique: true, required: true, immutable: true },
//     active: { type: Boolean, default: false },
//     hideRule: { type: Boolean, default: false },
//     showReject: { type: Boolean, default: false },
//     nonBlocking: { type: <PERSON>olean, default: false },
//     title: { type: String, default: "" },
//     rootCauseCategory: { type: String, enum: [
//         "Administration",
//         "CommPilot",
//         "Configuration",
//         "Connection",
//         "Data Collection",
//         "Planned Outage",
//         "Sable",
//         "Service Status",
//         "Telstra Network",
//         "Test Team",
//         "3rd Party Network",
//         "Ticket",
//         "Unplanned Outage"
//     ], default: "Administration" },
//     categoryColour: { type: String, default: "#5bc0de" },
//     level: { type: Number, default: 0, min: 0, max: 6 },
//     description: { type: String, default: "" },
//     wikiPage: { type: String, default: "" },
//     ruleType: { type: String, enum: [
//         "Logical",
//         "Extract",
//         "Action"
//     ], default: "Logical" },
//     isWarning: { type: Boolean, default: false },
//     displayInSummary: { type: Boolean, default: false },
//     failedInSummary: { type: Boolean, default: true },
//     preSources: {
//         type: [{
//             type: String,
//             validate: /^(?!InRequest-.*)[A-Za-z]+[:\w\-\.\s]*$/
//         }],
//         default: []
//     },
//     preSourcesRunOnFail: { type: Boolean, default: false },
//     preRules: {
//         type: [{
//             type: String,
//             validate: /^[A-Z]{3}[0-9]{3}$/
//         }],
//         default: []
//     },
//     preRulesRunOnFail: { type: Boolean, default: false },
//     preCondition: { type: String, default: "" },
//     preConditionMsg: { type: String, default: "" },
//     trueMsg: { type: String, default: "" },
//     falseMsg: { type: String, default: "" },
//     errorMsg: { type: String, default: "" },
//     ruleCode: { type: String, default: "" },
//     valueMsgStm: { type: String, default: "" },
//     extraInfo: { type: String, default: "" },
//     ignoreCompare: { type: Boolean, default: false },
//     overrideCompare: { type: String, default: "" },
//     unitTests: {
//         type: [{
//             type: mongoose.Schema.Types.Mixed
//         }],
//         default: []
//     },
//     action: {
//         type: actionSchema,
//         default: null
//     },
//     createdBy: { type: String, required: true, immutable: true, default: "unknown" },
//     createdOn: { type: Date, required: true, immutable: true, default: Date.now }
// }, { minimize: false });

// const SourceModel = new mongoose.Schema({
//     name: { type: String, validate: /^(?!InRequest-.*)[A-Za-z]+[:\w\-\.\s]*$/, unique: true, required: true, immutable: true },
//     active: { type: Boolean, default: false },
//     nonBlocking: { type: Boolean, default: false },
//     title: { type: String, validate: /^([A-Za-z]+[:\w\-\.\s]*|)$/, default: "" },
//     level: { type: Number, default: 0, min: 0, max: 6 },
//     description: { type: String, default: "" },
//     wikiPage: { type: String, default: "" },
//     suite: {
//         type: [{
//             type: String,
//             enum: [
//                 "standard",
//                 "wireless",
//                 "ipvoice",
//                 "outageInfo",
//                 "wholesale"
//             ]
//         }],
//         default: ["standard"]
//     },
//     api: { type: String, required: true },
//     param: { type: String, default: "" },
//     dependToSources: {
//         type: [{
//             type: String,
//             validate: /^(?!InRequest-.*)[A-Za-z]+[:\w\-\.\s]*$/
//         }],
//         default: []
//     },
//     preSourcesRunOnFail: { type: Boolean, default: false },
//     dependedToRules: {
//         type: [{
//             type: String,
//             validate: /^[A-Z]{3}[0-9]{3}$/
//         }],
//         default: []
//     },
//     preRulesRunOnFail: { type: Boolean, default: false },
//     conditionToCollect: { type: String, default: "" },
//     overrideErrorCondition: { type: String, default: "" },
//     errorMessage: { type: String, default: "" },
//     async: { type: Boolean, default: false },
//     defaultDelayBetweenCall: { type: Number, default: null },
//     delayForNextCall: { type: String, default: "" },
//     maxNumCall: { type: Number, default: null },
//     createdBy: { type: String, required: true, immutable: true, default: "unknown" },
//     createdOn: { type: Date, required: true, immutable: true, default: Date.now, }
// }, { minimize: false });


// const Rule = mongoose.model("rule", RuleModel);
// const Source = mongoose.model("source", SourceModel);

/**
 * Make any changes you need to make to the database here
 */
async function up () {
    // Unsets fields from rules and sources that are no longer used
    // await migrate.connect();

    // await Rule.updateMany({}, {
    //     $unset: {
    //         "action.timeout": "",
    //         displayBlock: ""
    //     }
    // }, { multi: true, strict: false });

    // await Source.updateMany({}, {
    //     $unset: {
    //         doneCcondition: 1,
    //         errorCondition: 1,
    //         timeout: 1
    //     }
    // }, { multi: true, strict: false });
}

/**
 * Make any changes that UNDO the up function side effects here (if possible)
 */
async function down () {
    // No reverse migration as unused fields are deleted
}

module.exports = { up, down };
