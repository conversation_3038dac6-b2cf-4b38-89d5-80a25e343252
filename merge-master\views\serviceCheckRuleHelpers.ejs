<script>
    // Shows service check comparison between two records
    function showCompareResults(currentServiceCheckId, compareServiceCheckId) {
        console.log("showCompareResults currentServiceCheckId: " + currentServiceCheckId + " compareServiceCheckId: " + compareServiceCheckId);
        window.open("/compareResults/view?" + $.param({
            id1: currentServiceCheckId,
            id2: compareServiceCheckId
        }));
    }

    // Shows T-Live Grafana graph for passed vaiables
    function tliveGrafanaGraph(startTime, deviceId, instanceName, variableName, stepTime, titleGraph) {
        console.log("tliveGrafanaGraph SC Params st: " + startTime + " di: " + deviceId + " in: " + instanceName + " vn: " + variableName + " st: " + stepTime + " dn: " + titleGraph);
        window.open("https://tlive.in.telstra.com.au/grafana/d-solo/bps/bps?" + $.param({
            "orgId": 1,
            "panelId": 1,
            "theme": "light",
            "from": `now-${startTime}`,
            "to": "now-1h",
            "var-graphUid": "",
            "var-deviceId": deviceId,
            "var-category": "interface",
            "var-instance": instanceName,
            "var-variable": variableName,
            "var-minStep": stepTime,
            "var-title": titleGraph,
            "var-source": "influx"
        }));
    }

    // Shows Splunk T-Live dashboard for passed variables
    function splunkTLiveDashboard(inputHour, inputDevice, inputLink, inputFilter) {
        console.log("splunkTLiveDashboard SC Params ih: " + inputHour + " id: " + inputDevice + " il: " + inputLink + " if: " + inputFilter);
        window.open("https://nextgen.splunk.in.telstra.com.au/en-GB/app/tlive/tlive__packet_loss_monitoring?" +
            "form.metrics=ifInDiscards&form.metrics=ifInErrors&form.metrics=ifOutDiscards&form.metrics=ifOutErrors&form.metrics=ifOutOctets&form.metrics=ifInOctets&" +
            $.param({
                "form.listDevices": "",
                "latest": "now",
                "form.tok_in_bw_util_perc_gt": 0.5,
                "form.tok_out_bw_util_perc_gt": 0,
                "form.tok_in_pkt_loss_perc_gt": 0,
                "form.tok_out_pkt_loss_perc_gt": 0,
                "form.field1": "All",
                "form.tok_input_time.earliest": "@d",
                "form.tok_input_time.latest": "now",
                "form.unit": 1,
                "form.displaytotlbw": "table _time, \"In traffic (*bps)\", \"Out traffic (*bps)\"",
                "form.detailedmetricstime.latest": "now",
                "form.tok_input_domain": "*",
                "earliest": `-${inputHour}@h`,
                "form.detailedmetricstime.earliest": `-${inputHour}@h`,
                "form.tok_input_device": inputDevice,
                "form.tok_input_link": inputLink,
                "form.filter": `${inputFilter}=\"Y\"`
            })
        );
    }

    // Shows Splunk VOIP data explorer
    function splunkVOIPDataExplorer(fnn1) {
        console.log("splunkVOIPDataExplorer SC Params fnn1: " + fnn1);
        window.open("https://nextgen.splunk.in.telstra.com.au/en-GB/app/sable/voip__cdr?" + $.param({
            "form.tok_direction": "Calling_FNN",
            "form.tok_fnn_2": "*",
            "form.tok_timerange_picker.earliest": "-30d@d",
            "form.tok_timerange_picker.latest": "now",
            "form.tok_fnn_1": fnn1
        }));
    }

    // Shows Grafana live monitor
    function faeGrafanaLiveMonitor() {
        // Deprecated
    }
</script>