
import _ from 'lodash';
import ivm from 'isolated-vm';

function defaultRuleDataComparison(ruleData1, ruleData2) {
    let valueMsg1 = _.get(ruleData1, ["valueMsg"]);
    let valueMsg2 = _.get(ruleData2, ["valueMsg"]);
    return valueMsg1 === valueMsg2;
}


async function isEqual(ruleData1, ruleData2, overrideCompare) {
    let ruleDataEqual;

    if (typeof overrideCompare === 'string' && overrideCompare) {
        const isolate = new ivm.Isolate({
            memoryLimit: 64
        });
        const compareFunctionContext = await isolate.createContext();

        try {
            await compareFunctionContext.eval(overrideCompare, { timeout: 2000 });

            await compareFunctionContext.global.set('ruleData1', new ivm.ExternalCopy(ruleData1).copyInto());
            await compareFunctionContext.global.set('ruleData2', new ivm.ExternalCopy(ruleData2).copyInto());

            ruleDataEqual = await compareFunctionContext.eval('compare(ruleData1, ruleData2);', { timeout: 2000 }) ? true : false;
        } finally {
            compareFunctionContext.release();
            if (!isolate.isDisposed) {
                isolate.dispose();
            }
        }
    } else {
        ruleDataEqual = _.isEqualWith(ruleData1, ruleData2, defaultRuleDataComparison);
    }

    return ruleDataEqual;
}

export default {
    isEqual: isEqual
};
