import { Surface } from '@able/react'
import classNames from 'classnames'
import styles from './Panel.module.scss'

interface PanelProps {
    children: React.ReactElement
    background?: MaterialTypes
    className?: string
}

type MaterialTypes =
    | 'materialBasePrimary'
    | 'materialBaseSecondary'
    | 'materialBaseTertiary'
    | 'materialBaseBrandPrimary'
    | 'materialBaseBrandSecondary'
    | 'materialBaseBrandTertiary'
    | 'materialBaseBrandQuaternary'
export const Panel = ({
    children,
    background = 'materialBaseSecondary',
    className,
}: PanelProps) => {
    return children ? (
        <Surface
            variant={'SurfaceFlat'}
            background={background}
            className={classNames(styles.panel, className)}
        >
            {children}
        </Surface>
    ) : null;
}
