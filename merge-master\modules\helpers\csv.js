
import moment from 'moment';
import { Parser } from 'json2csv';
import { Readable } from 'stream';


const SERVICE_CHECK_FIELDS = Object.freeze([{
    label: 'FNN',
    value: 'fnn'
},
{
    label: 'Status',
    value: 'status'
},
{
    label: 'Start Method',
    value: 'startMethod'
},
{
    label: 'Carriage Type',
    value: 'carriageType'
},
{
    label: 'Carriage FNN',
    value: 'carriageFNN'
},
{
    label: 'Device Name',
    value: 'deviceName'
},
{
    label: 'User',
    value: 'createdBy'
},
{
    label: 'Service Type',
    value: 'serviceType'
},
{
    label: 'SIIAM Cases',
    value: 'siiamCases'
},
{
    label: 'Service Central Incidents',
    value: 'serviceCentralIncidents'
},
{
    label: 'Time',
    value: 'createdOn'
}]);


const dateTransform = function(item) {
    if (item.createdOn) {
        let date = moment(new Date(item.createdOn));
        if (date.isValid()) {
            item.createdOn = date.format(global.gConfig.dateTimeFormat);
        }
    }
    return item;
}


const QUICK_COMMAND_FIELDS = Object.freeze([{
    label: 'Command',
    value: 'command'
},
{
    label: 'Status',
    value: 'status'
},
{
    label: 'User',
    value: 'createdBy'
},
{
    label: 'Time',
    value: 'createdOn'
}]);

class MongooseCursorStreamRead extends Readable {
    constructor(cursor, fields) {
        super();
        this.cursor = cursor;

        this.parser = new Parser({
            fields: fields,
            transforms: [dateTransform],
            header: true
        });
    }

    async _read() {
        try {
            let serviceCheckRecord = await this.cursor.next();

            if (serviceCheckRecord) {
                this.push(this.parser.parse([serviceCheckRecord]));
                this.push('\n');

                // Sets opts.header to false so that future service checks don't include the field headers
                this.parser.opts.header = false;
            } else {
                this.push(null);
            }
        } catch(error) {
            // Unsure of the best implementation for this, because at this point, the HTTP headers have been
            // sent to the client, will send the error message last in the stream
            this.push(`MongoDB cursor stream terminated unexpectedly with error, ${error.toString()}`);
            this.destroy(error);
        }
    }
}



export class ServiceCheckStreamRead extends MongooseCursorStreamRead {
    constructor(cursor) {
        super(cursor, SERVICE_CHECK_FIELDS);
    }
}


export class QuickCommandStreamRead extends MongooseCursorStreamRead {
    constructor(cursor) {
        super(cursor, QUICK_COMMAND_FIELDS);
    }
}
