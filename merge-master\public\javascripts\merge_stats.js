$(document).ready(function() {
    // Prevent non-numeric characters from being entered and disable pasting into input field
    $("#statsLastQuantity").keypress("keypress", function(e) {
        if (e.which < "0".charCodeAt(0) || e.which > "9".charCodeAt(0)) {
            e.preventDefault();
        }
    });

    $("#statsLastQuantity").on("paste",function(e) {
        e.preventDefault();
    });

    var ctx = document.getElementById("statisticsChart").getContext("2d");
    $("#statsCarriageTypeFilter").select2({
        tags: true
    });
    $("#statsUserFilter").select2({
        tags: true
    });
    $("#statsFnnFilter").select2({
        tags: true
    });
    $("#statsGroupSelect").select2();
    $("#statsRuleFilter").select2();
    $("#statsRuleStatusFilter").select2();
    $("#statsRuleResultFilter").select2();
    $("#statsRuleTriggerSourceFilter").select2();

    $("#statsSourceFilter").select2();
    $("#statsSourceStatusFilter").select2();

    httpApiGetAllResults("/rules", null, [], updateRuleList);
    httpApiGetAllResults("/sources", null, [], updateSourceList);

    function updateRuleList(err, rules) {
        if (err) {
            console.error(err);
            return;
        }

        let ruleItemList = $.map(rules, function(item) {
            return {
                id: item.name,
                text: item.name
            }
        });

        $("#statsRuleFilter").select2({
            data: ruleItemList
        });
    }

    function updateSourceList(err, sources) {
        if (err) {
            console.error(err);
            return;
        }

        let sourceItemList = $.map(sources, function(item) {
            return {
                id: item.name,
                text: item.name
            }
        });

        $("#statsRuleTriggerSourceFilter").select2({
            data: sourceItemList
        });

        $("#statsSourceFilter").select2({
            data: sourceItemList
        });
    }

    var chart = new Chart(ctx, {
        type: "line",
        options: {
            scales: {
                x: {
                    type: "time",
                    ticks: {
                        callback: function(_, index, values) {
                            let timestamp = values[index].value;
                            if (timestamp) {
                                return new Date(timestamp).toLocaleDateString();
                            } else {
                                return ""
                            }
                        }
                    }
                },
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        title: function(contexts) {
                            if (contexts && contexts.length > 0) {
                                return `From: ${new Date(contexts[0].raw.x).toLocaleString()}`;
                            } else {
                                // Occurs for tooltip with no legend items, which should never happen
                                return "Unknown";
                            }
                        }
                    }
                },
                legend: {
                    // Default legend click handler, except if total legend is selected, also
                    // change statsShowTotal checkbox to reflect its current state
                    onClick: function(e, legendItem) {
                        let dataset = chart.data.datasets[legendItem.datasetIndex];

                        var index = legendItem.datasetIndex;
                        var ci = this.chart;
                        var meta = ci.getDatasetMeta(index);

                        // See controller.isDatasetVisible comment
                        meta.hidden = meta.hidden === null ? !ci.data.datasets[index].hidden : null;

                        if (dataset.isTotal) {
                            let checkboxEnabled = meta.hidden === null ? true : false;
                            $("#statsShowTotal").prop("checked", checkboxEnabled);
                        }

                        // We hid a dataset, rerender the chart
                        ci.update();
                    }
                }
            }
        }
    });

    updateStatisticsGraph(chart);

    $("#statsType").on("change", function() {
        const groupLabels = Object.freeze({
            "scsUsers": "users",
            "scsCarriageTypes": "carriage types",
            "scsFnn": "FNNs",
            "scsRules": "rules",
            "scsSources": "sources",
            "aaaToolUsers": "users"
        });

        const aaaToolDisabledElements = Object.freeze(["#statsCarriageTypeFilter", "#statsFnnFilter", "#statsRuleFilter", "#statsSourceFilter"]);

        $("#resultFilterTopLabel").text(`${groupLabels[this.value]} with the most service checks`);
        $("#resultFilterAllLabel").text(`All ${groupLabels[this.value]}`);

        for (let i in aaaToolDisabledElements) {
            if (this.value == "aaaToolUsers") {
                $(aaaToolDisabledElements[i]).val(null).trigger("change");
                $(aaaToolDisabledElements[i]).prop("disabled", true);
                updateRuleFilters();
                updateSourceFilters();
            } else {
                $(aaaToolDisabledElements[i]).prop("disabled", false);
            }
        }
    });

    $("#statsTimeApply").click(function () {
        updateStatisticsGraph(chart);
    });

    $("#statsGroupSelect").on("select2:select", function (e) {
        updateByFilter(chart, $("#statsGroupSelect").select2("data"));
    });

    $("#statsGroupSelect").on("select2:unselect", function (e) {
        updateByFilter(chart, $("#statsGroupSelect").select2("data"));
    });

    $("#statsRuleFilter").on("select2:select", function (e) {
        updateRuleFilters();
    });

    $("#statsRuleFilter").on("select2:unselect", function (e) {
        updateRuleFilters();
    });

    $("#statsSourceFilter").on("select2:select", function (e) {
        updateSourceFilters();
    });

    $("#statsSourceFilter").on("select2:unselect", function (e) {
        updateSourceFilters();
    });

    $("#statsShowTotal").change(function() {
        showTotalLine(chart, this.checked);
    });
});

const TIME_ZONE = "Australia/Victoria";
const UNITS = Object.freeze({
    "h": "hour",
    "d": "day",
    "w": "isoWeek",
    "m": "month",
    "y": "year"
});

function updateStatisticsGraph(chart) {
    const maxDatasetsShowLegend = 28;
    let resultLimitFilterSelection = $("input[name='resultFilterRadios']:checked").val();
    let selectGroupElement = $("#statsGroupSelect");
    let lastQuantity = $("#statsLastQuantity").val();
    let lastUnit = $("#statsLastUnit").val();
    let intervalUnit = $("#statsIntervalUnit").val();
    let statsType = $("#statsType").val();
    let statsTotalCheckbox = $("#statsShowTotal");

    let carriageTypeFilter = $("#statsCarriageTypeFilter").val();
    let userFilter = $("#statsUserFilter").val();
    let fnnFilter = $("#statsFnnFilter").val();
    let ruleFilter = $("#statsRuleFilter").val();
    let ruleStatusFilter = $("#statsRuleStatusFilter").val();
    let ruleResultFilter = $("#statsRuleResultFilter").val();
    let ruleTriggerSourceFilter = $("#statsRuleTriggerSourceFilter").val();
    let sourceFilter = $("#statsSourceFilter").val();
    let sourceStatusFilter = $("#statsSourceStatusFilter").val();

    let groupKeys = Object.freeze({
        "scsUsers": "user",
        "scsCarriageTypes": "carriageType",
        "scsFnn": "fnn",
        "scsRules": "rule",
        "scsSources": "source",
        "aaaToolUsers": "user"
    });

    // Labels for filtering graph lines by the selected group
    let groupLabels = Object.freeze({
        "scsUsers": "User",
        "scsCarriageTypes": "Carriage type",
        "scsFnn": "FNN",
        "scsRules": "Rule",
        "scsSources": "Source",
        "aaaToolUsers": "User"
    });
    $("#statsGroupLabel").text(groupLabels[statsType]);

    let queryParameters = {
        limit: 10000,
        last: `${lastQuantity}${lastUnit}`,
        interval: intervalUnit,
        carriageType: carriageTypeFilter,
        user: userFilter,
        fnn: fnnFilter,
        rule: ruleFilter,
        ruleStatus: ruleStatusFilter,
        ruleResult: ruleResultFilter,
        ruleTriggerSource: ruleTriggerSourceFilter,
        source: sourceFilter,
        sourceStatus: sourceStatusFilter
    };

    setButtonSpinner($("#statsTimeApply"), "Apply", true);

    let statKey = groupKeys[statsType];
    let statsDisableTotal = ["scsRules", "scsSources"].includes(statsType);
    let url = `/stats/${statsType}`;

    if (resultLimitFilterSelection == "top") {
        let numResults = $("#resultFilterTopNumber").val();
        getTopGroupsStatistics(numResults, function(err, topGroups) {
            if (err) {
                return;
            }

            getChartData(topGroups);
        });
    } else if (resultLimitFilterSelection == "all") {
        getChartData();
    }


    function getTopGroupsStatistics(numGroups, callback) {
        let parameters = Object.assign({}, queryParameters);
        parameters.limit = numGroups;
        delete parameters.interval;

        $.ajax({
            url: url,
            type: "GET",
            data: parameters,
            cache: false,
            success: function(response) {
                let groupList = [];

                for (var i in response.results) {
                    groupList.push(response.results[i][statKey]);
                }

                callback(null, groupList);
            },
            error: function (xhr, status, e) {
                callback(e, null);
            }
        });
    }

    function getChartData(topGroups=[]) {
        let parameters = Object.assign({}, queryParameters);

        if (topGroups.length > 0) {
            switch (statsType) {
                case "scsUsers":
                    parameters.user = topGroups;
                    break;
                case "scsCarriageTypes":
                    parameters.carriageType = topGroups;
                    break;
                case "scsFnn":
                    parameters.fnn = topGroups;
                    break;
                case "scsRules":
                    parameters.rule = topGroups;
                    break;
                case "scsSources":
                    parameters.source = topGroups;
                    break;
                case "aaaToolUsers":
                    parameters.user = topGroups;
                    break;
            }
        }

        httpApiGetAllResults(url, parameters, [], updateChartData);
    }


    function updateChartData(err, data) {
        if (err) {
            // Just alerts for now if stats cannot be obtained.
            alert("Encountered error in obtaining statistics.");
            console.error(err);
            return;
        }

        let parameters = Object.assign({}, queryParameters);

        // Clear select box and enable checkbox for show total
        selectGroupElement.empty();

        statsTotalCheckbox.prop("disabled", statsDisableTotal);
        statsTotalCheckbox.prop("checked", !statsDisableTotal);
        statsTotalCheckbox.attr("title", statsDisableTotal ? "Total service checks line is not applicable to rule or source statistics" : null);

        let datasets = {};
        let emptyDataset = {};
        let dateLabels = [];

        let nowDate = moment();
        let startDate = calculateStartDate(nowDate, lastQuantity, lastUnit);
        $("#statsSince").text(`Showing records since ${startDate.toDate().toLocaleString()}`);

        // Set start date to unit before actual start
        startDate.startOf(UNITS[intervalUnit]);

        let currDate = startDate.clone();

        while (currDate < nowDate) {
            let date = new Date(currDate.toDate()).toISOString();
            dateLabels.push(date);
            emptyDataset[date] = 0;

            currDate.add(1, UNITS[intervalUnit]);
        }

        for (var i in data) {
            if (!(data[i][statKey] in datasets)) {
                let lineColour = getLineColour(Object.keys(datasets).length);
                let datasetLabel = handleEmptyOrNullLabel(data[i][statKey]);

                datasets[data[i][statKey]] = {
                    label: datasetLabel,
                    fill: false,
                    tension: 0.4,
                    backgroundColor: `rgba(${lineColour[0]}, ${lineColour[1]}, ${lineColour[2]}, 0.4)`,
                    borderColor: `rgba(${lineColour[0]}, ${lineColour[1]}, ${lineColour[2]}, 1)`,
                    dataByDate: Object.assign({}, emptyDataset)
                }
            }

            let startDate = new Date(data[i].start).toISOString();
            datasets[data[i][statKey]].dataByDate[startDate] = data[i].count;
        }

        let datasetsArray = [];

        for (var key in datasets) {
            datasets[key].data = [];

            for (var date in datasets[key].dataByDate) {
                datasets[key].data.push({
                    x: date,
                    y: datasets[key].dataByDate[date]
                });
            }

            delete datasets[key].dataByDate;
            datasetsArray.push(datasets[key]);

            // Add key to select box
            selectGroupElement.append(new Option(key === "" ? "''" : key, key, false, false));
        }

        // Disable legends in chart if there are more than maxDatasetsShowLegend datasets
        if (datasetsArray.length > maxDatasetsShowLegend) {
            chart.options.plugins.legend.display = false;
        } else {
            chart.options.plugins.legend.display = true;
        }

        setButtonSpinner($("#statsTimeApply"), "Apply", false);

        if (!statsDisableTotal) {
            if (statsType.startsWith("scs")) {
                httpApiGetAllResults('/stats/scsTotal', parameters, [], updateChartTotal);
            } else {
                httpApiGetAllResults('/stats/aaaToolTotal', parameters, [], updateChartTotal);
            }

        }

        chart.data.labels = dateLabels;
        chart.data.datasets = datasetsArray;
        chart.update();
    }


    function updateChartTotal(err, data) {
        if (err) {
            alert("Encountered error in obtaining total.");
            console.error(err);
            return;
        }

        let emptyDataset = {};

        let nowDate = moment();
        let startDate = calculateStartDate(nowDate, lastQuantity, lastUnit);

        // Set start date to unit before actual start
        startDate.startOf(UNITS[intervalUnit]);

        let currDate = startDate.clone();

        while (currDate < nowDate) {
            let date = new Date(currDate.toDate()).toISOString();
            emptyDataset[date] = 0;
            currDate.add(1, UNITS[intervalUnit]);
        }

        let datasetTotal = {
            label: "total",
            fill: false,
            tension: 0.4,
            backgroundColor: "rgba(80, 80, 80, 0.4)",
            borderColor: "rgba(80, 80, 80, 1)",
            dataByDate: Object.assign({}, emptyDataset),
            data: [],
            isTotal: true
        };

        for (var i in data) {
            let startDate = new Date(data[i].start).toISOString();
            datasetTotal.dataByDate[startDate] = data[i].count;
        }

        for (var date in datasetTotal.dataByDate) {
            datasetTotal.data.push({
                x: date,
                y: datasetTotal.dataByDate[date]
            });
        }

        delete datasetTotal.dataByDate;

        // Append dataset to chart
        chart.data.datasets.push(datasetTotal);
        chart.update();
    }
}


function httpApiGetAllResults(url, parameters, resultList, callback) {
    // Goes through the response from a REST API and continues to the next pagination link if available
    $.ajax({
        url: url,
        type: "GET",
        data: parameters,
        cache: false,
        success: function(response) {
            try {
                resultList.push(...response.results);

                if (response.metadata.pagination.next === null) {
                    callback(null, resultList);
                } else {
                    httpApiGetAllResults(response.metadata.pagination.next, null, resultList, callback);
                }
            } catch(e) {
                callback(e, null);
            }
        },
        error: function (xhr, status, e) {
            callback(e, null);
        }
    });
}


// Determines colour of line as a shade of a colour in the baseColours array
function getLineColour(index) {
    let shades = 4;
    let baseColours = [[238, 46, 46], [0, 140, 72], [24, 90, 170], [244, 125, 35], [102,44, 145], [162, 30, 34], [180, 56, 148]];
    let totalColours = baseColours.length;

    let colourIndex = index % totalColours;
    let colourShade = Math.floor((index / totalColours) % shades);

    let lineColour = baseColours[colourIndex];

    for (var i = 0; i < shades; i++) {
        lineColour[i] += Math.floor(((256 - lineColour[i])*colourShade)/shades);
    }

    return lineColour;
}


function calculateStartDate(nowDate, lastQuantity, lastUnit) {
    let startDate = moment(nowDate).tz(TIME_ZONE);

    startDate.subtract(lastQuantity, UNITS[lastUnit]);
    return startDate;
}


function updateByFilter(chart, selected) {
    let filteredKeys = {};
    for (var i in selected) {
        filteredKeys[selected[i].text] = 0;
    }

    chart.data.datasets.forEach(function(e, i) {
        // Select box selections do not apply to the total line dataset
        if (!e.isTotal) {
            let meta = chart.getDatasetMeta(i);
            if (selected.length == 0 || selected.filter(select => e.label == select.text).length) {
                meta.hidden = null;
            } else {
                meta.hidden = true;
            }
        }
    });

    chart.update();
}


function showTotalLine(chart, enableTotal) {
    // Find the total measurement dataset and show / hide it
    chart.data.datasets.forEach(function(e, i) {
        if (e.isTotal) {
            let meta = chart.getDatasetMeta(i);
            if (enableTotal) {
                meta.hidden = null;
            } else {
                meta.hidden = true;
            }
        }
    });

    chart.update();
}


function handleEmptyOrNullLabel(label) {
    if (label === null) {
        return "null";
    } else if (label === "") {
        return "''";
    } else {
        return label;
    }
}


function updateRuleFilters() {
    let ruleFilterIsEmpty = $("#statsRuleFilter").val().length == 0;
    $("#statsRuleStatusFilter").prop("disabled", ruleFilterIsEmpty);
    $("#statsRuleResultFilter").prop("disabled", ruleFilterIsEmpty);
    $("#statsRuleTriggerSourceFilter").prop("disabled", ruleFilterIsEmpty);
    if (ruleFilterIsEmpty) {
        $("#statsRuleStatusFilter").val(null).trigger("change");
        $("#statsRuleResultFilter").val(null).trigger("change");
        $("#statsRuleTriggerSourceFilter").val(null).trigger("change");
    }
}


function updateSourceFilters() {
    let sourceFilterIsEmpty = $("#statsSourceFilter").val().length == 0;
    $("#statsSourceStatusFilter").prop("disabled", sourceFilterIsEmpty);
    if (sourceFilterIsEmpty) {
        $("#statsSourceStatusFilter").val(null).trigger("change");
    }
}
