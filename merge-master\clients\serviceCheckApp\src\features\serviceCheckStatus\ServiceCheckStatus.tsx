import { TextStyle } from '@able/react'
import { faSpinner } from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import classNames from 'classnames'
import { useAppState } from '../../hooks/useAppState'
import styles from './ServiceCheckStatus.module.scss'

interface ServiceCheckStatusProps {
    className?: string
}

export const ServiceCheckStatus = ({ className }: ServiceCheckStatusProps) => {
    const { appState } = useAppState()

    const serviceCheckStatus = appState.serviceDetails.status

    if (serviceCheckStatus !== 'running') return

    return (
        <div className={classNames(styles.serviceCheckStatus, className)}>
            <TextStyle alias="LabelA1">Service check is running</TextStyle>
            <FontAwesomeIcon icon={faSpinner} className={'fa-spin'} />
        </div>
    )
}
