
import Ajv from 'ajv';
import debug from 'debug';
import { isValidPhoneNumber, parsePhoneNumber } from 'libphonenumber-js';
import _ from 'lodash';

import serviceCheck from '../modules/serviceCheck.js';
import ServiceCheckModel from '../db/model/serviceCheck.js';
import auth from '../modules/auth.js';
import { NotFoundError } from '../modules/error.js';
import { generateId } from '../modules/helpers/id.js';
import logger from '../modules/logger.js';
import validators from '../modules/validators.js';
import { ServiceCheckLockError } from '../modules/error.js';
import { ServiceCheckStartMethod } from '../modules/enumerations.js';

const debugMsg = debug('merge:socketSC');

const ajv = new Ajv({ allErrors: true });

ajv.addFormat('date-time', function(value) {
    return !isNaN(Date.parse(value));
});


const testPackageRunSchema = {
    type: 'object',
    required: ['id', 'testPackages'],
    properties: {
        id: {
            type: 'string',
            minLength: 1
        },
        testPackages: {
            type: 'array',
            items: {
                type: 'object',
                required: ['name'],
                properties: {
                    name: {
                        type: 'string',
                        minLength: 1
                    },
                    parameters: {
                        type: 'object',
                        required: ['startDate', 'endDate'],
                        nullable: true,
                        properties: {
                            startDate: {
                                type: 'string',
                                format: 'date-time',
                                nullable: true
                            },
                            endDate: {
                                type: 'string',
                                format: 'date-time',
                                nullable: true
                            }
                        }
                    }
                },
                additionalProperties: false
            }
        }
    },
    additionalProperties: false
};

const fullRunSchema = {
    type: 'object',
    required: ['id'],
    properties: {
        id: {
            type: 'string',
            minLength: 1
        }
    },
    additionalProperties: false
};


const validateTestPackageRunData = ajv.compile(testPackageRunSchema);
const validateFullRunData = ajv.compile(fullRunSchema);


export function socketListen(socket) {
    socket.on('serviceCheckStart', async function(data) {
        console.log('servicecheck started')

        if (!_.isPlainObject(data) || !_.isPlainObject(data.input) || !_.isPlainObject(data.additionalParameters)) {
            logger.warn('Invalid input passed into serviceCheckStart socket');
            return;
        }

        // Omits empty strings from the data.input object
        data.input = _.omitBy(data.input, function(item) { return !item; });

        // Sanitizes input fields similar to API endpoint
        if (typeof data.input.carriageFNN === 'string') {
            data.input.carriageFNN = data.input.carriageFNN.trim().toUpperCase();
        }

        if (typeof data.input.deviceIP === 'string') {
            data.input.deviceIP = data.input.deviceIP.trim().toUpperCase();
        }

        if (typeof data.input.deviceName === 'string') {
            data.input.deviceName = data.input.deviceName.trim().toUpperCase();
        }

        if (typeof data.input.CIDN === 'string') {
            data.input.CIDN = data.input.CIDN.trim();
        }

        if (typeof data.input.inputLevel === 'string') {
            // If the integer failed to parse, a modified message may have been sent through the socket
            // logs and returns for now
            try {
                data.input.inputLevel = Integer.parseInt(data.input.inputLevel);
            } catch(error) {
                logger.warn('Invalid level passed into serviceCheckStart socket');
                return;
            }
        }

        const runOutageInfo = data.input.suite === 'outageInfo';
        if (data.input.suite !== 'outageInfo') {
            data.input.suite = 'standard';
        }

        let user = socket.request.user;
        let SCr = new ServiceCheckModel({
            id: generateId('MSC'),
            input: data.input,
            createdBy: user.username
        });

        // Note: Validation functions could be more streamlined
        if (data.input.adborId && !validators.isAdborId(data.input.adborId)) {
            socket.emit('serviceCheckStarted', SCr.id);
            socket.emit('SCStatus', SCr.id, 'Input validation');
            socket.emit('serviceCheckError', SCr.id, 'Error validating Adbor ID field, should be a valid Adbor Id (8-9 digits)');
            return;
        }

        // Note: Validation functions could be more streamlined
        if ((typeof data.input.latitude === 'number' && typeof data.input.longitude !== 'number') ||
            (typeof data.input.latitude !== 'number' && typeof data.input.longitude === 'number')) {
            socket.emit('serviceCheckStarted', SCr.id);
            socket.emit('SCStatus', SCr.id, 'Input validation');
            socket.emit('serviceCheckError', SCr.id, 'Latitude and longitude must both be specified');
            return;
        }

        if (typeof data.input.latitude === 'number' && (data.input.latitude < -90.0 || data.input.latitude > 90.0)) {
            socket.emit('serviceCheckStarted', SCr.id);
            socket.emit('SCStatus', SCr.id, 'Input validation');
            socket.emit('serviceCheckError', SCr.id, 'Error validating Latitude field, should be a valid Latitude coordinate between -90.0 to 90.0');
            return;
        }

        if (typeof data.input.longitude ==='number' && (data.input.longitude < -180.0 || data.input.longitude > 180.0)) {
            socket.emit('serviceCheckStarted', SCr.id);
            socket.emit('SCStatus', SCr.id, 'Input validation');
            socket.emit('serviceCheckError', SCr.id, 'Error validating Longitude field, should be a valid Longitude coordinate between -180.0 to 180.0');
            return;
        }

        if (data.input.adborId && (typeof data.input.latitude === 'number' || typeof data.input.longitude === 'number')) {
            socket.emit('serviceCheckStarted', SCr.id);
            socket.emit('SCStatus', SCr.id, 'Input validation');
            socket.emit('serviceCheckError', SCr.id, 'Cannot specify both Adbor Id and latitude / longitude');
            return;
        }

        if (data.input.fieldInterfaceIP && !validators.isIPv4Address(data.input.fieldInterfaceIP)) {
            socket.emit('serviceCheckStarted', SCr.id);
            socket.emit('SCStatus', SCr.id, 'Input validation');
            socket.emit('serviceCheckError', SCr.id, 'Error validating interface IP field, should be a valid IPv4 address');
            return;
        }

        if (!validators.isValidAdditionalParameters(data.additionalParameters)) {
            socket.emit('serviceCheckStarted', SCr.id);
            socket.emit('SCStatus', SCr.id, 'Input validation');
            socket.emit('serviceCheckError', SCr.id, 'Error validating additional parameters fields, dateFrom should be before current date and dateTo should be after dateFrom');
            return;
        }

        if (Array.isArray(data.input.ruleNames)) {
            data.input.ruleNames = data.input.ruleNames.filter(name => {
                return typeof name === 'string';
            });
        } else {
            socket.emit('serviceCheckStarted', SCr.id);
            socket.emit('SCStatus', SCr.id, 'Input validation');
            socket.emit('serviceCheckError', SCr.id, 'Error validating rule names field, should be an array of strings');
            return;
        }

        if (Array.isArray(data.input.sourceNames)) {
            data.input.sourceNames = data.input.sourceNames.filter(name => {
                return typeof name === 'string';
            });
        } else {
            socket.emit('serviceCheckStarted', SCr.id);
            socket.emit('SCStatus', SCr.id, 'Input validation');
            socket.emit('serviceCheckError', SCr.id, 'Error validating source names field, should be an array of strings');
            return;
        }

        SCr.additionalParameters = data.additionalParameters;

        try {
            // Attempts to validate the inputs to the service check record passed from the socket
            // This should only fail if the frontend validation does not match that of the schema
            await SCr.validate();
        } catch(error) {
            logger.error(`Socket service check start: error validating service check record inputs for ${SCr.id}, ${error.toString()}`);
            socket.emit('serviceCheckStarted', SCr.id);
            socket.emit('SCStatus', SCr.id, 'Input validation');
            socket.emit('serviceCheckError', SCr.id, 'Error validating input fields to service check');
            return;
        }
        let inputLevel = SCr.input.level;

        if (!SCr.input.searchFNN || typeof SCr.input.searchFNN !== 'string') {
            debugMsg('Error, Empty search submitted');
            socket.emit('serviceCheckStarted', SCr.id);
            socket.emit('SCStatus', SCr.id, 'Empty / invalid FNN');
            socket.emit('serviceCheckError', SCr.id, `FNN provided is empty or is an invalid type`);
            return;
        }

        if (!auth.checkUserAuthorizedForServiceCheckLevel(user, inputLevel)) {
            socket.emit('serviceCheckStarted', SCr.id);
            socket.emit('SCStatus', SCr.id, 'Incorrect service check level');
            socket.emit('serviceCheckError', SCr.id, `User ${user.username} is not allowed to start a service check with level ${inputLevel}`);
            return;
        }

        // Missing fields in input object in data result in an exception being thrown
        // this can occur if a user were to manually interact with the client socket object to start a service check
        try {
            logger.info(`Service Check '${SCr.input.searchFNN}' by '${SCr.createdBy}' submitted via socket.`);

            SCr.fnn = SCr.input.searchFNN.trim().toUpperCase();

            // Sets carriageType and deviceName fields from inputs
            SCr.carriageType = SCr.input.carriageType;
            SCr.deviceName = SCr.input.deviceName ? SCr.input.deviceName.toUpperCase() : null;

            // Store valid phone number inputs in E.164 format
            if (isValidPhoneNumber(SCr.fnn, 'AU')) {
                SCr.phoneNumber = parsePhoneNumber(SCr.fnn, 'AU').format('E.164');
            }

            SCr.startMethod = ServiceCheckStartMethod.userInterfaceClassic;
            // Call ServiceCheck Module to start check Service
            debugMsg('#> Start serviceCheck Module');
        } catch(error) {
            logger.error(`Socket service check start: unexpected error occurred when processing input, ${error.toString()}`);
            return;
        }

        serviceCheck.start(SCr, user, socket, runOutageInfo);
        socket.emit('serviceCheckStarted', SCr.id);

        debugMsg('======== serviceCheckStart SCr ===========');
        debugMsg(SCr);
    });

    socket.on('serviceCheck:runTestPackage', async function(data) {
        if (!validateTestPackageRunData(data)) {
            socket.emit('serviceCheck:error', {
                message: 'Cannot perform test package run, invalid input payload'
            });
            return;
        }

        try {
            let SCr = await serviceCheck.findAndPopulate(data.id);

            if (!SCr) {
                throw new NotFoundError(`Service check record with id ${data.id} not found`);
            }

            let user = socket.request.user;

            // Ensures test packages can only be run by the user who created the service check
            if (SCr.createdBy !== user.username) {
                throw new Error(`Incorrect user to run for service check ${data.id}`);
            }

            await serviceCheck.runServiceCheckTestPackages(SCr, data.testPackages, user, socket);
        } catch(error) {
            if (error instanceof ServiceCheckLockError) {
                logger.warn(`Service check test package lock error with service check ${data.id}`);

                socket.emit('serviceCheck:error', {
                    message: 'Cannot perform test package run as another operation is already running on this service check'
                });
            } else {
                logger.warn(`Service check test package run unhandled error, ${error.toString()}`);

                socket.emit('serviceCheck:error', {
                    message: 'Unknown error occurred'
                });
            }
        }
    });

    socket.on('serviceCheck:runFull', async function(data) {
        if (!validateFullRunData(data)) {
            socket.emit('serviceCheck:error', {
                message: 'Cannot perform full service check run, invalid input payload'
            });
            return;
        }

        try {
            let SCr = await serviceCheck.findAndPopulate(data.id);

            if (!SCr) {
                throw new NotFoundError(`Service check record with id ${data.id} not found`);
            }

            let user = socket.request.user;

            // Ensures test packages can only be run by the user who created the service check
            if (SCr.createdBy !== user.username) {
                throw new Error(`Incorrect user to run for service check ${data.id}`);
            }

            await serviceCheck.runServiceCheckFull(SCr, user, socket);
        } catch(error) {
            if (error instanceof ServiceCheckLockError) {
                logger.warn(`Service check full run lock error with service check ${data.id}`);

                socket.emit('serviceCheck:error', {
                    message: 'Cannot perform full service check as another operation is already running on this service check'
                });
            } else {
                logger.warn(`Service check full run unhandled error, ${error.toString()}`);

                socket.emit('serviceCheck:error', {
                    message: 'Unknown error occurred'
                });
            }
        }
    });
}
