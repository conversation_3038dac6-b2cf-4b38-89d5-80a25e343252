
import mongoose from 'mongoose';
import chai from 'chai';
import chaiHttp from 'chai-http';
import chaiLike from 'chai-like';
import chaiDateTime from 'chai-datetime';
import sinon from 'sinon';
import axios from 'axios';
import axiosCookieJarSupport from 'axios-cookiejar-support';
const should = chai.should();
import superagentDefaults from 'superagent-defaults';
import supertest from 'supertest';
import _ from 'lodash';
import assert from 'assert';

var app;
var request;

// Clones config before importing other modules, as dependent modules reference
// global.gConfig before initialization
// TODO: Cleaner implementation of config management and application entrypoints
import '../set_config.js';
import config from '../config.js';

import Api from '../../db/model/api.js';
import CommandRun from '../../db/model/commandRun.js';
import ValidateFnnCheck from '../../db/model/validateFnnCheck.js';
import { getCommandsList } from '../../routes/command.js';
import helpers from '../helpers.js';
import { UNIT_TEST_ALL_ACCESS_USERNAME, UNIT_TEST_USERS } from '../users.js';

chaiLike.extend(helpers.dateFieldCheck);
chaiLike.extend(helpers.regexCheck);
chai.use(chaiLike);
chai.use(chaiHttp);
chai.use(chaiDateTime);


describe('Merge API: Commands', () => {
    // Setup and connect to mongodb memory server, start merge app instance
    // Set request variable with a configured supertest agent with test JWT and app instance
    before(async function() {
        global.gConfig = _.cloneDeep(config.default);
        app = (await import('../../app.js')).default;
        request = superagentDefaults(supertest(app));

        let token = await helpers.authenticateApi(request, UNIT_TEST_ALL_ACCESS_USERNAME);
        request.set("Authorization", `Bearer ${token}`);
    });

    // Drop all contents of database before each test
    beforeEach(async function() {
        global.gConfig = _.cloneDeep(config.default);
        // Uses deleteMany instead of mongoose.connection.db.dropDatabase() because
        // custom indexes appear to be deleted if dropDatabase() is used
        for (let collectionName in mongoose.connection.collections) {
            await mongoose.connection.collections[collectionName].deleteMany({});
        }
    });

    describe('Command List', () => {
        it('Ensure command list is returned', (done) => {
            request.get('/api/command/list')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(200);

                // Compares returned to the output of getCommandList()
                // Only ensures that the route exists and is serving the same command list,
                // not the correctness of the commands themselves
                chai.expect(res.body).to.eql(getCommandsList());

                done();
            });
        });

        it('Unauthenticated request', async() => {
            let res = await request.get('/api/command/list').unset('Authorization');
            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                let token = await helpers.authenticateApi(request, username);
                let res = await request.get('/api/command/list').set('Authorization', `Bearer ${token}`);

                switch(username) {
                    case 'apiAccess':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });
    });

    describe('Run commands', () => {
        beforeEach(async function() {
            // Stubs the axios cookie jar wrapper function so unit tests will run
            sinon.stub(axiosCookieJarSupport, 'wrapper').callsFake(function(_) {});
        });

        afterEach(async function() {
            sinon.restore();
        });

        it('Unauthenticated request', async() => {
            let res = await request.post('/api/command/run').unset('Authorization');
            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                let token = await helpers.authenticateApi(request, username);
                let res = await request.post('/api/command/run').set('Authorization', `Bearer ${token}`);

                switch(username) {
                    case 'apiAccess':
                        try {
                            res.should.have.status(400);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Empty request', (done) => {
            request.post('/api/command/run')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(400);

                done();
            });
        });

        it('No fields specified', (done) => {
            request.post('/api/command/run')
            .send('')
            .type('form')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(400);

                done();
            });
        });

        it('Unrecognised command', (done) => {
            let currDate = new Date();

            request.post('/api/command/run')
            .send('command=invalid')
            .type('form')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(200);

                chai.expect(res.body).to.not.have.any.keys('_id');
                chai.expect(res.body).to.like({
                    id: /.*/,
                    command: "invalid",
                    createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                    createdOn: currDate,
                    result: null,
                    statusCode: null,
                    status: "Error",
                    error: "NotFoundError: Command \"INVALID\" not found",
                    duration: /.*/
                });

                done();
            });
        });

        it('Unrecognised command with arguments', (done) => {
            let currDate = new Date();

            request.post('/api/command/run')
            .send('command=badcommand -f N1111111R')
            .type('form')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(200);

                chai.expect(res.body).to.not.have.any.keys('_id');
                chai.expect(res.body).to.like({
                    command: "badcommand -f N1111111R",
                    createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                    createdOn: currDate,
                    result: null,
                    statusCode: null,
                    status: "Error",
                    error: "NotFoundError: Command \"BADCOMMAND\" not found",
                    duration: /.*/
                });

                done();
            });
        });

        it('Valid command (MAGPIE)', async() => {
            let currDate = new Date();

            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: {
                            FOUND: true,
                            SUCCESS: true,
                            HOST: "hostname_here",
                            MULTIPLE_RESULTS: false,
                            DEVICESTATUS: "ACTIVE",
                            CARRIAGEID: "A012345678",
                            DEVICENAME: "abcdefgr01c02",
                            ORGCODE: "BEN",
                            STATE: "NSW",
                            LOCALITY: "RYDALMERE",
                            MDNNETWORKFNN: "N1234567R",
                            MDNFNN: "N1234567R",
                            MDN: true,
                            processErrors: [],
                            host: "hapi_hostname",
                            magpie: "https://magpie.hostname",
                            PAT: []
                        },
                        status: 200
                    };
                }
            });

            // Creates mock MAGPIE API document
            await new Api({
                name: "MAGPIE",
                active: true,
                type: "rest",
                method: "get",
                baseUrl: "'https://magpie.hostname'"
            }).save();

            let res = await request.post('/api/command/run').send('command=magpie -f N1234567R').type('form');

            res.should.have.status(200);

            chai.expect(res.body).to.not.have.any.keys('_id');
            chai.expect(res.body).to.like({
                command: "magpie -f N1234567R",
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                createdOn: currDate,
                result: JSON.stringify({
                    FOUND: true,
                    SUCCESS: true,
                    HOST: "hostname_here",
                    MULTIPLE_RESULTS: false,
                    DEVICESTATUS: "ACTIVE",
                    CARRIAGEID: "A012345678",
                    DEVICENAME: "abcdefgr01c02",
                    ORGCODE: "BEN",
                    STATE: "NSW",
                    LOCALITY: "RYDALMERE",
                    MDNNETWORKFNN: "N1234567R",
                    MDNFNN: "N1234567R",
                    MDN: true,
                    processErrors: [],
                    host: "hapi_hostname",
                    magpie: "https://magpie.hostname",
                    PAT: []
                }),
                statusCode: 200,
                status: "Completed",
                error: null,
                duration: /.*/
            });
        });
    });

    describe('View command record', () => {
        it('Unauthenticated request', async() => {
            let res = await request.get('/api/command/view/commandId').unset('Authorization');
            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                let token = await helpers.authenticateApi(request, username);
                let res = await request.get('/api/command/view/commandId').set('Authorization', `Bearer ${token}`);

                switch(username) {
                    case 'apiAccess':
                        try {
                            res.should.have.status(404);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Command ID that does not exist', (done) => {
            request.get('/api/command/view/5fa3f523243eac04880d1a34')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(404);

                done();
            });
        });

        it('Record created from model', (done) => {
            let date = new Date("2020-09-20T05:22:11.000Z");
            let commandId = "60074c0abfa892318231bba5";

            let commandPromises = [];
            commandPromises.push(new CommandRun({
                id: commandId,
                command: "testcommand",
                duration: "5.15s",
                status: "completed",
                error: null,
                createdBy: "username",
                createdOn: date,
                result: "commandresult",
                statusCode: null
            }).save());

            Promise.all(commandPromises).then(() =>  {
                request.get(`/api/command/view/${commandId}`)
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }
                    res.should.have.status(200);

                    chai.expect(res.body).to.eql({
                        id: commandId,
                        command: "testcommand",
                        duration: "5.15s",
                        status: "completed",
                        error: null,
                        createdBy: "username",
                        createdOn: date.toISOString(),
                        result: "commandresult",
                        statusCode: null
                    });

                    done();
                });
            });
        });
    });

    describe('Validate FNN', () => {
        beforeEach(async function() {
            // Stubs the axios cookie jar wrapper function so unit tests will run
            sinon.stub(axiosCookieJarSupport, 'wrapper').callsFake(function(_) {});

            // Creates API instances that are required for validateFNN
            let promises = [];

            promises.push(new Api({
                name: "MAGPIE",
                active: true,
                type: "rest",
                method: "get",
                baseUrl: "'https://magpie.hostname'"
            }).save());

            promises.push(new Api({
                name: "COMMPilot",
                active: true,
                type: "rest",
                method: "post",
                baseUrl: "'https://commpilot.hostname'"
            }).save());

            promises.push(new Api({
                name: "BandF",
                active: true,
                type: "rest",
                method: "post",
                baseUrl: "'https://brunoandfluffy.hostname'"
            }).save());

            await Promise.all(promises);
        });

        afterEach(async function() {
            sinon.restore();
        });

        it('Unauthenticated request', async() => {
            let res = await request.get('/api/command/validateFnn/N1111111R').unset('Authorization');
            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: {
                            FOUND: true,
                            SUCCESS: true
                        }
                    };
                }
            });
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                let token = await helpers.authenticateApi(request, username);
                let res = await request.get('/api/command/validateFnn/N1111111R').set('Authorization', `Bearer ${token}`);

                switch(username) {
                    case 'apiAccess':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('MAGPIE response contains FOUND and SUCCESS true', (done) => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: {
                            FOUND: true,
                            SUCCESS: true
                        },
                        status: 200
                    };
                }
            });

            request.get('/api/command/validateFnn/N1111111R')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: true });

                done();
            });
        });

        it('MAGPIE response contains FOUND and SUCCESS false', (done) => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: {
                            FOUND: false,
                            SUCCESS: false
                        },
                        status: 200
                    };
                }
            });

            request.get('/api/command/validateFnn/N1111111R')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: false });

                done();
            });
        });

        it('MAGPIE response contains FOUND false and SUCCESS true', (done) => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: {
                            FOUND: false,
                            SUCCESS: true
                        },
                        status: 200
                    };
                }
            });

            request.get('/api/command/validateFnn/N1111111R')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: true });

                done();
            });
        });

        it('MAGPIE response contains FOUND true and SUCCESS false', (done) => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: {
                            FOUND: true,
                            SUCCESS: false
                        },
                        status: 200
                    };
                }
            });

            request.get('/api/command/validateFnn/N1111111R')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: true });

                done();
            });
        });

        it('MAGPIE response is empty', (done) => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: {},
                        status: 200
                    };
                }
            });

            request.get('/api/command/validateFnn/N1111111R')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: false });

                done();
            });
        });

        it('MAGPIE response is null', (done) => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: null,
                        status: 200
                    };
                }
            });

            request.get('/api/command/validateFnn/N1111111R')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: false });

                done();
            });
        });

        it('MAGPIE response is a string', (done) => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: "string",
                        status: 200
                    };
                }
            });

            request.get('/api/command/validateFnn/N1111111R')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: false });

                done();
            });
        });

        it('MAGPIE response contains success key', (done) => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: {
                            success: "expected"
                        },
                        status: 200
                    };
                }
            });

            request.get('/api/command/validateFnn/N1111111R')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: false });

                done();
            });
        });

        it('MAGPIE response contains user key', (done) => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: {
                            user: "expected"
                        },
                        status: 200
                    };
                }
            });

            request.get('/api/command/validateFnn/N1111111R')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: false });

                done();
            });
        });

        it('MAGPIE response has FOUND field but no SUCCESS field', (done) => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: {
                            FOUND: true
                        },
                        status: 200
                    };
                }
            });

            request.get('/api/command/validateFnn/N1111111R')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: true });

                done();
            });
        });

        it('MAGPIE response has SUCCESS field but no FOUND field', (done) => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: {
                            SUCCESS: true
                        },
                        status: 200
                    };
                }
            });

            request.get('/api/command/validateFnn/N1111111R')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: true });

                done();
            });
        });

        it('MAGPIE response has FOUND and SUCCESS field that is not a boolean', (done) => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: {
                            FOUND: "true",
                            SUCCESS: "true"
                        },
                        status: 200
                    };
                }
            });

            request.get('/api/command/validateFnn/N1111111R')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: false });

                done();
            });
        });

        it('MAGPIE response has SUCCESS field that is not a boolean', (done) => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: {
                            FOUND: "true",
                            SUCCESS: true
                        },
                        status: 200
                    };
                }
            });

            request.get('/api/command/validateFnn/N1111111R')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: true });

                done();
            });
        });

        it('Bruno and Fluffy response contains correct response and carrier for IMSI test 1', (done) => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: {
                            id: "test-id-1",
                            number: "123456789012345",
                            results: {
                                statusCode: 200,
                                error: false,
                                APIresponse: {
                                    Carrier: "Retail"
                                }
                            }
                        },
                        status: 200
                    };
                }
            });

            request.get('/api/command/validateFnn/123456789012345')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: true });

                done();
            });
        });

        it('Bruno and Fluffy response contains correct response and carrier for IMSI test 2', (done) => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: {
                            id: "test-id-1",
                            number: "123456789012345",
                            results: {
                                statusCode: 200,
                                error: false,
                                APIresponse: {
                                    Carrier: "Jasper"
                                }
                            }
                        },
                        status: 200
                    };
                }
            });

            request.get('/api/command/validateFnn/123456789012345')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: true });

                done();
            });
        });

        it('Bruno and Fluffy response contains correct response and incorrect carrier for IMSI', (done) => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: {
                            id: "test-id-1",
                            number: "123456789012345",
                            results: {
                                statusCode: 200,
                                error: false,
                                APIresponse: {
                                    Carrier: "Other Carrier"
                                }
                            }
                        },
                        status: 200
                    };
                }
            });

            request.get('/api/command/validateFnn/123456789012345')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: false });

                done();
            });
        });

        it('Bruno and Fluffy response contains incorrect response (empty object)', (done) => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: {},
                        status: 200
                    };
                }
            });

            request.get('/api/command/validateFnn/123456789012345')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: false });

                done();
            });
        });

        it('Bruno and Fluffy response contains incorrect response (null)', (done) => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: null,
                        status: false
                    };
                }
            });

            request.get('/api/command/validateFnn/123456789012345')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: false });

                done();
            });
        });

        it('Bruno and Fluffy response is a string', (done) => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: "string",
                        status: false
                    };
                }
            });

            request.get('/api/command/validateFnn/123456789012345')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: false });

                done();
            });
        });

        it('COMMPilot response contains user key (string)', (done) => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: {
                            user: "expected"
                        },
                        status: 200
                    };
                }
            });

            request.get('/api/command/validateFnn/0899999999')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: true });

                done();
            });
        });

        it('COMMPilot response contains user key (boolean)', (done) => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: {
                            user: false
                        },
                        status: false
                    };
                }
            });

            request.get('/api/command/validateFnn/0899999999')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: false });

                done();
            });
        });

        it('COMMPilot response contains user key (number)', (done) => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: {
                            user: 5
                        },
                        status: false
                    };
                }
            });

            request.get('/api/command/validateFnn/0899999999')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: true });

                done();
            });
        });

        it('COMMPilot response does not contain success key', (done) => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: {
                            field: true
                        },
                        status: false
                    };
                }
            });

            request.get('/api/command/validateFnn/0899999999')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: false });

                done();
            });
        });

        it('COMMPilot response is empty', (done) => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: {},
                        status: false
                    };
                }
            });

            request.get('/api/command/validateFnn/0899999999')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: false });

                done();
            });
        });

        it('COMMPilot response is null', (done) => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: null,
                        status: false
                    };
                }
            });

            request.get('/api/command/validateFnn/0899999999')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: false });

                done();
            });
        });

        it('COMMPilot response is a string', (done) => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: "string",
                        status: false
                    };
                }
            });

            request.get('/api/command/validateFnn/0899999999')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: false });

                done();
            });
        });

        it('COMMPilot response contains FOUND and SUCCESS true', (done) => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: {
                            FOUND: true,
                            SUCCESS: true
                        },
                        status: 200
                    };
                }
            });

            request.get('/api/command/validateFnn/0899999999')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: false });

                done();
            });
        });

        it('COMMPilot response contains success key', (done) => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: {
                            success: "expected"
                        },
                        status: 200
                    };
                }
            });

            request.get('/api/command/validateFnn/0899999999')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }
                res.should.have.status(200);

                chai.expect(res.body).to.eql({ isValid: false });

                done();
            });
        });
    });

    describe('Validate FNN Check, synchronous poll start', () => {
        beforeEach(async function() {
            // Stubs the axios cookie jar wrapper function so unit tests will run
            sinon.stub(axiosCookieJarSupport, 'wrapper').callsFake(function(_) {});

            // Creates API instances that are required for validateFNN
            let promises = [];

            promises.push(new Api({
                name: "MAGPIE",
                active: true,
                type: "rest",
                method: "get",
                baseUrl: "'https://magpie.hostname'"
            }).save());

            promises.push(new Api({
                name: "COMMPilot",
                active: true,
                type: "rest",
                method: "post",
                baseUrl: "'https://commpilot.hostname'"
            }).save());

            promises.push(new Api({
                name: "BandF",
                active: true,
                type: "rest",
                method: "post",
                baseUrl: "'https://brunoandfluffy.hostname'"
            }).save());

            await Promise.all(promises);
        });

        afterEach(async function() {
            sinon.restore();
        });

        it('Unauthenticated request', async() => {
            let res = await request.get('/api/command/validateFnnCheck').send({ fnn: 'N1111111R' }).unset('Authorization');
            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: {
                            FOUND: true,
                            SUCCESS: true
                        }
                    };
                }
            });
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                let token = await helpers.authenticateApi(request, username);
                let res = await request.post('/api/command/validateFnnCheck').send({ fnn: 'N1111111R' }).set('Authorization', `Bearer ${token}`);

                switch(username) {
                    case 'apiAccess':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('MAGPIE response contains FOUND and SUCCESS true', async() => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: {
                            FOUND: true,
                            SUCCESS: true
                        },
                        status: 200
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: 'N1111111R' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(true);
                    validateComplete = true;
                }
            }
        });


        it('MAGPIE response contains FOUND and SUCCESS false', async() => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: {
                            FOUND: false,
                            SUCCESS: false
                        },
                        status: 200
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: 'N1111111R' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(false);
                    validateComplete = true;
                }
            }
        });

        it('MAGPIE response contains FOUND false and SUCCESS true', async() => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: {
                            FOUND: false,
                            SUCCESS: true
                        },
                        status: 200
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: 'N1111111R' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(true);
                    validateComplete = true;
                }
            }
        });

        it('MAGPIE response contains FOUND true and SUCCESS false', async() => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: {
                            FOUND: true,
                            SUCCESS: false
                        },
                        status: 200
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: 'N1111111R' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(true);
                    validateComplete = true;
                }
            }
        });

        it('MAGPIE response is empty', async() => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: {},
                        status: 200
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: 'N1111111R' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(false);
                    validateComplete = true;
                }
            }
        });

        it('MAGPIE response is null', async() => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: null,
                        status: 200
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: 'N1111111R' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(false);
                    validateComplete = true;
                }
            }
        });

        it('MAGPIE response is a string', async() => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: "string",
                        status: 200
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: 'N1111111R' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(false);
                    validateComplete = true;
                }
            }
        });

        it('MAGPIE response contains success key', async() => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: {
                            success: "expected"
                        },
                        status: 200
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: 'N1111111R' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(false);
                    validateComplete = true;
                }
            }
        });

        it('MAGPIE response contains user key', async() => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: {
                            user: "expected"
                        },
                        status: 200
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: 'N1111111R' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(false);
                    validateComplete = true;
                }
            }
        });

        it('MAGPIE response has FOUND field but no SUCCESS field', async() => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: {
                            FOUND: true
                        },
                        status: 200
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: 'N1111111R' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(true);
                    validateComplete = true;
                }
            }
        });

        it('MAGPIE response has SUCCESS field but no FOUND field', async() => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: {
                            SUCCESS: true
                        },
                        status: 200
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: 'N1111111R' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(true);
                    validateComplete = true;
                }
            }
        });

        it('MAGPIE response has FOUND and SUCCESS field that is not a boolean', async() => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: {
                            FOUND: "true",
                            SUCCESS: "true"
                        },
                        status: 200
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: 'N1111111R' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(false);
                    validateComplete = true;
                }
            }
        });

        it('MAGPIE response has SUCCESS and FOUND field that is not a boolean', async() => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: {
                            FOUND: "true",
                            SUCCESS: "true"
                        },
                        status: 200
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: 'N1111111R' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(false);
                    validateComplete = true;
                }
            }
        });

        it('Bruno and Fluffy response contains correct response and carrier for IMSI test 1', async() => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: {
                            id: "test-id-1",
                            number: "123456789012345",
                            results: {
                                statusCode: 200,
                                error: false,
                                APIresponse: {
                                    Carrier: "Retail"
                                }
                            }
                        },
                        status: 200
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: '123456789012345' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(true);
                    validateComplete = true;
                }
            }
        });

        it('Bruno and Fluffy response contains correct response and carrier for IMSI test 2', async() => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: {
                            id: "test-id-1",
                            number: "123456789012345",
                            results: {
                                statusCode: 200,
                                error: false,
                                APIresponse: {
                                    Carrier: "Jasper"
                                }
                            }
                        },
                        status: 200
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: '123456789012345' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(true);
                    validateComplete = true;
                }
            }
        });

        it('Bruno and Fluffy response contains correct response and incorrect carrier for IMSI', async() => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: {
                            id: "test-id-1",
                            number: "123456789012345",
                            results: {
                                statusCode: 200,
                                error: false,
                                APIresponse: {
                                    Carrier: "Other Carrier"
                                }
                            }
                        },
                        status: 200
                    };
                }
            });
            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: '123456789012345' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(false);
                    validateComplete = true;
                }
            }
        });

        it('Bruno and Fluffy response contains incorrect response (empty object)', async() => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: {},
                        status: 200
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: '123456789012345' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(false);
                    validateComplete = true;
                }
            }
        });

        it('Bruno and Fluffy response contains incorrect response (null)', async() => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: {},
                        status: 200
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: '123456789012345' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(false);
                    validateComplete = true;
                }
            }
        });

        it('Bruno and Fluffy response contains incorrect response (string)', async() => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: "string",
                        status: false
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: '123456789012345' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(false);
                    validateComplete = true;
                }
            }
        });

        it('COMMPilot response contains user key (string)', async() => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: {
                            user: "expected"
                        },
                        status: 200
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: '0899999999' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(true);
                    validateComplete = true;
                }
            }
        });

        it('COMMPilot response contains user key (boolean)', async() => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: {
                            user: false
                        },
                        status: false
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: '0899999999' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(false);
                    validateComplete = true;
                }
            }
        });

        it('COMMPilot response contains user key (number)', async() => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: {
                            user: 5
                        },
                        status: false
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: '0899999999' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(true);
                    validateComplete = true;
                }
            }
        });

        it('COMMPilot response does not contain success key', async() => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: {
                            field: true
                        },
                        status: false
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: '0899999999' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(false);
                    validateComplete = true;
                }
            }
        });

        it('COMMPilot response is empty', async() => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: {},
                        status: false
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: '0899999999' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(false);
                    validateComplete = true;
                }
            }
        });

        it('COMMPilot response is null', async() => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: null,
                        status: false
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: '0899999999' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(false);
                    validateComplete = true;
                }
            }
        });

        it('COMMPilot response is a string', async() => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: "string",
                        status: false
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: '0899999999' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(false);
                    validateComplete = true;
                }
            }
        });

        it('COMMPilot response contains FOUND and SUCCESS true', async() => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: {
                            FOUND: true,
                            SUCCESS: true
                        },
                        status: 200
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: '0899999999' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(false);
                    validateComplete = true;
                }
            }
        });

        it('COMMPilot response contains success key', async() => {
            sinon.stub(axios, "create").returns({
                post: async function() {
                    return {
                        data: {
                            success: "expected"
                        },
                        status: 200
                    };
                }
            });

            let res = await request.post('/api/command/validateFnnCheck').send({ fnn: '0899999999' });
            res.should.have.status(200);

            let id = res.body.id;

            let validateComplete = false;
            while (!validateComplete) {
                let resultRes = await request.get(`/api/command/validateFnnCheck/${id}`);
                resultRes.should.have.status(200);

                if (resultRes.body.status === 'done') {
                    chai.expect(resultRes.body.isValid).to.equal(false);
                    validateComplete = true;
                }
            }
        });
    });

    describe('Validate FNN Check, synchronous poll retrieve', () => {
        it('Unauthenticated request', async() => {
            await new ValidateFnnCheck({
                id: 'checkId',
                fnn: 'N1111111R',
                status: 'done',
                isValid: true
            }).save();

            let res = await request.get('/api/command/validateFnnCheck/checkId').unset('Authorization');
            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            sinon.stub(axios, "create").returns({
                get: async function() {
                    return {
                        data: {
                            FOUND: true,
                            SUCCESS: true
                        }
                    };
                }
            });
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await new ValidateFnnCheck({
                    id: 'checkId',
                    fnn: 'N1111111R',
                    status: 'done',
                    isValid: true,
                    createdBy: username
                }).save();

                let token = await helpers.authenticateApi(request, username);
                let res = await request.get('/api/command/validateFnnCheck/checkId').set('Authorization', `Bearer ${token}`);

                switch(username) {
                    case 'apiAccess':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }

                await ValidateFnnCheck.deleteOne({ id: 'checkId' });
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Non-existant ID', async() => {
            let id = '11111111111111111111111111111111';
            let res = await request.get(`/api/command/validateFnnCheck/${id}`);
            res.should.have.status(404);
        });

        it('Existing ID, incorrect user', async() => {
            let id = '11111111111111111111111111111111';
            await new ValidateFnnCheck({
                id: id,
                fnn: 'N1111111R',
                status: 'done',
                isValid: true,
                createdBy: 'anotheruser'
            }).save();

            let res = await request.get(`/api/command/validateFnnCheck/${id}`);
            res.should.have.status(403);
        });

        it('Existing ID, correct user', async() => {
            let id = '11111111111111111111111111111111';
            await new ValidateFnnCheck({
                id: id,
                fnn: 'N1111111R',
                status: 'done',
                isValid: true,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME
            }).save();

            let res = await request.get(`/api/command/validateFnnCheck/${id}`);
            res.should.have.status(200);


            chai.expect(res.body).to.be.like({
                id: id,
                fnn: 'N1111111R',
                status: 'done',
                isValid: true,
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                createdOn: new Date()
            });
        });
    });
});
