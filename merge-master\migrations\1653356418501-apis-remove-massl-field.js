/**
 * Note: This migration file was created when 'migrate-mongoose' was the npm package
 * used to perform MongoDB migrations, since the 'migrate' npm package is now used
 * the implementation may not be compatible and all functionality is commented out
 */

// const mongoose = require('mongoose');
// const migrate = require('./migrate');


// const resultAPISchema = new mongoose.Schema({
//     name: { type: String, required: true },
//     timeout: { type: Number, minimum: 1, default: 60000 },
//     uri: { type: String, required: true },
//     header: { type: String, default: "" },
//     authKeyDb: { type: String, default: "" },
//     parseResponse: { type: String, default: "" },
//     transform: { type: mongoose.Schema.Types.Mixed, default: null }
// }, { _id: false });

// const asyncPollSchema = new mongoose.Schema({
//     name: { type: String, required: true },
//     interval: { type: Number, default: null },
//     uri: { type: String, default: "" },
//     header: { type: String, default: "" },
//     authKeyDb: { type: String, default: "" },
//     timeout: { type: Number, min: 1, default: 60000 },
//     parseResponse: { type: String, default: "" },
//     doneCondition: { type: String, required: true },
//     errorCondition: { type: String, default: "" },
//     transform: { type: mongoose.Schema.Types.Mixed, default: null },
//     resultAPI: { type: resultAPISchema, default: null }
// }, { _id: false });

// const apiSchema = new mongoose.Schema({
//     name: { type: String, validate: /^([A-Za-z0-9]+[:\w\-\.\s]*|)$/, unique: true, required: true, immutable: true },
//     description: { type: String, default: "" },
//     active: { type: Boolean, default: false },
//     apiType: { type: String, enum: [ "rest" ], default: "rest" },
//     method: { type: String, enum: [ "post", "get" ], default: "get" },
//     parameters: { type: [ String ], default: [] },
//     uri: { type: String, default: "" },
//     body: { type: String, default: "" },
//     header: { type: String, default: "" },
//     authKeyDb: { type: String, default: null },
//     timeout: { type: Number, min: 1, default: 300000 },
//     parseResponse: { type: String, default: null },
//     pollCondition: { type: String, default: null },
//     proxyRequired: { type: Boolean, default: false },
//     useCookies: { type: Boolean, default: false },
//     tlsMinVersion: { type: String, enum: [ null, "TLSv1.3", "TLSv1.2", "TLSv1.1", "TLSv1" ], default: null },
//     errorCondition: { type: String, default: "typeof response.data.error !== 'undefined'" },
//     asyncPoll: { type: asyncPollSchema, default: null },
//     masslCertificateName: { type: String, default: null },
//     wikiPage: { type: String, default: "" },
//     createdBy: { type: String, required: true, immutable: true, default: "unknown" },
//     createdOn: { type: Date, required: true, immutable: true, default: Date.now }
// });

// apiSchema.methods.toJSON = function() {
//     let obj = this.toObject();
//     delete obj._id;
//     delete obj.__v;
//     return obj;
// }


// const Api = mongoose.model("api", apiSchema);


/**
 * Make any changes you need to make to the database here
 */
async function up () {
    // Write migration here
    // await migrate.connect();

    // await Api.updateMany({}, {
    //     $unset: {
    //         massl: 1,
    //         "asyncPoll.massl": 1
    //     }
    // }, { multi: true, strict: false });
}

/**
 * Make any changes that UNDO the up function side effects here (if possible)
 */
async function down () {
    // Write migration here
}

module.exports = { up, down };
