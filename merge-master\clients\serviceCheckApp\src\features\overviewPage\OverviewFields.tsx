import { Panel } from '../../components/panel/Panel'
import { useAppState } from '../../hooks/useAppState'
import { NetworkMap } from '../networkMap/NetworkMap'
import { OverviewLayoutContainer } from '../serviceCheck/components/overview/ OverviewLayoutContainer'
import { AccountStatusPanel } from '../serviceDetails/accountStatus/AccountStatusPanel'
import { ActiveIncidentsPanel } from '../serviceDetails/activeIncidents/ActiveIncidentsPanel'
import { DeviceDetailsPanel } from '../serviceDetails/deviceDetails/DeviceDetailsPanel'
// import { BackupField } from '../serviceDetails/deviceDetails/fields/BackupField'
import { BasementSwitchField } from '../serviceDetails/deviceDetails/fields/BasementSwitchField'
import { DeviceNameField } from '../serviceDetails/deviceDetails/fields/DeviceNameField'
import { DeviceStatusVoipField } from '../serviceDetails/deviceDetails/fields/DeviceStatusVoipField'
import { DeviceRegisterationStatusVoipField } from '../serviceDetails/deviceDetails/fields/DeviceRegisterationStatusVoipField'
import { DeviceClusterVoipField } from '../serviceDetails/deviceDetails/fields/DeviceClusterVoipField'
import { DeviceNameVoipField } from '../serviceDetails/deviceDetails/fields/DeviceNameVoipField'
import { DeviceVersionVoipField } from '../serviceDetails/deviceDetails/fields/DeviceVersionVoipField'
import { DeviceIPWorksDomainVoipField } from '../serviceDetails/deviceDetails/fields/DeviceIPWorksDomainVoipField'
import { DeviceUserFNNVoipField } from '../serviceDetails/deviceDetails/fields/DeviceUserFNNVoipField'
import { DeviceGroupFNNVoipField } from '../serviceDetails/deviceDetails/fields/DeviceGroupFNNVoipField'
import { DeviceTrunkFNNVoipField } from '../serviceDetails/deviceDetails/fields/DeviceTrunkFNNVoipField'
import { DeviceSiteFNNVoipField } from '../serviceDetails/deviceDetails/fields/DeviceSiteFNNVoipField'
import { DeviceNumberVoipField } from '../serviceDetails/deviceDetails/fields/DeviceNumberVoipField'
import { DeviceDetailsVoipField } from '../serviceDetails/deviceDetails/fields/DeviceDetailsVoipField'
import { DeviceRegistrationsVoipField } from '../serviceDetails/deviceDetails/fields/DeviceRegistrationsVoipField'
import { DeviceTypeSIPNTUField } from '../serviceDetails/deviceDetails/fields/DeviceTypeSIPNTUField'
import { CarriageFNNField } from '../serviceDetails/productDetails/fields/CarriageFNNField'
import { CarriageTypeField } from '../serviceDetails/productDetails/fields/CarriageTypeField'
// import { DeviceStatusField } from '../serviceDetails/deviceDetails/fields/DeviceStatusField'
import { DeviceTypeField } from '../serviceDetails/deviceDetails/fields/DeviceTypeField'
import { NtuField } from '../serviceDetails/deviceDetails/fields/NtuField'
import { NtuTypeField } from '../serviceDetails/deviceDetails/fields/NtuTypeField'
import { ProductCodeField } from '../serviceDetails/deviceDetails/fields/ProductCodeField'
import { SerialNumberField } from '../serviceDetails/deviceDetails/fields/SerialNumberField'
import { SupplierCodeField } from '../serviceDetails/deviceDetails/fields/SupplierCodeField'
import { OutagesPanel } from '../serviceDetails/outages/OutagesPanel'
import { AccessTypeField } from '../serviceDetails/productDetails/fields/AccessTypeField'
import { AssociatedAccessServiceField } from '../serviceDetails/productDetails/fields/AssociatedAccessServiceField'
import { AssociatedAccessServiceFnnField } from '../serviceDetails/productDetails/fields/AssociatedAccessServiceFnnField'
import { AvcField } from '../serviceDetails/productDetails/fields/AvcField'
import { AvcNBNEEField } from '../serviceDetails/productDetails/fields/AvcNBNEEField'
import { BpiIdField } from '../serviceDetails/productDetails/fields/BpiIdField'
import { CategoryOfServiceField } from '../serviceDetails/productDetails/fields/CategoryOfServiceField'
import { CeIpAddressField } from '../serviceDetails/productDetails/fields/CeIpAddressField'
import { FNNField } from '../serviceDetails/productDetails/fields/FNNField'
import { InterfaceTypeField } from '../serviceDetails/productDetails/fields/InterfaceTypeField'
import { IpwanNetworkField } from '../serviceDetails/productDetails/fields/IpwanNetworkField'
import { IpwanPortField } from '../serviceDetails/productDetails/fields/IpwanPortField'
import { MdnNetworkFnnField } from '../serviceDetails/productDetails/fields/MdnNetworkFnnField'
import { MdnServiceField } from '../serviceDetails/productDetails/fields/MdnServiceField'
import { MediaTypeField } from '../serviceDetails/productDetails/fields/MediaTypeField'
import { OvcTypeField } from '../serviceDetails/productDetails/fields/OvcTypeField'
import { ProductField } from '../serviceDetails/productDetails/fields/ProductField'
import { RoutingProtocolField } from '../serviceDetails/productDetails/fields/RoutingProtocolField'
import { ServiceSpeedField } from '../serviceDetails/productDetails/fields/ServiceSpeedField'
import { ServiceTypeField } from '../serviceDetails/productDetails/fields/ServiceTypeField'
// import { TrunkFnnField } from '../serviceDetails/productDetails/fields/TrunkFnnField'
// import { CfnnClusterField } from '../serviceDetails/deviceDetails/fields/CfnnClusterField'
// import { CfnnDeviceField } from '../serviceDetails/deviceDetails/fields/CfnnDeviceField'
// import { CfnnNumberField } from '../serviceDetails/deviceDetails/fields/CfnnNumberField'
// import { CfnnRegistrationStatusField } from '../serviceDetails/deviceDetails/fields/CfnnRegistrationStatusField'
// import { CfnnStatusField } from '../serviceDetails/deviceDetails/fields/CfnnStatusField'
// import { CfnnVersionField } from '../serviceDetails/deviceDetails/fields/CfnnVersionField'
import { PhoneNumberRangesField } from '../serviceDetails/productDetails/fields/PhoneNumberRangesField'
import { UniPortField } from '../serviceDetails/productDetails/fields/UniPortField'
import { ProductDetailsPanel } from '../serviceDetails/productDetails/ProductDetailsPanel'

export const OverviewFields = () => {
    const { appState } = useAppState()
    const productTypes = appState.serviceDetails.productTypes
    const deviceDetails = appState.serviceDetails?.deviceDetails || {}
    const productDetails = appState.serviceDetails?.productDetails || {}
    const accountStatus = appState.serviceDetails?.accountStatus

    return (
        <OverviewLayoutContainer
            networkDiagramsPanel={<NetworkMap />}
            productDetailsPanel={
                <ProductDetailsPanel>
                    <>
                        <Panel>
                            <>
                                <ProductField
                                    value={productDetails.productName}
                                />
                                <FNNField value={productDetails.fnn} />
                                <ServiceTypeField
                                    value={productDetails.serviceType}
                                />
                                {productTypes?.some((type) =>
                                    [
                                        'ADSL',
                                        'BDSL',
                                        'IPMAN',
                                        'NBN',
                                        'NBN EE',
                                    ].includes(type)
                                ) && (
                                    <ServiceSpeedField
                                        value={productDetails.serviceSpeed}
                                    />
                                )}
                                {productTypes?.some((type) =>
                                    [
                                        'ADSL',
                                        'BDSL',
                                        'FR',
                                        'INTERNATIONAL',
                                        'IPMAN',
                                        'MOBILE',
                                        'NBN',
                                        'NBN EE',
                                        'NextG IPWAN',
                                        'Telstra Fibre Adapt',
                                    ].includes(type)
                                ) && (
                                    <CarriageFNNField
                                        value={productDetails.carriageFNN}
                                    />
                                )}
                                <CarriageTypeField
                                    value={productDetails.carriageType}
                                />
                            </>
                        </Panel>

                        {productTypes?.includes('NBN EE') &&
                            (productDetails.associatedAccessService ||
                                productDetails.associatedAccessServiceFNN ||
                                productDetails.mediaType ||
                                productDetails.bpiId ||
                                productDetails.avcNBNEE ||
                                productDetails.interfaceType ||
                                productDetails.ovcType ||
                                productDetails.uniPort) && (
                                <Panel>
                                    <>
                                        {/*<TrunkFnnField
                                        value={productDetails.trunkFNN}
                                    />*/}
                                        <AssociatedAccessServiceField
                                            value={
                                                productDetails.associatedAccessService
                                            }
                                        />
                                        <AssociatedAccessServiceFnnField
                                            value={
                                                productDetails.associatedAccessServiceFNN
                                            }
                                        />
                                        <MediaTypeField
                                            value={productDetails.mediaType}
                                        />
                                        <BpiIdField
                                            value={productDetails.bpiId}
                                        />
                                        <AvcNBNEEField
                                            value={productDetails.avcNBNEE}
                                        />
                                        <InterfaceTypeField
                                            value={productDetails.interfaceType}
                                        />
                                        <OvcTypeField
                                            value={productDetails.ovcType}
                                        />
                                        <UniPortField
                                            value={productDetails.uniPort}
                                        />
                                    </>
                                </Panel>
                            )}

                        {productTypes?.includes('NBN') && (
                            <Panel>
                                <>
                                    <AvcField value={productDetails.avc} />
                                    <AccessTypeField
                                        value={productDetails.accessType}
                                    />
                                </>
                            </Panel>
                        )}

                        {productTypes?.some((type) =>
                            ['BDSL', 'IPMAN', 'NBN', 'NBN EE'].includes(type)
                        ) &&
                            productDetails.ipwanNetwork && (
                                <Panel>
                                    <>
                                        <IpwanNetworkField
                                            value={productDetails.ipwanNetwork}
                                        />
                                        {productTypes?.some((type) =>
                                            ['BDSL', 'IPMAN', 'NBN'].includes(
                                                type
                                            )
                                        ) && (
                                            <>
                                                <IpwanPortField
                                                    value={
                                                        productDetails.ipwanPort
                                                    }
                                                />
                                                <CeIpAddressField
                                                    value={
                                                        productDetails.ceIpAddress
                                                    }
                                                />
                                                <RoutingProtocolField
                                                    value={
                                                        productDetails.routingProtocol
                                                    }
                                                />
                                            </>
                                        )}
                                    </>
                                </Panel>
                            )}

                        {productTypes?.some((type) =>
                            [
                                'ADSL',
                                'BDSL',
                                'FR',
                                'INTERNATIONAL',
                                'IPMAN',
                                'MOBILE',
                                'NBN',
                                'NBN EE',
                                'NextG IPWAN',
                                'Telstra Fibre Adapt',
                            ].includes(type)
                        ) &&
                            productDetails.categoryOfService && (
                                <Panel>
                                    <>
                                        <CategoryOfServiceField
                                            value={
                                                productDetails.categoryOfService
                                            }
                                        />
                                        {productTypes?.includes('MDN') && (
                                            <>
                                                <MdnNetworkFnnField
                                                    value={
                                                        productDetails.mdnNetworkFnn
                                                    }
                                                />
                                                <MdnServiceField
                                                    value={
                                                        productDetails.mdnService
                                                    }
                                                />
                                            </>
                                        )}
                                    </>
                                </Panel>
                            )}

                        {productTypes?.includes('VOIP') && (
                            <PhoneNumberRangesField
                                value={productDetails.phoneNumberRanges ?? null}
                            />
                        )}
                    </>
                </ProductDetailsPanel>
            }
            deviceDetailsPanel={
                <DeviceDetailsPanel>
                    <>
                        {productTypes?.includes('MDN') && (
                            <Panel>
                                <>
                                    <DeviceNameField
                                        value={deviceDetails.deviceName}
                                    />
                                    <DeviceTypeField
                                        value={deviceDetails.deviceType}
                                    />
                                    <SupplierCodeField
                                        value={deviceDetails.supplierCode}
                                    />
                                    <ProductCodeField
                                        value={deviceDetails.productCode}
                                    />
                                    <SerialNumberField
                                        value={deviceDetails.deviceSerialNumber}
                                    />
                                    {/* <BackupField value={deviceDetails.backup} />
                                    <DeviceStatusField
                                        value={deviceDetails.deviceStatus}
                                    /> */}
                                </>
                            </Panel>
                        )}

                        {/* Basement Switch - Not in IPMAN NON basement, VOIP, Mobile */}
                        {productTypes?.includes('Basement Switch Device') && (
                            <Panel>
                                <BasementSwitchField
                                    value={deviceDetails.basementSwitch}
                                />
                            </Panel>
                        )}

                        {/* IPMAN NTU */}
                        {productTypes?.includes('NTU Device') && (
                            <Panel>
                                <NtuField value={deviceDetails.ntu} />
                            </Panel>
                        )}

                        {productTypes?.includes('BDSL') && (
                            <Panel>
                                <NtuTypeField value={deviceDetails.ntuType} />
                            </Panel>
                        )}

                        {productTypes?.includes('VOIP') && (
                            <>
                                {(deviceDetails.deviceStatusVoip 
                                    || deviceDetails.deviceRegisterationStatusVoip
                                    || deviceDetails.deviceClusterVoip
                                    || deviceDetails.deviceNameVoip 
                                    || deviceDetails.deviceVersionVoip 
                                    || deviceDetails.deviceIPWorksDomainVoip 
                                    || deviceDetails.deviceUserFNNVoip
                                    || deviceDetails.deviceGroupFNNVoip
                                    || deviceDetails.deviceTrunkFNNVoip
                                    || deviceDetails.deviceSiteFNNVoip
                                    || deviceDetails.deviceNumberVoip
                                    || deviceDetails.deviceDetailsVoip) && <Panel>
                                    <>
                                        <DeviceStatusVoipField value={deviceDetails.deviceStatusVoip} />
                                        <DeviceRegisterationStatusVoipField value={deviceDetails.deviceRegisterationStatusVoip} />
                                        <DeviceClusterVoipField value={deviceDetails.deviceClusterVoip} />
                                        <DeviceIPWorksDomainVoipField value={deviceDetails.deviceIPWorksDomainVoip} />
                                        <DeviceUserFNNVoipField value={deviceDetails.deviceUserFNNVoip} />
                                        <DeviceGroupFNNVoipField value={deviceDetails.deviceGroupFNNVoip} />
                                        <DeviceTrunkFNNVoipField value={deviceDetails.deviceTrunkFNNVoip} />
                                        <DeviceSiteFNNVoipField value={deviceDetails.deviceSiteFNNVoip} />
                                        <DeviceNameVoipField value={deviceDetails.deviceNameVoip} />
                                        <DeviceVersionVoipField value={deviceDetails.deviceVersionVoip} />
                                        <DeviceNumberVoipField value={deviceDetails.deviceNumberVoip} />
                                        <DeviceDetailsVoipField value={deviceDetails.deviceDetailsVoip} />
                                    </>
                                </Panel>}
                                <DeviceRegistrationsVoipField value={deviceDetails.deviceRegistrationsVoip ?? null} />
                                {deviceDetails.deviceTypeSIPNTU && <Panel>
                                    <DeviceTypeSIPNTUField value={deviceDetails.deviceTypeSIPNTU} />
                                </Panel>}
                            </>
                        )}
                    </>
                </DeviceDetailsPanel>
            }
            accountStatusPanel={
                <AccountStatusPanel accountStatus={accountStatus} />
            }
            outagesPanel={<OutagesPanel />}
            activeIncidentsPanel={<ActiveIncidentsPanel />}
        />
    )
}
