
import express from 'express';
import _ from 'lodash';

import callbackEmitter from '../../modules/callbackEmitter.js';
import CallbackResponse from '../../db/model/callbackResponse.js';
import NBNChangeRequest from '../../db/model/nbnChangeRequest.js';
import logger from '../../modules/logger.js';

const router = express.Router();


router.post('/:callbackName?', async function(req, res) {
    try {
        let callbackName = req.params.callbackName;

        switch (callbackName) {
            case 'nbnChangeRequest':
                if (req.body && ['ChangeRequestCreationNotification', 'ChangeRequestAttributeValueChangeNotification', 'ChangeRequestStateChangeNotification', 'ChangeRequestRemoveNotification'].includes(req.body.eventType)
                    && typeof _.get(req.body, ['event', 'changeRequest', 'id']) === 'string') {
                    let changeRequestId = _.get(req.body, ['event', 'changeRequest', 'id']);
                    let impactedServices = _.get(req.body, ['event', 'changeRequest', 'impactedServices'], []);
                    let nbnIds = [];

                    // Adds all IDs from impacted services to store in change request object for
                    // later reference / search, only adds strings
                    for (let i = 0; i < impactedServices.length; i++) {
                        if (Array.isArray(impactedServices[i].id)) {
                            nbnIds.push(...impactedServices[i].id.filter(nbnId => { return typeof nbnId === 'string' }));
                        }
                    }

                    let changeRequest = new NBNChangeRequest({
                        changeRequestId: changeRequestId,
                        nbnIds: nbnIds,
                        data: req.body
                    });
                    await changeRequest.save();
                }
                break;
            default:
                // All other callback names are handled as responses to a call to mergeCollect.collectApi()

                // If the database for Merge is a replica set, use MongoDB change streams to
                // send an event on saving a new document, the listener below will then receive
                // an event and send it to the callback emitter
                if (global.gConfig.dbReplicaSet) {
                    let callbackResponse = new CallbackResponse({
                        data: req.body
                    });
                    await callbackResponse.save();
                } else {
                    // Testing environment only: logs and saves callback response
                    logger.info('Callback response received');
                    let callbackResponse = new CallbackResponse({
                        data: req.body
                    });
                    await callbackResponse.save();

                    callbackEmitter.emit('response', req.body);
                }
                break;
        }

        res.sendStatus(201);
    } catch(error) {
        logger.error(`Callback response error when processing notification, ${error.toString()}`);
        res.sendStatus(500);
    }
});


export default router;
