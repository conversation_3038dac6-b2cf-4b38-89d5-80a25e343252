import { useCallback } from 'react'
import { useSocketContext } from '../../context/SocketContext'
import { useUser } from '../../context/UserContext'
import { useAppState } from '../../hooks/useAppState'

export interface FullServiceCheckParams {
    id: string
}

export const useFullServiceCheckRun = () => {
    const { socket, isConnected } = useSocketContext()
    const { appState, setAppState } = useAppState()
    const { canModify } = useUser()
    // Check that we are connected and have a valid id.
    const canRunFullServiceCheck =
        isConnected &&
        socket !== null &&
        appState.id?.trim() !== '' &&
        canModify(appState.createdBy)

    const runFullServiceCheck = useCallback(() => {
        if (!canRunFullServiceCheck) {
            return
        }
        setAppState((prevState) => ({
            ...prevState,
            serviceDetails: {
                ...prevState.serviceDetails,
                status: 'running',
            },
        }))

        // Construct the payload and emit the full service check event.
        const payload: FullServiceCheckParams = {
            id: appState.id,
        }

        socket.emit('serviceCheck:runFull', payload)
    }, [socket, appState, canRunFullServiceCheck, setAppState])

    return {
        runFullServiceCheck,
    }
}
