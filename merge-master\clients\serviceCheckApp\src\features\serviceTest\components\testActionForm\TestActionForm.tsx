import { DetailsPanel } from '../../../../components/detailsPanel/DetailsPanel'
import { FunctionNotReady } from '../../../../components/functionNotReady/FunctionNotReady'
import { useAppState } from '../../../../hooks/useAppState'
import { LoadingPanel } from '../serviceCheckLoadingPanel/LoadingPanel'
import styles from './TestActionForm.module.scss'
import { TestActionRow } from './components/TestActionRow/TestActionRow'

export const TestActionForm = () => {
    const { appState } = useAppState()

    const renderContent = () => {
        const status = appState.serviceDetails.status

        if (status === 'running') {
            return <LoadingPanel />
        } else if (status !== 'completedWithError' && status !== 'done') {
            return <FunctionNotReady />
        } else {
            return (
                <div className={styles.actionList}>
                    {appState.testActions
                        .slice()
                        .sort((a, b) => a.name.localeCompare(b.name))
                        .map((action) => (
                            <TestActionRow key={action.name} action={action} />
                        ))}
                </div>
            )
        }
    }

    return <DetailsPanel label="Run actions">{renderContent()}</DetailsPanel>
}
