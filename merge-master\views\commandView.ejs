<!-- v1 -->
<%- include('header', {}); %>
<%- include('menu', {currentTab: 'command'}); %>
<script src="/public/javascripts/ace.js" crossorigin="anonymous"></script>
<script src="/public/javascripts/merge_QC_lib.js" crossorigin="anonymous"></script>
<div class="container">
    <br>
    <div class="jumbotron py-1">
        <div class="container">
            <h2 class="display-4"><%= title  %></h2>
        </div>
    </div>
    <div id="QCResults"></div>
</div>
<script>
    const command = Object.freeze(JSON.parse(atob("<%- command %>")));
    addQCCommand(command);
    updateQCResult(command);
</script>
<%- include('footer', {}); %>