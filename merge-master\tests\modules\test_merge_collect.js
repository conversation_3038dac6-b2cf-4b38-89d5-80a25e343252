'use strict';

import axios from 'axios';
import mongoose from 'mongoose';
import chai from 'chai';
import chaiHttp from 'chai-http';
import chaiLike from 'chai-like';
import chaiDateTime from 'chai-datetime';
import chaiAsPromised from 'chai-as-promised';
import sinon from 'sinon';
import _ from 'lodash';
import superagentDefaults from 'superagent-defaults';
import supertest from 'supertest';
import fs from 'fs';

var app;
var request;
import config from '../config.js';
import helpers from '../helpers.js';
import Api from '../../db/model/api.js';
import AuthKey from '../../db/model/apiAuthKeys.js';
import mergeCollect from '../../modules/mergeCollect.js';
import callbackEmitter from '../../modules/callbackEmitter.js';
import { ApiFunctionError, ApiUriError, InactiveError, NotFoundError } from '../../modules/error.js';
import apiFunctions from '../../modules/apiFunctions.js';


chaiLike.extend(helpers.dateFieldCheck);
chaiLike.extend(helpers.regexCheck);
chai.use(chaiLike);
chai.use(chaiHttp);
chai.use(chaiDateTime);
chai.use(chaiAsPromised);

const COLLECT_API_RESULT_FIELDS = Object.freeze([
    "collectionDuration",
    "data",
    "status"
]);

const COLLECT_API_FUNCTION_RESULT_FIELDS = Object.freeze([
    "collectionDuration",
    "data"
]);


describe('collectApi()', () => {
    // Setup and connect to mongodb memory server, start merge app instance
    // Set request variable with a configured supertest agent with test JWT and app instance
    before(async function() {
        global.gConfig = _.cloneDeep(config.default);
        app = (await import('../../app.js')).default;
        request = superagentDefaults(supertest(app));
    });

    beforeEach(async function() {
        global.gConfig = _.cloneDeep(config.default);

        // Uses deleteMany instead of mongoose.connection.db.dropDatabase() because
        // custom indexes appear to be deleted if dropDatabase() is used
        for (let collectionName in mongoose.connection.collections) {
            await mongoose.connection.collections[collectionName].deleteMany({});
        }
    });

    afterEach(async function() {
        sinon.restore();
        callbackEmitter.removeAllListeners('newListener');
    });

    it('Valid API and HTTP GET OK response', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            active: true,
            description: "",
            apiType: "rest",
            method: "get",
            parameters: [],
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: "",
            wikiPage: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };

        await setupStubsAxios("get", "https://test.host/api/uri", axiosResponse);
        let result = await mergeCollect.collectApi(api, {});

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: axiosResponse.data,
            status: axiosResponse.status
        });
    });

    it('Valid API and HTTP POST OK response', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            active: true,
            description: "",
            apiType: "rest",
            method: "post",
            parameters: [],
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: "",
            wikiPage: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);
        let result = await mergeCollect.collectApi(api, {});

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: axiosResponse.data,
            status: axiosResponse.status
        });
    });

    it('Valid API and HTTP GET Error response', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            active: true,
            description: "",
            apiType: "rest",
            method: "get",
            parameters: [],
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: "",
            wikiPage: ""
        });
        await api.save();

        let axiosResponse = {
            response: {
                data: {
                    error: "Error Message Here"
                },
                status: 500
            },
            isAxiosError: true
        };

        await setupStubsAxios("get", "https://test.host/api/uri", axiosResponse);

        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(Error).then(function(error) {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: axiosResponse.response.data,
                status: axiosResponse.response.status
            });
        });
    });

    it('Valid API and HTTP POST Error response', async() => {
        let api = new Api({
            active: true,
            name: "TestApi",
            active: true,
            description: "",
            apiType: "rest",
            method: "post",
            parameters: [],
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: "",
            wikiPage: ""
        });
        await api.save();

        let axiosResponse = {
            response: {
                data: {
                    error: "Error Message Here"
                },
                status: 500
            },
            isAxiosError: true
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);

        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(Error).then(async function(error) {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: axiosResponse.response.data,
                status: axiosResponse.response.status
            });
        });
    });

    it('Inactive API', async() => {
        let api = new Api({
            name: "TestApi",
            active: false,
            apiType: "rest",
            method: "get",
        });
        await api.save();

        await setupStubsAxios("get", undefined, null);

        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(InactiveError, "API 'TestApi' is not active");
    });

    it('Undefined API URI', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "get"
        });
        await api.save();

        await setupStubsAxios("get", undefined, null);

        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(ApiUriError);
    });

    it('API with invalid baseUrl', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "get",
            baseUrl: "`https//test.host`"
        });
        await api.save();

        await setupStubsAxios("get", undefined, null);

        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(ApiUriError);
    });

    it('API with valid baseUrl', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "get",
            baseUrl: "`https://test.host`"
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };

        await setupStubsAxios("get", "https://test.host/", axiosResponse);
        let result = await mergeCollect.collectApi(api, {});

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: axiosResponse.data,
            status: axiosResponse.status
        });
    });

    it('API with invalid uri', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "get",
            baseUrl: "`https//test.host`",
            uri: "`unterminated string"
        });
        await api.save();

        await setupStubsAxios("get", undefined, null);

        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(ApiUriError);
    });

    it('API with invalid query params', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "get",
            baseUrl: "`https//test.host`",
            uri: "",
            queryParams: "throw new Error('query param error');"
        });
        await api.save();

        await setupStubsAxios("get", undefined, null);

        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(ApiUriError);
    });

    it('API with URI (evaluation parameter undefined)', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "get",
            baseUrl: "`https://test.host`",
            uri: "`${parameters.non_existant}`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };

        await setupStubsAxios("get", "https://test.host/undefined", axiosResponse);
        let result = await mergeCollect.collectApi(api, {});

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: axiosResponse.data,
            status: axiosResponse.status
        });
    });

    it('API with URI (evaluation syntax error)', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "get",
            baseUrl: "`https://test.host`",
            uri: "`${bad.syntax}",
            queryParams: ""
        });
        await api.save();

        await setupStubsAxios("get", undefined, null);

        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(ApiUriError);
    });

    it('API with URI (valid evaluation)', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "get",
            baseUrl: "`https://test.host`",
            uri: "`api/uri/${parameters.field}`",
            queryParams: "new Object({a: 9999, b: \"value\"});"
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };

        await setupStubsAxios("get", "https://test.host/api/uri/1234?a=9999&b=value", axiosResponse);
        let result = await mergeCollect.collectApi(api, { field: "1234" }, null);

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: axiosResponse.data,
            status: axiosResponse.status
        });
    });

    it('API with header (evaluation syntax error)', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "get",
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: "",
            header: "`{",
        });
        await api.save();

        await setupStubsAxios("get", undefined, null);
        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(ApiUriError);
    });

    it('API with header (invalid JSON)', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "get",
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: "",
            header: "`{\"content-type\":\"application/x-www-form-urlencoded\",}`",
        });
        await api.save();

        await setupStubsAxios("get", undefined, null);
        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(ApiUriError);
    });

    it('API with header (valid)', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "get",
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: "",
            header: "`{\"content-type\":\"application/x-www-form-urlencoded\"}`",
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };

        await setupStubsAxios("get", "https://test.host/api/uri", axiosResponse);
        let result = await mergeCollect.collectApi(api, {});

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: axiosResponse.data,
            status: axiosResponse.status
        });
    });

    it('API with body (evaluation syntax error)', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: "",
            body: "`{\"key1\": \"data1\", \"key2\": \"data2\"}",
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);
        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(ApiUriError);
    });

    it('API with body (invalid JSON)', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: "",
            body: "`{\"key1\": \"data1\", \"key2\": \"data2\", 10: 20}`",
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);
        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(ApiUriError);
    });

    it('API with body (valid)', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: "",
            body: "`{\"key1\": \"data1\", \"key2\": \"data2\"}`",
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);
        let result = await mergeCollect.collectApi(api, {});

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: axiosResponse.data,
            status: axiosResponse.status
        });
    });

    it('API with proxy required set true', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "get",
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: "",
            header: "`{\"content-type\":\"application/x-www-form-urlencoded\"}`",
            proxyRequired: true
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };

        await setupStubsAxios("get", "https://test.host/api/uri", axiosResponse);
        let result = await mergeCollect.collectApi(api, {});

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: axiosResponse.data,
            status: axiosResponse.status
        });
    });

    it('API with proxy required set false', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "get",
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: "",
            proxyRequired: false
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };

        await setupStubsAxios("get", "https://test.host/api/uri", axiosResponse);
        let result = await mergeCollect.collectApi(api, {});

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: axiosResponse.data,
            status: axiosResponse.status
        });
    });

    it('API with existing MASSL certificate name', async() => {
        global.gConfig.APIMasslCertificates = {
            MasslName: {
                cert: "/path/to/cert",
                key: "/path/to/key"
            }
        }

        sinon.stub(fs.promises, "readFile").returns(null);

        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "get",
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: "",
            masslCertificateName: 'MasslName'
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };

        await setupStubsAxios("get", "https://test.host/api/uri", axiosResponse);
        let result = await mergeCollect.collectApi(api, {});

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: axiosResponse.data,
            status: axiosResponse.status
        });
    });

    it('API with non-existant MASSL certificate name', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "get",
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: "",
            masslCertificateName: 'MasslName'
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };

        await setupStubsAxios("get", "https://test.host/api/uri", axiosResponse);
        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(NotFoundError);
    });

    it('API with existing auth key from database', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "get",
            authKeyDb: [{ authKeyHeader: 'Authorization', authKeyName: 'Test' }],
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };

        await setupStubsAxios("get", "https://test.host/api/uri", axiosResponse);
        await new AuthKey({
            name: "Test",
            authKey: "test_token_1",
            createdOn: new Date(),
            updatedOn: new Date()
        }).save();
        let result = await mergeCollect.collectApi(api, {});

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: axiosResponse.data,
            status: axiosResponse.status
        });
    });

    it('API with multiple existing existing auth keys from database', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "get",
            authKeyDb: [{ authKeyHeader: 'Authorization', authKeyName: 'Test' }, { authKeyHeader: 'Authorize', authKeyName: 'Test2' }],
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };

        await setupStubsAxios("get", "https://test.host/api/uri", axiosResponse);
        await new AuthKey({
            name: "Test",
            authKey: "test_token_1",
            createdOn: new Date(),
            updatedOn: new Date()
        }).save();
        await new AuthKey({
            name: "Test2",
            authKey: "test_token_2",
            createdOn: new Date(),
            updatedOn: new Date()
        }).save();

        let result = await mergeCollect.collectApi(api, {});

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: axiosResponse.data,
            status: axiosResponse.status
        });
    });

    it('API with non-existant auth key from database', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "get",
            authKeyDb: [{ authKeyHeader: 'Authorization', authKeyName: 'Test' }],
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };

        await setupStubsAxios("get", "https://test.host/api/uri", axiosResponse);
        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(Error, "API auth key Test is not set");
    });

    it('API HTTP GET with parse response successful', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "get",
            parseResponse: "JSON.parse(response.data)",
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: "{ \"success\": true }",
            status: 200
        };

        await setupStubsAxios("get", "https://test.host/api/uri", axiosResponse);
        let result = await mergeCollect.collectApi(api, {});

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: {
                success: true
            },
            status: axiosResponse.status
        });
    });

    it('API HTTP GET with parse response failure', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "get",
            parseResponse: "JSON.parse(response.data)",
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };

        await setupStubsAxios("get", "https://test.host/api/uri", axiosResponse);
        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(SyntaxError);
    });

    it('API HTTP GET with error condition true', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "get",
            errorCondition: "response.data.success === true",
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };

        await setupStubsAxios("get", "https://test.host/api/uri", axiosResponse);

        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(Error, "API error condition met").then(function(error) {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: axiosResponse.data,
                status: axiosResponse.status
            });
        });
    });

    it('API HTTP GET with error condition false', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "get",
            errorCondition: "response.data.success !== true",
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };

        await setupStubsAxios("get", "https://test.host/api/uri", axiosResponse);
        let result = await mergeCollect.collectApi(api, {});

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: axiosResponse.data,
            status: axiosResponse.status
        });
    });

    it('API HTTP GET with error condition failure', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "get",
            errorCondition: "response.data.success asdf== true",
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };

        await setupStubsAxios("get", "https://test.host/api/uri", axiosResponse);

        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(SyntaxError, "Unexpected identifier").then(function(error) {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: {
                    success: true
                },
                status: axiosResponse.status
            });
        });
    });

    it('API HTTP POST with parse response successful', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            parseResponse: "JSON.parse(response.data)",
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: "{ \"success\": true }",
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);
        let result = await mergeCollect.collectApi(api, {});

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: {
                success: true
            },
            status: axiosResponse.status
        });
    });

    it('API HTTP POST with parse response failure', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            parseResponse: "JSON.parse(response.data)",
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);
        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(SyntaxError);
    });

    it('API HTTP POST with error condition true', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            errorCondition: "response.data.success === true",
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);

        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(Error, "API error condition met").then(function(error) {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: axiosResponse.data,
                status: axiosResponse.status
            });
        });
    });

    it('API HTTP POST with error condition false', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            errorCondition: "response.data.success !== true",
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);
        let result = await mergeCollect.collectApi(api, {});

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: {
                success: true
            },
            status: axiosResponse.status
        });
    });

    it('API HTTP POST with error condition failure', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            errorCondition: "response.data.success asdf== true",
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);

        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(SyntaxError, "Unexpected identifier").then(function(error) {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: axiosResponse.data,
                status: axiosResponse.status
            });
        });
    });

    it('API HTTP POST with async poll null', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            asyncPoll: null,
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true);

        let result = await mergeCollect.collectApi(api, {});

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: axiosResponse.data,
            status: axiosResponse.status
        });
    });

    it('API HTTP POST with async poll invalid uri 1', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            asyncPoll: {
                interval: 200,
                timeout: 1000,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll",
                method: "get",
                doneCondition: "response.data.status == 'completed'"
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponseSuccessful = {
            data: {
                status: "completed"
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", {}, axiosPollResponseSuccessful, 0);
        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(SyntaxError);
    });

    it('API HTTP POST with async poll invalid uri 2', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            asyncPoll: {
                name: "TestApiPoll",
                interval: 200,
                timeout: 1000,
                uri: "bad_field;",
                method: "get",
                doneCondition: "response.data.status == 'completed'",
                errorCondition: "",
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponseSuccessful = {
            data: {
                status: "completed"
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", {}, axiosPollResponseSuccessful, 0);
        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(ReferenceError, "bad_field is not defined");
    });

    it('API HTTP POST with async poll successful after 1 attempt', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            asyncPoll: {
                interval: 200,
                timeout: 1000,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll`",
                method: "get",
                doneCondition: "response.data.status == 'completed'"
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponseSuccessful = {
            data: {
                status: "completed"
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", {}, axiosPollResponseSuccessful, 0);
        let result = await mergeCollect.collectApi(api, {});

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: axiosPollResponseSuccessful.data,
            status: axiosPollResponseSuccessful.status
        });
    });

    it('API HTTP POST with async poll successful after 2 attempts', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            asyncPoll: {
                interval: 50,
                timeout: 1000,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll`",
                method: "get",
                doneCondition: "response.data.status == 'completed'"
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponsePending = {
            data: {
                status: "running"
            },
            status: 200
        };
        let axiosPollResponseSuccessful = {
            data: {
                status: "completed"
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", axiosPollResponsePending, axiosPollResponseSuccessful, 1);
        let result = await mergeCollect.collectApi(api, {});

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: axiosPollResponseSuccessful.data,
            status: axiosPollResponseSuccessful.status
        });
    });

    it('API HTTP POST with async poll successful after 2 attempts with HTTP 400 response in attempt 1', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            asyncPoll: {
                interval: 50,
                timeout: 1000,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll`",
                method: "get",
                doneCondition: "response.data.status == 'completed'"
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponsePending = {
            response: {
                data: {
                    status: "running"
                },
                status: 400,
            },
            isAxiosError: true
        };
        let axiosPollResponseSuccessful = {
            data: {
                status: "completed"
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", axiosPollResponsePending, axiosPollResponseSuccessful, 1);
        let result = await mergeCollect.collectApi(api, {});

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: axiosPollResponseSuccessful.data,
            status: axiosPollResponseSuccessful.status
        });
    });

    it('API HTTP POST with async poll successful after 2 attempts with HTTP 400 response in all attempts', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            asyncPoll: {
                interval: 10,
                timeout: 100,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll`",
                method: "get",
                doneCondition: "response.data.status == 'completed'"
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponsePending = {
            response: {
                data: {
                    status: "running"
                },
                status: 400,
            },
            isAxiosError: true
        };
        let axiosPollResponseSuccessful = {
            response: {
                data: {
                    status: "running"
                },
                status: 400,
            },
            isAxiosError: true
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", axiosPollResponsePending, axiosPollResponseSuccessful, 1);
        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(Error);
    });

    it('API HTTP POST with async poll timeout', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            asyncPoll: {
                interval: 10,
                timeout: 100,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll`",
                method: "get",
                doneCondition: "response.data.status == 'completed'"
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponsePending = {
            data: {
                status: "running"
            },
            status: 200
        };
        let axiosPollResponseSuccessful = {
            data: {
                status: "completed"
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", axiosPollResponsePending, axiosPollResponseSuccessful, 100);
        // Expects standard Error for now until proper timeout retry function is used
        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(Error);
    });

    it('API HTTP POST with async poll valid header', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            asyncPoll: {
                interval: 50,
                timeout: 100,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll`",
                header: "`{\"Content-Type\": \"application/json\"}`",
                method: "get",
                doneCondition: "response.data.status == 'completed'"
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponseSuccessful = {
            data: {
                status: "completed"
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", {}, axiosPollResponseSuccessful, 0);
        let result = await mergeCollect.collectApi(api, {});

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: axiosPollResponseSuccessful.data,
            status: axiosPollResponseSuccessful.status
        });
    });

    it('API HTTP POST with async poll invalid header', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            asyncPoll: {
                interval: 50,
                timeout: 100,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll`",
                header: "`{\"Content-Type\": \"application/json\"}",
                method: "get",
                doneCondition: "response.data.status == 'completed'"
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponseSuccessful = {
            data: {
                status: "completed"
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", {}, axiosPollResponseSuccessful, 0);
        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(SyntaxError);
    });

    it('API HTTP POST with async poll invalid header JSON', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            asyncPoll: {
                interval: 50,
                timeout: 100,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll`",
                header: "`{\"Content-Type\": \"application/json\",}`",
                method: "get",
                doneCondition: "response.data.status == 'completed'"
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponseSuccessful = {
            data: {
                status: "completed"
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", {}, axiosPollResponseSuccessful, 0);
        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(SyntaxError);
    });

    it('API HTTP POST with async poll parse response successful', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            asyncPoll: {
                interval: 50,
                timeout: 100,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll`",
                header: "`{\"Content-Type\": \"application/json\"}`",
                parseResponse: "JSON.parse(response.data)",
                method: "get",
                doneCondition: "response.data.status == 'completed'"
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponsePending = {
            data: "{ \"status\": \"running\" }",
            status: 200
        };
        let axiosPollResponseSuccessful = {
            data: "{ \"status\": \"completed\" }",
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", axiosPollResponsePending, axiosPollResponseSuccessful, 0);
        let result = await mergeCollect.collectApi(api, {});

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: {
                status: "completed"
            },
            status: axiosPollResponseSuccessful.status
        });
    });

    it('API HTTP POST with async poll parse response failure', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            asyncPoll: {
                interval: 50,
                timeout: 100,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll`",
                header: "`{\"Content-Type\": \"application/json\"}`",
                parseResponse: "JSON.parse(response.data)",
                method: "get",
                doneCondition: "response.data.status == 'completed'"
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponseSuccessful = {
            data: {
                status: "completed"
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", {}, axiosPollResponseSuccessful, 0);

        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(SyntaxError).then(function(error) {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: axiosPollResponseSuccessful.data,
                status: axiosPollResponseSuccessful.status
            });
        });
    });

    it('API HTTP POST with async poll condition true', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            pollCondition: "(response.success)",
            asyncPoll: {
                interval: 50,
                timeout: 100,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll`",
                method: "get",
                doneCondition: "response.data.status == 'completed'"
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponseSuccessful = {
            data: {
                status: "completed"
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", {}, axiosPollResponseSuccessful, 0);
        let result = await mergeCollect.collectApi(api, {});

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: axiosPollResponseSuccessful.data,
            status: axiosPollResponseSuccessful.status
        });
    });

    it('API HTTP POST with async poll condition false', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            pollCondition: "!(response.success)",
            asyncPoll: {
                interval: 50,
                timeout: 100,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll`",
                method: "get",
                doneCondition: "response.data.status == 'completed'"
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponseSuccessful = {
            data: {
                status: "completed"
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", {}, axiosPollResponseSuccessful, 0);

        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(Error, "Poll condition not met").then(function(error) {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: axiosResponse.data,
                status: axiosResponse.status
            });
        });
    });

    it('API HTTP POST with async poll condition failure', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            pollCondition: "syntax_bad;",
            asyncPoll: {
                interval: 50,
                timeout: 100,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll`",
                method: "get",
                doneCondition: "response.data.status == 'completed'"
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponseSuccessful = {
            data: {
                status: "completed"
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", {}, axiosPollResponseSuccessful, 0);

        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(ReferenceError, "syntax_bad is not defined").then(function(error) {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: axiosResponse.data,
                status: axiosResponse.status
            });
        });
    });

    it('API HTTP POST with async poll error condition true', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            asyncPoll: {
                interval: 50,
                timeout: 100,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll`",
                method: "get",
                doneCondition: "response.data.status == 'completed'",
                errorCondition: "response.data.status == 'failed'",
                errorMessage: "poll failed"
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponseSuccessful = {
            data: {
                status: "failed"
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", {}, axiosPollResponseSuccessful, 0);

        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(Error, "Error condition met for async poll API TestApiPoll").then(function(error) {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: axiosPollResponseSuccessful.data,
                status: axiosPollResponseSuccessful.status
            });
        });
    });

    it('API HTTP POST with async poll error condition false', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            asyncPoll: {
                interval: 50,
                timeout: 100,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll`",
                method: "get",
                doneCondition: "response.data.status == 'completed'",
                errorCondition: "response.data.status == 'failed'"
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponseSuccessful = {
            data: {
                status: "completed"
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", {}, axiosPollResponseSuccessful, 0);
        let response = await mergeCollect.collectApi(api, {});

        chai.expect(response).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(response).to.be.like({
            collectionDuration: /.*/,
            data: axiosPollResponseSuccessful.data,
            status: axiosPollResponseSuccessful.status
        });
    });

    it('API HTTP POST with async poll error condition failure', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            asyncPoll: {
                interval: 50,
                timeout: 100,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll`",
                method: "get",
                doneCondition: "response.data.status == 'completed'",
                errorCondition: "reference_error;"
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponseSuccessful = {
            data: {
                status: "completed"
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", {}, axiosPollResponseSuccessful, 0);

        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(ReferenceError, "reference_error is not defined").then(function(error) {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: axiosPollResponseSuccessful.data,
                status: axiosPollResponseSuccessful.status
            });
        });
    });

    it('API HTTP POST with async poll done condition failure', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            asyncPoll: {
                interval: 50,
                timeout: 100,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll`",
                method: "get",
                doneCondition: "bad_reference;"
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponseSuccessful = {
            data: {
                status: "completed"
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", {}, axiosPollResponseSuccessful, 0);

        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(ReferenceError, "bad_reference is not defined").then(function(error) {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: axiosPollResponseSuccessful.data,
                status: axiosPollResponseSuccessful.status
            });
        });
    });

    it('API HTTP POST with async poll transform response', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            asyncPoll: {
                interval: 50,
                timeout: 100,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll`",
                method: "get",
                doneCondition: "response.data",
                transform: { key_a: "JSON.parse(response.data.key_a)", key_b: "JSON.parse(response.data.key_b)" }
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponseSuccessful = {
            data: {
                key_a: "{ \"a\": 1, \"b\": true }",
                key_b: "{ \"c\": \"value\", \"d\": null }",
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", {}, axiosPollResponseSuccessful, 0);
        let response = await mergeCollect.collectApi(api, {});

        chai.expect(response).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(response).to.be.like({
            collectionDuration: /.*/,
            data: {
                key_a: {
                    a: 1,
                    b: true
                },
                key_b: {
                    c: "value",
                    d: null
                }
            },
            status: axiosPollResponseSuccessful.status
        });
    });

    it('API HTTP POST with async poll transform response failure', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            asyncPoll: {
                interval: 50,
                timeout: 100,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll`",
                method: "get",
                doneCondition: "response.data",
                transform: { key_a: "JSON.parse(response.data.key_a)", key_b: "JSON.parse(response.data.key_b)" }
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponseSuccessful = {
            data: {
                key_a: "error",
                key_b: "not parsable",
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", {}, axiosPollResponseSuccessful, 0);

        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(SyntaxError).then(function(error) {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: axiosPollResponseSuccessful.data,
                status: axiosPollResponseSuccessful.status
            });
        });
    });

    it('API HTTP POST with async poll parse response and transform response', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            asyncPoll: {
                interval: 50,
                timeout: 100,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll`",
                method: "get",
                doneCondition: "response.data",
                parseResponse: "typeof response.data === 'string' ? JSON.parse(response.data) : response.data;",
                transform: { key_a: "JSON.parse(response.data.key_a)", key_b: "JSON.parse(response.data.key_b)" }
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponseSuccessful = {
            data: JSON.stringify({
                key_a: "{ \"a\": 1, \"b\": true }",
                key_b: "{ \"c\": \"value\", \"d\": null }",
            }),
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", {}, axiosPollResponseSuccessful, 0);
        let response = await mergeCollect.collectApi(api, {});

        chai.expect(response).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(response).to.be.like({
            collectionDuration: /.*/,
            data: {
                key_a: {
                    a: 1,
                    b: true
                },
                key_b: {
                    c: "value",
                    d: null
                }
            },
            status: axiosPollResponseSuccessful.status
        });
    });

    it('API HTTP POST with async poll result API', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            asyncPoll: {
                interval: 50,
                timeout: 100,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll`",
                method: "get",
                doneCondition: "response.data.status == 'completed'",
                resultAPI: {
                    name: "TestApiPollResult",
                    title: "TestApiPollResult",
                    method: "get",
                    uri: "`api/uri/poll_result`"
                }
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponseSuccessful = {
            data: {
                status: "completed"
            },
            status: 200
        };
        let axiosPollResultResponse = {
            data: {
                result: [1, 2, 3, 4]
            },
            status: 200
        }

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", {}, axiosPollResponseSuccessful, 0, "https://test.host/api/uri/poll_result", axiosPollResultResponse);
        let response = await mergeCollect.collectApi(api, {});

        chai.expect(response).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(response).to.be.like({
            collectionDuration: /.*/,
            data: axiosPollResultResponse.data,
            status: axiosPollResultResponse.status
        });
    });

    it('API HTTP POST with async poll result API parse response success', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            asyncPoll: {
                interval: 50,
                timeout: 100,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll`",
                method: "get",
                doneCondition: "response.data.status == 'completed'",
                resultAPI: {
                    name: "TestApiPollResult",
                    title: "TestApiPollResult",
                    method: "get",
                    parseResponse: "JSON.parse(response.data)",
                    uri: "`api/uri/poll_result`"
                }
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponseSuccessful = {
            data: {
                status: "completed"
            },
            status: 200
        };
        let axiosPollResultResponse = {
            data: "{ \"result\": [1, 2, 3, 4] }",
            status: 200
        }

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", {}, axiosPollResponseSuccessful, 0, "https://test.host/api/uri/poll_result", axiosPollResultResponse);
        let response = await mergeCollect.collectApi(api, {});

        chai.expect(response).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(response).to.be.like({
            collectionDuration: /.*/,
            data: {
                result: [1, 2, 3, 4]
            },
            status: axiosPollResultResponse.status
        });
    });

    it('API HTTP POST with async poll result API parse response failure', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            asyncPoll: {
                interval: 50,
                timeout: 100,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll`",
                method: "get",
                doneCondition: "response.data.status == 'completed'",
                resultAPI: {
                    name: "TestApiPollResult",
                    title: "TestApiPollResult",
                    method: "get",
                    parseResponse: "JSON.parse(response.data)",
                    uri: "`api/uri/poll_result`"
                }
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponseSuccessful = {
            data: {
                status: "completed"
            },
            status: 200
        };
        let axiosPollResultResponse = {
            data: {
                result: [1, 2, 3, 4]
            },
            status: 200
        }

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", {}, axiosPollResponseSuccessful, 0, "https://test.host/api/uri/poll_result", axiosPollResultResponse);

        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(SyntaxError).then(function(error) {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: axiosPollResultResponse.data,
                status: axiosPollResultResponse.status
            });
        });
    });

    it('API HTTP POST with async poll result API transform response', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            asyncPoll: {
                interval: 50,
                timeout: 100,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll`",
                method: "get",
                doneCondition: "response.data",
                resultAPI: {
                    name: "TestApiPollResult",
                    title: "TestApiPollResult",
                    method: "get",
                    transform: { key_a: "JSON.parse(response.data.key_a)", key_b: "JSON.parse(response.data.key_b)" },
                    uri: "`api/uri/poll_result`"
                }
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponseSuccessful = {
            data: {
                status: "completed"
            },
            status: 200
        };
        let axiosPollResultResponse = {
            data: {
                key_a: "{ \"a\": 1, \"b\": true }",
                key_b: "{ \"c\": \"value\", \"d\": null }",
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", {}, axiosPollResponseSuccessful, 0, "https://test.host/api/uri/poll_result", axiosPollResultResponse);
        let response = await mergeCollect.collectApi(api, {});

        chai.expect(response).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(response).to.be.like({
            collectionDuration: /.*/,
            data: {
                key_a: {
                    a: 1,
                    b: true
                },
                key_b: {
                    c: "value",
                    d: null
                }
            },
            status: axiosPollResultResponse.status
        });
    });

    it('API HTTP POST with async poll result API transform response failure', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            asyncPoll: {
                interval: 50,
                timeout: 100,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll`",
                method: "get",
                doneCondition: "response.data",
                resultAPI: {
                    name: "TestApiPollResult",
                    title: "TestApiPollResult",
                    method: "get",
                    transform: { key_a: "JSON.parse(response.data.key_a)", key_b: "JSON.parse(response.data.key_b)" },
                    uri: "`api/uri/poll_result`"
                }
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponseSuccessful = {
            data: {
                status: "completed"
            },
            status: 200
        };
        let axiosPollResultResponse = {
            data: {
                key_a: "error",
                key_b: "not parsable",
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", {}, axiosPollResponseSuccessful, 0, "https://test.host/api/uri/poll_result", axiosPollResultResponse);

        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(SyntaxError).then(function(error) {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: axiosPollResultResponse.data,
                status: axiosPollResultResponse.status
            });
        });
    });

    it('API HTTP POST with async poll result API parse response and transform response', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            asyncPoll: {
                interval: 50,
                timeout: 100,
                name: "TestApiPoll",
                title: "Test Api Poll",
                uri: "`api/uri/poll`",
                method: "get",
                doneCondition: "response.data",
                resultAPI: {
                    name: "TestApiPollResult",
                    title: "TestApiPollResult",
                    method: "get",
                    parseResponse: "typeof response.data === 'string' ? JSON.parse(response.data) : response.data;",
                    transform: { key_a: "JSON.parse(response.data.key_a)", key_b: "JSON.parse(response.data.key_b)" },
                    uri: "`api/uri/poll_result`"
                }
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                success: true
            },
            status: 200
        };
        let axiosPollResponseSuccessful = {
            data: {
                status: "completed"
            },
            status: 200
        };
        let axiosPollResultResponse = {
            data: JSON.stringify({
                key_a: "{ \"a\": 1, \"b\": true }",
                key_b: "{ \"c\": \"value\", \"d\": null }",
            }),
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse, true, "https://test.host/api/uri/poll", {}, axiosPollResponseSuccessful, 0, "https://test.host/api/uri/poll_result", axiosPollResultResponse);
        let response = await mergeCollect.collectApi(api, {});

        chai.expect(response).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(response).to.be.like({
            collectionDuration: /.*/,
            data: {
                key_a: {
                    a: 1,
                    b: true
                },
                key_b: {
                    c: "value",
                    d: null
                }
            },
            status: axiosPollResultResponse.status
        });
    });

    it('API HTTP POST with async callback null', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            pollCondition: "response && response.data && response.data.id ? response.data.id : null;",
            asyncCallback: null,
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                id: "1001"
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);

        let result = await mergeCollect.collectApi(api, {});

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: axiosResponse.data,
            status: axiosResponse.status
        });
    });

    it('API HTTP POST with async callback and poll condition error', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            pollCondition: "throw new Error(\"Example Error\");",
            asyncCallback: {
                timeout: 100,
                enabledEnvs: [
                    "localDev"
                ],
                idField: "response.data.id",
                parseResponse: "",
                doneCondition: "response.data && response.data.status === \"done\";",
                errorCondition: ""
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                id: "1001"
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);

        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(Error, "Example Error").then((error) => {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: axiosResponse.data,
                status: axiosResponse.status
            });
        });
    });

    it('API HTTP POST with async callback and no callback made', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            pollCondition: "response && response.data && response.data.id ? response.data.id : null;",
            asyncCallback: {
                timeout: 100,
                enabledEnvs: [
                    "localDev"
                ],
                idField: "",
                parseResponse: "",
                doneCondition: "response.data && response.data.status === \"done\";",
                errorCondition: ""
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                id: "1001"
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);

        await chai.expect(mergeCollect.collectApi(api, {})).to.be.rejectedWith(Error, "Timeout of 100ms exceeded without meeting done condition for callback response").then((error) => {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: axiosResponse.data,
                status: axiosResponse.status
            });
        });
    });

    it('API HTTP POST with async callback and callback made with empty async callback ID field', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            pollCondition: "response && response.data && response.data.id ? response.data.id : null;",
            asyncCallback: {
                timeout: 100,
                enabledEnvs: [
                    "localDev"
                ],
                idField: "",
                parseResponse: "",
                doneCondition: "response.data && response.data.status === \"done\";",
                errorCondition: ""
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                id: "1001"
            },
            status: 200
        };

        let callbackResponse = {
            id: "1001",
            status: "done",
            result: 11111
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);

        let newListenerPromise = new Promise((resolve, reject) => {
            callbackEmitter.on('newListener', (listenerName) => {
                if (listenerName === 'response') {
                    resolve();
                }
            });
        });
        let collectApiPromise = mergeCollect.collectApi(api, {});
        // Waits for the event listener for callbackEmitter to be added before
        // sending a callback response
        await newListenerPromise;
        await request.post('/api/callback').send(callbackResponse);

        await chai.expect(collectApiPromise).to.be.rejectedWith(Error, "Timeout of 100ms exceeded without meeting done condition for callback response").then((error) => {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: axiosResponse.data,
                status: axiosResponse.status
            });
        });
    });

    it('API HTTP POST with async callback and callback made with matching callback ID field', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            pollCondition: "response && response.data && response.data.id ? response.data.id : null;",
            asyncCallback: {
                timeout: 100,
                enabledEnvs: [
                    "localDev"
                ],
                idField: "response && response.data && response.data.id ? response.data.id : null; ",
                parseResponse: "",
                doneCondition: "response.data && response.data.status === \"done\";",
                errorCondition: ""
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                id: "1001"
            },
            status: 200
        };

        let callbackResponse = {
            id: "1001",
            status: "done",
            result: 11111
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);

        let newListenerPromise = new Promise((resolve, reject) => {
            callbackEmitter.on('newListener', (listenerName) => {
                if (listenerName === 'response') {
                    resolve();
                }
            });
        });
        let collectApiPromise = mergeCollect.collectApi(api, {});
        // Waits for the event listener for callbackEmitter to be added before
        // sending a callback response
        await newListenerPromise;
        await request.post('/api/callback').send(callbackResponse);

        let result = await collectApiPromise;

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: callbackResponse,
            status: axiosResponse.status
        });
    });

    it('API HTTP POST with async callback and callback made with non-matching callback ID field', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            pollCondition: "response && response.data && response.data.id ? response.data.id : null;",
            asyncCallback: {
                timeout: 100,
                enabledEnvs: [
                    "localDev"
                ],
                idField: "response && response.data && response.data.id ? response.data.id : null; ",
                parseResponse: "",
                doneCondition: "response.data && response.data.status === \"done\";",
                errorCondition: ""
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                id: "1001"
            },
            status: 200
        };

        let callbackResponse = {
            id: "1002",
            status: "done",
            result: 11111
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);

        let newListenerPromise = new Promise((resolve, reject) => {
            callbackEmitter.on('newListener', (listenerName) => {
                if (listenerName === 'response') {
                    resolve();
                }
            });
        });
        let collectApiPromise = mergeCollect.collectApi(api, {});
        // Waits for the event listener for callbackEmitter to be added before
        // sending a callback response
        await newListenerPromise;
        await request.post('/api/callback').send(callbackResponse);

        await chai.expect(collectApiPromise).to.be.rejectedWith(Error, "Timeout of 100ms exceeded without meeting done condition for callback response").then((error) => {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: axiosResponse.data,
                status: axiosResponse.status
            });
        });
    });

    it('API HTTP POST with async callback and callback made with matching callback ID field, done condition false', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            pollCondition: "response && response.data && response.data.id ? response.data.id : null;",
            asyncCallback: {
                timeout: 100,
                enabledEnvs: [
                    "localDev"
                ],
                idField: "response && response.data && response.data.id ? response.data.id : null; ",
                parseResponse: "",
                doneCondition: "response.data && response.data.status === \"done\";",
                errorCondition: ""
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                id: "1001"
            },
            status: 200
        };

        let callbackResponse = {
            id: "1001",
            status: "pending",
            result: 11111
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);

        let newListenerPromise = new Promise((resolve, reject) => {
            callbackEmitter.on('newListener', (listenerName) => {
                if (listenerName === 'response') {
                    resolve();
                }
            });
        });
        let collectApiPromise = mergeCollect.collectApi(api, {});
        // Waits for the event listener for callbackEmitter to be added before
        // sending a callback response
        await newListenerPromise;
        await request.post('/api/callback').send(callbackResponse);

        await chai.expect(collectApiPromise).to.be.rejectedWith(Error, "Timeout of 100ms exceeded without meeting done condition for callback response").then((error) => {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: callbackResponse,
                status: axiosResponse.status
            });
        });
    });

    it('API HTTP POST with async callback and callback made with matching callback ID field, done condition true after 2 callbacks', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            pollCondition: "response && response.data && response.data.id ? response.data.id : null;",
            asyncCallback: {
                timeout: 100,
                enabledEnvs: [
                    "localDev"
                ],
                idField: "response && response.data && response.data.id ? response.data.id : null; ",
                parseResponse: "",
                doneCondition: "response.data && response.data.status === \"done\";",
                errorCondition: ""
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                id: "1001"
            },
            status: 200
        };

        let pendingResponse = {
            id: "1001",
            status: "pending"
        };

        let callbackResponse = {
            id: "1001",
            status: "done",
            result: 12345
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);

        let newListenerPromise = new Promise((resolve, reject) => {
            callbackEmitter.on('newListener', (listenerName) => {
                if (listenerName === 'response') {
                    resolve();
                }
            });
        });
        let collectApiPromise = mergeCollect.collectApi(api, {});
        // Waits for the event listener for callbackEmitter to be added before
        // sending a callback response
        await newListenerPromise;
        await request.post('/api/callback').send(pendingResponse);
        await request.post('/api/callback').send(callbackResponse);

        let result = await collectApiPromise;

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: callbackResponse,
            status: axiosResponse.status
        });
    });

    it('API HTTP POST with async callback and callback made with matching callback ID field, done condition true after 5 callbacks', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            pollCondition: "response && response.data && response.data.id ? response.data.id : null;",
            asyncCallback: {
                timeout: 100,
                enabledEnvs: [
                    "localDev"
                ],
                idField: "response && response.data && response.data.id ? response.data.id : null; ",
                parseResponse: "",
                doneCondition: "response.data && response.data.status === \"done\";",
                errorCondition: ""
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                id: "1001"
            },
            status: 200
        };

        let pendingResponse = {
            id: "1001",
            status: "pending"
        };

        let callbackResponse = {
            id: "1001",
            status: "done",
            result: 12345
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);

        let newListenerPromise = new Promise((resolve, reject) => {
            callbackEmitter.on('newListener', (listenerName) => {
                if (listenerName === 'response') {
                    resolve();
                }
            });
        });
        let collectApiPromise = mergeCollect.collectApi(api, {});
        // Waits for the event listener for callbackEmitter to be added before
        // sending a callback response
        await newListenerPromise;
        await request.post('/api/callback').send(pendingResponse);
        await request.post('/api/callback').send(pendingResponse);
        await request.post('/api/callback').send(pendingResponse);
        await request.post('/api/callback').send(pendingResponse);
        await request.post('/api/callback').send(callbackResponse);

        let result = await collectApiPromise;

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: callbackResponse,
            status: axiosResponse.status
        });
    });

    it('API HTTP POST with async callback and callback made with matching callback ID field, done condition error', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            pollCondition: "response && response.data && response.data.id ? response.data.id : null;",
            asyncCallback: {
                timeout: 100,
                enabledEnvs: [
                    "localDev"
                ],
                idField: "response && response.data && response.data.id ? response.data.id : null; ",
                parseResponse: "",
                doneCondition: "throw new Error(\"Bad done condition\");",
                errorCondition: ""
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                id: "1001"
            },
            status: 200
        };

        let callbackResponse = {
            id: "1001",
            status: "done",
            result: 11111
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);

        let newListenerPromise = new Promise((resolve, reject) => {
            callbackEmitter.on('newListener', (listenerName) => {
                if (listenerName === 'response') {
                    resolve();
                }
            });
        });
        let collectApiPromise = mergeCollect.collectApi(api, {});
        // Waits for the event listener for callbackEmitter to be added before
        // sending a callback response
        await newListenerPromise;

        let expectErrorPromise = chai.expect(collectApiPromise).to.be.rejectedWith(Error, "Bad done condition").then((error) => {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: callbackResponse,
                status: axiosResponse.status
            });
        });
        await request.post('/api/callback').send(callbackResponse);
        await expectErrorPromise;
    });

    it('API HTTP POST with async callback and callback made with matching callback ID field, multiple callbacks with done condition', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            pollCondition: "response && response.data && response.data.id ? response.data.id : null;",
            asyncCallback: {
                timeout: 100,
                enabledEnvs: [
                    "localDev"
                ],
                idField: "response && response.data && response.data.id ? response.data.id : null; ",
                parseResponse: "",
                doneCondition: "response.data && response.data.status === \"done\";",
                errorCondition: ""
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                id: "1001"
            },
            status: 200
        };

        let callbackResponse1 = {
            id: "1001",
            status: "done",
            result: 54321
        };

        let callbackResponse2 = {
            id: "1001",
            status: "done",
            result: 12345
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);

        let newListenerPromise = new Promise((resolve, reject) => {
            callbackEmitter.on('newListener', (listenerName) => {
                if (listenerName === 'response') {
                    resolve();
                }
            });
        });
        let collectApiPromise = mergeCollect.collectApi(api, {});
        // Waits for the event listener for callbackEmitter to be added before
        // sending a callback response
        await newListenerPromise;
        await request.post('/api/callback').send(callbackResponse1);
        await request.post('/api/callback').send(callbackResponse2);

        let result = await collectApiPromise;

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: callbackResponse1,
            status: axiosResponse.status
        });
    });

    it('API HTTP POST with async callback and callback made with matching callback ID field, parse response', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            pollCondition: "response && response.data && response.data.id ? response.data.id : null;",
            asyncCallback: {
                timeout: 100,
                enabledEnvs: [
                    "localDev"
                ],
                idField: "response && response.data && response.data.id ? response.data.id : null; ",
                parseResponse: "let newResponseData = Object.assign({}, response.data);\ndelete newResponseData.extraFields;\nnewResponseData;",
                doneCondition: "response.data && response.data.status === \"done\";",
                errorCondition: ""
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                id: "1001"
            },
            status: 200
        };

        let callbackResponse = {
            id: "1001",
            status: "done",
            result: 12345,
            extraFields: [{
                metric: 1
            },
            {
                metric: 2
            },
            {
                metric: 3
            }]
        };

        let callbackResponseExpected = {
            id: "1001",
            status: "done",
            result: 12345
        };


        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);

        let newListenerPromise = new Promise((resolve, reject) => {
            callbackEmitter.on('newListener', (listenerName) => {
                if (listenerName === 'response') {
                    resolve();
                }
            });
        });
        let collectApiPromise = mergeCollect.collectApi(api, {});
        // Waits for the event listener for callbackEmitter to be added before
        // sending a callback response
        await newListenerPromise;
        await request.post('/api/callback').send(callbackResponse);

        let result = await collectApiPromise;

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: callbackResponseExpected,
            status: axiosResponse.status
        });
        chai.expect(result.data).to.eql(callbackResponseExpected);
    });

    it('API HTTP POST with async callback and callback made with matching callback ID field, parse response field error', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            pollCondition: "response && response.data && response.data.id ? response.data.id : null;",
            asyncCallback: {
                timeout: 100,
                enabledEnvs: [
                    "localDev"
                ],
                idField: "response.data.id;",
                parseResponse: "throw new SyntaxError(\"Example syntax error\");",
                doneCondition: "response.data && response.data.status === \"done\";",
                errorCondition: ""
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                id: "1001"
            },
            status: 200
        };

        let callbackResponse = {
            id: "1001",
            status: "done",
            result: 11111
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);

        let newListenerPromise = new Promise((resolve, reject) => {
            callbackEmitter.on('newListener', (listenerName) => {
                if (listenerName === 'response') {
                    resolve();
                }
            });
        });
        let collectApiPromise = mergeCollect.collectApi(api, {});
        // Waits for the event listener for callbackEmitter to be added before
        // sending a callback response
        await newListenerPromise;

        let expectErrorPromise = chai.expect(collectApiPromise).to.be.rejectedWith(SyntaxError, "Example syntax error").then((error) => {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: callbackResponse,
                status: axiosResponse.status
            });
        });
        await request.post('/api/callback').send(callbackResponse);
        await expectErrorPromise;
    });

    it('API HTTP POST with async callback and callback made with matching callback ID field, with valid ID field', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            pollCondition: "response && response.data && response.data.id ? response.data.id : null;",
            asyncCallback: {
                timeout: 100,
                enabledEnvs: [
                    "localDev"
                ],
                idField: "response.data.anotherfield.id",
                parseResponse: "",
                doneCondition: "response.data && response.data.anotherfield && response.data.anotherfield.status === \"done\";",
                errorCondition: ""
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                id: "1001"
            },
            status: 200
        };

        let callbackResponse = {
            anotherfield: {
                id: "1001",
                status: "done",
                result: 11111
            }
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);

        let newListenerPromise = new Promise((resolve, reject) => {
            callbackEmitter.on('newListener', (listenerName) => {
                if (listenerName === 'response') {
                    resolve();
                }
            });
        });
        let collectApiPromise = mergeCollect.collectApi(api, {});
        // Waits for the event listener for callbackEmitter to be added before
        // sending a callback response
        await newListenerPromise;
        await request.post('/api/callback').send(callbackResponse);

        let result = await collectApiPromise;

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: callbackResponse,
            status: axiosResponse.status
        });
    });

    it('API HTTP POST with async callback and callback made with matching callback ID field, with ID field error', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            pollCondition: "response && response.data && response.data.id ? response.data.id : null;",
            asyncCallback: {
                timeout: 100,
                enabledEnvs: [
                    "localDev"
                ],
                idField: "response.data.anotherfield.id",
                parseResponse: "",
                doneCondition: "response.data && response.data.status === \"done\";",
                errorCondition: ""
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                id: "1001"
            },
            status: 200
        };

        let callbackResponse = {
            id: "1001",
            status: "done",
            result: 11111
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);

        let newListenerPromise = new Promise((resolve, reject) => {
            callbackEmitter.on('newListener', (listenerName) => {
                if (listenerName === 'response') {
                    resolve();
                }
            });
        });
        let collectApiPromise = mergeCollect.collectApi(api, {});
        // Waits for the event listener for callbackEmitter to be added before
        // sending a callback response
        await newListenerPromise;

        let expectErrorPromise = chai.expect(collectApiPromise).to.be.rejectedWith(TypeError, "Cannot read properties of undefined (reading 'id')").then((error) => {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: axiosResponse.data,
                status: axiosResponse.status
            });
        });
        await request.post('/api/callback').send(callbackResponse);
        await expectErrorPromise;
    });

    it('API HTTP POST with async callback and callback made with matching callback ID field, with ID field error and valid callback before', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            pollCondition: "response && response.data && response.data.id ? response.data.id : null;",
            asyncCallback: {
                timeout: 100,
                enabledEnvs: [
                    "localDev"
                ],
                idField: "response.data.anotherfield.id",
                parseResponse: "",
                doneCondition: "response.data && response.data.anotherfield.status === \"done\";",
                errorCondition: ""
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                id: "1001"
            },
            status: 200
        };

        let callbackResponseInvalid = {
            id: "1001",
            status: "done",
            result: 11111
        };

        let callbackResponseValid = {
            anotherfield: {
                id: "1001",
                status: "done",
                result: 11111
            }
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);

        let newListenerPromise = new Promise((resolve, reject) => {
            callbackEmitter.on('newListener', (listenerName) => {
                if (listenerName === 'response') {
                    resolve();
                }
            });
        });
        let collectApiPromise = mergeCollect.collectApi(api, {});
        // Waits for the event listener for callbackEmitter to be added before
        // sending a callback response
        await newListenerPromise;

        await request.post('/api/callback').send(callbackResponseValid);
        await request.post('/api/callback').send(callbackResponseInvalid);

        let result = await collectApiPromise;

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: callbackResponseValid,
            status: axiosResponse.status
        });
    });

    it('API HTTP POST with async callback and callback made with matching callback ID field, with ID field error and valid callback after', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            pollCondition: "response && response.data && response.data.id ? response.data.id : null;",
            asyncCallback: {
                timeout: 100,
                enabledEnvs: [
                    "localDev"
                ],
                idField: "response.data.anotherfield.id",
                parseResponse: "",
                doneCondition: "response.data && response.data.status === \"done\";",
                errorCondition: ""
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                id: "1001"
            },
            status: 200
        };

        let callbackResponseInvalid = {
            id: "1001",
            status: "done",
            result: 11111
        };

        let callbackResponseValid = {
            anotherField: {
                id: "1001",
                status: "done",
                result: 11111
            }
        };
        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);

        let newListenerPromise = new Promise((resolve, reject) => {
            callbackEmitter.on('newListener', (listenerName) => {
                if (listenerName === 'response') {
                    resolve();
                }
            });
        });
        let collectApiPromise = mergeCollect.collectApi(api, {});
        // Waits for the event listener for callbackEmitter to be added before
        // sending a callback response
        await newListenerPromise;

        let expectErrorPromise = chai.expect(collectApiPromise).to.be.rejectedWith(TypeError, "Cannot read properties of undefined (reading 'id')").then((error) => {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: axiosResponse.data,
                status: axiosResponse.status
            });
        });
        await request.post('/api/callback').send(callbackResponseInvalid);
        await request.post('/api/callback').send(callbackResponseValid);
        await expectErrorPromise;
    });

    it('API HTTP POST with async callback and callback made with matching callback ID field, error condition field true', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            pollCondition: "response && response.data && response.data.id ? response.data.id : null;",
            asyncCallback: {
                timeout: 100,
                enabledEnvs: [
                    "localDev"
                ],
                idField: "response.data.id;",
                parseResponse: "",
                doneCondition: "response.data && response.data.status === \"done\";",
                errorCondition: "response.data.result > 10000;"
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                id: "1001"
            },
            status: 200
        };

        let callbackResponse = {
            id: "1001",
            status: "done",
            result: 11111
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);

        let newListenerPromise = new Promise((resolve, reject) => {
            callbackEmitter.on('newListener', (listenerName) => {
                if (listenerName === 'response') {
                    resolve();
                }
            });
        });
        let collectApiPromise = mergeCollect.collectApi(api, {});
        // Waits for the event listener for callbackEmitter to be added before
        // sending a callback response
        await newListenerPromise;

        let expectErrorPromise = chai.expect(collectApiPromise).to.be.rejectedWith(Error, "Error condition met for async callback API TestApi").then((error) => {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: callbackResponse,
                status: axiosResponse.status
            });
        });
        await request.post('/api/callback').send(callbackResponse);
        await expectErrorPromise;
    });

    it('API HTTP POST with async callback and callback made with matching callback ID field, error condition field false', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            pollCondition: "response && response.data && response.data.id ? response.data.id : null;",
            asyncCallback: {
                timeout: 100,
                enabledEnvs: [
                    "localDev"
                ],
                idField: "response.data.id;",
                parseResponse: "",
                doneCondition: "response.data && response.data.status === \"done\";",
                errorCondition: "response.data.result < 10000;"
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                id: "1001"
            },
            status: 200
        };

        let callbackResponse = {
            id: "1001",
            status: "done",
            result: 11111
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);

        let newListenerPromise = new Promise((resolve, reject) => {
            callbackEmitter.on('newListener', (listenerName) => {
                if (listenerName === 'response') {
                    resolve();
                }
            });
        });
        let collectApiPromise = mergeCollect.collectApi(api, {});
        // Waits for the event listener for callbackEmitter to be added before
        // sending a callback response
        await newListenerPromise;
        await request.post('/api/callback').send(callbackResponse);

        let result = await collectApiPromise;
        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: callbackResponse,
            status: axiosResponse.status
        });
    });

    it('API HTTP POST with async callback and callback made with matching callback ID field, error condition field error', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            pollCondition: "response && response.data && response.data.id ? response.data.id : null;",
            asyncCallback: {
                timeout: 100,
                enabledEnvs: [
                    "localDev"
                ],
                idField: "response.data.id;",
                parseResponse: "",
                doneCondition: "response.data && response.data.status === \"done\";",
                errorCondition: "throw new TypeError(\"test error\")"
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                id: "1001"
            },
            status: 200
        };

        let callbackResponse = {
            id: "1001",
            status: "done",
            result: 11111
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);

        let newListenerPromise = new Promise((resolve, reject) => {
            callbackEmitter.on('newListener', (listenerName) => {
                if (listenerName === 'response') {
                    resolve();
                }
            });
        });
        let collectApiPromise = mergeCollect.collectApi(api, {});
        // Waits for the event listener for callbackEmitter to be added before
        // sending a callback response
        await newListenerPromise;

        let expectErrorPromise = chai.expect(collectApiPromise).to.be.rejectedWith(TypeError, "test error").then((error) => {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: callbackResponse,
                status: axiosResponse.status
            });
        });
        await request.post('/api/callback').send(callbackResponse);
        await expectErrorPromise;
    });

    it('API HTTP POST with async callback and callback made with non-matching callback ID field, error condition field error', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            pollCondition: "response && response.data && response.data.id ? response.data.id : null;",
            asyncCallback: {
                timeout: 100,
                enabledEnvs: [
                    "localDev"
                ],
                idField: "response.data.id;",
                parseResponse: "",
                doneCondition: "response.data && response.data.status === \"done\";",
                errorCondition: "throw new TypeError(\"test error\")"
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                id: "1001"
            },
            status: 200
        };

        let callbackResponse = {
            id: "1002",
            status: "done",
            result: 11111
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);

        let newListenerPromise = new Promise((resolve, reject) => {
            callbackEmitter.on('newListener', (listenerName) => {
                if (listenerName === 'response') {
                    resolve();
                }
            });
        });
        let collectApiPromise = mergeCollect.collectApi(api, {});
        // Waits for the event listener for callbackEmitter to be added before
        // sending a callback response
        await newListenerPromise;

        let expectErrorPromise = chai.expect(collectApiPromise).to.be.rejectedWith(Error, "Timeout of 100ms exceeded without meeting done condition for callback response").then((error) => {
            let result = error.result;

            chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
            chai.expect(result).to.be.like({
                collectionDuration: /.*/,
                data: axiosResponse.data,
                status: axiosResponse.status
            });
        });
        await request.post('/api/callback').send(callbackResponse);
        await expectErrorPromise;
    });

    it('API HTTP POST with async callback and callback made with non-matching callback ID field and matching callback ID field, error condition field error for non-matching callback ID field', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            pollCondition: "response && response.data && response.data.id ? response.data.id : null;",
            asyncCallback: {
                timeout: 100,
                enabledEnvs: [
                    "localDev"
                ],
                idField: "response.data.id;",
                parseResponse: "",
                doneCondition: "response.data && response.data.status === \"done\";",
                errorCondition: "if (response.data.id == \"1002\") { throw new TypeError(\"test error\"); } else { false; }"
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                id: "1001"
            },
            status: 200
        };

        let callbackResponse1 = {
            id: "1001",
            status: "done",
            result: 11111
        };

        let callbackResponse2 = {
            id: "1002",
            status: "done",
            result: 22222
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);

        let newListenerPromise = new Promise((resolve, reject) => {
            callbackEmitter.on('newListener', (listenerName) => {
                if (listenerName === 'response') {
                    resolve();
                }
            });
        });
        let collectApiPromise = mergeCollect.collectApi(api, {});
        // Waits for the event listener for callbackEmitter to be added before
        // sending a callback response
        await newListenerPromise;
        await request.post('/api/callback').send(callbackResponse2);
        await request.post('/api/callback').send(callbackResponse1);

        let result = await collectApiPromise;

        chai.expect(result).to.have.all.keys(COLLECT_API_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: callbackResponse1,
            status: axiosResponse.status
        });
    });

    it('API HTTP POST with async callback and enableCallbackAPIs false', async() => {
        global.gConfig.enableCallbackAPIs = false;

        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "rest",
            method: "post",
            pollCondition: "response && response.data && response.data.id ? response.data.id : null;",
            asyncCallback: {
                timeout: 100,
                enabledEnvs: [
                    "localDev"
                ],
                idField: "response.data.id;",
                parseResponse: "",
                doneCondition: "response.data && response.data.status === \"done\";",
                errorCondition: "throw new TypeError(\"test error\")"
            },
            baseUrl: "`https://test.host`",
            uri: "`api/uri`",
            queryParams: ""
        });
        await api.save();

        let axiosResponse = {
            data: {
                id: "1001"
            },
            status: 200
        };

        await setupStubsAxios("post", "https://test.host/api/uri", axiosResponse);

        let collectApiPromise = mergeCollect.collectApi(api, {});

        let expectErrorPromise = chai.expect(collectApiPromise).to.be.rejectedWith(Error, "Async callback APIs are not enabled in this environment");
        await expectErrorPromise;
    });

    it('API function with no function name', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "function",
            baseUrl: ""
        });
        await api.save();

        let collectApiPromise = mergeCollect.collectApi(api, {});

        let expectErrorPromise = chai.expect(collectApiPromise).to.be.rejectedWith(NotFoundError);
        await expectErrorPromise;
    });

    it('API function with non-existant function name', async() => {
        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "function",
            baseUrl: "testFunctionName"
        });
        await api.save();

        let collectApiPromise = mergeCollect.collectApi(api, {});

        let expectErrorPromise = chai.expect(collectApiPromise).to.be.rejectedWith(NotFoundError);
        await expectErrorPromise;
    });

    it('API function with valid function name', async() => {
        apiFunctions.testFunctionName = null;
        sinon.stub(apiFunctions, "testFunctionName").value(() => {
            return {
                value: 100
            };
        });

        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "function",
            baseUrl: "testFunctionName"
        });
        await api.save();

        let result = await mergeCollect.collectApi(api, {});

        chai.expect(result).to.have.all.keys(COLLECT_API_FUNCTION_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: {
                value: 100
            }
        });
    });

    it('API function with valid function name and parameters', async() => {
        apiFunctions.testFunctionName = null;
        sinon.stub(apiFunctions, "testFunctionName").value(({ a, b }) => {
            return {
                value: a + b
            };
        });

        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "function",
            parameters: [
                "a",
                "b"
            ],
            baseUrl: "testFunctionName",
            queryParams: "new Object({ a: parameters.a, b: parameters.b });"
        });
        await api.save();

        let result = await mergeCollect.collectApi(api, { a: 10, b: 20 });

        chai.expect(result).to.have.all.keys(COLLECT_API_FUNCTION_RESULT_FIELDS);
        chai.expect(result).to.be.like({
            collectionDuration: /.*/,
            data: {
                value: 30
            }
        });
    });

    it('API function with valid function name and error in function', async() => {
        apiFunctions.testFunctionName = null;
        sinon.stub(apiFunctions, "testFunctionName").value(() => {
            throw new Error('API function error');
        });

        let api = new Api({
            name: "TestApi",
            active: true,
            apiType: "function",
            baseUrl: "testFunctionName"
        });
        await api.save();

        let collectApiPromise = mergeCollect.collectApi(api, {});

        let expectErrorPromise = chai.expect(collectApiPromise).to.be.rejectedWith(ApiFunctionError);
        await expectErrorPromise;
    });
});


describe('createUrl()', () => {
    beforeEach(() => {
        global.gConfig = _.cloneDeep(config.default);
        global.gConfig.APIUriConfig = {
            testUrlValid: 'https://test.host',
            testUrlInvalid: 'https//[];//test.host',
            testUrlWithUri: 'https://test.host/api/test/v1',
        }
    });

    it('No arguments', async() => {
        chai.expect(mergeCollect.createUrl()).to.be.rejectedWith(TypeError);
    });

    it('Valid URL argument only', async() => {
        let createdUrl = await mergeCollect.createUrl("`https://test.host`");
        chai.expect(createdUrl).to.equal("https://test.host/");
    });

    it('Invalid URL argument only', async() => {
        chai.expect(mergeCollect.createUrl("`https//[];//test.host`")).to.be.rejectedWith(TypeError);
    });

    it('Valid URL from config variable', async() => {
        let createdUrl = await mergeCollect.createUrl("config.testUrlValid", "", "", {}, null);
        chai.expect(createdUrl).to.equal("https://test.host/");
    });

    it('Invalid URL from config variable', async() => {
        chai.expect(mergeCollect.createUrl("config.testUrlInvalid", "", "", {}, null)).to.be.rejectedWith(TypeError);
    });

    it('Invalid URL data type', async() => {
        chai.expect(mergeCollect.createUrl("100;", "", "", {}, null)).to.be.rejectedWith(TypeError);
    });

    it('URL with additional URI (leading /)', async() => {
        let createdUrl = await mergeCollect.createUrl("config.testUrlValid", "`/api/v2`", "", {}, null);
        chai.expect(createdUrl).to.equal("https://test.host/api/v2");
    });

    it('URL with additional URI (no leading /)', async() => {
        let createdUrl = await mergeCollect.createUrl("config.testUrlValid", "`api/v2`", "", {}, null);
        chai.expect(createdUrl).to.equal("https://test.host/api/v2");
    });

    it('URL with additional URI (reserved characters)', async() => {
        let createdUrl = await mergeCollect.createUrl("config.testUrlValid", "`a[[#}[}{591?>?$~(!@*$$$J@!~_)`", "", {}, null);
        chai.expect(createdUrl).to.equal("https://test.host/a[[%23%7D[%7D%7B591%3F%3E&$~(!@*$$$J@!~_)");
    });

    it('URL with additional URI (invalid expression)', async() => {
        chai.expect(mergeCollect.createUrl("config.testUrlValid", "`api/v2", "", {}, null)).to.be.rejectedWith(SyntaxError);
    });

    it('URL with additional URI (invalid URI type)', async() => {
        chai.expect(mergeCollect.createUrl("config.testUrlValid", "true", "", {}, null)).to.be.rejectedWith(TypeError);
    });

    it('URL with query parameters (empty object)', async() => {
        let createdUrl = await mergeCollect.createUrl("config.testUrlWithUri", "", "new Object({});", {}, null);
        chai.expect(createdUrl).to.equal("https://test.host/api/test/v1");
    });

    it('URL with query parameters (one parameter)', async() => {
        let createdUrl = await mergeCollect.createUrl("config.testUrlWithUri", "", "new Object({ a: \"parameterValue\" });", {}, null);
        chai.expect(createdUrl).to.equal("https://test.host/api/test/v1?a=parameterValue");
    });

    it('URL with query parameters (multiple parameters)', async() => {
        let createdUrl = await mergeCollect.createUrl("config.testUrlWithUri", "", "new Object({ a: 100, b: \"stringValue\", c: false });", {}, null);
        chai.expect(createdUrl).to.equal("https://test.host/api/test/v1?a=100&b=stringValue&c=false");
    });

    it('URL with query parameters (array parameter)', async() => {
        let createdUrl = await mergeCollect.createUrl("config.testUrlWithUri", "", "new Object({ a: [\"test1\", \"test2\", \"test3\"] });", {}, null);
        chai.expect(createdUrl).to.equal("https://test.host/api/test/v1?a[]=test1&a[]=test2&a[]=test3");
    });

    it('URL with query parameters (escaped character values)', async() => {
        let createdUrl = await mergeCollect.createUrl("config.testUrlWithUri", "", "new Object({ a: \"\\\"[]\" });", {}, null);
        chai.expect(createdUrl).to.equal("https://test.host/api/test/v1?a=%22%5B%5D");
    });

    it('URL with query parameters (string value)', async() => {
        chai.expect(mergeCollect.createUrl("config.testUrlWithUri", "", "`a=100&b=20`;", {}, null)).to.be.rejectedWith(TypeError);
    });

    it('URL with additional URI and query parameters and parameter object test 1', async() => {
        let parameters = {
            id: "ABC9999",
            limit: 200
        };
        let createdUrl = await mergeCollect.createUrl("config.testUrlWithUri", "`objectName/itemName/${parameters.id}`", "new Object({ a: \"variableValue\", l: parameters.limit });", parameters, null);
        chai.expect(createdUrl).to.equal("https://test.host/api/test/v1/objectName/itemName/ABC9999?a=variableValue&l=200");
    });

    it('URL with additional URI and query parameters and parameter object test 2', async() => {
        let parameters = {
            id: 1234,
            type: "ROUTER"
        };

        let createdUrl = await mergeCollect.createUrl("config.testUrlWithUri", "`/${parameters.id}/objectName/itemName`", "new Object({ a: \"variableValue\", b: 100, interfaceType: parameters.type });", parameters, null);
        chai.expect(createdUrl).to.equal("https://test.host/api/test/v1/1234/objectName/itemName?a=variableValue&b=100&interfaceType=ROUTER");
    });

    it('URL with response variable set', async() => {
        let response = {
            data: {
                testId: "TEST001"
            }
        };

        let createdUrl = await mergeCollect.createUrl("config.testUrlWithUri", "`${response.data.testId}`", "", {}, response);
        chai.expect(createdUrl).to.equal("https://test.host/api/test/v1/TEST001");
    });

    it('URL with query parameters, empty queryParams field', async() => {
        let createdUrl = await mergeCollect.createUrl("`https://test.host/api/endpoint?a=1&b=2&c=3`", "", "", {}, null);
        chai.expect(createdUrl).to.equal("https://test.host/api/endpoint?a=1&b=2&c=3");
    });

    it('URL with query parameters, set queryParams field to empty object', async() => {
        let createdUrl = await mergeCollect.createUrl("`https://test.host/api/endpoint?a=1&b=2&c=3`", "", "new Object({});", {}, null);
        chai.expect(createdUrl).to.equal("https://test.host/api/endpoint");
    });

    it('URL with query parameters, test overwrite from queryParams field', async() => {
        let createdUrl = await mergeCollect.createUrl("`https://test.host/api/endpoint?a=1&b=2&c=3`", "", "new Object({ d: 4, e: 5, f: 6 });", {}, null);
        chai.expect(createdUrl).to.equal("https://test.host/api/endpoint?d=4&e=5&f=6");
    });
});


async function setupStubsAxios(httpMethod, httpExpectedUri, httpResponse, poll=false, pollExpectedUri=null, pollResponsePending=null, pollResponseSuccessful=null, afterPolls=0, pollResultExpectedUri=null, pollResultResponse=null) {
    if (httpMethod) {
        let handlers = [];
        let pollCount = 0;

        let handleStubHttpMethod = async function(uri, params, options) {
            if (!options) {
                options = {};
            }

            options.headers = {
                common: {},
                delete: {},
                get: {},
                head: {},
                post: { 'Content-Type': 'application/x-www-form-urlencoded' },
                put: { 'Content-Type': 'application/x-www-form-urlencoded' },
                patch: { 'Content-Type': 'application/x-www-form-urlencoded' }
            };

            let promise = Promise.resolve(options);

            // Attempts to mock interceptor behaviour in axios
            let chain = [];

            handlers.forEach(function unshiftRequestInterceptors(interceptor) {
                chain.unshift(interceptor.fulfilled, interceptor.rejected);
            });

            while (chain.length) {
                promise = promise.then(chain.shift(), chain.shift());
            }

            await promise;

            if (typeof uri !== "string") {
                return Promise.reject(new TypeError());
            }

            if (uri == httpExpectedUri) {
                if (httpResponse.isAxiosError) {
                    let responseError = new Error();
                    responseError.response = httpResponse.response;
                    return Promise.reject(responseError);
                }
                return Promise.resolve(httpResponse);
            }
        }

        let handlePollHttpMethod = async function(uri, params, options) {
            if (!options) {
                options = {};
            }

            options.headers = {
                common: {},
                delete: {},
                get: {},
                head: {},
                post: { 'Content-Type': 'application/x-www-form-urlencoded' },
                put: { 'Content-Type': 'application/x-www-form-urlencoded' },
                patch: { 'Content-Type': 'application/x-www-form-urlencoded' }
            };

            let promise = Promise.resolve(options);

            let chain = [];

            handlers.forEach(function unshiftRequestInterceptors(interceptor) {
                chain.unshift(interceptor.fulfilled, interceptor.rejected);
            });

            while (chain.length) {
                promise = promise.then(chain.shift(), chain.shift());
            }

            await promise;

            if (typeof uri !== "string") {
                return Promise.reject(new TypeError());
            }

            if (uri == pollExpectedUri) {
                pollCount += 1;
                if (pollCount > afterPolls) {
                    if (pollResponseSuccessful.isAxiosError) {
                        let responseError = new Error();
                        responseError.response = pollResponseSuccessful.response;
                        return Promise.reject(responseError);
                    }

                    return Promise.resolve(pollResponseSuccessful);
                } else {
                    if (pollResponsePending.isAxiosError) {
                        let responseError = new Error();
                        responseError.response = pollResponsePending.response;
                        return Promise.reject(responseError);
                    }

                    return Promise.resolve(pollResponsePending);
                }
            } else if (uri == pollResultExpectedUri) {
                if (pollResultResponse.isAxiosError) {
                    let responseError = new Error();
                    responseError.response = pollResultResponse.response;
                    return Promise.reject(responseError);
                }
                return Promise.resolve(pollResultResponse);
            }
        }

        let stubHttpGetMethod = null;
        let stubHttpPostMethod = null;

        switch (httpMethod) {
            case "get":
                stubHttpGetMethod = async function(uri, options) {
                    return await handleStubHttpMethod(uri, null, options);
                }
                break;
            case "post":
                stubHttpPostMethod = async function(uri, params, options) {
                    return await handleStubHttpMethod(uri, params, options);
                }
                break;
        }

        // In the case where a poll method is specified, it is always a GET method and should not interfere with
        // the initial HTTP POST stubbed method, otherwise this functionality will need to be refactored
        if (poll) {
            stubHttpGetMethod = async function(uri, options) {
                return await handlePollHttpMethod(uri, null, options);
            }
        }

        sinon.stub(axios, "create").returns({
            get: stubHttpGetMethod,
            post: stubHttpPostMethod,
            interceptors: {
                request: {
                    use: function(fulfilled, rejected) {
                        handlers.push({
                            fulfilled: fulfilled,
                            rejected: rejected
                        });
                    }
                }
            }
        });
    }
}
