/**
 * This file loads the database config synchronously as it is used by the migration CLI script
 */

const _ = require('lodash');
const mongoose = require('mongoose');
const config = require('../config/config.json');

const defaultConfig = config.default;
const environment = process.env.MERGE_ENV || 'localDev';
const environmentConfig = config[environment];
let gConfig = null;


if (environmentConfig) {
    gConfig = _.merge(defaultConfig, environmentConfig);
}

let mongodbUri = null;
let mongodbUseSsl = null;
let mongodbCaCertFile = null;
let mongodbClientCertFile = null;

if (gConfig) {
    let dbCredentials = gConfig.dbUser ? `${gConfig.dbUser}:${gConfig.dbPass}@` : '';
    let dbReplicaSet = gConfig.dbReplicaSet ? `?replicaSet=${gConfig.dbReplicaSet}` : '';

    mongodbUseSsl = gConfig.dbUseSsl;
    mongodbCaCertFile = gConfig.dbCaCert;
    mongodbClientCertFile = gConfig.dbClientCert;

    mongodbUri = `mongodb://${dbCredentials}${gConfig.dbHost}/${gConfig.dbName}${dbReplicaSet}`;
};


async function connect() {
    const mongodbConfig = {
        caCertFile: mongodbCaCertFile,
        clientCertFile: mongodbClientCertFile,
        uri: mongodbUri,
        useSsl: mongodbUseSsl
    };

    await mongoose.connect(mongodbConfig.uri, {
        ssl: mongodbConfig.useSsl,
        tlsAllowInvalidCertificates: false,
        tlsCAFile: mongodbConfig.caCertFile,
        tlsCertificateKeyFile: mongodbConfig.clientCertFile,
    });
}


async function disconnect() {
    await mongoose.disconnect();
}


module.exports = {
    connect,
    disconnect
};
