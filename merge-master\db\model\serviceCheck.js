import mongoose from 'mongoose';

import ServiceCheckSchema from '../base/serviceCheckSchema.js';
import GeneratedServiceCheckModel from './generatedServiceCheck.js';
import MessageBucket from './messageBucket.js';
import { ServiceCheckStartMethod } from '../../modules/enumerations.js';

const feedbackSchema = new mongoose.Schema({
    isPositive: { type: Boolean, default: false },
    messageExists: { type: Boolean, default: false },
    messageRead: { type: Boolean, default: false },
    message: { type: String, default: null },
    createdOn: { type: Date, required: true, immutable: true, default: Date.now, index: true },
}, { _id: false });

const serviceCheckSchema = new ServiceCheckSchema();

serviceCheckSchema.add({
    generatedServiceChecks: {
        type: [{
            type: mongoose.Schema.Types.ObjectId,
            ref: GeneratedServiceCheckModel
        }],
        default: []
    },
    feedback: {
        type: feedbackSchema,
        default: null
    },
    messageBucketName: {
        type: String,
        default: null
    },
    startMethod: {
        type: String,
        enum: [...Object.values(ServiceCheckStartMethod), null],
        default: null
    },
    locked: {
        type: Boolean,
        default: false,
        select: false
    },
    lockedDatetime: {
        type: Date,
        default: null,
        select: false
    },
    lockedDuration: {
        type: Number,
        default: 0,
        select: false
    }
});


serviceCheckSchema.virtual('messageBucket', {
    ref: MessageBucket,
    localField: 'messageBucketName',
    foreignField: 'name',
    justOne: true
});


serviceCheckSchema.index({ feedback: 1, createdOn: -1 });
serviceCheckSchema.index({ 'feedback.isPositive': 1, createdOn: -1 });
serviceCheckSchema.index({ 'feedback.messageExists': 1, createdOn: -1 });
serviceCheckSchema.index({ 'feedback.messageRead': 1, createdOn: -1 });
serviceCheckSchema.index({ startMethod: 1, createdOn: -1 });

serviceCheckSchema.set('minimize', false);

serviceCheckSchema.methods.toJSON = function() {
    let obj = this.toObject();
    delete obj._id;
    delete obj.__v;

    // Converts nested generated service check instances to plain objects
    if (Array.isArray(this.generatedServiceChecks)) {
        obj.generatedServiceChecks = this.generatedServiceChecks.map((generatedServiceCheck) => {
            if (generatedServiceCheck && typeof generatedServiceCheck.toJSON === 'function') {
                return generatedServiceCheck.toJSON();
            } else {
                return generatedServiceCheck;
            }
        });
    }

    return obj;
}


const BaseServiceCheckModel = mongoose.model('serviceCheck', serviceCheckSchema);


// Extends base service check model to set a default on the virtual field 'sourcesData'
export default class ServiceCheckModel extends BaseServiceCheckModel {
    constructor(...args) {
        super(...args);
        // Sets sources data to default if it is selected
        if (!this.sourcesData && this.isSelected('sourcesData')) {
            this.sourcesData = {};
        }
    }
}
