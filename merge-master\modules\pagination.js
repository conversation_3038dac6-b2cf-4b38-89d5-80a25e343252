import url from 'url';

function createPaginationMetadata(request, limit, offset, total) {
    let host = request.get('x-forwarded-host') ? request.get('x-forwarded-host') : request.get('host');

    let baseUrl = url.format({
        protocol: request.protocol,
        host: host,
        pathname: request.baseUrl + request.path,
        query: request.query
    });

    return calculatePaginationMetadata(baseUrl, limit, offset, total);
}

function calculatePaginationMetadata(baseUrl, limit, offset, total) {
    let metadata = {
        limit: limit,
        offset: offset,
        total: total,
        prev: null,
        next: null
    };

    let prevUrl = new url.URL(baseUrl);
    let nextUrl = new url.URL(baseUrl);

    let prevOffset = offset;
    let prevLimit = limit;
    let nextOffset = offset;

    if (offset > 0) {
        prevOffset -= limit;

        // If the last page starts before offset 0, move it back to
        // the start of data and set total current offset
        if (prevOffset < 0) {
            prevOffset = 0;
            prevLimit = offset;
        }

        prevUrl.searchParams.set('offset', prevOffset);
        prevUrl.searchParams.set('limit', prevLimit);
        metadata.prev = prevUrl;
    }

    if (offset + limit < total) {
        nextOffset += limit;

        nextUrl.searchParams.set('limit', limit);
        nextUrl.searchParams.set('offset', nextOffset);

        metadata.next = nextUrl;
    }

    return metadata;
}

export default {
    createPaginationMetadata: createPaginationMetadata
};
