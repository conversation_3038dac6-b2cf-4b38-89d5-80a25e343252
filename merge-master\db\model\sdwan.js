
import mongoose from 'mongoose';

// defining a model
const sdwan = new mongoose.Schema({
    deviceName: { type: String, required: true },
    deviceStatus: { type: String },
    deviceUptimeDate: { type: Number },
    deviceIP: { type: String },
    latitude: { type: Number },
    longitude: { type: Number },
    deviceModel: { type: String },
    siteID: { type: String },
    deviceTimestamp: { type: Number },
    serviceType: { type: String },
    interfaceData: [{
        _id: false,
        bwUp: { type: Number },
        bwDown: { type: Number },
        rxKbps: { type: Number },
        txKbps: { type: Number },
        rxDrops: { type: Number },
        txDrops: { type: Number },
        rxOctets: { type: Number },
        txOctets: { type: Number },
        rxPackets: { type: Number },
        txPackets: { type: Number },
        interfaceIP: { type: String },
        interfaceName: { type: String },
        interfaceType: { type: String },
        interfaceStatus: { type: String },
        interfaceTimestamp: { type: Number }
    }],
    sdwanFnn: { type: String, required: true },
    cidn: { type: String },
    customerName: { type: String },
    customerAddress: { type: String },
    networkTopology: [{
        _id: false,
        neName: { type: String },
        neType: { type: String }
    }],
    carriageFnn: [
        {},
        { strict: false }
    ],
    imsi: { type: String },
    flexiPlanName: { type: String },
    flexiPlanLimit: { type: String },
    itamIncident: [{
        _id: false,
        incidentNumber: { type: String },
        impactedElement: { type: String },
        impactedElementType: { type: String }
    }],
    planUsage: { type: String }
},
{
    timestamps: true,
    versionKey: false,
    strict: false,
    collection: 'sdwan',
    unique: true
});

sdwan.index({ createdAt: 1 }, { expireAfterSeconds: 60 * 60 * 24 * 7 });
sdwan.index({ sdwanFnn: 1, deviceTimestamp: -1 });

export default mongoose.model('sdwan', sdwan);


