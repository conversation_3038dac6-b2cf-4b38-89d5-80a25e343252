// mergeCollect.js
// V4
// =========
// All functions for parsing command and script.
import https from 'https';
import axiosM from 'axios';
// import axiosCookieJarSupport from 'axios-cookiejar-support';
import { createCookieAgent } from 'http-cookie-agent/http';
import toughCookie from 'tough-cookie';
import debug from 'debug';
import moment from 'moment';
import tunnel from 'tunnel';
import fs from 'fs';
import ivm from 'isolated-vm';
import _ from 'lodash';
import queryString from 'query-string';
import { v4 as uuidv4 } from 'uuid';
import url from 'url';
import urlJoin from 'url-join';

import Api from '../db/model/api.js';
import ApiAuthKeys from '../db/model/apiAuthKeys.js';
import logger from './logger.js';
import callbackEmitter from './callbackEmitter.js';
import { ApiUriError, ApiFunctionError, InactiveError, NotFoundError } from './error.js';
import { geoliteCallCountCheck } from './geoliteCallCount.js';
import apiFunctions from './apiFunctions.js';


const debugMsg = debug('merge:collect');

/**
 *
 * @param {Api} api API to collect
 * @param {Object} parameters Object containing parameters to be rendered in API fields
 * @param {Object} socket Optional, object with the key 'socket' containing socket
 * @returns Object containing the result of the API collection, including collectionDuration, data and status
 */
async function collectApi(api, parameters, signal=null, socketObj=null) {
    if (!(api instanceof Api)) {
        throw new TypeError('Input api must be an instance of the API model');
    }

    if (!api.active) {
        throw new InactiveError(`API '${api.name}' is not active`);
    }

    let result = {};
    let errorConditionMet;

    // Sets TLS min version in config if it is specified in API config
    let axiosConfig = {
        proxy: false
    };
    let httpsAgentConfig = {};

    if (api.masslCertificateName) {
        let masslConfig = _.get(global.gConfig, ["APIMasslCertificates", api.masslCertificateName], null);

        if (masslConfig) {
            httpsAgentConfig.cert = await fs.promises.readFile(masslConfig.cert);
            httpsAgentConfig.key = await fs.promises.readFile(masslConfig.key);
        } else {
            throw new NotFoundError(`MASSL Certificate ${api.masslCertificateName} does not exist in the configuration`);
        }
    }

    if (api.tlsMinVersion) {
        httpsAgentConfig.minVersion = api.tlsMinVersion;
    }

    if (api.proxyRequired) {
        httpsAgentConfig.proxy = {
            host: global.gConfig.proxyHost,
            port: global.gConfig.proxyPort,
            proxyAuth: `${global.gConfig.robotAccount01Username}:${global.gConfig.robotAccount01Password}`
        };
    }

    let cookieJar;

    if (api.useCookies) {
        cookieJar = new toughCookie.CookieJar();
        axiosConfig.jar = cookieJar;
        axiosConfig.withCredentials = true;
    }

    let httpsAgent;

    // Developer note: Currently unsure whether all keys such as minVersion are
    // supported in both TunnelingAgent (proxy) and Agent (no proxy) objects
    if (api.proxyRequired) {
        if (api.useCookies) {
            httpsAgent = new (createCookieAgent(tunnel.httpsOverHttp))({ jar: cookieJar, ...httpsAgentConfig });
        } else {
            httpsAgent = tunnel.httpsOverHttp(httpsAgentConfig);
        }
    } else {
        // Allows allows unauthorized certs for internal endpoints
        httpsAgentConfig.rejectUnauthorized = false;

        if (api.useCookies) {
            httpsAgent = new (createCookieAgent(https.Agent))({ jar: cookieJar, ...httpsAgentConfig });
        } else {
            httpsAgent = new https.Agent(httpsAgentConfig);
        }
    }

    axiosConfig.signal = signal;
    axiosConfig.httpsAgent = httpsAgent;

    const axios = axiosM.create(axiosConfig);

    // Uses the same context over the entire collectApi call, which may run into memory issues
    // Needs to be refactored if a lot of crashes / segfaults occur
    const isolate = new ivm.Isolate({
        memoryLimit: 128
    });
    let apiContext = await isolate.createContext();

    try {
        let apiBodyConfigExternalCopy = new ivm.ExternalCopy(global.gConfig.APIBodyConfig);
        let apiHeaderConfigExternalCopy = new ivm.ExternalCopy(global.gConfig.APIHeaderConfig);

        await apiContext.global.set('parameters', new ivm.ExternalCopy(parameters).copyInto());
        await apiContext.global.set('uuidv4', function(...args) {
            return uuidv4(...args);
        });
        await apiContext.global.set('base64', function(inputString) {
            return Buffer.from(inputString).toString('base64');
        });

        let startTime;

        switch (api.apiType) {
        case "rest":
            let restBody = null;
            let restHeader = null;
            let callURL;

            try {
                callURL = await createUrl(api.baseUrl, api.uri, api.queryParams, parameters, null);

                debugMsg(`#> Calculated callURL for API ${api.name} = ${callURL}`);

                if (api.method == 'post' && api.body) {
                    await apiContext.global.set('config', apiBodyConfigExternalCopy.copyInto());
                    restBody = JSON.parse(await apiContext.eval(api.body, { copy: true, timeout: 2000 }));
                }

                if (api.header) {
                    await apiContext.global.set('config', apiHeaderConfigExternalCopy.copyInto());

                    restHeader = JSON.parse(await apiContext.eval(api.header, { copy: true, timeout: 2000 }));
                    debugMsg(`#> Calculated header for API ${api.name} = ${restHeader}`);
                }
            } catch(error) {
                let apiUriError = new ApiUriError('Error when evaluating API uri / body / header', { cause: error });
                throw apiUriError;
            }

            if (Array.isArray(api.authKeyDb) && api.authKeyDb.length) {
                // intercept axios requests to include auth key header from database

                axios.interceptors.request.use(async(config) => {
                    try {
                        for (let authKey of api.authKeyDb) {
                            let apiKey = await ApiAuthKeys.findOne({ name: authKey.authKeyName });
                            if (!apiKey) {
                                throw new Error(`API auth key ${authKey.authKeyName} is not set`);
                            }

                            config.headers[authKey.authKeyHeader] = apiKey.authKey;
                        }

                        return Promise.resolve(config);
                    } catch(error) {
                        debugMsg('!> Error while fetching the API key from database for API "' + api.name + '" :' , error);
                        logger.error('Error while fetching the API key from database for API "' + api.name + '" : ' + error);
                        return Promise.reject(error);
                    }
                }, null);
            }

            let response;

            switch (api.method) {
            case "get":
                if (socketObj && socketObj.socket) {
                    let socket = socketObj.socket;

                    socket.emit('QCDebug', 'GET URL', callURL);
                }

                startTime = moment();
                try {
                    response = await axios.get(callURL, {
                        headers: restHeader,
                        timeout: api.timeout
                    });

                    // Checks if the URL called is the Geolite city API
                    // Note: This check is only done on successful HTTP GET requests
                    // and has the limitation of only comparing the URL's origin with the one in the configuration
                    geoliteCallCountCheck(callURL);
                } catch(error) {
                    // Check if a response was received from the axios request and store the result
                    // in the exception to throw
                    if (error.response) {
                        result.collectionDuration = (moment().diff(startTime) / 1000).toFixed(2);
                        result.data = error.response.data;
                        result.status = error.response.status;

                        error.result = result;
                    }

                    throw error;
                }

                result.collectionDuration = (moment().diff(startTime) / 1000).toFixed(2);
                result.data = response.data;
                result.status = response.status;

                await apiContext.global.delete('config');

                await apiContext.global.set('response', new ivm.ExternalCopy(makeResponseTransferable(response)).copyInto());

                // Runs parse response code if applicable
                if (api.parseResponse) {
                    try {
                        response.data = await apiContext.eval(api.parseResponse, { copy: true, timeout: 2000 });
                        result.data = response.data;

                        // Could possibly re-write so that transformations are performed in
                        // the isolate context and checked there to avoid unnecessary copying of objects
                        await apiContext.global.set('response', new ivm.ExternalCopy(makeResponseTransferable(response)).copyInto());
                    } catch(error) {
                        error.result = result;
                        throw error;
                    }
                }

                errorConditionMet = false;
                if (api.errorCondition) {
                    try {
                        errorConditionMet = await apiContext.eval(api.errorCondition, { copy: true, timeout: 2000 });
                    } catch (error) {
                        debugMsg(`#> Got error when checking error condition for API "${api.name}"`);
                        error.result = result;
                        throw error;
                    }
                }

                if (errorConditionMet) {
                    debugMsg(`#> Error condition met for API "${api.name}"`);
                    let apiConditionError = new Error("API error condition met");
                    apiConditionError.result = result;
                    throw apiConditionError;
                }

                break;
            case "post":
                // Disables asyncCallback APIs in environments which do not have a known callback URL configured
                // Implemented so that sources / APIs that use async callbacks will fail immediately instead of after
                // the async callback timeout duration
                if (api.asyncCallback && (!api.asyncCallback.enabledEnvs.includes(global.gConfig.config_id) || global.gConfig.enableCallbackAPIs === false)) {
                    let callbackDisabledError = new Error("Async callback APIs are not enabled in this environment");
                    throw callbackDisabledError;
                }

                if (socketObj && socketObj.socket) {
                    let socket = socketObj.socket;

                    socket.emit('QCDebug', 'POST URL', callURL);
                }

                let params = null;
                if (restBody) {
                    params = restBody;
                }

                if (restHeader && restHeader["content-type"]) {
                    if (restHeader["content-type"] === "application/x-www-form-urlencoded") {
                        params = new URLSearchParams(params).toString();
                    }
                }

                startTime = moment();
                try {
                    response = await axios.post(callURL, params, {
                        headers: restHeader,
                        timeout: api.timeout
                    });
                } catch(error) {
                    // Check if a response was received from the axios request and store the result
                    // in the exception to throw
                    if (error.response) {
                        result.collectionDuration = (moment().diff(startTime) / 1000).toFixed(2);
                        result.data = error.response.data;
                        result.status = error.response.status;

                        error.result = result;
                    }

                    throw error;
                }

                result.collectionDuration = (moment().diff(startTime) / 1000).toFixed(2);
                result.data = response.data;
                result.status = response.status;

                await apiContext.global.delete('config');
                await apiContext.global.set('response', new ivm.ExternalCopy(makeResponseTransferable(response)).copyInto());

                if (api.parseResponse) {
                    try {
                        response.data = await apiContext.eval(api.parseResponse, { copy: true, timeout: 2000 });
                        result.data = response.data;

                        // Could possibly re-write so that transformations are performed in
                        // the isolate context and checked there to avoid unnecessary copying of objects
                        await apiContext.global.set('response', new ivm.ExternalCopy(makeResponseTransferable(response)).copyInto());
                    } catch(error) {
                        error.result = result;
                        throw error;
                    }
                }

                if (api.asyncPoll) {
                    let pollCondition = false;
                    if (api.pollCondition) {
                        try {
                            pollCondition = await apiContext.eval(api.pollCondition, { copy: true, timeout: 2000 });
                        } catch (error) {
                            logger.error(`Error when determining poll condition for API ${api.name}, ${error.toString()}`);
                            error.result = result;
                            throw error;
                        }
                    }

                    if (pollCondition) {
                        let pollError = new Error("Poll condition not met");
                        pollError.result = result;
                        throw pollError;
                    }

                    let pollURL = await createUrl(api.baseUrl, api.asyncPoll.uri, api.asyncPoll.queryParams, parameters, response);

                    let asyncPollRestHeaders = null;
                    if (api.asyncPoll.header) {
                        await apiContext.global.set('config', apiHeaderConfigExternalCopy.copyInto());
                        asyncPollRestHeaders = JSON.parse(await apiContext.eval(api.asyncPoll.header, { copy: true, timeout: 2000 }));
                    }

                    let asyncPollStart = moment();
                    let asyncPollSuccess = false;

                    let pollResponse = null;

                    // Note: Currently the total async poll timeout is the same as the request timeout
                    while (moment().diff(asyncPollStart) < api.asyncPoll.timeout) {
                        try {
                            pollResponse = await axios.get(pollURL, {
                                headers: asyncPollRestHeaders
                            });

                            result.collectionDuration = (moment().diff(startTime) / 1000).toFixed(2);
                            result.data = pollResponse.data;
                            result.status = pollResponse.status;

                            try {
                                await apiContext.global.delete('config');
                                await apiContext.global.set('response', new ivm.ExternalCopy(makeResponseTransferable(pollResponse)).copyInto());

                                if (api.asyncPoll.parseResponse) {
                                    pollResponse.data = await apiContext.eval(api.asyncPoll.parseResponse, { copy: true, timeout: 2000 });
                                    result.data = pollResponse.data;

                                    // Probably not ideal, but creates new copy of poll response for isolate
                                    // Could possibly re-write so that transformations are performed in
                                    // the isolate context and checked there to avoid unnecessary copying of objects
                                    await apiContext.global.set('response', new ivm.ExternalCopy(makeResponseTransferable(pollResponse)).copyInto());
                                }

                                if (api.asyncPoll.errorCondition) {
                                    let errorConditionMet = await apiContext.eval(api.asyncPoll.errorCondition, { copy: true, timeout: 2000 });
                                    debugMsg(`#> "${api.asyncPoll.name}" errorCondition result = ${errorConditionMet} for response: `, pollResponse.data);

                                    if (errorConditionMet) {
                                        throw new Error('Error condition met for async poll API ' + api.asyncPoll.name);
                                    }
                                }


                                if (await apiContext.eval(api.asyncPoll.doneCondition, { copy: true, timeout: 2000 })) {
                                    debugMsg(`done condition met`, pollResponse.data);
                                    if (api.asyncPoll.transform) {
                                        Object.keys(api.asyncPoll.transform).forEach((key) => {
                                            if (pollResponse.data.hasOwnProperty(key)) {
                                                // Uses evalSync() here as it's inside the forEach function, could possible be moved
                                                pollResponse.data[key] = apiContext.evalSync(api.asyncPoll.transform[key], { copy: true, timeout: 2000 });
                                            }
                                        });

                                        result.data = pollResponse.data;
                                    }

                                    asyncPollSuccess = true;
                                    break;
                                }
                            } catch(error) {
                                error.result = result;
                                throw error;
                            }
                        } catch(error) {
                            // If a non HTTP 2XX response is received, continue polling
                            if (!error.response) {
                                throw error;
                            }
                        }

                        let timeRemaining = api.asyncPoll.timeout - moment().diff(asyncPollStart);
                        if (timeRemaining > 0) {
                            // Waits for the specified interval for asyncpoll;
                            await new Promise(resolve => { setTimeout(resolve, Math.min(api.asyncPoll.interval, timeRemaining)); });
                        }
                    }

                    if (asyncPollSuccess && api.asyncPoll.resultAPI) {
                        let resultAPI = api.asyncPoll.resultAPI;

                        let resultURL = await createUrl(api.baseUrl, resultAPI.uri, resultAPI.queryParams, parameters, pollResponse);
                        debugMsg(`#> Result set URI "${resultURL}"`);

                        let restHeader = null;
                        if (resultAPI.header) {
                            await apiContext.global.set('config', apiHeaderConfigExternalCopy);
                            restBody = JSON.parse(await apiContext.eval(resultAPI.header, { copy: true, timeout: 2000 }));
                        }

                        let resultResponse;
                        try {
                            resultResponse = await axios.get(resultURL, {
                                headers: restHeader,
                                timeout: resultAPI.timeout
                            });
                        } catch(error) {
                            if (error.response) {
                                result.collectionDuration = (moment().diff(startTime) / 1000).toFixed(2);
                                result.data = error.response.data;
                                result.status = error.response.status;

                                error.result = result;
                            }
                            throw error;
                        }

                        result.collectionDuration = result.collectionDuration = (moment().diff(startTime) / 1000).toFixed(2);
                        result.data = resultResponse.data;
                        result.status = resultResponse.status;

                        try {
                            await apiContext.global.delete('config');
                            await apiContext.global.set('response', new ivm.ExternalCopy(makeResponseTransferable(resultResponse)).copyInto());

                            if (resultAPI.parseResponse) {
                                resultResponse.data = await apiContext.eval(resultAPI.parseResponse, { copy: true, timeout: 2000 });
                                result.data = resultResponse.data;

                                await apiContext.global.set('response', new ivm.ExternalCopy(makeResponseTransferable(resultResponse)).copyInto());
                            }

                            if (resultAPI.transform) {
                                Object.keys(resultAPI.transform).forEach((key) => {
                                    if (resultResponse.data && resultResponse.data.hasOwnProperty(key)) {
                                        resultResponse.data[key] = apiContext.evalSync(resultAPI.transform[key], { copy: true, timeout: 2000 });
                                    }
                                });
                                result.data = resultResponse.data;
                            }
                        } catch(error) {
                            // Handle error from parse response or transform in result API
                            error.result = result;
                            throw error;
                        }
                    } else if (!asyncPollSuccess) {
                        throw new Error(`Timeout of ${api.asyncPoll.timeout}ms exceeded without meeting done condition`);
                    }
                } else if (api.asyncCallback) {
                    let pollCondition = null;

                    if (api.pollCondition) {
                        try {
                            pollCondition = await apiContext.eval(api.pollCondition, { copy: true, timeout: 2000 });
                        } catch (error) {
                            logger.error(`Error when determining poll condition for API ${api.name}, ${error.toString()}`);
                            error.result = result;
                            throw error;
                        }
                    }

                    // TODO: Currently poll condition has the opposite effect of what's being done in asyncPoll type APIs,
                    // make api.pollCondition condition consistent with both at a later time
                    if (!pollCondition) {
                        let pollError = new Error("No value present from poll condition for ID in asynchronous callback API");
                        pollError.result = result;
                        throw pollError;
                    }

                    let resolveCallbackResultPromise = null;
                    let rejectCallbackResultPromise = null;
                    const handleAsyncCallbackResponseFunction = function(callbackResponseData) {
                        apiContext.global.setSync('response', new ivm.ExternalCopy({
                            data: callbackResponseData
                        }).copyInto());

                        let idValue = null;
                        try {
                            idValue = apiContext.evalSync(api.asyncCallback.idField, { copy: true, timeout: 2000 });
                        } catch(error) {
                            rejectCallbackResultPromise(error);
                            return;
                        }

                        // The value from poll condition should resolve to a value that can be checked in
                        // request data made to the /callback endpoint and if it matches, then check
                        // the request data (response in this context) for its done condition
                        if (pollCondition == idValue) {
                            try {
                                // The HTTP status is not set here because HTTP asynchronous callbacks do not contain
                                // a HTTP status code from the server sending the request
                                result.collectionDuration = (moment().diff(startTime) / 1000).toFixed(2);
                                result.data = callbackResponseData;

                                if (api.asyncCallback.parseResponse) {
                                    callbackResponseData = apiContext.evalSync(api.asyncCallback.parseResponse, { copy: true, timeout: 2000 });
                                    result.data = callbackResponseData;
                                }

                                if (api.asyncCallback.errorCondition) {
                                    let errorConditionMet = apiContext.evalSync(api.asyncCallback.errorCondition, { copy: true, timeout: 2000 });

                                    if (errorConditionMet) {
                                        throw new Error('Error condition met for async callback API ' + api.name);
                                    }
                                }

                                // Only resolves the promise if the async callback done condition is met
                                if (apiContext.evalSync(api.asyncCallback.doneCondition, { copy: true, timeout: 2000 })) {
                                    resolveCallbackResultPromise();
                                }
                            } catch(error) {
                                rejectCallbackResultPromise(error);
                            }
                        }
                    }

                    let asyncCallbackResultPromise = new Promise((resolve, reject) => {
                        resolveCallbackResultPromise = resolve;
                        rejectCallbackResultPromise = reject;

                        callbackEmitter.on('response', handleAsyncCallbackResponseFunction);
                    });

                    let timeoutPromise = new Promise((resolve, reject) => {
                        setTimeout(() => {
                            reject(new Error(`Timeout of ${api.asyncCallback.timeout}ms exceeded without meeting done condition for callback response`));
                        }, api.asyncCallback.timeout);
                    });

                    try {
                        await Promise.race([
                            asyncCallbackResultPromise,
                            timeoutPromise
                        ]);
                    } catch(error) {
                        error.result = result;
                        throw error;
                    } finally {
                        // Removes current instance of the listener for async callback response
                        callbackEmitter.removeListener('response', handleAsyncCallbackResponseFunction);
                    }
                }

                errorConditionMet = false;
                if (api.errorCondition) {
                    try {
                        errorConditionMet = await apiContext.eval(api.errorCondition, { copy: true, timeout: 2000 });
                    } catch (error) {
                        debugMsg(`#> Got error when checking error condition for API "${api.name}"`);
                        error.result = result;
                        throw error;
                    }
                }

                if (errorConditionMet) {
                    debugMsg(`#> Error condition met for API "${api.name}"`);
                    let apiConditionError = new Error("API error condition met");
                    apiConditionError.result = result;
                    throw apiConditionError;
                }

                break;
            default:
                throw new Error(`Unrecognosed HTTP REST method ${api.method} for API ${api.name}`);
            }
            break;
        case "function":
            // Currently the API function name is stored in the baseUrl field, may be moved in the future
            const apiFunction = apiFunctions[api.baseUrl];

            if (typeof apiFunction !== 'function') {
                throw new NotFoundError(`API function not found: ${api.baseUrl}`);
            }

            startTime = moment();

            // Note: Currently uses object evaluated from queryParams for the function parameters
            const apiFunctionParameters = await createFunctionParameters(api.queryParams, parameters);
            try {
                const apiFunctionReturnValue = await apiFunction(apiFunctionParameters);

                result.data = apiFunctionReturnValue;
            } catch(error) {
                logger.error(`Uncaught exception when running API function for API: ${api.name}, ${error.toString()}`);
                throw new ApiFunctionError('Uncaught exception when running API function');
            } finally {
                result.collectionDuration = (moment().diff(startTime) / 1000).toFixed(2);
            }

            break;
        default:
            throw new Error(`Unrecognosed API type ${api.apiType} for API ${api.name}`);
        }

        return result;
    } finally {
        apiContext.release();
        if (!isolate.isDisposed) {
            isolate.dispose();
        }
    }
}


async function createUrl(baseUrl, uri, queryParams, parameters, response) {
    const isolate = new ivm.Isolate({
        memoryLimit: 32
    });
    let urlContext = await isolate.createContext();

    try {
        await urlContext.global.set('config', new ivm.ExternalCopy(global.gConfig.APIUriConfig).copyInto());
        await urlContext.global.set('parameters', new ivm.ExternalCopy(parameters).copyInto());

        // Includes response in URL creation, currently used for async poll / async poll results
        if (response) {
            await urlContext.global.set('response', new ivm.ExternalCopy(makeResponseTransferable(response)).copyInto());
        }

        let baseUrlString = await urlContext.eval(baseUrl, { timeout: 1000 });
        if (typeof baseUrlString !== 'string') {
            throw new TypeError('baseUrl field should resolve as a string');
        }

        let apiUrl = new url.URL(baseUrlString);

        if (uri) {
            let uriString = await urlContext.eval(uri, { timeout: 1000 });
            if (typeof uriString !== 'string') {
                throw new TypeError('uri field should resolve as a string');
            }

            apiUrl.pathname = urlJoin(apiUrl.pathname, uriString);
        }

        if (queryParams) {
            let searchParamsObject = await urlContext.eval(queryParams, { copy: true, timeout: 1000 });
            if (!_.isPlainObject(searchParamsObject)) {
                throw new TypeError('queryParams field should resolve as a plain object');
            }

            apiUrl.search = queryString.stringify(searchParamsObject, { arrayFormat: 'bracket' });
        }

        return apiUrl.toString();
    } finally {
        urlContext.release();
        if (!isolate.isDisposed) {
            isolate.dispose();
        }
    }
}


async function createFunctionParameters(queryParams, parameters) {
    const isolate = new ivm.Isolate({
        memoryLimit: 32
    });
    let parameterContext = await isolate.createContext();

    try {
        await parameterContext.global.set('parameters', new ivm.ExternalCopy(parameters).copyInto());

        if (queryParams) {
            let functionParamsObject = await parameterContext.eval(queryParams, { copy: true, timeout: 1000 });
            if (!_.isPlainObject(functionParamsObject)) {
                throw new TypeError('queryParams field should resolve as a plain object');
            }

            return functionParamsObject;
        } else {
            return {};
        }
    } finally {
        parameterContext.release();
        if (!isolate.isDisposed) {
            isolate.dispose();
        }
    }
}


// Helper function that attempts to make an instance of axios.Resopnse transferable
// for use in the isolated-vm context (creates a copy of the response)
// Excludes fields from Response class that may be non-transferable (i.e. containing functions / symbols etc.)
function makeResponseTransferable(response) {
    let transferableResponse = {
        ..._.pick(response, [
            'status',
            'statusText',
            'headers',
            'data'
        ])
    };

    // Copies all request headers into request.headers for the transferable response
    if (response.request) {
        transferableResponse.request = {
            headers: response.request.getHeaders()
        };
    }

    return transferableResponse;
}

export default {
    collectApi: collectApi,
    createUrl: createUrl
};
