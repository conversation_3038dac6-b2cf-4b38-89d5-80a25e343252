'use strict';

import express from 'express';
import moment from 'moment';
import { query, validationResult } from 'express-validator';
import { isValidPhoneNumber, parsePhoneNumber } from 'libphonenumber-js';
import _ from 'lodash';

import dbSession from '../db/model/sessions.js';

import ServiceCheckModel from '../db/model/serviceCheck.js';

import dbCommandRuns from '../db/model/commandRun.js';
import dbAggTopUsersQC from '../db/model/aggCommandRuns.js';
import dbAggWeeklyQuickCommands from '../db/model/aggCommandRuns.js';
import dbAggMonthlyQuickCommands from '../db/model/aggCommandRuns.js';

import { calculateStartDate } from '../modules/helpers/date.js';
import statistics from '../modules/statistics.js';
import validators from '../modules/validators.js';
import pagination from '../modules/pagination.js';
import logger from '../modules/logger.js';

const TIME_ZONE = 'Australia/Victoria';
const router = express.Router();


/**
 * @swagger
 * components:
 *   parameters:
 *     last:
 *       name: last
 *       in: query
 *       description: Time from when service checks will be included
 *       required: false
 *       schema:
 *         type: string
 *         pattern: '^(\d+[d|w|m|y])$'
 *     interval:
 *       name: interval
 *       in: query
 *       description: Time interval to split the service checks by
 *       required: false
 *       schema:
 *         type: string
 *         pattern: '^([d|w|m|y])$'
 *     limit:
 *       name: limit
 *       in: query
 *       description: The maximum number of results returned
 *       required: false
 *       schema:
 *         type: integer
 *         minimum: 1
 *     offset:
 *       name: offset
 *       in: query
 *       description: The offset from where the results start
 *       required: false
 *       schema:
 *         type: integer
 *         minimum: 0
 *     carriageTypeFilter:
 *       name: carriageType
 *       in: query
 *       description: Carriage type filter for service checks, will only include service checks with any of the specified carriage types
 *       required: false
 *       schema:
 *         type: array
 *         items:
 *           type: string
 *     userFilter:
 *       name: user
 *       in: query
 *       description: Username filter for service checks, will only include service checks created by any of the specified user
 *       required: false
 *       schema:
 *         type: array
 *         items:
 *           type: string
 *     fnnFilter:
 *       name: fnn
 *       in: query
 *       description: FNN filter for service checks, will only include service checks with any of the specified FNNs
 *       required: false
 *       schema:
 *         type: array
 *         items:
 *           type: string
 *     ruleFilter:
 *       name: rule
 *       in: query
 *       description: Rule name filter for service checks, will only include service checks with any of the specified rules
 *       required: false
 *       schema:
 *         type: array
 *         items:
 *           type: string
 *     ruleStatusFilter:
 *       name: ruleStatus
 *       in: query
 *       description: Rule status filter for service checks, will only be applied when used in conjunction with rule name filter
 *       required: false
 *       schema:
 *         type: array
 *         items:
 *           type: string
 *     ruleResultFilter:
 *       name: ruleResult
 *       in: query
 *       description: Rule result filter for service checks, will only be applied when used in conjunction with rule name filter
 *       required: false
 *       schema:
 *         type: array
 *         items:
 *           type: string
 *     ruleTriggerSourceFilter:
 *       name: ruleTriggerSource
 *       in: query
 *       description: Rule trigger source filter for service checks, will only be applied when used in conjunction with rule name filter
 *       required: false
 *       schema:
 *         type: array
 *         items:
 *           type: string
 *     sourceFilter:
 *       name: source
 *       in: query
 *       description: Source name filter for service checks, will only include service checks with any of the specified sources
 *       required: false
 *       schema:
 *         type: array
 *         items:
 *           type: string
 *     sourceStatusFilter:
 *       name: sourceStatus
 *       in: query
 *       description: Source status filter for service checks, will only be applied when used in conjunction with source name filter
 *       required: false
 *       schema:
 *         type: array
 *         items:
 *           type: string
 *     order:
 *       name: order
 *       in: query
 *       description: Parameter to sort the results in ascending or descending order
 *       required: false
 *       schema:
 *         type: string
 *         enum: [asc, desc]
 *   schemas:
 *     start:
 *       description: Starting date and time for the current interval for service checks
 *       type: string
 *       format: date-time
 *     user:
 *       description: Username for user
 *       type: string
 *     carriageType:
 *       description: Carriage type name
 *       type: string
 *     rule:
 *       description: Rule name
 *       type: string
 *     source:
 *       description: Source name
 *       type: string
 *     fnn:
 *       description: Service FNN
 *       type: string
 *     count:
 *       description: Number of service checks in group
 *       type: integer
 *     lastServiceCheck:
 *       description: Date and time of last service check
 *       type: string
 *       format: date-time
 *     pagination:
 *       type: object
 *       properties:
 *         limit:
 *           description: Maximum number of results that are returned
 *           type: integer
 *         offset:
 *           description: Pagination offset index
 *           type: integer
 *         total:
 *           description: Count of all results for this query
 *           type: integer
 *         prev:
 *           description: Pagination previous page URL
 *           type: string
 *         next:
 *           description: Pagination next page URL
 *           type: string
 */

// ------------------------------------------------------------
async function getUsersOn() {
    let users = new Map();
    let userSessions = [];

    let sessions = await dbSession.find({expires:{ $gte: new Date() }}).sort({ expires: -1 }).limit(100);

    sessions.forEach(function(session, i) {
        let obj = JSON.parse(sessions[i].session);
        if (obj && obj.passport && obj.passport.user) {
            let lvl = obj.passport && obj.passport.user && obj.passport.user.isDeveloper?"Developer":"";
            lvl = `${lvl}${!lvl.endsWith("/")&&obj.passport.user.isAdmin?"/":""}${obj.passport.user.isAdmin?"Admin":""}`
            lvl = `${lvl}${!lvl.endsWith("/")&&obj.passport.user.leveLead?"/":""}${obj.passport.user.leveLead?"LevelLead":""}`
            lvl = `${lvl}${!lvl.endsWith("/")&&obj.passport.user.level0?"/":""}${obj.passport.user.level0?"Level0":""}`
            lvl = `${lvl}${!lvl.endsWith("/")&&obj.passport.user.level1?"/":""}${obj.passport.user.level1?"Level1":""}`
            lvl = `${lvl}${!lvl.endsWith("/")&&obj.passport.user.level2?"/":""}${obj.passport.user.level2?"Level2":""}`
            lvl = `${lvl}${!lvl.endsWith("/")&&obj.passport.user.level3?"/":""}${obj.passport.user.level3?"Level3":""}`
            sessions[i].userInfo = `${obj.passport.user.name} (${obj.passport.user.username}) [${obj.passport.user.department?obj.passport.user.department:""} ${lvl}]`
            sessions[i].userExpires = moment(sessions[i].expires).subtract(global.gConfig.cookieAgeHours, 'hours').format("DD/MM/YYYY HH:mm")
            sessions[i].session = JSON.parse(sessions[i].session);
            sessions[i].userLogin = moment(obj.passport.user.login).format("DD/MM/YYYY HH:mm");
            if (!users.has(obj.passport.user.username)) {
                users.set(obj.passport.user.username, obj.passport.user.username)
                userSessions.push({
                    userInfo: sessions[i].userInfo,
                    userExpires: sessions[i].userExpires,
                    session: sessions[i].session,
                    userLogin: sessions[i].userLogin
                });
            };
        }
    });

    return userSessions;
}

async function getServiceCheckStatisticsByField(req, responseFieldName, modelFieldName) {
    let limit = (req.query.limit === undefined) ? 100 : req.query.limit;
    let offset = (req.query.offset === undefined) ? 0 : req.query.offset;
    let carriageTypeFilter = (req.query.carriageType === undefined) ? [] : req.query.carriageType;
    let userFilter = (req.query.user === undefined) ? [] : req.query.user;
    let fnnFilter = (req.query.fnn === undefined) ? [] : req.query.fnn;
    let rulesFilter = (req.query.rule === undefined) ? [] : req.query.rule;
    let ruleStatusFilter = (req.query.ruleStatus === undefined) ? [] : req.query.ruleStatus;
    let ruleResultFilter = (req.query.ruleResult === undefined) ? [] : req.query.ruleResult;
    let ruleTriggerSourceFilter = (req.query.ruleTriggerSource === undefined) ? [] : req.query.ruleTriggerSource;
    let sourcesFilter = (req.query.source === undefined) ? [] : req.query.source;
    let sourceStatusFilter = (req.query.sourceStatus === undefined) ? [] : req.query.sourceStatus;
    let orderBy = (req.query.order === undefined) ? 'desc' : req.query.order;
    let orderByParam = (orderBy === 'desc') ? -1 : 1;
    let last = (req.query.last === undefined) ? "1d" : req.query.last;
    let interval = req.query.interval;

    let serviceCheckStartDate = calculateStartDate(last);

    // Setup filters here
    let serviceCheckMatch = { createdOn: { $gte: serviceCheckStartDate }};
    let filters = {
        carriageType: carriageTypeFilter,
        user: userFilter,
        fnn: fnnFilter,
        rules: rulesFilter,
        ruleStatus: ruleStatusFilter,
        ruleResult: ruleResultFilter,
        ruleTriggerSource: ruleTriggerSourceFilter,
        sources: sourcesFilter,
        sourceStatus: sourceStatusFilter
    }

    let serviceCheckFilters = getServiceCheckFilters(filters);

    if (serviceCheckFilters.length > 0) {
        serviceCheckMatch.$and = serviceCheckFilters;
    }

    let statisticsAggregation;

    if (interval === undefined) {
        statisticsAggregation = [
            { $addFields: {
                fnn: {
                    $ifNull: ['$phoneNumber', '$fnn']
                }
            }},
            { $match: serviceCheckMatch },
            { $group: { _id: { start: serviceCheckStartDate, [responseFieldName]: modelFieldName }, count: {$sum: 1}}}
        ];
    } else {
        // Get the project field for mongo that will place service check entries into their appropriate
        // time interval and add user field as well to group by
        let project = calculateAggregateDateProject(interval);
        project["field"] = modelFieldName;

        statisticsAggregation = [
            { $addFields: {
                fnn: {
                    $ifNull: ['$phoneNumber', '$fnn']
                }
            }},
            { $match: serviceCheckMatch },
            { $project: project },
            { $group: { _id: { start: '$start', [responseFieldName]: '$field' }, count: {$sum: 1}}},
        ];
    }

    let [stats, countResult] = await Promise.all([
        await ServiceCheckModel.aggregate([
            ...statisticsAggregation,
            { $sort: { '_id.start': -1, count: orderByParam, ['_id.' + responseFieldName]: 1 }},
            { $skip: offset },
            { $limit: limit }
        ]).collation({ locale: 'en' }),
        await ServiceCheckModel.aggregate([
            ...statisticsAggregation,
            { $count: 'total' }
        ]).collation({ locale: 'en' })
    ]);
    let count = _.get(countResult, [0, 'total'], 0);

    let paginationMetadata = pagination.createPaginationMetadata(req, limit, offset, count);

    let resultsArray = [];
    stats.forEach(function (sc, _) {
        let result = {
            start: new Date(sc._id.start),
            count: sc.count
        };

        if (responseFieldName !== null) {
            result[responseFieldName] = sc._id[responseFieldName];
        }

        resultsArray.push(result);
    });

    let filterMetadata = {};
    for (const [filterName, filter] of Object.entries(filters)) {
        if (filter.length > 0) {
            filterMetadata[filterName] = filter;
        }
    }

    return {
        metadata: {
            pagination: paginationMetadata,
            filter: filterMetadata,
            order: orderBy,
            startDate: serviceCheckStartDate
        },
        results: resultsArray
    };
}


async function getServiceCheckStatisticsByNestedField(req, responseFieldName, modelFieldName) {
    let limit = (req.query.limit === undefined) ? 100 : req.query.limit;
    let offset = (req.query.offset === undefined) ? 0 : req.query.offset;
    let carriageTypeFilter = (req.query.carriageType === undefined) ? [] : req.query.carriageType;
    let userFilter = (req.query.user === undefined) ? [] : req.query.user;
    let fnnFilter = (req.query.fnn === undefined) ? [] : req.query.fnn;
    let rulesFilter = (req.query.rule === undefined) ? [] : req.query.rule;
    let ruleStatusFilter = (req.query.ruleStatus === undefined) ? [] : req.query.ruleStatus;
    let ruleResultFilter = (req.query.ruleResult === undefined) ? [] : req.query.ruleResult;
    let ruleTriggerSourceFilter = (req.query.ruleTriggerSource === undefined) ? [] : req.query.ruleTriggerSource;
    let sourcesFilter = (req.query.source === undefined) ? [] : req.query.source;
    let sourceStatusFilter = (req.query.sourceStatus === undefined) ? [] : req.query.sourceStatus;
    let orderBy = (req.query.order === undefined) ? 'desc' : req.query.order;
    let orderByParam = (orderBy === 'desc') ? -1 : 1;
    let last = (req.query.last === undefined) ? "1d" : req.query.last;
    let interval = req.query.interval;

    let serviceCheckStartDate = calculateStartDate(last);

    // Setup filters here, also do not include service checks where the nested field is not an object
    let serviceCheckMatch = { createdOn: { $gte: serviceCheckStartDate }, [modelFieldName.replace("$", "")]: { $type: "object" }};
    let filters = {
        carriageType: carriageTypeFilter,
        user: userFilter,
        fnn: fnnFilter,
        rules: rulesFilter,
        ruleStatus: ruleStatusFilter,
        ruleResult: ruleResultFilter,
        ruleTriggerSource: ruleTriggerSourceFilter,
        sources: sourcesFilter,
        sourceStatus: sourceStatusFilter
    }
    let serviceCheckFilters = getServiceCheckFilters(filters);

    if (serviceCheckFilters.length > 0) {
        serviceCheckMatch.$and = serviceCheckFilters;
    }

    // Only include rules / sources in the final result if a filter is enabled
    let nestedFieldMatch = {};
    if (responseFieldName == "rule" && filters.rules.length > 0) {
        nestedFieldMatch = { data: { $in: rulesFilter }};
    } else if (responseFieldName == "source" && filters.sources.length > 0) {
        nestedFieldMatch = { data: { $in: sourcesFilter }};
    }

    let statisticsAggregation;

    if (interval === undefined) {
        statisticsAggregation = [
            { $match: serviceCheckMatch },
            { $addFields: { dataArray: { $objectToArray: modelFieldName }}},
            { $unwind: '$dataArray' },
            { $addFields: { data: '$dataArray.k' }},
            { $match: nestedFieldMatch },
            { $group: { _id: { start: serviceCheckStartDate, [responseFieldName]: '$data' }, count: {$sum: 1}}}
        ];
    } else {
        let project = calculateAggregateDateProject(interval);
        project["field"] = "$data";

        statisticsAggregation = [
            { $match: serviceCheckMatch },
            { $addFields: { dataArray: { $objectToArray: modelFieldName }}},
            { $unwind: '$dataArray' },
            { $addFields: { data: '$dataArray.k' }},
            { $match: nestedFieldMatch },
            { $project: project },
            { $group: { _id: { start: '$start', [responseFieldName]: '$field' }, count: {$sum: 1}}}
        ];
    }

    let [stats, countResult] = await Promise.all([
        await ServiceCheckModel.aggregate([
            ...statisticsAggregation,
            { $sort: { '_id.start': -1, count: orderByParam, ['_id.' + responseFieldName]: 1 }},
            { $skip: offset },
            { $limit: limit }
        ]).collation({ locale: 'en' }),
        await ServiceCheckModel.aggregate([
            ...statisticsAggregation,
            { $count: 'total' }
        ]).collation({ locale: 'en' })
    ]);
    let count = _.get(countResult, [0, 'total'], 0);

    let paginationMetadata = pagination.createPaginationMetadata(req, limit, offset, count);
    let resultsArray = [];

    stats.forEach(function (sc, _) {
        resultsArray.push({
            start: new Date(sc._id.start),
            [responseFieldName]: sc._id[responseFieldName],
            count: sc.count
        });
    });


    let filterMetadata = {};
    for (const [filterName, filter] of Object.entries(filters)) {
        if (filter.length > 0) {
            filterMetadata[filterName] = filter;
        }
    }

    return {
        metadata: {
            pagination: paginationMetadata,
            filter: filterMetadata,
            order: orderBy,
            startDate: serviceCheckStartDate
        },
        results: resultsArray
    };
}


async function getScsLastCheckPerUser(req, callback) {
    let last = req.query.last;
    let limit = req.query.limit;
    let offset = req.query.offset;

    let serviceCheckStartDate = calculateStartDate(last);

    let lastCheckPerUserAggregate = [
        { $match:  { createdOn: { $gte: serviceCheckStartDate }}},
        { $project: { createdOn: '$createdOn' , createdBy: '$createdBy' }},
        { $group: { _id: {user: '$createdBy'}, createdOn: { $max: '$createdOn' }}}
    ];

    let [stats, countResult] = await Promise.all([
        await ServiceCheckModel.aggregate([
            ...lastCheckPerUserAggregate,
            { $sort: { _id: -1 }},
            { $skip: offset },
            { $limit: limit }
        ]).collation({ locale: 'en' }),
        await ServiceCheckModel.aggregate([
            ...lastCheckPerUserAggregate,
            { $count: 'total' }
        ]).collation({ locale: 'en' })
    ]);
    let count = _.get(countResult, [0, 'total'], 0);

    let paginationMetadata = pagination.createPaginationMetadata(req, limit, offset, count);

    let resultsArray = [];
    stats.forEach(function (sc, _) {
        resultsArray.push({
            user: sc._id.user,
            lastServiceCheck: sc.createdOn
        });
    });

    return {
        metadata: {
            pagination: paginationMetadata,
            startDate: serviceCheckStartDate
        },
        results: resultsArray
    };
}


// ------------------------------------------------------------
async function getQcsToday() {
    return await dbCommandRuns.find({
        createdOn: {
            $lt: new Date(),
            $gte: new Date(new Date().setDate(new Date().getDate() - 1))
        }
    });
}

// ------------------------------------------------------------
async function getQcsTopUsers() {
   return await dbAggTopUsersQC.aggregate(
        [ { $match: { createdOn: { $gte: new Date((new Date().getTime() - (365 * 24 * 60 * 60 * 1000))) } } }
          , { $group: { _id: '$createdBy', count: {$sum: 1}}}
          , { $sort:  {count: -1}}]);
}

// ------------------------------------------------------------
async function getQcsWeekly() {
    // Past 90 days only
    let quickCommands = await dbAggWeeklyQuickCommands.aggregate([
        { $match: { createdOn: { $gte: new Date((new Date().getTime() - (90 * 24 * 60 * 60 * 1000))) } } },
        { $project: {
            'week': { $week: '$createdOn' },
            'weekStart': {
                "$let": {
                    "vars": {
                        "dayMillis": 1000 * 60 * 60 * 24,
                        "beginWeek": { "$subtract": [{ "$subtract": [ "$createdOn", new Date("1970-01-01") ] },
                            { "$cond": [{ "$eq": [{ "$dayOfWeek": "$createdOn" }, 1 ] },
                                0,
                                { "$multiply": [1000 * 60 * 60 * 24,{ "$subtract": [{ "$dayOfWeek": "$createdOn" }, 1 ]}]}
                            ]}
                        ]}
                    },
                    "in": { "$subtract": ["$$beginWeek",{ "$mod": [ "$$beginWeek", "$$dayMillis" ]}]}
                }
            }
        }},
        { $group: { _id: { week: '$week', weekStart: '$weekStart' }, documentCount: {$sum: 1}}},
        { $sort: { _id: -1 }}
    ]);

    let unique = [];
    quickCommands.forEach(function (qc, i) {
        unique.push({
            _id: {
                week: qc._id.week,
                weekStart: msToDate(qc._id.weekStart)
            },
            documentCount: qc.documentCount
        });
    });

    return unique;
}

// ------------------------------------------------------------
async function getQcsMonthly() {
    // Past 12 months
    return await dbAggMonthlyQuickCommands.aggregate([
        { $match: { createdOn: { $gte: new Date((new Date().getTime() - (365 * 24 * 60 * 60 * 1000))) } } },
        { $group: { _id: {$month: '$createdOn'}, documentCount: {$sum: 1}}},
        { $sort:  { _id: -1} }
    ]);
}

// ------------------------------------------------------------
function msToDate(_ms) {
    // Week starting on Monday
    return moment(_ms).add(1,'days').format("DD/MM/YYYY")
}

// Routes
//-------------------------------------------------------------------
router.get('/usersOn', async function (req, res, next) {
    try {
        res.send(await getUsersOn());
    } catch (error) {
        logger.error(`Error getting users logged on, ${error.toString()}`);
        res.sendStatus(500);
    }
});


/**
 * @swagger
 * /stats/scsUsers:
 *   get:
 *     summary: Get the number of service checks grouped by users
 *     parameters:
 *       - $ref: '#/components/parameters/last'
 *       - $ref: '#/components/parameters/interval'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/offset'
 *       - $ref: '#/components/parameters/carriageTypeFilter'
 *       - $ref: '#/components/parameters/userFilter'
 *       - $ref: '#/components/parameters/fnnFilter'
 *       - $ref: '#/components/parameters/ruleFilter'
 *       - $ref: '#/components/parameters/ruleStatusFilter'
 *       - $ref: '#/components/parameters/ruleResultFilter'
 *       - $ref: '#/components/parameters/ruleTriggerSourceFilter'
 *       - $ref: '#/components/parameters/sourceFilter'
 *       - $ref: '#/components/parameters/sourceStatusFilter'
 *       - $ref: '#/components/parameters/order'
 *     security:
 *       - MergeToken: []
 *     responses:
 *       200:
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *                type: object
 *                properties:
 *                  results:
 *                    type: array
 *                    items:
 *                      properties:
 *                        start:
 *                          $ref: '#/components/schemas/start'
 *                        user:
 *                          $ref: '#/components/schemas/user'
 *                        count:
 *                          $ref: '#/components/schemas/count'
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Invalid parameters
 *     tags:
 *       - Statistics
 */
router.get('/scsUsers', [
    query('last').optional().custom(validators.isTimePeriod),
    query('interval').optional().custom(validators.isTimeInterval),
    query('limit').optional().isInt({ min: 1 }).toInt(),
    query('offset').optional().isInt({ min: 0 }).toInt(),
    query('carriageType').optional().toArray(),
    query('user').optional().toArray(),
    query('fnn').optional().toArray(),
    query('rule').optional().toArray(),
    query('ruleStatus').optional().toArray(),
    query('ruleResult').optional().toArray(),
    query('ruleTriggerSource').optional().toArray(),
    query('source').optional().toArray(),
    query('sourceStatus').optional().toArray(),
    query('order').optional().isIn(['asc', 'desc'])
        .withMessage('Order must be either "asc" or "desc"')
], async function (req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }

    try {
        res.send(await getServiceCheckStatisticsByField(req, 'user', '$createdBy'));
    } catch(error) {
        res.status(500).send({ error: error.message });
    }
});

/**
 * @swagger
 * /stats/scsCarriageTypes:
 *   get:
 *     summary: Get the number of service checks grouped by carriage types
 *     parameters:
 *       - $ref: '#/components/parameters/last'
 *       - $ref: '#/components/parameters/interval'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/offset'
 *       - $ref: '#/components/parameters/carriageTypeFilter'
 *       - $ref: '#/components/parameters/userFilter'
 *       - $ref: '#/components/parameters/fnnFilter'
 *       - $ref: '#/components/parameters/ruleFilter'
 *       - $ref: '#/components/parameters/ruleStatusFilter'
 *       - $ref: '#/components/parameters/ruleResultFilter'
 *       - $ref: '#/components/parameters/ruleTriggerSourceFilter'
 *       - $ref: '#/components/parameters/sourceFilter'
 *       - $ref: '#/components/parameters/sourceStatusFilter'
 *       - $ref: '#/components/parameters/order'
 *     security:
 *       - MergeToken: []
 *     responses:
 *       200:
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *                type: object
 *                properties:
 *                  results:
 *                    type: array
 *                    items:
 *                      properties:
 *                        start:
 *                          $ref: '#/components/schemas/start'
 *                        carriageType:
 *                          $ref: '#/components/schemas/carriageType'
 *                        count:
 *                          $ref: '#/components/schemas/count'
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Invalid parameters
 *     tags:
 *       - Statistics
 */
router.get('/scsCarriageTypes', [
    query('last').optional().custom(validators.isTimePeriod),
    query('interval').optional().custom(validators.isTimeInterval),
    query('limit').optional().isInt({ min: 1 }).toInt(),
    query('offset').optional().isInt({ min: 0 }).toInt(),
    query('carriageType').optional().toArray(),
    query('user').optional().toArray(),
    query('fnn').optional().toArray(),
    query('rule').optional().toArray(),
    query('ruleStatus').optional().toArray(),
    query('ruleResult').optional().toArray(),
    query('ruleTriggerSource').optional().toArray(),
    query('source').optional().toArray(),
    query('sourceStatus').optional().toArray(),
    query('order').optional().isIn(['asc', 'desc'])
        .withMessage('Order must be either "asc" or "desc"')
], async function (req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }

    try {
        res.send(await getServiceCheckStatisticsByField(req, 'carriageType', '$carriageType'));
    } catch(error) {
        res.status(500).send({ error: error.message });
    }
});


/**
 * @swagger
 * /stats/scsFnn:
 *   get:
 *     summary: Get the number of service checks grouped by FNN
 *     parameters:
 *       - $ref: '#/components/parameters/last'
 *       - $ref: '#/components/parameters/interval'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/offset'
 *       - $ref: '#/components/parameters/carriageTypeFilter'
 *       - $ref: '#/components/parameters/userFilter'
 *       - $ref: '#/components/parameters/fnnFilter'
 *       - $ref: '#/components/parameters/ruleFilter'
 *       - $ref: '#/components/parameters/ruleStatusFilter'
 *       - $ref: '#/components/parameters/ruleResultFilter'
 *       - $ref: '#/components/parameters/ruleTriggerSourceFilter'
 *       - $ref: '#/components/parameters/sourceFilter'
 *       - $ref: '#/components/parameters/sourceStatusFilter'
 *       - $ref: '#/components/parameters/order'
 *     security:
 *       - MergeToken: []
 *     responses:
 *       200:
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *                type: object
 *                properties:
 *                  results:
 *                    type: array
 *                    items:
 *                      properties:
 *                        start:
 *                          $ref: '#/components/schemas/start'
 *                        fnn:
 *                          $ref: '#/components/schemas/fnn'
 *                        count:
 *                          $ref: '#/components/schemas/count'
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Invalid parameters
 *     tags:
 *       - Statistics
 */
router.get('/scsFnn', [
    query('last').optional().custom(validators.isTimePeriod),
    query('interval').optional().custom(validators.isTimeInterval),
    query('limit').optional().isInt({ min: 1 }).toInt(),
    query('offset').optional().isInt({ min: 0 }).toInt(),
    query('carriageType').optional().toArray(),
    query('user').optional().toArray(),
    query('fnn').optional().toArray(),
    query('rule').optional().toArray(),
    query('ruleStatus').optional().toArray(),
    query('ruleResult').optional().toArray(),
    query('ruleTriggerSource').optional().toArray(),
    query('source').optional().toArray(),
    query('sourceStatus').optional().toArray(),
    query('order').optional().isIn(['asc', 'desc'])
        .withMessage('Order must be either "asc" or "desc"')
], async function (req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }

    try {
        res.send(await getServiceCheckStatisticsByField(req, 'fnn', '$fnn'));
    } catch(error) {
        res.status(500).send({ error: error.message });
    }
});


/**
 * @swagger
 * /stats/scsTotal:
 *   get:
 *     summary: Get the total number of service checks in a time period
 *     parameters:
 *       - $ref: '#/components/parameters/last'
 *       - $ref: '#/components/parameters/interval'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/offset'
 *       - $ref: '#/components/parameters/carriageTypeFilter'
 *       - $ref: '#/components/parameters/userFilter'
 *       - $ref: '#/components/parameters/fnnFilter'
 *       - $ref: '#/components/parameters/ruleFilter'
 *       - $ref: '#/components/parameters/ruleStatusFilter'
 *       - $ref: '#/components/parameters/ruleResultFilter'
 *       - $ref: '#/components/parameters/ruleTriggerSourceFilter'
 *       - $ref: '#/components/parameters/sourceFilter'
 *       - $ref: '#/components/parameters/sourceStatusFilter'
 *       - $ref: '#/components/parameters/order'
 *     security:
 *       - MergeToken: []
 *     responses:
 *       200:
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *                type: object
 *                properties:
 *                  results:
 *                    type: array
 *                    items:
 *                      properties:
 *                        start:
 *                          $ref: '#/components/schemas/start'
 *                        fnn:
 *                          $ref: '#/components/schemas/fnn'
 *                        count:
 *                          $ref: '#/components/schemas/count'
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Invalid parameters
 *     tags:
 *       - Statistics
 */
 router.get('/scsTotal', [
    query('last').optional().custom(validators.isTimePeriod),
    query('interval').optional().custom(validators.isTimeInterval),
    query('limit').optional().isInt({ min: 1 }).toInt(),
    query('offset').optional().isInt({ min: 0 }).toInt(),
    query('carriageType').optional().toArray(),
    query('user').optional().toArray(),
    query('fnn').optional().toArray(),
    query('rule').optional().toArray(),
    query('ruleStatus').optional().toArray(),
    query('ruleResult').optional().toArray(),
    query('ruleTriggerSource').optional().toArray(),
    query('source').optional().toArray(),
    query('sourceStatus').optional().toArray(),
    query('order').optional().isIn(['asc', 'desc'])
        .withMessage('Order must be either "asc" or "desc"')
], async function (req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }

    try {
        res.send(await getServiceCheckStatisticsByField(req, null, null));
    } catch(error) {
        res.status(500).send({ error: error.message });
    }
});


/**
 * @swagger
 * /stats/scsRules:
 *   get:
 *     summary: Get the number of service checks grouped by rules
 *     parameters:
 *       - $ref: '#/components/parameters/last'
 *       - $ref: '#/components/parameters/interval'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/offset'
 *       - $ref: '#/components/parameters/carriageTypeFilter'
 *       - $ref: '#/components/parameters/userFilter'
 *       - $ref: '#/components/parameters/fnnFilter'
 *       - $ref: '#/components/parameters/ruleFilter'
 *       - $ref: '#/components/parameters/ruleStatusFilter'
 *       - $ref: '#/components/parameters/ruleResultFilter'
 *       - $ref: '#/components/parameters/ruleTriggerSourceFilter'
 *       - $ref: '#/components/parameters/sourceFilter'
 *       - $ref: '#/components/parameters/sourceStatusFilter'
 *       - $ref: '#/components/parameters/order'
 *     security:
 *       - MergeToken: []
 *     responses:
 *       200:
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *                type: object
 *                properties:
 *                  results:
 *                    type: array
 *                    items:
 *                      properties:
 *                        start:
 *                          $ref: '#/components/schemas/start'
 *                        rule:
 *                          $ref: '#/components/schemas/rule'
 *                        count:
 *                          $ref: '#/components/schemas/count'
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Invalid parameters
 *     tags:
 *       - Statistics
 */
router.get('/scsRules', [
    query('last').optional().custom(validators.isTimePeriod),
    query('interval').optional().custom(validators.isTimeInterval),
    query('limit').optional().isInt({ min: 1 }).toInt(),
    query('offset').optional().isInt({ min: 0 }).toInt(),
    query('carriageType').optional().toArray(),
    query('user').optional().toArray(),
    query('fnn').optional().toArray(),
    query('rule').optional().toArray(),
    query('ruleStatus').optional().toArray(),
    query('ruleResult').optional().toArray(),
    query('ruleTriggerSource').optional().toArray(),
    query('source').optional().toArray(),
    query('sourceStatus').optional().toArray(),
    query('order').optional().isIn(['asc', 'desc'])
        .withMessage('Order must be either "asc" or "desc"')
], async function (req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }

    try {
        res.send(await getServiceCheckStatisticsByNestedField(req, 'rule', '$rulesData'));
    } catch(error) {
        res.status(500).send({ error: error.message });
    }
});


/**
 * @swagger
 * /stats/scsSources:
 *   get:
 *     summary: Get the number of service checks grouped by sources
 *     parameters:
 *       - $ref: '#/components/parameters/last'
 *       - $ref: '#/components/parameters/interval'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/offset'
 *       - $ref: '#/components/parameters/carriageTypeFilter'
 *       - $ref: '#/components/parameters/userFilter'
 *       - $ref: '#/components/parameters/fnnFilter'
 *       - $ref: '#/components/parameters/ruleFilter'
 *       - $ref: '#/components/parameters/ruleStatusFilter'
 *       - $ref: '#/components/parameters/ruleResultFilter'
 *       - $ref: '#/components/parameters/ruleTriggerSourceFilter'
 *       - $ref: '#/components/parameters/sourceFilter'
 *       - $ref: '#/components/parameters/sourceStatusFilter'
 *       - $ref: '#/components/parameters/order'
 *     security:
 *       - MergeToken: []
 *     responses:
 *       200:
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *                type: object
 *                properties:
 *                  results:
 *                    type: array
 *                    items:
 *                      properties:
 *                        start:
 *                          $ref: '#/components/schemas/start'
 *                        source:
 *                          $ref: '#/components/schemas/source'
 *                        count:
 *                          $ref: '#/components/schemas/count'
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Invalid parameters
 *     tags:
 *       - Statistics
 */
router.get('/scsSources', [
    query('last').optional().custom(validators.isTimePeriod),
    query('interval').optional().custom(validators.isTimeInterval),
    query('limit').optional().isInt({ min: 1 }).toInt(),
    query('offset').optional().isInt({ min: 0 }).toInt(),
    query('carriageType').optional().toArray(),
    query('user').optional().toArray(),
    query('fnn').optional().toArray(),
    query('rule').optional().toArray(),
    query('ruleStatus').optional().toArray(),
    query('ruleResult').optional().toArray(),
    query('ruleTriggerSource').optional().toArray(),
    query('source').optional().toArray(),
    query('sourceStatus').optional().toArray(),
    query('order').optional().isIn(['asc', 'desc'])
        .withMessage('Order must be either "asc" or "desc"')
], async function (req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }

    try {
        res.send(await getServiceCheckStatisticsByNestedField(req, 'source', '$sourcesMetadata'));
    } catch(error) {
        res.status(500).send({ error: error.message });
    }
});


/**
 * @swagger
 * /stats/scsLastCheckPerUser:
 *   get:
 *     summary: Get the last time users made a service check
 *     security:
 *       - MergeToken: []
 *     responses:
 *       200:
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *                title: Last Service Check List
 *                type: array
 *                items:
 *                  properties:
 *                    user:
 *                      $ref: '#/components/schemas/user'
 *                    lastServiceCheck:
 *                      $ref: '#/components/schemas/lastServiceCheck'
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Invalid parameters
 *     tags:
 *       - Statistics
 */
router.get('/scsLastCheckPerUser', [
    query('last').default('1m').custom(validators.isTimePeriod),
    query('limit').default(100).isInt({ min: 1 }).toInt(),
    query('offset').default(0).isInt({ min: 0 }).toInt()
], async function (req, res) {
    try {
        res.send(await getScsLastCheckPerUser(req));
    } catch(error) {
        res.status(500).send({ error: error.message });
    }
});


/**
 * @swagger
 * /stats/aaaToolUsers:
 *   get:
 *     summary: Get the number of AAA tool queries grouped by user
 *     parameters:
 *       - $ref: '#/components/parameters/last'
 *       - $ref: '#/components/parameters/interval'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/offset'
 *       - $ref: '#/components/parameters/userFilter'
 *     security:
 *       - MergeToken: []
 *     responses:
 *       200:
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *                title: AAA tool statistics
 *                type: object
 *                properties:
 *                  metadata:
 *                    type: object
 *                    properties:
 *                      pagination:
 *                        "$ref": '#/components/schemas/pagination'
 *                  results:
 *                    type: array
 *                    items:
 *                      properties:
 *                        start:
 *                          $ref: '#/components/schemas/start'
 *                        user:
 *                          $ref: '#/components/schemas/user'
 *                        count:
 *                          $ref: '#/components/schemas/count'
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Invalid parameters
 *     tags:
 *       - Statistics
 */
router.get('/aaaToolUsers', [
    query('last').optional().custom(validators.isTimePeriod),
    query('interval').optional().custom(validators.isTimeInterval),
    query('limit').optional().isInt({ min: 1 }).toInt(),
    query('offset').optional().isInt({ min: 0 }).toInt(),
    query('user').optional().toArray()
], function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }

    let limit = (req.query.limit === undefined) ? 100 : req.query.limit;
    let offset = (req.query.offset === undefined) ? 0 : req.query.offset;
    let last = (req.query.last === undefined) ? "1d" : req.query.last;
    let interval = req.query.interval;
    let userFilter = (req.query.user === undefined) ? [] : req.query.user;

    let startDate = calculateStartDate(last);

    statistics.getAAAUsage(startDate, interval, limit, offset, userFilter, "user", function(err, results, total) {
        if (err) {
            logger.error(err);
            res.sendStatus(500);
            return;
        }

        let paginationMetadata = pagination.createPaginationMetadata(req, limit, offset, total);

        let response = {
            metadata: {
                pagination: paginationMetadata
            },
            results: results
        };

        res.send(response);
    });
});


/**
 * @swagger
 * /stats/aaaToolTotal:
 *   get:
 *     summary: Get the total number of AAA tool queries in a time period
 *     parameters:
 *       - $ref: '#/components/parameters/last'
 *       - $ref: '#/components/parameters/interval'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/offset'
 *       - $ref: '#/components/parameters/userFilter'
 *     security:
 *       - MergeToken: []
 *     responses:
 *       200:
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *                title: AAA tool statistics
 *                type: object
 *                properties:
 *                  metadata:
 *                    type: object
 *                    properties:
 *                      pagination:
 *                        "$ref": '#/components/schemas/pagination'
 *                  results:
 *                    type: array
 *                    items:
 *                      properties:
 *                        start:
 *                          $ref: '#/components/schemas/start'
 *                        user:
 *                          $ref: '#/components/schemas/user'
 *                        count:
 *                          $ref: '#/components/schemas/count'
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Invalid parameters
 *     tags:
 *       - Statistics
 */
router.get('/aaaToolTotal', [
    query('last').optional().custom(validators.isTimePeriod),
    query('interval').optional().custom(validators.isTimeInterval),
    query('limit').optional().isInt({ min: 1 }).toInt(),
    query('offset').optional().isInt({ min: 0 }).toInt(),
    query('user').optional().toArray()
], function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }

    let limit = (req.query.limit === undefined) ? 100 : req.query.limit;
    let offset = (req.query.offset === undefined) ? 0 : req.query.offset;
    let last = (req.query.last === undefined) ? "1d" : req.query.last;
    let interval = req.query.interval;
    let userFilter = (req.query.user === undefined) ? [] : req.query.user;

    let startDate = calculateStartDate(last);

    statistics.getAAAUsage(startDate, interval, limit, offset, userFilter, null, function(err, results, total) {
        if (err) {
            logger.error(err);
            res.sendStatus(500);
            return;
        }

        let paginationMetadata = pagination.createPaginationMetadata(req, limit, offset, total);

        let response = {
            metadata: {
                pagination: paginationMetadata
            },
            results: results
        };

        res.send(response);
    });
});


router.get('/qcsToday', async function (req, res) {
    try {
        res.send(await getQcsToday());
    } catch (error) {
        logger.error(`Error getting quick commands (today), ${error.toString()}`);
        res.sendStatus(500);
    }
});

router.get('/qcsTopUsers', async function (req, res) {
    try {
        res.send(await getQcsTopUsers());
    } catch (error) {
        logger.error(`Error getting quick commands top users, ${error.toString()}`);
        res.sendStatus(500);
    }
});

router.get('/qcsWeek', async function (req, res) {
    try {
        res.send(await getQcsWeekly());
    } catch (error) {
        logger.error(`Error getting quick commands (week), ${error.toString()}`);
        res.sendStatus(500);
    }
});

router.get('/qcsMonth', async function (req, res) {
    try {
        res.send(await getQcsMonthly());
    } catch (error) {
        logger.error(`Error getting quick commands (month), ${error.toString()}`);
        res.sendStatus(500);
    }
});


function calculateAggregateDateProject(interval) {
    // let dateCalculation;
    let aggregateVariables;

    switch (interval) {
        case "h":
            // Gets the week number and year for the service check created date in
            // the current timezone
            aggregateVariables = {
                year: {
                    "$year": { date: "$createdOn", timezone: TIME_ZONE }
                },
                month: {
                    "$month": { date: "$createdOn", timezone: TIME_ZONE }
                },
                day: {
                    "$dayOfMonth": { date: "$createdOn", timezone: TIME_ZONE }
                },
                hour: {
                    "$hour": { date: "$createdOn", timezone: TIME_ZONE }
                }
            };
            break;
        case "d":
            // Gets the day / month / year for when the service check was created in
            // the current timezone
            aggregateVariables = {
                year: {
                    "$year": { date: "$createdOn", timezone: TIME_ZONE }
                },
                month: {
                    "$month": { date: "$createdOn", timezone: TIME_ZONE }
                },
                day: {
                    "$dayOfMonth": { date: "$createdOn", timezone: TIME_ZONE }
                },
                hour: 0
            }
            break;
        case "w":
            // Gets the week number and year for the service check created date in
            // the current timezone
            aggregateVariables = {
                year: {
                    "$isoWeekYear": { date: "$createdOn", timezone: TIME_ZONE }
                },
                week: {
                    "$isoWeek": { date: "$createdOn", timezone: TIME_ZONE }
                }
            };
            break;
        case "m":
            // Gets the month / year for when the service check was created in
            // the current timezone
            aggregateVariables = {
                year: {
                    "$year": { date: "$createdOn", timezone: TIME_ZONE }
                },
                month: {
                    "$month": { date: "$createdOn", timezone: TIME_ZONE }
                },
                day: 1,
                hour: 0
            };
            break;
        case "y":
            // Gets the year for when the service check was created in
            // the current timezone
            aggregateVariables = {
                year: {
                    "$year": { date: "$createdOn", timezone: TIME_ZONE }
                },
                month: 1,
                day: 1,
                hour: 0
            };
            break;
    }

    let project;
    if (interval !== "w") {
        project = {
            "start": {
                "$let": {
                    "vars": aggregateVariables,
                    "in": {
                        "$dateFromParts": {
                            year: "$$year",
                            month: "$$month",
                            day: "$$day",
                            hour: "$$hour",
                            timezone: TIME_ZONE
                        }
                    }
                }
            }
        };
    } else {
        project = {
            "start": {
                "$let": {
                    "vars": aggregateVariables,
                    "in": {
                        "$dateFromParts": {
                            isoWeekYear: "$$year",
                            isoWeek: "$$week",
                            isoDayOfWeek: 1,
                            timezone: TIME_ZONE
                        }
                    }
                }
            }
        };
    }

    return project;
}


function getServiceCheckFilters(filters) {
    let serviceCheckFilters = [];
    let ruleFilters = [];
    let sourceFilters = [];

    if (filters.carriageType.length > 0) {
        serviceCheckFilters.push({ carriageType: { $in: filters.carriageType }});
    }

    if (filters.user.length > 0) {
        serviceCheckFilters.push({ createdBy: { $in: filters.user }});
    }

    if (filters.fnn.length > 0) {
        let filterFnnList = [];

        filters.fnn.forEach(fnn => {
            if (isValidPhoneNumber(fnn, "AU")) {
                fnn = parsePhoneNumber(fnn, "AU").format("E.164");
            }
            filterFnnList.push(fnn);
        })

        serviceCheckFilters.push({ fnn: { $in: filterFnnList }});
    }

    filters.rules.forEach(rule => {
        let ruleFilter = [{['rulesData.' + rule]: {$exists: true}}];

        if (filters.ruleStatus.length > 0) {
            ruleFilter.push({['rulesData.' + rule + '.status']: { $in: filters.ruleStatus }});
        }

        if (filters.ruleResult.length > 0) {
            ruleFilter.push({['rulesData.' + rule + '.result']: { $in: filters.ruleResult }});
        }

        if (filters.ruleTriggerSource.length > 0) {
            ruleFilter.push({['rulesData.' + rule + '.TrgSource']: { $in: filters.ruleTriggerSource }});
        }

        ruleFilters.push({ $and: ruleFilter });
    });

    filters.sources.forEach(source => {
        let sourceFilter = [{['sourcesMetadata.' + source]: { $exists: true }}];

        if (filters.sourceStatus.length > 0) {
            sourceFilter.push({['sourcesMetadata.' + source + '.status']: { $in: filters.sourceStatus }});
        }

        sourceFilters.push({ $and: sourceFilter });
    });

    // Add all the rule and source filter groups as a logical OR between each group
    if (ruleFilters.length > 0) {
        serviceCheckFilters.push({ $or: ruleFilters });
    }

    if (sourceFilters.length > 0) {
        serviceCheckFilters.push({ $or: sourceFilters });
    }

    return serviceCheckFilters;
}


export default router;
