'use strict';

import express from 'express';
import fs from 'fs';
import { parse } from 'marked';

import auth from '../modules/auth.js';
import { AuthorizationRoles } from '../modules/enumerations.js';
import logger from '../modules/logger.js';

const router = express.Router();

const changelogHeader = 'All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.';
let changelogHtml = null;

try {
    let changelogMarkdown = fs.readFileSync('./CHANGELOG.md').toString()
    changelogHtml = parse(changelogMarkdown.replace(changelogHeader, ''));
} catch(error) {
    logger.error(`Error reading changelog file, ${error.toString()}`);
}


router.get('/', auth.authorizeForRoles([
    AuthorizationRoles.readOnly,
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), function(req, res, next) {
    if (changelogHtml === null) {
        next(new Error('Error reading changelog'));
        return;
    }

    res.render('changelog', { title: 'Changelog', user: req.user, changelog: changelogHtml });
});


export default router;
