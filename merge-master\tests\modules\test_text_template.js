'use strict';

import mongoose from 'mongoose';
import chai from 'chai';
import chaiAsPromised from 'chai-as-promised';
import _ from 'lodash';

import '../set_config.js';
import Template from '../../db/model/template.js';
import textTemplate from '../../modules/textTemplate.js';
import { NotFoundError } from '../../modules/error.js';

const should = chai.should();
chai.use(chaiAsPromised);


describe('renderTemplateByName()', () => {
    beforeEach(async function() {
        // Uses deleteMany instead of mongoose.connection.db.dropDatabase() because
        // custom indexes appear to be deleted if dropDatabase() is used
        for (let collectionName in mongoose.connection.collections) {
            await mongoose.connection.collections[collectionName].deleteMany({});
        }
    });

    it('Empty parameters', async() => {
        await chai.expect(textTemplate.renderTemplateByName(null, null, null)).to.be.rejectedWith(NotFoundError);
    });

    it('Non-existant template name', async() => {
        await chai.expect(textTemplate.renderTemplateByName(null, 'TemplateName', null)).to.be.rejectedWith(NotFoundError);
    });

    it('Inactive template', async() => {
        await new Template({
            name: 'TemplateName',
            active: false,
            title: '',
            description: '',
            suite: [],
            templateType: 'Service Check',
            formatType: 'Text',
            listCondition: '',
            defaultPriority: 0,
            template: ''
        }).save();

        await chai.expect(textTemplate.renderTemplateByName(null, 'TemplateName', null)).to.be.rejectedWith(NotFoundError);
    });

    it('Active template, null service check record', async() => {
        await new Template({
            name: 'TemplateName',
            active: true,
            title: '',
            description: '',
            suite: [],
            templateType: 'Service Check',
            formatType: 'Text',
            listCondition: '',
            defaultPriority: 0,
            template: ''
        }).save();


        await chai.expect(textTemplate.renderTemplateByName(null, 'TemplateName', null)).to.be.rejectedWith(TypeError);
    });

    it('Active template, empty object service check record', async() => {
        await new Template({
            name: 'TemplateName',
            active: true,
            title: '',
            description: '',
            suite: [
                'standard'
            ],
            templateType: 'Service Check',
            formatType: 'Text',
            listCondition: '',
            defaultPriority: 0,
            template: ''
        }).save();

        chai.expect(await textTemplate.renderTemplateByName({}, 'TemplateName', null)).to.eql('');
    });

    it('Active template, service check record with rulesData and sourcesData field', async() => {
        await new Template({
            name: 'TemplateName',
            active: true,
            title: '',
            description: '',
            suite: [],
            templateType: 'Service Check',
            formatType: 'Text',
            listCondition: '',
            defaultPriority: 0,
            template: ''
        }).save();

        chai.expect(await textTemplate.renderTemplateByName({input: { suite: 'standard' }, sourcesData: {}, rulesData: {}}, 'TemplateName', null)).to.eql('');
    });

    it('Failed rendering of template (key of undefined field)', async() => {
        let serviceCheckRecord = {
            input: {
                suite: 'standard'
            },
            rulesData: {},
            sourcesData: {}
        };

        await new Template({
            name: 'TemplateName',
            active: true,
            title: '',
            description: '',
            suite: [
                'standard'
            ],
            templateType: 'Service Check',
            formatType: 'Text',
            listCondition: '',
            defaultPriority: 0,
            template: '<%= s.SourceName.data %>'
        }).save();

        await chai.expect(textTemplate.renderTemplateByName(serviceCheckRecord, 'TemplateName', false)).to.be.rejectedWith(Error);
    });

    it('Successful rendering of template', async() => {
        let serviceCheckRecord = {
            input: {
                suite: 'standard'
            },
            rulesData: {},
            sourcesData: {
                SourceName: {
                    data: 'My data here'
                }
            }
        };

        await new Template({
            name: 'TemplateName',
            active: true,
            title: '',
            description: '',
            suite: [
                'standard'
            ],
            templateType: 'Service Check',
            formatType: 'Text',
            listCondition: '',
            defaultPriority: 0,
            template: '<%= s.SourceName.data %>'
        }).save();

        chai.expect(await textTemplate.renderTemplateByName(serviceCheckRecord, 'TemplateName', false)).to.eql('My data here');
    });

    it('Successful rendering of template with module', async() => {
        let serviceCheckRecord = {
            input: {
                suite: 'standard'
            },
            rulesData: {},
            sourcesData: {
                SourceName: {
                    data: 'My data here'
                }
            }
        };

        await new Template({
            name: 'TemplateName',
            active: true,
            title: '',
            description: '',
            suite: [
                'standard'
            ],
            templateType: 'Service Check',
            formatType: 'Text',
            listCondition: '',
            defaultPriority: 0,
            template: '<%= s.SourceName.data; %>\n<%= renderTemplate("TemplateModule"); -%>'
        }).save();

        await new Template({
            name: 'TemplateModule',
            active: true,
            title: '',
            description: '',
            suite: [
                'standard'
            ],
            templateType: 'Module',
            formatType: 'Text',
            listCondition: '',
            defaultPriority: 0,
            template: 'Module text'
        }).save();

        chai.expect(await textTemplate.renderTemplateByName(serviceCheckRecord, 'TemplateName', false)).to.eql('My data here\nModule text');
    });

    it('Successful rendering of template with module with parameters', async() => {
        let serviceCheckRecord = {
            input: {
                suite: 'standard'
            },
            rulesData: {},
            sourcesData: {
                SourceName: {
                    data: 'My data here'
                }
            }
        };

        await new Template({
            name: 'TemplateName',
            active: true,
            title: '',
            description: '',
            suite: [
                'standard'
            ],
            templateType: 'Service Check',
            formatType: 'Text',
            listCondition: '',
            defaultPriority: 0,
            template: '<%= s.SourceName.data; %>\n<%= renderTemplate("TemplateModule", { paramName: "Parameter Text" }); -%>'
        }).save();

        await new Template({
            name: 'TemplateModule',
            active: true,
            title: '',
            description: '',
            suite: [
                'standard'
            ],
            templateType: 'Module',
            formatType: 'Text',
            listCondition: '',
            defaultPriority: 0,
            template: '<%= paramName; %>'
        }).save();

        chai.expect(await textTemplate.renderTemplateByName(serviceCheckRecord, 'TemplateName', false)).to.eql('My data here\nParameter Text');
    });

    it('Failed rendering of template with module (module has incorrect templateType)', async() => {
        let serviceCheckRecord = {
            input: {
                suite: 'standard'
            },
            rulesData: {},
            sourcesData: {
                SourceName: {
                    data: 'My data here'
                }
            }
        };

        await new Template({
            name: 'TemplateName',
            active: true,
            title: '',
            description: '',
            suite: [
                'standard'
            ],
            templateType: 'Service Check',
            formatType: 'Text',
            listCondition: '',
            defaultPriority: 0,
            template: '<%= s.SourceName.data %>\n<%= renderTemplate("TemplateModule"); -%>'
        }).save();

        await new Template({
            name: 'TemplateModule',
            active: true,
            title: '',
            description: '',
            suite: [
                'standard'
            ],
            templateType: 'Service Check',
            formatType: 'Text',
            listCondition: '',
            defaultPriority: 0,
            template: 'Module text'
        }).save();

        await chai.expect(textTemplate.renderTemplateByName(serviceCheckRecord, 'TemplateName', false)).to.be.rejectedWith(Error);
    });

    it('Failed rendering of template with module (module is not active)', async() => {
        let serviceCheckRecord = {
            input: {
                suite: 'standard'
            },
            rulesData: {},
            sourcesData: {
                SourceName: {
                    data: 'My data here'
                }
            }
        };

        await new Template({
            name: 'TemplateName',
            active: true,
            title: '',
            description: '',
            suite: [
                'standard'
            ],
            templateType: 'Service Check',
            formatType: 'Text',
            listCondition: '',
            defaultPriority: 0,
            template: '<%= s.SourceName.data %>\n<%= renderTemplate("TemplateModule"); -%>'
        }).save();

        await new Template({
            name: 'TemplateModule',
            active: false,
            title: '',
            description: '',
            suite: [
                'standard'
            ],
            templateType: 'Module',
            formatType: 'Text',
            listCondition: '',
            defaultPriority: 0,
            template: 'Module text'
        }).save();

        await chai.expect(textTemplate.renderTemplateByName(serviceCheckRecord, 'TemplateName', false)).to.be.rejectedWith(Error);
    });

    it('Failed rendering of template with module (main template has incorrect templateType)', async() => {
        let serviceCheckRecord = {
            input: {
                suite: 'standard'
            },
            rulesData: {},
            sourcesData: {
                SourceName: {
                    data: 'My data here'
                }
            }
        };

        await new Template({
            name: 'TemplateName',
            active: true,
            title: '',
            description: '',
            suite: [
                'standard'
            ],
            templateType: 'Module',
            formatType: 'Text',
            listCondition: '',
            defaultPriority: 0,
            template: '<%= s.SourceName.data %>\n<%= renderTemplate("TemplateModule"); -%>'
        }).save();

        await new Template({
            name: 'TemplateModule',
            active: true,
            title: '',
            description: '',
            suite: [
                'standard'
            ],
            templateType: 'Module',
            formatType: 'Text',
            listCondition: '',
            defaultPriority: 0,
            template: 'Module text'
        }).save();

        await chai.expect(textTemplate.renderTemplateByName(serviceCheckRecord, 'TemplateName', false)).to.be.rejectedWith(Error);
    });

    it('showRulesData field passed to template test 1', async() => {
        let serviceCheckRecord = {
            input: {
                suite: 'standard'
            },
            rulesData: {},
            sourcesData: {
                SourceName: {
                    data: 'My data here'
                }
            }
        };

        await new Template({
            name: 'TemplateName',
            active: true,
            title: '',
            description: '',
            suite: [
                'standard'
            ],
            templateType: 'Service Check',
            formatType: 'Text',
            listCondition: '',
            defaultPriority: 0,
            template: '<%= showRulesData %>'
        }).save();

        chai.expect(await textTemplate.renderTemplateByName(serviceCheckRecord, 'TemplateName', false)).to.eql('false');
    });

    it('showRulesData field passed to template test 2', async() => {
        let serviceCheckRecord = {
            input: {
                suite: 'standard'
            },
            rulesData: {},
            sourcesData: {
                SourceName: {
                    data: 'My data here'
                }
            }
        };

        await new Template({
            name: 'TemplateName',
            active: true,
            title: '',
            description: '',
            suite: [
                'standard'
            ],
            templateType: 'Service Check',
            formatType: 'Text',
            listCondition: '',
            defaultPriority: 0,
            template: '<%= showRulesData %>'
        }).save();

        chai.expect(await textTemplate.renderTemplateByName(serviceCheckRecord, 'TemplateName', true)).to.eql('true');
    });

    it('Infinite loop in template', async() => {
        let serviceCheckRecord = {
            input: {
                suite: 'standard'
            },
            rulesData: {},
            sourcesData: {}
        };

        await new Template({
            name: 'TemplateName',
            active: true,
            title: '',
            description: '',
            suite: [
                'standard'
            ],
            templateType: 'Service Check',
            formatType: 'Text',
            listCondition: '',
            defaultPriority: 0,
            template: '<% while (true) { %><% } %>'
        }).save();

        await chai.expect(textTemplate.renderTemplateByName(serviceCheckRecord, 'TemplateName', false)).to.be.rejectedWith(Error, 'Template rendering terminated, timeout of 5000ms exceeded');
    }).timeout(8000);

    it('IPMANSummaryText template name with no user', async() => {
        await new Template({
            name: 'IPMANSummaryText',
            active: true,
            title: '',
            description: '',
            suite: [
                'standard'
            ],
            templateType: 'Service Check',
            formatType: 'Text',
            listCondition: '',
            defaultPriority: 0,
            template: 'IPMAN Summary Text Here'
        }).save();

        await chai.expect(textTemplate.renderTemplateByName(null, 'IPMANSummaryText', null)).to.be.rejectedWith(NotFoundError);
    });
});


describe('renderTemplate()', () => {
    beforeEach(async function() {
        // Uses deleteMany instead of mongoose.connection.db.dropDatabase() because
        // custom indexes appear to be deleted if dropDatabase() is used
        for (let collectionName in mongoose.connection.collections) {
            await mongoose.connection.collections[collectionName].deleteMany({});
        }
    });

    it('Empty parameters', async() => {
        await chai.expect(textTemplate.renderTemplate(null, null, null, null, null)).to.be.rejectedWith(Error);
    });

    it('Empty parameters, empty string template code', async() => {
        await chai.expect(textTemplate.renderTemplate(null, '', null, null, null)).to.be.rejectedWith(Error);
    });

    it('Empty object service check record, null template code', async() => {
        await chai.expect(textTemplate.renderTemplate({}, null, null, null, null)).to.be.rejectedWith(Error);
    });

    it('Empty object service check record, empty string template code', async() => {
        chai.expect(await textTemplate.renderTemplate({}, '', null, null, null)).to.eql('');
    });

    it('Service check record with rulesData and sourcesData fields, null template code', async() => {
        let serviceCheckRecord = {
            rulesData: {},
            sourcesData: {}
        };

        await new Template({
            name: 'TemplateName',
            active: true,
            title: '',
            description: '',
            suite: [],
            templateType: 'Service Check',
            formatType: 'Text',
            listCondition: '',
            defaultPriority: 0,
            template: ''
        }).save();

        await chai.expect(textTemplate.renderTemplate(serviceCheckRecord, null, null, null, null)).to.be.rejectedWith(Error);
    });

    it('Service check record with rulesData and sourcesData fields, empty string template code', async() => {
        let serviceCheckRecord = {
            rulesData: {},
            sourcesData: {}
        };

        await new Template({
            name: 'TemplateName',
            active: true,
            title: '',
            description: '',
            suite: [],
            templateType: 'Service Check',
            formatType: 'Text',
            listCondition: '',
            defaultPriority: 0,
            template: ''
        }).save();

        chai.expect(await textTemplate.renderTemplate(serviceCheckRecord, '', null, null, null)).to.eql('');
    });

    it('Failed rendering of template (key of undefined field)', async() => {
        let serviceCheckRecord = {
            rulesData: {},
            sourcesData: {}
        };
        let templateCode = '<%= s.SourceName.data %>';

        await chai.expect(textTemplate.renderTemplate(serviceCheckRecord, templateCode, null, false, false)).to.be.rejectedWith(Error);
    });

    it('Successful rendering of template', async() => {
        let serviceCheckRecord = {
            rulesData: {},
            sourcesData: {
                SourceName: {
                    data: 'My data here'
                }
            }
        };
        let templateCode = '<%= s.SourceName.data %>';

        chai.expect(await textTemplate.renderTemplate(serviceCheckRecord, templateCode, null, false, false)).to.eql('My data here');
    });

    it('Successful rendering of template with module (enableTemplateModules true)', async() => {
        await new Template({
            name: 'TestModule',
            active: true,
            title: 'Test Module',
            description: '',
            suite: [],
            templateType: 'Module',
            formatType: 'Text',
            listCondition: '',
            defaultPriority: 0,
            template: '<%= s.SourceName.data; -%>'
        }).save();

        let serviceCheckRecord = {
            rulesData: {},
            fnn: 'N1234567R',
            sourcesData: {
                SourceName: {
                    data: 'My data here'
                }
            }
        };
        let templateCode = '<%= data.fnn %>\n<%= renderTemplate("TestModule"); -%>';

        chai.expect(await textTemplate.renderTemplate(serviceCheckRecord, templateCode, null, false, true)).to.eql('N1234567R\nMy data here');
    });

    it('Successful rendering of template with module and parameters (enableTemplateModules true)', async() => {
        await new Template({
            name: 'TestModule',
            active: true,
            title: 'Test Module',
            description: '',
            suite: [],
            templateType: 'Module',
            formatType: 'Text',
            listCondition: '',
            defaultPriority: 0,
            template: '<%= myParam; -%>'
        }).save();

        let serviceCheckRecord = {
            rulesData: {},
            fnn: 'N1234567R',
            sourcesData: {
                SourceName: {
                    data: 'My data here'
                }
            }
        };
        let templateCode = '<%= data.fnn; %>\n<%= renderTemplate("TestModule", { myParam: "test string" }); -%>';

        chai.expect(await textTemplate.renderTemplate(serviceCheckRecord, templateCode, null, false, true)).to.eql('N1234567R\ntest string');
    });

    it('Failed rendering of template with parameters (enableTemplateModules true)', async() => {
        let serviceCheckRecord = {
            rulesData: {},
            fnn: 'N1234567R',
            sourcesData: {
                SourceName: {
                    data: 'My data here'
                }
            }
        };
        let templateCode = '<%= data.fnn; %>\n<%= paramName1; %>\n<%= paramName2; %>\n<%= paramName3; %>';

        await chai.expect(textTemplate.renderTemplate(serviceCheckRecord, templateCode, null, false, true, { paramName1: "A", paramName2: "B", paramName3: "C" })).to.be.rejectedWith(Error);
    });

    it('Failed rendering of template with module (enableTemplateModules false)', async() => {
        await new Template({
            name: 'TestModule',
            active: true,
            title: 'Test Module',
            description: '',
            suite: [],
            templateType: 'Module',
            formatType: 'Text',
            listCondition: '',
            defaultPriority: 0,
            template: '<%= s.SourceName.data; -%>'
        }).save();

        let serviceCheckRecord = {
            rulesData: {},
            fnn: 'N1234567R',
            sourcesData: {
                SourceName: {
                    data: 'My data here'
                }
            }
        };
        let templateCode = '<%= data.fnn; %>\n<%= renderTemplate("TestModule"); -%>';

        await chai.expect(textTemplate.renderTemplate(serviceCheckRecord, templateCode, null, false, false)).to.be.rejectedWith(Error);
    });

    it('Successful rendering of template with parameters (enableTemplateModules false)', async() => {
        let serviceCheckRecord = {
            rulesData: {},
            fnn: 'N1234567R',
            sourcesData: {
                SourceName: {
                    data: 'My data here'
                }
            }
        };
        let templateCode = '<%= data.fnn; %>\n<%= paramName1; %>\n<%= paramName2; %>\n<%= paramName3; %>';

        chai.expect(await textTemplate.renderTemplate(serviceCheckRecord, templateCode, null, false, false, { paramName1: "A", paramName2: "B", paramName3: "C" })).to.eql('N1234567R\nA\nB\nC');
    });

    it('showRulesData field passed to template test 1', async() => {
        let serviceCheckRecord = {
            rulesData: {},
            sourcesData: {
                SourceName: {
                    data: 'My data here'
                }
            }
        };

        let templateCode = '<%= showRulesData %>';

        chai.expect(await textTemplate.renderTemplate(serviceCheckRecord, templateCode, null, false, false)).to.eql('false');
    });

    it('showRulesData field passed to template test 2', async() => {
        let serviceCheckRecord = {
            rulesData: {},
            sourcesData: {
                SourceName: {
                    data: 'My data here'
                }
            }
        };

        let templateCode = '<%= showRulesData %>';

        chai.expect(await textTemplate.renderTemplate(serviceCheckRecord, templateCode, null, true, false)).to.eql('true');
    });

    it('Infinite loop in template', async() => {
        let serviceCheckRecord = {
            rulesData: {},
            sourcesData: {}
        };

        let templateCode = '<% while (true) { %><% } %>';

        await chai.expect(textTemplate.renderTemplate(serviceCheckRecord, templateCode, null, false, false)).to.be.rejectedWith(Error, 'Template rendering terminated, timeout of 5000ms exceeded');
    }).timeout(8000);
});
