import { SelectedTestPackage } from "../../../../infrastructure/models"

export const validateDateRange = (startDate: string, endDate: string): string => {
    const start = new Date(startDate)
    const end = new Date(endDate)
    const now = new Date()

    // Validate that both dates are valid
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return 'Invalid date(s) provided.'
    }

    // Ensure start date is strictly before end date
    if (start >= end) {
        return 'Start date must be earlier than end date.'
    }

    // Ensure the end date is not in the future
    if (end > now) {
        return 'End date cannot be in the future.'
    }

    // All validations pass
    return ''
}

export const allDatesValid = (selectedTestPackages: SelectedTestPackage[]): boolean => {
    return selectedTestPackages.filter(pkg => pkg.isSelected).every(pkg => {
      if (pkg.parameters?.startDate && pkg.parameters?.endDate) {
        return validateDateRange(pkg.parameters.startDate, pkg.parameters.endDate) === ''
      }
      return true
    })
  }
  