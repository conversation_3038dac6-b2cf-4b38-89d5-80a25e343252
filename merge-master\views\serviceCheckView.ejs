<%- include('header', {}); %>
<%- include('menu', {currentTab: 'Form'}); %>
<%- include('serviceCheckRuleHelpers'); %>
<link rel="stylesheet" type="text/css" href="/public/stylesheets/select2.min.css">
<script src="/public/javascripts/select2.min.js" crossorigin="anonymous"></script>
<script src="/public/javascripts/ace.js" crossorigin="anonymous"></script>
<script src="/public/javascripts/merge_SC_lib.js" crossorigin="anonymous"></script>
<script src="/public/javascripts/magpie-schematics.js" crossorigin="anonymous"></script>
<div class="container">
    <br>
    <div class="jumbotron py-2">
        <div class="form-group container">
            <div class="row">
                <div class="col-sm-6">
                    <h1 id="fnnHeader" class="display-4"></h1>
                </div>
                <div class="col-sm-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="" id="showOKRules" checked>
                        <label class="form-check-label" for="showOKRules">Show OK Rules</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="" id="showHiddenSources">
                        <label class="form-check-label" for="showHiddenSources">Show Hidden Sources</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="" id="showPreConditionFailedSources">
                        <label class="form-check-label" for="showPreConditionFailedSources">Show Pre-condition Failed Sources</label>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-4"></div>
            </div>
        </div>
    </div>
    <h5><a id="linkNewUi">New UI &rarr;</a></h5>
    <div class="modal fade" id="textTemplateModal" tabindex="-1" role="dialog" aria-labelledby="Title" aria-hidden="true">
        <div class="modal-dialog modal-xlg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <input type="hidden" id="textTemplateModal-ID" name="textTemplateModal-ID" value="">
                    <div class="row">
                        <div class="form-inline col-md-auto">
                            <h5 class="modal-title" id="textTemplateModal-Title"></h5>
                            <div class="form-group mr-2">
                                <label for="inputTemplateName" class="col-sm-auto col-form-label">Template:</label>
                                <select id="inputTemplateName" class="form-control" data-service-check-id=""></select>
                            </div>
                            <div class="form-group mr-2">
                                <span style="display:none;" id="templateListLoading" class="spinner-border spinner-border-sm"></span>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showRulesData"><label class="form-check-label" for="showRulesData">Show rules data</label>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="textTemplateModal-controls" class="form-row p-2" style="display:none;">
                        <span class="icons">
                            <button
                                class="btn btn-outline-secondary btn-sm copyToClipboardTemplate"
                                data-toggle="tooltip"
                                data-placement="top"
                                title="Copy to clipboard"
                            >
                                <span class="fa fa-copy"></span>
                            </button>
                            <button id="textTemplateModal-download" class="btn btn-dark btn-sm" title="Download template to text file">
                                <span class="fas fa-download"></span> Text
                            </button>
                        </span>
                    </div>
                    <pre id="textTemplateModal-text" style="display:none;" class="border"></pre>
                    <div id="textTemplateModal-alert" style="display:none;" class="alert alert-danger" role="alert">
                        <pre id="textTemplateModal-alertText"></pre>
                    </div>
                    <div id="textTemplateModal-emptyWarning-alert" style="display:none;" class="alert alert-warning" role="alert">
                        <pre>No relevant text templates found for this service check.</pre>
                    </div>
                </div>
                <div class="modal-footer"></div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="feedbackModal" tabindex="-1" role="dialog" aria-labelledby="Title" aria-hidden="true">
        <div class="modal-dialog modal-xlg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    Service Check Feedback
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    Please provide further details as to what can be improved for this service check
                    <textarea class="form-control" id="feedbackMessageInput" maxlength="4000"></textarea>
                    <small class="text-muted" id="feedbackMessageInputCharCount"></small><small class="text-muted"> characters remaining</small>
                </div>
                <div class="modal-footer">
                    <button id="feedbackRemove" type="button" class="btn btn-sm btn-danger">Remove feedback</button>
                    <button id="feedbackSend" type="button" class="btn btn-sm btn-primary">Send feedback</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="magpieSchematicsModal" tabindex="-1" role="dialog" aria-labelledby="Title" aria-hidden="true">
        <div class="modal-dialog modal-xlg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    Magpie Schematics
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div id="magpieSchematicModalBody" class="modal-body">
                    <div class="form-row">
                        <input type="range" class="custom-range col-4" id="magpieSchematicZoom" min="50" max="120" step="5"><label id="magpieSchematicZoomValue" for="magpieSchematicZoom" class="col-2">100%</label>
                    </div>
                    <div id="magpieSchematic" class="overflow-auto"></div>
                </div>
                <div class="modal-footer">
                </div>
            </div>
        </div>
    </div>

    <div id="allServiceCheckRecords">
        <div class='text-left border rounded'>
            <div class='row mb-m-10'>
                <div class='col-md-auto'>
                    <div class='row'>
                        <div class='col-md-auto'>
                            <span id='serviceCheckFnn' class='h2'></span>
                        </div>
                    </div>
                </div>
                <div class='col-md-auto'>
                    <div class='row'>
                        <div class='col-md-auto'>
                            Status: <b id='serviceCheckStatus'></b>,&nbsp;
                            Level: <span id="serviceCheckLevel"></span>,&nbsp;
                            Suite: <span id="serviceCheckSuite"></span>,&nbsp;
                            By: <span id="serviceCheckCreatedBy"></span>,&nbsp;
                            On: <span id="serviceCheckCreatedOn"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div id="serviceCheckRecordBlock"></div>
        </div>
    </div>
</div>
<script>
    const wikiBaseURL = "<%= global.gConfig.wikiBaseURL %>";
    const SCr = Object.freeze(JSON.parse(atob("<%- SCrEncoded %>")));
    const SCRules = Object.freeze(JSON.parse(atob("<%- SCRules %>")));
    const rules = Object.freeze(arrayToObject(SCRules.rules, 'name'));
    const sources = Object.freeze(arrayToObject(SCRules.sources , 'name'));
    const feedbackMessageCharacterLimit = 4000;
    const username = "<%= user.username %>";

    $(document).ready(function () {
        //Show and Hide Rules
        $('#showOKRules').change(function() {
            if (this.checked) {
                $('.rule-OK').fadeIn('fast');
                localStorage.setItem('showOKRules', true);
            } else {
                $('.rule-OK').fadeOut('fast');
                localStorage.setItem('showOKRules', false);
            }
        });

        $('#showHiddenSources,#showPreConditionFailedSources').change(function() {
            let showHiddenSources = $('#showHiddenSources').prop('checked');
            let showPreConditionFailedSources = $('#showPreConditionFailedSources').prop('checked');

            if (showHiddenSources && showPreConditionFailedSources) {
                $('.sourceHideInResult,.sourcePreConditionFailed').fadeIn('fast');
            } else if (showHiddenSources && !showPreConditionFailedSources) {
                $('.sourceHideInResult').not('.sourcePreConditionFailed').fadeIn('fast');
                $('.sourcePreConditionFailed').fadeOut('fast');
            } else if (!showHiddenSources && showPreConditionFailedSources) {
                $('.sourcePreConditionFailed').not('.sourceHideInResult').fadeIn('fast');
                $('.sourceHideInResult').fadeOut('fast');
            } else {
                $('.sourceHideInResult,.sourcePreConditionFailed').fadeOut('fast');
            }
        });

        $('#showHiddenSources').change(function() {
            if (this.checked) {
                localStorage.setItem('showHiddenSources', true);
            } else {
                localStorage.setItem('showHiddenSources', false);
            }
        });

        $('#showPreConditionFailedSources').change(function() {
            if (this.checked) {
                localStorage.setItem('showPreConditionFailedSources', true);
            } else {
                localStorage.setItem('showPreConditionFailedSources', false);
            }
        });

        if (localStorage.getItem('showOKRules') != null) {
            $('#showOKRules').prop('checked', JSON.parse(localStorage.getItem('showOKRules')));
            $('#showOKRules').trigger('change');
        }

        if (localStorage.getItem('showHiddenSources') != null) {
            $('#showHiddenSources').prop('checked', JSON.parse(localStorage.getItem('showHiddenSources')));
            $('#showHiddenSources').trigger('change');
        }

        if (localStorage.getItem('showPreConditionFailedSources') != null) {
            $('#showPreConditionFailedSources').prop('checked', JSON.parse(localStorage.getItem('showPreConditionFailedSources')));
            $('#showPreConditionFailedSources').trigger('change');
        }

        $('#fnnHeader').text(`History ${SCr.fnn}`);
        $('#serviceCheckFnn').text(SCr.fnn);
        $('#serviceCheckStatus').text(SCr.status);
        $('#serviceCheckLevel').text(SCr.input.level);

        $('#serviceCheckCreatedBy').text(SCr.createdBy);
        $('#serviceCheckCreatedOn').text(new Date(SCr.createdOn).toLocaleString('en-GB', { hour12: false }));


        addServiceCheckRecord(SCr, $('#serviceCheckRecordBlock'));

        if (Array.isArray(SCr.generatedServiceChecks)) {
            for (let generatedSCr of SCr.generatedServiceChecks) {
                addServiceCheckRecord(generatedSCr, $('#allServiceCheckRecords'), SCr.id);
            }
        }

        $("#inputTemplateName,#showRulesData").on('change', function() {
            let showRulesData = $("#showRulesData").prop("checked");
            let templateName = $('#inputTemplateName').val();
            let serviceCheckId = $('#inputTemplateName').attr('data-service-check-id');

            if (!templateName) {
                alert("Template not selected");
                return;
            }

            $.ajax({
                cache: false,
                type: "GET",
                url: `/serviceCheck/${encodeURIComponent(serviceCheckId)}/renderTemplate`,
                data: {
                    name: templateName,
                    showRulesData: showRulesData
                },
                dataType: "text",
                success: function (response) {
                    console.log('Template rendered is: ', response);
                    $("#textTemplateModal-alert").hide();

                    $("#textTemplateModal-controls").show();
                    $("#textTemplateModal-text").text(response);
                    $("#textTemplateModal-text").show();
                },
                error: function (err) {
                    $("#textTemplateModal-alertText").text(err.responseText);
                    $("#textTemplateModal-alert").show();

                    $("#textTemplateModal-controls").hide();
                    $("#textTemplateModal-text").hide();
                }
            });
        });

        $("#feedbackMessageInputCharCount").text(`${feedbackMessageCharacterLimit - $("#feedbackMessageInput").val().length}/${feedbackMessageCharacterLimit}`);

        $("#feedbackMessageInput").bind('input propertychange', function() {
            $("#feedbackMessageInputCharCount").text(`${feedbackMessageCharacterLimit - $("#feedbackMessageInput").val().length}/${feedbackMessageCharacterLimit}`);
        });

        $("#textTemplateModal-download").click(function() {
            const templateText = $("#textTemplateModal-text").text();
            const templateName = $("#inputTemplateName").val().replace(/[^a-z0-9]/gi, '_');
            const dateString = new Date().toISOString().replace(/[^0-9]/g, '');
            const fileName = `Text_Template_${templateName}_${dateString}.txt`;

            downloadToFile(fileName, templateText);
        });

        $("#magpieSchematicZoom").change(function() {
            $("#magpieSchematicZoomValue").text(`${$("#magpieSchematicZoom").val()}%`);
            const scaleValue = parseInt($("#magpieSchematicZoom").val()) / 100;
            $("#magpieSchematic svg").css('transform', `scale(${scaleValue})`);
            $("#magpieSchematic svg").css('transform-origin', "0 0");
        });

        $("#magpieSchematicZoom").val(100);

        $("#magpieSchematicModalBody").on("wheel", function(event) {
            let currVal = parseInt($("#magpieSchematicZoom").val());
            if (event?.originalEvent?.deltaY < 0) {
                $("#magpieSchematicZoom").val(currVal + 5);
            } else if (event?.originalEvent?.deltaY > 0) {
                $("#magpieSchematicZoom").val(currVal - 5);
            }
            $("#magpieSchematicZoom").trigger("change");
        });
    });

    function addServiceCheckRecord(serviceCheckRecord, baseElement, originServiceCheckId=null) {
        let id = serviceCheckRecord.id;

        let serviceCheckRecordJson;
        try {
            serviceCheckRecordJson = JSON.stringify(serviceCheckRecord, null, 4);
        } catch(error) {
            console.error(`Error converting service check record to JSON format ${error.toString()}`);
            serviceCheckRecordJson = 'Error: cannot be displayed';
        }

        let fullRecordSection = $("<div>").addClass("border border-info rounded").append(
            $("<span>").addClass("h4").text("Full Record")
        ).append(
            $("<a>").addClass("expand btn").attr("data-toggle", "collapse").attr("data-target", `#fullRecord-${id}`).html('<span style="color:blue" class="fas fa-plus-square" title="Expand/Collapse"></span>')
        ).append($("<div>").attr("id", `fullRecord-${id}`).addClass("collapse p-2").append(
            $("<button>").addClass("btn btn-outline-secondary btn-sm copyToClipboardRecord mt-2 mb-2")
                .attr("data-id", id)
                .attr("data-toggle", "tooltip")
                .attr("data-placement", "top")
                .attr("title", "Copy to clipboard").html("<span class=\"fa fa-copy\"></span>")
            ).append($("<pre>").addClass("border border-secondary rounded").text(serviceCheckRecordJson))
        );

        if (!originServiceCheckId) {
            baseElement.append(SCResultsTemplate(id, serviceCheckRecord.fnn));
            baseElement.append(fullRecordSection);
        } else {
            baseElement.append(generatedServiceCheckCard(id, SCr.fnn));
            $(`#serviceCheckRecordBlock-${id}`).append(fullRecordSection);

            // Removes copy links, templates, restart service check icons from generated service checks
            $(`#pills-dicon-${id}`).addClass("d-none");
        }

        addSCBlock('Summary', serviceCheckRecord.id);

        $(`#expandAllSources-${id}`).click(function() {
            $(`.source-expand-${id}`).each(function() {
                let dataTarget = $(this).attr('data-target');

                if (dataTarget && !$(dataTarget).hasClass('show')) {
                    $(this).click();
                }
            });
        });

        $(`#collapseAllSources-${id}`).click(function() {
            $(`.source-expand-${id}`).each(function() {
                let dataTarget = $(this).attr('data-target');

                if (dataTarget && $(dataTarget).hasClass('show')) {
                    $(this).click();
                }
            });
        });

        $(`#search-rules-text-${id}`).keyup(function(e) {
            // Enter key
            if (e.keyCode === 13) {
                filterRulesByText(id, serviceCheckRecord.rulesData, $(`#search-rules-text-${id}`).val());
            }
        });

        $(`#search-rules-clear-${id}`).click(function() {
            $(`#search-rules-text-${id}`).val("");
            filterRulesByText(id, serviceCheckRecord.rulesData, $(`#search-rules-text-${id}`).val());
        });

        $(`#search-rules-${id}`).click(function() {
            filterRulesByText(id, serviceCheckRecord.rulesData, $(`#search-rules-text-${id}`).val());
        });

        updateHeaderInfo(serviceCheckRecord.id, 'header-top', 'FNN', serviceCheckRecord.fnn);
        updateHeaderInfo(serviceCheckRecord.id, 'header', 'Carriage FNN', serviceCheckRecord.carriageFNN);
        updateHeaderInfo(serviceCheckRecord.id, 'header', 'Device Name', serviceCheckRecord.deviceName);
        updateHeaderInfo(serviceCheckRecord.id, 'header', 'CIDN', serviceCheckRecord.CIDN);
        updateHeaderInfo(serviceCheckRecord.id, 'header', 'Carriage Type', serviceCheckRecord.carriageType);

        if (serviceCheckRecord.rulesData && serviceCheckRecord.rulesData.MDR012 && serviceCheckRecord.rulesData.MDR012.lastActiveCaseID) {
            updateHeaderInfo(serviceCheckRecord.id, 'header-bottom', 'SIIAM', serviceCheckRecord.rulesData.MDR012.lastActiveCaseID);
        }
        if (serviceCheckRecord.nbnAccessType) {
            updateHeaderInfo(serviceCheckRecord.id, 'header', 'Access Type', serviceCheckRecord.nbnAccessType);
        }
        if (serviceCheckRecord.nbnId) {
            updateHeaderInfo(serviceCheckRecord.id, 'header', 'NBN ID', serviceCheckRecord.nbnId);
        }

        if (serviceCheckRecord.rulesData && serviceCheckRecord.rulesData.MDR106 && serviceCheckRecord.rulesData.MDR106.deviceType) {
            updateHeaderInfo( serviceCheckRecord.id, 'header-bottom', 'Device Type', serviceCheckRecord.rulesData.MDR106.deviceType);
        }

        if (SCr.serviceType) {
            updateHeaderInfo( SCr.id, 'header-bottom', 'Service Type', SCr.serviceType );
        }

        if (serviceCheckRecord.rulesData && serviceCheckRecord.rulesData.MDR005 && serviceCheckRecord.rulesData.MDR005.customerName) {
            updateHeaderInfo(serviceCheckRecord.id, 'header-bottom', 'Customer', serviceCheckRecord.rulesData.MDR005.customerName);
        }
        if (serviceCheckRecord.address) {
            updateHeaderInfo(serviceCheckRecord.id, 'header-top', 'Address', serviceCheckRecord.address);
        }

        if (serviceCheckRecord.OffshoreResources === 'No' || serviceCheckRecord.OffshoreResources === 'Conditional') {
            updateHeaderInfo(serviceCheckRecord.id, 'header-middle', 'Offshore Consent', serviceCheckRecord.OffshoreResources, 'text-warning');
        }

        if (serviceCheckRecord.serviceType === "GH" &&
            serviceCheckRecord.rulesData &&
            serviceCheckRecord.rulesData.MDR203 &&
            Array.isArray(serviceCheckRecord.rulesData.MDR203.numberRanges) &&
            serviceCheckRecord.rulesData.MDR203.numberRanges.length > 0
        ) {
            const numberRanges = serviceCheckRecord.rulesData.MDR203.numberRanges;
            addNumberRanges(serviceCheckRecord.id, numberRanges);
        }

        if (serviceCheckRecord.errorMessage) {
            $(`#error-${id}`).html(serviceCheckRecord.errorMessage);
            $(`#error-${id}`).show();
        }

        //Print Rules in summary
        for (var ruleName in serviceCheckRecord.rulesData) {
            console.log('>> Add Rule ' + ruleName);

            let ruleConfig;

            if (ruleName in rules) {
                ruleConfig = rules[ruleName];
            } else {
                // Default settings for any rule that no longer exists
                let substituteRule = {
                    name: ruleName,
                    active: true,
                    hideInResult: false,
                    showReject: false,
                    requiresReview: false,
                    includedInitialServiceCheck: false,
                    title: "",
                    rootCauseCategory: "",
                    level: 0,
                    description: "Rule does not exist anymore",
                    wikiPage: "",
                    ruleType: null,
                    isWarning: false,
                    displayInSummary: false,
                    failedInSummary: true,
                    preSources: [],
                    preSourcesRunOnFail: false,
                    preRules: [],
                    preRulesRunOnFail: false,
                    runWhenDependenciesResolved: false,
                    depedenciesFromRelatedServiceChecks: false,
                    runForEachPreSource: false,
                    preCondition: "",
                    preConditionMsg: "",
                    trueMsg: "",
                    falseMsg: "",
                    errorMessage: "",
                    ruleCode: "",
                    valueMsgStm: "",
                    extraInfo: "",
                    ignoreCompare: false,
                    overrideCompare: '',
                    unitTest: [],
                    action: null
                };

                ruleConfig = substituteRule;
            }

            ruleCheckMsg(serviceCheckRecord.id, ruleConfig, serviceCheckRecord.rulesData[ruleName], true, true, originServiceCheckId);
        };

        // Displays source data
        for (var sourceName in serviceCheckRecord.sourcesMetadata) {
            console.log('>> Add Source ' + sourceName);
            if (!sources[sourceName]) {
                let sourceErrorRow = $('<tr>').attr('class', 'rule rule-Error')
                    .html($('<td>').attr('colspan', '100%').html(
                        $('<span>').attr('style', 'color:red').attr('class', 'fas fa-unlink').attr('title', 'Error').append(
                            $('<a>').attr('target', sourceName).attr('href', '/edit/sources').text(`${sourceName} does not exist anymore in Merge!`)
                        ).append(` : ${errorMessage}`)
                    ));

                addToSCBlock('Summary', serviceCheckRecord.id, sourceErrorRow, 'prepend');
                continue;
            }

            if (MagpieSchematicsSourceNames.includes(sourceName)) {
                continue;
            }

            let cd = {
                category: sources[sourceName].title,
                name: sourceName,
                SCid: serviceCheckRecord.id,
                metadata: serviceCheckRecord.sourcesMetadata[sourceName],
                data: serviceCheckRecord.sourcesData[sourceName],
                hideInResult: sources[sourceName].hideInResult
            };

            console.log(cd);
            // Display source if pre condition result is fulfilled and hideInResult is not set to true
            let hideSource = !$('#showHiddenSources').prop('checked');
            let hidePreConditionFailedSource = !$('#showPreConditionFailedSources').prop('checked');

            updateCollectedData(cd, hideSource, hidePreConditionFailedSource);
            //Print failed Sources
            if (['error', 'Pre-Condition Error', 'Parameter Error'].includes(serviceCheckRecord.sourcesMetadata[sourceName].status)) {
                let errorMessage = serviceCheckRecord.sourcesMetadata[sourceName].errorMessageSummary ? serviceCheckRecord.sourcesMetadata[sourceName].errorMessageSummary : serviceCheckRecord.sourcesMetadata[sourceName].error;

                let sourceErrorRow = $('<tr>').attr('class', 'rule rule-Error')
                    .html($('<td>').attr('colspan', '100%').html(
                        $('<span>').attr('style', 'color:red').attr('class', 'fas fa-unlink').attr('title', 'Error').append(
                            $('<a>').attr('target', sourceName).attr('href', `${wikiBaseURL}${sourceName}`).text(sourceName)
                        ).append(` : ${errorMessage}`)
                    ));

                addToSCBlock('Summary', serviceCheckRecord.id, sourceErrorRow, 'prepend');
            }
        }

        let magpieSchematicsShown = false;

        // Displays generated source data (InRequest- prefix)
        for (var sourceName in serviceCheckRecord.sourcesData) {
            if (sourceName.indexOf("InRequest-") === 0) {
                let cd = {
                    category: sourceName,
                    name: sourceName,
                    SCid: serviceCheckRecord.id,
                    data: serviceCheckRecord.sourcesData[sourceName]
                };

                updateCollectedData(cd, false, false);
            }

            if (MagpieSchematicsSourceNames.includes(sourceName) && (
                typeof serviceCheckRecord.sourcesData?.[sourceName]?.schematics === 'string' ||
                typeof serviceCheckRecord.sourcesData?.[sourceName]?.data?.schematics === 'string')
            ) {

                let schematicsXml = typeof serviceCheckRecord.sourcesData?.[sourceName]?.schematics === 'string' ? serviceCheckRecord.sourcesData[sourceName].schematics : serviceCheckRecord.sourcesData?.[sourceName]?.data?.schematics;
                let isMDN = typeof serviceCheckRecord.sourcesData?.[sourceName]?.schematics === 'string' ? false : true;
                let schematicsTitle = isMDN ? 'Magpie MDN Schematics' : 'Magpie Schematics';
                let fnn = serviceCheckRecord.sourcesMetadata?.[sourceName]?.parameters?.fnn;
                const schematicsElement = $(schematicsXml);

                const rowElement = $('<tr>').html(
                    $('<td>').attr('colspan', '100%').text(
                        `${schematicsTitle} (${fnn}) `
                    ).append(
                        $('<button>').addClass('btn btn-sm btn-secondary').attr('title', 'View schematics').html($('<span>').addClass('fas fa-external-link-alt')).click(function() {
                            $('#magpieSchematic').html(schematicsElement);
                            $('#magpieSchematicsModal').modal('show');

                            // In the <script> tag in schematics, the ht and wh values are reduced each time at the start
                            // which may affect the schematics if set again with .html()
                            // This implementation multiplies the current value to set it to its original
                            if (!isMDN && magpieSchematicsShown) {
                                ht *= 3.8;
                                wh *= 2;
                                SVGRoot.setAttribute('height', ht);
                                SVGRoot.setAttribute('width',  wh);
                            }

                            magpieSchematicsShown = true;

                            ShowTooltip = overrideShowTooltip;
                        })
                    )
                );

                addToSCBlock('Summary', serviceCheckRecord.id, rowElement, 'prepend');
            }
        }

        // Adds rules with code errors / other errors to summary tab
        for (var ruleName in serviceCheckRecord.rulesData) {
            if (serviceCheckRecord.rulesData[ruleName].result === 'Error') {
                let ruleErrorRow = $('<tr>').addClass('rule rule-Error').html(
                    $('<td>').attr('colspan', '100%').append(
                        $('<span>').attr('style', 'color:red').attr('title', 'Error').addClass('fas fa-unlink')
                    ).append(
                        ` Rule ${ruleName} error: ${serviceCheckRecord.rulesData[ruleName].error}`
                    )
                );
                addToSCBlock('Summary', serviceCheckRecord.id, ruleErrorRow, 'prepend');
            }
        }

        // Allows templates to be selected if the service check isn't running or has an error
        if (!["running", "error"].includes(serviceCheckRecord.status)) {
            $(`#textTemplate-${id}`).removeAttr("data-disable-select");
        }

        // Disables feedback for service checks that do not belong to the current user
        if (username !== SCr.createdBy) {
            feedbackDisable(SCr.id);
        }

        feedbackStateChange(SCr.id, SCr.feedback);

        // To update: New UI link
        $('#linkNewUi').attr('href', `${location.protocol}//${location.host}/serviceCheck/new?${new URLSearchParams({ id: SCr.id }).toString()}`);
    }
</script>
<%- include('footer', {}); %>
