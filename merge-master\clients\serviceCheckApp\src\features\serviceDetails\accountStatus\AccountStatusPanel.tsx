import classNames from 'classnames'
import { DetailsPanel } from '../../../components/detailsPanel/DetailsPanel'
import { GreenCheckIcon } from '../../../components/statusIcon/GreenCheckIcon'
import { OrangeWarningIcon } from '../../../components/statusIcon/OrangeWarningIcon'
import { RedExclamationIcon } from '../../../components/statusIcon/RedExclamationIcon'
import { AccountStatus } from '../../../infrastructure/models'
import { ActiveRASSOrdersField } from './fields/ActiveRASSOrdersField'
import { RASSStatusField } from './fields/RASSStatusField'

interface AccountStatusPanelProps {
    accountStatus: AccountStatus
    className?: string
}

export const AccountStatusPanel = ({
    accountStatus,
    className,
}: AccountStatusPanelProps) => {
    const isActive = accountStatus?.rassStatus === 'FNN found'
    const hasOrders = (accountStatus?.rassCancelledOrders?.length ?? 0) > 0

    const getStatusIcon = () => {
        if (!isActive) {
            return <RedExclamationIcon />
        }
        return hasOrders ? <OrangeWarningIcon /> : <GreenCheckIcon />
    }

    return (
        <DetailsPanel
            label="Account Status"
            className={classNames(className)}
            panelElements={getStatusIcon()}
        >
            <>
                <RASSStatusField value={accountStatus?.rassStatus} />
                <ActiveRASSOrdersField
                    label="Active CAN Orders"
                    value={accountStatus?.rassCancelledOrders ?? []}
                />
                <ActiveRASSOrdersField
                    label="Active MOD Orders"
                    value={accountStatus?.rassModOrders ?? []}
                />
            </>
        </DetailsPanel>
    )
}
