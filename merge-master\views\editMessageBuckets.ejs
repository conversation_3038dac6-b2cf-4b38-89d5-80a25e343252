<%- include('header') %>
<%- include('jsonEditInclude', {}) %>
<%- include('menu', {currentTab: 'Form'}); %>
<link rel="stylesheet" type="text/css" href="/public/stylesheets/select2.min.css">
<script src="/public/javascripts/select2.min.js" crossorigin="anonymous"></script>
<script src="/public/javascripts/merge_SC_lib.js" crossorigin="anonymous"></script>
<br>
<div class="container">
    <div class="row">
        <div class="col-8">
            <a href="/edit/messageBuckets">< Back to messageBuckets</a>
        </div>
        <div class="col-4">
            <div class="float-right">
                <button class="btn btn-primary" id="createMessageBucket" data-toggle="modal" data-target="#confirmCreateModal" style="display:none;" title="Create" disabled><span class="fas fa-plus-square"></span></button>
                <button class="btn btn-primary" id="saveMessageBucket" data-toggle="modal" data-target="#confirmSaveModal" style="display:none;" title="Save" disabled><span class="fas fa-save"></span></button>
                <button class="btn btn-primary" id="refreshMessageBucket" data-toggle="modal" data-target="#confirmRefreshModal"  style="display:none;" title="Refresh"><span class="fas fa-sync"></span></button>
                <button class="btn btn-danger" id="deleteMessageBucket" data-toggle="modal" data-target="#confirmDeleteModal" style="display:none;" title="Delete" disabled><span class="fas fa-trash"></span></button>
            </div>
        </div>
    </div>
    <br>
    <div class="row">
        <div class="col-12">
            <div id="readonlyAlert" style="display:none;" class="alert alert-warning">Warning: Message Bucket is read only, editing is disabled.</div>
        </div>
    </div>

    <ul class="nav nav-tabs" role="tablist">
        <li role="presentation" class="nav-item active">
            <a class="nav-link active" data-target="#editMessageBucketsTab" data-toggle="tab" role="tab" href="">Edit Message Bucket</a>
        </li>
    </ul>

    <div class="tab-content">
        <div class="tab-pane active border" id="editMessageBucketsTab" style="height:75vh;overflow-y:auto;padding-bottom:16px;">
            <div style="padding: 2rem;">
                <div id="messageBucketsLoadError" class="alert alert-danger" role="alert" style="display:none;padding-left:16px;"></div>
                <div id="messageBucketsEdit" style="display:none;"></div>
            </div>
        </div>
    </div>
</div>

<div id="createMessageBucketModal" class="modal fade">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Error</h5>
            </div>
            <div class="modal-body">
                <code id="createMessageBucketModalMessage" style="white-space:pre-wrap;"></code>
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-outline-secondary">Close</button>
            </div>
        </div>
    </div>
</div>

<div id="confirmSaveModal" class="modal fade">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Save</h5>
            </div>
            <div class="modal-body">
                Would you like to save the changes for this message bucket?
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-primary" id="confirmSave">Save</button>
                <button type="button" data-dismiss="modal" class="btn btn-outline-secondary">Cancel</button>
            </div>
        </div>
    </div>
</div>

<div id="confirmRefreshModal" class="modal fade">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Refresh</h5>
            </div>
            <div class="modal-body">
                Would you like to refresh the message bucket contents? Any changes made will be lost.
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-primary" id="confirmRefresh">Refresh</button>
                <button type="button" data-dismiss="modal" class="btn btn-outline-secondary">Cancel</button>
            </div>
        </div>
    </div>
</div>

<div id="confirmDeleteModal" class="modal fade">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
            </div>
            <div class="modal-body">
                Would you like to delete this message bucket? This action cannot be reversed.
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-danger" id="confirmDelete">Delete</button>
                <button type="button" data-dismiss="modal" class="btn btn-outline-secondary">Cancel</button>
            </div>
        </div>
    </div>
</div>

<script>
    const messageBucketName = atob("<%- messageBucketName %>");
    const wikiBaseURL = "<%= global.gConfig.wikiBaseURL %>";
    const createMessageBucket = <%= createMessageBucket %>;
    const disableRuleSourceEditing = <%- user.isAdmin ? false : global.gConfig.disableRuleSourceEditing %>;
    const schema = JSON.parse(atob("<%- schema %>"));

    var dataLoaded = false;
    var dataModified = false;
    var autosaveInterval = null;

    $(document).ready(function() {
        $("#createMessageBucket").prop("disabled", disableRuleSourceEditing);
        $("#saveMessageBucket").prop("disabled", disableRuleSourceEditing);
        $("#deleteMessageBucket").prop("disabled", disableRuleSourceEditing);

        let jsonEditor = new JSONEditor(document.getElementById('messageBucketsEdit'), {
            ajax: false,
            enable_array_copy: false,
            schema: schema,
            startval: null,
            no_additional_properties: true,
            required_by_default: true,
            show_errors: "always"
        });

        if (disableRuleSourceEditing) {
            jsonEditor.disable();
            $("input[name='saveOptionRadios']").attr("disabled", true);
            $("#readonlyAlert").show();
        }

        if (createMessageBucket) {
            $("#createMessageBucket").show();
            $("#messageBucketsEdit").show();
        } else {
            $("#saveMessageBucket").show();
            $("#refreshMessageBucket").show();
            $("#deleteMessageBucket").show();
            getMessageBucketToForm(jsonEditor);
        }

        $("#createMessageBucket").click(function() {
            createMessageBucketFromForm(jsonEditor);
        });

        $("#confirmSave").click(function() {
            writeMessageBucketFromForm(jsonEditor);
        });

        $("#confirmRefresh").click(function() {
            getMessageBucketToForm(jsonEditor);
        });

        $("#confirmDelete").click(function() {
            $.ajax({
                type: "DELETE",
                url: `/messageBuckets/${encodeURIComponent(messageBucketName)}`,
                success: function (response) {
                    window.location.href = "/edit/messageBuckets";
                },
                error: function (error) {
                    alert("Error, could not delete message bucket:" + error.responseText);
                },
            });
        });

        $('.close').click(function() {
            $(this).parent().hide();
        });

        jsonEditor.on("change", function() {
            // If the message bucket was loaded recently via AJAX, the change event will still be triggered,
            // do not count this instance as the message bucket being modified by the user
            if (!dataLoaded) {
                dataModified = true;
            } else {
                dataLoaded = false;
            }
        });

        $(window).on("beforeunload", function(e) {
            if (dataModified) {
                return confirm("");
            } else {
                return;
            }
        });
    });

    function getMessageBucketToForm(editor) {
        setButtonSpinner($("#refreshMessageBucket"), $("<span>").addClass("fas fa-sync"), true);

        $.ajax({
            cache: false,
            type: "GET",
            url: `/messageBuckets/${encodeURIComponent(messageBucketName)}`,
            dataType: "json",
            success: function (response) {
                dataLoaded = true;
                dataModified = false;

                editor.setValue(response);
                $("#messageBucketsEdit").show();

                // Load code obtained from request into execute message bucket editors
                $("#developRefreshCode").click();
            },
            error: function (error) {
                let errorMessage = '';
                if (error.status == 404) {
                    errorMessage = `MessageBucket ${messageBucketName} does not exist`;
                } else {
                    errorMessage = `Error encountered loading message bucket ${messageBucketName}: HTTP ${error.status} (${error.statusText}): ${error.responseText}`;
                }
                $("#messageBucketsLoadError").text(errorMessage);
                $("#messageBucketsLoadError").show();
            },
            complete: function(xhr, status) {
                setButtonSpinner($("#refreshMessageBucket"), $("<span>").addClass("fas fa-sync"), false);
            }
        });
    }

    function createMessageBucketFromForm(editor) {
        let errors = editor.validate();

        if (!errors.length) {
            $.ajax({
                type: "POST",
                url: "/messageBuckets/",
                contentType: "application/json",
                dataType: "json",
                data: JSON.stringify(editor.getValue()),
                success: function (response) {
                    dataModified = false;
                    window.location.href = `/edit/messageBuckets/${encodeURIComponent(editor.getValue().name)}`;
                },
                error: function (error) {
                    if (error.status == 400) {
                        $("#createMessageBucketModalMessage").text(`Error in fields for message bucket:\n${JSON.stringify(error.responseJSON, null, 4)}`);
                    } else if (error.status == 409) {
                        $("#createMessageBucketModalMessage").text(`A message bucket with name ${editor.getValue().name} already exists.`);
                    } else {
                        $("#createMessageBucketModalMessage").text(`Unknown Error:\n${error.responseJSON}`);
                    }

                    $("#createMessageBucketModal").modal({ show: true });
                },
            });
        } else {
            $("#createMessageBucketModalMessage").text("Validation errors in editor, please check fields in form.");
            $("#createMessageBucketModal").modal({ show: true });
        }
    }

    function writeMessageBucketFromForm(editor) {
        let errors = editor.validate();

        if (!errors.length) {
            setButtonSpinner($("#saveMessageBucket"), $("<span>").addClass("fas fa-save"), true);

            $.ajax({
                cache: false,
                type: "PUT",
                url: `/messageBuckets/${encodeURIComponent(messageBucketName)}`,
                contentType: "application/json",
                dataType: "json",
                data: JSON.stringify(editor.getValue()),
                success: function (_) {
                    dataModified = false;
                },
                error: function(error) {
                    // just alerts the user for now
                    alert("Error, could not save message bucket:" + error.responseText);
                },
                complete: function(xhr, status) {
                    setButtonSpinner($("#saveMessageBucket"), $("<span>").addClass("fas fa-save"), false);
                }
            });
        } else {
            $("#createMessageBucketModalMessage").text("Validation errors in editor, please check fields in form.");
            $("#createMessageBucketModal").modal({ show: true });
        }
    }
</script>
</body>
</html>
