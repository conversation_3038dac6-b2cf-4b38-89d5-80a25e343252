import { useEffect, useState } from 'react'
import { DateRange } from '../../../../../../components/dateRange/DateRange'
import { Panel } from '../../../../../../components/panel/Panel'
import { validateDateRange } from '../../../testRunForm/validation'

export interface DateInputActionRowProps {
    onDatesChange: (startDate: string, endDate: string, canRun: boolean) => void
}

export const DateInputActionRow = ({
    onDatesChange,
}: DateInputActionRowProps) => {
    const now = new Date()
    const defaultStart = new Date(now.getTime() - 30 * 60 * 1000).toISOString()
    const defaultEnd = now.toISOString()

    const [startDate, setStartDate] = useState<string>(defaultStart)
    const [endDate, setEndDate] = useState<string>(defaultEnd)

    const errorMessage = validateDateRange(startDate, endDate)

    const handleStartDateChange = (date: string) => {
        setStartDate(date)
        const newErrorMessage = validateDateRange(date, endDate)
        const newCanRun = newErrorMessage === ''
        onDatesChange(date, endDate, newCanRun)
    }

    const handleEndDateChange = (date: string) => {
        setEndDate(date)
        const newErrorMessage = validateDateRange(startDate, date)
        const newCanRun = newErrorMessage === ''
        onDatesChange(startDate, date, newCanRun)
    }

    useEffect(() => {
        const newErrorMessage = validateDateRange(startDate, endDate)
        onDatesChange(startDate, endDate, newErrorMessage === '')
        // Run only once on mount.
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])

    return (
        <Panel>
            <DateRange
                startDate={startDate}
                endDate={endDate}
                onStartDateChange={handleStartDateChange}
                onEndDateChange={handleEndDateChange}
                errorMessage={errorMessage}
            />
        </Panel>
    )
}
