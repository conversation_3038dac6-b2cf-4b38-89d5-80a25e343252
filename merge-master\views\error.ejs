<%- include('header', {title: 'Merge Error'}); %>
<%- include('menu', {currentTab: 'Home'}); %>
<div class="container">
    <br>
    <div class="jumbotron">
        <div class="row">
            <div class="col-12 text-center">
                <span style="font-size: 6em;color:red"class="fa fa-fire-extinguisher"></span> <span style="font-size: 6em;color:orange"class="fa fa-fire"></span> <span style="font-size: 6em;color:grey"class="fa fa-desktop"></span>
            </div>
        </div>
        <br>
        <br>
        <div class="row">
            <div class="col-12 text-center">
                <h2>HTTP 500 Internal Server Error</h2>
                <p>An unexpected error has occurred, if this persists, please contact the Merge development team (<a href="<%= microsoftTeamsChannelLink %>" target="blank">Microsoft Teams Channel</a>) with the error message below.</p>
            </div>
        </div>
        <% if (error) { %>
        <div class="row">
            <span class="alert alert-danger"><%= error.toString() %></span>
        </div>
            <% if (user && user.isAdmin) { %>
            <h6>Stack trace:</h6>
            <pre><%= error.stack %></pre>
            <% } %>
        <% } %>
    </div>
</div>

<%- include('footer', {}); %>