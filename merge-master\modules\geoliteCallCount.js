
import { <PERSON>ronJob } from 'cron';
import _ from 'lodash';
import promClient from 'prom-client';
import url from 'url';

import logger from './logger.js';
import GeoliteCall from '../db/model/geoliteCall.js';


const sourceCollectedCountGeoliteGauge = new promClient.Gauge({
    name: 'geolite_api_call_count_month',
    help: 'Number of times the Maxmind Geolite API has been called in the last month'
});


async function setGeoliteCallCountPrometheusMetric() {
    try {
        let count = await GeoliteCall.countDocuments();
        sourceCollectedCountGeoliteGauge.set(count);
    } catch(error) {
        logger.error(`Error when obtaining Geolite call count document count, ${error.toString()}`);
    }
}


CronJob.from({
    cronTime: '*/5 * * * *',
    onTick: setGeoliteCallCountPrometheusMetric,
    start: true
});
setGeoliteCallCountPrometheusMetric();


export async function geoliteCallCountCheck(inputUrl) {
    try {
        let calledUrl = new url.URL(inputUrl);
        let geoliteUrl = new url.URL(_.get(global, ['gConfig', 'APIUriConfig', 'GeoliteAPI', 'cityURL']));

        if (calledUrl.origin === geoliteUrl.origin) {
            await createGeoliteCallRecord();
        }
    } catch(error) {
        logger.error(`Geolite API call check failed, ${error.toString()}`);
    }
}


/**
 * Creates an instance of a GeoliteCall document in the database which expires in 1 month
 */
async function createGeoliteCallRecord() {
    try {
        let removeOnDate = new Date();
        removeOnDate.setMonth(removeOnDate.getMonth() + 1);

        await new GeoliteCall({
            removeOn: removeOnDate
        }).save();
    } catch(error) {
        logger.error(`Error when saving Geolite API call record to database, ${error.toString()}`);
    }
}
