import { CollapsablePanel } from '../../../../components/collapsablePanel/CollapsablePanel'
import { DetailField } from '../../../../components/detailsPanel/DetailField'
import { Panel } from '../../../../components/panel/Panel'
import { formatDate } from '../../../../helpers/formatDate'
import { RassOrder } from '../../../../infrastructure/models'
import styles from './ActiveRASSOrdersField.module.scss'
interface ActiveRASSOrdersFieldProps {
    label: string
    value: RassOrder[]
}

export const ActiveRASSOrdersField = ({ label, value }: ActiveRASSOrdersFieldProps) => {
    if (value === null || value === undefined) {
        return (
            <Panel>
                <DetailField label={label} value="Unknown" />
            </Panel>
        )
    }

    if (value.length === 0) {
        return (
            <Panel>
                <DetailField label={label} value="None" />
            </Panel>
        )
    }

    return (
        <CollapsablePanel
            headerElement={<DetailField label={label} value="" />}
            canOpen={false}
            itemCount={value.length}
        >
            <div className={styles.activeOrder}>
                <div className={styles.orderRow}>
                    {value.map((order) => (
                        <div key={order.ORDER_NUMBER}>
                            <DetailField
                                label="Order Number"
                                value={order.ORDER_NUMBER}
                                inline={true}
                            />
                            <DetailField
                                label="Order Type"
                                value={order.SERVICE_ORDER_TYPE}
                                inline={true}
                            />
                            <DetailField
                                label="Cust Required Date"
                                value={formatDate(
                                    order.CUST_REQUIRED_DATE,
                                    'Unknown'
                                )}
                                inline={true}
                            />
                        </div>
                    ))}
                </div>
            </div>
        </CollapsablePanel>
    )
}
