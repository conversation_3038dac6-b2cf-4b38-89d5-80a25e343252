<!-- v1 -->
<%- include('header', {}); %>
<%- include('menu' , {currentTab: 'Home' }); %>
<link rel="stylesheet" href="/public/stylesheets/flatpickr.min.css">
<script src="/public/javascripts/flatpickr.min.js" crossorigin="anonymous"></script>
<link rel="stylesheet" type="text/css" href="/public/stylesheets/jsgrid.min.css">
<link rel="stylesheet" type="text/css" href="/public/stylesheets/jsgrid-theme.min.css">
<link rel="stylesheet" type="text/css" href="/public/stylesheets/select2.min.css">
<script type="text/javascript" src="/public/javascripts/jsgrid.min.js"></script>
<script type="text/javascript" src="/public/javascripts/select2.min.js"></script>
<script type="text/javascript" src="/public/javascripts/chart.min.js"></script>
<script type="text/javascript" src="/public/javascripts/moment.min.js"></script>
<script type="text/javascript" src="/public/javascripts/chartjs-adapter-moment.min.js"></script>
<script type="text/javascript" src="/public/javascripts/hammer.min.js"></script>
<script type="text/javascript" src="/public/javascripts/chartjs-plugin-zoom.min.js"></script>
<style>
    .select2-drop {
        display: none !important;
    }

    tr.highlight td.jsgrid-cell {
        background-color: #00fd9a !important;
    }
</style>

<div class="d-flex flex-grow-1 flex-column" style="padding:1rem;height:100%">
    <div class="d-inline">
        <br>
        <div class="jumbotron py-1 px-4 text-white rounded bg-secondary">
            <h3 class="display-6 font-weight-normal">
                <%= title %>
            </h3>
        </div>
        <div class="row">
            <div class="col-6">
                <p id="displayServiceCheckTotal"></p>
            </div>
            <div class="col-6">
                <div class="float-right">
                    <button id="resetDates" class="btn btn-sm btn-secondary" title="Default time range (1 month)"><span class="fas fa-undo"></span></button>&nbsp;
                    <label for="startDate" class="col-form-label">Start:</label>&nbsp;
                    <input type="text" id="startDate">&nbsp;
                    <label for="endDate" class="col-form-label">End:</label>&nbsp;
                    <input type="text" id="endDate">&nbsp;
                    <span id="endDateClear" class="fas fa-times" title="Clear end date"></span>&nbsp;
                    <button id="refresh" class="btn btn-primary" title="Refresh"><span class="fas fa-sync"></span></button>
                </div>
            </div>
        </div>
        <br>
        <div class="row">
            <div class="col-4">
                <label for="numRecords">Records per page:</label>
                <select id="numRecords" style="width: 6em">
                    <option value=10>10</option>
                    <option value=25>25</option>
                    <option value=50 selected>50</option>
                    <option value=100>100</option>
                    <option value=500>500</option>
                    <option value=1000>1000</option>
                </select>
            </div>
            <div class="col-8">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="" id="includeEmpty">
                    <label class="form-check-label" for="includeEmpty">Include empty case ID list in unique sets</label>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div>
                            <label class="col-form-label" for="chartResetZoom">The zoom on this graph is adjustable with the scroll wheel and can be panned with the mouse. Clicking a point will open the service check record at the time.</label> <button class="btn btn-secondary btn-sm" id="chartResetZoom">Reset zoom</button>
                        </div>
                        <div><canvas id="serviceCheckCaseHistoryChart" width="100%" height="140px"></canvas></div>
                    </div>
                </div>
            </div>
        </div>
        <br>
    </div>
    <div id="serviceCheckGrid" class="flex-grow-1"></div>
</div>

<script>
    let startDatePicker;
    let endDatePicker;

    $(document).ready(function () {
        let selectedFnn = null;

        // Variables to determine ID of internal div to view case list
        let siiamCaseElementId = 0;
        let serviceCentralIncidentElementId = 0;

        let startDateServiceCheck = localStorage.getItem("startDateServiceCheck");
        let endDateServiceCheck = localStorage.getItem("endDateServiceCheck");
        let startDate = null;
        let endDate = null;

        if (isNaN(Date.parse(startDateServiceCheck))) {
            startDate = new Date();
            startDate.setMonth(startDate.getMonth() - 1);
        } else {
            startDate = new Date(startDateServiceCheck);
        }

        if (!isNaN(Date.parse(endDateServiceCheck))) {
            endDate = new Date(endDateServiceCheck);
        }

        startDatePicker = flatpickr("#startDate", {
            defaultDate: startDate,
            enableTime: true,
            enableSeconds: true,
            dateFormat: "d/m/Y h:i:S K",
            position: "below",
            onChange: function(selectedDates, dateStr, instance) {
                if (Array.isArray(selectedDates) && selectedDates[0] instanceof Date) {
                    localStorage.setItem("startDateServiceCheck", selectedDates[0].toISOString());
                }
            }
        });

        endDatePicker = flatpickr("#endDate", {
            defaultDate: endDate,
            enableTime: true,
            enableSeconds: true,
            dateFormat: "d/m/Y h:i:S K",
            position: "below",
            onChange: function(selectedDates, dateStr, instance) {
                if (Array.isArray(selectedDates) && selectedDates[0] instanceof Date) {
                    localStorage.setItem("endDateServiceCheck", selectedDates[0].toISOString());
                }
            }
        });

        $("#endDateClear").click(function() {
            endDatePicker.clear();
            localStorage.removeItem("endDateServiceCheck");
        });

        $("#resetDates").click(function() {
            let defaultDate = new Date();
            defaultDate.setMonth(defaultDate.getMonth() - 1);

            startDatePicker.setDate(defaultDate);
            endDatePicker.clear();

            localStorage.removeItem("startDateServiceCheck");
            localStorage.removeItem("endDateServiceCheck");
        });

        $("#serviceCheckGrid").jsGrid({
            width: "100%",
            height: "100%",
            filtering: true,
            inserting: false,
            editing: false,
            sorting: true,
            autoload: true,
            paging: true,
            pageLoading: true,
            pageSize: parseInt($("#numRecords").val()),
            pageIndex: 1,
            editButton: false,
            deleteButton: false,
            controller: {
                loadData: function(filter) {
                    siiamCaseElementId = 0;
                    serviceCentralIncidentElementId = 0;

                    var data = $.Deferred();

                    let queryData = setupQueryDataFromFilter(filter);

                    $.ajax({
                        cache: false,
                        type: "GET",
                        url: "/serviceCheck/uniqueCases",
                        data: queryData,
                        success: function (response) {
                            let total = response.metadata && response.metadata.pagination ? response.metadata.pagination.total : 0;

                            data.resolve({
                                data: response.results,
                                itemsCount: total
                            });

                            let startDate = "";
                            let endDate = "";

                            if (response.metadata) {
                                if (response.metadata.startDate) {
                                    startDate = new Date(response.metadata.startDate).toLocaleString('en-GB');
                                }
                                if (response.metadata.endDate) {
                                    endDate = ` To: ${new Date(response.metadata.endDate).toLocaleString('en-GB')}`;
                                }
                            }

                            $("#displayServiceCheckTotal").text(`${total} records total (From: ${startDate}${endDate})`);
                        },
                        error: function (xhr, status, e) {
                            // Not the best solution, puts nothing in the table
                            data.resolve({
                                data: [],
                                itemsCount: 0
                            });
                        },
                        complete: function(_) {
                            setButtonSpinner($("#refresh"), $("<span>").addClass("fas fa-sync"), false);
                        }
                    });

                    return data.promise();
                }
            },
            rowClick: function(args) {
                // Removes highlighting from all other rows
                $(".jsgrid-row, .jsgrid-alt-row").removeClass("highlight");
                var currRow = this.rowByItem(args.item);
                currRow.toggleClass("highlight");

                if (args && args.item) {
                    // Avoid processing graphs for same FNN if already loaded
                    if (args.item.fnn != selectedFnn) {
                        selectedFnn = args.item.fnn;
                        loadUniqueCaseData(args.item.fnn, args.item.siiamCases, args.item.serviceCentralIncidents);
                    }
                } else {
                    alert("No item found for selected row");
                }
            },
            fields: [
                { name: "fnn", title: "FNN", type: "text", width: "16em" },
                { name: "serviceType", title: "Service Type", type: "text", width: "16em" },
                { name: "siiamCases", title: "Unique SIIAM Cases", type: "text", filtering: false, width: "50%" , itemTemplate: function(value, item) {
                    if (value.length == 0) {
                        return "None";
                    } else {
                        value = $.map(value, item => {
                            return JSON.stringify(item);
                        });

                        let caseList = value.join("<br>");
                        let elementId =  "siiam_cases_" + siiamCaseElementId;
                        siiamCaseElementId++;
                        let expandElement = `<a>${value.length} unique sets of SIIAM case IDs</a> <a href="#" data-toggle="collapse" data-target="#${elementId}">view</a>\
                                             <div class="collapse" id="${elementId}"><pre>${caseList}</pre></div>`;
                        return expandElement;
                    }
                }},
                { name: "serviceCentralIncidents", title: "Unique Service Central Incidents", type: "text", filtering: false, width: "50%" , itemTemplate: function(value, item) {
                    if (value.length == 0) {
                        return "None";
                    } else {
                        value = $.map(value, item => {
                            return JSON.stringify(item);
                        });

                        let caseList = value.join("<br>");
                        let elementId =  "service_central_incidents_" + serviceCentralIncidentElementId;
                        serviceCentralIncidentElementId++;
                        let expandElement = `<a>${value.length} unique sets of Service Central incident IDs</a> <a href="#" data-toggle="collapse" data-target="#${elementId}">view</a>\
                                             <div class="collapse" id="${elementId}"><pre>${caseList}</pre></div>`;
                        return expandElement;
                    }
                }},
            ]
        });

        $("#numRecords").select2({
            minimumResultsForSearch: -1,
            width: "resolve"
        });

        $("#numRecords").on('select2:select', function (e) {
            $("#serviceCheckGrid").jsGrid("option", "pageSize", parseInt($("#numRecords").val()));
        });

        $("#refresh").click(function() {
            setButtonSpinner($("#refresh"), $("<span>").addClass("fas fa-sync"), true);
            $("#serviceCheckGrid").jsGrid("loadData");
        });

        var caseHistoryChartContext = document.getElementById("serviceCheckCaseHistoryChart").getContext("2d");

        var caseHistoryChart = new Chart(caseHistoryChartContext, {
            type: "scatter",
            data: {},
            options: {
                responsive: true,
                maintainAspectRatio: false,
                // Opens Service Check Record for clicked point from chart
                onClick: function(event) {
                    var elements = caseHistoryChart.getElementsAtEventForMode(event, 'nearest', { intersect: true }, false);
                    if (elements.length > 0) {
                        elements.forEach(element => {
                            let datasetIndex = element.datasetIndex;
                            let index = element.index;
                            if (datasetIndex != undefined && index != undefined) {
                                let serviceCheckId = caseHistoryChart.data.datasets[datasetIndex].data[index].id;
                                window.open(`/serviceCheck/view/html/${encodeURIComponent(serviceCheckId)}`, "_blank");
                            }
                        });
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: "No FNN selected"
                    },
                    tooltip: {
                        callbacks: {
                            title: function(contexts) {
                                if (contexts && contexts.length > 0) {
                                    return contexts[0].label;
                                } else {
                                    return "";
                                }
                            },
                            afterTitle: function(contexts) {
                                if (contexts && contexts.length > 0) {
                                    let caseIdSet = new Set();
                                    contexts.forEach(context => {
                                        caseIdSet.add(context.dataset.label);
                                    });
                                    return Array.from(caseIdSet).join(",");
                                } else {
                                    return "";
                                }
                            },
                            label: function(context) {
                                let dataIndex = context.dataIndex;

                                if (dataIndex !== undefined) {
                                    let record = context.dataset.data[dataIndex];
                                    return record.id;
                                } else {
                                    return "";
                                }
                            }
                        }
                    },
                    zoom: {
                        pan: {
                            mode: 'x',
                            enabled: true
                        },
                        zoom: {
                            wheel: {
                                enabled: true
                            },
                            pinch: {
                                enabled: false
                            },
                            mode: 'x'
                        }
                    }
                },
                scales: {
                    x: {
                        type: "time",
                        offset: true,
                        time: {
                            unit: 'day'
                        }
                    },
                    y: {
                        min: -1,
                        max: 1,
                        grid: {
                            display: true,
                            lineWidth: 2,
                            color: context => context.tick.value == 0 ? "rgba(0, 0, 0, 0.1)" : "rgba(0, 0, 0, 0)"
                        },
                        ticks: {
                            display: false
                        }
                    }
                }
            }
        });

        $("#chartResetZoom").click(function() {
            caseHistoryChart.resetZoom();
        });

        function loadUniqueCaseData(fnn, siiamCasesUnique, serviceCentralIncidentsUnique) {
            let includeEmpty = $("#includeEmpty").prop("checked");

            let queryData = {
                fnn: fnn,
                sort: "createdOn",
                sortOrder: "desc"
            };

            if (startDatePicker && Array.isArray(startDatePicker.selectedDates) && startDatePicker.selectedDates[0] instanceof Date) {
                queryData.startDate = startDatePicker.selectedDates[0].toISOString();
            }

            if (endDatePicker && Array.isArray(endDatePicker.selectedDates) && endDatePicker.selectedDates[0] instanceof Date) {
                queryData.endDate = endDatePicker.selectedDates[0].toISOString();
            }

            if (!includeEmpty) {
                if (siiamCasesUnique.length > 0) {
                    queryData.siiamCases = true;
                }
                if (serviceCentralIncidentsUnique.length > 0) {
                    queryData.serviceCentralIncidents = true;
                }
            }

            httpApiGetAllResults("/serviceCheck/history", queryData, [], function(err, serviceCheckRecords) {
                if (err) {
                    console.error(err);
                    return;
                }
                updateCaseHistoryChart(caseHistoryChart, fnn, siiamCasesUnique, serviceCentralIncidentsUnique, serviceCheckRecords);
            });
        }
    });

    function setupQueryDataFromFilter(filter) {
        let limit = filter.pageSize;
        let offset = (filter.pageIndex - 1) * filter.pageSize;
        let includeEmpty = $("#includeEmpty").prop("checked");

        let queryData = {};

        queryData.limit = limit;
        queryData.offset = offset;
        queryData.includeEmpty = includeEmpty;

        if (filter.sortField) {
            queryData.sort = filter.sortField;
            queryData.order = filter.sortOrder;
        }

        if (startDatePicker && Array.isArray(startDatePicker.selectedDates) && startDatePicker.selectedDates[0] instanceof Date) {
            queryData.startDate = startDatePicker.selectedDates[0].toISOString();
        }

        if (endDatePicker && Array.isArray(endDatePicker.selectedDates) && endDatePicker.selectedDates[0] instanceof Date) {
            queryData.endDate = endDatePicker.selectedDates[0].toISOString();
        }

        ["fnn", "serviceType"].forEach(field => {
            if (filter[field]) {
                queryData[field] = filter[field];
            }
        });

        return queryData;
    }

    function httpApiGetAllResults(url, parameters, resultList, callback) {
        // Goes through the response from a REST API and continues to the next pagination link if available
        $.ajax({
            url: url,
            type: "GET",
            data: parameters,
            cache: false,
            success: function(response) {
                try {
                    resultList.push(...response.results);

                    if (response.metadata.pagination.next === null) {
                        callback(null, resultList);
                    } else {
                        httpApiGetAllResults(response.metadata.pagination.next, null, resultList, callback);
                    }
                } catch(e) {
                    callback(e, null);
                }
            },
            error: function (xhr, status, e) {
                callback(e, null);
            }
        });
    }

    function updateCaseHistoryChart(chart, fnn, siiamCasesList, serviceCentralIncidentsList, serviceCheckRecords) {
        let datasetBySiiamCases = {};
        let datasetByServiceCentralIncidents = {};
        let datasets = [];

        if (serviceCheckRecords.length > 0) {
            serviceCheckRecords.forEach(serviceCheckRecord => {
                if (!(serviceCheckRecord.siiamCases in datasetBySiiamCases)) {
                    datasetBySiiamCases[serviceCheckRecord.siiamCases] = [];
                }

                if (!(serviceCheckRecord.serviceCentralIncidents in datasetByServiceCentralIncidents)) {
                    datasetByServiceCentralIncidents[serviceCheckRecord.serviceCentralIncidents] = [];
                }

                let recordCreatedOn = new Date(serviceCheckRecord.createdOn).toISOString();
                datasetBySiiamCases[serviceCheckRecord.siiamCases].push({
                    x: new Date(recordCreatedOn),
                    y: 0,
                    id: serviceCheckRecord.id
                });

                datasetByServiceCentralIncidents[serviceCheckRecord.serviceCentralIncidents].push({
                    x: new Date(recordCreatedOn),
                    y: 0,
                    id: serviceCheckRecord.id
                });
            });

            let colourIndex = 0;

            for (let i in siiamCasesList) {
                let colour = getLineColour(colourIndex);
                colourIndex++;

                datasets.push({
                    label: `SIIAM: ${JSON.stringify(siiamCasesList[i])}`,
                    data: datasetBySiiamCases[siiamCasesList[i]],
                    backgroundColor: `rgba(${colour[0]}, ${colour[1]}, ${colour[2]}, 0.4)`,
                    pointRadius: 5,
                    pointHoverRadius: 10
                });
            }

            for (let i in serviceCentralIncidentsList) {
                let colour = getLineColour(colourIndex);
                colourIndex++;

                datasets.push({
                    label: `Service Central: ${JSON.stringify(serviceCentralIncidentsList[i])}`,
                    data: datasetByServiceCentralIncidents[serviceCentralIncidentsList[i]],
                    backgroundColor: `rgba(${colour[0]}, ${colour[1]}, ${colour[2]}, 0.4)`,
                    pointStyle: 'rect',
                    pointRadius: 6,
                    pointHoverRadius: 12
                });
            }
        }

        chart.data.datasets = datasets;
        chart.options.plugins.title.text = `Service Check Record History for FNN: ${fnn}`;
        chart.resetZoom();
        chart.update();
    }


    function getLineColour(index) {
        let shades = 4;
        let baseColours = [[238, 46, 46], [0, 140, 72], [24, 90, 170], [244, 125, 35], [102,44, 145], [162, 30, 34], [180, 56, 148]];
        let totalColours = baseColours.length;

        let colourIndex = index % totalColours;
        let colourShade = Math.floor((index / totalColours) % shades);

        let lineColour = baseColours[colourIndex];

        for (var i = 0; i < shades; i++) {
            lineColour[i] += Math.floor(((256 - lineColour[i])*colourShade)/shades);
        }

        return lineColour;
    }
</script>

<%- include('footer', {}); %>