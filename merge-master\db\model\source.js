//source schema
//v1

import mongoose from 'mongoose';

import regex from '../../modules/helpers/regex.js';

const sourceSchema = new mongoose.Schema({
    name: { type: String, default: null, validate: regex.sourceName, unique: true, required: true, immutable: true },
    active: { type: Boolean, default: false },
    hideInResult: { type: Boolean, default: false },
    includedInitialServiceCheck: { type: Boolean, default: false },
    title: { type: String, validate: regex.sourceTitle, default: '' },
    level: { type: Number, default: 0, min: 0, max: 6 },
    description: { type: String, default: '' },
    wikiPage: { type: String, default: '' },
    suite: {
        type: [{
            type: String,
            enum: [
                'standard',
                'andig',
                'ipvoice',
                'outageInfo',
                'wholesale'
            ]
        }],
        default: ['standard'],
        validate: {
            validator: function(suite) {
                return suite.length === new Set(suite).size;
            },
            message: 'array should only contain unique string values'
        }
    },
    api: { type: String, default: null, required: true },
    preSources: {
        type: [{
            type: String,
            validate: regex.sourceName
        }],
        default: [],
        validate: {
            validator: function(preSources) {
                return preSources.length === new Set(preSources).size;
            },
            message: 'array should only contain unique string values'
        }
    },
    preSourcesRunOnFail: { type: Boolean, default: false },
    preRules: {
        type: [{
            type: String,
            validate: regex.ruleName
        }],
        default: [],
        validate: {
            validator: function(preRules) {
                return preRules.length === new Set(preRules).size;
            },
            message: 'array should only contain unique string values'
        }
    },
    preRulesRunOnFail: { type: Boolean, default: false },
    runWhenDependenciesResolved: { type: Boolean, default: false },
    preCondition: { type: String, default: '' },
    param: { type: String, default: '' },
    overrideErrorCondition: { type: String, default: '' },
    errorMessage: { type: String, default: '' },
    createdBy: { type: String, required: true, immutable: true, default: 'unknown' },
    createdOn: { type: Date, required: true, immutable: true, default: Date.now }
});


sourceSchema.methods.toJSON = function() {
    let obj = this.toObject();
    delete obj._id;
    delete obj.__v;
    return obj;
}


export default mongoose.model('source', sourceSchema);