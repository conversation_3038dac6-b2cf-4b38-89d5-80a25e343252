<%- include("header", {title: "Merge Config Upload"}) %>
<%- include("jsonEditInclude", {}) %>
<%- include("menu", {currentTab: "Form"}); %>
<link rel="stylesheet" type="text/css" href="/public/stylesheets/select2.min.css">
<script src="/public/javascripts/select2.min.js" crossorigin="anonymous"></script>
<div class="container">
    <br>
    <div class="jumbotron py3">
        <h1 class="display-4">Configuration Upload</h1>
        <br>
        <h6>This page allows uploading a JSON file to replace all APIs / rules and sources / text templates.</h6>
        <br>
        <div id="readonlyAlert" style="display:none;" class="alert alert-warning">Warning: all configurations are read only, upload is not available.</div>
        <label for="uploadConfigName">Config name:</label>
        <select id="uploadConfigName">
            <option></option>
            <option value="api">APIs</option>
            <option value="messagebucket">Message Buckets</option>
            <option value="rulesource">Rules / Sources</option>
            <option value="outcome">Outcomes</option>
            <option value="template">Text Templates</option>
        </select>
        <p id="uploadNote"></p>
        <label id="uploadFormat"></label>
        <pre class="border border-info bg-light" id="uploadExample" style="display:none;"></pre>
        <div><label class="btn btn-secondary"><input type="file" id="uploadFileSelect" disabled></label></div>
        <button id="uploadSubmit" class="btn btn-success" disabled>Upload</button>
    </div>
    <div>
        <div id="uploadConfigAlert" style="display:none" class="alert alert-success"></div>
    </div>
    <br>
    <label for="uploadProgressBar">Progress:</label>
    <div class="progress">
        <div id="uploadProgressBar" class="progress-bar progress-bar-striped active" role="progressbar" style="width:0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
    </div>

    <div id="uploadResultModal" class="modal fade" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-xlg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Config upload</h5>
                </div>
                <div class="modal-body">
                    <div id="uploadResultModalMessage"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" data-dismiss="modal" class="btn btn-outline-secondary">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        const disableRuleSourceEditing = <%- user.isAdmin ? false : global.gConfig.disableRuleSourceEditing %>;

        const configKeysAll = Object.freeze({
            api: ["apis"],
            messagebucket: ["messageBuckets"],
            rulesource: ["rules", "sources"],
            outcome: ["outcomes"],
            template: ["textTemplates"]
        });

        const uriNames = Object.freeze({
            apis: "apis",
            messageBuckets: "messageBuckets",
            rules: "rules",
            sources: "sources",
            outcomes: "outcomes",
            textTemplates: "templates"
        });

        const configKeyLabel = Object.freeze({
            apis: "APIs",
            messageBuckets: "Message Buckets",
            rules: "Rules",
            sources: "Sources",
            outcomes: "Outcomes",
            textTemplates: "Text Templates"
        });

        $(document).ready(function() {
            $("#uploadConfigName").prop("disabled", disableRuleSourceEditing);

            if (disableRuleSourceEditing) {
                $("#readonlyAlert").show();
            }

            $("#uploadConfigName").select2({
                minimumResultsForSearch: -1,
                placeholder: "select config",
                width: "16em"
            });

            $("#uploadConfigName").on("select2:select", function (e) {
                let configName = $("#uploadConfigName").val();
                let displayName = $("#uploadConfigName option:selected").text();

                const uploadExample = Object.freeze({
                    api: `{
    "version": 100,
    "apis": [{
        "name": "COMMPilot",
        ...
    }]
}`,
                    messagebucket: `{
    "version": 100,
    "messageBuckets": [{
        "name": "Customer_or_Telstra_CT",
        ...
    }]
}`,
                    rulesource: `{
    "version": 100,
    "rules": [{
        "name": "MDR001",
        ...
    }],
    "sources": [{
        "name": "MAGPIE",
        ...
    }]
}`,
                    outcome: `{
    "version": 100,
    "outcomes": [{
        "name": "net001",
        ...
    }]
}`,
                    template: `{
    "version": 100,
    "textTemplates": [{
        "name": "FOHNewIncident",
        ...
    }]
}`
                });

                $("#uploadNote").text(`Note: ${displayName} uploaded from this page will overwrite ALL other ${displayName} in the database. Any ${displayName} not present in the file will be deleted from the database.`);
                $("#uploadFormat").text(`${displayName} config file JSON format`);
                $("#uploadExample").text(uploadExample[configName]);
                $("#uploadExample").css({ display: "block" });

                $("#uploadFileSelect").prop("disabled", false);
                $("#uploadSubmit").prop("disabled", false);

                $("#uploadFileSelect").trigger("change");
            });

            let metadataVersion = null;
            let uploadInstances = {};

            $("#uploadFileSelect").change(function() {
                let configName = $("#uploadConfigName").val();

                if (this.files.length > 0) {
                    const fileReader = new FileReader();
                    fileReader.onload = function(event) {
                        let uploadFileConfig = null;
                        try {
                            uploadFileConfig = JSON.parse(event.target.result);
                            changeAlertBox("Config file contains valid JSON", error=false);
                        } catch(err) {
                            changeAlertBox("Warning: Could not parse JSON in config file", error=true);
                        }

                        if (uploadFileConfig) {
                            uploadInstances = {};
                            let numInvalid = {};

                            if (!Number.isInteger(uploadFileConfig.version) || uploadFileConfig.version <= 0) {
                                metadataVersion = null;
                                changeAlertBox("Warning: 'version' key in JSON file does not exist or is not an integer above 0", error=true);
                            } else {
                                metadataVersion = uploadFileConfig.version;

                                let configKeys = configKeysAll[configName];
                                let displayMessages = [];

                                configKeys.forEach(key => {
                                    numInvalid[key] = 0;

                                    if (Array.isArray(uploadFileConfig[key])) {
                                        uploadInstances[key] = {};

                                        uploadFileConfig[key].forEach(instance => {
                                            if (instance.name && !(instance.name in uploadInstances[key])) {
                                                uploadInstances[key][instance.name] = instance;
                                            } else {
                                                numInvalid[key] += 1;
                                            }
                                        });

                                        displayMessages.push(`${configKeyLabel[key]} to upload: ${Object.keys(uploadInstances[key]).length}`);

                                        if (numInvalid[key]) {
                                            displayMessages.push(`Warning: ${numInvalid[key]} ${configKeyLabel[key].toLower()} did not contain a name or are a duplicate of another, these will not be uploaded.`);
                                        }
                                    } else {
                                        displayMessages.push(`Warning: JSON in configuration file does not contain array for key "${key}"`);
                                    }
                                });

                                let countMessage = displayMessages.join(" | ");

                                changeAlertBox(countMessage, error=false);
                            }
                        } else {
                            changeAlertBox("Warning: empty contents in file", error=true);
                        }
                    }
                    fileReader.readAsText(this.files[0]);
                }
            });

            $("#uploadSubmit").click(function() {
                let configName = $("#uploadConfigName").val();

                let existingInstancesByKey = {};
                let uploadStats = {};

                if (!configName) {
                    $("#uploadResultModalMessage").text("Please select a configuration type to upload.");
                    $("#uploadResultModal").modal("show");
                    return;
                }

                let configKeys = configKeysAll[configName];
                let numTotalTasks = 0;
                let promises = [];

                for (let i = 0; i < configKeys.length; i++) {
                    let key = configKeys[i];

                    if (!uploadInstances[key]) {
                        $("#uploadResultModalMessage").text(`Warning: JSON in configuration file does not contain array for key "${key}"`);
                        $("#uploadResultModal").modal("show");
                        return;
                    }

                    let uriName = uriNames[key];
                    let promise = getAllInstances(`/${uriName}`);
                    promises.push(promise);

                };

                $.when(...promises).done(function(...existingInstancesLists) {
                    for (let i = 0; i < configKeys.length; i++) {
                        let key = configKeys[i];
                        let existingInstances = {};
                        if (existingInstancesLists[i] && existingInstancesLists[i].length) {
                            existingInstances = Object.assign(...existingInstancesLists[i].map(instance => ({ [instance.name]: instance })));
                        }

                        let nameSet = new Set();

                        for (const [name, instance] of Object.entries(uploadInstances[key])) {
                            nameSet.add(name);
                        };

                        for (const [name, instance] of Object.entries(existingInstances)) {
                            nameSet.add(name);
                        };

                        numTotalTasks += nameSet.size;
                        existingInstancesByKey[key] = existingInstances;
                        uploadStats[key] = {
                            updated: 0,
                            created: 0,
                            deleted: 0,
                            errors: {}
                        };
                    }

                    if (!numTotalTasks) {
                        $("#uploadResultModalMessage").text(`No items detected for upload, ensure a valid ${configName} configuration file is selected.`);
                        $("#uploadResultModal").modal("show");
                    } else if (!metadataVersion) {
                        $("#uploadResultModalMessage").text("No valid 'version' key in JSON detected, ensure object contains a positive integer 'version' key.");
                        $("#uploadResultModal").modal("show");
                    } else {
                        $(`#uploadSubmit`).prop("disabled", true);
                        $(`#uploadSubmit`).html($("<span>").addClass("spinner-border spinner-border-sm").attr("role", "status").attr("aria-hidden", "true"));

                        updateProgressBar(numTotalTasks, true);

                        $.ajax({
                            type: "POST",
                            url: "/config/metadata",
                            contentType: "application/json",
                            timeout: 5000,
                            data: JSON.stringify({
                                configName: configName,
                                version: metadataVersion
                            }),
                            error: function(err) {
                                alert("Error updating metadata, status: " + err.statusText);
                            }
                        });

                        configKeys.forEach(key => {
                            let uriName = uriNames[key];
                            // Empty ajax request to return a promise that will resolve instantly
                            let deferred = $.ajax();

                            for (const [name, instance] of Object.entries(uploadInstances[key])) {
                                if (Object.keys(existingInstancesByKey[key]).includes(name)) {
                                    deferred = deferred.then(function() {
                                        return $.ajax({
                                            type: "PUT",
                                            url: `/${uriName}/${name}`,
                                            contentType: "application/json",
                                            dataType: "json",
                                            data: JSON.stringify(instance),
                                            timeout: 10000,
                                            success: function (response) {
                                                uploadStats[key].updated += 1;
                                            }
                                        }).catch(function(xhr, status, err) {
                                            // This catches exceptions from the AJAX request itself such as timeout
                                            if (xhr && xhr.responseText) {
                                                uploadStats[key].errors[name] = `${xhr.status} ${err}: ${xhr.responseText}`;
                                            } else {
                                                uploadStats[key].errors[name] = err;
                                            }
                                        }).always(function() {
                                            let isComplete = updateProgressBar();
                                            if (isComplete) {
                                                showCompletedUpload(configName, configKeys, uploadStats);
                                            }
                                        });
                                    });
                                } else {
                                    deferred = deferred.then(function() {
                                        return $.ajax({
                                            type: "POST",
                                            url: `/${uriName}/`,
                                            contentType: "application/json",
                                            dataType: "json",
                                            data: JSON.stringify(instance),
                                            timeout: 10000,
                                            success: function (response) {
                                                uploadStats[key].created += 1;
                                            },
                                            error: function (err) {
                                                uploadStats[key].errors[name] = err.responseText;
                                            }
                                        }).catch(function(xhr, status, err) {
                                            // This catches exceptions from the AJAX request itself such as timeout
                                            if (xhr && xhr.responseText) {
                                                uploadStats[key].errors[name] = `${xhr.status} ${err}: ${xhr.responseText}`;
                                            } else {
                                                uploadStats[key].errors[name] = err;
                                            }
                                        }).always(function() {
                                            let isComplete = updateProgressBar();
                                            if (isComplete) {
                                                showCompletedUpload(configName, configKeys, uploadStats);
                                            }
                                        });
                                    });
                                }
                            }

                            for (const [name, instance] of Object.entries(existingInstancesByKey[key])) {
                                if (!Object.keys(uploadInstances[key]).includes(name)) {
                                    deferred = deferred.then(function() {
                                        $.ajax({
                                            type: "DELETE",
                                            url: `/${uriName}/${name}`,
                                            contentType: "application/json",
                                            timeout: 10000,
                                            success: function (response) {
                                                uploadStats[key].deleted += 1;
                                            },
                                            error: function (err) {
                                                uploadStats[key].errors[name] = err.responseText;
                                            }
                                        }).catch(function(xhr, status, err) {
                                            // This catches exceptions from the AJAX request itself such as timeout
                                            if (xhr && xhr.responseText) {
                                                uploadStats[key].errors[name] = `${xhr.status} ${err}: ${xhr.responseText}`;
                                            } else {
                                                uploadStats[key].errors[name] = err;
                                            }
                                        }).always(function() {
                                            let isComplete = updateProgressBar();
                                            if (isComplete) {
                                                showCompletedUpload(configName, configKeys, uploadStats);
                                            }
                                        });
                                    });
                                }
                            }
                        });
                    }
                });
            });
        });

        function changeAlertBox(message, error=false, hide=false) {
            $("#uploadConfigAlert").text(message);

            if (error) {
                $("#uploadConfigAlert").removeClass("alert-success").addClass("alert-danger");
            } else {
                $("#uploadConfigAlert").removeClass("alert-danger").addClass("alert-success");
            }

            if (hide) {
                $("#uploadConfigAlert").hide();
            } else {
                $("#uploadConfigAlert").show();
            }
        }

        function updateProgressBar(valueMax=0, reset=false) {
            let isComplete = false;

            if (reset) {
                $("#uploadProgressBar").width("0%");
                $("#uploadProgressBar").attr('aria-valuenow', 0);
                $("#uploadProgressBar").attr('aria-valuemax', valueMax);
                $("#uploadProgressBar").addClass("progress-bar-animated");
            } else {
                let valueNow = parseInt($("#uploadProgressBar").attr('aria-valuenow')) + 1;
                $("#uploadProgressBar").attr('aria-valuenow', valueNow);
                let valueMax = parseInt($("#uploadProgressBar").attr('aria-valuemax'));
                let percentage = valueNow / valueMax * 100;
                $("#uploadProgressBar").width(`${percentage}%`);

                if (valueNow === valueMax) {
                    $("#uploadProgressBar").removeClass("progress-bar-animated");
                    isComplete = true;
                }
            }

            return isComplete;
        }

        function showCompletedUpload(configName, configKeys, uploadStats) {
            let showErrorContents = $("<div>").attr("id", "errorCollapse").attr("class", "collapse");
            let hasErrors = false;

            $("#uploadResultModalMessage").html("Upload complete<br>");

            configKeys.forEach(key => {
                $("#uploadResultModalMessage").append(`Updated ${configKeyLabel[key]}: ${uploadStats[key].updated}<br>`);
                $("#uploadResultModalMessage").append(`Created ${configKeyLabel[key]}: ${uploadStats[key].created}<br>`);
                $("#uploadResultModalMessage").append(`Deleted ${configKeyLabel[key]}: ${uploadStats[key].deleted}<br>`);

                if (!$.isEmptyObject(uploadStats[key].errors)) {
                    hasErrors = true;

                    showErrorContents.append($("<label>").text(configKeyLabel[key]));
                    showErrorContents.append($("<pre>").attr("class", "border border-info").text(JSON.stringify(uploadStats[key].errors, null, 4)));
                }
            });

            if (hasErrors) {
                $("#uploadResultModalMessage").append($("<a>").attr("href", "#").attr("data-toggle", "collapse").attr("data-target", "#errorCollapse").text("View Errors"));
                $("#uploadResultModalMessage").append(showErrorContents);
            }

            $("#uploadResultModal").modal("show");

            $(`#uploadSubmit`).prop("disabled", false);
            $(`#uploadSubmit`).text("Upload");
        };

        function getAllInstances(url) {
            let deferred = $.Deferred();
            let instances = [];

            queryApiWithPagination(deferred, instances, url);

            return deferred.promise();
        }

        function queryApiWithPagination(deferred, results, url) {
            $.ajax({
                cache: false,
                type: "GET",
                url: url,
                success: function(response) {
                    results.push(...response.results);

                    if (response.metadata && response.metadata.pagination && response.metadata.pagination.next) {
                        queryApiWithPagination(deferred, results, response.metadata.pagination.next);
                    } else {
                        deferred.resolve(results);
                    }
                },
                error: function(xhr, status, e) {
                    console.error(`Error with obtaining data from ${url}`);
                    deferred.reject(e);
                }
            });
        }
    </script>
</div>
</body>
</html>
