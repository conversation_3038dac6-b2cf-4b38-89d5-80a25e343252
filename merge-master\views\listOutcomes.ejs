
<%- include('header', { title: "Merge Service Check Outcomes Edit" }) %>
<%- include('menu', { currentTab: 'Form' }); %>
<script type="text/javascript" src="/public/javascripts/jsgrid.min.js"></script>
<link rel="stylesheet" type="text/css" href="/public/stylesheets/jsgrid.min.css">
<link rel="stylesheet" type="text/css" href="/public/stylesheets/jsgrid-theme.min.css">
<style>
    tr.jsgrid-highlight-row td.jsgrid-cell {
        background: #c4e2ff;
        border-color: #c4e2ff;
    }
</style>
<script type="text/javascript" src="/public/javascripts/jsgrid.min.js"></script>
<div class="container d-flex flex-grow-1 flex-column">
    <div class="d-inline">
        <br>
        <div class="jumbotron py-3">
            <span>Current version is <%- metadata && metadata.version ? `v${metadata.version}` : 'unknown' %> last updated on <%- metadata && metadata.updatedOn ? metadata.updatedOn : 'unknown' %> by <%- metadata && metadata.updatedBy ? metadata.updatedBy : 'unknown' %></span>
        </div>
        <div class="row">
            <div class="col-md-6">
                <button id="createOutcome" class="btn btn-primary" onclick="window.location.href='/edit/create/outcome'" disabled>Create Outcome</button>
            </div>
            <div class="col-md-6">
                <div class="float-right">
                    <p id="displayOutcomeCount"></p>
                </div>
            </div>
        </div>
        <br>
    </div>
    <div id="outcomesGrid" class="flex-grow-1"></div>
</div>
<script>
    const disableRuleSourceEditing = <%- user.isAdmin ? false : global.gConfig.disableRuleSourceEditing %>;

    $(document).ready(function() {
        let gridColumns =  [
            { name: "name", title: "Name", type: "text", width: "24em", itemTemplate: function(value) {
                return $("<a>").attr("href", `/edit/outcomes/${encodeURIComponent(value)}`).attr("target", "_Merge_Outcomes").text(value);
            }},
            { name: "title", title: "Title", width: "auto", type: "text", itemTemplate: function(value) {
                return $("<div>").text(value).html();
            }},
            { name: "description", title: "Description", width: "auto", type: "text", itemTemplate: function(value) {
                return $("<div>").text(value).html();
            }}
        ];

        if (!disableRuleSourceEditing) {
            $("#createOutcome").prop('disabled', false);
            gridColumns.push({ type: "control", width: "60px", editButton: false, modeSwitchButton: false });
        }

        $("#outcomesGrid").jsGrid({
            width: "100%",
            height: "100%",
            filtering: true,
            inserting: false,
            editing: false,
            sorting: true,
            autoload: true,
            paging: true,
            pageLoading: true,
            pageSize: 50,
            pageIndex: 1,
            controller: {
                loadData: function(filter) {
                    var data = $.Deferred();

                    let queryData = setupQueryDataFromFilter(filter);
                    $.ajax({
                        cache: false,
                        type: "GET",
                        url: "/outcomes",
                        data: queryData,
                        success: function (response) {
                            $("#displayOutcomeCount").text(`${response.metadata.pagination.total} outcomes displayed`);
                            data.resolve({
                                data: response.results,
                                itemsCount: response.metadata.pagination.total
                            });
                        },
                        error: function (xhr, status, e) {
                            // Not the best solution, puts nothing in the table
                            data.resolve({
                                data: [],
                                itemsCount: 0
                            });
                        }
                    });

                    return data.promise();
                },
                deleteItem: function(item) {
                    return $.ajax({
                        type: "DELETE",
                        url: `/outcomes/${encodeURIComponent(item.name)}`
                    });
                }
            },
            fields: gridColumns
        });
    });

    function setupQueryDataFromFilter(filter) {
        let limit = filter.pageSize;
        let offset = (filter.pageIndex - 1)*filter.pageSize;

        let queryData = {};

        if (filter.sortField) {
            queryData.sort = filter.sortField;
            queryData.order = filter.sortOrder;
        }

        ["name", "title", "description"].forEach(field => {
            if (filter[field]) {
                queryData[field] = {
                    like: filter[field]
                };
            }
        });

        queryData.limit = limit;
        queryData.offset = offset;

        return queryData;
    }
</script>
</body>
</html>
