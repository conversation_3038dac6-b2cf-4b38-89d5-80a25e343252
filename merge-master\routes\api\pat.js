'use strict';

import axios from 'axios';
import express from 'express';
import { param, body, validationResult } from 'express-validator';
import https from 'https';
import _ from 'lodash';
import url from 'url';
import urlJoin from 'url-join';

import logger from '../../modules/logger.js';

const mecBaseUrl = global.gConfig.APIUriConfig?.MergeExternalCollectorAPI?.baseURI;
const mecUsername = global.gConfig.APIHeaderConfig?.mergeExternalCollectorUsername;
const mecPassword = global.gConfig.APIHeaderConfig?.mergeExternalCollectorPassword;


const router = express.Router();


/**
 * @swagger
 * components:
 *   schemas:
 *     patScheduleInput:
 *       type: object
 *       properties:
 *         scheduleType:
 *           type: string
 *           description: PAT schedule type
 *           enum: [CIDN, FNNs]
 *         cidn:
 *           type: string
 *           description: CIDN input for CIDN schedule type, must not be empty with the CIDN schedule type
 *         fnn:
 *           type: array
 *           description: FNN array input for FNNs schedule type, must contain at least one element with the FNNs schedule type, where each FNN must be unique
 *           items:
 *             type: string
 *         auditTypes:
 *           type: array
 *           description: Audit types to use, each element must be unique
 *           items:
 *             type: string
 *             enum: [carriage, CNSDI Audit, config, device, EWS Dashboard, nonMDN]
 *         customerConfig:
 *           type: string
 *           description: Customer config string to include with audit job
 *         amdocsUser:
 *           type: string
 *           description: Amdocs user string to include with audit queue documents
 *         priority:
 *            type: integer
 *            enum:
 *              - 0
 *              - 1
 *              - 2
 *            description: Priority for job in PAT between 0 and 2 inclusive (0 is normal, 1 is high, 2 is very high)
 *         amdocsDeviceName:
 *           type: array
 *           description: AMDOCS device name array input for device names to associate with each FNN (only used in FNNs audit type)
 *           items:
 *             type: string
 *         productName:
 *           type: string
 *           description: Product name string, stored with each FNN entry for the FNNs audit type
 *         productGroupName:
 *           type: string
 *           description: Product group name string, stored with each FNN entry for the FNNs audit type
 *         orderType:
 *           type: string
 *           description: Order type string, stored with each FNN entry for the FNNs audit type
 *         sdwanFeatures:
 *           type: string
 *           description: Management tier string, stored with each FNN entry for the FNNs audit type
 *         ncpId:
 *           type: string
 *           description: NCP ID string, stored with each FNN entry for the FNNs audit type
 *         managementTier:
 *           type: string
 *           description: Management tier string, stored with each FNN entry for the FNNs audit type
 *         ncoId:
 *           type: string
 *           description: NCO ID string, stored with each FNN entry for the FNNs audit type
 *       required:
 *         - scheduleType
 *         - auditTypes
 */


/**
 * @swagger
 * /api/pat:
 *   post:
 *     summary: Schedules a task in PAT
 *     description: Schedules a CIDN / FNNs type task for auditing in PAT
 *     security:
 *       - MergeToken: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/x-www-form-urlencoded:
 *           schema:
 *             $ref: '#/components/schemas/patScheduleInput'
 *           encoding:
 *             fnn:
 *               style: form
 *               explode: true
 *             auditTypes:
 *               style: form
 *               explode: true
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/patScheduleInput'
 *           examples:
 *             CIDN schedule type:
 *               value:
 *                 scheduleType: CIDN
 *                 auditTypes:
 *                 - carriage
 *                 - config
 *                 - device
 *                 cidn: CIDN_Number
 *             CIDN schedule type with customer config:
 *               value:
 *                 scheduleType: CIDN
 *                 auditTypes:
 *                 - carriage
 *                 - config
 *                 - device
 *                 cidn: CIDN_Number
 *                 customerConfig: Customer_config_string
 *             FNNs schedule type:
 *               value:
 *                 scheduleType: FNNs
 *                 auditTypes:
 *                 - carriage
 *                 - config
 *                 - device
 *                 fnn:
 *                 - N0000001R
 *                 - N0000002R
 *                 - N0000003R
 *             FNNs schedule type with AMDOCS user:
 *               value:
 *                 scheduleType: FNNs
 *                 auditTypes:
 *                 - carriage
 *                 - config
 *                 - device
 *                 fnn:
 *                 - N0000001R
 *                 - N0000002R
 *                 - N0000003R
 *                 amdocsUser: d123456
 *             FNNs schedule type with AMDOCS user, very high priority:
 *               value:
 *                 scheduleType: FNNs
 *                 auditTypes:
 *                 - carriage
 *                 - config
 *                 - device
 *                 fnn:
 *                 - N0000001R
 *                 - N0000002R
 *                 - N0000003R
 *                 amdocsUser: d123456
 *                 priority: 2
 *             FNNs schedule type with all fields set:
 *               value:
 *                 scheduleType: FNNs
 *                 auditTypes:
 *                 - carriage
 *                 - config
 *                 - device
 *                 fnn:
 *                 - N0000001R
 *                 - N0000002R
 *                 - N0000003R
 *                 customerConfig: customer config string
 *                 amdocsUser: d123456
 *                 priority: 2
 *                 amdocsDeviceName:
 *                 - ABCDEFGR01C01
 *                 - ABCDEFGR01C02
 *                 - ABCDEFGR01C03
 *                 productName: MDN Managed Router
 *                 productGroupName: 01 - MDN and Devices
 *                 orderType: Provide
 *                 ncpId: NCP-ID1234
 *                 sdwanFeatures: Yes
 *                 managementTier: IOM Dedicated
 *                 ncoId: NCO-12345678
 *     responses:
 *       202:
 *         description: An object with a number jobId field representing the jobId of the scheduled PAT job
 *         content:
 *           application/json:
 *             schema:
 *                type: object
 *                properties:
 *                  jobId:
 *                    type: number
 *                    description: Job ID in PAT
 *                    example: 1234
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Not authenticated
 *       403:
 *         description: Forbidden
 *       500:
 *         description: Internal server error
 *     tags:
 *       - PAT
 */
router.post('/', [
    body('scheduleType').isIn(['CIDN', 'FNNs']).withMessage("scheduleType should one of the values in ('CIDN', 'FNNs')"),
    body('cidn').default('').isString().trim().isLength({ max: 45 }),
    body('fnn').toArray().optional().isArray({ min: 1 }).custom(param => { return new Set(param).size === param.length; }).withMessage("fnn array should be non-empty and contain unique elements"),
    body('fnn.*').optional().isString().trim().isLength({ min: 1, max: 45 }).withMessage("fnn array elements should all be non-empty strings"),
    body('auditTypes').toArray().isArray({ min: 1 }).custom(param => { return new Set(param).size === param.length; }).withMessage("auditTypes array should be non-empty and contain unique elements"),
    body('auditTypes.*').optional().isIn(['carriage', 'CNSDI Audit', 'config', 'device', 'EWS Dashboard', 'nonMDN']).withMessage("auditTypes elements should one of the values in ('carriage', 'CNSDI Audit', 'config', 'device', 'EWS Dashboard', 'nonMDN')"),
    body('customerConfig').default('').isString().isLength({ max: 4000 }),
    body('amdocsUser').optional({ nullable: true }).isString().isLength({ max: 50 }),
    body('priority').default(0).isInt({ min: 0, max: 2 }).withMessage("priority should be a number between 0 and 2"),
    body('amdocsDeviceName').toArray().optional().isArray({ min: 0 }).withMessage("AMDOCS device name should be an array of strings"),
    body('amdocsDeviceName.*').optional({ nullable: true }).isString().trim().isLength({ min: 0, max: 255 }).withMessage("AMDOCS device name array elements should all be strings"),
    body('productName').optional({ nullable: true }).isString().isLength({ max: 255 }),
    body('productGroupName').optional({ nullable: true }).isString().isLength({ max: 255 }),
    body('orderType').optional({ nullable: true }).isString().isLength({ max: 255 }),
    body('sdwanFeatures').optional({ nullable: true }).isString().isLength({ max: 10 }),
    body('ncpId').optional({ nullable: true }).isString().isLength({ max: 255 }),
    body('managementTier').optional({ nullable: true }).isString().isLength({ max: 255 }),
    body('ncoId').optional({ nullable: true }).isString().isLength({ max: 50 })
], async function(req, res) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    // Ensures that if the schedule type if CIDN, the CIDN field is present,
    // or if the schedule type is FNNs, the fnn array is present
    // TODO: Could be moved into a custom validator later
    switch(req.body.scheduleType) {
        case "CIDN":
            if (!req.body.cidn) {
                res.status(400).json({ errors: ['CIDN field must not be empty if the scheduleType is "CIDN"'] });
                return;
            }
            break;
        case "FNNs":
            if (!req.body.fnn) {
                res.status(400).json({ errors: ['fnn field must not be empty if the scheduleType is "FNNs"'] });
                return;
            }
            break;
    }

    try {
        const mecUrl = new url.URL(mecBaseUrl);
        mecUrl.pathname = urlJoin(mecUrl.pathname, 'pat');

        const createJobBody = {
            ...req.body,
            username: req.user.username,
        };

        const mecResponse = await axios.post(mecUrl.toString(), createJobBody, {
            validateStatus: () => true,
            headers: {
                Authorization: `Basic ${Buffer.from(`${mecUsername}:${mecPassword}`).toString('base64')}`
            },
            httpsAgent: new https.Agent({ rejectUnauthorized: false }),
            proxy: false,
            timeout: 120000
        });

        res.status(mecResponse.status).send(mecResponse.data);
    } catch(error) {
        logger.error(`PAT API: Error when obtaining results for job id ${jobId}, ${error.toString()}`);
        res.sendStatus(500);
    }
});


/**
 * @swagger
 * /api/pat/{jobId}:
 *   get:
 *     summary: Gets a job from PAT
 *     description: Gets a scheduled job from PAT and any associated FNNs from audit Q
 *     security:
 *       - MergeToken: []
 *     parameters:
 *       - name: jobId
 *         in: path
 *         description: Mandatory field, job ID of the scheduled PAT job
 *         required: true
 *         schema:
 *           type: number
 *           example: 1234
 *     responses:
 *       200:
 *         description: Content is JSON output representing the audit job from PAT and any items for FNNs in the audit Q for sched_type FNNs
 *         content:
 *           application/json:
 *             schema:
 *                type: object
 *                properties:
 *                  id:
 *                    type: number
 *                    example: 1234
 *                  name:
 *                    type: string
 *                    example: Merge 2022-01-01T00:00:00.000Z
 *                  sched_type:
 *                    type: string
 *                    example: FNNs
 *                  cidn:
 *                    type: string
 *                    example: FNNs
 *                  status:
 *                    type: string
 *                    example: Scheduled
 *                    enum:
 *                    - Scheduled
 *                    - Initialising
 *                    - Pending
 *                    - Running
 *                    - Completed
 *                    - Cancelled
 *                    - Paused
 *                    - Completed with Error
 *                    - To be Deleted
 *                    - Deleted
 *                    - Unknown
 *                  dir:
 *                    type: string
 *                    example: ""
 *                  server:
 *                    type: string
 *                    example: ""
 *                  _inserted:
 *                    type: string
 *                    example: FNNs
 *                  _runtime:
 *                    type: string
 *                    example: null
 *                  progress:
 *                    type: number
 *                    example: 0
 *                  num_fnn:
 *                    type: number
 *                    example: 1
 *                  audit_types:
 *                    type: string
 *                    example: carriage,config,device,
 *                  _ended_on:
 *                    type: string
 *                    example: null
 *                  scheduled_by:
 *                    type: string
 *                    example: d123456
 *                  customer_config:
 *                    type: string
 *                    example: customer_config_string
 *                  after_hours:
 *                    type: number
 *                    example: 0
 *                  priority:
 *                    type: number
 *                    example: 2
 *                  audit_run_log:
 *                    type: string
 *                    example: /home/<USER>/data/audit_jobs/10000_Merge.log
 *                  cancelled_by_pat:
 *                    type: string
 *                    example: NO
 *                  audit_q:
 *                    type: array
 *                    items:
 *                      type: object
 *                      properties:
 *                        id:
 *                          type: number
 *                          example: 10001
 *                        job_id:
 *                          type: number
 *                          example: 1234
 *                        process_id:
 *                          type: number
 *                          example: null
 *                        child_id:
 *                          type: number
 *                          example: null
 *                        is_in_Q:
 *                          type: string
 *                          example: Y
 *                        FNN:
 *                          type: string
 *                          example: N1234567R
 *                        status:
 *                          type: string
 *                          example: planned
 *                        network:
 *                          type: string
 *                          example: null
 *                        source:
 *                          type: string
 *                          example: USER
 *                        running_server:
 *                          type: string
 *                          example: null
 *                        inserted_on:
 *                          type: string
 *                          example: 2022-01-01T00:00:00.000Z
 *                        started_on:
 *                          type: string
 *                          example: null
 *                        ended_on:
 *                          type: string
 *                          example: null
 *                        message:
 *                          type: string
 *                          example: null
 *                        amdocs_user:
 *                          type: string
 *                          example: d123456
 *                        priority:
 *                          type: number
 *                          example: 2
 *                        amdocsDeviceName:
 *                          type: string
 *                          example: ABCDEFGR01C01
 *                        productName:
 *                          type: string
 *                          example: MDN Managed Router
 *                        productGroupName:
 *                          type: string
 *                          example: 01 - MDN and Devices
 *                        orderType:
 *                          type: string
 *                          example: Provide
 *                        ncpId:
 *                          type: string
 *                          example: NCP-ID1234
 *                        sdwanFeatures:
 *                          type: string
 *                          example: Yes
 *                        managementTier:
 *                          type: string
 *                          example: IOM Dedicated
 *                        ncoId:
 *                          type: string
 *                          example: NCO-12345678
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Not authenticated
 *       403:
 *         description: Forbidden
 *       500:
 *         description: Internal server error
 *     tags:
 *       - PAT
 */
router.get('/:jobId', [
    param('jobId').isInt({ min: 0 }).toInt()
], async function(req, res) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }
    let jobId = req.params.jobId;

    try {
        const mecUrl = new url.URL(mecBaseUrl);
        mecUrl.pathname = urlJoin(mecUrl.pathname, `pat/${encodeURIComponent(jobId)}`);

        const mecResponse = await axios.get(mecUrl.toString(), {
            validateStatus: () => true,
            headers: {
                Authorization: `Basic ${Buffer.from(`${mecUsername}:${mecPassword}`).toString('base64')}`
            },
            httpsAgent: new https.Agent({ rejectUnauthorized: false }),
            proxy: false,
            timeout: 120000
        });

        res.status(mecResponse.status).send(mecResponse.data);
    } catch(error) {
        logger.error(`PAT API: Error when obtaining results for job id ${jobId}, ${error.toString()}`);
        res.sendStatus(500);
    }
});


/**
 * @swagger
 * /api/pat/{jobId}/results:
 *   get:
 *     summary: Gets results of PAT audit
 *     description: Gets the results of a PAT audit including results run on each FNN
 *     security:
 *       - MergeToken: []
 *     parameters:
 *       - name: jobId
 *         in: path
 *         description: Mandatory field, job ID of the scheduled PAT job
 *         required: true
 *         schema:
 *           type: number
 *           example: 1234
 *     responses:
 *       200:
 *         description: Array of results for each FNN in the PAT audit job
 *         content:
 *           application/json:
 *             schema:
 *                type: array
 *                items:
 *                  type: object
 *                  properties:
 *                    FNN:
 *                      type: string
 *                      example: N1234567R
 *                    started_on:
 *                      type: string
 *                      example: 2022-01-01T00:00:00.000Z
 *                    type:
 *                      type: string
 *                      example: raw
 *                    job_id:
 *                      type: number
 *                      example: 1234
 *                    pat_ver:
 *                      type: string
 *                      example: *******-Dev
 *                    file:
 *                      type: string
 *                      example: ""
 *                    created_on:
 *                      type: string
 *                      example: 2022-01-01T00:00:00.000Z
 *                    overall_audit:
 *                      type: string
 *                      example: PASS
 *                    carriage_audit:
 *                      type: string
 *                      example: PASS
 *                    wireless_backup_audit:
 *                      type: string
 *                      example: PASS
 *                    backend_management_audit:
 *                      type: string
 *                      example: PASS
 *                    router_config_audit:
 *                      type: string
 *                      example: PASS
 *                    device_live:
 *                      type: string
 *                      example: PASS
 *                    speed_check:
 *                      type: string
 *                      example: PASS
 *                    policy_speed_check:
 *                      type: string
 *                      example: PASS
 *                    policy_check:
 *                      type: string
 *                      example: PASS
 *                    rcbu_backup:
 *                      type: string
 *                      example: PASS
 *                    framed_routes:
 *                      type: string
 *                      example: PASS
 *                    framed_routes_unused:
 *                      type: string
 *                      example: PASS
 *                    rmon_alarms:
 *                      type: string
 *                      example: PASS
 *                    rmon_ifindex:
 *                      type: string
 *                      example: PASS
 *                    snmp:
 *                      type: string
 *                      example: PASS
 *                    snmp_host:
 *                      type: string
 *                      example: PASS
 *                    manat:
 *                      type: string
 *                      example: PASS
 *                    error_summary:
 *                      type: string
 *                      example: error summary string
 *                    commission_date:
 *                      type: string
 *                      example: null
 *                    carriage_type:
 *                      type: string
 *                      example: NBN
 *                    rcbu_add_date:
 *                      type: string
 *                      example: 2022-01-01T00:00:00.000Z
 *                    dns:
 *                      type: string
 *                      example: PASS
 *                    bgp:
 *                      type: string
 *                      example: ""
 *                    overall_rmon:
 *                      type: string
 *                      example: PASS
 *                    overall_qos:
 *                      type: string
 *                      example: PASS
 *                    cisco_wireless_backup:
 *                      type: string
 *                      example: PASS
 *                    juniper_wireless_backup:
 *                      type: string
 *                      example: N/A
 *                    nbn_audit:
 *                      type: string
 *                      example: PASS
 *                    nbn_edge_audit:
 *                      type: string
 *                      example: PASS
 *                    nbn_device_audit:
 *                      type: string
 *                      example: PASS
 *                    magpie_data_quality:
 *                      type: string
 *                      example: PASS
 *                    device_name:
 *                      type: string
 *                      example: ABCDEFR01C01
 *                    device_fnn:
 *                      type: string
 *                      example: N1234567R
 *                    wireless_type:
 *                      type: string
 *                      example: Backup
 *                    iom_device_audit:
 *                      type: string
 *                      example: N/A
 *                    iom_dataquality_audit:
 *                      type: string
 *                      example: N/A
 *                    carriage_fnn:
 *                      type: string
 *                      example: AVC000012345678
 *                    overall_data_quality:
 *                      type: string
 *                      example: PASS
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Not authenticated
 *       403:
 *         description: Forbidden
 *       500:
 *         description: Internal server error
 *     tags:
 *       - PAT
 */
router.get('/:jobId/results', [
    param('jobId').isInt({ min: 0 }).toInt()
], async function(req, res) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let jobId = req.params.jobId;

    try {
        const mecUrl = new url.URL(mecBaseUrl);
        mecUrl.pathname = urlJoin(mecUrl.pathname, `pat/${encodeURIComponent(jobId)}/results`);

        const mecResponse = await axios.get(mecUrl.toString(), {
            validateStatus: () => true,
            headers: {
                Authorization: `Basic ${Buffer.from(`${mecUsername}:${mecPassword}`).toString('base64')}`
            },
            httpsAgent: new https.Agent({ rejectUnauthorized: false }),
            proxy: false,
            timeout: 120000
        });

        res.status(mecResponse.status).send(mecResponse.data);
    } catch(error) {
        logger.error(`PAT API: Error when obtaining results for job id ${jobId}, ${error.toString()}`);
        res.sendStatus(500);
    }
});


export default router;
