import { GreenCheckIcon } from '../../components/statusIcon/GreenCheckIcon'
import { OrangeCrossIcon } from '../../components/statusIcon/OrangeCrossIcon'
import { OrangeWarningIcon } from '../../components/statusIcon/OrangeWarningIcon'
import { RedCrossIcon } from '../../components/statusIcon/RedCrossIcon'
import { RedExclamationIcon } from  '../../components/statusIcon/RedExclamationIcon'
import { TestResultStatus } from '../../infrastructure/models'

interface RagStatusProps {
    ragStatus: TestResultStatus
}

export const RagStatus = ({ ragStatus }: RagStatusProps) => {
    const renderIcon = () => {
        switch (ragStatus) {
            case 'OK':
                return <GreenCheckIcon />
            case 'Failed':
                return <RedExclamationIcon />
            case 'Error':
                return <RedCrossIcon />
            case 'Reject':
                return <OrangeCrossIcon />
            case 'Warning':
                return <OrangeWarningIcon />
            default:
                return null // Handle unexpected cases
        }
    }

    return <div>{renderIcon()}</div>
}
