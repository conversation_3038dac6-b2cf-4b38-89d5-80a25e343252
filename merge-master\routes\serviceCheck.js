'use strict';

import axios from 'axios';
import debug from 'debug';
import escapeStringRegexp from 'escape-string-regexp';
import express from 'express';
import { body, param, query, validationResult } from 'express-validator';

import { isValidPhoneNumber, parsePhoneNumber } from 'libphonenumber-js';
import _ from 'lodash';

import Rule from '../db/model/rule.js';
import ServiceCheckModel from '../db/model/serviceCheck.js';
import Source from '../db/model/source.js';
import Template from '../db/model/template.js';
import auth from '../modules/auth.js';
import constants from '../modules/helpers/constants.js';
import logger from '../modules/logger.js';
import mergeRule from '../modules/mergeRule.js';
import pagination from '../modules/pagination.js';
import serviceCheck, { getRecords, runServiceCheckAction } from '../modules/serviceCheck.js';
import textTemplate from '../modules/textTemplate.js';
import validators from '../modules/validators.js';

import { AuthorizationRoles } from '../modules/enumerations.js';
import { NotFoundError, RuleCodeError, RuleNotActionableError, ServiceCheckLockError } from '../modules/error.js';
import { ServiceCheckStreamRead } from '../modules/helpers/csv.js';

const debugMsg = debug('merge:serviceCheckRoute');
const router = express.Router();
/* GET ServiceCheck Form/home page. */
// To update: New UI link (change back to /)
router.get('/new', auth.authorizeForRoles([
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), async function(req, res) {
    res.render('serviceCheck', { title: 'Service Check', user: req.user, manifest: req.app.locals.manifest });
});

// To update: New UI link (change back to /classic)
router.get('/', auth.authorizeForRoles([
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), async function(req, res) {
    res.render('serviceCheckClassic', { title: 'Service Check Classic', user: req.user});
});

router.post('/', auth.authorizeForRoles([
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), [
    body('id').isString().isLength({ min: 1 }).withMessage("id should be a non-empty string"),
], function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    res.render('serviceCheck', { title: 'Service Check', user: req.user, serviceCheckRepeatId: req.body.id });
});


/* GET one Script. */
router.get('/view/:format/:id', auth.authorizeForRoles([
    AuthorizationRoles.readOnly,
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), [
    param('id').isString().isLength({ min: 1 }).trim(),
    param('format').isString().isIn(['html', 'json'])
], async function (req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let inputID = req.params.id;
    let format = req.params.format;

    debugMsg('#> request to view ' + inputID);

    try {
        let serviceCheckRecord = await serviceCheck.findAndPopulate(inputID);

        if (serviceCheckRecord) {
            let disableDisplayMessage = serviceCheck.disableDisplayCondition(serviceCheckRecord, req.user);
            let level = _.get(serviceCheckRecord, ['input', 'level'], 0);

            if (disableDisplayMessage) {
                if (format == 'html') {
                    res.status(403).render('serviceCheckViewError', {
                        fnn: serviceCheckRecord.fnn,
                        errorMessage: [disableDisplayMessage.message, disableDisplayMessage.link].join(' '),
                        user: req.user
                    });
                } else if (format == 'json') {
                    res.status(403).send({
                        error: disableDisplayMessage.message
                    });
                }
            } else if (!auth.checkUserAuthorizedForServiceCheckLevel(req.user, level)) {
                let errorMessage = `You do not have access to view a service check record with level: ${level}`;
                if (format == 'html') {
                    res.status(403).render('serviceCheckViewError', {
                        fnn: serviceCheckRecord.fnn,
                        errorMessage: errorMessage,
                        user: req.user
                    });
                } else if (format == 'json') {
                    res.status(403).send({
                        error: errorMessage
                    });
                }
            } else {
                let serviceCheckRecordObject = serviceCheckRecord.toJSON();

                if (format == 'html') {
                    let ruleSourceData = await mergeRule.getRulesAndSources();
                    let serviceCheckRecordBase64 = Buffer.from(JSON.stringify(serviceCheckRecordObject)).toString('base64');
                    let ruleSourceDataBase64 = Buffer.from(JSON.stringify(ruleSourceData)).toString('base64');

                    res.render('serviceCheckView', {
                        SCrEncoded: serviceCheckRecordBase64,
                        SCRules: ruleSourceDataBase64,
                        user: req.user
                    });
                } else if (format == 'json') {
                    res.send(serviceCheckRecordObject);
                }
            }
        } else {
            res.status(404).render('serviceCheckViewNotFound', { id : inputID });
        }
    } catch(error) {
        logger.error(`Could not obtain service check ${inputID}, ${error.toString()}`);
        res.sendStatus(500);
    }
});


router.get('/history/view/all', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), function(req, res) {
    res.render('serviceCheckHistory', { title: 'Service Check History', scope: 'all', user: req.user, limitServiceChecks: false });
});


router.get('/history/view/my', auth.authorizeForRoles([
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), function(req, res) {
    res.render('serviceCheckHistory', { title: 'Service Check History', scope: 'all', user: req.user, limitServiceChecks: true });
});


router.get('/uniqueCases', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), [
    query('startDate').customSanitizer(validators.startDateSanitizer).isISO8601().toDate(),
    query('endDate').optional().isISO8601().toDate().custom(validators.isValidEndDate).withMessage('End date must be a valid date and after the start date'),
    query('limit').default(100).isInt({ min: 1 }).toInt(),
    query('offset').default(0).isInt({ min: 0 }).toInt(),
    query('fnn').optional().isString(),
    query('serviceType').optional().isString(),
    query('includeEmpty').default(false).isBoolean().toBoolean(),
    query('sort').default('fnn').isString(),
    query('order').default('asc').isIn(['asc', 'desc'])
        .withMessage('Order must be either "asc" or "desc"')
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    try {
        let [uniqueCases, countResult] = await getUniqueCases(req, req.query.startDate, req.query.endDate);
        let count = _.get(countResult, [0, 'total'], 0);

        let paginationMetadata = pagination.createPaginationMetadata(req, req.query.limit, req.query.offset, count);

        let uniqueCasesResult = uniqueCases.map(uniqueCase => {
            return {
                fnn: uniqueCase.fnn,
                serviceType: uniqueCase.serviceType,
                siiamCases: uniqueCase.siiamCases.sort(),
                serviceCentralIncidents: uniqueCase.serviceCentralIncidents.sort()
            };
        });

        let metadata = {
            pagination: paginationMetadata,
            startDate: req.query.startDate,
            endDate: req.query.endDate
        };

        res.send({
            metadata: metadata,
            results: uniqueCasesResult
        });
    } catch(error) {
        logger.error(`Service check history: error retrieving unique cases, ${error.toString()}`);
        res.status(500).send({ error: error.message });
    }
});


router.get('/history', auth.authorizeForRoles([
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), [
    query('startDate').customSanitizer(validators.startDateSanitizer).isISO8601().toDate(),
    query('endDate').optional().isISO8601().toDate().custom(validators.isValidEndDate).withMessage('End date must be a valid date and after the start date'),
    query('limit').default(100).isInt({ min: 1 }).toInt(),
    query('offset').default(0).isInt({ min: 0 }).toInt(),
    query('fnn').optional().isString(),
    query('status').optional().isString(),
    query('suite').optional().isString(),
    query('carriageType').optional().isString(),
    query('carriageFNN').optional().isString(),
    query('createdBy').optional().isString(),
    query('deviceName').optional().custom(validators.createFilterQueryValidator(["like", "equal"]))
        .customSanitizer(validators.createFilterQuerySanitizer())
        .withMessage("deviceName filter must contain 'like' or 'equal' operator"),
    query('billingFNN').optional().isString(),
    query('MDNFNN').optional().isString(),
    query('CIDN').optional().isString(),
    query('nbnAccessType').optional().isString(),
    query('nbnId').optional().isString(),
    query('serviceType').optional().isString(),
    query('siiamCases').optional().isBoolean().toBoolean(),
    query('serviceCentralIncidents').optional().isBoolean().toBoolean(),
    query('feedback').optional().isIn([
        'feedbackNotExists',
        'feedbackExists',
        'feedbackIsPositive',
        'feedbackIsNegative',
        'feedbackIsNegativeWithMessage',
        'feedbackIsNegativeWithMessageRead',
        'feedbackIsNegativeWithMessageUnread',
        'feedbackIsPositiveWithMessage',
        'feedbackIsPositiveWithMessageRead',
        'feedbackIsPositiveWithMessageUnread',
        'feedbackWithMessage',
        'feedbackWithMessageRead',
        'feedbackWithMessageUnread'
    ]),
    query('startMethod').optional().isString(),
    query('sort').default('createdOn').isIn([
        'fnn',
        'status',
        'startMethod',
        'suite',
        'billingFNN',
        'carriageType',
        'carriageFNN',
        'MDNFNN',
        'CIDN',
        'nbnAccessType',
        'nbnId',
        'createdBy',
        'deviceName',
        'serviceType',
        'createdOn'
    ]),
    query('order').default('desc').isIn(['asc', 'desc'])
        .withMessage('Order must be either "asc" or "desc"')
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    // Only allows admins and level leads to retrieve other users' service checks
    if (req.query.createdBy !== req.user.username &&
        !(req.user.isAdmin || req.user.levelLead)) {
        res.sendStatus(403);
        return;
    }

    try {
        let [serviceChecks, countResult] = await Promise.all(getRecords(req.query, true));

        let count = _.get(countResult, [0, 'total'], 0);

        let paginationMetadata = pagination.createPaginationMetadata(req, req.query.limit, req.query.offset, count);

        let metadata = {
            pagination: paginationMetadata,
            startDate: req.query.startDate,
            endDate: req.query.endDate
        };

        res.send({
            metadata: metadata,
            results: serviceChecks
        });

    } catch(error) {
        logger.error(`Service check history: error retrieving record history, ${error.toString()}`);
        res.status(500).send({ error: error.message });
    }
});


router.get('/history/download', auth.authorizeForRoles([
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), [
    query('startDate').customSanitizer(validators.startDateSanitizer).isISO8601().toDate(),
    query('endDate').optional().isISO8601().toDate().custom(validators.isValidEndDate).withMessage('End date must be a valid date and after the start date'),
    query('limit').default(100).isInt({ min: 1 }).toInt(),
    query('offset').default(0).isInt({ min: 0 }).toInt(),
    query('fnn').optional().isString(),
    query('status').optional().isString(),
    query('suite').optional().isString(),
    query('carriageType').optional().isString(),
    query('carriageFNN').optional().isString(),
    query('createdBy').optional().isString(),
    query('deviceName').optional().isString().optional().custom(validators.createFilterQueryValidator(["like", "equal"]))
        .customSanitizer(validators.createFilterQuerySanitizer())
        .withMessage("deviceName filter must contain 'like' or 'equal' operator"),
    query('billingFNN').optional().isString(),
    query('MDNFNN').optional().isString(),
    query('CIDN').optional().isString(),
    query('nbnAccessType').optional().isString(),
    query('nbnId').optional().isString(),
    query('serviceType').optional().isString(),
    query('siiamCases').optional().isBoolean().toBoolean(),
    query('serviceCentralIncidents').optional().isBoolean().toBoolean(),
    query('feedback').optional().isIn([
        'feedbackNotExists',
        'feedbackExists',
        'feedbackIsPositive',
        'feedbackIsNegative',
        'feedbackIsNegativeWithMessage',
        'feedbackIsNegativeWithMessageRead',
        'feedbackIsNegativeWithMessageUnread',
        'feedbackIsPositiveWithMessage',
        'feedbackIsPositiveWithMessageRead',
        'feedbackIsPositiveWithMessageUnread',
        'feedbackWithMessage',
        'feedbackWithMessageRead',
        'feedbackWithMessageUnread'
    ]),
    query('sort').default('createdOn').isIn([
        'fnn',
        'status',
        'startMethod',
        'suite',
        'billingFNN',
        'carriageType',
        'carriageFNN',
        'MDNFNN',
        'CIDN',
        'nbnAccessType',
        'nbnId',
        'createdBy',
        'deviceName',
        'serviceType',
        'createdOn'
    ]),
    query('order').default('desc').isIn(['asc', 'desc'])
        .withMessage('Order must be either "asc" or "desc"')
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    // Only allows admins and level leads to retrieve other users' service checks
    if (req.query.createdBy !== req.user.username &&
        !(req.user.isAdmin || req.user.levelLead)) {
        res.sendStatus(403);
        return;
    }

    try {
        let dateStr = new Date().toISOString().replace(/[^0-9]/g, '');
        let fileName = `Service_check_history_${global.gConfig.env}-v${global.gConfig.ver}_${req.user.username}_${dateStr}.csv`;

        let serviceCheckQueryPromise = getRecords(req.query, false);
        let cursor = serviceCheckQueryPromise.cursor();

        let serviceCheckStream = new ServiceCheckStreamRead(cursor);
        res.attachment(fileName);
        serviceCheckStream.on('error', (error) => {
            logger.error(`Service check history: error downloading record history in stream, ${error.toString()}`);
            res.end();
        });

        serviceCheckStream.pipe(res);
    } catch(error) {
        logger.error(`Service check history: error downloading record history, ${error.toString()}`);
        res.status(500).send({ error: error.message });
    }
});


router.get('/ruleStatus/error', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin
], false), [
    query('last').default(1).isInt({ min: 1 }).toInt(),
    query('limit').default(100).isInt({ min: 1 }).toInt(),
    query('offset').default(0).isInt({ min: 0 }).toInt(),
    query('rule').optional().custom(validators.createFilterQueryValidator(["like", "equal"]))
        .customSanitizer(validators.createFilterQuerySanitizer())
        .withMessage("Rule filter must contain 'like' or 'equal' operator"),
    query('includeError').optional().isBoolean().toBoolean(),
    query('sort').default('rule').isIn(['rule', 'count', 'errorCount']),
    query('order').default('asc').isIn(['asc', 'desc'])
        .withMessage('Order must be either "asc" or "desc"')
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    try {
        let rules = await Rule.find({ active: true }).select(['name']);
        let ruleNames = rules.map(rule => { return rule.name });

        let response = await getErrorStatus(req, 'rule', 'rulesData', 'result', 'Error', ruleNames, []);
        res.send(response);
    } catch(error) {
        res.setHeader('Content-Type', 'application/json');
        res.status(500).send({ error: error.message });
    }
});


router.get('/ruleStatus/failed', auth.authorizeForRoles([
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), [
    query('last').default(1).isInt({ min: 1 }).toInt(),
    query('limit').default(100).isInt({ min: 1 }).toInt(),
    query('offset').default(0).isInt({ min: 0 }).toInt(),
    query('rule').optional().custom(validators.createFilterQueryValidator(["like", "equal"]))
        .customSanitizer(validators.createFilterQuerySanitizer())
        .withMessage("Rule filter must contain 'like' or 'equal' operator"),
    query('includeFailed').optional().isBoolean().toBoolean(),
    query('sort').default('rule').isIn(['rule', 'count', 'failedCount']),
    query('order').default('asc').isIn(['asc', 'desc'])
        .withMessage('Order must be either "asc" or "desc"')
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    try {
        let rules = await Rule.find({ active: true }).select(['name']);
        let ruleNames = rules.map(rule => { return rule.name });

        let response = await getFailedStatus(req, 'rule', 'rulesData', 'result', 'Failed', ruleNames);
        res.send(response);
    } catch(error) {
        res.setHeader('Content-Type', 'application/json');
        res.status(500).send({ error: error.message });
    }
});


router.get('/sourceStatus', auth.authorizeForRoles([
    AuthorizationRoles.readOnly,
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), [
    query('last').default(1).isInt({ min: 1 }).toInt(),
    query('limit').default(100).isInt({ min: 1 }).toInt(),
    query('offset').default(0).isInt({ min: 0 }).toInt(),
    query('source').optional().custom(validators.createFilterQueryValidator(["like", "equal"]))
        .customSanitizer(validators.createFilterQuerySanitizer())
        .withMessage("Source filter must contain 'like' or 'equal' operator"),
    query('includeError').optional().isBoolean().toBoolean(),
    query('excludeNetworkErrors').default(false).isBoolean().toBoolean(),
    query('sort').default('source').isIn(['source', 'count', 'errorCount']),
    query('order').default('asc').isIn(['asc', 'desc'])
    .withMessage('Order must be either "asc" or "desc"')
], async function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    try {
        let sources = await Source.find({ active: true }).select(['name']);
        let sourceNames = sources.map(source => { return source.name });

        let excludeErrorMessages = [...constants.SOURCE_STATUS_ERROR_MESSAGE_EXCLUSIONS];
        if (req.query.excludeNetworkErrors) {
            excludeErrorMessages.push(...constants.SOURCE_STATUS_NETWORK_ERROR_MESSAGE_EXCLUSIONS);
        };

        let response = await getErrorStatus(req, 'source', 'sourcesMetadata', 'status', 'error', sourceNames, excludeErrorMessages);
        res.send(response);
    } catch(error) {
        res.setHeader('Content-Type', 'application/json');
        res.status(500).send({ error: error.message });
    }
});


router.post('/:id/action', auth.authorizeForRoles([
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), [
    body('rule').isString().isLength({ min: 1 }).withMessage('Rule name is required'),
    body('userInputs').optional().isString()
], async function(req, res) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let ruleName = req.body.rule;
    let id = req.params.id;
    // any additional params to be injected in MAR
    let additionalParameters = typeof req.body.userInputs === 'string' ?  JSON.parse(req.body.userInputs) : null;

    let serviceCheckRecord = null;
    let rule = null;

    try {
        // Get serviceCheckRecord aka the data. variable or Full Record from sources
        [serviceCheckRecord, rule] = await Promise.all([
            serviceCheck.findAndPopulate(id),
            Rule.findOne({ name: ruleName })
        ]);

        if (serviceCheckRecord === null) {
            res.sendStatus(404);
            return;
        }

        // Executing an action for a service check not owned by the current user
        // is forbidden
        if (serviceCheckRecord.createdBy !== req.user.username) {
            res.sendStatus(403);
            return;
        }

        if (rule === null) {
            res.status(422).send({
                message: `Rule ${ruleName} does not exist.`
            });
            return;
        }
    } catch(error) {
        logger.error(`Could not read service check record ${id} or action rule ${ruleName}, ${error.toString()}`);
        res.sendStatus(500);
    }

    try {
        //inject addtional params
        if(additionalParameters){
            rule.userInputs = additionalParameters;
        } 
        let actionResult = await runServiceCheckAction(serviceCheckRecord, rule);

        res.send({
            response: actionResult,
            updatedRule: serviceCheckRecord.rulesData[rule.name]
        });
    } catch(error) {
        logger.error(`Could not run action for service check record ${id} action rule ${ruleName}, ${error.toString()}`);

        if (error instanceof RuleCodeError) {
            res.status(500).send({
                response: null,
                updatedRule: serviceCheckRecord.rulesData[rule.name]
            });
        } else if ((error instanceof RuleNotActionableError) || (error instanceof ServiceCheckLockError)) {
            res.status(422).send({
                message: `Rule ${ruleName} is not actionable for this service check.`
            });
        } else {
            res.sendStatus(500);
        }
    }
});


// Get a list of all the active templates
router.get('/:id/listTemplates', auth.authorizeForRoles([
    AuthorizationRoles.readOnly,
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), async function(req, res) {
    let id = req.params.id;

    try {
        let record = await serviceCheck.findAndPopulate(id);

        if (!record) {
            throw new NotFoundError(`Service check record with id ${id} not found`);
        }

        await record.populateSourcesData();
        serviceCheck.validateAccessForUser(record, req.user);
        res.send(await textTemplate.listActiveTemplatesServiceCheck(record, req.user));
    } catch(error) {
        if (error.name === "NotFoundError") {
            res.status(404).send({
                error: error.message
            });
        } else if (error.name === "ServiceCheckAccessError") {
            res.status(403).send({
                error: error.message
            });
        } else {
            res.status(500).send(error.message);
        }
    }
});


router.get('/:id/listIncidentsForTemplates', auth.authorizeForRoles([
    AuthorizationRoles.level3,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), async function(req, res) {
    let id = req.params.id;

    debugMsg(`#> listIncidentsForTemplates`);
    try {
        let serviceCheckRecord = await ServiceCheckModel.findOne({ id: id });
        if (!serviceCheckRecord) {
            throw new NotFoundError(`Service check record with id ${id} not found`);
        }
        await serviceCheckRecord.populateSourcesData();
        serviceCheck.validateAccessForUser(serviceCheckRecord, req.user);

        let SIIAMTickets = [];
        let serviceCentralIncidents = [];

        let SIIAMCases = _.get(serviceCheckRecord, ['sourcesData', 'SIIAMfnn', 'FNNCases']);
        if (_.isPlainObject(SIIAMCases)) {
            for (let [ticketId, ticketData] of Object.entries(SIIAMCases)) {
                if (_.isPlainObject(ticketData) && ['Open', 'Assigned', 'In Progress'].includes(ticketData.STATUS)) {
                    SIIAMTickets.push(ticketId);
                }
            }
        }
        let serviceCentralCases = _.get(serviceCheckRecord, ['sourcesData', 'Hangar - Service central incident API']);
        if (_.isPlainObject(serviceCentralCases)) {
            for (let [incidentId, incidentData] of Object.entries(serviceCentralCases)) {
                if (_.isPlainObject(incidentData) && ['Open', 'Assigned', 'In Progress'].includes(incidentData.state_dv)) {
                    serviceCentralIncidents.push(incidentId);
                }
            }
        }
        res.send({
            SIIAM: SIIAMTickets,
            ServiceCentral: serviceCentralIncidents
        });
    } catch(error) {
        if (error.name === "NotFoundError") {
            debugMsg(`#> listIncidentsForTemplates Error 404  ${error.message}`);
            res.status(404).send({
                error: error.message
            });
        } else if (error.name === "ServiceCheckAccessError") {
            debugMsg(`#> listIncidentsForTemplates Error 403  ${error.message}`);
            res.status(403).send({
                error: error.message
            });
        } else {
            debugMsg(`#> listIncidentsForTemplates Error 500 ${error.message}`);
            res.status(500).send(error.message);
        }
    }
});


router.get('/:id/renderTemplate', auth.authorizeForRoles([
    AuthorizationRoles.readOnly,
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), [
    query('name').isString().isLength({ min: 1 }),
    query('showRulesData').default(false).isBoolean().toBoolean()
], async function(req, res) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let id = req.params.id;
    let templateName = req.query.name;
    let showRulesData = req.query.showRulesData;

    debugMsg(`#> Template Name is: ${templateName}`);

    try {
        let record = await serviceCheck.findAndPopulate(id);

        if (!record) {
            res.status(404).send(`Service check record with id ${id} not found`);
            return;
        }

        // Ensures user is authorized to view data inside this service check
        let disableDisplayMessage = serviceCheck.disableDisplayCondition(record, req.user);
        let level = _.get(record, ['input', 'level'], 0);

        if (disableDisplayMessage) {
            res.status(403).send({
                error: disableDisplayMessage.message
            });
            return;
        } else if (!auth.checkUserAuthorizedForServiceCheckLevel(req.user, level)) {
            res.status(403).send({
                error: `You do not have access to view service check record ${record.id} with level: ${level}`
            });
            return;
        }

        let text = await textTemplate.renderTemplateByName(record.toJSON(), templateName, showRulesData, req.user);
        debugMsg(`#> template is returned:\n${text}`);

        res.set('Content-Type', 'text/html');
        res.send(text);
    } catch(error) {
        if (error.name === "NotFoundError") {
            res.status(422).send(error.message);
        } else {
            res.status(500).send(error.message);
        }
    }
});


router.post('/:id/appendTemplateToTicket', auth.authorizeForRoles([
    AuthorizationRoles.level3,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), [
    body('templateName').isString(),
    body('ticketSystem').isIn(['ServiceCentral', 'SIIAM']),
    body('ticketID').isString(),
    body('showRulesData').default(false).isBoolean().toBoolean()
], async function(req, res) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let id = req.params.id;
    let templateName = req.body.templateName;
    let ticketSystem = req.body.ticketSystem;
    let ticketID = req.body.ticketID;
    let showRulesData = req.body.showRulesData;

    try {
        let record = await serviceCheck.findAndPopulate(id);

        if (!record) {
            res.status(404).send(`Service check record with id ${id} not found`);
            return;
        }

        // Ensures user is authorized to view data inside this service check
        let disableDisplayMessage = serviceCheck.disableDisplayCondition(record, req.user);
        let level = _.get(record, ['input', 'level'], 0);

        if (disableDisplayMessage) {
            res.status(403).send({
                error: disableDisplayMessage.message
            });
            return;
        } else if (!auth.checkUserAuthorizedForServiceCheckLevel(req.user, level)) {
            res.status(403).send({
                error: `You do not have access to view service check record ${record.id} with level: ${level}`
            });
            return;
        }

        let template = await Template.findOne({
            name: templateName,
            allowedSystemsToAppend: ticketSystem,
            active: true,
            templateType: "Service Check"
        });

        if (!template) {
            res.sendStatus(422);
            return;
        }

        let appendText = await textTemplate.renderTemplateByName(record.toJSON(), templateName, showRulesData, req.user);
        appendText += '\nAppended by ' + req.user.name +  ' - ' + req.user.username;

        debugMsg(`#> Template to be appended returned with length: ${appendText.length}`);

        let ticketAppendResponse;

        switch (ticketSystem) {
            case 'ServiceCentral':
                debugMsg(`#> Calling ServiceCentral with INC: ${ticketID}`);
                ticketAppendResponse = await serviceCentralAppend(appendText, ticketID);
                debugMsg('#> Calling ServiceCentral response: ' + JSON.stringify(ticketAppendResponse, null, 4));
                break;
            case 'SIIAM':
                debugMsg(`#> Calling SIIAM with TicketID: ${ticketID}`);
                ticketAppendResponse = await SIIAMAppend(appendText, ticketID);
                debugMsg('#> Calling ServiceCentral response: ' + JSON.stringify(ticketAppendResponse, null, 4));
                break;
        }

        if (_.get(ticketAppendResponse, ['success']) === true) {
            res.sendStatus(200);
        } else {
            res.status(422).send({
                error: _.get(ticketAppendResponse, ['error'], null)
            });
        }
    } catch(error) {
        if (error.name === "NotFoundError") {
            debugMsg(`#> catch 404 appendTemplateToTicket Error ` + error);
            res.status(404).send({ error: error.message });
        } else {
            debugMsg(`#> appendTemplateToTicket unhandled error ` + error.toString());
            res.sendStatus(500);
        }
    }
});


router.put('/:id/feedback', auth.authorizeForRoles([
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), [
    body('isPositive').isBoolean().toBoolean(),
    body('message').optional().isString().isLength({ max: 4000 })
], async function(req, res) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let id = req.params.id;

    try {
        let record = await ServiceCheckModel.findOne({ id: id }, {
            createdBy: 1
        });

        if (!record) {
            throw new NotFoundError(`Service check record with id ${id} not found`);
        }

        if (record.createdBy !== req.user.username) {
            res.sendStatus(403);
            return;
        }

        await ServiceCheckModel.findOneAndUpdate(
            { id: id },
            { $set: {
                feedback: {
                    isPositive: req.body.isPositive,
                    messageExists: typeof req.body.message === 'string' ? true : false,
                    message: typeof req.body.message === 'string' ? req.body.message : null
                }
            }},
            { upsert: false, strict: "throw", runValidators: true, setDefaultsOnInsert: true, useFindAndModify: false }
        );

        res.sendStatus(200);
    } catch(error) {
        if (error.name === "NotFoundError") {
            res.status(404).send({ error: error.message });
        } else {
            res.sendStatus(500);
        }
    }
});


router.delete('/:id/feedback', auth.authorizeForRoles([
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), async function(req, res) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let id = req.params.id;

    try {
        let record = await ServiceCheckModel.findOne({ id: id }, {
            createdBy: 1
        });

        if (!record) {
            throw new NotFoundError(`Service check record with id ${id} not found`);
        }

        if (record.createdBy !== req.user.username) {
            res.sendStatus(403);
            return;
        }

        await ServiceCheckModel.findOneAndUpdate(
            { id: id },
            { $set: {
                feedback: null
            }},
            { upsert: false, useFindAndModify: false }
        );

        res.sendStatus(200);
    } catch(error) {
        if (error.name === "NotFoundError") {
            res.status(404).send({ error: error.message });
        } else {
            res.sendStatus(500);
        }
    }
});


router.put('/:id/feedbackRead', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], false), async function(req, res) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    let id = req.params.id;

    try {
        let record = await ServiceCheckModel.findOne({ id: id });

        if (!record) {
            throw new NotFoundError(`Service check record with id ${id} not found`);
        }

        await ServiceCheckModel.findOneAndUpdate(
            {
                id: id,
                'feedback.messageExists': true
            },
            { $set: {
                'feedback.messageRead': true
            }},
            { upsert: false, strict: "throw", runValidators: true, useFindAndModify: false }
        );

        res.sendStatus(200);
    } catch(error) {
        if (error.name === "NotFoundError") {
            res.status(404).send({ error: error.message });
        } else {
            res.sendStatus(500);
        }
    }
});


router.get('/advancedSearch', auth.authorizeForRoles([
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.isDeveloper,
], true), function(req, res) {
    res.render('serviceCheckRecordSearch', { title: 'Service Check Advanced Search', user: req.user });
});


// ServiceCentralAppend  "ServiceCentral" "SNI1234567"
async function serviceCentralAppend(templateText, ticketID) {
    let resultResponse;

    try {
        let response = await axios.post(global.gConfig.HAPIURI + 'rTicketUpdate', {
            headers: { "Content-Type": "application/json" },
            request: {
                ticketID: ticketID,
                ticketSystem: "sc",
                notes: templateText
            }
        });
        // Success response
        debugMsg(`#> SData ` + JSON.stringify(response.data, null, 4));
        resultResponse = Object.assign({}, response.data);
    } catch(error) {
        if (error.response) {
            // Error response
            debugMsg(`#> Error Response ` + error.response.status + ' Data ' + JSON.stringify(error.response.data) + ' Header '  + JSON.stringify(error.response.headers));
        } else if (error.request) {
            // Error request
            debugMsg(`#> Error Request ` + error.request);
        } else {
            // Error unknown
            debugMsg(`#> Error Unknown ` + error.message);
        }
        resultResponse = Object.assign({}, error.response);
    };
    debugMsg(`#> serviceCentralAppend end`);
    return resultResponse;
}


async function SIIAMAppend(templateText, ticketID) {
    let resultResponse;

    try {
        let response = await axios.post(global.gConfig.HAPIURI + 'rTicketUpdate', {
            headers: { "Content-Type": "application/json" },
            request: {
                ticketID: ticketID,
                ticketSystem: "siiam",
                notes: templateText
            }
        });

        // Success response
        debugMsg(`#> SData ` + JSON.stringify(response.data, null, 4));
        resultResponse = Object.assign({}, response.data);
    } catch(error) {
        if (error.response) {
            // Error response
            debugMsg(`#> Error Response ` + error.response.status + ' Data ' + JSON.stringify(error.response.data) + ' Header '  + JSON.stringify(error.response.headers));
        } else if (error.request) {
            // Error request
            debugMsg(`#> Error Request ` + error.request);
        } else {
            // Error unknown
            debugMsg(`#> Error Unknown ` + error.message);
        }
        resultResponse = Object.assign({}, error.response);
    };
    debugMsg(`#> SIIAMAppend end`);
    return resultResponse;
}


function getServiceChecksFromRequest(req, startDate, endDate, includeCount) {
    let limit = req.query.limit;
    let offset = req.query.offset;
    let sort = req.query.sort;
    let orderBy = req.query.order;
    let orderByParam = (orderBy === 'desc') ? -1 : 1;

    // Rewrites sorting by suite field here as $sort occurs before $project
    // The aggregation pipeline needs this order as using $project before $sort
    // can cause memory limit issues when sorting with projected fields
    if (sort === 'suite') {
        sort = 'input.suite';
    }

    let querySort = { [sort]: orderByParam };

    // Get all the query parameters that can be used to filter service checks that does not require case sensitivity
    let valueFilters = {
        fnn: req.query.fnn,
        status: req.query.status,
        suite: req.query.suite,
        carriageType: req.query.carriageType,
        carriageFNN: req.query.carriageFNN,
        createdBy: req.query.createdBy,
        billingFNN: req.query.billingFNN,
        MDNFNN: req.query.MDNFNN,
        CIDN: req.query.CIDN,
        nbnAccessType: req.query.nbnAccessType,
        nbnId: req.query.nbnId,
        serviceType: req.query.serviceType
    };

    // Get all the query parameters that need to searched case insensitively or partially
    let queryFilters = {
        deviceName: req.query.deviceName
    };

    let booleanFilters = {
        siiamCasesLength: req.query.siiamCases,
        serviceCentralIncidentsLength: req.query.serviceCentralIncidents
    };

    let createdOnFilter = { $gte: startDate };
    if (endDate) {
        createdOnFilter['$lt'] = endDate;
    }

    let serviceCheckFilters = [{ createdOn: createdOnFilter }];

    // Add each query parameter to condition for service checks
    for (let filter in valueFilters) {
        if (valueFilters[filter] != undefined) {
            if (filter === "fnn" && isValidPhoneNumber(valueFilters[filter], "AU")) {
                let phoneNumber = parsePhoneNumber(valueFilters[filter], "AU").format("E.164");
                serviceCheckFilters.push({ phoneNumber: { $eq: phoneNumber }});
            } else if (filter === "suite") {
                serviceCheckFilters.push({ "input.suite": { $eq: valueFilters[filter] }});
            } else {
                serviceCheckFilters.push({ [filter]: { $eq: valueFilters[filter] }});
            }
        }
    }

    // Add every query parameter to check partial text or case insensitively
    for (let filter in queryFilters) {
        if (queryFilters[filter] != undefined) {
            for (let operation in queryFilters[filter]) {
                switch (operation) {
                    case "equal":
                        serviceCheckFilters.push({ [filter]: { $eq: queryFilters[filter][operation] }});
                        break;
                    case "like":
                        serviceCheckFilters.push({ [filter]: { $regex: new RegExp(escapeStringRegexp(queryFilters[filter][operation]), 'i') }});
                        break;
                }
            }
        }
    }

    switch (req.query.feedback) {
        case 'feedbackNotExists':
            serviceCheckFilters.push({ feedback: null });
            break;
        case 'feedbackExists':
            serviceCheckFilters.push({ feedback: { $ne: null }});
            break;
        case 'feedbackIsPositive':
            serviceCheckFilters.push({ 'feedback.isPositive': true });
            break;
        case 'feedbackIsNegative':
            serviceCheckFilters.push({ 'feedback.isPositive': false });
            break;
        case 'feedbackIsNegativeWithMessage':
            serviceCheckFilters.push(...[
                { 'feedback.isPositive': false },
                { 'feedback.messageExists': true }
            ]);
            break;
        case 'feedbackIsNegativeWithMessageRead':
            serviceCheckFilters.push(...[
                { 'feedback.isPositive': false },
                { 'feedback.messageExists': true },
                { 'feedback.messageRead': true }
            ]);
            break;
        case 'feedbackIsNegativeWithMessageUnread':
            serviceCheckFilters.push(...[
                { 'feedback.isPositive': false },
                { 'feedback.messageExists': true },
                { 'feedback.messageRead': false }
            ]);
            break;
        case 'feedbackIsPositiveWithMessage':
            serviceCheckFilters.push(...[
                { 'feedback.isPositive': true },
                { 'feedback.messageExists': true }
            ]);
            break;
        case 'feedbackIsPositiveWithMessageRead':
            serviceCheckFilters.push(...[
                { 'feedback.isPositive': true },
                { 'feedback.messageExists': true },
                { 'feedback.messageRead': true }
            ]);
            break;
        case 'feedbackIsPositiveWithMessageUnread':
            serviceCheckFilters.push(...[
                { 'feedback.isPositive': true },
                { 'feedback.messageExists': true },
                { 'feedback.messageRead': false }
            ]);
            break;
        case 'feedbackWithMessage':
            serviceCheckFilters.push(...[
                { 'feedback.messageExists': true }
            ]);
            break;
        case 'feedbackWithMessageRead':
            serviceCheckFilters.push(...[
                { 'feedback.messageExists': true },
                { 'feedback.messageRead': true }
            ]);
            break;
        case 'feedbackWithMessageUnread':
            serviceCheckFilters.push(...[
                { 'feedback.messageExists': true },
                { 'feedback.messageRead': false }
            ]);
            break;
    }

    for (let filter in booleanFilters) {
        if (booleanFilters[filter] != undefined) {
            if (booleanFilters[filter] == true) {
                serviceCheckFilters.push({ [filter]: { $gt: 0 }});
            } else {
                serviceCheckFilters.push({ [filter]: { $eq: 0 }});
            }
        }
    }

    let condition = { $and: serviceCheckFilters };

    let documentAggregate = [
        { $match: condition },
        { $sort: querySort },
        { $skip: offset },
        { $limit: limit },
        { $project: {
            _id: 0,
            id: 1,
            fnn: 1,
            status: 1,
            suite: '$input.suite',
            billingFNN: 1,
            carriageType: 1,
            carriageFNN: 1,
            MDNFNN: 1,
            CIDN: 1,
            nbnAccessType: 1,
            nbnId: 1,
            createdBy: 1,
            deviceName: 1,
            serviceType: 1,
            siiamCases: 1,
            serviceCentralIncidents: 1,
            feedback: 1,
            createdOn: 1
        }}
    ];

    if (includeCount) {
        return Promise.all([
            ServiceCheckModel.aggregate(documentAggregate).collation({ locale: 'en', strength: 1 }),
            ServiceCheckModel.aggregate([
                { $match: condition },
                { $count: 'total' }
            ]).collation({ locale: 'en', strength: 1 })
        ]);
    } else {
        return ServiceCheckModel.aggregate(documentAggregate).collation({ locale: 'en', strength: 1 });
    }
}


function getUniqueCases(req, startDate, endDate) {
    let limit = req.query.limit;
    let offset = req.query.offset;
    let sort = req.query.sort;
    let orderBy = req.query.order;
    let orderByParam = (orderBy === 'desc') ? -1 : 1;
    let includeEmpty = req.query.includeEmpty;
    let querySort;

    switch (sort) {
        case 'siiamCases':
            querySort = { siiamCaseCount: orderByParam };
            break;
        case 'serviceCentralIncidents':
            querySort = { serviceCentralIncidentCount: orderByParam };
            break;
        default:
            querySort = { [sort]: orderByParam };
            break;
    }

    // Sort by FNN ascending if another field is being sorted
    if (!('fnn' in querySort)) {
        querySort['fnn'] = 1;
    }

    let serviceCheckCondition = {};
    let excludeEmptyCasesCondition = includeEmpty ? {} : {
        $or: [
            { siiamCasesLength: { $exists: true, $gt: 0 } },
            { serviceCentralIncidentsLength: { $exists: true, $gt: 0 } }
        ]
    };

    let projectSiiamCases = includeEmpty ? '$siiamCases' : {
        $filter: {
            input: '$siiamCases',
            cond: { $ne: ["$$this", []] }
        }
    }

    let projectServiceCentralIncidents = includeEmpty ? '$serviceCentralIncidents' : {
        $filter: {
            input: '$serviceCentralIncidents',
            cond: { $ne: ["$$this", []] }
        }
    }

    let serviceTypeCondition = req.query.serviceType !== undefined ? { serviceType: { $eq: req.query.serviceType }} : {};

    serviceCheckCondition.createdOn = { $gte: startDate };
    if (endDate) {
        serviceCheckCondition.createdOn['$lt'] = endDate;
    }

    // Get all the query parameters that can be used to filter service checks
    let queryFilters = {
        fnn: req.query.fnn
    };

    // Add each query parameter to condition for service checks
    for (let filter in queryFilters) {
        if (queryFilters[filter] != undefined) {
            if (filter == "fnn" && isValidPhoneNumber(queryFilters[filter], "AU")) {
                queryFilters[filter] = parsePhoneNumber(queryFilters[filter], "AU").format("E.164");
            }

            serviceCheckCondition[filter] = { $eq: queryFilters[filter] };
        }
    }

    let uniqueCasesAggregate = [
        { $sort: { createdOn: -1 }},
        {
            $addFields: {
                fnn: {
                    $ifNull: ['$phoneNumber', '$fnn']
                }
            }
        },
        { $match: serviceCheckCondition },
        { $match: excludeEmptyCasesCondition },
        { $group: { _id:
            { fnn: '$fnn' },
            serviceTypeSet: { $push: '$serviceType' },
            siiamCases: { $addToSet: '$siiamCases' },
            serviceCentralIncidents: { $addToSet: '$serviceCentralIncidents' }
        }},
        { $addFields: {
            siiamCases: projectSiiamCases,
            serviceCentralIncidents: projectServiceCentralIncidents
        }},
        { $addFields: {
            siiamCaseCount: { $size: '$siiamCases' },
            serviceCentralIncidentCount: { $size: '$serviceCentralIncidents' },
            serviceTypeFiltered: {
                $filter: {
                    input: "$serviceTypeSet",
                    cond: { $ne: [ "$$this", null ] }
                }
            }
        }},
        { $addFields: {
            serviceType: { $ifNull: [{ $arrayElemAt: ["$serviceTypeFiltered", 0] }, null] }
        }},
        { $match: { $or: [{ siiamCaseCount: { $gt: 1 }}, { serviceCentralIncidentCount: { $gt: 1 }}]}},
        { $match: serviceTypeCondition },
        { $addFields: {
            siiamCases: {
                $cond: {
                    if: { $lte: ['$siiamCaseCount', 1] },
                    then: [],
                    else: '$siiamCases'
                }
            },
            serviceCentralIncidents: {
                $cond: {
                    if: { $lte: ['$serviceCentralIncidentCount', 1] },
                    then: [],
                    else: '$serviceCentralIncidents'
                }
            }
        }},
        {
            $addFields: {
                fnn: '$_id.fnn'
            }
        },
        {
            $unset: ['_id.fnn']
        }
    ];

    return Promise.all([
        ServiceCheckModel.aggregate([
            ...uniqueCasesAggregate,
            { $sort: querySort },
            { $skip: offset },
            { $limit: limit }
        ]).allowDiskUse(true).collation({ locale: 'en' }),
        ServiceCheckModel.aggregate([
            ...uniqueCasesAggregate,
            { $count: 'total' }
        ]).allowDiskUse(true).collation({ locale: 'en' })
    ])
}


// Mostly repeated code from getErrorStatus(), could possibly integrate both functions together
async function getFailedStatus(req, fieldName, serviceCheckRecordFieldName, statusFieldName, statusFieldValue, possibleNestedFieldNames) {
    let limit = req.query.limit;
    let offset = req.query.offset;
    let fieldNameFilter = req.query[fieldName];
    let sort = req.query.sort;
    let orderBy = req.query.order;
    let orderByParam = (orderBy === 'asc') ? 1 : -1;
    let includeFailed = (req.query.includeFailed === undefined) ? null : req.query.includeFailed;
    let lastHours = req.query.last;

    let serviceCheckStartDate = new Date();
    serviceCheckStartDate.setHours(serviceCheckStartDate.getHours() - lastHours);

    // Do not include service check records which have a non-object field
    let serviceCheckCondition = {
        createdOn: { $gte: serviceCheckStartDate },
        [serviceCheckRecordFieldName]: { $type: "object" }
    };

    let fieldNameMatch = { $regex: /.*/ };
    let querySort = { [sort]: orderByParam };

    // Sorts by field name in ascending order as a second priority
    // after sort field / order from query parameters
    if (!(fieldName in querySort)) {
        querySort[fieldName] = 1;
    }

    if (fieldNameFilter != undefined) {
        for (let operation in fieldNameFilter) {
            switch (operation) {
                case "equal":
                    fieldNameMatch = { $eq: fieldNameFilter[operation] };
                    break;
                case "like":
                    fieldNameMatch = { $regex: new RegExp(escapeStringRegexp(fieldNameFilter[operation]), 'i') }
                    break;
            }
        }
    }

    let statusAggregation = [
        { $sort: { createdOn: -1 }},
        { $match: serviceCheckCondition },
        { $addFields: { dataArray: { $objectToArray: `$${serviceCheckRecordFieldName}` }}},
        { $unwind: '$dataArray' },
        { $addFields: { data: '$dataArray.k' }},
        { $match: { data: { $in: possibleNestedFieldNames }}},
        { $addFields: {
            fnnGroup: {
                $cond: {
                    if: { $eq: [`$dataArray.v.${statusFieldName}`, statusFieldValue] },
                    then: '$fnn',
                    else: null
                }
            }
        }},
        { $group: {
            _id: { [fieldName]: '$data', fnn: '$fnnGroup' },
            records: { $push: '$id' },
            subCount: { $sum: 1 }
        }},
        { $group: {
            _id: { [fieldName]: `$_id.${fieldName}` },
            count: {
                $sum: '$subCount'
            },
            failedCount: {
                $sum: {
                    $cond: {
                        if: { $ne: ['$_id.fnn', null] },
                        then: '$subCount',
                        else: 0
                    }
                }
            },
            stats: {
                $push: {
                    $cond: {
                        if: { $ne: ['$_id.fnn', null] },
                        then: {
                            fnn: '$_id.fnn',
                            records: '$records'
                        },
                        else: null
                    }
                }
            }
        }},
        { $addFields: {
            stats: {
                $filter: {
                    input: '$stats',
                    cond: { $ne: ['$$this', null] }
                }
            }
        }},
        { $group: {
            _id: '',
            data: {
                $push: '$$ROOT'
            }
        }},
        { $project: {
            _id: 0,
            data: 1,
            [fieldName]: possibleNestedFieldNames
        }},
        { $project: {
            data: {
                $concatArrays: [
                    '$data',
                    {
                        $map: {
                            input: {
                                $setDifference: [
                                    `$${fieldName}`,
                                    `$data._id.${fieldName}`
                                ]
                            },
                            in: {
                                _id: {
                                    [fieldName]: '$$this',
                                },
                                count: 0,
                                failedCount: 0,
                                stats: []
                            }
                        }
                    }
                ]
            }
        }},
        { $unwind: '$data' },
        {
            $replaceRoot: {
                newRoot: '$data'
            }
        },
        { $project: {
            _id: 0,
            [fieldName]: `$_id.${fieldName}`,
            count: 1,
            failedCount: 1,
            stats: 1
        }},
        { $match: { [fieldName]: fieldNameMatch }}
    ];

    if (includeFailed != null) {
        let failedCountCondition = null;
        if (includeFailed) {
            failedCountCondition = {
                $match: { failedCount: { $gt: 0 }}
            };
        } else {
            failedCountCondition = {
                $match: { failedCount: { $eq: 0 }}
            };
        }

        statusAggregation.push(failedCountCondition);
    }

    let [statusResults, countResults] = await Promise.all([
        await ServiceCheckModel.aggregate([
            ...statusAggregation,
            { $sort: querySort },
            { $skip: offset },
            { $limit: limit }
        ]).collation({ locale: 'en' }).allowDiskUse(true),
        await ServiceCheckModel.aggregate([
            ...statusAggregation,
            { $count: 'total' }
        ]).collation({ locale: 'en' }).allowDiskUse(true)
    ]);
    let count = _.get(countResults, [0, 'total'], 0);

    let paginationMetadata = pagination.createPaginationMetadata(req, limit, offset, count);

    let metadata = {
        pagination: paginationMetadata,
        order: orderBy,
        startDate: serviceCheckStartDate
    };

    return {
        metadata: metadata,
        results: statusResults
    };
}

async function getErrorStatus(req, fieldName, serviceCheckRecordFieldName, statusFieldName, statusFieldValue, possibleNestedFieldNames, excludedErrorMessages) {
    let limit = req.query.limit;
    let offset = req.query.offset;
    let fieldNameFilter = req.query[fieldName];
    let sort = req.query.sort;
    let orderBy = req.query.order;
    let orderByParam = (orderBy === 'asc') ? 1 : -1;
    let includeError = (req.query.includeError === undefined) ? null : req.query.includeError;
    let lastHours = req.query.last;

    let serviceCheckStartDate = new Date();
    serviceCheckStartDate.setHours(serviceCheckStartDate.getHours() - lastHours);

    // Do not include service check records which have a non-object field
    let serviceCheckCondition = {
        createdOn: { $gte: serviceCheckStartDate },
        [serviceCheckRecordFieldName]: { $type: "object" }
    };

    let fieldNameMatch = { $regex: /.*/ };
    let querySort = { [sort]: orderByParam };

    // Sorts by field name in ascending order as a second priority
    // after sort field / order from query parameters
    if (!(fieldName in querySort)) {
        querySort[fieldName] = 1;
    }

    if (fieldNameFilter != undefined) {
        for (let operation in fieldNameFilter) {
            switch (operation) {
                case "equal":
                    fieldNameMatch = { $eq: fieldNameFilter[operation] };
                    break;
                case "like":
                    fieldNameMatch = { $regex: new RegExp(escapeStringRegexp(fieldNameFilter[operation]), 'i') }
                    break;
            }
        }
    }

    // Note: regex if excluded error messages is empty, it should match nothing
    let errorExcludeRegex = excludedErrorMessages.length ? new RegExp(excludedErrorMessages.map((r) => { return r.source }).join('|')) : /$^/;
    let errorExcludeCondition = Object.freeze({ $not: {
        $or: [{
                $regexFind: {
                    input: '$_id.error',
                    regex: errorExcludeRegex
                }
            },
            { $eq: [{$type: '$_id.error'}, 'missing'] },
            { $eq: [{$type: '$_id.error'}, 'null'] }
        ]
    }});

    let statusAggregation = [
        { $sort: { createdOn: -1 }},
        { $match: serviceCheckCondition },
        { $addFields: { dataArray: { $objectToArray: `$${serviceCheckRecordFieldName}` }}},
        { $unwind: '$dataArray' },
        { $addFields: { data: '$dataArray.k' }},
        { $match: { data: { $in: possibleNestedFieldNames }}},
        { $addFields: {
            error: {
                $cond: {
                    if: { $eq: [`$dataArray.v.${statusFieldName}`, statusFieldValue] },
                    then: '$dataArray.v.error',
                    else: null
                }
            }
        }},
        { $group: {
            _id: { [fieldName]: '$data', error: '$error' },
            records: { $push: '$id' },
            subCount: { $sum: 1 }
        }},
        { $group: {
            _id: { [fieldName]: `$_id.${fieldName}` },
            count: {
                $sum: '$subCount'
            },
            errorCount: {
                $sum: {
                    $cond: {
                        if: errorExcludeCondition,
                        then: '$subCount',
                        else: 0
                    }
                }
            },
            stats: {
                $push: {
                    $cond: {
                        if: errorExcludeCondition,
                        then: {
                            error: '$_id.error',
                            records: '$records'
                        },
                        else: null
                    }
                }
            }
        }},
        { $addFields: {
            stats: {
                $filter: {
                    input: '$stats',
                    cond: { $ne: ['$$this', null] }
                }
            }
        }},
        { $group: {
            _id: '',
            data: {
                $push: '$$ROOT'
            }
        }},
        { $project: {
            _id: 0,
            data: 1,
            [fieldName]: possibleNestedFieldNames
        }},
        { $project: {
            data: {
                $concatArrays: [
                    '$data',
                    {
                        $map: {
                            input: {
                                $setDifference: [
                                    `$${fieldName}`,
                                    `$data._id.${fieldName}`
                                ]
                            },
                            in: {
                                _id: {
                                    [fieldName]: '$$this',
                                },
                                count: 0,
                                errorCount: 0,
                                stats: []
                            }
                        }
                    }
                ]
            }
        }},
        { $unwind: '$data' },
        {
            $replaceRoot: {
                newRoot: '$data'
            }
        },
        { $project: {
            _id: 0,
            [fieldName]: `$_id.${fieldName}`,
            count: 1,
            errorCount: 1,
            stats: 1
        }},
        { $match: { [fieldName]: fieldNameMatch }}
    ];

    if (includeError != null) {
        let errorCountCondition = null;
        if (includeError) {
            errorCountCondition = {
                $match: { errorCount: { $gt: 0 }}
            };
        } else {
            errorCountCondition = {
                $match: { errorCount: { $eq: 0 }}
            };
        }

        statusAggregation.push(errorCountCondition);
    }

    let [statusResults, countResults] = await Promise.all([
        await ServiceCheckModel.aggregate([
            ...statusAggregation,
            { $sort: querySort },
            { $skip: offset },
            { $limit: limit }
        ]).collation({ locale: 'en' }).allowDiskUse(true),
        await ServiceCheckModel.aggregate([
            ...statusAggregation,
            { $count: 'total' }
        ]).collation({ locale: 'en' }).allowDiskUse(true)
    ]);
    let count = _.get(countResults, [0, 'total'], 0);

    let paginationMetadata = pagination.createPaginationMetadata(req, limit, offset, count);

    let metadata = {
        pagination: paginationMetadata,
        order: orderBy,
        startDate: serviceCheckStartDate
    };

    return {
        metadata: metadata,
        results: statusResults
    };
}


export default router;
