/**
 * Note: This migration file was created when 'migrate-mongoose' was the npm package
 * used to perform MongoDB migrations, since the 'migrate' npm package is now used
 * the implementation may not be compatible and all functionality is commented out
 */

// const mongoose = require('mongoose');

// const migrate = require('./migrate');

// const CommandRunModel = new mongoose.Schema({
//     id: { type: String, index: true, immutable: true },
//     command: { type: String, default: "" },
//     duration: { type: String, default: null },
//     status: { type: String, default: null },
//     error: { type: String, default: null },
//     result: { type: String, default: null },
//     statusCode: { type: Number, default: null },
//     createdBy: { type: String, required: true, immutable: true, default: "unknown", index: true },
//     createdOn: { type: Date, required: true, immutable: true, default: Date.now, index: true }
// });

// const CommandRun = mongoose.model('commandRun', CommandRunModel);

/**
 * Make any changes you need to make to the database here
 */
async function up () {
    // await migrate.connect();
    // for await (const record of CommandRun.find({})) {
    //     if (record.duration != null) {
    //         if (record.duration.slice(-1) == "s") {
    //             record.duration = record.duration.slice(0, -1)
    //         }
    //     }
    //     await record.save();
    // }
}

/**
 * Make any changes that UNDO the up function side effects here (if possible)
 */
async function down () {
    // await migrate.connect();
    // for await (const record of CommandRun.find({})) {
    //     if (record.duration != null) {
    //         record.duration.concat("s");
    //     }
    //     await record.save();
    // }
}

module.exports = { up, down };
