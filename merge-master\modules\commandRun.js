// commandRun.js
//v2
// =========
// All functions for running command. First Layer
import debug from 'debug';
import fs from 'fs';
import ivm from 'isolated-vm';

import { parseCommand } from './commandParser.js';
import logger from './logger.js';
import mergeCollect from './mergeCollect.js';
import mergeLib from './mergeLib.js';
import authEmitter from '../modules/authEmitter.js';
import constants from '../modules/helpers/constants.js';
import Api from '../db/model/api.js';
import { NotFoundError, NotImplementedError } from './error.js';

const commandsListArr = JSON.parse(await fs.promises.readFile('./config/commands.json'));
const commandsList = mergeLib.arrayToObject(commandsListArr, 'name');
const debugMsg = debug('merge:commandRun');


export function runCommand(QCr, socket, callback) {
    if (QCr.type == 'bash' || !QCr.type) {
        let command;

        try {
            // Process command and extract parammeters and command
            command = parseCommand(QCr.command, 'bash');
            debugMsg('Parsed Command', command);

            if (!command) {
                throw new NotFoundError(`Command could not be parsed from ${QCr.command}`);
            }
        } catch (error) {
            logger.error('Error while parsing command: ' + error);

            callback(error, null);
            return;
        }

        if (socket) {
            socket.emit('QCDebug', `Triggered command "${command.name}" processed`, command);
        }

        if (commandsList[command.name]) {
            if (socket) {
                socket.emit('QCDebug', `Command "${command.name}" definition details`, commandsList[command.name]);
            }

            if (commandsList[command.name].type == 'API') {
                QCr.sourcesMetadata = {
                    [command.name]: { status: "init" }
                };

                collectSource(command, QCr, socket, function(error, response) {
                    callback(error, response);
                });
            } else {
                debugMsg(`Command type "${commandsList[command.name].type}" is not supported`);
                callback(new NotImplementedError(`Command type "${commandsList[command.name].type}" is not supported`), null);
            }
        } else {
            debugMsg(`Command "${command.name}" not found`);
            callback(new NotFoundError(`Command "${command.name}" not found`), null);
        }
    } else {
        callback(new NotImplementedError(`No support for "${QCr.type}" command type`), null);
    }
}


function collectSource(command, QCr, socket, callback) {
    let socketObj = {
        socket: socket
    };

    Api.findOne({ name: commandsList[command.name].api }).then((api) => {
        if (api == null) {
            callback(new Error(`API ${commandsList[command.name].api} does not exist`), null);
        } else {
            // Determines parameters here because the parameter names are required from the API
            // to extract from the context
            // Future wishlist: convert the functions here and above into async syntax and
            // change isolated-vm functions to async versions
            let parameters = {};

            const isolate = new ivm.Isolate({
                memoryLimit: 32
            });
            let parameterContext = isolate.createContextSync();

            try {
                parameterContext.global.setSync('p', new ivm.ExternalCopy(command).copyInto());

                parameterContext.evalSync(commandsList[command.name].paramCode, { timeout: 2000 });

                for (let parameterName of api.parameters) {
                    let parameterValue = parameterContext.global.getSync(parameterName, { copy: true });
                    if (parameterValue !== undefined) {
                        parameters[parameterName] = parameterValue;
                    }
                }
            } catch(error) {
                debugMsg('Error in prepare parameters for command: ' + command.name, error);
                callback(error, null);
                return;
            } finally {
                parameterContext.release();
                if (!isolate.isDisposed) {
                    isolate.dispose();
                }
            }

            mergeCollect.collectApi(api, parameters, null, socketObj).then((result) => {
                if (socketObj && socketObj.socket) {
                    let socket = socketObj.socket;

                    socket.emit('QCDebug', command.name + ' collected', QCr);
                }

                callback(null, result);
            }).catch((error) => {
                if (error.message == constants.PROXY_AUTH_ERROR_MESSAGE) {
                    authEmitter.emit('AuthError');
                }

                debugMsg('Collect error: ', error);
                logger.error('Command run collect API error ' + error.toString());

                if (socketObj && socketObj.socket) {
                    socket.emit('QCDebug', 'Error with API request for command ' + QCr.id + ', API: '+ api.name, error.toString());
                }

                if (error.result) {
                    callback(error, error.result);
                } else {
                    callback(error, null);
                }
            });
        }
    }).catch((error) => {
        callback(error, null);
    });
}
