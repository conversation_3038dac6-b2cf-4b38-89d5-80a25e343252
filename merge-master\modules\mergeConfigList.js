
import fs from 'fs';
import path from 'path';


/**
 *
 * @param {Array<mongoose.Model>} dataModels Array of mongoose models to retrieve configuration documents from (sorted by "name" ascending)
 * @param {Array<String>} dataModelNames Array of strings that should be the same length as dataModels, for the key to maps documents to
 * @param {mongoose.Model} metadataModel Mongoose model containing the metadata for the configuration documents
 * @returns {Object} Plain object containing the metadata and field(s) with the configuration document data
 */
async function getObjectsAndMetadataFromDatabase(dataModels, dataModelNames, metadataModel) {
    let objectData = {};
    let promises = [];

    for (let dataModel of dataModels) {
        promises.push(dataModel.find({}).sort('name').collation({ locale: 'en' }).select({ createdBy: 0, createdOn: 0 }));
    }

    let metadata = await metadataModel.findOne({}).select(['-__v', '-_id']);
    let configData = await Promise.all(promises);

    // Save metadata entry in database if it does not exist
    if (metadata === null) {
        metadata = new metadataModel({
            version: 1,
            updatedBy: 'unknown',
            updatedOn: new Date()
        });

        await metadata.save();
    }

    Object.assign(objectData, metadata.toJSON());

    for (let i = 0; i < dataModelNames.length; i++) {
        objectData[dataModelNames[i]] = configData[i].map(item => { return item.toJSON(); });
    }

    return objectData;
}


/**
 *
 * @param {String} fileName input file name with relative path to application's current working directory
 * @returns {Object} Plain object with contents of fileName
 */
async function getObjectsAndMetadataFromFile(fileName) {
    return JSON.parse(await fs.promises.readFile(path.join(process.cwd(), fileName)));
}


export default {
    getObjectsAndMetadataFromDatabase: getObjectsAndMetadataFromDatabase,
    getObjectsAndMetadataFromFile: getObjectsAndMetadataFromFile
};
