import { CollapsablePanel } from '../../../../components/collapsablePanel/CollapsablePanel'
import { DetailField } from '../../../../components/detailsPanel/DetailField'
import { Panel } from '../../../../components/panel/Panel'
import styles from './PhoneNumberRangesField.module.scss'

interface PhoneNumberRangeFieldProps {
    value: string[] | null
}

export const PhoneNumberRangesField = ({ value }: PhoneNumberRangeFieldProps) => {
    if (value === null || value === undefined) {
        return null;
    }

    if (value.length === 0) {
        return (
            <Panel>
                <DetailField label="Registrations" value="None" />
            </Panel>
        )
    }

    return (
        <CollapsablePanel
            headerElement={<DetailField label="Number Ranges" value="" />}
            canOpen={value.length > 0}
            itemCount={value.length}
        >
            <div className={styles.phoneNumberRanges}>
                <div className={styles.phoneNumberRow}>
                    {value.map((phoneNumber, index) => (
                        <div key={`${index}`}>
                            <DetailField
                                label=""
                                value={phoneNumber ?? null}
                                inline={true}
                            />
                        </div>
                    ))}
                </div>
            </div>
        </CollapsablePanel>
    )
}
