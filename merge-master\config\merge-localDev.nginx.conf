# For Nginx Windows version 1.15.12

server {
	listen                          8080;
	server_name                     localhost;
	return 301                      https://$server_name$request_uri;
}

server {
        listen 	8081 ssl;
        listen 	[::]:8081 ssl;

        server_name 				localhost;
		ssl_certificate             D:/Users/<USER>/Documents/Git/merge/config/merge_signed.crt;
		ssl_certificate_key         D:/Users/<USER>/Documents/Git/merge/config/merge_signed.key;

		client_max_body_size 10M;

		location /public {
			alias D:/Users/<USER>/Documents/Git/merge/public;
			access_log off;
			expires -1;
		}

		location / {

			if (-f D:/Users/<USER>/Documents/Git/merge/config/maintenance_on) {
            	return 503;
        	}

#			auth_basic                            "Username and Password Required (Specific and Limited to Merge-Testing)";
#			auth_basic_user_file                  /usr/app/merge/config/users.htpasswd;

			proxy_pass http://localhost:3005;
			proxy_http_version 1.1;

			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_set_header X-Forwarded-Proto $scheme;
#		    proxy_set_header X-NginX-Proxy true;

			proxy_set_header Upgrade $http_upgrade;
			proxy_set_header Connection 'upgrade';
			proxy_set_header Host $host;
			proxy_cache_bypass $http_upgrade;
		}

		# Maintenance pages.
		error_page 503 /maintenance.html;
		location = /maintenance.html {
			root D:/Users/<USER>/Documents/Git/merge/public/maintenance/;
		}
}
