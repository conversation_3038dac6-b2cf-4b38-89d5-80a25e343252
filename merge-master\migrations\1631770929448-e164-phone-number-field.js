/**
 * Note: This migration file was created when 'migrate-mongoose' was the npm package
 * used to perform MongoDB migrations, since the 'migrate' npm package is now used
 * the implementation may not be compatible and all functionality is commented out
 */

// const { isValidPhoneNumber, parsePhoneNumber } = require('libphonenumber-js');
// const mongoose = require('mongoose');

// const migrate = require('./migrate');

// const serviceCheckModel = new mongoose.Schema({
//     id: { type: String, index: true, immutable: true },
//     fnn: { type: String, index: true },
//     phoneNumber: { type: String, index: true, default: null },
//     billingFNN: String,
//     carriageFNN: String,
//     carriageFNNBackup: String,
//     MDNFNN: String,
//     carriageType: String,
//     nbnServiceType: String,
//     nbnAccessType: String,
//     nbnSubAccessType: String,
//     deviceName: String,
//     CIDN: String,
//     address: String,
//     OffshoreResources: String,
//     status: String,
//     createdBy: { type: String, required: true, immutable: true, default: "unknown", index: true },
//     createdOn: { type: Date, required: true, immutable: true, default: Date.now, index: true },
//     endedOn: Date,
//     durationMilliSec: Number,
//     input: {
//         searchFNN: String,
//         carriageType: String,
//         carriageFNN: String,
//         deviceName: String,
//         deviceIP: String,
//         level: Number,
//         process: String,
//         suite : String,
//         idType : String
//     },
//     additionalParameters: Object,
//     rulesData: Object,
//     sourcesData: Object,
//     saveError: String
// }, { minimize: false });

// const ServiceCheckModel = mongoose.model('servicechecks', serviceCheckModel);
/**
 * Make any changes you need to make to the database here
 */
async function up () {
    // await migrate.connect();

    // for await (const record of ServiceCheckModel.find({})) {
    //     if (isValidPhoneNumber(record.fnn, "AU")) {
    //         let phoneNumber = parsePhoneNumber(record.fnn, "AU");
    //         record.phoneNumber = phoneNumber.format("E.164");
    //     } else {
    //         record.phoneNumber = null;
    //     }

    //     await record.save();
    // }
}

/**
 * Make any changes that UNDO the up function side effects here (if possible)
 */
async function down () {
    // await migrate.connect();

    // await ServiceCheckModel.updateMany({"phoneNumber": {$exists: true}}, {
    //     $unset: { "phoneNumber": "" }
    // }, { multi: true, strict: false });
}

module.exports = { up, down };
