import { DetailsPanel } from '../../../../components/detailsPanel/DetailsPanel'
import { FunctionNotReady } from '../../../../components/functionNotReady/FunctionNotReady'
import { useAppState } from '../../../../hooks/useAppState'
import { LoadingPanel } from '../serviceCheckLoadingPanel/LoadingPanel'
import { TextTemplateRow } from './components/TextTemplateRow'
import styles from './TestSummaryTemplates.module.scss'

export const TestSummaryTemplates = () => {
    const { appState } = useAppState()
    const serviceTestState = appState.serviceDetails.status
    const textTemplates = appState.textTemplates

    // Conditional rendering logic.
    const testSummaryContent = (() => {
        if (serviceTestState === 'running') {
            return <LoadingPanel />
        } else if (
            serviceTestState !== 'completedWithError' &&
            serviceTestState !== 'done'
        ) {
            return <FunctionNotReady />
        } else {
            return (
                <div>
                    {textTemplates.map((template) => (
                        <TextTemplateRow
                            key={template.name}
                            template={template}
                            serviceId={appState.id}
                        />
                    ))}
                </div>
            )
        }
    })()

    return (
        <DetailsPanel
            label="Test Summary Templates"
            className={styles.testSummaryTemplates}
        >
            {testSummaryContent}
        </DetailsPanel>
    )
}
