<%- include('header') %>
<%- include('jsonEditInclude', {}) %>
<%- include('menu', {currentTab: 'Form'}); %>
<link href="/public/stylesheets/ace-diff.min.css" rel="stylesheet">
<link rel="stylesheet" type="text/css" href="/public/stylesheets/select2.min.css">
<script src="/socket.io/socket.io.js" crossorigin="anonymous"></script>
<script src="/public/javascripts/ace-diff.min.js" crossorigin="anonymous"></script>
<script src="/public/javascripts/select2.min.js" crossorigin="anonymous"></script>
<script src="/public/javascripts/merge_SC_lib.js" crossorigin="anonymous"></script>
<br>
<div class="container">
    <div class="row">
        <div class="col-8">
            <a href="/edit/apis">< Back to APIs</a>
        </div>
        <div class="col-4">
            <div class="float-right">
                <button class="btn btn-primary" id="createApi" data-toggle="modal" data-target="#confirmCreateModal" style="display:none;" title="Create" disabled><span class="fas fa-plus-square"></span></button>
                <button class="btn btn-primary" id="saveApi" data-toggle="modal" data-target="#confirmSaveModal" style="display:none;" title="Save" disabled><span class="fas fa-save"></span></button>
                <button class="btn btn-primary" id="refreshApi" data-toggle="modal" data-target="#confirmRefreshModal"  style="display:none;" title="Refresh"><span class="fas fa-sync"></span></button>
                <button class="btn btn-danger" id="deleteApi" data-toggle="modal" data-target="#confirmDeleteModal" style="display:none;" title="Delete" disabled><span class="fas fa-trash"></span></button>
            </div>
        </div>
    </div>
    <br>
    <div class="row">
        <div class="col-12">
            <div id="readonlyAlert" style="display:none;" class="alert alert-warning">Warning: API is read only, editing is disabled.</div>
        </div>
    </div>

    <ul class="nav nav-tabs" role="tablist">
        <li role="presentation" class="nav-item active">
            <a class="nav-link active" data-target="#editApisTab" data-toggle="tab" role="tab" href="">Edit API</a>
        </li>
    </ul>

    <div class="tab-content">
        <div class="tab-pane active border" id="editApisTab" style="height: 75vh;overflow-y: auto;padding-bottom: 16px;">
            <div style="padding: 2rem;">
                <div id="apisLoadError" class="alert alert-danger" role="alert" style="display: none;padding-left: 16px;"></div>
                <div id="apisEdit" style="display: none;"></div>
                <div class="tab-pane active" id="executeApisTab">
                    <div class="row">
                        <div class="col-md-6 border rounded codeBackground" style="padding:8px;">
                            <label for="developApisVariables" class="codeLabel">Input Variables</label>
                            <button id="resetApiVariables" class="btn btn-primary btn-sm" title="Reset variables"><span class="fas fa-undo"></span></button>
                            <button id="viewUrl" class="btn btn-primary btn-sm" title="View URL"><span class="fas fa-eye"></span></button>
                            <button id="runApiCode" class="btn btn-primary btn-sm" title="Run API"><span class="fas fa-play"></span></button>
                            <button id="cancelRunApiParameters" class="btn btn-secondary btn-sm" title="Cancels collection of API in the case of socket.io disconnect or other cases where no response is received" style="display:none;">Cancel</button>
                            <div class="alert alert-warning" role="alert" id="apiVariablesSizeAlert" style="display:none;">
                                <small>The input parameters text exceeds 8MB. This may cause errors when collecting the API.</small>
                            </div>
                            <div id="developApiVariables" style="height: 100%; width: 100%;"></div>
                        </div>
                        <div class="col-md-6 border rounded codeBackground" style="padding:8px;">
                            <label for="apiResponse" class="codeLabel">API Response</label>
                            <small id="apiOutputUpdatedAt" class="text-muted"></small>
                            <br>
                            <input id="apiResponseWordWrap" type="checkbox">
                            <label class="col-form-label" for="apiResponseWordWrap">Word Wrap</label>
                            <br>
                            <div class="alert alert-danger p-1" role="alert" id="apiViewUrlAlert" style="display:none;">
                                <small id="apiViewUrlAlertText"></small>
                            </div>
                            <div class="alert alert-danger p-1" role="alert" id="apiRunAlert" style="display:none;">
                                <small id="apiRunAlertText"></small>
                            </div>
                            <div id="apiStatusCode" class="text-left text-monospace"></div>
                            <div id="apiResponse" style="height: 100%; width: 100%;"></div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 border rounded bg-light" style="padding:8px;">
                            <label for="developOutputConsole" class="text-dark">Console</label>
                            <div id="developOutputConsole" style="height: 100%; width: 100%; max-height: 2160px; resize: vertical; overflow: hidden;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="createApiModal" class="modal fade">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Error</h5>
            </div>
            <div class="modal-body">
                <code id="createApiModalMessage" style="white-space:pre-wrap;"></code>
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-outline-secondary">Close</button>
            </div>
        </div>
    </div>
</div>

<div id="confirmSaveModal" class="modal fade">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Save</h5>
            </div>
            <div class="modal-body">
                Would you like to save the changes for this API?
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-primary" id="confirmSave">Save</button>
                <button type="button" data-dismiss="modal" class="btn btn-outline-secondary">Cancel</button>
            </div>
        </div>
    </div>
</div>

<div id="confirmRefreshModal" class="modal fade">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Refresh</h5>
            </div>
            <div class="modal-body">
                Would you like to refresh the API contents? Any changes made will be lost.
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-primary" id="confirmRefresh">Refresh</button>
                <button type="button" data-dismiss="modal" class="btn btn-outline-secondary">Cancel</button>
            </div>
        </div>
    </div>
</div>

<div id="confirmDeleteModal" class="modal fade">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
            </div>
            <div class="modal-body">
                Would you like to delete this API? This action cannot be reversed.
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-danger" id="confirmDelete">Delete</button>
                <button type="button" data-dismiss="modal" class="btn btn-outline-secondary">Cancel</button>
            </div>
        </div>
    </div>
</div>


<script>
    const apiName = atob("<%- apiName %>");
    const wikiBaseURL = "<%= global.gConfig.wikiBaseURL %>";
    const createApi = <%= createApi %>;
    const disableApiSourceEditing = <%- user.isAdmin ? false : global.gConfig.disableRuleSourceEditing %>;
    const socket = io();
    const schema = JSON.parse(atob("<%- schema %>"));

    var dataLoaded = false;
    var dataModified = false;

    $(document).ready(function() {
        $("#createApi").prop("disabled", disableApiSourceEditing);
        $("#saveApi").prop("disabled", disableApiSourceEditing);
        $("#deleteApi").prop("disabled", disableApiSourceEditing);

        let jsonEditor = new JSONEditor(document.getElementById('apisEdit'), {
            // Enable fetching schemas via ajax
            ajax: false,
            enable_array_copy: false,
            // The schema for the editor
            schema: schema,
            // Seed the form with a SCreules APIs for init
            startval: null,

            // Disable additional properties
            no_additional_properties: true,

            // Require all properties by default
            required_by_default: true,

            show_errors: "always"
        });

        let runApiCodeTimeout;

        if (disableApiSourceEditing) {
            jsonEditor.disable();
            $("#readonlyAlert").show();
        }

        const aceEditors = setupDevelopmentEditors([]);

        if (createApi) {
            $("#createApi").show();
            $("#apisEdit").show();
        } else {
            $("#saveApi").show();
            $("#refreshApi").show();
            $("#deleteApi").show();
            getApiToForm(jsonEditor, aceEditors.apiVariables);
        }

        $("#resetApiVariables").click(function() {
            let api = jsonEditor.getValue();
            aceEditors.apiVariables.setValue(nullVariableList(api.parameters));
            aceEditors.apiVariables.clearSelection();
        });

        $("#viewUrl").click(function() {
            let api = jsonEditor.getValue();
            $("#apiViewUrlAlert").hide();
            $("#apiRunAlert").hide();

            socket.emit('apiDev:createUrl', {
                variables: aceEditors.apiVariables.getValue(),
                api: api
            });
        });

        $("#runApiCode").click(function() {
            let api = jsonEditor.getValue();

            $("#cancelRunApiParameters").show();
            setButtonSpinner($("#runApiCode"), $("<span>").addClass("fas fa-play"), true);
            editorAppendText(aceEditors.developOutputConsole, "Api run started");
            $("#apiViewUrlAlert").hide();
            $("#apiRunAlert").hide();

            $("#apiStatusCode").html("");
            aceEditors.apiResponse.setValue("");
            aceEditors.apiResponse.clearSelection();

            socket.emit('apiDev:runApiCode', {
                variables: aceEditors.apiVariables.getValue(),
                api: api
            });
        });

        $("#createApi").click(function() {
            createApiFromForm(jsonEditor);
        });

        $("#confirmSave").click(function() {
            writeApiFromForm(jsonEditor);
        });

        $("#confirmRefresh").click(function() {
            getApiToForm(jsonEditor, aceEditors.apiVariables);
        });

        $("#confirmDelete").click(function() {
            $.ajax({
                type: "DELETE",
                url: `/apis/${encodeURIComponent(apiName)}`,
                success: function (response) {
                    window.location.href = "/edit/apis";
                },
                error: function (error) {
                    alert("Error, could not delete API:" + error.responseText);
                },
            });
        });

        $("#apiResponseWordWrap").change(function() {
            aceEditors.apiResponse.setOption("wrap", this.checked);
        });

        socket.on('apiDev:createUrlResult', (data) => {
            if (data.error) {
                $("#apiViewUrlAlertText").text(data.error);
                $("#apiViewUrlAlert").removeClass('alert-success').addClass('alert-danger');
                $("#apiViewUrlAlert").show();
                editorAppendText(aceEditors.developOutputConsole, "View URL error, " + data.error);
            } else {
                $("#apiViewUrlAlertText").text(data.url);
                $("#apiViewUrlAlert").removeClass('alert-danger').addClass('alert-success');
                $("#apiViewUrlAlert").show();
                editorAppendText(aceEditors.developOutputConsole, "View URL competed, url: " + data.url);
            }
        });

        socket.on('apiDev:runApiCodeResult', (data) => {
            $("#cancelRunApiParameters").hide();
            setButtonSpinner($("#runApiCode"), $("<span>").addClass("fas fa-play"), false);

            $("#apiStatusCode").html("");
            editorAppendText(aceEditors.developOutputConsole, "Api run completed");

            if (data.error) {
                $("#apiRunAlertText").text(data.error);
                $("#apiRunAlert").show();
                editorAppendText(aceEditors.developOutputConsole, "Api run error, " + data.error);
            }

            if (data.response != null) {
                let currDate = new Date().toLocaleString("en-GB");
                $("#apiOutputUpdatedAt").text(`Last updated at: ${currDate} Duration: ${data.collectionDuration ? data.collectionDuration : ''} seconds`);

                if (typeof data.status === 'number') {
                    $("#apiStatusCode").append("HTTP status code: ");
                    $("#apiStatusCode").append($("<span>").addClass("badge badge-light").text(data.status));
                }

                let response;
                let mode;

                if (typeof(data.response) === 'object' && data.response != null) {
                    response = JSON.stringify(data.response, null, 4);
                    mode = "ace/mode/json";
                } else {
                    response = data.response;
                    mode = "ace/mode/text";
                }

                aceEditors.apiResponse.setOption("mode", mode);
                aceEditors.apiResponse.setValue(response);
                aceEditors.apiResponse.clearSelection();
            }
        });

        $("#cancelRunApiParameters").click(function() {
            // Hides the cancel button and enables the run API code button again
            setButtonSpinner($("#runApiCode"), $("<span>").addClass("fas fa-play"), false);
            $("#cancelRunApiParameters").hide();
        });

        jsonEditor.on("change", function() {
            // If the api was loaded recently via AJAX, the change event will still be triggered,
            // do not count this instance as the api being modified by the user
            if (!dataLoaded) {
                dataModified = true;
            } else {
                dataLoaded = false;
            }
        });

        aceEditors.apiVariables.getSession().on('change', function(e) {
            // Warns the user if the input variables are larger than 8MB as a payload greater than
            // 10MB will cause the socket.io connection to be terminated by the server
            if (aceEditors.apiVariables.getValue().length >= 8e6) {
                $("#apiVariablesSizeAlert").show();
            } else {
                $("#apiVariablesSizeAlert").hide();
            }
        });

        $(window).on("beforeunload", function(e) {
            if (dataModified) {
                return confirm("");
            } else {
                return;
            }
        });
    });

    function getApiToForm(editor, apiVariablesEditor) {
        setButtonSpinner($("#refreshApi"), $("<span>").addClass("fas fa-sync"), true);

        $.ajax({
            cache: false,
            type: "GET",
            url: `/apis/${encodeURIComponent(apiName)}`,
            dataType: "json",
            success: function (response) {
                dataLoaded = true;
                dataModified = false;

                editor.setValue(response);
                $("#apisEdit").show();

                // Load code obtained from request into execute api editors
                $("#developRefreshCode").click();
                let apiVariables =
                editor.apiVariables = ace.edit("developApiVariables", {
                    mode: "ace/mode/javascript",
                    minLines: 3,
                    maxLines: 32
                });

                // If the input variables text input is empty, add the variables from the response
                // to the editor
                if (apiVariablesEditor.getValue().trim().length === 0) {
                    apiVariablesEditor.setValue(nullVariableList(response.parameters));
                    apiVariablesEditor.clearSelection();
                }
            },
            error: function (error) {
                let errorMessage = '';
                if (error.status == 404) {
                    errorMessage = `Api ${apiName} does not exist`;
                } else {
                    errorMessage = `Error encountered loading API ${apiName}: HTTP ${error.status} (${error.statusText}): ${error.responseText}`;
                }
                $("#apisLoadError").text(errorMessage);
                $("#apisLoadError").show();
            },
            complete: function(xhr, status) {
                setButtonSpinner($("#refreshApi"), $("<span>").addClass("fas fa-sync"), false);
            }
        });
    }

    function createApiFromForm(editor) {
        let errors = editor.validate();

        if (!errors.length) {
            $.ajax({
                type: "POST",
                url: "/apis/",
                contentType: "application/json",
                dataType: "json",
                data: JSON.stringify(editor.getValue()),
                success: function (response) {
                    dataModified = false;
                    window.location.href = `/edit/apis/${encodeURIComponent(editor.getValue().name)}`;
                },
                error: function (error) {
                    if (error.status == 400) {
                        $("#createApiModalMessage").text(`Error in fields for api:\n${JSON.stringify(error.responseJSON, null, 4)}`);
                    } else if (error.status == 409) {
                        $("#createApiModalMessage").text(`A api with name ${editor.getValue().name} already exists.`);
                    } else {
                        $("#createApiModalMessage").text(`Unknown Error:\n${error.responseJSON}`);
                    }

                    $("#createApiModal").modal({ show: true });
                },
            });
        } else {
            $("#createApiModalMessage").text("Validation errors in editor, please check fields in form.");
            $("#createApiModal").modal({ show: true });
        }
    }

    function writeApiFromForm(editor) {
        let errors = editor.validate();

        if (!errors.length) {
            setButtonSpinner($("#saveApi"), $("<span>").addClass("fas fa-save"), true);

            $.ajax({
                cache: false,
                type: "PUT",
                url: `/apis/${encodeURIComponent(apiName)}`,
                contentType: "application/json",
                dataType: "json",
                data: JSON.stringify(editor.getValue()),
                success: function (_) {
                    dataModified = false;
                },
                error: function(error) {
                    // just alerts the user for now
                    alert("Error, could not save api:" + error.responseText);
                },
                complete: function(xhr, status) {
                    setButtonSpinner($("#saveApi"), $("<span>").addClass("fas fa-save"), false);
                }
            });
        } else {
            $("#createApiModalMessage").text("Validation errors in editor, please check fields in form.");
            $("#createApiModal").modal({ show: true });
        }
    }

    function setupDevelopmentEditors(parameters) {
        let editors = {};
        editors.apiVariables = ace.edit("developApiVariables", {
            mode: "ace/mode/javascript",
            minLines: 3,
            maxLines: 32
        });

        editors.apiVariables.setValue(nullVariableList(parameters));
        editors.apiVariables.clearSelection();

        editors.apiResponse = ace.edit("apiResponse", {
            mode: "ace/mode/json",
            minLines: 8,
            maxLines: 32,
            readOnly: true,
            showLineNumbers: false,
            printMargin: false
        });

        editors.developOutputConsole = ace.edit("developOutputConsole", {
            mode: "ace/mode/text",
            minLines: 10,
            maxLines: 100,
            readOnly: true,
            showLineNumbers: false,
            printMargin: false,
            showGutter: false,
            showFoldWidgets: false
        });

        return Object.freeze(editors);
    }

    function editorAppendText(editor, text) {
        let currDate = new Date().toLocaleString("en-GB");
        editor.session.insert({
            row: editor.session.getLength(),
            column: 0
        }, `[${currDate}] ${text}\n`);
    }

    function nullVariableList(parameters) {
        let variableList = "";

        for (let i = 0; i < parameters.length; i++) {
            variableList += `${parameters[i]} = null;\n`;
        }

        return variableList;
    }
</script>
</body>
</html>
