@use '@able/web/src/index' as able;

.modalContent {
    white-space: pre-wrap; // Preserve line breaks in content

    margin-top: able.spacing(spacing2x);
    
    .summary {
        margin-top: 1rem;
        display: flex;
        align-items: center;
   
    }

    .modalActions {
        display: flex;
        gap: 1rem;
        align-items: center;
        justify-content: end;
        
        .modalButton {
            padding: able.spacing(spacing1x) able.spacing(spacing2x);
            background-color: able.color(materialBaseSecondary); // Success button color
            color: able.color(textOnPrimary); // Text color for success buttons
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border-radius: 10px;   
        }
    }
}
