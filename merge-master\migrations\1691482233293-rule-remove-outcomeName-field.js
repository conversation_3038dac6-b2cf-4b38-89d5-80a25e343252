/**
 * Note: This migration file was created when 'migrate-mongoose' was the npm package
 * used to perform MongoDB migrations, since the 'migrate' npm package is now used
 * the implementation may not be compatible and all functionality is commented out
 */

// const mongoose = require('mongoose');
// const migrate = require('./migrate');

// const actionSchema = new mongoose.Schema({
//     api: { type: String, default: null, required: true },
//     parameterCode: { type: String, default: "" },
//     codeCondition: { type: String, default: "" },
//     autoExecution: { type: Boolean, default: false }
// }, { _id: false });

// const ruleSchema = new mongoose.Schema({
//     name: { type: String, default: null, validate: /^[A-Z]{3}[0-9]{3}$/, unique: true, required: true, immutable: true },
//     active: { type: Boolean, default: false },
//     hideInResult: { type: Boolean, default: false },
//     showReject: { type: Boolean, default: false },
//     title: { type: String, default: "" },
//     rootCauseCategory: { type: String, enum: [
//         "Administration",
//         "CommPilot",
//         "Configuration",
//         "Connection",
//         "Data Collection",
//         "Planned Outage",
//         "Sable",
//         "Service Status",
//         "Telstra Network",
//         "Test Team",
//         "3rd Party Network",
//         "Ticket",
//         "Unplanned Outage"
//     ], default: "Administration" },
//     level: { type: Number, default: 0, min: 0, max: 6 },
//     description: { type: String, default: "" },
//     wikiPage: { type: String, default: "" },
//     ruleType: { type: String, enum: [
//         "Logical",
//         "Extract",
//         "Action",
//         "Start Service Check",
//         "Symptom",
//         "Outcome"
//     ], default: "Logical" },
//     isWarning: { type: Boolean, default: false },
//     displayInSummary: { type: Boolean, default: false },
//     failedInSummary: { type: Boolean, default: true },
//     preSources: {
//         type: [{
//             type: String,
//             validate: /^(?!InRequest-.*)[A-Za-z]+[:\w\-\.\s]*$/
//         }],
//         default: [],
//         validate: {
//             validator: function(preSources) {
//                 return preSources.length === new Set(preSources).size;
//             },
//             message: "array should only contain unique string values"
//         }
//     },
//     preSourcesRunOnFail: { type: Boolean, default: false },
//     preRules: {
//         type: [{
//             type: String,
//             validate: /^[A-Z]{3}[0-9]{3}$/
//         }],
//         default: [],
//         validate: {
//             validator: function(preRules) {
//                 return preRules.length === new Set(preRules).size;
//             },
//             message: "array should only contain unique string values"
//         }
//     },
//     preRulesRunOnFail: { type: Boolean, default: false },
//     runWhenDependenciesResolved: { type: Boolean, default: false },
//     depedenciesFromRelatedServiceChecks: { type: Boolean, default: false },
//     runOnlyWhenSymptomsPresent: { type: Boolean, default: false },
//     preCondition: { type: String, default: "" },
//     preConditionMsg: { type: String, default: "" },
//     trueMsg: { type: String, default: "" },
//     falseMsg: { type: String, default: "" },
//     errorMsg: { type: String, default: "" },
//     ruleCode: { type: String, default: "" },
//     valueMsgStm: { type: String, default: "" },
//     extraInfo: { type: String, default: "" },
//     ignoreCompare: { type: Boolean, default: false },
//     overrideCompare: { type: String, default: "" },
//     unitTests: {
//         type: [{
//             type: mongoose.Schema.Types.Mixed
//         }],
//         default: []
//     },
//     action: {
//         type: actionSchema,
//         default: null
//     },
//     createdBy: { type: String, required: true, immutable: true, default: "unknown" },
//     createdOn: { type: Date, required: true, immutable: true, default: Date.now }
// }, {
//     autoIndex: true
// });


// ruleSchema.methods.toJSON = function() {
//     let obj = this.toObject();
//     delete obj._id;
//     delete obj.__v;
//     return obj;
// }


// const Rule = mongoose.model("rule", ruleSchema);


/**
 * Make any changes you need to make to the database here
 */
async function up () {
    // Write migration here
    // await migrate.connect();

    // await Rule.updateMany({}, {
    //     $unset: {
    //         outcomeName: 1
    //     }
    // }, { multi: true, strict: false });
}

/**
 * Make any changes that UNDO the up function side effects here (if possible)
 */
async function down () {
    // Write migration here
}

module.exports = { up, down };
