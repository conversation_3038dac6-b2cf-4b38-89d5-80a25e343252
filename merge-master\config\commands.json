[{"name": "ALWAYSON", "title": "AlwaysOn", "description": "Get service information from Always On", "type": "API", "api": "AlwaysOn", "param": [{"name": "fnn", "switch": "-f or --fnn (or first param)", "help": "search term like fnn"}], "paramCode": "fnn= p.fnn || p.f || p._[1]", "example": "ALWAYSON -f N2651597R", "help": "Search a term like FNN", "errorMessage": ""}, {"name": "B2B", "title": "B2B", "description": "B2B data from Network Service OKAPI", "type": "API", "api": "B2B", "param": [{"name": "fnn", "switch": "-f or --fnn (or first param)", "help": "search term like fnn"}], "paramCode": "fnn= p.fnn || p.f || p._[1]", "example": "B2B -f N2651597R", "help": "Search a term like FNN", "errorMessage": ""}, {"name": "B2BS", "title": "B2BS", "description": "B2B data from Service OKAPI", "type": "API", "api": "B2BS", "param": [{"name": "fnn", "switch": "-f or --fnn (or first param)", "help": "search term like fnn"}], "paramCode": "fnn= p.fnn || p.f || p._[1]", "example": "B2BS -f N2651597R", "help": "Search a term like FNN", "errorMessage": ""}, {"name": "BF", "title": "BandF", "description": "Virtual Operator API for Bruno and Fluffy API", "type": "API", "api": "BandF", "param": [{"name": "fnn", "switch": "-f or --fnn (or first param)", "help": "search term like fnn"}, {"name": "IDENTIFIER", "switch": "-ID or --IDENTIFIER", "help": "search term like Id"}], "paramCode": "fnn= p.fnn || p.f || p._[1]; id = p.IDENTIFIER || p.ID", "example": "BF -f 61448724923 -ID BandF2a-6694ab072879a97f7b0a2e79", "help": "Search a term like FNN", "errorMessage": ""}, {"name": "BASEMENTPORTRESET", "title": "BasementPortReset", "description": "Reset basement switch port via API in hangar", "type": "API", "api": "BasementPortReset", "param": [{"name": "fnn", "switch": "-f or --fnn (or first param)", "help": "search term like fnn"}], "paramCode": "FNN= p.fnn || p.f || p._[1]", "example": "BASEMENTPORTRESET -f N2651597R", "help": "Reset basement switch port for the given search term (FNN)", "errorMessage": ""}, {"name": "BATIPV4", "title": "BATIPV4", "description": "Search for Sessions by IPv4 Address via the Broadband Assurance Tool", "type": "API", "api": "BATIPV4", "param": [{"name": "ip", "switch": "--ip (or first param.)", "help": "IPv4 Address"}], "paramCode": "BATipv4 = p.ip || p._[1];", "example": "BATIPV4 X.X.X.X", "help": "Search for Sessions by IPv4 Address via the Broadband Assurance Tool.", "errorMessage": ""}, {"name": "BATIPV6", "title": "BATIPV6", "description": "Search for Sessions by IPv4 Address via the Broadband Assurance Tool", "type": "API", "api": "BATIPV6", "param": [{"name": "ip", "switch": "--ip (or first param.)", "help": "IPv4 Address"}], "paramCode": "BATipv6 = p.ip || p._[1];", "example": "BATIPV6 X:X:X:X:X:X:X", "help": "Search for Sessions by IPv4 Address via the Broadband Assurance Tool.", "errorMessage": ""}, {"name": "BATMODEM", "title": "BATMODEM", "description": "Broadband Assurance Tool Session by MODEM", "type": "API", "api": "BATMODEM", "param": [{"name": "serialNumber", "switch": "--sn or --serialNumber (or first param)", "help": "search term like B03956-4P947C5R04172"}, {"name": "model", "switch": "--model", "help": "search term like V7610"}], "paramCode": "BATserial = `p.serialNumber || p.sn || p._[1]`; BATmodel = p.model;", "example": "BATMODEM --serialNumber B03956-4P947C5R04172 --model V7610", "help": "Broadband Assurance Tool Session by MODEM for the given modela and serial number", "errorMessage": ""}, {"name": "BATNBN", "title": "BATNBN", "description": "Broadband Assurance Tool Session by NBN", "type": "API", "api": "BATNBN", "param": [{"name": "avc", "switch": "--f or --fnn or --avc (or first param)", "help": "search term like avc"}], "paramCode": "BATavc = p.fnn || p.f || p.avc || p._[1];", "example": "BATNBN --f AVC000156586813", "help": "Broadband Assurance Tool Session by NBN for the given avc", "errorMessage": ""}, {"name": "BATNEMDMS", "title": "BATNEMDMS", "description": "Broadband Assurance Tool Session by NEMDMS", "type": "API", "api": "BATNEMDMS", "param": [{"name": "avc", "switch": "--f or --fnn or --avc (or first param)", "help": "search term like fnn or avc"}], "paramCode": "BATinput = p.fnn || p.f || p.avc || p._[1];", "example": "BATNEMDMS --f AVC000156586813", "help": "Broadband Assurance Tool Session by NEMDMS for the given avc", "errorMessage": ""}, {"name": "BATSID", "title": "BATSID", "description": "Search for Sessions by Session ID via the Broadband Assurance Tool", "type": "API", "api": "BATSID", "param": [{"name": "sid", "switch": "--sid or --sessionID (or first param.)", "help": "<BigPond ID | NR Number, MAC Address, iWifi User ID>"}], "paramCode": "BATsid = p.sid || p.sessionID || p._[1];", "example": "BATSID NxxxxxxxR", "help": "Search for Sessions by Session ID via the Broadband Assurance Tool.", "errorMessage": ""}, {"name": "BATTAAA", "title": "BATTAAA", "description": "Broadband Assurance Tool Session by TAAA", "type": "API", "api": "BATTAAA", "param": [{"name": "avc", "switch": "--f or --fnn or --avc (or first param)", "help": "search term like avc"}], "paramCode": "BATuid = p.fnn || p.f || p.avc || p._[1];", "example": "BATTAAA --f AVC000063878037", "help": "Broadband Assurance Tool Session by TAAA for the given avc", "errorMessage": ""}, {"name": "BSIPC", "title": "BSIPC", "description": "BSIP customer data from OKAPI", "type": "API", "api": "BSIPC", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}], "paramCode": "cid = p.cid || p._[1];", "example": "BSIPC --cid TC8844097185", "help": "BSIP customer data from OKAPI for the given TC number", "errorMessage": ""}, {"name": "BSIPCC", "title": "BSIPCC", "description": "BSIP customer CDR data from OKAPI", "type": "API", "api": "BSIPCC", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}], "paramCode": "cid = p.cid || p._[1];", "example": "BSIPCC --cid TC8844097185", "help": "BSIP customer CDR data from OKAPI for the given TC number", "errorMessage": ""}, {"name": "BSIPCS", "title": "BSIPCS", "description": "BSIP customer site data from OKAPI", "type": "API", "api": "BSIPCS", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}], "paramCode": "cid = p.cid || p._[1];", "example": "BSIPCS --cid TC8844097185", "help": "BSIP customer site data from OKAPI for the given TC number", "errorMessage": ""}, {"name": "BSIPCSA1", "title": "BSIPCSA1", "description": "BSIP customer site access data from OKAPI", "type": "API", "api": "BSIPCSA1", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}, {"name": "stNumber", "switch": "--sid", "help": "search term like ST**********"}], "paramCode": "cid = p.cid || p._[1];sid = p.sid;", "example": "BSIPCSA1 --cid TC8844097185 --sid ST2870659201", "help": "BSIP customer site access data from OKAPI for the given TC and ST number", "errorMessage": ""}, {"name": "BSIPCSA2", "title": "BSIPCSA2", "description": "BSIP customer site activation data from OKAPI", "type": "API", "api": "BSIPCSA2", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}, {"name": "stNumber", "switch": "--sid", "help": "search term like ST**********"}], "paramCode": "cid = p.cid || p._[1];sid = p.sid;", "example": "BSIPCSA2 --cid TC8844097185 --sid ST2870659201", "help": "BSIP customer site activation data from OKAPI for the given TC and ST number", "errorMessage": ""}, {"name": "BSIPCSA3", "title": "BSIPCSA3", "description": "BSIP customer site admins data from OKAPI", "type": "API", "api": "BSIPCSA3", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}, {"name": "stNumber", "switch": "--sid", "help": "search term like ST**********"}], "paramCode": "cid = p.cid || p._[1];sid = p.sid;", "example": "BSIPCSA3 --cid TC8844097185 --sid ST2870659201", "help": "BSIP customer site admins data from OKAPI for the given TC and ST number", "errorMessage": ""}, {"name": "BSIPCSA4", "title": "BSIPCSA4", "description": "BSIP customer site analogue data from OKAPI", "type": "API", "api": "BSIPCSA4", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}, {"name": "stNumber", "switch": "--sid", "help": "search term like ST**********"}], "paramCode": "cid = p.cid || p._[1];sid = p.sid;", "example": "BSIPCSA4 --cid TC8844097185 --sid ST2870659201", "help": "BSIP customer site analogue data from OKAPI for the given TC and ST number", "errorMessage": ""}, {"name": "BSIPCSAU", "title": "BSIPCSAU", "description": "BSIP customer site analogue users data from OKAPI", "type": "API", "api": "BSIPCSAU", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}, {"name": "stNumber", "switch": "--sid", "help": "search term like ST**********"}], "paramCode": "cid = p.cid || p._[1];sid = p.sid;", "example": "BSIPCSAU --cid TC8844097185 --sid ST2870659201", "help": "BSIP customer site analogue users data from OKAPI for the given TC and ST number", "errorMessage": ""}, {"name": "BSIPCSBC", "title": "BSIPCSBC", "description": "BSIP customer site business continuity data from OKAPI", "type": "API", "api": "BSIPCSBC", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}, {"name": "stNumber", "switch": "--sid", "help": "search term like ST**********"}], "paramCode": "cid = p.cid || p._[1];sid = p.sid;", "example": "BSIPCSBC --cid TC8844097185 --sid ST2870659201", "help": "BSIP customer site business continuity data from OKAPI for the given TC and ST number", "errorMessage": ""}, {"name": "BSIPCSCB", "title": "BSIPCSCB", "description": "BSIP customer site call barring data from OKAPI", "type": "API", "api": "BSIPCSCB", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}, {"name": "stNumber", "switch": "--sid", "help": "search term like ST**********"}], "paramCode": "cid = p.cid || p._[1];sid = p.sid;", "example": "BSIPCSCB --cid TC8844097185 --sid ST2870659201", "help": "BSIP customer site call barring data from OKAPI for the given TC and ST number", "errorMessage": ""}, {"name": "BSIPCSD", "title": "BSIPCSD", "description": "BSIP customer site devices data from OKAPI", "type": "API", "api": "BSIPCSD", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}, {"name": "stNumber", "switch": "--sid", "help": "search term like ST**********"}], "paramCode": "cid = p.cid || p._[1];sid = p.sid;", "example": "BSIPCSD --cid TC8844097185 --sid ST2870659201", "help": "BSIP customer site devices data from OKAPI for the given TC and ST number", "errorMessage": ""}, {"name": "BSIPCSE", "title": "BSIPCSE", "description": "BSIP customer site extensions data from OKAPI", "type": "API", "api": "BSIPCSE", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}, {"name": "stNumber", "switch": "--sid", "help": "search term like ST**********"}], "paramCode": "cid = p.cid || p._[1];sid = p.sid;", "example": "BSIPCSE --cid TC8844097185 --sid ST2870659201", "help": "BSIP customer site devices data from OKAPI for the given TC and ST number", "errorMessage": ""}, {"name": "BSIPCSFP", "title": "BSIPCSFP", "description": "BSIP customer site feature packs data from OKAPI", "type": "API", "api": "BSIPCSFP", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}, {"name": "stNumber", "switch": "--sid", "help": "search term like ST**********"}], "paramCode": "cid = p.cid || p._[1];sid = p.sid;", "example": "BSIPCSFP --cid TC8844097185 --sid ST2870659201", "help": "BSIP customer site feature packs data from OKAPI for the given TC and ST number", "errorMessage": ""}, {"name": "BSIPCSHG", "title": "BSIPCSHG", "description": "BSIP customer site hunt groups data from OKAPI", "type": "API", "api": "BSIPCSHG", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}, {"name": "stNumber", "switch": "--sid", "help": "search term like ST**********"}], "paramCode": "cid = p.cid || p._[1];sid = p.sid;", "example": "BSIPCSHG --cid TC8844097185 --sid ST2870659201", "help": "BSIP customer site hunt groups data from OKAPI for the given TC and ST number", "errorMessage": ""}, {"name": "BSIPCSN", "title": "BSIPCSN", "description": "BSIP customer site numbers data from OKAPI", "type": "API", "api": "BSIPCSN", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}, {"name": "stNumber", "switch": "--sid", "help": "search term like ST**********"}], "paramCode": "cid = p.cid || p._[1];sid = p.sid;", "example": "BSIPCSN --cid TC8844097185 --sid ST2870659201", "help": "BSIP customer site hunt groups data from OKAPI for the given TC and ST number", "errorMessage": ""}, {"name": "BSIPCSS", "title": "BSIPCSS", "description": "BSIP customer site services data from OKAPI", "type": "API", "api": "BSIPCSS", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}, {"name": "stNumber", "switch": "--sid", "help": "search term like ST**********"}], "paramCode": "cid = p.cid || p._[1];sid = p.sid;", "example": "BSIPCSS --cid TC8844097185 --sid ST2870659201", "help": "BSIP customer site services data from OKAPI for the given TC and ST number", "errorMessage": ""}, {"name": "BSIPCSSS", "title": "BSIPCSSS", "description": "BSIP customer site site-services data from OKAPI", "type": "API", "api": "BSIPCSSS", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}, {"name": "stNumber", "switch": "--sid", "help": "search term like ST**********"}], "paramCode": "cid = p.cid || p._[1];sid = p.sid;", "example": "BSIPCSSS --cid TC8844097185 --sid ST2870659201", "help": "BSIP customer site site-services data from OKAPI for the given TC and ST number", "errorMessage": ""}, {"name": "BSIPCST", "title": "BSIPCST", "description": "BSIP customer site trunk data from OKAPI", "type": "API", "api": "BSIPCST", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}, {"name": "stNumber", "switch": "--sid", "help": "search term like ST**********"}], "paramCode": "cid = p.cid || p._[1];sid = p.sid;", "example": "BSIPCST --cid TC8844097185 --sid ST2870659201", "help": "BSIP customer site trunk data from OKAPI for the given TC and ST number", "errorMessage": ""}, {"name": "BSIPCSTC1", "title": "BSIPCSTC1", "description": "BSIP customer site trunk contact from OKAPI", "type": "API", "api": "BSIPCSTC1", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}, {"name": "stNumber", "switch": "--sid", "help": "search term like ST**********"}], "paramCode": "cid = p.cid || p._[1];sid = p.sid;", "example": "BSIPCSTC1 --cid TC8844097185 --sid ST2870659201", "help": "BSIP customer site trunk contact from OKAPI for the given TC and ST number", "errorMessage": ""}, {"name": "BSIPCSTC2", "title": "BSIPCSTC2", "description": "BSIP customer site trunk credentials from OKAPI", "type": "API", "api": "BSIPCSTC2", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}, {"name": "stNumber", "switch": "--sid", "help": "search term like ST**********"}], "paramCode": "cid = p.cid || p._[1];sid = p.sid;", "example": "BSIPCSTC2 --cid TC8844097185 --sid ST2870659201", "help": "BSIP customer site trunk credentials from OKAPI for the given TC and ST number", "errorMessage": ""}, {"name": "BSIPCSTF", "title": "BSIPCSTF", "description": "BSIP customer site trunk features from OKAPI", "type": "API", "api": "BSIPCSTF", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}, {"name": "stNumber", "switch": "--sid", "help": "search term like ST**********"}], "paramCode": "cid = p.cid || p._[1];sid = p.sid;", "example": "BSIPCSTF --cid TC8844097185 --sid ST2870659201", "help": "BSIP customer site trunk features from OKAPI for the given TC and ST number", "errorMessage": ""}, {"name": "BSIPCSTP", "title": "BSIPCSTP", "description": "BSIP customer site trunk partition data from OKAPI", "type": "API", "api": "BSIPCSTP", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}, {"name": "stNumber", "switch": "--sid", "help": "search term like ST**********"}], "paramCode": "cid = p.cid || p._[1];sid = p.sid;", "example": "BSIPCSTP --cid TC8844097185 --sid ST2870659201", "help": "BSIP customer site trunk partition data from OKAPI for the given TC and ST number", "errorMessage": ""}, {"name": "BSIPCSTRD", "title": "BSIPCSTRD", "description": "BSIP customer site trunk reporting data from OKAPI", "type": "API", "api": "BSIPCSTRD", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}, {"name": "stNumber", "switch": "--sid", "help": "search term like ST**********"}, {"name": "year", "switch": "--year", "help": "search term like 2020"}, {"name": "month", "switch": "--month", "help": "search term like 6"}, {"name": "gmetrics", "switch": "--g<PERSON>", "help": "search term like size,CapExc etc"}], "paramCode": "cid = p.cid || p._[1]; sid = p.sid; ayear = p.year; amonth = p.month; getmetrics = p.gmetric;", "example": "BSIPCSTRD --cid TC8844097185 --sid ST2870659201 --year 2024 --month 6 --getmetrics CapExc,UnrDest,TermFail", "help": "BSIP customer site trunk reporting data from OKAPI for the given TC and ST number", "errorMessage": ""}, {"name": "BSIPCSTRM", "title": "BSIPCSTRM", "description": "BSIP customer site trunk reporting metrics data from OKAPI", "type": "API", "api": "BSIPCSTRM", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}, {"name": "stNumber", "switch": "--sid", "help": "search term like ST**********"}], "paramCode": "cid = p.cid || p._[1]; sid = p.sid", "example": "BSIPCSTRM --cid TC8844097185 --sid ST2870659201", "help": "BSIP customer site trunk reporting metrics data from OKAPI for the given TC and ST number", "errorMessage": ""}, {"name": "BSIPCSTU", "title": "BSIPCSTU", "description": "BSIP customer site trunk users data from OKAPI", "type": "API", "api": "BSIPCSTU", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}, {"name": "stNumber", "switch": "--sid", "help": "search term like ST**********"}], "paramCode": "cid = p.cid || p._[1]; sid = p.sid", "example": "BSIPCSTU --cid TC8844097185 --sid ST2870659201", "help": "BSIP customer site trunk users data from OKAPI for the given TC and ST number", "errorMessage": ""}, {"name": "BSIPCSU", "title": "BSIPCSU", "description": "BSIP customer site users data from OKAPI", "type": "API", "api": "BSIPCSU", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}, {"name": "stNumber", "switch": "--sid", "help": "search term like ST**********"}], "paramCode": "cid = p.cid || p._[1]; sid = p.sid", "example": "BSIPCSU --cid TC8844097185 --sid ST2870659201", "help": "BSIP customer site users data from OKAPI for the given TC and ST number", "errorMessage": ""}, {"name": "BSIPCSVR", "title": "BSIPCSVR", "description": "BSIP customer site virtual receptionists data from OKAPI", "type": "API", "api": "BSIPCSVR", "param": [{"name": "tcNumber", "switch": "--cid (or first param)", "help": "search term like TC**********"}, {"name": "stNumber", "switch": "--sid", "help": "search term like ST**********"}], "paramCode": "cid = p.cid || p._[1]; sid = p.sid", "example": "BSIPCSVR --cid TC8844097185 --sid ST2870659201", "help": "BSIP customer site virtual receptionists data from OKAPI for the given TC and ST number", "errorMessage": ""}, {"name": "BSIPS", "title": "BSIPS", "description": "BSIP service provider data from OKAPI", "type": "API", "api": "BSIPS", "param": [{}], "paramCode": "", "example": "BSIPS", "help": "BSIP service provider data from OKAPI", "errorMessage": ""}, {"name": "BSIPU", "title": "BSIPU", "description": "BSIP user data from OKAPI", "type": "API", "api": "BSIPU", "param": [{"name": "fnn", "switch": "-f or --fnn or (or first param)", "help": "search term like fnn"}], "paramCode": "fnn= p.fnn || p.f || p._[1]", "example": "BSIPU --fnn **********", "help": "BSIP user data from OKAPI for the given FNN", "errorMessage": ""}, {"name": "BSIPUCF", "title": "BSIPUCF", "description": "BSIP user calling features from OKAPI", "type": "API", "api": "BSIPUCF", "param": [{"name": "fnn", "switch": "-f or --fnn or (or first param)", "help": "search term like fnn"}], "paramCode": "fnn= p.fnn || p.f || p._[1]", "example": "BSIPUCF --fnn **********", "help": "BSIP user calling features from OKAPI for the given FNN", "errorMessage": ""}, {"name": "BSIPUCFA", "title": "BSIPUCFA", "description": "BSIP user call forwarding always data from OKAPI", "type": "API", "api": "BSIPUCFA", "param": [{"name": "fnn", "switch": "-f or --fnn or (or first param)", "help": "search term like fnn"}], "paramCode": "fnn= p.fnn || p.f || p._[1]", "example": "BSIPUCFA --fnn **********", "help": "BSIP user call forwarding always data from OKAPI for the given FNN", "errorMessage": ""}, {"name": "BSIPUCFB", "title": "BSIPUCFB", "description": "BSIP user call forwarding busy data from OKAPI", "type": "API", "api": "BSIPUCFB", "param": [{"name": "fnn", "switch": "-f or --fnn or (or first param)", "help": "search term like fnn"}], "paramCode": "fnn= p.fnn || p.f || p._[1]", "example": "BSIPUCFB --fnn **********", "help": "BSIP user call forwarding busy data from OKAPI for the given FNN", "errorMessage": ""}, {"name": "BSIPUCFNA", "title": "BSIPUCFNA", "description": "BSIP user call forwarding no answering data from OKAPI", "type": "API", "api": "BSIPUCFNA", "param": [{"name": "fnn", "switch": "-f or --fnn or (or first param)", "help": "search term like fnn"}], "paramCode": "fnn= p.fnn || p.f || p._[1]", "example": "BSIPUCFNA --fnn **********", "help": "BSIP user call forwarding no answering data from OKAPI for the given FNN", "errorMessage": ""}, {"name": "BSIPUCFNR", "title": "BSIPUCFNR", "description": "BSIP user call forwarding not reachable data from OKAPI", "type": "API", "api": "BSIPUCFNR", "param": [{"name": "fnn", "switch": "-f or --fnn or (or first param)", "help": "search term like fnn"}], "paramCode": "fnn= p.fnn || p.f || p._[1]", "example": "BSIPUCFNR --fnn **********", "help": "BSIP user call forwarding not reachable data from OKAPI for the given FNN", "errorMessage": ""}, {"name": "BSIPUCLDB", "title": "BSIPUCLDB", "description": "BSIP user calling line delivery blocking data from OKAPI", "type": "API", "api": "BSIPUCLDB", "param": [{"name": "fnn", "switch": "-f or --fnn or (or first param)", "help": "search term like fnn"}], "paramCode": "fnn= p.fnn || p.f || p._[1]", "example": "BSIPUCLDB --fnn **********", "help": "BSIP user calling line delivery blocking data from OKAPI for the given FNN", "errorMessage": ""}, {"name": "BSIPUCW", "title": "BSIPUCW", "description": "BSIP user call waiting data from OKAPII", "type": "API", "api": "BSIPUCW", "param": [{"name": "fnn", "switch": "-f or --fnn or (or first param)", "help": "search term like fnn"}], "paramCode": "fnn= p.fnn || p.f || p._[1]", "example": "BSIPUCW --fnn **********", "help": "BSIP user call waiting data from OKAPI for the given FNN", "errorMessage": ""}, {"name": "BSIPURO", "title": "BSIPURO", "description": "BSIP user remote office data from OKAPI", "type": "API", "api": "BSIPURO", "param": [{"name": "fnn", "switch": "-f or --fnn or (or first param)", "help": "search term like fnn"}], "paramCode": "fnn= p.fnn || p.f || p._[1]", "example": "BSIPURO --fnn **********", "help": "BSIP user remote office data from OKAPI for the given FNN", "errorMessage": ""}, {"name": "BSIPUSRP", "title": "BSIPUSRP", "description": "BSIP user simultaneous ring personal data from OKAPI", "type": "API", "api": "BSIPUSRP", "param": [{"name": "fnn", "switch": "-f or --fnn or (or first param)", "help": "search term like fnn"}], "paramCode": "fnn= p.fnn || p.f || p._[1]", "example": "BSIPUSRP --fnn **********", "help": "BSIP user simultaneous ring personal data from OKAPI for the given FNN", "errorMessage": ""}, {"name": "BSIPUVM", "title": "BSIPUVM", "description": "BSIP user voice mail data from OKAPI", "type": "API", "api": "BSIPUVM", "param": [{"name": "fnn", "switch": "-f or --fnn or (or first param)", "help": "search term like fnn"}], "paramCode": "fnn= p.fnn || p.f || p._[1]", "example": "BSIPUVM --fnn **********", "help": "BSIP user voice mail data from OKAPI for the given FNN", "errorMessage": ""}, {"name": "CDRLIVE", "title": "CDR Live API", "description": "CDR live data", "type": "API", "api": "CDRLive", "param": [{"name": "TYPE", "switch": "--TYPE (or first param) or --t", "help": "can be `MSISDN` or `IMSI` "}, {"name": "IDENTIFIER", "switch": "--IDENTIFIER or --ID", "help": "IMSI pr MSISDN number"}, {"name": "Period From", "switch": "--PF or --PERIODFROM", "help": "date time in format YYYYMMDDHimmss, specifying `from` which date time you need data"}, {"name": "Period To", "switch": "--PT  or --PERIODTO", "help": "date time in format YYYYMMDDHimmss, specifying `to` which date time you need data"}], "paramCode": "TYPE = p.TYPE || p._[1] || p.t; IDENTIFIER = p.IDENTIFIER || p.ID, PERIODTO = p.PT || p.PERIODTO, PERIODFROM = p.PF || p.PERIODFROM", "example": "CDRLive --TYPE MSISDN --ID 0418311227 --PF 1579241843 --PT 1597645043", "help": "Run CDRLive execute test. See MERGE wiki for more info.", "errorMessage": ""}, {"name": "CDRPURGEALL", "title": "CDRPurgeAll", "description": "To free All API connections by purging all CDRLive reports", "type": "API", "api": "CDRPurgeAll", "param": [{}], "paramCode": "", "example": "CDRPURGEALL", "help": "To free All API connections by purging all CDRLive reports", "errorMessage": ""}, {"name": "CDRPURGEONE", "title": "CDRPurgeOne", "description": "To free a particular API connection by purging it using RunId", "type": "API", "api": "CDRPurgeOne", "param": [{"name": "runID", "switch": "--ID (or first param)", "help": "search runID like 2754608"}], "paramCode": "RunId = p.runID || p._[1];", "example": "CDRPURGEONE --runID 2754608", "help": "To free a particular API connection by purging it using RunId", "errorMessage": ""}, {"name": "CDRQUEUE", "title": "CDRQueue", "description": "To get a list of previously executed CDRLive report(s)", "type": "API", "api": "CDRQueue", "param": [{}], "paramCode": "", "example": "CDRQUEUE", "help": "To get a list of previously executed CDRLive report(s)", "errorMessage": ""}, {"name": "CFNN", "title": "CFNN", "description": "Search CFNN by FNN", "type": "API", "api": "CFNN", "param": [{"name": "fnn", "switch": "-f or --fnn (or first param)", "help": "search term like fnn"}], "paramCode": "IDENTIFIER = p.fnn  || p.f || p._[1]", "example": "cfnn -f N2766194R", "help": "search CFNN by FNN '-f'", "errorMessage": ""}, {"name": "CIDNCUSTCONSENT", "title": "CIDNCustomerConsentExclusion", "description": "Read Hangar db to display a CIDN for conditional consent", "type": "API", "api": "CIDNCustomerConsentExclusion", "param": [{"name": "cidn", "switch": "--cidn (or first param)", "help": "search term like **********"}], "paramCode": "cidn = p.cidn || p._[1];", "example": "CIDNCUSTCONSENT --cidn **********", "help": "To check if any CIDN in a list of CIDNs should be displayed if the customer consent is conditional", "errorMessage": ""}, {"name": "CISCOIOTAD", "title": "CiscoIOTAD", "description": "To get Cisco IOT account details information", "type": "API", "api": "CiscoIOTAD", "param": [{"name": "accountID", "switch": "--accountID (or first param)", "help": "search term like an Cisco IOT account ID"}], "paramCode": "AccountID = p.accountID || p._[1];", "example": "CISCOIOTAD --accountID 11******", "help": "To get Cisco IOT account details information", "errorMessage": ""}, {"name": "CISCOIOTDD", "title": "CiscoIOTDD", "description": "To get Cisco IOT device details information", "type": "API", "api": "CiscoIOTDD", "param": [{"name": "ICCID", "switch": "--ICCID (or first param)", "help": "search term like ICCID"}], "paramCode": "ICCID = p.ICCID || p._[1];", "example": "CISCOIOTDD --ICCID 11******", "help": "To get Cisco IOT device details information", "errorMessage": ""}, {"name": "CISCOIOTDU", "title": "CiscoIOTDU", "description": "To get Cisco IOT device usage", "type": "API", "api": "CiscoIOTDU", "param": [{"name": "ICCID", "switch": "--ICCID (or first param)", "help": "search term like ICCID"}], "paramCode": "ICCID = p.ICCID || p._[1];", "example": "CISCOIOTDU --ICCID 11******", "help": "To get Cisco IOT device usage", "errorMessage": ""}, {"name": "CISCOIOTSD", "title": "CiscoIOTSD", "description": "To get Cisco IOT session details", "type": "API", "api": "CiscoIOTSD", "param": [{"name": "ICCID", "switch": "--ICCID (or first param)", "help": "search term like ICCID"}], "paramCode": "ICCID = p.ICCID || p._[1];", "example": "CISCOIOTSD --ICCID 11******", "help": "To get Cisco IOT session details", "errorMessage": ""}, {"name": "CMART", "title": "CMART", "description": "Fetch CMART data", "type": "API", "api": "CMART", "param": [{"name": "fnn", "switch": "-f or --fnn or (or first param)", "help": "search term like fnn"}], "paramCode": "CMARTfnn= p.fnn || p.f || p._[1]", "example": "CMART --fnn N2651597R", "help": "Fetch CMART data", "errorMessage": ""}, {"name": "CMIBNA", "title": "CMIBNA", "description": "Fetch IOM status", "type": "API", "api": "CMIBNA", "param": [{"name": "fnn", "switch": "-f or --fnn (or first param.)", "help": "search term like fnn"}], "paramCode": "IOMDevice = p.fnn || p.f || p._[1]", "example": "cmibna -f RIVERNNSOR06C08", "help": "search CMIBNA by IOMdevice '-f'", "errorMessage": ""}, {"name": "CMI", "title": "Command Interface (CMI)", "description": "Run command on a device", "type": "API", "api": "CMIn", "param": [{"name": "device", "switch": "--device or -d (or first param)", "help": "Devise hostname or IP to connect"}, {"name": "interfaces", "switch": "--interfaces or -i", "help": "list of interfaces with no space and ; seperated. Will be used in interfaceStatus, intPolicyMap and intRunConfig"}, {"name": "version", "switch": "--version or -v", "help": "get device version and uptime 'show ver'"}, {"name": "ipInterfaceBrief", "switch": "--ipInterfaceBrief or --iib", "help": "get all interfaces configuration 'show ip interface brief'"}, {"name": "interfaceDesc", "switch": "--interfaceDesc or --id", "help": "get list of all interfaces and their descriptions 'show int desc'"}, {"name": "interfaceSum", "switch": "--interfaceSum or --isum ", "help": "get interface summary 'show int sum'"}, {"name": "track", "switch": "--track", "help": "get Tracking information 'show track'"}, {"name": "dialer", "switch": "--dialer", "help": "get Dialer parameters and statistics 'show dialer'"}, {"name": "caller", "switch": "--caller", "help": "get information about dialup connections 'show caller'"}, {"name": "cellularStatus", "switch": "--cellularStatus {int num} or --cs {int num}", "help": "get all cellular information of an interface 0 or 1 or 0;1 'show cellular {int} all'"}, {"name": "cellularHistory", "switch": "--cellularHistory {int num} or --ch {int num} ", "help": "get all Radio history of an interface 0 or 1 or 0;1 'show cellular {int} radio history all'"}, {"name": "interfaceStatus", "switch": "--interfaceStatus or --is ", "help": "get status of inut interfaces from -i 'show interface {int}'"}, {"name": "intPolicyMap", "switch": "--intPolicyMap or --ipm", "help": "get input and output policy of given list of interface by -i 'show policy-map int {int}'"}, {"name": "intRunConfig", "switch": "--intRunConfig or --irc ", "help": "get 'show run interface {int}'"}, {"name": "routes", "switch": "--routes or -r ", "help": "get 'show run | inc route'"}, {"name": "IPRoute", "switch": "--IPRoute {IPs} or --ir {IPs} ", "help": "get IP route, IP must be given as input seperated with ; 'show ip route {IP}'"}, {"name": "alarm", "switch": "--alarm", "help": "get device alarms 'show alarm'"}, {"name": "snmp", "switch": "--snmp", "help": "get 'show snmp'"}, {"name": "log", "switch": "--log or -l", "help": "get device logs 'show log'"}, {"name": "bgp", "switch": "--bgp", "help": "get Summary of BGP neighbor status 'show ip bgp summ'"}, {"name": "fullConfig", "switch": "--fullConfig or --config ", "help": "get full device configuration 'show configuration | display set'"}], "paramCode": "CMIdevice = p.device || p.d || p._[1] ; CMIinterfaces = p.interfaces || p.i ; CMIcommands =  (p.version || p.v ?'getVersion,':'') + (p.ipInterfaceBrief || p.iib ?'getIPInterfaceBrief,':'') + (p.interfaceDesc || p.id?'getInterfaceDescription,':'') + (p.interfaceSum || p.isum ?'getIntSum,':'') + (p.track ?'getTrack,':'') + (p.dialer ?'getDialer,':'') + (p.caller ?'getCaller,':'') + (p.cellularStatus || p.cs ?('getCellularStatus:'+(p.cellularStatus || p.cs)+','):'') + (p.cellularHistory || p.ch ?'getCellularHistory:'+((p.cellularHistory || p.ch)+','):'') + (p.interfaceStatus || p.is ?('getInterfaceStatus:'+(p.interfaces||p.i)+','):'') + (p.intPolicyMap || p.ipm ?('getPolicyMap:'+(p.interfaces||p.i)+','):'') + (p.intRunConfig || p.irc ?'getRunConfig,':'') + (p.routes || p.r ?'getRoutes,':'') + (p.IPRoute || p.ir ?('getShowIPRoute:'+(p.IPRoute||p.ir)+','):'') + (p.alarm ?'getAlarm,':'') + (p.snmp ?'getShowSNMP,':'') + (p.log || p.l ?'getLog,':'') + (p.bgp ?'getBGP,':'') + (p.fullConfig || p.config ?'getFullConfig,':'');", "help": "Connect to device and run pre determined commands on it.", "example": "cmi -d riverqbohr01c08 -i Ce0;Gi7 -v --log -r --snmp --interfaceDesc --intRunConfig --interfaceStatus --intPolicyMap --cs 0 --fullConfig", "errorMessage": ""}, {"name": "CNSDI", "title": "CNSDI", "description": "To check where the number active in CNSDI and compare with Magpie details", "type": "API", "api": "CNSDI", "param": [{"name": "fnn", "switch": "-f or --fnn or (or first param)", "help": "search term like fnn"}, {"name": "TYPE", "switch": "--type", "help": "Service type term like Managed Router"}], "paramCode": "id = p.fnn || p._[1]; type = p.type;", "example": "CNSDI --fnn N3713243R --type 'Managed Router'", "help": "To check where the number active in CNSDI and compare with Magpie details", "errorMessage": ""}, {"name": "COMMPILOT", "title": "COMMPilot", "description": "COMMPilot data", "type": "API", "api": "COMMPilot", "param": [{"name": "IDENTIFIER", "switch": "-ID or --IDENTIFIER (or first param)", "help": "Please give a valid Phone Number"}], "paramCode": "IDENTIFIER = p.IDENTIFIER || p.ID", "example": "COMMPILOT -ID 0738425948", "help": "Get COMMPilot details for Identifier", "errorMessage": ""}, {"name": "COMMPILOTUPDATE", "title": "COMMPilotUpdate", "description": "To update incoming call control settings", "type": "API", "api": "COMMPilotUpdate", "param": [{"name": "IDENTIFIER", "switch": "-ID or --IDENTIFIER (or first param)", "help": "Please give a valid Phone Number"}, {"name": "Arg1", "switch": "--Arg1", "help": "Fixed term U01B"}, {"name": "Arg2", "switch": "--Arg2", "help": "Email ID like IDEN<PERSON><PERSON><PERSON>@team.telstra.com"}, {"name": "Arg3", "switch": "--Arg3", "help": "Fixed Status like 'Available In Office'"}], "paramCode": "IDENTIFIER = p.IDENTIFIER || p.ID || p._[1]; Arg1 = p.Arg1; Arg2 = p.Arg2; Arg3 = p.Arg3;", "example": "COMMPILOTUPDATE -ID 0738****** --Arg1 U01B --Arg2 <EMAIL> --Arg3 'Available In Office'", "help": "To update incoming call control settings", "errorMessage": ""}, {"name": "CONEN", "title": "<PERSON><PERSON>", "description": "Search Blackhawk Conen database, maximum one minute delay to real Conen", "type": "API", "api": "CONEN", "param": [{"name": "ticketID", "switch": "--ticketID or -t (or first param)", "help": "search ticket record based on Conen record ID"}, {"name": "facilityTerm", "switch": "--facility or -f", "help": "search a regular expresion term in field of facilityID on Conen"}, {"name": "locationTerm", "switch": "--location or -l", "help": "search a regular expresion term in field of AFFECTED_LOCATION on Conen"}, {"name": "historyOption", "switch": "--historyOption or -h", "help": "history option , 'ACT' for only active records (event_end=0000-00-00 00:00:00)[default], '24H' for Active or ended in last 24 hours, '30D' for active or ended in last 30 days, 'ALL' for all record maximum 100 records"}], "paramCode": "conenTicketID = (p.ticketID || p.t || p._[1]?p.ticketID || p.t || p._[1]:'') ; conenFacilityTerm= (p.facility || p.f ? p.facility || p.f :''); conenLocationTerm = (p.location || p.l ?p.location || p.l: '') ;conenCommands = (p.ticketID || p.t || p._[1] ? 'getTicket,' : '' ) + ( p.f || p.facility || p.l || p.location ?'searchAll,':'') ; conenHistoryOption = (p.historyOption || p.h ?p.historyOption || p.h:'')", "help": "search CUSTALIGN by CIDN for all customer contacts", "example": "conen 1093272 ; conen -f QG<PERSON>BE4010M ; conen -f QGLEBE4010M -h 24H ; conen -f QGLEBE40 -l Glebe -h 30D  ", "errorMessage": ""}, {"name": "CONENN", "title": "CONENn", "description": "Read CONEN BlackHawk Database", "type": "API", "api": "CONENn", "param": [{"name": "Command", "switch": "--command or --cmd (or first param)", "help": "Please give a valid command like searchAll"}, {"name": "FacilityTerm", "switch": "--facilityTerm", "help": "Search term QCHT-E-070,CPE,QCHT-E-070,NTU"}], "paramCode": "conenCommands = p.cmd || p.command || p._[1]; conenFacilityTerm = p.facilityTerm;", "example": "CONENN --command searchAll --facilityTerm QCHT-E-070,CPE,QCHT-E-070,NTU", "help": "Read CONEN BlackHawk Database for the given command and facility term", "errorMessage": ""}, {"name": "CONGESTIONBOARD", "title": "CongestionBoard", "description": "Check DSLAM in Congestion Board", "type": "API", "api": "CongestionBoard", "param": [{"name": "slam", "switch": "--slam (or first param)", "help": "Please give a valid term like cbdslam"}], "paramCode": "CBDSLAM = p.slam || p._[1];", "example": "CONGESTIONBOARD --slam", "help": "Check DSLAM in Congestion Board", "errorMessage": ""}, {"name": "CUSTALIGN", "title": "Custalign", "description": "Search CIDN in Custalign live database", "type": "API", "api": "CUSTALIGN", "param": [{"name": "cidn", "switch": "-c or --cidn (or first param)", "help": "search cidn"}], "paramCode": "custalignCIDN = p.cidn || p.c || p._[1]; custalignCommands = 'Contacts' ;", "help": "search CUSTALIGN by CIDN for all customer contacts", "example": "custalign --cidn 2101977065", "errorMessage": ""}, {"name": "CUSTCONSENT", "title": "CustomerConsent", "description": "Customer Consent data", "type": "API", "api": "CustomerConsent", "param": [{"name": "ucidn", "switch": "--ucidn (or first param)", "help": "Please give a valid ucidn number"}, {"name": "cidn", "switch": "--cidn", "help": "Please give a valid cidn number"}], "paramCode": "UCIDN = p.ucidn || p._[1];CIDN = p.cidn", "example": "CUSTCONSENT --ucidn  --cidn ", "help": "Check Customer Consent data", "errorMessage": ""}, {"name": "EDCP", "title": "eDCP", "description": "To search the eDCP", "type": "API", "api": "eDCP", "param": [{"name": "input", "switch": "--input (or first param)", "help": "Please give a valid input"}, {"name": "type", "switch": "--type", "help": "Please give a valid type"}, {"name": "range", "switch": "--range", "help": "Please give a valid range"}, {"name": "trace", "switch": "--trace", "help": "Please give a valid trace"}, {"name": "startDate", "switch": "--startDate", "help": "Please give a valid startDate"}, {"name": "endDate", "switch": "--endDate", "help": "Please give a valid endDate"}], "paramCode": "INPUT = p.input || p._[1];\nTYPE = p.type;\nRANGE=p.range;\nTRACE = p.trace;\nSTARTDATE = p.startDate;\nENDDATE = p.endDate", "example": "EDCP --input  --type --range --trace --startDate --endDate ", "help": "To search the eDCP", "errorMessage": ""}, {"name": "FETCHCMART", "title": "Get CMART from the CMART ID", "description": "Fetch CMART information from the given cmart ID", "type": "API", "api": "FetchCMART", "param": [{"name": "ticketID", "switch": "-t or --ticket", "help": "search term like fnn"}], "paramCode": "ticketID = p.ticketID || p.t || p._[1]", "example": "FetchCMART -t 35253", "help": "Fetch CMART information from the CMART ID", "errorMessage": "Failed to fetch CMART"}, {"name": "GEO", "title": "Geolite", "description": "Geolite city plus service", "type": "API", "api": "Geolite", "param": [{"name": "ip", "switch": "--ip (or first param.)", "help": "IPv4 Address"}], "paramCode": "ipAddress= p.ip || p._[1]", "example": "GEO --ip ***.***.*.**", "help": "Geolite city plus service", "errorMessage": ""}, {"name": "GMAC", "title": "GMAC", "description": "Call GMAC portal and do BDSL test on the given BDDSL Carriage", "type": "API", "api": "GMACS", "param": [{"name": "fnn", "switch": "-f or --fnn (or first param)", "help": "Carriage FNN to be tested (Y-N)"}], "paramCode": "GMACSfnn = p.fnn || p.f || p._[1]", "help": "Call GMAC portal and do BDSL test on the given BDDSL Carriage", "example": "gmac Y00000048107N", "errorMessage": ""}, {"name": "INCIDENTS", "title": "Live Incidents", "description": "Fetch live Incidents for itam, cmart and conen", "type": "API", "api": "INCIDENTS", "param": [{"name": "First CGI", "switch": "--FCGI (or first param) ", "help": "first CGI from CDR Live data"}], "paramCode": "FIRSTCGI = p.FCGI || p._[1]", "example": "INCIDENTS --FCGI KACD", "help": "Run Incidents to fetch all the live incidents based on first CGI from CDR Live", "errorMessage": ""}, {"name": "WASCUSTACCT", "title": "WASCustomerAccounts", "description": "Internet Direct OKAPI (WAS API) - Customer accounts endpoint", "type": "API", "api": "InternetDirectCustomerAccounts", "param": [{"name": "accountNumber", "switch": "--acctnbr (or first param)", "help": "search term like account number **********"}], "paramCode": "accountNumber= p.acctnbr || p._[1]", "example": "WASCUSTACCT --acctnbr **********", "help": "Customer accounts endpoint for the given account number", "errorMessage": ""}, {"name": "WASORDERDETAIL", "title": "WASProductOrderDetails", "description": "Internet Direct OKAPI (WAS API) - Product order details endpoint", "type": "API", "api": "InternetDirectProductOrderDetails", "param": [{"name": "IDENTIFIER", "switch": "-ID or --IDENTIFIER (or first param)", "help": "search ID like 4094434"}], "paramCode": "id= p.ID || p.IDENTIFIER || p._[1]", "example": "WASORDERDETAIL -ID 4094434", "help": "Product order details endpoint for the given id", "errorMessage": ""}, {"name": "WASPRODORDER", "title": "WASProductOrders", "description": "Internet Direct OKAPI (WAS API) - Product orders endpoint", "type": "API", "api": "InternetDirectProductOrders", "param": [{"name": "fnn", "switch": "--fnn or --f or --avc (or first param)", "help": "search term like fnn"}], "paramCode": "fnn= p.fnn || p.f || p.avc || p._[1]", "example": "WASPRODORDER --fnn N2854542R", "help": "Product order endpoint for the given id", "errorMessage": ""}, {"name": "IPWORK", "title": "IPWorks", "description": "IPWorks Enum Scheduled List", "type": "API", "api": "IPWorks", "param": [{"name": "ms_isdn", "switch": "--isdn (or first param)", "help": "search term like isdn number 61261694000"}], "paramCode": "isdn = p.isdn || p._[1];", "example": "IPWORK --isdn 61261694000", "help": "IPWorks Enum Scheduled List for the given ms_isdn", "errorMessage": ""}, {"name": "MAGPIE", "title": "<PERSON><PERSON><PERSON>", "description": "Search FNN or any term in Magpie Portal", "type": "API", "api": "MAGPIE", "param": [{"name": "fnn", "switch": "-f or --fnn (or first param)", "help": "search term like fnn"}], "paramCode": "MAGPIEfnn= p.fnn || p.f || p._[1]", "example": "magpie -f N2651597R", "help": "Search a term like FNN in Magpie (MEM Servers) at 'https://lxapp9923.in.telstra.com.au:8443/magpie/app'", "errorMessage": ""}, {"name": "MAGPIEPF", "title": "MagpiePFCAPI", "description": "Magpie API - Details need for RFO", "type": "API", "api": "MagpiePFCAPI", "param": [{"name": "fnn", "switch": "--fnn or --f or --avc (or first param)", "help": "search term like fnn"}], "paramCode": "FNN = p.fnn || p.f || p.avc || p._[1]", "example": "MAGPIEPF --fnn N7805522R", "help": "Magpie API - Details need for RFO", "errorMessage": ""}, {"name": "MANAT", "title": "MANAT", "description": "Get Community Strings and Route IPs from MANAT", "type": "API", "api": "MANAT", "param": [{"name": "device", "switch": "--device or --d (or first param)", "help": "search term like device Id QFEQCPLF01M06"}, {"name": "command", "switch": "--command or --cmd (or first param)", "help": "Please give a valid command like ManageUnmanageInt,CommunityString"}], "paramCode": "MANATdevice = p.device || p.d || p._[1];\n MANATcommands = p.command || p.cmd;", "example": "MANAT --device QFEQCPLF01M06 --command ManageUnmanageInt,CommunityString", "help": "Get Community Strings and Route IPs from MANAT", "errorMessage": ""}, {"name": "MERAKI", "title": "MerakiWifi", "description": "Meraki API for the given device", "type": "API", "api": "<PERSON><PERSON><PERSON>", "param": [{"name": "device", "switch": "--device or --d (or first param)", "help": "search term like device Id QFEQCPLF01M06"}, {"name": "command", "switch": "--command or --cmd (or first param)", "help": "Please give a valid command like deviceStatus"}], "paramCode": "device = p.device || p.d || p._[1];\n commands = p.command || p.cmd;", "example": "<PERSON><PERSON><PERSON> --device QFEQCPLF01M06 --command deviceStatus", "help": "Meraki API details for the given device", "errorMessage": ""}, {"name": "MERGESCHIST", "title": "MergeServiceCheckHistory", "description": "Merge API Service Check History for a FNN", "type": "API", "api": "MergeServiceCheckHistory", "param": [{"name": "fnn", "switch": "--fnn or --f or --avc (or first param)", "help": "search term like fnn N7805522R"}, {"name": "suite", "switch": "--suite or --s (or first param)", "help": "Please give a valid suite"}], "paramCode": "fnn = p.fnn || p.f || p.avc || p._[1];\n suite = p.suite || p.s;", "example": "MERGESCHIST --fnn N7805522R --suite", "help": "Merge API Service Check History for a FNN", "errorMessage": ""}, {"name": "MODINI", "title": "Modini UI API", "description": "Modini API actual data being scraped from modini website", "type": "API", "api": "MODINI", "param": [{"name": "TYPE", "switch": "--TYPE (or first param) or --t", "help": "can be `MSISDN` or `IMSI` or `AUTO`"}, {"name": "IDENTIFIER", "switch": "--IDENTIFIER or --ID", "help": "IMSI pr MSISDN number"}], "paramCode": "TYPE = p.TYPE || p._[1] || p.t || 'AUTO'; IDENTIFIER = p.IDENTIFIER || p.ID", "example": "MODINI --TYPE MSISDN --ID **********", "help": "Run Modini to extract APNs of service. See MERGE wiki for more info.", "errorMessage": ""}, {"name": "PSTNACCTCHECK", "title": "PSTNAccountStatusCheck", "description": "Gets the account status for a PSTN / ISDN service", "type": "API", "api": "MozartPSTNAccountStatusCheck", "param": [{"name": "fnn", "switch": "--fnn or --f or --avc (or first param)", "help": "search term like fnn **********"}], "paramCode": "pstnFnn = p.fnn || p.f || p.avc || p._[1];", "example": "PSTNACCTCHECK --fnn **********", "help": "Gets the account status for a PSTN / ISDN service", "errorMessage": ""}, {"name": "PSTNDEVICETEST", "title": "PSTNDeviceTest", "description": "Performs a device test on a PSTN / ISDN service", "type": "API", "api": "MozartPSTNDeviceTest", "param": [{"name": "fnn", "switch": "--fnn or --f or --avc (or first param)", "help": "search term like fnn **********"}], "paramCode": "pstnFnn = p.fnn || p.f || p.avc || p._[1];", "example": "PSTNDEVICETEST --fnn **********", "help": "Performs a device test on a PSTN / ISDN service", "errorMessage": ""}, {"name": "PSTNEXCHANGEDEVICE", "title": "PSTNExchangeDeviceCheck", "description": "Performs a exchange device check on a PSTN / ISDN service", "type": "API", "api": "MozartPSTNExchangeDeviceCheck", "param": [{"name": "fnn", "switch": "--fnn or --f or --avc (or first param)", "help": "search term like fnn **********"}], "paramCode": "pstnFnn = p.fnn || p.f || p.avc || p._[1];", "example": "PSTNEXCHANGEDEVICE --fnn **********", "help": "Performs a exchange device check on a PSTN / ISDN service", "errorMessage": ""}, {"name": "PSTNFACILITYPROGCHECK", "title": "PSTNFacilityProgrammingCheck", "description": "Performs a facility programming check on PSTN/ISDN service", "type": "API", "api": "MozartPSTNFacilityProgrammingCheck", "param": [{"name": "fnn", "switch": "--fnn or --f or --avc (or first param)", "help": "search term like fnn **********"}], "paramCode": "pstnFnn = p.fnn || p.f || p.avc || p._[1];", "example": "PSTNFACILITYPROGCHECK --fnn **********", "help": "Performs a facility programming check on a PSTN / ISDN service", "errorMessage": ""}, {"name": "PSTNPLANNEDOUTAGE", "title": "PSTNPlannedUnplannedOutage", "description": "Gets the outage status for a PSTN / ISDN service", "type": "API", "api": "MozartPSTNPlannedUnplannedOutage", "param": [{"name": "fnn", "switch": "--fnn or --f or --avc (or first param)", "help": "search term like fnn **********"}], "paramCode": "pstnFnn = p.fnn || p.f || p.avc || p._[1];", "example": "PSTNPLANNEDOUTAGE --fnn **********", "help": "Gets the outage status for a PSTN / ISDN service", "errorMessage": ""}, {"name": "PSTND", "title": "PSTNDashboard", "description": "Run a test on PSTN Dashboard", "type": "API", "api": "MozartPSTNSultanLineTest", "param": [{"name": "fnn", "switch": "-f or --fnn (or first param)", "help": "Line test an FNN"}], "paramCode": "pstnFnn = (p.fnn || p.f || p._[1]) ; ", "example": "PSTND -f 0398798877", "help": "Call PSTN Dashboard for test fnn", "errorMessage": "`PSTN Dashboard issue!`"}, {"name": "NAAS", "title": "NAASMobileAccess", "description": "NAAS data from NaaS+ MobileAccess with ServiceDepth0", "type": "API", "api": "NAASMobileAccess", "param": [{"name": "IDENTIFIER", "switch": "-ID or --IDENTIFIER(or first param)", "help": "search term like id"}], "paramCode": "id = p.ID || p.IDENTIFIER || p._[1];", "example": "NAAS -ID ", "help": "NAAS data from NaaS+ MobileAccess with ServiceDepth0", "errorMessage": ""}, {"name": "NAASSD1", "title": "NAASMobileAccessSD1", "description": "NAAS data from NaaS+ MobileAccess with ServiceDepth1", "type": "API", "api": "NAASMobileAccessSD1", "param": [{"name": "IDENTIFIER", "switch": "-ID or --IDENTIFIER(or first param)", "help": "search term like id"}], "paramCode": "id = p.ID || p.IDENTIFIER || p._[1];", "example": "NAASSD1 -ID ", "help": "NAAS data from NaaS+ MobileAccess with ServiceDepth1", "errorMessage": ""}, {"name": "NBNDIAGNOSTICS", "title": "NBNDiagnostics", "description": "NBN service health API for a service", "type": "API", "api": "NBNDiagnostics", "param": [{"name": "IDENTIFIER", "switch": "-ID or --IDENTIFIER(or first param)", "help": "search term like ovc id OVC000000157052"}, {"name": "type", "switch": "--type", "help": "search term like OVC"}, {"name": "testSpecification", "switch": "--testSpec", "help": "Please give valid test Specification"}, {"name": "testParameters", "switch": "--<PERSON><PERSON><PERSON><PERSON>", "help": "Please give valid test parameter {}"}], "paramCode": "id = p.ID || p.IDENTIFIER || p._[1];\n type = p.type;\n try { testSpecification = JSON.parse(p.testSpec); } catch (e) { testSpecification = null; } \ntry { testParameters = JSON.parse(p.testParam); } catch (e) { testParameters = null; }", "example": "NBNDIAGNOSTICS --IDENTIFIER OVC000000157052 --type OVC --testSpec '{\"id\":\"TST000000001011\", \"version\":\"1.0\"}' --testParam '{}'", "help": "Ensure the testSpecification json is passed without any space and all keys and value enclosed in double quotes without backslash", "errorMessage": ""}, {"name": "NBNDIAGOKAPI", "title": "NBN Diagnostics API", "description": "Run tests via the Diagnostics API", "type": "API", "api": "NBNDIAGOKAPI", "param": [{"name": "avc", "switch": "-f or --fnn --avc (or first param)", "help": "NBN AVC carriage ID"}, {"name": "Service ID", "switch": "--ID or --serviceID", "help": "Service ID of the NBN service. E.g. NR, 03x"}, {"name": "Service Type", "switch": "--serviceType or --st", "help": "internet|voice|mobile"}, {"name": "Access Type", "switch": "--accessType or --at", "help": "fttp|fttb|fttc|fttn|wireless|hfc|..."}, {"name": "Sub Access Type", "switch": "--subAccessType or --sat", "help": "ngc|ippots|ipvoice|virtualvoice"}, {"name": "Diagnostic or Test Name", "switch": "--dn or --tn", "help": "ntdstatuscheck|linestate|loopbacktest|unidstatuscheck|univstatuscheck|..."}], "paramCode": "DIAGNAME = p.dn || p.tn;NBNAVC = p.f || p.fnn || p.avc || p._[1] ; ServiceID = p.ID || p.serviceID ; ServiceType= p.serviceType || p.st ; AccessType = p.at || p.accessType; SubAccessType = p.subAccessType || p.sat", "example": "NBNDIAGOKAPI --ID A07xxxxxxx --st VOICE --avc AVC0000xxxxxxx --at FTTP --sat IPPOTS --dn ntdstatuscheck", "help": "Run a package test, individual test or command via the Diagnostics API. See MERGE wiki for more info.", "errorMessage": ""}, {"name": "NBNFILTERED", "title": "NBN Filtered results", "description": "NBN Filtered results from MEC for Sevice central team", "type": "API", "api": "NBNFilteredResults", "param": [{"name": "fnn", "switch": "--fnn (or first param)", "help": "search term like AVC id AVC000000157052 or FNN like NXXXXXXR"}], "paramCode": "fnn = p.fnn || p._[1];", "example": "NBNFILTERED --fnn N3889244R", "help": "MEC NBN Filtered API API", "errorMessage": ""}, {"name": "NBNPRODINV", "title": "NBNProductInventory", "description": "NBN Product Inventory API", "type": "API", "api": "NBNProductInventory", "param": [{"name": "ovc", "switch": "--ovc (or first param)", "help": "search term like ovc id OVC000000157052"}], "paramCode": "ovc = p.ovc || p._[1];", "example": "NBNPRODINV --ovc OVC000000157052", "help": "NBN Product Inventory API", "errorMessage": ""}, {"name": "NBNRESET", "title": "NBNReset", "description": "Reset NBN port via API in hangar", "type": "API", "api": "NBNReset", "param": [{"name": "fnn", "switch": "-f or --fnn (or first param)", "help": "search term like fnn"}], "paramCode": "NBNFnn = p.fnn || p.f || p._[1]", "example": "NBNRESET -f N2651597R", "help": "Search a term like FNN", "errorMessage": ""}, {"name": "NBNSEARCH", "title": "NBN product details", "description": "Given FNN fetch NBN product details", "type": "API", "api": "NBNSearch", "param": [{"name": "fnn or avc", "switch": "-f or --fnn (or first param.)", "help": "search term like fnn or AVC"}], "paramCode": "NBNTerm = p.fnn || p.f || p._[1]", "example": "NBNSEARCH -f N2811858R", "help": "run NBN search by AVC or FNN", "errorMessage": "Error with NBNSearch quick command"}, {"name": "OATS", "title": "OATS", "description": "Test or just Smart Gather A FNN in OATS", "type": "API", "api": "OATS", "param": [{"name": "fnn", "switch": "-f or --fnn (or first param.)", "help": "FNN carriage ID (Pnone number start with A)"}, {"name": "<PERSON><PERSON><PERSON>", "switch": "-s or --<PERSON><PERSON><PERSON>", "help": "Do the Smart Gather only"}, {"name": "test", "switch": "-t or --fullTest", "help": "Do the Smart Gather and Full Test (it is default if -s does not mentioned)"}], "paramCode": "OATSfnn = p.fnn || p.f || p._[1] ; OATScommands=(p.s || p.<PERSON> ?'SmartGather,':'')+(p.t || p.fullTest || !(p.s || p.<PERSON>) ?'Test,getNotes,':'') ", "example": "OATS -f A0296844951", "help": "Test (or Smart Gather) a FNN in OATS portal at 'http://oatsp.in.telstra.com.au/adsltesting/BBHD/TESTING.aspx'", "errorMessage": ""}, {"name": "ODIN", "title": "ODIN", "description": "Search device by FNN in ODIN portal", "type": "API", "api": "ODIN", "param": [{"name": "fnn", "switch": "-f or --fnn (or first param)", "help": "search term like fnn"}], "paramCode": "ODINfnn = p.fnn || p.f || p._[1] ", "example": "odin --fnn N2651597R", "help": "Search device by FNN in ODIN portal at 'http://odin2.ims.telstra.com.au/farm/odin'", "errorMessage": ""}, {"name": "ODINDEVICEDET", "title": "ODINDeviceDetails", "description": "Obtains additional device details from ODIN", "type": "API", "api": "ODINDeviceDetails", "param": [], "paramCode": "", "example": "ODINDEVICEDET", "help": "Obtains additional device details from ODIN", "errorMessage": ""}, {"name": "ODINDEVICENAME", "title": "ODINDeviceName", "description": "Obtain ODIN results via devicename", "type": "API", "api": "ODINDeviceName", "param": [{"name": "device", "switch": "--device or --d (or first param)", "help": "search term like device Id"}], "paramCode": "ODINdevice = p.device || p.d || p._[1];", "example": "ODINDEVICENAME --device", "help": "Obtain ODIN results via devicename", "errorMessage": ""}, {"name": "PING", "title": "<PERSON>", "description": "Ping device from IMS net jumpbox", "type": "API", "api": "PING", "param": [{"name": "deviceName", "switch": "-d or --device or --ip (or first param.)", "help": "Devise hostname or IP to connect"}, {"name": "simplePing", "switch": "-s or --simple", "help": "simply check device is live or not (default)"}, {"name": "advancedPing", "switch": "-a or --advance", "help": "advanced Ping to do snmpwalk in addition to ping"}], "paramCode": "pingDevice = p.device || p.d  || p.ip || p._[1] ; pingCommands = (p.a || p.advance ?'advancedPing':'simplePing')", "help": "Ping device from IMS Net Jump box. Advanced Ping do the snapwalk, find all device interfaces and ping them.", "example": "ping -d DPWHLNMTVR01C08  (or just 'ping DPWHLNMTVR01C08')", "errorMessage": ""}, {"name": "PWROUTAGE", "title": "PowerOutage", "description": "Power outage info based on state and suburb from HAPI", "type": "API", "api": "PowerOutage", "param": [{"name": "suburb", "switch": "--suburb (or first param)", "help": "Please give valid suburb name CAPE CLEVELAND"}, {"name": "state", "switch": "--state", "help": "Please give valid state name QLD"}], "paramCode": "suburb = p.suburb || p._[1];\n state = p.state", "example": "PWROUTAGE --suburb 'CAPE CLEVELAND' --state QLD", "help": "Power outage info based on state and suburb from HAPI", "errorMessage": ""}, {"name": "PROMISE", "title": "Promise", "description": "Promise task data from OKAPI", "type": "API", "api": "Promise", "param": [{"name": "fnn", "switch": "--fnn or --f or --avc (or first param)", "help": "search term like fnn N7805522R"}], "paramCode": "fnn = p.fnn || p.f || p.avc || p._[1];", "example": "PROMISE --fnn N7805522R", "help": "Promise task data from OKAPI", "errorMessage": ""}, {"name": "RASS", "title": "RASS Puma", "description": "Getting FNN records out of RASS Puma Database. RASS Puma is a daily snapshot of RASS-P.", "type": "API", "api": "RASSP", "param": [{"name": "fnn", "switch": "-f or --fnn (or frist input)", "help": "search term like fnn"}], "paramCode": "RASSPfnn = p.fnn || p.f || p._[1] ", "example": "RASS -f N2651597R", "help": "Getting FNN records out of RASS Puma Database. RASS Puma is the daily to weekly snapshot of RASS-P.", "errorMessage": ""}, {"name": "RDNBOSS", "title": "RDNBOSS", "description": "Run commands via RDNBOSS", "type": "API", "api": "RDNBOSS", "param": [{"name": "fnn", "switch": "--f or --fnn (or first param.)", "help": "TID FNN"}, {"name": "vfimon show_fnn", "switch": "--vsf or --vfimonShowFnn", "help": "Check RDNBOSS for Switch Configuration via vfimon script."}], "paramCode": "RDNBOSSfnn = p.fnn || p.f || p._[1] ; RDNBOSScommands=(p.vsf || p.vfimonShowFnn ?'vfimon_show_fnn,':'') ", "example": "RDNBOSS --fnn n6683032r --vsf", "help": "Run commands via RDNBOSS.", "errorMessage": ""}, {"name": "RFOALARM", "title": "RFOAlarms", "description": "Alarms and outages information sourced from Tomahawk API", "type": "API", "api": "RFOAlarms", "param": [{"name": "identifier", "switch": "--id (or first param)", "help": "search term like N7805522R"}, {"name": "fromDate", "switch": "--fromDate", "help": "Please give valid from date"}, {"name": "toDate", "switch": "--toDate", "help": "Please give valid to date"}, {"name": "sources", "switch": "--sourcesData", "help": "Please give valid test parameter"}], "paramCode": "linkID = p.id || p._[1];\n dateFrom = p.fromDate;\n dateTo = p.toDate;\n sourcesData = p.sourcesData;", "example": "RFOALARM --id N7805522R --fromDate 2024-07-15T19:53:15Z --toDate 2024-07-22T19:53:15Z --sourcesData {\"InRequest-magpieServiceAttributes\":{\"vpn\":null,\"customer\":null,\"location\":null,\"vlanfnn\":null,\"mediaType\":null,\"ntu\":null,\"speed\":null,\"status\":null,\"auditCount\":0,\"cidn\":\"**********\"}}", "help": "Ensure the sourcesData json is passed without any space and all keys and value enclosed in double quotes without backslash", "errorMessage": ""}, {"name": "RIVERBED", "title": "Riverbed", "description": "Riverbed data from Hangar API", "type": "API", "api": "Riverbed", "param": [{"name": "device", "switch": "--device or --d (or first param)", "help": "device id like dotnslev01rar"}, {"name": "org", "switch": "--org", "help": "Please give valid org name like DOT"}], "paramCode": "device = p.device || p.d || p._[1];\n org = p.org;", "example": "RIVERBED --device dotnslev01rar --org DOT", "help": "Riverbed data from Hangar API", "errorMessage": ""}, {"name": "RODS", "title": "RODS", "description": "Rods data from hangar", "type": "API", "api": "RODS", "param": [{"name": "cellName", "switch": "--cellname or --name (or first param)", "help": "search term like cell name KUWELM1"}], "paramCode": "cellName = p.cellname || p.name || p._[1];", "example": "RODS --cellname KUWELM1", "help": "Rods data from hangar", "errorMessage": ""}, {"name": "SABLENBN", "title": "SableNBNAlert", "description": "NGAPI for NBN ALert Version 2", "type": "API", "api": "SableNBNAlert", "param": [{"name": "IDENTIFIER", "switch": "-ID or --IDENTIFIER (or first param)", "help": "Please give a valid TIPT Identifier"}], "paramCode": "identifier = p.ID || p.IDENTIFIER || p._[1];", "example": "SABLENBN --IDENTIFIER 0730210986", "help": "NGAPI for NBN ALert Version 2", "errorMessage": ""}, {"name": "SABLERAW", "title": "SableRaw", "description": "NGAPI for Raw Cdr data Version 2", "type": "API", "api": "SableRaw", "param": [{"name": "IDENTIFIER", "switch": "-ID or --IDENTIFIER (or first param)", "help": "Please give a valid TIPT Identifier"}], "paramCode": "identifier = p.ID || p.IDENTIFIER || p._[1];", "example": "SABLERAW -ID 0730210986", "help": "NGAPI for Raw Cdr data Version 2", "errorMessage": ""}, {"name": "SABLEVOIP", "title": "SableVOIP", "description": "NGAPI for TIPT and IPVPN data Version 2", "type": "API", "api": "SableVOIP", "param": [{"name": "IDENTIFIER", "switch": "-ID or --IDENTIFIER (or first param)", "help": "Please give a valid TIPT Identifier"}], "paramCode": "identifier = p.ID || p.IDENTIFIER || p._[1];", "example": "SABLEVOIP --IDENTIFIER 0730210986", "help": "NGAPI for TIPT and IPVPN data Version 2", "errorMessage": ""}, {"name": "SABLEVOIPHIST", "title": "SableVOIPHistory", "description": "NGAPI for TIPT and IPVPN data Version 2", "type": "API", "api": "SableVOIPHistory", "param": [{"name": "IDENTIFIER", "switch": "-ID or --IDENTIFIER (or first param)", "help": "Please give a valid TIPT Identifier"}], "paramCode": "identifier = p.ID || p.IDENTIFIER || p._[1];", "example": "SABLEVOIPHIST --IDENTIFIER 0730210986", "help": "NGAPI for TIPT and IPVPN data Version 2", "errorMessage": ""}, {"name": "SCILOGRID", "title": "ScienceLogic - Grid List", "description": "Grid List from PAT Science Logic", "type": "API", "api": "ScienceLogicGridList", "param": [], "paramCode": "", "example": "SCILOGRID", "help": "Get grid list from PAT science logic", "errorMessage": "Error with scilo grid list quick command"}, {"name": "SCILONODEINTERFACE", "title": "ScienceLogic - Interface Details", "description": "For a given device name Interface Details", "type": "API", "api": "ScienceLogicInterface", "param": [{"name": "deviceName", "switch": "--deviceName or -d (or first param)", "help": "DeviceName like dutvcwxs01j22"}], "paramCode": "deviceName = p.deviceName || p.d || p._[1];", "example": "SCILONODEINTERFACE -d dutvcwxs01j22", "help": "Get interface details for the given device name", "errorMessage": "Error with scilo Interface Details quick command"}, {"name": "SCILONODEIP", "title": "ScienceLogic - IP Details", "description": "For a given device name IP Details", "type": "API", "api": "ScienceLogicIpDetails", "param": [{"name": "deviceName", "switch": "--deviceName or -d (or first param)", "help": "DeviceName like dutvcwxs01j22"}], "paramCode": "deviceName = p.deviceName || p.d || p._[1];", "example": "SCILONODEIP -d dutvcwxs01j22", "help": "Get IP details for the given device name", "errorMessage": "Error with scilo IP Details quick command"}, {"name": "SCILONODESTATUS", "title": "ScienceLogic - Node Status", "description": "For a given device name get node status", "type": "API", "api": "ScienceLogicNodeStatus", "param": [{"name": "deviceName", "switch": "--deviceName  or -d (or first param)", "help": "DeviceName like dutvcwxs01j22"}], "paramCode": "deviceName = p.deviceName || p.d || p._[1];", "example": "SCILONODESTATUS -d dutvcwxs01j22", "help": "Get node status for the given device name", "errorMessage": "Error with scilo node status quick command"}, {"name": "SCILOSNMP", "title": "ScienceLogic - SNMP", "description": "For a given device name get snmp details", "type": "API", "api": "ScienceLogicSNMP", "param": [{"name": "deviceName", "switch": "--deviceName  or -d (or first param)", "help": "DeviceName like dutvcwxs01j22"}], "paramCode": "deviceName = p.deviceName || p.d || p._[1]", "example": "SCILOSNMP -d dutvcwxs01j22", "help": "Get snmp details for the given device name", "errorMessage": "Error with scilo snmp details quick command"}, {"name": "SDWAN", "title": "SDWAN", "description": "Fetch SDWAN information from the given fnn", "type": "API", "api": "SDWAN", "param": [{"name": "fnn", "switch": "-f or --fnn (or first param.)", "help": "search term like fnn"}], "paramCode": "fnn = p.fnn || p.f || p._[1]", "example": "sdwan -f N2811858R", "help": "run commands via SDWAN", "errorMessage": ""}, {"name": "SERVCENT", "title": "ServiceCentral", "description": "Get Service Central Incidents", "type": "API", "api": "ServiceCentralIncident", "param": [{"name": "ticketID", "switch": "-t or --ticket", "help": "Service Now incident number, eg. SNI1234567"}, {"name": "fnn", "switch": "-f or --fnn (or first param)", "help": "Search term like fnn or phone number"}, {"name": "skipLastWorklog", "switch": "--skipLastWorklog", "help": "Set flag to not display last worklog in response"}, {"name": "limit", "switch": "-l or --limit", "help": "Number of service now incidents to limit to"}, {"name": "offset", "switch": "-o or --offset", "help": "Number of most recent service now incidents to skip"}, {"name": "startDate", "switch": "--startDate", "help": "Start date of when Service Central incidents were created"}, {"name": "endDate", "switch": "--endDate", "help": "End date of when Service Central incidents were created"}], "paramCode": "fnn = p.fnn || p.f || p._[1];\nnumber = p.t || p.ticket;\nlastWorklog = 'skipLastWorklog' in p ? false : true;\nlimit = p.limit;\noffset = p.offset;\nstartDate = p.startDate;\nendData = p.endDate;", "example": "servcent -t SNI1575366", "help": "Search Service Central Incidents by fnn '-f' or by ticketID '-t'", "errorMessage": ""}, {"name": "SERVICEHEALTH", "title": "ServiceHealth", "description": "NBN service health API for a service", "type": "API", "api": "ServiceHealth", "param": [{"name": "avc", "switch": "--avc (or first param)", "help": "search term like avc id"}, {"name": "ServiceHealthSpec", "switch": "--healthSpec", "help": "search term like id and version number together as an object"}], "paramCode": "AVCID = p.avc || p._[1]; \n try { ServiceHealthSpec = JSON.parse(p.healthSpec); } catch (e) { ServiceHealthSpec = null; }", "example": "SERVICEHEALTH --avc AVC000087475640 --healthSpec '{\"id\":\"HLT000000000001\", \"version\":\"2.0\"}'", "help": "Ensure the healthSpec json is passed without any space and all keys and value enclosed in double quotes without backslash", "errorMessage": ""}, {"name": "SERVICETXALARM", "title": "ServiceTXAlarms", "description": "Alarms and outages information sourced from Tomahawk API", "type": "API", "api": "ServiceTXAlarms", "param": [{"name": "IDENTIFIER", "switch": "-ID or --IDENTIFIER(or first param)", "help": "search term like id N7805522R"}, {"name": "sources", "switch": "--sourceData or -sd", "help": "search term like SourceData"}], "paramCode": "linkID = (p.ID || p.IDENTIFIER || p._[1]); \n try { sourcesData = JSON.parse(p.sourceData || p.sd); } catch (e) { sourcesData = null; }", "example": "SERVICETXALARM --IDENTIFIER N7805522R --sourceData '{\"InRequest-magpieServiceAttributes\": {\"vpn\":null, \"customer\":null, \"location\":null, \"vlanfnn\":null, \"mediaType\":null, \"ntu\":null, \"speed\":null, \"status\":null, \"auditCount\":0, \"cidn\":\"**********\"}}'", "help": "Ensure the sourceData json is passed without any space and all keys and value enclosed in double quotes without backslash", "errorMessage": ""}, {"name": "SHAREDBOSSNTUHEALTH", "title": "SHAREDBOSS NTU Health", "description": "SHAREDBOSS NTU Health test", "type": "API", "api": "SHAREDBOSS", "param": [{"name": "NTU Device Name", "switch": "-d or --deviceName", "help": "NTU device name such as abcd-ntu-1100"}, {"name": "FNN", "switch": "-f or --fnn", "help": "FNN in N-R format"}], "paramCode": "device = p.d || p.deviceName;\nfnn = p.f || p.fnn;\ncommands='NTUHealthCheck';", "example": "SHAREDBOSSNTUHEALTH -d abcd-ntu-1100 -f N1234567R", "help": "Run SHAREDBOSS NTU Health check on NTU device and FNN", "errorMessage": "Error with SHAREDBOSSNTUHEALTH quick command"}, {"name": "SIIAM", "title": "SIIAM", "description": "Search FNN or ticket in SIIAM live database", "type": "API", "api": "SIIAM", "param": [{"name": "fnn", "switch": "-f or --fnn (or first param)", "help": "search term like fnn"}, {"name": "ticketID", "switch": "-t or --ticket", "help": "search term like fnn"}, {"name": "activeFNN", "switch": "-a or --activeCases", "help": "search active cases for one fnn"}, {"name": "fnnHistory", "switch": "-h or --history", "help": "get history of cases for an FNN. By default is set for FNN search to 5"}], "paramCode": "SIIAMfnn = p.fnn || p.f || p._[1] ||  '' ; SIIAMcaseID = p.t || p.ticket ||'' ; SIIAMcommands=(typeof p.h == 'number' || typeof p.history == 'number'?('CaseHistory:' + (p.h || p.history) +',') :(p.fnn || p.f || p.h ||p.history ?'CaseHistory,':''))+(p.a||p.activeCases?'ActiveCasesByFNN,':'')+(p.t || p.ticket?'CaseForID,SubCases,ChildCases,ActivityLogs,NotificationMessage,':'')", "help": "search SIIAM by FNN '-f' or ticket ID '-t'", "example": "siiam -f N2766194R -a -h 4 [Search FNN in siaam and return Active tickets (are open) and last 4 tickets]", "errorMessage": ""}, {"name": "SMDM", "title": "SMDM", "description": "B2C or Wholesale data from SMDM OKAPI", "type": "API", "api": "SMDM", "param": [{"name": "fnn", "switch": "--fnn or --f or --avc (or first param)", "help": "search term like fnn 61486870837"}, {"name": "TYPE", "switch": "--TYPE or --t", "help": "search term like service type "}], "paramCode": "fnn = (p.fnn || p.f || p._[1]); \n serviceType = (p.type || p.t);", "example": "SMDM --fnn 61486870837 --type mobile", "help": "B2C or Wholesale data from SMDM OKAPI", "errorMessage": ""}, {"name": "SOLWAGL", "title": "SolarWind - Instance for AGL", "description": "For a given device name fetch details from AGL instance of SolarWinds", "type": "API", "api": "SolarWindsAgl", "param": [{"name": "deviceName", "switch": "--deviceName or -d (or first param)", "help": "DeviceName like aglelvmacr01c11"}], "paramCode": "deviceName = p.deviceName || p.d || p._[1];", "example": "SOLWAGL -d aglelvmacr01c11", "help": "Get details for the given device name from AGL solarwinds", "errorMessage": "Error with solw agl quick command"}, {"name": "SOLWARROW", "title": "SolarWind - Instance for ARROW", "description": "For a given device name fetch details from ARROW instance of SolarWinds", "type": "API", "api": "SolarWindsArrow", "param": [{"name": "deviceName", "switch": "--deviceName or -d (or first param)", "help": "DeviceName like ao5qdbyr04c11"}], "paramCode": "deviceName = p.deviceName || p.d || p._[1];", "example": "SOLWARROW -d ao5qdbyr04c11", "help": "Get details for the given device name from ARROW solarwinds", "errorMessage": "Error with solw arrow quick command"}, {"name": "SOLWBP", "title": "SolarWind - Instance for BP", "description": "For a given device name fetch details from BP instance of SolarWinds", "type": "API", "api": "SolarWindsBP", "param": [{"name": "deviceName", "switch": "--deviceName or -d (or first param)", "help": "DeviceName like rivernguir03c08"}], "paramCode": "deviceName = p.deviceName || p.d || p._[1];", "example": "SOLWBP -d rivernguir03c08", "help": "Get details for the given device name from BP solarwinds", "errorMessage": "Error with solw bp quick command"}, {"name": "SOLWCBA", "title": "SolarWind - Instance for CBA", "description": "For a given device name fetch details from CBA instance of SolarWinds", "type": "API", "api": "SolarWindsCBA", "param": [{"name": "deviceName", "switch": "--deviceName or -d (or first param)", "help": "DeviceName like cbn09ir-h2-c10gw02"}], "paramCode": "deviceName = p.deviceName || p.d || p._[1];", "example": "SOLWCBA -d cbn09ir-h2-c10gw02", "help": "Get details for the given device name from CBA solarwinds", "errorMessage": "Error with solw cba quick command"}, {"name": "SOLWCCA", "title": "SolarWind - Instance for CCA", "description": "For a given device name fetch details from CCA instance of SolarWinds", "type": "API", "api": "SolarWindsCCA", "param": [{"name": "deviceName", "switch": "--deviceName or -d (or first param)", "help": "DeviceName like ccavmbws04c40"}], "paramCode": "deviceName = p.deviceName || p.d || p._[1];", "example": "SOLWCCA -d ccavmbws04c40", "help": "Get details for the given device name from CCA solarwinds", "errorMessage": "Error with solw cca quick command"}, {"name": "SOLWCHEVRON", "title": "SolarWind - Instance for Chevron", "description": "For a given device name fetch details from Chevron instance of SolarWinds", "type": "API", "api": "SolarWindsChevron", "param": [{"name": "deviceName", "switch": "--deviceName or -d (or first param)", "help": "DeviceName like AU-WA-F08941-CE01"}], "paramCode": "deviceName = p.deviceName || p.d || p._[1];", "example": "SOLWCHEVRON -d AU-WA-F08941-CE01", "help": "Get details for the given device name from Chevron solarwinds", "errorMessage": "Error with solw Chevron quick command"}, {"name": "SOLWDFAT", "title": "SolarWind - Instance for DFAT", "description": "For a given device name fetch details from DFAT instance of SolarWinds", "type": "API", "api": "SolarWindsDFAT", "param": [{"name": "deviceName", "switch": "--deviceName or -d (or first param)", "help": "DeviceName like dffiaucr01j03"}], "paramCode": "deviceName = p.deviceName || p.d || p._[1];", "example": "SOLWDFAT -d dffiaucr01j03", "help": "Get details for the given device name from DFAT solarwinds", "errorMessage": "Error with solw dfat quick command"}, {"name": "SOLWDOWNER", "title": "SolarWind - Instance for Downer", "description": "For a given device name fetch details from Downer instance of SolarWinds", "type": "API", "api": "SolarWindsDowner", "param": [{"name": "deviceName", "switch": "--deviceName or -d (or first param)", "help": "DeviceName like dwgqmils21m25"}], "paramCode": "deviceName = p.deviceName || p.d || p._[1];", "example": "SOLWDOWNER -d dwgqmils21m25", "help": "Get details for the given device name from Downer solarwinds", "errorMessage": "Error with solw downer quick command"}, {"name": "SOLWMEDIBANK", "title": "SolarWind - Instance for Medibank", "description": "For a given device name fetch details from Medibank instance of SolarWinds", "type": "API", "api": "SolarWindsMedibank", "param": [{"name": "deviceName", "switch": "--deviceName or -d (or first param)", "help": "DeviceName like medvmbws04c40"}], "paramCode": "deviceName = p.deviceName || p.d || p._[1];", "example": "SOLWMEDIBANK -d medvmbws04c40", "help": "Get details for the given device name from Medibank solarwinds", "errorMessage": "Error with solw medibank quick command"}, {"name": "SOLWORIGIN", "title": "SolarWind - Instance for Origin", "description": "For a given device name fetch details from Origin instance of SolarWinds", "type": "API", "api": "SolarWindsOrigin", "param": [{"name": "deviceName", "switch": "--deviceName or -d (or first param)", "help": "DeviceName like ognqgmir01c43"}], "paramCode": "deviceName = p.deviceName || p.d || p._[1];", "example": "SOLWORIGIN -d ognqgmir01c43", "help": "Get details for the given device name from Origin solarwinds", "errorMessage": "Error with solw origin quick command"}, {"name": "SOLWORIGINCLAYTON", "title": "SolarWind - Instance for Origin Clayton", "description": "For a given device fetch info from OriginClayton - SolarWinds", "type": "API", "api": "SolarWindsOriginClayton", "param": [{"name": "deviceName", "switch": "--deviceName or -d (or first param)", "help": "DeviceName like ognqgmir01c43"}], "paramCode": "deviceName = p.deviceName || p.d || p._[1];", "example": "SOLWORIGINCLAYTON -d ognqgmir01c43", "help": "Get details for the given device name from Origin Clayton solarwinds", "errorMessage": "Error with solw origin clayton quick command"}, {"name": "SOLWOT", "title": "SolarWind - Instance for OT/Shared", "description": "For a given device name fetch details from OT/Shared instance of SolarWinds", "type": "API", "api": "SolarWindsOT", "param": [{"name": "deviceName", "switch": "--deviceName or -d (or first param)", "help": "DeviceName like bssnpyrr01c44"}], "paramCode": "deviceName = p.deviceName || p.d || p._[1];", "example": "SOLWOT -d bssnpyrr01c44", "help": "Get details for the given device name from OT/Shared solarwinds", "errorMessage": "Error with solw OT/Shared quick command"}, {"name": "SOLWSADFE", "title": "SolarWind - Instance for SADfe", "description": "For a given device name fetch details from SADfe instance of SolarWinds", "type": "API", "api": "SolarWindsSADfe", "param": [{"name": "deviceName", "switch": "--deviceName or -d (or first param)", "help": "DeviceName like spasater02c11"}], "paramCode": "deviceName = p.deviceName || p.d || p._[1];", "example": "SOLWSADFE -d spasater02c11", "help": "Get details for the given device name from SADfe solarwinds", "errorMessage": "Error with solw SADfe quick command"}, {"name": "SOLWTOLL", "title": "SolarWind - Instance for TOLL", "description": "For a given device name fetch details from TOLL instance of SolarWinds", "type": "API", "api": "SolarWindsTOLL", "param": [{"name": "deviceName", "switch": "--deviceName or -d (or first param)", "help": "DeviceName like tl7vmoor01c11"}], "paramCode": "deviceName = p.deviceName || p.d || p._[1];", "example": "SOLWTOLL -d tl7vmoor01c11", "help": "Get details for the given device name from TOLL solarwinds", "errorMessage": "Error with solw toll quick command"}, {"name": "SOLWVTS", "title": "SolarWind - Instance for VTS", "description": "For a given device name fetch details from VTS instance of SolarWinds", "type": "API", "api": "SolarWindsVTS", "param": [{"name": "deviceName", "switch": "--deviceName or -d (or first param)", "help": "DeviceName like vdevgorr02c83"}], "paramCode": "deviceName = p.deviceName || p.d || p._[1];", "example": "SOLWVTS -d vdevgorr02c83", "help": "Get details for the given device name from VTS solarwinds", "errorMessage": "Error with solw vts quick command"}, {"name": "SPLUNKAAASC", "title": "SplunkAAASC", "description": "Search Splunk for index AAASC in syslog", "type": "API", "api": "SplunkAAASC", "param": [{"name": "fnn", "switch": "--fnn or --f or --avc (or first param)", "help": "search term like fnn"}], "paramCode": "fnn = (p.fnn || p.f || p.avc || p._[1]);", "example": "SPLUNKAAASC --fnn N7805522R", "help": "Search Splunk for index AAASC in syslog", "errorMessage": ""}, {"name": "SSP", "title": "SSP", "description": "Gives outages details of most of the products support by Merge using conen data", "type": "API", "api": "SSP", "param": [{"name": "fnn", "switch": "--fnn or --f or --avc (or first param)", "help": "search term like fnn"}], "paramCode": "fnn = (p.fnn || p.f || p.avc || p._[1]);", "example": "SSP --fnn N7805522R", "help": "Gives outages details of most of the products support by Merge", "errorMessage": ""}, {"name": "TID", "title": "TID", "description": "Run commands via EDNBOSS and pit-assurance.", "type": "API", "api": "TID", "param": [{"name": "serviceId", "switch": "--f or --fnn or --sid or --serviceId (or first parameter).", "help": "TID FNN or Opshandle"}, {"name": "CoreDB fn_new", "switch": "--fn or --fnNew", "help": "Search for FNN or Opshandle in CoreDB."}, {"name": "Layer 3 Switch Configuration", "switch": "--si or --showInterface", "help": "Check Interface Configuration by logging into the TID edge device and checking the bundle interface from EDNBOSS and pit-assurance."}, {"name": "Layer 3 Switch Configuration (with a 30 second delay)", "switch": "--dsi or --delayShowInterface", "help": "Check Interface Configuration by logging into the TID edge device and checking the bundle interface from EDNBOSS and pit-assurance."}, {"name": "Layer 3 Switch Configuration", "switch": "--sri or --showRunInterface", "help": "Check Interface Configuration by logging into the TID edge device and checking the bundle interface from EDNBOSS and pit-assurance."}, {"name": "Layer 2 MAC Address", "switch": "--arp or --showArp", "help": "Check MAC Address by logging into the TID edge device and checking the bundle interface from EDNBOSS and pit-assurance."}], "paramCode": "TIDsid = p.fnn || p.f || p.sid || p.serviceId || p._[1] ; TIDcommands=(p.fn || p.fnNew ?'fnNew,':'')+(p.si || p.showInterface ?'showInterface,':'')+(p.dsi || p.delayShowInterface ?'delayShowInterface,':'')+(p.sri || p.showRunInterface ?'showRunInterface,':'')+(p.arp || p.showArp ?'showArp,':'')", "example": "TID N6022371R --fnNew", "help": "Run commands via TID.", "errorMessage": ""}, {"name": "TIPTCONF", "title": "TIPTConfluence", "description": "TIPT Confluence Data", "type": "API", "api": "TIPTConfluence", "param": [{"name": "pageID", "switch": "--pageID (or first param)", "help": "search term like pageID"}], "paramCode": "Pageid= p.pageID || p._[1]", "example": "TIPTCONF --pageID", "help": "TIPT Confluence Data", "errorMessage": ""}, {"name": "TLIVE", "title": "TLIVEIncoming", "description": "TLIVE API V3", "type": "API", "api": "TLIVE", "param": [{"name": "device", "switch": "--device or --d (or first param)", "help": "search term like 29023"}, {"name": "category", "switch": "--category", "help": "search term like interface"}, {"name": "instance", "switch": "--instance", "help": "search term like Bundle-Ether1054.120"}, {"name": "variable", "switch": "--var", "help": "search term like ifInOctets"}, {"name": "startTime", "switch": "--start", "help": "search term like 1721093536"}, {"name": "stopTime", "switch": "--stop", "help": "search term like 1721698336"}, {"name": "step", "switch": "--step", "help": "search term like 300"}, {"name": "startTest", "switch": "--startTest", "help": "search term like 1613000000"}, {"name": "stopTest", "switch": "--stopTest", "help": "search term like 1613650000"}, {"name": "stepTest", "switch": "--stepTest", "help": "search term like 300"}], "paramCode": "deviceId= (p.device || p.d || p._[1]); \n category1 = p.category; \n instance1 = p.instance; \n variable1 = p.var; \n start1 = p.start; \n stop1 = p.stop; \n step1 = p.step; \n start_test = p.startTest; \n stop_test = p.stopTest; \n step_test = p.stepTest", "example": "TLIVE --device 29023 --category interface --instance Bundle-Ether1054.120 --var ifInOctets --start 1721093536 --stop 1721698336 --step 300 --startTest 1613000000 --stopTest 1613650000 --stepTest 300", "help": "TLIVE API V3", "errorMessage": ""}, {"name": "TLIVEPK", "title": "TLIVEPKSplunkED1", "description": "TLIVE API V3 Primary Key", "type": "API", "api": "TLIVEPK", "param": [{"name": "device", "switch": "--device or --d (or first param)", "help": "search term like device name"}], "paramCode": "device_name= p.device || p.d || p._[1]", "example": "TLIVEPK --device lvep-s-752", "help": "TLIVE API V3 Primary Key", "errorMessage": ""}, {"name": "TLIVESPLUNK", "title": "TLIVESplunk", "description": "TLIVE ALL SPLUNK SOURCES API", "type": "API", "api": "TLIVESplunk", "param": [{"name": "HighSpeedBSNTU", "switch": "--HSBSNTU (or first param)", "help": "search term for BSNTU"}, {"name": "HighSpeedFAE1", "switch": "--HSFAE1", "help": "search term for FAE1"}, {"name": "HighSpeedFAE2", "switch": "--HSFAE2", "help": "search term for FAE2"}, {"name": "HighSpeedFAE3", "switch": "--HSFAE3", "help": "search term for FAE3"}, {"name": "HighSpeedFAE4", "switch": "--HSFAE4", "help": "search term for FAE4"}, {"name": "HighSpeedPOPSEP1", "switch": "--HSPOPSEP1", "help": "search term for POPSEP1"}, {"name": "HighSpeedPOPSEP2", "switch": "--HSPOPSEP2", "help": "search term for POPSEP2"}, {"name": "InDiscardBSNTU", "switch": "--IDBSNTU", "help": "search term for BSNTU"}, {"name": "InDiscardFAE1", "switch": "--IDFAE1", "help": "search term for FAE1"}, {"name": "InDiscardFAE2", "switch": "--IDFAE2", "help": "search term for FAE2"}, {"name": "InDiscardFAE3", "switch": "--IDFAE3", "help": "search term for FAE3"}, {"name": "InDiscardFAE4", "switch": "--IDFAE4", "help": "search term for FAE4"}, {"name": "InDiscardPOPSEP1", "switch": "--IDPOPSEP1", "help": "search term for POPSEP1"}, {"name": "InDiscardPOPSEP2", "switch": "--IDPOPSEP2", "help": "search term for POPSEP2"}, {"name": "InErrorBSNTU", "switch": "--IEBSNTU", "help": "search term for BSNTU"}, {"name": "InErrorFAE1", "switch": "--IEFAE1", "help": "search term for FAE1"}, {"name": "InErrorFAE2", "switch": "--IEFAE2", "help": "search term for FAE2"}, {"name": "InErrorFAE3", "switch": "--IEFAE3", "help": "search term for FAE3"}, {"name": "InErrorFAE4", "switch": "--IEFAE4", "help": "search term for FAE4"}, {"name": "InErrorPOPSEP1", "switch": "--IEPOPSEP1", "help": "search term for POPSEP1"}, {"name": "InErrorPOPSEP2", "switch": "--IEPOPSEP2", "help": "search term for POPSEP2"}, {"name": "InOctetBSNTU", "switch": "--IOBSNTU", "help": "search term for BSNTU"}, {"name": "InOctetFAE1", "switch": "--IOFAE1", "help": "search term for FAE1"}, {"name": "InOctetFAE2", "switch": "--IOFAE2", "help": "search term for FAE2"}, {"name": "InOctetFAE3", "switch": "--IOFAE3", "help": "search term for FAE3"}, {"name": "InOctetFAE4", "switch": "--IOFAE4", "help": "search term for FAE4"}, {"name": "InOctetPOPSEP1", "switch": "--IOPOPSEP1", "help": "search term for POPSEP1"}, {"name": "InOctetPOPSEP2", "switch": "--IOPOPSEP2", "help": "search term for POPSEP2"}, {"name": "InPacketBSNTU", "switch": "--IPBSNTU", "help": "search term for BSNTU"}, {"name": "InPacketFAE1", "switch": "--IPFAE1", "help": "search term for FAE1"}, {"name": "InPacketFAE2", "switch": "--IPFAE2", "help": "search term for FAE2"}, {"name": "InPacketFAE3", "switch": "--IPFAE3", "help": "search term for FAE3"}, {"name": "InPacketFAE4", "switch": "--IPFAE4", "help": "search term for FAE4"}, {"name": "InPacketPOPSEP1", "switch": "--IPPOPSEP1", "help": "search term for POPSEP1"}, {"name": "InPacketPOPSEP2", "switch": "--IPPOPSEP2", "help": "search term for POPSEP2"}, {"name": "OutDiscardBSNTU", "switch": "--ODBSNTU", "help": "search term for BSNTU"}, {"name": "OutDiscardFAE1", "switch": "--ODFAE1", "help": "search term for FAE1"}, {"name": "OutDiscardFAE2", "switch": "--ODFAE2", "help": "search term for FAE2"}, {"name": "OutDiscardFAE3", "switch": "--ODFAE3", "help": "search term for FAE3"}, {"name": "OutDiscardFAE4", "switch": "--ODFAE4", "help": "search term for FAE4"}, {"name": "OutDiscardPOPSEP1", "switch": "--ODPOPSEP1", "help": "search term for POPSEP1"}, {"name": "OutDiscardPOPSEP2", "switch": "--ODPOPSEP2", "help": "search term for POPSEP2"}, {"name": "OutErrorBSNTU", "switch": "--OEBSNTU", "help": "search term for BSNTU"}, {"name": "OutErrorFAE1", "switch": "--OEFAE1", "help": "search term for FAE1"}, {"name": "OutErrorFAE2", "switch": "--OEFAE2", "help": "search term for FAE2"}, {"name": "OutErrorFAE3", "switch": "--OEFAE3", "help": "search term for FAE3"}, {"name": "OutErrorFAE4", "switch": "--OEFAE4", "help": "search term for FAE4"}, {"name": "OutErrorPOPSEP1", "switch": "--OEPOPSEP1", "help": "search term for POPSEP1"}, {"name": "OutErrorPOPSEP2", "switch": "--OEPOPSEP2", "help": "search term for POPSEP2"}, {"name": "OutPacketBSNTU", "switch": "--OPBSNTU", "help": "search term for BSNTU"}, {"name": "OutPacketFAE1", "switch": "--OPFAE1", "help": "search term for FAE1"}, {"name": "OutPacketFAE2", "switch": "--OPFAE2", "help": "search term for FAE2"}, {"name": "OutPacketFAE3", "switch": "--OPFAE3", "help": "search term for FAE3"}, {"name": "OutPacketFAE4", "switch": "--OPFAE4", "help": "search term for FAE4"}, {"name": "OutPacketPOPSEP1", "switch": "--OPPOPSEP1", "help": "search term for POPSEP1"}, {"name": "OutPacketPOPSEP2", "switch": "--OPPOPSEP2", "help": "search term for POPSEP2"}, {"name": "OutOctetBSNTU", "switch": "--OOBSNTU", "help": "search term for BSNTU"}, {"name": "OutOctetFAE1", "switch": "--OOFAE1", "help": "search term for FAE1"}, {"name": "OutOctetFAE2", "switch": "--OOFAE2", "help": "search term for FAE2"}, {"name": "OutOctetFAE3", "switch": "--OOFAE3", "help": "search term for FAE3"}, {"name": "OutOctetFAE4", "switch": "--OOFAE4", "help": "search term for FAE4"}, {"name": "OutOctetPOPSEP1", "switch": "--OOPOPSEP1", "help": "search term for POPSEP1"}, {"name": "OutOctetPOPSEP2", "switch": "--OOPOPSEP2", "help": "search term for POPSEP2"}], "paramCode": "TLIVESplunkHighSpeedBSNTU = (p.HSBSNTU || p._[1]); \n TLIVESplunkHighSpeedFAE1 = p.HSFAE1; \n TLIVESplunkHighSpeedFAE2 = p.HSFAE2; \n TLIVESplunkHighSpeedFAE3 = p.HSFAE3; \n TLIVESplunkHighSpeedFAE4 = p.HSFAE4; \n TLIVESplunkHighSpeedPOPSEP1 = p.HSPOPSEP1; \n TLIVESplunkHighSpeedPOPSEP2 = p.HSPOPSEP2; \n TLIVESplunkInDiscardBSNTU = p.IDBSNTU; \n TLIVESplunkInDiscardFAE1 = p.IDFAE1; \n TLIVESplunkInDiscardFAE2 = p.IDFAE2; \n TLIVESplunkInDiscardFAE3 = p.IDFAE3; \n TLIVESplunkInDiscardFAE4 = p.IDFAE4; \n TLIVESplunkInDiscardPOPSEP1 = p.IDPOPSEP1; \n TLIVESplunkInDiscardPOPSEP2 = p.IDPOPSEP2; \n TLIVESplunkInErrorBSNTU = p.IEBSNTU; \n TLIVESplunkInErrorFAE1 = p.IEFAE1; \n TLIVESplunkInErrorFAE2 = p.IEFAE2; \n TLIVESplunkInErrorFAE3 = p.IEFAE3; \n TLIVESplunkInErrorFAE4 = p.IEFAE4; \n TLIVESplunkInErrorPOPSEP1 = p.IEPOPSEP1; \n TLIVESplunkInErrorPOPSEP2 = p.IEPOPSEP2; \n TLIVESplunkInOctetBSNTU = p.IOBSNTU; \n ********************** = p.IOFAE1; \n TLIVESplunkInOctetFAE2 = p.IOFAE2; \n ********************** = p.IOFAE3; \n TLIVESplunkInOctetFAE4 = p.IOFAE4; \n TLIVESplunkInOctetPOPSEP1 = p.IOPOPSEP1; \n TLIVESplunkInOctetPOPSEP2 = p.IOPOPSEP2; \n TLIVESplunkInPacketBSNTU = p.IPBSNTU; \n TLIVESplunkInPacketFAE1 = p.IPFAE1; \n TLIVESplunkInPacketFAE2 = p.IPFAE2; \n TLIVESplunkInPacketFAE3 = p.IPFAE3; \n TLIVESplunkInPacketFAE4 = p.IPFAE4; \n TLIVESplunkInPacketPOPSEP1 = p.IPPOPSEP1; \n TLIVESplunkInPacketPOPSEP2 = p.IPPOPSEP2; \n TLIVESplunkOutDiscardBSNTU = p.ODBSNTU; \n TLIVESplunkOutDiscardFAE1 = p.ODFAE1; \n TLIVESplunkOutDiscardFAE2 = p.ODFAE2; \n TLIVESplunkOutDiscardFAE3 = p.ODFAE3; \n TLIVESplunkOutDiscardFAE4 = p.ODFAE4; \n TLIVESplunkOutDiscardPOPSEP1 = p.ODPOPSEP1; \n TLIVESplunkOutDiscardPOPSEP2 = p.ODPOPSEP2; \n TLIVESplunkOutErrorBSNTU = p.OEBSNTU; \n TLIVESplunkOutErrorFAE1 = p.OEFAE1; \n TLIVESplunkOutErrorFAE2 = p.OEFAE2; \n TLIVESplunkOutErrorFAE3 = p.OEFAE3; \n TLIVESplunkOutErrorFAE4 = p.OEFAE4; \n TLIVESplunkOutErrorPOPSEP1 = p.OEPOPSEP1; \n TLIVESplunkOutErrorPOPSEP2 = p.OEPOPSEP2; \n TLIVESplunkOutPacketBSNTU = p.OPBSNTU; \n TLIVESplunkOutPacketFAE1 = p.OPFAE1; \n TLIVESplunkOutPacketFAE2 = p.OPFAE2; \n TLIVESplunkOutPacketFAE3 = p.OPFAE3; \n TLIVESplunkOutPacketFAE4 = p.OPFAE4; \n TLIVESplunkOutPacketPOPSEP1 = p.OPPOPSEP1; \n TLIVESplunkOutPacketPOPSEP2 = p.OPPOPSEP2; \n TLIVESplunkOutOctetBSNTU = p.OOBSNTU; \n TLIVESplunkOutOctetFAE1 = p.OOFAE1; \n TLIVESplunkOutOctetFAE2 = p.OOFAE2; \n TLIVESplunkOutOctetFAE3 = p.OOFAE3; \n TLIVESplunkOutOctetFAE4 = p.OOFAE4; \n TLIVESplunkOutOctetPOPSEP1 = p.OOPOPSEP1; \n TLIVESplunkOutOctetPOPSEP2 = p.OOPOPSEP2;", "example": "TLIVESPLUNK --HSBSNTU --HSFAE1 --HSFAE2 --HSFAE3 --HSFAE4 --HSPOPSEP1 --HSPOPSEP2 --IDBSNTU --IDFAE1 --IDFAE2 --IDFAE3 --IDFAE4 --IDPOPSEP1 --IDPOPSEP2 --IEBSNTU --IEFAE1 --IEFAE2 --IEFAE3 --IEFAE4 --IEPOPSEP1 --IEPOPSEP2 --IOBSNTU --IOFAE1 --IOFAE2 --IOFAE3 --IOFAE4 --IOPOPSEP1 --IOPOPSEP2 --IPBSNTU --IPFAE1 --IPFAE2 --IPFAE3 --IPFAE4 --IPPOPSEP1 --IPPOPSEP2 --ODBSNTU --ODFAE1 --ODFAE2 --ODFAE3 --ODFAE4 --ODPOPSEP1 --ODPOPSEP2 --OEBSNTU --OEFAE1 --OEFAE2 --OEFAE3 --OEFAE4 --OEPOPSEP1 --OEPOPSEP2 --OPBSNTU --OPFAE1 --OPFAE2 --OPFAE3 --OPFAE4 --OPPOPSEP1 --OPPOPSEP2 --OOBSNTU --OOFAE1 --OOFAE2 --OOFAE3 --OOFAE4 --OOPOPSEP1 --OOPOPSEP2", "help": "TLIVE ALL SPLUNK SOURCES API", "errorMessage": ""}, {"name": "TRAD", "title": "Trad", "description": "Trad to network element and run commands. Not all options and commands work all type of devices!", "type": "API", "api": "TRAD", "param": [{"name": "device", "switch": "--device or -d (or first param)", "help": "Devise hostname or IP to trad to"}, {"name": "interface", "switch": "--interfaces or -i (or second parameter)", "help": "list of interfaces to get status, configuration and calss-of-service of each interface (comma seperated)"}, {"name": "showInterface", "switch": "--showInterfaces or --int", "help": "get all interfaces (show interface)"}, {"name": "showInterfacesDesc", "switch": "--showInterfacesDesc or --intd", "help": "get all interfaces Description (show interface desc)"}, {"name": "versionBrief", "switch": "--version or -v", "help": "show device version and uptime (show version brief)"}, {"name": "interfaceStatus", "switch": "--interfaceStatus or -s ", "help": "get interfaces status (show interface) [default for -i]"}, {"name": "interfaceDelayedStatus", "switch": "--interfaceDelayed<PERSON>tatus or --ds ", "help": "waite 30 second and get interfaces status (show interface)"}, {"name": "interfaceConfig", "switch": "--interfaceConfig or --ic", "help": "get interfaces config 'show configuration interfaces'"}, {"name": "interfaceRun", "switch": "--interfaceRun or -r ", "help": "get interfaces run status 'show run interfaces'"}, {"name": "interfaceClassOfService", "switch": "--interfaceClassOfService or --cos ", "help": "get interface class of service 'show class-of-service interface'"}, {"name": "interfacePolicyMap", "switch": "--interfacePolicyMap or --pm ", "help": "get interfaces policy map 'show policy-map int'"}, {"name": "showLog", "switch": "--log or -l", "help": "get device log 'show log'"}, {"name": "showConfig", "switch": "--config or -c", "help": "get device configuration'show config'"}, {"name": "showSNMP", "switch": "--SNMP", "help": "get SNMP status 'show SNMP'"}, {"name": "interfacePolicyAgg", "switch": "--interfacePolicyAgg or --pa ", "help": "get interfaces policy Aggregate of input policy that extracted from 'interfacePolicyMap' 'show policer aggregate NXXXXXXXXR-XXXX'"}, {"name": "rapidPing", "switch": "--ping or -p ", "help": "ping CPE from VPN network (need VPNFNN and pingIP). example: trad RQW2-E-005 -p --vpnFNN N7049604R --IP ************"}, {"name": "vpnFNN", "switch": "--vpnFNN {NXXXXXXXR}", "help": "set VPN FNN for ping CPE (--ping --IP xx.xx.xx.xx --vpnFNN NXXXXXXXR)"}, {"name": "pingIP", "switch": "--IP {XXX.XXX.XXX.XXX} ", "help": "set VPN FNN for ping CPE (--ping --IP xx.xx.xx.xx --vpnFNN NXXXXXXXR)"}], "paramCode": "tradDevice = p.device || p.d || p._[1] ; tradInterfaces= (p.interfaces || p.i ? p.interfaces || p.i :'') ; tradPingIP= p.IP ? p.IP :'' ; tradVPNFNN= p.vpnFNN ?  p.vpnFNN :'' ; tradCommands = (p.showInterfaces||p.int ?'allInterfaces,':'') + (p.showInterfacesDesc||p.intd ?'interfacesDesc,':'') + (p.version || p.v ? 'version,':'') + (p.interfaceStatus || p.s || p.interfaces || p.i ? 'interfaceStatus,':'') + (p.interfacePolicyMap || p.pm  ? 'interfacePolicyMap,':'') + (p.interfacePolicyAgg || p.pa  ? 'interfacePolicyAgg,':'') + (p.interfaceClassOfService || p.cos  ? 'interfaceClassOfService,':'') + (p.interfaceConfig || p.ic  ? 'interfaceConfig,':'') +(p.interfaceRun || p.r?'interfaceRun,':'')  + (p.ping || p.p?'rapidPing,':'')  + (p.interfaceDelayedStatus || p.ds? 'interfaceDelayedStatus,': '') + (p.log || p.l ? 'showLog,': '') + (p.config || p.c? 'showConfig,': '') + (p.SNMP ? 'showSNMP,': '') ; ", "help": "Trad to device and run pre determined commands on it.", "example": "trad -d S4BA-E-702 -v -i GigabitEthernet135/0/0/12,GigabitEthernet0/0/0/1", "errorMessage": ""}, {"name": "WECADBOR", "title": "WECAdbor", "description": "WEC - Adbor Mobile Service Experience Information", "type": "API", "api": "WECAdbor", "param": [{"name": "IDENTIFIER", "switch": "-ID or --IDENTIFIER(or first param)", "help": "search term like ID"}, {"name": "<PERSON><PERSON><PERSON>", "switch": "--<PERSON><PERSON><PERSON>", "help": "search term like adborid"}, {"name": "number", "switch": "--number", "help": "search term like number"}], "paramCode": "id = (p.ID || p.IDENTIFIER || p._[1]); \n adborid = p.adborid; \n number = p.number;", "example": "WECADBOR  --IDENTI<PERSON>ER MAGPIE.rawData.L2NATIVE['Basement Switch ADBoR ID']-669e5ea4c3b04bcfc5201210  --adborid  120543347  --number  N5608092R", "help": "Ensure the spacing used in the command matches with the given example", "errorMessage": ""}, {"name": "WECLATLONG", "title": "WECLongLat", "description": "WEC - Long Lat Mobile Service Experience Information", "type": "API", "api": "WECLongLat", "param": [{"name": "IDENTIFIER", "switch": "-ID or --IDENTIFIER(or first param)", "help": "search term like ID"}, {"name": "latitude", "switch": "--lat", "help": "search term like latitude"}, {"name": "longitude", "switch": "--long", "help": "search term like longitude"}], "paramCode": "id = (p.ID || p.IDENTIFIER || p._[1]); \n latitude = p.lat; \n longitude = p.long;", "example": "WECLATLONG  --IDENTIFIER  s.MAGPIE.rawData.MDN['address parsed']-669f18a912b613c017d8cb18  --latitude  -33.4848025  --longitude  150.1552848", "help": "Ensure the spacing used in the command matches with the given example", "errorMessage": ""}, {"name": "WHOIS", "title": "<PERSON><PERSON>", "description": "", "type": "API", "api": "<PERSON><PERSON>", "param": [{"name": "IP", "switch": "--ip (or first param)", "help": "search term like ipaddress"}], "paramCode": "ipAddress= p.ip || p._[1];", "example": "WHOIS --ip ***.***.*.**", "help": "", "errorMessage": ""}, {"name": "WOLF", "title": "WOLF", "description": "", "type": "API", "api": "WOLF", "param": [{"name": "address", "switch": "--address or --addr (or first param)", "help": "search term like address"}, {"name": "type", "switch": "--type", "help": "search term like type"}], "paramCode": "address= p.address || p.addr || p._[1];\n type = p.type;", "example": "WOLF --addr 15 - 19 FRANK RANDELLCPE CLVLND --type address", "help": "", "errorMessage": ""}, {"name": "XDIAVC", "title": "XDIAVC", "description": "XDI API Call for AVCs", "type": "API", "api": "XDIAVC", "param": [{"name": "search_id", "switch": "--searchID (or first param)", "help": "search term like Merge_AVC000177645303"}, {"name": "owner", "switch": "--owner", "help": "search term like ndsd_nbn_cramerli"}, {"name": "type", "switch": "--type", "help": "search term like AVC"}, {"name": "key", "switch": "--key", "help": "search term like AVC_AVC000177645303"}, {"name": "depth", "switch": "--depth", "help": "search term like 2"}, {"name": "includeActiveEvents", "switch": "--incActiveEvnt", "help": "search term like includeActiveEvents"}], "paramCode": "search_id= p.searchID || p._[1];\n owner = p.owner; \n type = p.type; \n key = p.key; \n depth = p.depth; \n includeActiveEvents = p.incActiveEvnt", "example": "XDIAVC --searchID Merge_AVC000177645303 --owner ndsd_nbn_cramerli --type AVC --key AVC_AVC000177645303 --depth 2 --incActiveEvnt", "help": "XDI API Call for AVCs", "errorMessage": ""}, {"name": "XDIEVNTIMPACT", "title": "XDIEventImpact", "description": "XDI API Call for iTAM Events", "type": "API", "api": "XDIEventImpact", "param": [{"name": "event_id", "switch": "--id (or first param)", "help": "search term like INC000106937078"}], "paramCode": "eventId= p.id || p._[1];", "example": "XDIEVNTIMPACT --id INC000106937078", "help": "XDI API Call for iTAM Events", "errorMessage": ""}, {"name": "XDIN2R", "title": "XDIN2R", "description": "XDI API Call for N2Rs", "type": "API", "api": "XDIN2R", "param": [{"name": "search_id", "switch": "--searchID (or first param)", "help": "search term like MERGE_N1019423R"}, {"name": "IDENTIFIER", "switch": "-ID or --IDENTIFIER", "help": "search term like CustomerService/SERVICE_N1019423R"}, {"name": "depth", "switch": "--depth", "help": "search term like 2"}, {"name": "includeActiveEvents", "switch": "--incActiveEvnt", "help": "search term like includeActiveEvents"}], "paramCode": "search_id= p.searchID || p._[1]; id = p.IDENTIFIER || p.ID; \n depth = p.depth; \n includeActiveEvents = p.incActiveEvnt", "example": "XDIN2R --searchID MERGE_N1019423R -ID CustomerService/SERVICE_N1019423R --depth 2 --incActiveEvnt", "help": "XDI API Call for N2Rs", "errorMessage": ""}]