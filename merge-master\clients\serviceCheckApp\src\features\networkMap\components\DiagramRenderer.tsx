import DOMPurify from 'dompurify'
import { useCallback, useEffect, useRef, useState } from 'react'
import { Diagram } from '../../../infrastructure/models'
import styles from './DiagramRenderer.module.scss'

interface DiagramRendererProps {
    diagram: Diagram
}

export const DiagramRenderer = ({ diagram }: DiagramRendererProps) => {
    const [svgContent, setSvgContent] = useState<string>('')
    const initialZoom = 70
    const minimumZoom = 40
    const maximumZoom = 150
    const [zoomLevel, setZoomLevel] = useState<number>(initialZoom)
    const svgContainerRef = useRef<HTMLDivElement | null>(null)
    const zoomSliderRef = useRef<HTMLInputElement | null>(null)

    // Only parse and sanitize when diagram.xml changes.
    useEffect(() => {
        if (diagram.xml) {
            const parser = new DOMParser()
            const xmlDoc = parser.parseFromString(
                diagram.xml,
                'application/xml'
            )
            const svgElement = xmlDoc.querySelector('svg')

            if (svgElement) {
                svgElement.style.transformOrigin = '0 0'
                const scaleValue = zoomLevel / 100
                svgElement.style.transform = `scale(${scaleValue})`
                const sanitizedSvg = DOMPurify.sanitize(
                    new XMLSerializer().serializeToString(svgElement),
                    { USE_PROFILES: { svg: true } }
                )
                setSvgContent(sanitizedSvg)
            }
        }
    }, [diagram.xml])

    // Separate effect for zoom updates.
    useEffect(() => {
        const svgElement = svgContainerRef.current?.querySelector('svg')
        if (svgElement) {
            const scaleValue = zoomLevel / 100
            svgElement.style.transform = `scale(${scaleValue})`
        }
    }, [zoomLevel])

    // Helper function to update zoom level, slider value, and SVG transform.
    const updateZoom = useCallback(
        (newZoom: number) => {
            // Clamp the zoom level between minimumZoom and maximumZoom.
            const clampedZoom = Math.min(
                maximumZoom,
                Math.max(minimumZoom, newZoom)
            )
            setZoomLevel(clampedZoom)
            if (zoomSliderRef.current) {
                zoomSliderRef.current.value = clampedZoom.toString()
            }
            const svgElement = svgContainerRef.current?.querySelector('svg')
            if (svgElement) {
                const scaleValue = clampedZoom / 100
                svgElement.style.transform = `scale(${scaleValue})`
            }
        },
        [minimumZoom, maximumZoom]
    )

    // Handle zoom slider changes
    const handleZoomChange = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            const newZoom = Number(e.target.value)
            updateZoom(newZoom)
        },
        [updateZoom]
    )

    // Handle wheel zoom events
    const handleWheelZoom = useCallback(
        (event: WheelEvent) => {
            event.preventDefault()
            if (!svgContainerRef.current || !zoomSliderRef.current) return

            const step = 5
            const newZoom = zoomLevel + (event.deltaY < 0 ? step : -step)
            updateZoom(newZoom)
        },
        [zoomLevel, updateZoom]
    )

    // Functions for highlighting/unhighlighting nodes.
    const highlightNode = useCallback(
        (targetId: string, svgElement: SVGSVGElement) => {
            const targetElementNode = svgElement.getElementById(
                targetId
            ) as SVGGElement
            if (targetElementNode) {
                let transformElement =
                    targetElementNode.firstElementChild as SVGGElement
                if (
                    targetElementNode.lastElementChild?.tagName.toLowerCase() ===
                    'a'
                ) {
                    const childElement =
                        targetElementNode.lastElementChild as SVGAElement
                    transformElement =
                        childElement.firstElementChild as SVGGElement
                }
                if (transformElement) {
                    const toBeHiddenImage =
                        transformElement.firstElementChild?.getAttribute(
                            'xlink:href'
                        )
                    if (toBeHiddenImage) {
                        const highlightElement = svgElement.getElementById(
                            `${targetId}_highlight`
                        ) as SVGGElement
                        if (highlightElement) {
                            const highlightElementChild =
                                highlightElement.firstElementChild as SVGGElement
                            if (highlightElementChild) {
                                const toBeShownImage =
                                    highlightElementChild.firstElementChild?.getAttribute(
                                        'xlink:href'
                                    )
                                if (toBeShownImage) {
                                    transformElement.firstElementChild?.setAttribute(
                                        'xlink:href',
                                        toBeShownImage
                                    )
                                    highlightElementChild.firstElementChild?.setAttribute(
                                        'xlink:href',
                                        toBeHiddenImage
                                    )
                                }
                            }
                        }
                    }
                }
            }
        },
        []
    )

    const unhighlightNode = useCallback(
        (targetId: string, svgElement: SVGSVGElement) => {
            const targetElementNode = svgElement.getElementById(
                targetId
            ) as SVGGElement
            if (targetElementNode) {
                let transformElement =
                    targetElementNode.firstElementChild as SVGGElement
                if (
                    targetElementNode.lastElementChild?.tagName.toLowerCase() ===
                    'a'
                ) {
                    const childElement =
                        targetElementNode.lastElementChild as SVGAElement
                    transformElement =
                        childElement.firstElementChild as SVGGElement
                }
                if (transformElement) {
                    const toBeShownImage =
                        transformElement.firstElementChild?.getAttribute(
                            'xlink:href'
                        )
                    if (toBeShownImage) {
                        const highlightElement = svgElement.getElementById(
                            `${targetId}_highlight`
                        ) as SVGGElement
                        if (highlightElement) {
                            const highlightElementChild =
                                highlightElement.firstElementChild as SVGGElement
                            if (highlightElementChild) {
                                const toBeHiddenImage =
                                    highlightElementChild.firstElementChild?.getAttribute(
                                        'xlink:href'
                                    )
                                if (toBeHiddenImage) {
                                    transformElement.firstElementChild?.setAttribute(
                                        'xlink:href',
                                        toBeHiddenImage
                                    )
                                    highlightElementChild.firstElementChild?.setAttribute(
                                        'xlink:href',
                                        toBeShownImage
                                    )
                                }
                            }
                        }
                    }
                }
            }
        },
        []
    )

    // Set up wheel zoom event listener
    useEffect(() => {
        const svgContainer = svgContainerRef.current
        if (svgContainer) {
            svgContainer.addEventListener('wheel', handleWheelZoom, {
                passive: false,
            })
        }
        return () => {
            if (svgContainer) {
                svgContainer.removeEventListener('wheel', handleWheelZoom)
            }
        }
    })

    const getCurrentZoomLevel = useCallback((): number => {
        const svgElement = svgContainerRef.current?.querySelector('svg')
        if (!svgElement) return initialZoom // Default if SVG is not available

        // Get computed transform style
        const computedTransform = window
            .getComputedStyle(svgElement)
            .getPropertyValue('transform')

        // Match the scale value from the matrix transformation
        const match = computedTransform.match(
            /matrix\(([\d.\-]+),\s*[\d.\-]+,\s*[\d.\-]+,\s*([\d.\-]+),/
        )

        if (match) {
            return parseFloat(match[1]) * 100 // Scale value is usually the first or second matrix value
        }

        return initialZoom // Fallback if no transform is found
    }, [])

    const handleMouseOver = useCallback((event: MouseEvent) => {
        const target = event.target as SVGElement
        const nodeId = target?.closest("[id^='y.node.']")?.id
        if (!nodeId) return

        const svgElement = svgContainerRef.current?.querySelector('svg')
        if (!svgElement) return

        // Get zoom level dynamically from the SVG transform scale
        const scale = getCurrentZoomLevel() / 100

        const tooltip = svgElement.querySelector(`[id="tooltip.${nodeId}"]`)
        if (tooltip) {
            const rect = svgElement.getBoundingClientRect()
            const x = (event.clientX - rect.left) / scale
            const y = (event.clientY - rect.top) / scale
            tooltip.setAttribute('visibility', 'visible')
            tooltip.setAttribute('transform', `translate(${x + 10}, ${y + 10})`)
            svgElement.appendChild(tooltip)
        }
        highlightNode(nodeId, svgElement)
    }, [])

    const handleMouseOut = useCallback((event: MouseEvent) => {
        const svgContainer = svgContainerRef.current
        if (!svgContainer) return
        const target = event.target as SVGElement
        const nodeId = target?.closest("[id^='y.node.']")?.id
        if (!nodeId) return

        const svgElement = svgContainer.querySelector('svg')
        if (!svgElement) return

        const tooltip = svgElement.querySelector(`[id="tooltip.${nodeId}"]`)
        tooltip?.setAttribute('visibility', 'hidden')
        unhighlightNode(nodeId, svgElement)
    }, [])

    // Set up mouseover/out event listeners for highlighting
    useEffect(() => {
        const svgContainer = svgContainerRef.current
        if (!svgContainer) return

        svgContainer.addEventListener('mouseover', handleMouseOver)
        svgContainer.addEventListener('mouseout', handleMouseOut)

        return () => {
            svgContainer.removeEventListener('mouseover', handleMouseOver)
            svgContainer.removeEventListener('mouseout', handleMouseOut)
        }
    }, [zoomLevel, svgContainerRef.current])

    return diagram.xml ? (
        <div className={styles.diagramContainer}>
            <div className={styles.zoomControls} ref={zoomSliderRef}>
                <input
                    id="zoomSlider"
                    type="range"
                    min={minimumZoom}
                    max={maximumZoom}
                    value={zoomLevel}
                    onChange={handleZoomChange}
                />
                <span id="zoomValue">{zoomLevel}%</span>
            </div>

            <div
                ref={svgContainerRef}
                dangerouslySetInnerHTML={{ __html: svgContent }}
            />
        </div>
    ) : null
}
