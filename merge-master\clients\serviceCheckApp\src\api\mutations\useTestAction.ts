import { useMutation } from '@tanstack/react-query'
import { runTestAction } from '../api'

export const useTestAction = () => {
    return useMutation({
        mutationFn: ({
            serviceId,
            rule,
            additionalParameters,
        }: {
            serviceId: string
            rule: string
            additionalParameters: Record<string, string>
        }) =>
            runTestAction({
                serviceId: serviceId,
                rule: rule,
                additionalParameters: additionalParameters,
            }),
    })
}
