'use strict';

import chai from 'chai';
import chai<PERSON>ike from 'chai-like';
import chaiAsPromised from 'chai-as-promised';
import chaiDateTime from 'chai-datetime';
import mongoose from 'mongoose';
import sinon from 'sinon';
const should = chai.should();

import helpers from '../helpers.js';
chaiLike.extend(helpers.dateFieldCheck);
chai.use(chaiLike);
chai.use(chaiAsPromised);
chai.use(chaiDateTime);


// Sets gConfig as it is used directly in some imported modules
global.gConfig = {};
import Api from '../../db/model/api.js';
import Rule from '../../db/model/rule.js';
import ServiceCheckModel from '../../db/model/serviceCheck.js';
import mergeRule from '../../modules/mergeRule.js';
import mergeCollect from '../../modules/mergeCollect.js';
import { RuleResult, RuleStatus } from '../../modules/enumerations.js';
import { InactiveError, RuleCodeError } from '../../modules/error.js';


describe('Merge Rule functions', () => {
    beforeEach(async function() {
        // Uses deleteMany instead of mongoose.connection.db.dropDatabase() because
        // custom indexes appear to be deleted if dropDatabase() is used
        for (let collectionName in mongoose.connection.collections) {
            await mongoose.connection.collections[collectionName].deleteMany({});
        }
    });

    afterEach(function() {
        sinon.restore();
    });

    describe('runRule()', () => {
        it('Null rule and service check record', async() => {
            let rule = null;
            let serviceCheckRecord = null;

            await chai.expect(mergeRule.runRule(rule, serviceCheckRecord)).to.be.rejectedWith(TypeError);
        });

        it('Null rule and valid service check record', async() => {
            let rule = null;
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            await chai.expect(mergeRule.runRule(rule, serviceCheckRecord)).to.be.rejectedWith(TypeError);
        });

        it('Valid rule and null service check record', async() => {
            let rule = new Rule({
                name: "MDR001"
            });
            let serviceCheckRecord = null;

            await chai.expect(mergeRule.runRule(rule, serviceCheckRecord)).to.be.rejectedWith(TypeError);
        });

        it('Valid rule and valid service check record', async() => {
            let rule = new Rule({
                name: "MDR001"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.failed);
            chai.expect(ruleCodeReturnValue).to.equal(undefined);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with false precondition', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "false;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.reject);
            chai.expect(ruleCodeReturnValue).to.equal(null);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with true precondition', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.failed);
            chai.expect(ruleCodeReturnValue).to.equal(undefined);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with true precondition from service check record data (variable data)', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "data.carriageType === 'ADSL'"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                carriageType: "ADSL"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.failed);
            chai.expect(ruleCodeReturnValue).to.equal(undefined);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with false precondition from service check record data (variable data)', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "data.carriageType === 'ADSL'"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                carriageType: "NBN"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.reject);
            chai.expect(ruleCodeReturnValue).to.equal(null);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with true precondition from rulesData (variable r)', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "r.MTR001 && r.MTR001.dataKey > 0"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                rulesData: {
                    MTR001: {
                        dataKey: 100
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.failed);
            chai.expect(ruleCodeReturnValue).to.equal(undefined);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with false precondition from rulesData (variable r)', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "r.MTR001 && r.MTR001.dataKey > 0"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                rulesData: {
                    MTR001: {
                        dataKey: -1
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.reject);
            chai.expect(ruleCodeReturnValue).to.equal(null);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with true precondition from sourcesData (variables s)', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "s.SourceName && Array.isArray(s.SourceName.results) && s.SourceName.results.length"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                sourcesData: {
                    SourceName: {
                        results: [{
                            a: 1,
                            b: 2
                        }]
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.failed);
            chai.expect(ruleCodeReturnValue).to.equal(undefined);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with false precondition from sourcesData (variable s)', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "s.SourceName && Array.isArray(s.SourceName.results) && s.SourceName.results.length"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                sourcesData: {
                    SourceName: {
                        results: []
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.reject);
            chai.expect(ruleCodeReturnValue).to.equal(null);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with code error in precondition, throw Error', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "throw new Error('Deliberate error');"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            await chai.expect(mergeRule.runRule(rule, serviceCheckRecord)).to.be.rejectedWith(RuleCodeError, 'Error when determining preCondition for rule MDR001').then((error) => {
                chai.expect(error.cause).to.be.an.instanceOf(Error);
            });
            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.codeError);
        });

        it('Rule with code error in precondition, accessing property of undefined', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "r.MTR001.dataKey;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            await chai.expect(mergeRule.runRule(rule, serviceCheckRecord)).to.be.rejectedWith(RuleCodeError, 'Error when determining preCondition for rule MDR001').then((error) => {
                chai.expect(error.cause).to.be.an.instanceOf(TypeError);
            });
            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.codeError);
        });

        it('Rule that reassigns variable data in precondition code', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "data = 'dummy data';"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.failed);
            chai.expect(ruleCodeReturnValue).to.equal(undefined);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule that reassigns variable r in precondition code', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "r = 'dummy data';"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.failed);
            chai.expect(ruleCodeReturnValue).to.equal(undefined);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule that reassigns variable s in precondition code', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "s = 'dummy data';"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.failed);
            chai.expect(ruleCodeReturnValue).to.equal(undefined);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule that sets variable r in code', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "r = null;true;",
                ruleCode: "r = null;true;",
                valueMsgStm: "r = null;null;",
                extraInfo: "r = null;null;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name]).to.eql({
                status: RuleStatus.done
            });
        });

        it('Rule that sets field in variable r in code', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "r.MDR001 = null;true;",
                ruleCode: "r.MDR001 = null;true;",
                valueMsgStm: "r.MDR001 = null;null;",
                extraInfo: "r.MDR001 = null;null;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name]).to.eql({
                status: RuleStatus.done
            });
        });

        it('Rule that removes field in variable r in code', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "delete r.MDR001;true;",
                ruleCode: "delete r.MDR001;true;",
                valueMsgStm: "delete r.MDR001;null;",
                extraInfo: "delete r.MDR001;null;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name]).to.eql({
                status: RuleStatus.done
            });
        });

        it('Rule that removes rulesData in code', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "delete data.rulesData;true;",
                ruleCode: "delete data.rulesData;true;",
                valueMsgStm: "delete data.rulesData;null;",
                extraInfo: "delete data.rulesData;null;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name]).to.eql({
                status: RuleStatus.done
            });
        });

        it('Rule that sets rulesData in code', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "data.rulesData = null;true;",
                ruleCode: "data.rulesData = null;true;",
                valueMsgStm: "data.rulesData = null;null;",
                extraInfo: "data.rulesData = null;null;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name]).to.eql({
                status: RuleStatus.done
            });
        });

        it('Rule that sets field in rulesData in code', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "data.rulesData.MDR001 = null;true;",
                ruleCode: "data.rulesData.MDR001 = null;true;",
                valueMsgStm: "data.rulesData.MDR001 = null;null;",
                extraInfo: "data.rulesData.MDR001 = null;null;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name]).to.eql({
                status: RuleStatus.done
            });
        });

        it('Rule that removes field in rulesData in code', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "delete data.rulesData.MDR001;true;",
                ruleCode: "delete data.rulesData.MDR001;true;",
                valueMsgStm: "delete data.rulesData.MDR001;null;",
                extraInfo: "delete data.rulesData.MDR001;null;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule that sets variable s in code', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "s = null;true;",
                ruleCode: "s = null;true;",
                valueMsgStm: "s = null;null;",
                extraInfo: "s = null;null;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                sourcesData: {
                    SourceName1: { a: 1 }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name]).to.eql({
                status: RuleStatus.done
            });
            chai.expect(serviceCheckRecord.sourcesData).to.eql({
                SourceName1: { a: 1 }
            });
        });

        it('Rule that sets field in variable s in code', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "s.SourceName1 = null;true;",
                ruleCode: "s.SourceName1 = null;true;",
                valueMsgStm: "s.SourceName1 = null;null;",
                extraInfo: "s.SourceName1 = null;null;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                sourcesData: {
                    SourceName1: { a: 1 }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name]).to.eql({
                status: RuleStatus.done
            });
            chai.expect(serviceCheckRecord.sourcesData).to.eql({
                SourceName1: { a: 1 }
            });
        });

        it('Rule that removes field in variable s in code', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "delete s.SourceName1;true;",
                ruleCode: "delete s.SourceName1;true;",
                valueMsgStm: "delete s.SourceName1;null;",
                extraInfo: "delete s.SourceName1;null;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                sourcesData: {
                    SourceName1: { a: 1 }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name]).to.eql({
                status: RuleStatus.done
            });
            chai.expect(serviceCheckRecord.sourcesData).to.eql({
                SourceName1: { a: 1 }
            });
        });

        it('Rule that removes sourcesData in code', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "delete data.sourcesData;true;",
                ruleCode: "delete data.sourcesData;true;",
                valueMsgStm: "delete data.sourcesData;null;",
                extraInfo: "delete data.sourcesData;null;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                sourcesData: {
                    SourceName1: { a: 1 }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name]).to.eql({
                status: RuleStatus.done
            });
            chai.expect(serviceCheckRecord.sourcesData).to.eql({
                SourceName1: { a: 1 }
            });
        });

        it('Rule that sets sourcesData in code', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "data.sourcesData = null;true;",
                ruleCode: "data.sourcesData = null;true;",
                valueMsgStm: "data.sourcesData = null;null;",
                extraInfo: "data.sourcesData = null;null;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                sourcesData: {
                    SourceName1: { a: 1 }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name]).to.eql({
                status: RuleStatus.done
            });
            chai.expect(serviceCheckRecord.sourcesData).to.eql({
                SourceName1: { a: 1 }
            });
        });

        it('Rule that sets field in sourcesData in code', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "data.sourcesData.SourceName1 = null;true;",
                ruleCode: "data.sourcesData.SourceName1 = null;true;",
                valueMsgStm: "data.sourcesData.SourceName1 = null;null;",
                extraInfo: "data.sourcesData.SourceName1 = null;null;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                sourcesData: {
                    SourceName1: { a: 1 }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name]).to.eql({
                status: RuleStatus.done
            });
            chai.expect(serviceCheckRecord.sourcesData).to.eql({
                SourceName1: { a: 1 }
            });
        });

        it('Rule that removes field in sourcesData in code', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "delete data.sourcesData.SourceName1;true;",
                ruleCode: "delete data.sourcesData.SourceName1;true;",
                valueMsgStm: "delete data.sourcesData.SourceName1;null;",
                extraInfo: "delete data.sourcesData.SourceName1;null;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                sourcesData: {
                    SourceName1: { a: 1 }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name]).to.eql({
                status: RuleStatus.done
            });
            chai.expect(serviceCheckRecord.sourcesData).to.eql({
                SourceName1: { a: 1 }
            });
        });

        it('Rule that removes sourcesMetadata in code', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "delete data.sourcesMetadata;true;",
                ruleCode: "delete data.sourcesMetadata;true;",
                valueMsgStm: "delete data.sourcesMetadata;null;",
                extraInfo: "delete data.sourcesMetadata;null;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                sourcesMetadata: {
                    SourceName1: { status: "Collected" }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name]).to.eql({
                status: RuleStatus.done
            });
            chai.expect(serviceCheckRecord.sourcesMetadata).to.eql({
                SourceName1: { status: "Collected" }
            });
        });

        it('Rule that sets sourcesMetadata in code', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "data.sourcesMetadata = null;true;",
                ruleCode: "data.sourcesMetadata = null;true;",
                valueMsgStm: "data.sourcesMetadata = null;null;",
                extraInfo: "data.sourcesMetadata = null;null;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                sourcesMetadata: {
                    SourceName1: { status: "Collected" }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name]).to.eql({
                status: RuleStatus.done
            });
            chai.expect(serviceCheckRecord.sourcesMetadata).to.eql({
                SourceName1: { status: "Collected" }
            });
        });

        it('Rule that sets field in sourcesMetadata in code', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "data.sourcesMetadata.SourceName1 = null;true;",
                ruleCode: "data.sourcesMetadata.SourceName1 = null;true;",
                valueMsgStm: "data.sourcesMetadata.SourceName1 = null;null;",
                extraInfo: "data.sourcesMetadata.SourceName1 = null;null;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                sourcesMetadata: {
                    SourceName1: { status: "Collected" }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name]).to.eql({
                status: RuleStatus.done
            });
            chai.expect(serviceCheckRecord.sourcesMetadata).to.eql({
                SourceName1: { status: "Collected" }
            });
        });

        it('Rule that removes field in sourcesMetadata in code', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "delete data.sourcesMetadata.SourceName1;true;",
                ruleCode: "delete data.sourcesMetadata.SourceName1;true;",
                valueMsgStm: "delete data.sourcesMetadata.SourceName1;null;",
                extraInfo: "delete data.sourcesMetadata.SourceName1;null;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                sourcesMetadata: {
                    SourceName1: { status: "Collected" }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name]).to.eql({
                status: RuleStatus.done
            });
            chai.expect(serviceCheckRecord.sourcesMetadata).to.eql({
                SourceName1: { status: "Collected" }
            });
        });

        it('Rule with empty string precondition and true rule code', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "",
                ruleCode: "true;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with false precondition and true rule code', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "false;",
                ruleCode: "true;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.reject);
            chai.expect(ruleCodeReturnValue).to.equal(null);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with true precondition and true rule code', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with true precondition and empty string rule code', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: ""
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.failed);
            chai.expect(ruleCodeReturnValue).to.equal(undefined);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with true precondition and false rule code', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "false;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.failed);
            chai.expect(ruleCodeReturnValue).to.equal(false);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with true precondition and false rule code, isWarning true', async() => {
            let rule = new Rule({
                name: "MDR001",
                isWarning: true,
                preCondition: "true;",
                ruleCode: "false;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.warning);
            chai.expect(ruleCodeReturnValue).to.equal(false);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with true rule code from service check record data (variable data)', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "data.carriageType;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                carriageType: "ADSL"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal("ADSL");
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with false rule code from service check record data (variable data)', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "data.carriageType;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.failed);
            chai.expect(ruleCodeReturnValue).to.equal(null);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with true rule code from rulesData (variable r)', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "r.MTR001 && r.MTR001.dataKey;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                rulesData: {
                    MTR001: {
                        dataKey: 100
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(100);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with true rule code, modify rule data and ensure it has changed in the record', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "s.SourceName && s.SourceName.results && Array.isArray(s.SourceName.results);",
                ruleCode: "r.MDR001.numItems = s.SourceName.results.length;\nr.MDR001.numItems;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                rulesData: {},
                sourcesData: {
                    SourceName: {
                        results: [1, 2, 3, 4, 5, 6, 7, 8]
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(8);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name]).to.eql({
                numItems: 8,
                status: RuleStatus.done
            });
        });

        it('Rule with false rule code from rulesData (variable r)', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "r.MTR001 && r.MTR001.dataKey;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                rulesData: {
                    MTR001: {}
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.failed);
            chai.expect(ruleCodeReturnValue).to.equal(undefined);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with true rule code from sourcesData (variable s)', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "s.SourceName && Array.isArray(s.SourceName.results) && s.SourceName.results.length ? s.SourceName.results : null;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                sourcesData: {
                    SourceName: {
                        results: [{
                            a: 1,
                            b: 2
                        }]
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.eql([{ a: 1, b: 2 }]);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with false rule code from sourcesData (variable s)', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "s.SourceName && Array.isArray(s.SourceName.results) && s.SourceName.results.length ? s.SourceName.results : null;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                sourcesData: {
                    SourceName: {
                        results: []
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.failed);
            chai.expect(ruleCodeReturnValue).to.eql(null);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with code error in rule code, throw Error', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "throw new Error('Deliberate error');"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            await chai.expect(mergeRule.runRule(rule, serviceCheckRecord)).to.be.rejectedWith(RuleCodeError, 'Error when running rule / value message / extra info code for rule MDR001').then((error) => {
                chai.expect(error.cause).to.be.an.instanceOf(Error);
            });
            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.codeError);
        });

        it('Rule with code error in rule code, accessing property of undefined', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "r.MTR001.dataKey;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            await chai.expect(mergeRule.runRule(rule, serviceCheckRecord)).to.be.rejectedWith(RuleCodeError, 'Error when running rule / value message / extra info code for rule MDR001').then((error) => {
                chai.expect(error.cause).to.be.an.instanceOf(TypeError);
            });
            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.codeError);
        });

        it('Rule with true rule code, non-empty string', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "'stringvalue';"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal('stringvalue');
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with false rule code, empty string', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "'';"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.failed);
            chai.expect(ruleCodeReturnValue).to.equal('');
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with true rule code, non-zero number', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "1;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(1);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with false rule code, zero number', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "0;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.failed);
            chai.expect(ruleCodeReturnValue).to.equal(0);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with true rule code, empty object', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "let retVal = {};\nretVal;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.eql({});
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with false rule code, null', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "null;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.failed);
            chai.expect(ruleCodeReturnValue).to.equal(null);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with value message, test 1', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;",
                valueMsgStm: "'Value message string';"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal("Value message string");
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with value message, test 2', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "r.MDR001.fieldKey = \"field value\";\nr.MDR001.fieldKey;",
                valueMsgStm: "'Value message string ' + r.MDR001.fieldKey;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal('field value');
            chai.expect(valueMsg).to.equal("Value message string field value");
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with value message, test 3', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;",
                valueMsgStm: "`${data.deviceName ? data.deviceName + ' ' : ''}${r.MTR001 && r.MTR001.a ? 'A: ' + r.MTR001.a + ' ' : ''}${r.MTR001 && r.MTR001.b ? 'B: ' + r.MTR001.b + ' ' : ''}${s.SourceName && s.SourceName.results && Array.isArray(s.SourceName.results) && s.SourceName.results.length ? s.SourceName.results.length : ''}`;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                deviceName: "AL0NMBAR01C01",
                rulesData: {
                    MTR001: {
                        a: 500,
                        b: 200
                    }
                },
                sourcesData: {
                    SourceName: {
                        results: [1, 2, 3, 4, 5, 6]
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal("AL0NMBAR01C01 A: 500 B: 200 6");
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with value message, test 4', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;",
                valueMsgStm: "`${data.deviceName ? data.deviceName + ' ' : ''}${r.MTR001 && r.MTR001.a ? 'A: ' + r.MTR001.a + ' ' : ''}${r.MTR001 && r.MTR001.b ? 'B: ' + r.MTR001.b + ' ' : ''}${s.SourceName && s.SourceName.results && Array.isArray(s.SourceName.results) && s.SourceName.results.length ? s.SourceName.results.length : ''}`;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                rulesData: {
                    MTR001: {
                        a: 500,
                        b: 200
                    }
                },
                sourcesData: {
                    SourceName: {
                        results: [1, 2, 3, 4, 5, 6]
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal("A: 500 B: 200 6");
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with value message, test 5', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;",
                valueMsgStm: "`${data.deviceName ? data.deviceName + ' ' : ''}${r.MTR001 && r.MTR001.a ? 'A: ' + r.MTR001.a + ' ' : ''}${r.MTR001 && r.MTR001.b ? 'B: ' + r.MTR001.b + ' ' : ''}${s.SourceName && s.SourceName.results && Array.isArray(s.SourceName.results) && s.SourceName.results.length ? s.SourceName.results.length : ''}`;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                deviceName: "AL0NMBAR01C01",
                rulesData: {
                    MTR001: {
                        a: 10
                    }
                },
                sourcesData: {
                    SourceName: {
                        results: "false"
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal("AL0NMBAR01C01 A: 10 ");
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with value message, test 6', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;",
                valueMsgStm: "s.SourceName && s.SourceName.results && Array.isArray(s.SourceName.results) && s.SourceName.results.length ? s.SourceName.results.join(',') : ''"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                deviceName: "AL0NMBAR01C01",
                rulesData: {
                    MTR001: {
                        a: 10
                    }
                },
                sourcesData: {
                    SourceName: {
                        results: [1, 2, 3, 4, 5, 6]
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal("1,2,3,4,5,6");
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with value message, test 7', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;",
                valueMsgStm: "`${r.MTR001}`;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                deviceName: "AL0NMBAR01C01",
                rulesData: {
                    MTR001: {
                        a: 10
                    }
                },
                sourcesData: {
                    SourceName: {
                        results: [1, 2, 3, 4, 5, 6]
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal("[object Object]");
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with value message, test 8', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;",
                valueMsgStm: "r.MTR001.a;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                deviceName: "AL0NMBAR01C01",
                rulesData: {
                    MTR001: {
                        a: 10
                    }
                },
                sourcesData: {
                    SourceName: {
                        results: [1, 2, 3, 4, 5, 6]
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal("10");
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with value message, test 9', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;",
                valueMsgStm: "r.MTR001.a === 10;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                deviceName: "AL0NMBAR01C01",
                rulesData: {
                    MTR001: {
                        a: 10
                    }
                },
                sourcesData: {
                    SourceName: {
                        results: [1, 2, 3, 4, 5, 6]
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal("true");
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with value message, test 10', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;",
                valueMsgStm: "r.MTR001.b;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                deviceName: "AL0NMBAR01C01",
                rulesData: {
                    MTR001: {
                        a: 10
                    }
                },
                sourcesData: {
                    SourceName: {
                        results: [1, 2, 3, 4, 5, 6]
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(undefined);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with value message, test 11', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;",
                valueMsgStm: "r.MTR001.b;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                deviceName: "AL0NMBAR01C01",
                rulesData: {
                    MTR001: {
                        a: 10,
                        b: null
                    }
                },
                sourcesData: {
                    SourceName: {
                        results: [1, 2, 3, 4, 5, 6]
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with value message, test 12', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;",
                valueMsgStm: "`${r.MTR001 && r.MTR001.b ? r.MTR001.b : ''}`;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                deviceName: "AL0NMBAR01C01",
                rulesData: {
                    MTR001: {
                        a: 10,
                        b: null
                    }
                },
                sourcesData: {
                    SourceName: {
                        results: [1, 2, 3, 4, 5, 6]
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal("");
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with code error in value message, throw Error', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;",
                valueMsgStm: "throw new Error('Deliberate error');"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            await chai.expect(mergeRule.runRule(rule, serviceCheckRecord)).to.be.rejectedWith(RuleCodeError, 'Error when running rule / value message / extra info code for rule MDR001').then((error) => {
                chai.expect(error.cause).to.be.an.instanceOf(Error);
            });
            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.codeError);
        });

        it('Rule with code error in value message, accessing property of undefined', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;",
                valueMsgStm: "r.MTR001.dataKey;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            await chai.expect(mergeRule.runRule(rule, serviceCheckRecord)).to.be.rejectedWith(RuleCodeError, 'Error when running rule / value message / extra info code for rule MDR001').then((error) => {
                chai.expect(error.cause).to.be.an.instanceOf(TypeError);
            });
            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.codeError);
        });

        it('Rule with extra info, test 1', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;",
                extraInfo: "'Value message string';"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal("Value message string");

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with extra info, test 2', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "r.MDR001.fieldKey = \"field value\";\nr.MDR001.fieldKey;",
                extraInfo: "'Value message string ' + r.MDR001.fieldKey;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal('field value');
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal("Value message string field value");

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with extra info, test 3', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;",
                extraInfo: "`${data.deviceName ? data.deviceName + ' ' : ''}${r.MTR001 && r.MTR001.a ? 'A: ' + r.MTR001.a + ' ' : ''}${r.MTR001 && r.MTR001.b ? 'B: ' + r.MTR001.b + ' ' : ''}${s.SourceName && s.SourceName.results && Array.isArray(s.SourceName.results) && s.SourceName.results.length ? s.SourceName.results.length : ''}`;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                deviceName: "AL0NMBAR01C01",
                rulesData: {
                    MTR001: {
                        a: 500,
                        b: 200
                    }
                },
                sourcesData: {
                    SourceName: {
                        results: [1, 2, 3, 4, 5, 6]
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal("AL0NMBAR01C01 A: 500 B: 200 6");

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with extra info, test 4', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;",
                extraInfo: "`${data.deviceName ? data.deviceName + ' ' : ''}${r.MTR001 && r.MTR001.a ? 'A: ' + r.MTR001.a + ' ' : ''}${r.MTR001 && r.MTR001.b ? 'B: ' + r.MTR001.b + ' ' : ''}${s.SourceName && s.SourceName.results && Array.isArray(s.SourceName.results) && s.SourceName.results.length ? s.SourceName.results.length : ''}`;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                rulesData: {
                    MTR001: {
                        a: 500,
                        b: 200
                    }
                },
                sourcesData: {
                    SourceName: {
                        results: [1, 2, 3, 4, 5, 6]
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal("A: 500 B: 200 6");

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with extra info, test 5', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;",
                extraInfo: "`${data.deviceName ? data.deviceName + ' ' : ''}${r.MTR001 && r.MTR001.a ? 'A: ' + r.MTR001.a + ' ' : ''}${r.MTR001 && r.MTR001.b ? 'B: ' + r.MTR001.b + ' ' : ''}${s.SourceName && s.SourceName.results && Array.isArray(s.SourceName.results) && s.SourceName.results.length ? s.SourceName.results.length : ''}`;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                deviceName: "AL0NMBAR01C01",
                rulesData: {
                    MTR001: {
                        a: 10
                    }
                },
                sourcesData: {
                    SourceName: {
                        results: "false"
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal("AL0NMBAR01C01 A: 10 ");

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with extra info, test 6', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;",
                extraInfo: "s.SourceName && s.SourceName.results && Array.isArray(s.SourceName.results) && s.SourceName.results.length ? s.SourceName.results.join(',') : ''"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                deviceName: "AL0NMBAR01C01",
                rulesData: {
                    MTR001: {
                        a: 10
                    }
                },
                sourcesData: {
                    SourceName: {
                        results: [1, 2, 3, 4, 5, 6]
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal("1,2,3,4,5,6");

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with extra info, test 7', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;",
                extraInfo: "`${r.MTR001}`;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                deviceName: "AL0NMBAR01C01",
                rulesData: {
                    MTR001: {
                        a: 10
                    }
                },
                sourcesData: {
                    SourceName: {
                        results: [1, 2, 3, 4, 5, 6]
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal("[object Object]");

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with extra info, test 8', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;",
                extraInfo: "r.MTR001.a;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                deviceName: "AL0NMBAR01C01",
                rulesData: {
                    MTR001: {
                        a: 10
                    }
                },
                sourcesData: {
                    SourceName: {
                        results: [1, 2, 3, 4, 5, 6]
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal("10");

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with extra info, test 9', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;",
                extraInfo: "r.MTR001.a === 10;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                deviceName: "AL0NMBAR01C01",
                rulesData: {
                    MTR001: {
                        a: 10
                    }
                },
                sourcesData: {
                    SourceName: {
                        results: [1, 2, 3, 4, 5, 6]
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal("true");

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with extra info, test 10', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;",
                extraInfo: "r.MTR001.b;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                deviceName: "AL0NMBAR01C01",
                rulesData: {
                    MTR001: {
                        a: 10
                    }
                },
                sourcesData: {
                    SourceName: {
                        results: [1, 2, 3, 4, 5, 6]
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(undefined);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with extra info, test 11', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;",
                extraInfo: "r.MTR001.b;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                deviceName: "AL0NMBAR01C01",
                rulesData: {
                    MTR001: {
                        a: 10,
                        b: null
                    }
                },
                sourcesData: {
                    SourceName: {
                        results: [1, 2, 3, 4, 5, 6]
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with extra info, test 12', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;",
                extraInfo: "`${r.MTR001 && r.MTR001.b ? r.MTR001.b : ''}`;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                deviceName: "AL0NMBAR01C01",
                rulesData: {
                    MTR001: {
                        a: 10,
                        b: null
                    }
                },
                sourcesData: {
                    SourceName: {
                        results: [1, 2, 3, 4, 5, 6]
                    }
                }
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.ok);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal("");

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with code error in extra info, throw Error', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;",
                extraInfo: "throw new Error('Deliberate error');"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            await chai.expect(mergeRule.runRule(rule, serviceCheckRecord)).to.be.rejectedWith(RuleCodeError, 'Error when running rule / value message / extra info code for rule MDR001').then((error) => {
                chai.expect(error.cause).to.be.an.instanceOf(Error);
            });
            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.codeError);
        });

        it('Rule with code error in extra info, accessing property of undefined', async() => {
            let rule = new Rule({
                name: "MDR001",
                preCondition: "true;",
                ruleCode: "true;",
                extraInfo: "r.MTR001.dataKey;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            await chai.expect(mergeRule.runRule(rule, serviceCheckRecord)).to.be.rejectedWith(RuleCodeError, 'Error when running rule / value message / extra info code for rule MDR001').then((error) => {
                chai.expect(error.cause).to.be.an.instanceOf(TypeError);
            });
            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.codeError);
        });

        it('Rule with action and auto execution false', async() => {
            let rule = new Rule({
                name: "MDR001",
                ruleType: "Action",
                preCondition: "true;",
                ruleCode: "true;",
                action: {
                    api: "APIName",
                    parameterCode: "",
                    codeCondition: "",
                    autoExecution: false
                }
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.actionable);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with action and auto execution false, test rule code false return value, non-empty value message and extra info', async() => {
            let rule = new Rule({
                name: "MDR001",
                ruleType: "Action",
                preCondition: "true;",
                ruleCode: "false;",
                valueMsgStm: "'value message string';",
                extraInfo: "'extra info string';",
                action: {
                    api: "APIName",
                    parameterCode: "",
                    codeCondition: "",
                    autoExecution: false
                }
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            // Rule still becomes actionable even if rule code returns a false type value,
            // If this behaviour changes, rewrite this test
            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.actionable);
            chai.expect(ruleCodeReturnValue).to.equal(false);
            chai.expect(valueMsg).to.equal("value message string");
            chai.expect(extraInfo).to.equal("extra info string");

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with action and auto execution true, no code condition and non-existant API', async() => {
            let rule = new Rule({
                name: "MDR001",
                ruleType: "Action",
                preCondition: "true;",
                ruleCode: "true;",
                action: {
                    api: "APIName",
                    parameterCode: "",
                    codeCondition: "",
                    autoExecution: true
                }
            });

            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            await chai.expect(mergeRule.runRule(rule, serviceCheckRecord)).to.be.rejectedWith(Error, "API APIName does not exist");
            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with action and auto execution true, no code condition and API with active false', async() => {
            let rule = new Rule({
                name: "MDR001",
                ruleType: "Action",
                preCondition: "true;",
                ruleCode: "true;",
                action: {
                    api: "APIName",
                    parameterCode: "",
                    codeCondition: "",
                    autoExecution: true
                }
            });

            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let api = new Api({
                name: "APIName",
                active: false
            });
            await api.save();

            await chai.expect(mergeRule.runRule(rule, serviceCheckRecord)).to.be.rejectedWith(InactiveError, "API 'APIName' is not active");
            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with action and auto execution true, no code condition', async() => {
            sinon.stub(mergeCollect, "collectApi").callsFake(async function(api, parameters) {
                return {
                    collectionDuration: "1.00",
                    data: {
                        successful: true
                    },
                    status: 200
                };
            });

            let rule = new Rule({
                name: "MDR001",
                ruleType: "Action",
                preCondition: "true;",
                ruleCode: "true;",
                action: {
                    api: "APIName",
                    parameterCode: "",
                    codeCondition: "",
                    autoExecution: true
                }
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });
            let api = new Api({
                name: "APIName",
                active: true,
                parameters: [
                    "paramField"
                ]
            });
            await api.save();

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.actioned);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
            chai.expect(serviceCheckRecord.rulesData[rule.name].action).to.be.like({
                createdOn: new Date(),
                response: {
                    successful: true
                },
                status: 200
            });
        });

        it('Rule with action and auto execution true, code condition true', async() => {
            sinon.stub(mergeCollect, "collectApi").callsFake(async function(api, parameters) {
                return {
                    collectionDuration: "1.00",
                    data: {
                        successful: true
                    },
                    status: 200
                };
            });

            let rule = new Rule({
                name: "MDR001",
                ruleType: "Action",
                preCondition: "true;",
                ruleCode: "true;",
                action: {
                    api: "APIName",
                    parameterCode: "",
                    codeCondition: "true;",
                    autoExecution: true
                }
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });
            let api = new Api({
                name: "APIName",
                active: true,
                parameters: [
                    "paramField"
                ]
            });
            await api.save();

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.actioned);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
            chai.expect(serviceCheckRecord.rulesData[rule.name].action).to.be.like({
                createdOn: new Date(),
                response: {
                    successful: true
                },
                status: 200
            });
        });

        it('Rule with action and auto execution true, code condition true from variables', async() => {
            sinon.stub(mergeCollect, "collectApi").callsFake(async function(api, parameters) {
                return {
                    collectionDuration: "1.00",
                    data: {
                        successful: true
                    },
                    status: 200
                };
            });

            let rule = new Rule({
                name: "MDR001",
                ruleType: "Action",
                preCondition: "true;",
                ruleCode: "true;",
                action: {
                    api: "APIName",
                    parameterCode: "",
                    codeCondition: "r.MTR001.field && data.id && s.SourceName.success;",
                    autoExecution: true
                }
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                rulesData: {
                    MTR001: {
                        field: true
                    }
                },
                sourcesData: {
                    SourceName: {
                        success: "positive"
                    }
                }
            });
            let api = new Api({
                name: "APIName",
                active: true,
                parameters: [
                    "paramField"
                ]
            });
            await api.save();

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.actioned);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
            chai.expect(serviceCheckRecord.rulesData[rule.name].action).to.be.like({
                createdOn: new Date(),
                response: {
                    successful: true
                },
                status: 200
            });
        });

        it('Rule with action and auto execution true, code condition false', async() => {
            let rule = new Rule({
                name: "MDR001",
                ruleType: "Action",
                preCondition: "true;",
                ruleCode: "true;",
                action: {
                    api: "APIName",
                    parameterCode: "",
                    codeCondition: "false;",
                    autoExecution: true
                }
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.warning);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
        });

        it('Rule with action and auto execution true, parameters code present', async() => {
            sinon.stub(mergeCollect, "collectApi").callsFake(async function(api, parameters) {
                if (parameters.paramField === "my data field") {
                    return {
                        collectionDuration: "1.00",
                        data: {
                            a: 1,
                            b: 2,
                            c: 3
                        },
                        status: 200
                    };
                } else {
                    throw new Error("Expected parameter field not present");
                }
            });

            let rule = new Rule({
                name: "MDR001",
                ruleType: "Action",
                preCondition: "true;",
                ruleCode: "r.MDR001.data='my data field';\nr.MDR001.data;",
                action: {
                    api: "APIName",
                    parameterCode: "paramField=r.MDR001.data;",
                    codeCondition: "",
                    autoExecution: true
                }
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });
            let api = new Api({
                name: "APIName",
                active: true,
                parameters: [
                    "paramField"
                ]
            });
            await api.save();

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.actioned);
            chai.expect(ruleCodeReturnValue).to.equal('my data field');
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
            chai.expect(serviceCheckRecord.rulesData[rule.name].action).to.be.like({
                createdOn: new Date(),
                response: {
                    a: 1,
                    b: 2,
                    c: 3
                },
                status: 200
            });
        });

        it('Rule with action and auto execution true, non 2XX HTTP response received', async() => {
            sinon.stub(mergeCollect, "collectApi").callsFake(async function(api, parameters) {
                let mockHttpError = new Error("Error: Request failed with status code 404");
                mockHttpError.result = {
                    collectionDuration: "1.00",
                    data: {
                        errorMessage: "Could not find resource"
                    },
                    status: 404
                }
                throw mockHttpError;
            });

            let rule = new Rule({
                name: "MDR001",
                ruleType: "Action",
                preCondition: "true;",
                ruleCode: "r.MDR001.data='my data field';\nr.MDR001.data;",
                action: {
                    api: "APIName",
                    parameterCode: "",
                    codeCondition: "",
                    autoExecution: true
                }
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });
            let api = new Api({
                name: "APIName",
                active: true,
                parameters: [
                    "paramField"
                ]
            });
            await api.save();

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.actionable);
            chai.expect(ruleCodeReturnValue).to.equal('my data field');
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
            chai.expect(serviceCheckRecord.rulesData[rule.name].action).to.be.like({
                createdOn: new Date(),
                response: {
                    errorMessage: "Could not find resource"
                },
                status: 404
            });
        });

        it('Rule with action and auto execution true, code condition code error', async() => {
            let rule = new Rule({
                name: "MDR001",
                ruleType: "Action",
                preCondition: "true;",
                ruleCode: "true;",
                action: {
                    api: "APIName",
                    parameterCode: "",
                    codeCondition: "throw new TypeError('test error');",
                    autoExecution: true
                }
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });
            let api = new Api({
                name: "APIName",
                active: true,
                parameters: [
                    "paramField"
                ]
            });
            await api.save();

            await chai.expect(mergeRule.runRule(rule, serviceCheckRecord)).to.be.rejectedWith(RuleCodeError, 'Error when determining action condition for rule MDR001').then((error) => {
                chai.expect(error.cause).to.be.an.instanceOf(TypeError);
            });
            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.codeError);
        });

        it('Rule with action and auto execution true, parameter code error', async() => {
            let rule = new Rule({
                name: "MDR001",
                ruleType: "Action",
                preCondition: "true;",
                ruleCode: "true;",
                action: {
                    api: "APIName",
                    parameterCode: "throw new TypeError('parameter error');",
                    codeCondition: "",
                    autoExecution: true
                }
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            let api = new Api({
                name: "APIName",
                active: true,
                parameters: [
                    "paramField"
                ]
            });
            await api.save();

            await chai.expect(mergeRule.runRule(rule, serviceCheckRecord)).to.be.rejectedWith(RuleCodeError, 'Error when determining parameters for action rule MDR001').then((error) => {
                chai.expect(error.cause).to.be.an.instanceOf(TypeError);
            });
            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.codeError);
        });

        it('Rule with action and auto execution true, test value message and extra info set afterwards', async() => {
            sinon.stub(mergeCollect, "collectApi").callsFake(async function(api, parameters) {
                return {
                    collectionDuration: "1.00",
                    data: {
                        a: true,
                        b: 354678
                    },
                    status: 200
                };
            });

            let rule = new Rule({
                name: "MDR001",
                ruleType: "Action",
                preCondition: "true;",
                ruleCode: "true;",
                valueMsgStm: "`${r.MDR001 && r.MDR001.action && r.MDR001.action.response && r.MDR001.action.response.a ? r.MDR001.action.response.a : 'none'}`;",
                extraInfo: "`${r.MDR001 && r.MDR001.action && r.MDR001.action.response && r.MDR001.action.response.b ? r.MDR001.action.response.b : 'none'}`;",
                action: {
                    api: "APIName",
                    parameterCode: "",
                    codeCondition: "",
                    autoExecution: true
                }
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });
            let api = new Api({
                name: "APIName",
                active: true,
                parameters: [
                    "paramField"
                ]
            });
            await api.save();

            let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(rule, serviceCheckRecord);

            chai.expect(ruleResult).to.equal(RuleResult.actioned);
            chai.expect(ruleCodeReturnValue).to.equal(true);
            chai.expect(valueMsg).to.equal("true");
            chai.expect(extraInfo).to.equal("354678");

            chai.expect(serviceCheckRecord.rulesData[rule.name].status).to.equal(RuleStatus.done);
            chai.expect(serviceCheckRecord.rulesData[rule.name].action).to.be.like({
                createdOn: new Date(),
                response: {
                    a: true,
                    b: 354678
                },
                status: 200
            });
        });
    });

    describe('runRuleAction()', () => {
        it('Null rule and service check record', async() => {
            let rule = null;
            let serviceCheckRecord = null;

            await chai.expect(mergeRule.runRuleAction(rule, serviceCheckRecord)).to.be.rejectedWith(TypeError);
        });

        it('Null rule and valid service check record', async() => {
            let rule = null;
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            await chai.expect(mergeRule.runRuleAction(rule, serviceCheckRecord)).to.be.rejectedWith(TypeError);
        });

        it('Valid rule and null service check record', async() => {
            let rule = new Rule({
                name: "MDR001"
            });
            let serviceCheckRecord = null;

            await chai.expect(mergeRule.runRuleAction(rule, serviceCheckRecord)).to.be.rejectedWith(TypeError);
        });

        it('Valid rule and valid service check record', async() => {
            let rule = new Rule({
                name: "MDR001"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            await chai.expect(mergeRule.runRuleAction(rule, serviceCheckRecord)).to.be.rejectedWith(Error, 'Action is not set for rule MDR001');
        });

        it('Rule with action and non-existant API', async() => {
            let rule = new Rule({
                name: "MDR001",
                action: {
                    api: "APIName",
                    parameterCode: "",
                    codeCondition: "",
                    autoExecution: false
                }
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });

            await chai.expect(mergeRule.runRuleAction(rule, serviceCheckRecord)).to.be.rejectedWith(Error, 'API APIName does not exist');
        });

        it('Rule with action valid HTTP 2XX response', async() => {
            sinon.stub(mergeCollect, "collectApi").callsFake(async function(api, parameters) {
                return {
                    collectionDuration: "1.00",
                    data: "<html><body><h1>Response Title</h1></body></html>",
                    status: 200
                };
            });

            let rule = new Rule({
                name: "MDR001",
                action: {
                    api: "APIName",
                    parameterCode: "",
                    codeCondition: "",
                    autoExecution: false
                }
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });
            let api = new Api({
                name: "APIName",
                active: true,
                parameters: [
                    "paramField"
                ]
            });
            await api.save();

            let [actionResult, valueMsg, extraInfo] = await mergeRule.runRuleAction(rule, serviceCheckRecord);

            chai.expect(actionResult).to.be.like({
                createdOn: new Date(),
                response: "<html><body><h1>Response Title</h1></body></html>",
                status: 200
            });
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);
        });

        it('Rule with action valid HTTP 2XX response and parameters', async() => {
            sinon.stub(mergeCollect, "collectApi").callsFake(async function(api, parameters) {
                if (parameters.paramField === "my data field") {
                    return {
                        collectionDuration: "1.00",
                        data: {
                            a: 1,
                            b: 2,
                            c: 3
                        },
                        status: 200
                    };
                } else {
                    throw new Error("Expected parameter field not present");
                }
            });

            let rule = new Rule({
                name: "MDR001",
                action: {
                    api: "APIName",
                    parameterCode: "paramField = 'my data field';",
                    codeCondition: "",
                    autoExecution: false
                }
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });
            let api = new Api({
                name: "APIName",
                active: true,
                parameters: [
                    "paramField"
                ]
            });
            await api.save();

            let [actionResult, valueMsg, extraInfo] = await mergeRule.runRuleAction(rule, serviceCheckRecord);

            chai.expect(actionResult).to.be.like({
                createdOn: new Date(),
                response: {
                    a: 1,
                    b: 2,
                    c: 3
                },
                status: 200
            });
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);
        });

        it('Rule with action valid HTTP non 2XX response', async() => {
            sinon.stub(mergeCollect, "collectApi").callsFake(async function(api, parameters) {
                let mockHttpError = new Error("Error: Request failed with status code 404");
                mockHttpError.result = {
                    collectionDuration: "1.00",
                    data: {
                        errorMessage: "Could not find resource"
                    },
                    status: 404
                }
                throw mockHttpError;
            });

            let rule = new Rule({
                name: "MDR001",
                action: {
                    api: "APIName",
                    parameterCode: "",
                    codeCondition: "",
                    autoExecution: false
                }
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });
            let api = new Api({
                name: "APIName",
                active: true,
                parameters: [
                    "paramField"
                ]
            });
            await api.save();

            let [actionResult, valueMsg, extraInfo] = await mergeRule.runRuleAction(rule, serviceCheckRecord);

            chai.expect(actionResult).to.be.like({
                createdOn: new Date(),
                error: "Error: Error: Request failed with status code 404",
                response: {
                    errorMessage: "Could not find resource"
                },
                status: 404
            });
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);
        });

        it('Rule with action and invalid parameter code', async() => {
            let rule = new Rule({
                name: "MDR001",
                action: {
                    api: "APIName",
                    parameterCode: "pa302-0=112001}P{\n;",
                    codeCondition: "",
                    autoExecution: false
                }
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                rulesData: {
                    MDR001: {}
                }
            });
            let api = new Api({
                name: "APIName",
                active: true,
                parameters: [
                    "paramField"
                ]
            });
            await api.save();

            await chai.expect(mergeRule.runRuleAction(rule, serviceCheckRecord)).to.be.rejectedWith(RuleCodeError, 'Error when determining parameters for action rule MDR001').then((error) => {
                chai.expect(error.cause).to.be.an.instanceOf(SyntaxError);
            });
        });

        it('Rule with action and collectApi throws an error with no result', async() => {
            sinon.stub(mergeCollect, "collectApi").callsFake(async function(api, parameters) {
                throw new Error("Error: connect ECONNREFUSED");
            });

            let rule = new Rule({
                name: "MDR001",
                action: {
                    api: "APIName",
                    parameterCode: "",
                    codeCondition: "",
                    autoExecution: false
                }
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111"
            });
            let api = new Api({
                name: "APIName",
                active: true,
                parameters: [
                    "paramField"
                ]
            });
            await api.save();

            await chai.expect(mergeRule.runRuleAction(rule, serviceCheckRecord)).to.be.rejectedWith(Error, 'connect ECONNREFUSED');
        });

        it('Rule with action test value message and extra info set', async() => {
            sinon.stub(mergeCollect, "collectApi").callsFake(async function(api, parameters) {
                return {
                    collectionDuration: "1.00",
                    data: {
                        resultPages: 23,
                        status: "SUCCESSFUL"
                    },
                    status: 200
                };
            });

            let rule = new Rule({
                name: "MDR001",
                action: {
                    api: "APIName",
                    parameterCode: "",
                    codeCondition: "",
                    autoExecution: false
                },
                valueMsgStm: "`${r.MDR001 && r.MDR001.action && r.MDR001.action.response && r.MDR001.action.response.resultPages ? 'Pages: ' + r.MDR001.action.response.resultPages : ''}`;",
                extraInfo: "`${r.MDR001 && r.MDR001.action && r.MDR001.action.response && r.MDR001.action.response.status ? 'Status: ' + r.MDR001.action.response.status : ''}`;"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                rulesData: {
                    MDR001: {
                        status: RuleStatus.done,
                        result: RuleResult.actionable
                    }
                }
            });
            let api = new Api({
                name: "APIName",
                active: true,
                parameters: [
                    "paramField"
                ]
            });
            await api.save();

            let [actionResult, valueMsg, extraInfo] = await mergeRule.runRuleAction(rule, serviceCheckRecord);

            chai.expect(actionResult).to.be.like({
                createdOn: new Date(),
                response: {
                    resultPages: 23,
                    status: "SUCCESSFUL"
                },
                status: 200
            });
            chai.expect(valueMsg).to.equal("Pages: 23");
            chai.expect(extraInfo).to.equal("Status: SUCCESSFUL");
        });

        it('Rule with action test invalid value message', async() => {
            sinon.stub(mergeCollect, "collectApi").callsFake(async function(api, parameters) {
                return {
                    collectionDuration: "1.00",
                    data: {
                        resultPages: 23,
                        status: "SUCCESSFUL"
                    },
                    status: 200
                };
            });

            let rule = new Rule({
                name: "MDR001",
                action: {
                    api: "APIName",
                    parameterCode: "",
                    codeCondition: "",
                    autoExecution: false
                },
                valueMsgStm: "throw new Error('value message error');",
                extraInfo: ""
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                rulesData: {
                    MDR001: {}
                }
            });
            let api = new Api({
                name: "APIName",
                active: true,
                parameters: [
                    "paramField"
                ]
            });
            await api.save();

            await chai.expect(mergeRule.runRuleAction(rule, serviceCheckRecord)).to.be.rejectedWith(RuleCodeError, 'Error when evaluating value message or extra info after action').then((error) => {
                chai.expect(error.cause).to.be.an.instanceOf(Error);
            });
        });

        it('Rule with action test invalid extra info', async() => {
            sinon.stub(mergeCollect, "collectApi").callsFake(async function(api, parameters) {
                return {
                    collectionDuration: "1.00",
                    data: {
                        resultPages: 23,
                        status: "SUCCESSFUL"
                    },
                    status: 200
                };
            });

            let rule = new Rule({
                name: "MDR001",
                action: {
                    api: "APIName",
                    parameterCode: "",
                    codeCondition: "",
                    autoExecution: false
                },
                valueMsgStm: "",
                extraInfo: "throw new Error('extra info error');"
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                rulesData: {
                    MDR001: {}
                }
            });
            let api = new Api({
                name: "APIName",
                active: true,
                parameters: [
                    "paramField"
                ]
            });
            await api.save();

            await chai.expect(mergeRule.runRuleAction(rule, serviceCheckRecord)).to.be.rejectedWith(RuleCodeError, 'Error when evaluating value message or extra info after action').then((error) => {
                chai.expect(error.cause).to.be.an.instanceOf(Error);
            });
        });

        it('Rule with action valid HTTP 2XX response and parameters writing / deleting read-only fields', async() => {
            sinon.stub(mergeCollect, "collectApi").callsFake(async function(api, parameters) {
                if (parameters.paramField === "my data field") {
                    return {
                        collectionDuration: "1.00",
                        data: {
                            a: 1,
                            b: 2,
                            c: 3
                        },
                        status: 200
                    };
                } else {
                    throw new Error("Expected parameter field not present");
                }
            });

            let rule = new Rule({
                name: "MDR001",
                action: {
                    api: "APIName",
                    parameterCode: "data.rulesData.MDR001 = null;delete data.rulesData.MDR001;delete data.rulesData;data.sourcesData.SourceName1 = null;delete data.sourcesData.SourceName1;delete data.sourcesData;data.sourcesMetadata.SourceName1 = null;delete data.sourcesMetadata.SourceName1;delete data.sourcesMetadata;paramField='my data field';",
                    codeCondition: "",
                    autoExecution: false
                }
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                rulesData: {
                    MDR001: {
                        result: "Actionable"
                    }
                },
                sourcesData: {
                    SourceName1: {
                        a: 1
                    }
                },
                sourcesMetadata: {
                    SourceName1: {
                        status: "Collected"
                    }
                }
            });
            let api = new Api({
                name: "APIName",
                active: true,
                parameters: [
                    "paramField"
                ]
            });
            await api.save();

            let [actionResult, valueMsg, extraInfo] = await mergeRule.runRuleAction(rule, serviceCheckRecord);

            chai.expect(actionResult).to.be.like({
                createdOn: new Date(),
                response: {
                    a: 1,
                    b: 2,
                    c: 3
                },
                status: 200
            });
            chai.expect(valueMsg).to.equal(null);
            chai.expect(extraInfo).to.equal(null);

            chai.expect(serviceCheckRecord.rulesData).to.be.like({
                MDR001: {
                    result: "Actionable"
                }
            });
            chai.expect(serviceCheckRecord.sourcesData).to.eql({
                SourceName1: {
                    a: 1
                }
            });
            chai.expect(serviceCheckRecord.sourcesMetadata).to.eql({
                SourceName1: {
                    status: "Collected"
                }
            });
        });

        it('Rule with action valid HTTP 2XX response and valueMsg, extraInfo writing / deleting read-only fields', async() => {
            sinon.stub(mergeCollect, "collectApi").callsFake(async function(api, parameters) {
                if (parameters.paramField === "my data field") {
                    return {
                        collectionDuration: "1.00",
                        data: {
                            a: 1,
                            b: 2,
                            c: 3
                        },
                        status: 200
                    };
                } else {
                    throw new Error("Expected parameter field not present");
                }
            });

            let rule = new Rule({
                name: "MDR001",
                valueMsgStm: "data.rulesData.MDR001 = null;delete data.rulesData.MDR001;delete data.rulesData;data.sourcesData.SourceName1 = null;delete data.sourcesData.SourceName1;delete data.sourcesData;data.sourcesMetadata.SourceName1 = null;delete data.sourcesMetadata.SourceName1;delete data.sourcesMetadata;'valuemessage';",
                extraInfo: "'extraInfo';",
                action: {
                    api: "APIName",
                    parameterCode: "paramField = 'my data field';",
                    codeCondition: "",
                    autoExecution: false
                }
            });
            let serviceCheckRecord = new ServiceCheckModel({
                id: "600000000000000011111111",
                rulesData: {
                    MDR001: {
                        result: "Actionable"
                    }
                },
                sourcesData: {
                    SourceName1: {
                        a: 1
                    }
                },
                sourcesMetadata: {
                    SourceName1: {
                        status: "Collected"
                    }
                }
            });
            let api = new Api({
                name: "APIName",
                active: true,
                parameters: [
                    "paramField"
                ]
            });
            await api.save();

            let [actionResult, valueMsg, extraInfo] = await mergeRule.runRuleAction(rule, serviceCheckRecord);

            chai.expect(actionResult).to.be.like({
                createdOn: new Date(),
                response: {
                    a: 1,
                    b: 2,
                    c: 3
                },
                status: 200
            });
            chai.expect(valueMsg).to.equal("valuemessage");
            chai.expect(extraInfo).to.equal("extraInfo");

            chai.expect(serviceCheckRecord.rulesData).to.be.like({
                MDR001: {
                    result: "Actionable"
                }
            });
            chai.expect(serviceCheckRecord.sourcesData).to.eql({
                SourceName1: {
                    a: 1
                }
            });
            chai.expect(serviceCheckRecord.sourcesMetadata).to.eql({
                SourceName1: {
                    status: "Collected"
                }
            });
        });
    });
});


