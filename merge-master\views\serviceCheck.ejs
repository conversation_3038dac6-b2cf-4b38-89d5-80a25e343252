
<%
  const isDevelopment = process.env.MERGE_ENV === 'localDev';

  // Determine head content dynamically
  const generateHeadContent = () => {
    if (isDevelopment) {
      return `
        <script type="module" src="http://localhost:5173/clients/@vite/client"></script>
        <script type="module">
            import RefreshRuntime from 'http://localhost:5173/clients/@react-refresh';
            RefreshRuntime.injectIntoGlobalHook(window);
            window.$RefreshReg$ = () => {};
            window.$RefreshSig$ = () => (type) => type;
            window.__vite_plugin_react_preamble_installed__ = true;
        </script>
        <script type="module" src="http://localhost:5173/clients/src/main.tsx"></script>
      `;
    } else {
      try {
        if (manifest && manifest["index.html"]) {
          const cssLinks = manifest["index.html"].css
            ? manifest["index.html"].css
                .map((cssFile) => `<link rel="stylesheet" href="/clients/${cssFile}">`)
                .join('\n')
            : '';

          const jsScript = `<script type="module" src="/clients/${manifest["index.html"].file}"></script>`;

          return `${cssLinks}\n${jsScript}`;
        } else {
          return `
            <script>
              console.error("Manifest is missing or invalid.");
            </script>
          `;
        }
      } catch (error) {
        console.error('Error reading or parsing manifest:', error.message);
        return `
          <script>
            console.error('Error loading scripts. Check the server logs for more details.');
          </script>
        `;
      }
    }
  };

  const headContent = generateHeadContent();
%>
<%- include('header', {}); %>
<%- include('menu', { currentTab: 'Form' }); %>
<%- include('serviceCheckRuleHelpers'); %>
<%- headContent %> <!-- Dynamically injected head content -->
<link rel="stylesheet" type="text/css" href="/public/stylesheets/select2.min.css">
<script src="/public/javascripts/select2.min.js" crossorigin="anonymous"></script>
<script src="/public/javascripts/magpie-schematics.js" crossorigin="anonymous"></script>
<script>
  window.__USER__ = {
    username: "<%= user.username %>"
  };
</script>
<div class="container">
    <body>
        <div id="react-root"></div>
    </body>
</div>
<%- include('footer', {}); %>
