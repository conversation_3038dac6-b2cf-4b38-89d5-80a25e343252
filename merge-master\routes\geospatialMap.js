
import express from 'express';
import { query, validationResult } from 'express-validator';

import auth from '../modules/auth.js';
import { AuthorizationRoles } from '../modules/enumerations.js';

const router = express.Router();


router.get('/coverage', auth.authorizeForRoles([
    AuthorizationRoles.readOnly,
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), [
    query('address').optional().isString(),
    query('latitude').optional().isFloat({ min: -90.0, max: 90.0 }).withMessage('Latitude must be between -90.0 and 90.0').toFloat(),
    query('longitude').optional().isFloat({ min: -180.0, max: 180.0 }).withMessage('Longitude must be between -180.0 and 180.0').toFloat(),
    query('adborId').optional().isString(),
    query('technology').optional().isString()
], function(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
    }

    res.render('geospatialCoverageMap', {
        title: 'Geospatial Coverage Map',
        geospatialServicesClientId: global.gConfig.geospatialServicesClientId,
        address: req.query.address || '',
        latitude: typeof req.query.latitude === 'number' ? req.query.latitude : '',
        longitude: typeof req.query.longitude === 'number' ? req.query.longitude : '',
        adborId: req.query.adborId || '',
        technology: req.query.technology || ''
    });
});


export default router;
