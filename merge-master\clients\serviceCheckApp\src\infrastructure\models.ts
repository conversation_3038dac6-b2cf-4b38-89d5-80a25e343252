export interface ValidateServiceNumberResponse {
    isValid: boolean
    message?: string
    serviceDetails: ServiceDetailsModel
}

export interface FieldProps {
    value: string | null | undefined
}

export interface Diagram {
    type: string
    fnn: string
    xml: string
}
export type TestResultStatus = 'OK' | 'Failed' | 'Error' | 'Reject' | 'Warning' | 'Actionable' | 'Actioned'

export interface ValueMsg {
    renderAsTable: boolean
    data: Record<string, unknown>[]
}

export interface TestPackageResultData {
    status: string
    type: string
    rootCauseCategory: string
    result?: TestResultStatus
    error?: string
    valueMsg: string
    extraInfo: string
    updatedOn: string
    msg: string
}

export interface TestPackageResultItem {
    name: string
    title: string
    ruleName: string
    data?: TestPackageResultData
}

export interface TestPackageResult {
    id: string
    name: string
    results: Array<TestPackageResultItem>
}

type Tab = 'overview' | 'tests'
type TestStatus = 'completed' | 'not-run' | 'loading' | 'timeout'

export interface TestResultsModel {
    packageName: string
    packageTitle: string
    status: TestStatus
    results: TestPackageResultItem[] | null
}
export interface TestActionResponse {
    createdOn: string
    response: unknown
    status?: number
}

export interface TestActionsResponse {
    updatedRule: TestActionsModel | null
    statusCode: number
    error?: string
}

export interface UserInput {
    type: 'daterange-locale' | 'text'
    fieldNames: string[]
}
export interface TestActionsModel {
    action?: TestActionResponse | null
    name: string
    msg: string
    result: string
    updatedOn: string
    valueMsg: string
    userInputs?: UserInput[]
}

export interface TextTemplate {
    name: string
    title: string
    description: string
    defaultPriority: number
    allowedSystemsToAppend: string[]
}

export type TestPackageRequest = Omit<
    SelectedTestPackage,
    'isSelected' | 'hasDateRangeFilter' | 'title'
>

export interface SelectedTestPackage {
    name: string
    title: string
    isSelected: boolean
    hasDateRangeFilter: boolean
    parameters: TestPackageParameter
}

export interface TestPackageParameter {
    startDate?: string
    endDate?: string
}

export interface AppState {
    id: string
    createdBy: string
    createdOn: string
    isLoadingService: boolean
    isLoadingServiceError: boolean
    serviceNumber: string
    serviceNumberErrorMessage: string
    serviceDetails: ServiceDetailsModel
    selectedTab: Tab
    selectedTestPackages: SelectedTestPackage[]
    testResults: TestResultsModel[]
    testPackages: TestPackage[]
    testActions: TestActionsModel[]
    textTemplates: TextTemplate[]
    nextBestAction?: string
    showOkResults: boolean
    showUndeterminedResults: boolean
}

export const InitialAppState: AppState = {
    id: '',
    createdBy: '',
    createdOn: '',
    isLoadingService: false,
    isLoadingServiceError: false,
    serviceNumber: '',
    serviceNumberErrorMessage: '',
    selectedTab: 'overview',
    selectedTestPackages: [],
    testResults: [],
    testActions: [],
    textTemplates: [],
    testPackages: [],
    nextBestAction: '',
    serviceDetails: {
        createdBy: '',
        createdOn: '',
        feedback: null,
        status: 'none',
        customerDetails: {},
        productDetails: {},
        deviceDetails: {},
        accountStatus: {},
        outages: {},
        activeIncidents: {},
        diagrams: [],
    },
    showOkResults: true,
    showUndeterminedResults: false,
}

// Request payload model
export interface StartServiceCheckRequest {
    fnn: string // The input FNN to start the service check
}

// Response models
export interface CustomerDetails {
    customer?: string | null
    location?: string | null
    cidn?: string | null
    customerConsent?: boolean | null
}

export interface ProductDetails {
    fnn?: string
    carriageFNN?: string | null
    carriageType?: string | null
    accessType?: string | null // Not in ADSL, BDSL, NBN EE,
    nbnId?: string | null // NBN only
    ipwanNetwork?: string | null
    ipwanPort?: string | null
    ceIpAddress?: string | null
    routingProtocol?: string | null
    serviceSpeed?: string | null
    avc?: string | null // NBN, IPMAN, VOIP only
    productName?: string | null
    serviceType?: string | null
    categoryOfService?: string | null
    mdnNetworkFnn?: string | null
    mdnService?: string | null

    // Additional fields that were missing
    mwanDeviceId?: string | null // ADSL only

    // NBN EE only
    memberOfIpwanNetwork?: string | null
    trunkFNN?: string | null
    associatedAccessService?: string | null
    associatedAccessServiceFNN?: string | null
    mediaType?: string | null
    avcNBNEE?: string | null
    bpiId?: string | null
    interfaceType?: string | null
    ovcType?: string | null
    uniPort?: string | null

    phoneNumberRanges?: string[] | null
}

export interface DeviceDetails {
    deviceName?: string | null
    deviceType?: string | null
    supplierCode?: string | null
    productCode?: string | null
    deviceSerialNumber?: string | null
    backup?: string | null
    backupAlternateAccessFnn?: string | null
    devicePowerResetStatus?: string | null
    deviceStatus?: string | null
    basementSwitch?: string | null // Not in IPMAN NON basement, VOIP, Mobile

    //FTTC, FTTB Fields
    macAddress?: string | null
    make?: string | null
    model?: string | null
    serialNumber?: string | null
    firmwareVersion?: string | null

    // FTTP fields
    fttpId?: string | null
    fttpPortId?: string | null

    // NTU fields
    ntu?: string | null
    ntuType?: string | null

    // VOIP fields
    deviceStatusVoip?: string | null
    deviceRegisterationStatusVoip?: string | null
    deviceClusterVoip?: string | null
    deviceNameVoip?: string | null
    deviceVersionVoip?: string | null
    deviceIPWorksDomainVoip?: string | null
    deviceUserFNNVoip?: string | null
    deviceGroupFNNVoip?: string | null
    deviceTrunkFNNVoip?: string | null
    deviceSiteFNNVoip?: string | null
    deviceNumberVoip?: string | null
    deviceDetailsVoip?: string | null
    deviceRegistrationsVoip?: DeviceRegistration[] | null
    deviceTypeSIPNTU?: string | null

    // NTD fields
    ntdId?: string | null // FTTP, Wireless
    ntdPortId?: string | null // NBN HFC, FTTP, Wireless
    ntdPortState?: string | null // NBN HFC, FTTP
    ntdVersion?: string | null // Wireless
    ntdSerialNumber?: string | null // FTTP
    ntdMake?: string | null // FTTP
    ntdModel?: string | null // NBN HFC
    ntdMacAddress?: string | null // NBN HFC
    ntdInstallLocation?: string | null // FTTP
    ntdCurrentSpeedDuplex?: string | null // FTTP
    ntdServiceConfiguration?: string | null // NBN HFC

    //CPE Fields
    cpeMacAddress?: string | null // NBN HFC
}

export interface Outages {
    plannedOutages?: PlannedOutage[]
    unplannedOutages?: UnplannedOutage[]
    powerOutages?: PowerOutage[]
    nbnOutages?: NBNOutage[]
}

export interface PlannedOutage {
    calm_id: string
    start_date: string
    end_date: string
}

export interface UnplannedOutage {
    REC_ID: string
    EVENT_DATE: string
    ETR: string
}

export interface PowerOutage {
    reason: string
    ETR: string
}

export interface NBNOutage {
    outage_type: string
    value: string
}

export interface ActiveIncident {
    id: string
    creationTime: string
    fnn: string
    status: string
}

export interface HistoryIncident {
    id: string
    creationTime: string
    fnn: string
    status: string
}

export interface ServiceCentralIncident {
    id: string
    openedAt: string
    fnn: string
    state: string
}

export interface PromiseTask {
    crn: string
    slaDate: string
    status: string
}

export interface ActiveIncidents {
    siiamActiveCases?: ActiveIncident[]
    siiamHistoryCases?: HistoryIncident[]
    serviceCentralIncidents?: ServiceCentralIncident[]
    promiseTasks?: PromiseTask[]
}

export interface TestPackage {
    name: string
    title: string
    sectionTitles: string[]
    hasDateRangeFilter: boolean
    defaultStartOffsetHours: number | null
}

export interface AccountStatus {
    rassStatus?: string
    rassCancelledOrders?: RassOrder[]
    rassModOrders?: RassOrder[]
}

export interface RassOrder {
    ORDER_NUMBER: string
    SERVICE_ORDER_TYPE: string
    CUST_REQUIRED_DATE: string
}

export interface DeviceRegistration {
    user: string,
    regType: string,
    device: string,
    deviceBak: string,
    linePort: string,
    expires: string,
    expiresBak: string,
    userAgent: string,
    SBC: string,
    callInfo: string,
    trunkFnn: string
}

type ProductType =
    | 'ADSL'
    | 'Basement Switch Device'
    | 'BDSL'
    | 'FR'
    | 'INTERNATIONAL'
    | 'IPMAN'
    | 'MDN'
    | 'MOBILE'
    | 'NBN'
    | 'NBN EE'
    | 'NBN FTTB'
    | 'NBN FTTC'
    | 'NBN FTTN'
    | 'NBN FTTP'
    | 'NBN HFC'
    | 'NBN Wireless'
    | 'NextG IPWAN'
    | 'NTU Device'
    | 'Telstra Fibre Adapt'
    | 'VOIP'

export type ServiceTestStatus =
    | 'running'
    | 'error'
    | 'donePartial'
    | 'done'
    | 'completedWithError'
    | 'completedWithErrorPartial'
    | 'abortedInitial'
    | 'none'

export interface ServiceTestFeedback {
    isPositive: boolean
    messageExists: boolean
    messageRead: boolean
    message: string | null
    createdOn: string
}

export interface ServiceTestFeedbackRequest {
    serviceCheckId: string
    feedback: { isPositive: boolean; message?: string }
}

export interface ServiceDetailsModel {
    id?: string
    feedback: ServiceTestFeedback | null
    createdBy: string
    createdOn: string
    status: ServiceTestStatus
    productTypes?: ProductType[]
    customerDetails: CustomerDetails
    productDetails: ProductDetails
    deviceDetails: DeviceDetails
    accountStatus: AccountStatus
    outages: Outages
    activeIncidents: ActiveIncidents
    testPackages?: TestPackage[]
    testActions?: TestActionsModel[]
    testResults?: TestResultsModel[]
    textTemplates?: TextTemplate[]
    nextBestAction?: string
    diagrams?: Diagram[]
}
