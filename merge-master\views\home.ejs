<%- include('header', {title: 'Merge Home'}); %>
<link href="public/stylesheets/landing-page.css" rel="stylesheet">
<%- include('menu', {currentTab: 'Merge'}); %>
<!-- Masthead -->
<header class="masthead text-white text-center">
    <div class="overlay"></div>
    <div class="container">
      <div class="row">
        <div class="col-xl-9 text-white mx-auto">
          <h1 class="mb-5">Test from one place, faster and deeper!</h1>
        </div>
        <div class="col-md-10 col-lg-8 col-xl-7 mx-auto">
            <!--
          <form>
            <div class="form-row">
              <div class="col-12 col-md-8 mb-2 mb-md-0">
                <input type="email" class="form-control form-control-lg" placeholder="FNN">
              </div>
              <div class="col-12 col-md-2">
                <button type="submit" class="btn btn-block btn-lg btn-primary">Check</button>
              </div>
              <div class="col-12 col-md-2">
                <button type="submit" class="btn btn-block btn-lg btn-primary">History</button>
              </div>
            </div>
          </form>
        -->
        </div>
      </div>
    </div>
  </header>

  <!-- Icons Grid -->
  <section class="features-icons bg-light text-center">
    <div class="container">
      <div class="row">
        <% if(user && user.isAAAUser) { %>
        <div class="col-lg-3">
        <% } else { %>
        <div class="col-lg-4">
        <% } %>
          <div class="features-icons-item mx-auto mb-5 mb-lg-0 mb-lg-3">
            <div class="features-icons-icon">
                <a href="ServiceCheck"><span class="fas fa-stethoscope fa-4x"></span></a>
            </div>
            <h3>Service Check</h3>
            <p class="lead mb-0">Check an FNN in 20 systems from 200 aspects by one <a href="ServiceCheck">click</a>!</p>
          </div>
        </div>
        <% if(user && user.isAAAUser) { %>
        <div class="col-lg-3">
        <% } else { %>
        <div class="col-lg-4">
        <% } %>
          <div class="features-icons-item mx-auto mb-5 mb-lg-0 mb-lg-3">
            <div class="features-icons-icon">
              <a href="command"><span class="fas fa-terminal fa-4x"></span></a>
            </div>
            <h3>Quick Command</h3>
            <p class="lead mb-0">Access to information and a system by one <a href="command">Enter</a>!</p>
          </div>
        </div>
        <% if(user && user.isAAAUser) { %>
        <div class="col-lg-3">
          <div class="features-icons-item mx-auto mb-5 mb-lg-0 mb-lg-3">
            <div class="features-icons-icon">
              <a target="_blank" href="<%=global.gConfig.HAPIURI.slice(0, -5)%>AAAData/index.php?userid=<%=user.encryptedUsername%>"><span class="fas fa-eye fa-4x"></span></a>
            </div>
            <h3>AAA Tool</h3>
            <p class="lead mb-0">Search AAA logs to investigate Dynamic Access Authentication Issues <a target="_blank" href="<%=global.gConfig.HAPIURI.slice(0, -5)%>AAAData/index.php?userid=<%=user.encryptedUsername%>">here</a>!</p>
          </div>
        </div>
        <% } %>
        <% if(user && user.isAAAUser) { %>
        <div class="col-lg-3">
        <% } else { %>
        <div class="col-lg-4">
        <% } %>
          <div class="features-icons-item mx-auto mb-0 mb-lg-3">
            <div class="features-icons-icon">
                    <a href="https://teams.microsoft.com/l/channel/19%3A72e52cc404e244abac8db5ab20a69023%40thread.skype/Feedback%20and%20SME%20Support?groupId=849954dd-a75d-4da2-9cd9-b81e07231040&tenantId=49dfc6a3-5fb7-49f4-adea-c54e725bb854"
                    onclick="return !window.open(this.href, 'Merge Feedback', 'width=800,height=600')"
                    target="_blank">
                        <span class="fas fa-comment-dots fa-4x"></span>
                    </a>
            </div>
            <h3>Easy to Extend</h3>
            <p class="lead mb-0">Ready to contribute, or want more sources and rules, tell us <a href="https://teams.microsoft.com/l/channel/19%3A72e52cc404e244abac8db5ab20a69023%40thread.skype/Feedback%20and%20SME%20Support?groupId=849954dd-a75d-4da2-9cd9-b81e07231040&tenantId=49dfc6a3-5fb7-49f4-adea-c54e725bb854"
                onclick="return !window.open(this.href, 'Merge Feedback', 'width=800,height=600')"
                target="_blank">here</a>!</p>
          </div>
        </div>
      </div>
    </div>
  </section>

<%- include('footer', {}); %>
