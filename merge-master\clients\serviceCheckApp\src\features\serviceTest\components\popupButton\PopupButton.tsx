import { faUpRightFromSquare } from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import classNames from 'classnames'
import styles from './PopupButton.module.scss'

interface PopupButtonProps {
    onClick: () => void
}

export const PopupButton = ({ onClick }: PopupButtonProps) => {
    return (
        <button
            className={classNames(styles.popupButton)}
            onClick={(e) => {
                e.stopPropagation()
                onClick()
            }}
        >
            <FontAwesomeIcon
                icon={faUpRightFromSquare}
                className={styles.icon}
            />
        </button>
    )
}
