import { TextStyle } from '@able/react'
import DOMPurify from 'dompurify'
import { ReactElement, useState } from 'react'
import { ContentModal } from '../../../../components/contentModal/ContentModal'
import { DetailField } from '../../../../components/detailsPanel/DetailField'
import { DetailsPanel } from '../../../../components/detailsPanel/DetailsPanel'
import { formatDate } from '../../../../helpers/formatDate'
import { TestPackageResultItem, TestResultsModel } from '../../../../infrastructure/models'
import { RagStatus } from '../../../ragStatus/RagStatus'
import { PopupButton } from '../popupButton/PopupButton'
import { LoadingPanel } from '../serviceCheckLoadingPanel/LoadingPanel'
import styles from './TestResults.module.scss'

interface TestResultItemProps {
    packageData: TestResultsModel
    result: TestPackageResultItem
}

export const TestResultItem = ({
    packageData,
    result,
}: TestResultItemProps) => {
    const [showModal, setShowModal] = useState(false)
    const [modalTitle, setModalTitle] = useState('')
    const [modalContent, setModalContent] = useState<ReactElement | null>(null)

    const handleOpenModal = () => {
        setModalTitle(`${packageData.packageTitle} - ${result.title}`)
        setModalContent(renderResultMessage(packageData, result))
        setShowModal(true)
    }

    return (
        <>
            <DetailsPanel
                fitContent={packageData.status === 'loading'}
                label={`${packageData.packageTitle} - ${result.title}`}
                panelElements={
                    packageData.status === 'completed' &&
                    result.data?.result ? (
                        <>
                            <PopupButton onClick={handleOpenModal} />
                            <RagStatus ragStatus={result.data.result} />
                        </>
                    ) : (
                        <></>
                    )
                }
            >
                {renderResultMessage(packageData, result)}
            </DetailsPanel>
            <ContentModal
                isOpen={showModal}
                title={modalTitle}
                isLoading={false}
                onClose={() => setShowModal(false)}
            >
                {modalContent}
            </ContentModal>
        </>
    )
}

const renderResultMessage = (
  packageData: TestResultsModel,
  result: TestPackageResultItem
): ReactElement => {
  if (packageData.status === 'loading') {
    return <LoadingPanel />
  }

  if (packageData.status === 'timeout') {
    return <TextStyle alias="FinePrintA">Response timeout</TextStyle>
  }

  if (!result.data) {
    return <TextStyle alias="FinePrintA">Result not determined</TextStyle>
  }

  const { result: resultStatus, msg, valueMsg, error, updatedOn } = result.data

  switch (resultStatus) {
    case 'Reject':
      return (
        <TextStyle alias="FinePrintA">
          {msg || 'No message provided'}
        </TextStyle>
      )

    case 'Error':
      return (
        <TextStyle alias="FinePrintA">
          <div style={{color: 'red'}}>
            {error || 'No error message'}
          </div>
        </TextStyle>
      )
    case 'OK':
    case 'Warning':
    case 'Failed':
      if (!valueMsg) {
        return <TextStyle alias="FinePrintA">Results empty</TextStyle>
      }

      return (
        <div>
          <DetailField
            label="Timestamp"
            value={formatDate(updatedOn)}
            rightAlign
            className={styles.timestamp}
          />
          <div
            className={styles.testResult}
            dangerouslySetInnerHTML={{
              __html: DOMPurify.sanitize(valueMsg),
            }}
          />
        </div>
      )

    default:
      return <TextStyle alias="FinePrintA">Results empty</TextStyle>
  }
}
