// mergeCollect.js
// V4
// =========
// All functions for parsing command and script.
import Ajv from 'ajv';
import fs from 'fs';
import { CronJob } from 'cron';
import https from 'https';
import axios from 'axios';
import tunnel from 'tunnel';

import debug from 'debug';
import authKeysModel from '../db/model/apiAuthKeys.js';
import authEmitter from '../modules/authEmitter.js';
import constants from '../modules/helpers/constants.js';
import logger from './logger.js';

const ajv = new Ajv({ allErrors: true });
const debugMsg = debug('merge:apiAuthKeyCron');

const authKeys = JSON.parse(await fs.promises.readFile('./config/authKeys.json'));
const authKeysSchema = JSON.parse(await fs.promises.readFile('./config/authKeySchema.json'));
const validate = ajv.compile(authKeysSchema);


export function runCronJob() {
    debugMsg('API auth key cron: start setup');
    logger.info('API auth key cron: start setup');

    if (!validate(authKeys)) {
        throw new Error(`Auth key configuration file does not match ` +
                        `JSON schema\n` +
                        `${JSON.stringify(validate.errors, null, 4)}`);
    }

    Object.keys(authKeys).forEach((authKeyName) => {
        let authKeyConfig = authKeys[authKeyName];
        let httpsTunnelConfig = {};

        let url;
        let restHeader;
        let restBody;

        let masslCert = null;
        let masslKey = null;

        // Skips current auth key config if it is not active
        if (!authKeyConfig.active) {
            return;
        }

        try {
            url = eval(authKeyConfig.url);
            restHeader = authKeyConfig.header ? JSON.parse(authKeyConfig.header) : {};
            restBody = eval(authKeyConfig.body);

            if (authKeyConfig.hasOwnProperty('masslCertificateName')) {
                let masslConfig = global.gConfig.APIMasslCertificates[authKeyConfig.masslCertificateName];

                if (masslConfig) {
                    masslCert = fs.readFileSync(masslConfig.cert);
                    masslKey = fs.readFileSync(masslConfig.key);
                } else {
                    logger.warn(`API auth key cron: ${authKeyName} MASSL certificate name ${authKeyConfig.masslCertificateName} does not appear to be valid`);
                }
            }

            if (authKeyConfig.proxyRequired) {
                httpsTunnelConfig.proxy = {
                    host: global.gConfig.proxyHost,
                    port: global.gConfig.proxyPort,
                    proxyAuth: `${global.gConfig.robotAccount01Username}:${global.gConfig.robotAccount01Password}`
                };
            }
        } catch(error) {
            logger.error(`API auth key cron: error in evaluating variables for auth key ${authKeyName} ${error.toString()}`);
            return;
        }

        try {
            CronJob.from({
                cronTime: authKeyConfig.cronTime,
                onTick: async function() {
                    try {
                        // Reconfigures the proxy auth credentials in case it has changed
                        if (authKeyConfig.proxyRequired) {
                            httpsTunnelConfig.proxy.proxyAuth = `${global.gConfig.robotAccount01Username}:${global.gConfig.robotAccount01Password}`;
                        }

                        let httpsAgent;

                        if (Object.keys(httpsTunnelConfig).length) {
                            if (masslCert && masslKey) {
                                httpsTunnelConfig.cert = masslCert;
                                httpsTunnelConfig.key = masslKey;
                            }
                            httpsAgent = tunnel.httpsOverHttp(httpsTunnelConfig);
                        } else {
                            let httpsAgentConfig = { rejectUnauthorized: false };
                            if (masslCert && masslKey) {
                                httpsAgentConfig.cert = masslCert;
                                httpsAgentConfig.key = masslKey;
                            }
                            httpsAgent = new https.Agent(httpsAgentConfig);
                        }

                        if (Array.isArray(authKeyConfig.requiredTokens)) {
                            for (let requiredToken of authKeyConfig.requiredTokens) {
                                try {
                                    let storedToken = await authKeysModel.findOne({ name: requiredToken.name });
                                    if (requiredToken?.header && storedToken?.authKey) {
                                        restHeader[requiredToken.header] = storedToken.authKey;
                                    }
                                } catch(error) {
                                    logger.warn(`Auth key cron, failed to retrieve previous token ${requiredToken.name} for ${authKeyName}, ${error.toString()}`);
                                }
                            }
                        }

                        // Currently all auth keys are obtained via HTTP POST
                        // If another method is required for getting auth keys,
                        // implement "method" field in the auth key config
                        let response = await axios.post(url, restBody, {
                            headers: restHeader,
                            httpsAgent: httpsAgent,
                            proxy: false,
                            timeout: authKeyConfig.timeout
                        });

                        let newApiKey = eval(authKeyConfig.apiAuthKey);
                        debugMsg('#> Auth key return for : ' + authKeyName, newApiKey);

                        if (authKeyConfig.authType) {
                            newApiKey = `${authKeyConfig.authType} ${newApiKey}`;
                        }

                        let authKeyRecord = await authKeysModel.findOne({ name: authKeyName });

                        // Creates a new auth key document if it does not exist
                        if (!authKeyRecord) {
                            authKeyRecord = new authKeysModel();
                            authKeyRecord.name = authKeyName;
                        }
                        authKeyRecord.authKey = newApiKey;
                        authKeyRecord.updatedOn = new Date();

                        // Saves auth key document to database
                        await authKeyRecord.save();
                        logger.info(`API auth key cron: Successfully saved API auth key ${authKeyName} to database`);
                    } catch(error) {
                        debugMsg(`!> Error when obtaining API auth key for ${authKeyName}, ${error.toString()}`);
                        logger.error(`API auth key cron: error when obtaining API auth key for ${authKeyName}, ${error.toString()}`);

                        if (error.message == constants.PROXY_AUTH_ERROR_MESSAGE) {
                            authEmitter.emit('AuthError');
                        }
                    }
                },
                start: true
            });

            logger.info(`API auth key cron: started cron job for ${authKeyName}`);
        } catch(error) {
            logger.error(`API auth key cron: error when starting cron job for  ${authKeyName}, ${error.toString()}`);
        }
    });
}
