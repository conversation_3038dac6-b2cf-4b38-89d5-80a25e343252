// Base schema for a service check record
// currently used by serviceCheck and generatedServiceCheck models
import util from 'util';
import mongoose from 'mongoose';
import _ from 'lodash';

import Outcome from '../model/outcome.js';
import { InputTypes, ProductTypes } from '../../modules/enumerations.js';
import ServiceCheckSourceData from '../model/serviceCheckSourceData.js';
import logger from '../../modules/logger.js';
import { SaveSourceDataError } from '../../modules/error.js';


const inputSchema = new mongoose.Schema({
    searchFNN: { type: String, default: null },
    suite: {
        type: String,
        default: 'standard',
        enum: [
            'standard',
            'andig',
            'ipvoice',
            'outageInfo',
            'wholesale'
        ]
    },
    level: { type: Number, min: 0, max: 6, default: 0 },
    carriageFNN: { type: String, default: null },
    carriageType: { type: String, default: null },
    deviceIP: { type: String, default: null },
    deviceName: { type: String, default: null },
    idType: { type: String, default: null },
    CIDN: { type: String, default: null },
    fieldInterfaceIP: { type: String, default: null },
    adborId: { type: String, default: null },
    latitude: { type: Number, min: -90, max: 90, default: null },
    longitude: { type: Number, min: -180, max: 180, default: null },
    ruleNames: { type: [{ type: String }], default: [] },
    sourceNames: { type: [{ type: String }], default: [] }
}, { _id: false });


function ServiceCheckSchema() {
    mongoose.Schema.apply(this, arguments);

    this.add({
        id: { type: String, default: null, index: true, immutable: true },
        fnn: { type: String, default: '', index: { collation: { locale: 'en', strength: 1 }}},
        inputType: { type: String, default: null, enum: [...Object.values(InputTypes), null], index: { collation: { locale: 'en', strength: 1 }}},
        phoneNumber: { type: String, default: null, index: true },
        billingFNN: { type: String, default: null, index: { collation: { locale: 'en', strength: 1 }}},
        carriageFNN: { type: String, default: null, index: { collation: { locale: 'en', strength: 1 }}},
        MDNFNN: { type: String, default: null, index: { collation: { locale: 'en', strength: 1 }} },
        carriageType: { type: String, default: null, index: { collation: { locale: 'en', strength: 1 }}},
        nbnServiceType: { type: String, default: null },
        nbnAccessType: { type: String, default: null, index: { collation: { locale: 'en', strength: 1 }}},
        nbnSubAccessType: { type: String, default: null },
        nbnId: { type: String, default: null, index: { collation: { locale: 'en', strength: 1 }} },
        deviceName: { type: String, default: null, index: { collation: { locale: 'en', strength: 1 }}},
        CIDN: { type: String, default: null, index: true },
        address: { type: String, default: null },
        OffshoreResources: { type: String, default: null },
        serviceType: { type: String, default: null, index: { collation: { locale: 'en', strength: 1 }}},
        siiamCases: {
            type: [{
                type: String
            }],
            default: []
        },
        siiamCasesLength: { type: Number, default: 0, select: false, index: true },
        serviceCentralIncidents: {
            type: [{
                type: String
            }],
            default: []
        },
        serviceCentralIncidentsLength: { type: Number, default: 0, select: false, index: true },
        status: { type: String, default: null, index: true },
        endedOn: { type: Date, default: null },
        durationMilliSec: { type: Number, default: 0 },
        input: {
            type: inputSchema,
            default: {}
        },
        additionalParameters: { type: Object, default: {} },
        rulesData: { type: Object, default: {} },
        sourcesMetadata: { type: Object, default: {} },
        errorMessage: { type: String, default: null },
        outcomeNames: {
            type: [{
                type: String
            }],
            default: []
        },
        productTypes: {
            type: [{
                type: String,
                enum: Object.values(ProductTypes),
                required: true
            }],
            default: [],
            validate: {
                validator: function(productTypes) {
                    return productTypes.length === new Set(productTypes).size;
                },
                message: 'array should only contain unique string values'
            }
        },
        createdBy: { type: String, required: true, immutable: true, default: 'unknown', index: { collation: { locale: 'en', strength: 1 }}},
        createdOn: { type: Date, required: true, immutable: true, default: Date.now, index: true }
    });

    this.virtual('outcomes', {
        ref: Outcome,
        localField: 'outcomeNames',
        foreignField: 'name',
        justOne: false
    });

    this.virtual('sourcesData');

    this.methods.populateSourcesData = async function() {
        this.sourcesData = {};

        let sourcesData = await ServiceCheckSourceData.find({ serviceCheckId: this._id });

        for (let document of sourcesData) {
            let sourceName = document.name;
            let sourceData = document.data;
            this.sourcesData[sourceName] = sourceData;
        }
    };

    this.index({ fnn: 1, createdOn: -1 }, { collation: { locale: 'en', strength: 1 }});
    this.index({ phoneNumber: 1, createdOn: -1 });
    this.index({ billingFNN: 1, createdOn: -1 }, { collation: { locale: 'en', strength: 1 }});
    this.index({ carriageFNN: 1, createdOn: -1 }, { collation: { locale: 'en', strength: 1 }});
    this.index({ MDNFNN: 1, createdOn: -1 }, { collation: { locale: 'en', strength: 1 }});
    this.index({ carriageType: 1, createdOn: -1 }, { collation: { locale: 'en', strength: 1 }});
    this.index({ nbnAccessType: 1, createdOn: -1 }, { collation: { locale: 'en', strength: 1 }});
    this.index({ nbnId: 1, createdOn: -1 }, { collation: { locale: 'en', strength: 1 }});
    this.index({ deviceName: 1, createdOn: -1 }, { collation: { locale: 'en', strength: 1 }});
    this.index({ CIDN: 1, createdOn: -1 });
    this.index({ serviceType: 1, createdOn: -1 }, { collation: { locale: 'en', strength: 1 }});
    this.index({ status: 1, createdOn: -1 });
    this.index({ siiamCasesLength: 1, createdOn: -1 });
    this.index({ serviceCentralIncidentsLength: 1, createdOn: -1 });
    this.index({ siiamCasesLength: 1, serviceCentralIncidentsLength: 1, createdOn: -1 });
    this.index({ createdBy: 1, createdOn: -1 }, { collation: { locale: 'en', strength: 1 }});
    this.index({ 'input.suite': 1, createdOn: -1 });

    this.methods.toJSON = function() {
        let obj = this.toObject();
        delete obj._id;
        delete obj.__v;
        return obj;
    }

    this.pre('validate', async function() {
        if (_.isArray(this.productTypes)) {
            this.productTypes = this.productTypes.filter((productType) => Object.values(ProductTypes).includes(productType));
        } else {
            this.productTypes = [];
        }
    });

    // Sets fields which store the length of array fields, as the length fields are
    // indexed and provide a quick lookup as to whether the array fields contain values or not
    this.pre('save', async function() {
        if (Array.isArray(this.siiamCases)) {
            this.siiamCasesLength = this.siiamCases.length;
        }

        if (Array.isArray(this.serviceCentralIncidents)) {
            this.serviceCentralIncidentsLength = this.serviceCentralIncidents.length;
        }

        // This creates instances of ServiceCheckSourceData, will does not remove source data if it
        // is removed from the object in a ServiceCheck / GeneratedServiceCheck instance
        // May not be the most efficient implementation, since the service check is currently saved
        // every 30 seconds, previously collected sources may also be updated at the moment
        if (_.isPlainObject(this.sourcesData) && !this.$skipSavingSourcesData) {
            for (let sourceName in this.sourcesData) {
                try {
                    await ServiceCheckSourceData.findOneAndUpdate(
                        { serviceCheckId: this._id, name: sourceName },
                        { $set: { serviceCheckId: this._id, name: sourceName, data: this.sourcesData[sourceName] }},
                        { upsert: true, strict: 'throw', runValidators: true, setDefaultsOnInsert: true, useFindAndModify: false }
                    );
                } catch(error) {
                    logger.warn(`Service check ${this.id} failed to save source ${sourceName}, ${error.toString()}`);
                    if (this.sourcesMetadata && _.isPlainObject(this.sourcesMetadata[sourceName])) {
                        this.sourcesMetadata[sourceName].status = 'error';
                        this.sourcesMetadata[sourceName].error = new SaveSourceDataError(`Failed to save source data for ${sourceName}`, { cause: error }).toString();
                    }
                }
            }
        }
    });

    // Includes virtual fields in toObject() and toJSON() calls
    this.options.toObject = { virtuals: true };
    this.options.toJSON = { virtuals: true };
}

util.inherits(ServiceCheckSchema, mongoose.Schema);

export default ServiceCheckSchema;
