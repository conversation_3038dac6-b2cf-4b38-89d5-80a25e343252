<%- include('header', {}); %>
<%- include('menu', {currentTab: 'Home'}); %>
<div class="container">
    <br>
    <div class="jumbotron">
        <h1 class="display-4">Add/Edit Script</h1>
        <br>
        <div class="row">
            <div class="col">
                <input id="scriptName" type="text" class="form-control" placeholder="Script Name">
                <input type="hidden" id="scriptOID" name="OID" value="" />
            </div>
            <div class="col">
                <button id="loadScript" type="button" class="btn btn-primary">Load</button>
                <button id="saveScript" type="button" class="btn btn-success">Save</button>
                <button id="saveScript" type="button" class="btn btn-secondary" onclick="clearForm()">New</button>
            </div>
        </div>
        <br />
        <fieldset class="form-group">
            <div class="row">
              <legend class="col-form-label col-sm-2 pt-0">Script Type</legend>
              <div class="col-sm-10">
                <div class="form-check-inline">
                  <input class="form-check-input" type="radio" name="scriptType" id="scriptTypeBash" value="bash" checked>
                  <label class="form-check-label" for="gridRadios1">Bash</label>
                </div>
                <div class="form-check-inline disabled">
                  <input class="form-check-input" type="radio" name="scriptType" id="scriptTypeJavaScript" value="JavaScript" disabled>
                  <label class="form-check-label" for="gridRadios3">JavaScript</label>
                </div>
              </div>
            </div>
          </fieldset>
        <input id="scriptPermission" type="text" class="form-control" placeholder="AGS Group Can Run (Empty means everyone)">
        <textarea id="scriptHelp" rows="2" class="form-control" placeholder="help"></textarea>
        <textarea id="scriptBody" rows="10" class="form-control" placeholder="script"></textarea>
        <br>
        <br>
        <input id="testCommand" type="text" class="form-control" placeholder="script input paramters">
        <button id="testRun" type="button" class="btn btn-success">Test Run</button>
        <button id="clear" type="button" class="btn btn-secondary" onclick="clearResultInPage()">Clear</button>

    </div>
    <div id="QCResults"></div>
</div>
<script>
    var socket = io();
    var currentScriptOID;

    $(document).ready(function () {

        //Triger when Save button clicked
        $("#saveScript").click(function () {
            saveScript();
        });
        //Triger when Load button clicked
        $("#loadScript").click(function () {
            loadScript();
        });
    });


    function saveScript(QCScript) {
        var scriptID = idGen('MCS');
        var QCScript = {
            id        : scriptID,
            name: $("#scriptName").val(),
            type: 'bash',
            permission: $("#scriptPermission").val(),
            help: $("#scriptHelp").val(),
            body: $("#scriptBody").val(),
        };

        $.post('/script/save', QCScript);

    }

    //Set value of script fields
    function setScript(script) {
        $("#scriptName").val(script.name);
        $("#scriptOID").val(script._id);
        $("#scriptPermission").val(script.permission);
        $("#scriptHelp").val(script.help);
        $("#scriptBody").val(script.body);
    }

    //Get a script by its name
    function loadScript() {
        $.get('/script/load/' + $("#scriptName").val(), (script) => {
            //var script = data.shift();
            if (script != null) {
                currentScriptOID = script._id;
                setScript(script);
                console.log('#> loaded script is : ' , script);

            } else {
                console.log('#> No script exist with this name!');

            }
        })
    }


    function clearForm() {
        $("#scriptName").val('');
        $("#scriptOID").val('');
        $("#scriptHelp").val('');
        $("#scriptPermission").val('');
        $("#scriptBody").val('');
        console.log('Cleared Form');
    }


</script>
<%- include('footer', {}); %>