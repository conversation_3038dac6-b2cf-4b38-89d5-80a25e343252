
import _ from 'lodash';

/**
 *
 * @param {Object} obj Input object to freeze recursively
 */
function deepFreeze(obj) {
    if (!_.isObjectLike(obj)) {
        return;
    }

    // Some objects like Buffer instances can't be frozen, but may exist
    // as properties of another object
    try {
        Object.freeze(obj);
    } catch(error) {
        // Will just skip the current object
    }


    _.forOwn(obj, function (value) {
        if (!_.isObjectLike(value) || Object.isFrozen(value)) {
            return;
        }

        deepFreeze(value);
    });
}


export default {
    deepFreeze: deepFreeze
};
