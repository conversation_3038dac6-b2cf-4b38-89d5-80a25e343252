// This module will set the minimum config required to start Merge for unit tests

// Mostly the default config, but uses a local unauthenticated memory based mongodb instance
const defaultConfig = {
    "ver": "1.0.0.0",
    "appName": "Merge",
    "wikiBaseURL": "https://confluence.tools.telstra.com/display/MERGE/",
    "dateTimeFormat": "DD/MM/YYYY HH:mm:ss",
    "cookieAgeHours": 72,
    "userDNBase" : "OU=People,OU=eProfile,DC=core,DC=dir,DC=telstra,DC=com",
    "robotDNBase": "CN=Users,DC=core,DC=dir,DC=telstra,DC=com",
    "userSearchBase" : "OU=People,OU=eProfile,DC=core,DC=dir,DC=telstra,DC=com",
    "permittedSSU": ["NONSP","NSBU"],
    "sessionCookieSecret": "developerSessionCookieSecret",
    "apiSecret": "UnitTestSecretKey",
    "apiTokenExpiry": "1d",
    "apiTokenCookieExpiry": "12m",
    "apiEncrytionAlg": "HS256",
    "config_id": "localDev",
    "env": "Testing",
    "node_port": 3004,
    "dbHost": "localhost:27018",
    "dbName": "mergeTest",
    "userConfig": ["tests/usersTest.json", "tests/usersTestApiServiceCheck.json", "tests/usersTestAllAccess.json", "tests/usersTestBusinessUnits.json"],
    "disableRuleSourceEditing": false,
    "enableAuthKeyCron": false,
    "enableCallbackAPIs": true,
    "proxyHost": "proxy.host",
    "proxyPort": 8080,
    "hangarDb": {
        "hostname": "localhost",
        "database": "enterprise",
        "databaseTableUsage": "AAA_usage",
        "username": "",
        "password": ""
    },
    "MERGE": {
        "baseURI": ""
    },
    "ServiceHealth": {
        "serviceCheckURI":"https://slot1.org006.t-dev.telstra.net/v2/nbnco/service-health/serviceHealth",
        "baseURI":"https://slot1.org006.t-dev.telstra.net",
        "cert": "./massl/slot1.org006.t-dev.net-cert",
        "key":"./massl/slot1.org006.t-dev.net-key"
    },
    "COMMPILOT": {
        "apiToken": "",
        "diagURI": ""
    },
    "APIUriConfig": {
        "MergeExternalCollectorAPI": {
            "baseURI": "http://localhost:3005/merge-external-collector"
        }
    },
    "APIHeaderConfig": {
        "mergeExternalCollectorUsername": "",
        "mergeExternalCollectorPassword": ""
    }
};

export default {
    default: defaultConfig
};
