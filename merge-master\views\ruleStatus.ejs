<%- include('header', {}); %>
<%- include('menu', {currentTab: 'Home'}); %>
<link rel="stylesheet" type="text/css" href="/public/stylesheets/jsgrid.min.css">
<link rel="stylesheet" type="text/css" href="/public/stylesheets/jsgrid-theme.min.css">
<link rel="stylesheet" type="text/css" href="/public/stylesheets/select2.min.css">
<script type="text/javascript" src="/public/javascripts/jsgrid.min.js"></script>
<script src="/public/javascripts/select2.min.js"></script>
<style>
    .jsgrid-cell {
        word-wrap: break-word;
    }
</style>

<div class="d-flex flex-grow-1 flex-column" style="padding:1rem;">
    <div class="d-inline">
        <br>
        <div class="jumbotron py-1 px-4 text-white rounded bg-secondary">
            <h3 class="display-6 font-weight-normal"><%= title %></h3>
        </div>

        <div class="row">
            <div class="col-sm-8">
                <p id="displayTotal"></p>
            </div>
            <div class="col-sm-4">
                <div class="float-right">
                    <label class="col-form-label" for="lastQuantity">Last</label>
                    <input id="lastQuantity" type="number" style="width: 4em" value=1 min=1>
                    <select id="lastUnit" style="width: 6em">
                        <option value="h" selected>hours</option>
                        <option value="d">days</option>
                    </select>
                    <button id="statusRefresh" class="btn btn-primary" title="Refresh"><span class="fas fa-sync"></span></button>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4"><p id="displayTime"></p></div>
            <div class="col-sm-4"></div>
            <div class="col-sm-4">
                <div class="float-right">
                    <label class="col-form-label" for="showSelect">Show: </label>
                    <select id="showSelect" style="width: 16em">
                        <option value="all" selected>all rules</option>
                        <option value="error">rules with errors</option>
                        <option value="ok">rules without errors</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="alert alert-warning alert-dismissible" role="alert" id="alertNoResults" style="display:none;">
            No results found, this may be due to no service checks being completed in the specified time period.
            <button type="button" class="close" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="alert alert-danger alert-dismissible" role="alert" id="alertErrorResults" style="display:none;">
            An error occured whilst obtaining rule status data.
            <button type="button" class="close" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    </div>
    <div id="statusGrid" class="flex-grow-1"></div>
</div>

<script>
    $(document).ready(function() {
        let ruleElementIdNumber = 0;
        let recordElementIdNumber = 0;

        $("#statusRefresh").click(function() {
            setButtonSpinner($("#statusRefresh"), $("<span>").addClass("fas fa-sync"), true);
            $("#statusGrid").jsGrid("loadData");
        });

        $("#lastUnit").select2({
            minimumResultsForSearch: -1,
            width: "resolve"
        });

        $("#showSelect").select2({
            minimumResultsForSearch: -1,
            width: "resolve"
        });

        $("#showSelect").on("change", function() {
            $("#statusGrid").jsGrid("loadData");
        });

        $('.close').click(function(){
            $(this).parent().hide();
        });

        $("#statusGrid").jsGrid({
            width: "100%",
            height: "100%",
            filtering: true,
            inserting: false,
            editing: false,
            sorting: true,
            autoload: true,
            paging: true,
            pageLoading: true,
            pageSize: 50,
            pageIndex: 1,
            editButton: false,
            deleteButton: false,
            controller: {
                loadData: function(filter) {
                    var data = $.Deferred();
                    ruleElementIdNumber = 0;
                    recordElementIdNumber = 0;

                    let queryData = setupQueryDataFromFilter(filter);
                    $.ajax({
                        cache: false,
                        type: "GET",
                        url: "/serviceCheck/ruleStatus/error",
                        data: queryData,
                        success: function (response) {
                            if ($("#showSelect").val() == "all" && response.metadata.pagination.total == 0) {
                                $("#alertNoResults").show();
                            } else {
                                $("#alertNoResults").hide();
                                $("#alertErrorResults").hide();
                            }

                            data.resolve({
                                data: response.results,
                                itemsCount: response.metadata.pagination.total
                            });

                            $("#displayTotal").text(response.metadata.pagination.total + " results total");

                            if (response.metadata.startDate) {
                                let startDate = new Date(response.metadata.startDate).toLocaleString('en-GB');
                                $("#displayTime").text("Including service check records since: " + startDate);
                            } else {
                                $("#displayTime").text("");
                            }
                        },
                        error: function (xhr, status, e) {
                            $("#alertErrorResults").show();
                            data.resolve({
                                data: [],
                                itemsCount: 0
                            });
                        },
                        complete: function(_) {
                            setButtonSpinner($("#statusRefresh"), $("<span>").addClass("fas fa-sync"), false);
                        }
                    });

                    return data.promise();
                }
            },
            fields: [
                { name: "statusIcon", title: "", width: "1em", type: "text", sorting: false, filtering: false, itemTemplate: function(value, item) {
                    if (item.errorCount > 0) {
                        return "<span style='color:red' class='fas fa-times-circle' title='Error'/>";
                    } else {
                        return "<span style='color:green' class='fas fa-check-circle' title='OK'/>";
                    }
                }},
                { name: "rule", title: "Rule Name", type: "text" },
                { name: "count", title: "Total Count", width: "4em", type: "number", filtering: false },
                { name: "errorCount", title: "Error Count", width: "4em", type: "number", filtering: false },
                { name: "stats", title: "Error Statistics", type: "text", sorting: false, width: "12em", filtering: false, itemTemplate: function(value, item) {
                    if (value.length == 0) {
                        return "None";
                    } else {
                        let errorTypeList = value.map(function(stat) {
                            let recordElementId = `record_errors_${recordElementIdNumber}`;
                            recordElementIdNumber++;

                            let recordList = stat.records.map(function(record) {
                                return $("<a>").attr("href", `/serviceCheck/view/html/${record}`).attr("target", "blank").text(record).prop("outerHTML");
                            }).join("<br>");

                            let records = (stat.records.length == 1) ? "record" : "records";
                            return $("<a>").text(`${stat.error}: ${stat.records.length} ${records} `).
                                append($("<a>").attr("href", "#").attr("data-toggle", "collapse").attr("data-target", `#${recordElementId}`).text("view")).
                                append($("<div>").attr("class", "collapse").attr("id", recordElementId).html(recordList)).prop("outerHTML");
                        }).join("<br><hr>");

                        let ruleElementId = `rule_errors_${ruleElementIdNumber}`;
                        ruleElementIdNumber++;
                        let errorTypes = (value.length == 1) ? "error type" : "error types";

                        return $("<a>").text(`${value.length} ${errorTypes} `).
                            append($("<a>").attr("href", "#").attr("data-toggle", "collapse").attr("data-target", `#${ruleElementId}`).text("view")).
                            append($("<div>").attr("class", "collapse").attr("id", ruleElementId).html(errorTypeList)).prop("outerHTML");
                    }
                }}
            ]
        });
    });

    function setupQueryDataFromFilter(filter) {
        let limit = filter.pageSize;
        let offset = (filter.pageIndex - 1)*filter.pageSize;
        let lastQuantity = $("#lastQuantity").val();
        let lastUnit = $("#lastUnit").val();
        let showSelect = $("#showSelect").val();

        let queryData = {};

        queryData.limit = limit;
        queryData.offset = offset;

        if (filter.sortField) {
            queryData.sort = filter.sortField;
            queryData.order = filter.sortOrder;
        }

        if (lastQuantity && lastUnit) {
            switch (lastUnit) {
                case "h":
                    break;
                case "d":
                    lastQuantity *= 24;
                    break;
            }
            queryData.last = lastQuantity;
        }

        let filterFields = [
            "rule"
        ];

        switch (showSelect) {
            case "error":
                queryData.includeError = true;
                break;
            case "ok":
                queryData.includeError = false;
                break;
        }

        filterFields.forEach(field => {
            if (filter[field] != undefined && filter[field] != "") {
                queryData[field] = {
                    like: filter[field]
                };
            }
        });

        return queryData;
    }
</script>

<%- include('footer', {}); %>
