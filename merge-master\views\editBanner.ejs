<%- include('header', {}); %>
<%- include('menu', {currentTab: 'Home'}); %>
<div class="container">
    <br>
    <div class="border border-secondary container rounded" style="height:80vh;overflow-y:auto;padding-bottom:16px;">
        <br>
        <h1 class="display-4">Edit Banner Message</h1>
        <div class="row">
            <p class="d-inline">Updated by: <span id="updatedBy" class="badge badge-primary"></span></p>
        </div>
        <div class="row">
            <p class="d-inline">Updated on: </p><p class="text-muted d-inline" id="updatedOn"></p>
        </div>
        <br>

        <div class="row">
            <div class="col-12">
                <label for="bannerMessageInput">Banner message</label>
                <small class="form-text">The banner message may contain newline characters, this will not be rendered in the banner itself but it will affect the message in Microsoft Teams.</small>
                <textarea class="form-control" id="bannerMessageInput" maxlength="500"></textarea>
            </div>
        </div>
        <small class="text-muted" id="bannerMessageCharCount"></small><small class="text-muted"> characters remaining</small>
        <br>
        <button class="btn btn-primary" title="Update Banner" id="updateBanner">Update banner</button>
        <button class="btn btn-secondary" title="Clear Banner" id="clearBanner">Clear banner</button>
        <br>
        <div id="bannerMessageError" style="display:none" class="alert alert-danger"></div>
    </div>
</div>
<script>

    $(document).ready(function() {
        const characterLimit = 500;

        $.ajax({
            type: "GET",
            url: "/bannerMessage",
            contentType: "application/json",
            timeout: 10000,
            success: function (bannerMessage) {
                if (Object.keys(bannerMessage).length > 0) {
                    $("#bannerMessageInput").val(bannerMessage.message);
                    $("#bannerMessageCharCount").text(`${characterLimit - $("#bannerMessageInput").val().length}/${characterLimit}`);
                    $("#updatedBy").text(bannerMessage.updatedBy);
                    $("#updatedOn").text(new Date(bannerMessage.updatedOn).toLocaleString("en-GB"));
                } else {
                    $("#updatedBy").text("unknown");
                    $("#updatedOn").text("never");
                }
            },
            error: function(err) {
                alert("Error getting banner message: " + err.statusText);
            }
        });

        $("#bannerMessageCharCount").text(`${characterLimit - $("#bannerMessageInput").val().length}/${characterLimit}`);

        $("#bannerMessageInput").bind('input propertychange', function() {
            $("#bannerMessageCharCount").text(`${characterLimit - $("#bannerMessageInput").val().length}/${characterLimit}`);
        });

        $("#updateBanner").click(function() {
            setButtonSpinner($("#updateBanner"), "Update banner", true);
            sendUpdateBanner($("#bannerMessageInput").val());
        });

        $("#clearBanner").click(function() {
            setButtonSpinner($("#clearBanner"), "Clear banner", true);
            sendUpdateBanner("");
            $("#bannerMessageInput").val("");
        });
    });

    function sendUpdateBanner(message) {
        $.ajax({
            type: "PUT",
            url: "/bannerMessage",
            contentType: "application/json",
            timeout: 10000,
            data: JSON.stringify({
                message: message
            }),
            dataType: "json",
            success: function() {
                location.reload();
            },
            error: function(err) {
                $("#bannerMessageError").text(err && err.responseJSON && err.responseJSON.error ? err.responseJSON.error : err.status + ": " + err.statusText);
                $("#bannerMessageError").show();
            },
            complete: function() {
                setButtonSpinner($("#updateBanner"), "Update banner", false);
                setButtonSpinner($("#clearBanner"), "Clear banner", false);
            }
        });
    }
</script>
<%- include('footer', {}); %>
