'use strict';

import mongoose from 'mongoose';
import chai from 'chai';
import chaiHttp from 'chai-http';
import chaiLike from 'chai-like';
import chaiDateTime from 'chai-datetime';
import sinon from 'sinon';
import superagentDefaults from 'superagent-defaults';
import supertest from 'supertest';
import cookie from 'cookie';
import _ from 'lodash';
import assert from 'assert';

var app;
var request;
import Source from '../../db/model/source.js';
import Api from '../../db/model/api.js';
import config from '../config.js';
import helpers from '../helpers.js';
import { UNIT_TEST_ALL_ACCESS_USERNAME, UNIT_TEST_USERS } from '../users.js';

const should = chai.should();

chaiLike.extend(helpers.dateFieldCheck);
chaiLike.extend(helpers.regexCheck);
chai.use(chaiLike);
chai.use(chaiHttp);
chai.use(chaiDateTime);


const SOURCE_KEYS = Object.freeze(Object.keys(Source.schema.obj));


describe('Merge Sources REST endpoints', () => {
    // Setup and connect to mongodb memory server, start merge app instance
    // Set request variable with a configured supertest agent with test JWT and app instance
    before(async function() {
        global.gConfig = _.cloneDeep(config.default);
        app = (await import('../../app.js')).default;
        request = superagentDefaults(supertest(app));
    });

    // Drop all contents of database before each test
    beforeEach(async function() {
        global.gConfig = _.cloneDeep(config.default);

        // Uses deleteMany instead of mongoose.connection.db.dropDatabase() because
        // custom indexes appear to be deleted if dropDatabase() is used
        for (let collectionName in mongoose.connection.collections) {
            await mongoose.connection.collections[collectionName].deleteMany({});
        }

        // Creates APIs to reference from test sources
        // TODO: Currently the Source model does not reference the APIs model
        // should be implemented later
        let promises = [];

        promises.push(new Api({
            name: 'APIName'
        }));
        promises.push(new Api({
            name: 'APIName1'
        }));
        promises.push(new Api({
            name: 'APIName2'
        }));
        promises.push(new Api({
            name: 'APIName3'
        }));

        await Promise.all(promises);

        await helpers.authenticateSession(request, UNIT_TEST_ALL_ACCESS_USERNAME);
    });

    afterEach(async function() {
        sinon.restore();
    });

    describe('Read list of sources', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/sources');

            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/sources');

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Get empty list of sources', (done) => {
            request.get('/sources')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(200);

                chai.expect(res.body).to.eql({
                    metadata: {
                        pagination: {
                            limit: 100,
                            offset: 0,
                            total: 0,
                            prev: null,
                            next: null
                        }
                    },
                    results: []
                });

                done();
            });
        });

        it('Get list of sources with one source (default values)', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName',
                api: 'APIName'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName',
                        api: 'APIName'
                    }]);

                    res.body.results.forEach((source) => {
                        chai.expect(source).to.have.all.keys(SOURCE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get list of sources with three sources (default values)', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName1'
            }).save());

            promises.push(new Source({
                name: 'SourceName2',
                api: 'APIName2'
            }).save());

            promises.push(new Source({
                name: 'SourceName3',
                api: 'APIName3'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName1',
                        api: 'APIName1'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName2',
                        api: 'APIName2'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName3',
                        api: 'APIName3'
                    }]);

                    res.body.results.forEach((source) => {
                        chai.expect(source).to.have.all.keys(SOURCE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get list of sources with one source', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                active: true,
                hideInResult: true,
                includedInitialServiceCheck: false,
                title: 'Source Title',
                level: 0,
                description: 'Source Description',
                wikiPage: 'WikiPage Link Here',
                suite: ['ipvoice', 'wholesale'],
                param: 'paramval',
                preSources: [
                    'AnotherSource'
                ],
                preSourcesRunOnFail: false,
                preRules: [
                    'MDR001'
                ],
                preRulesRunOnFail: true,
                runWhenDependenciesResolved: false,
                preCondition: 's.SourceName',
                overrideErrorCondition: 'response && response.status == 422',
                errorMessage: 's.SourceName.errorMessage',
                createdBy: 'testuser',
                name: 'SourceName',
                api: 'APIName'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: true,
                        hideInResult: true,
                        includedInitialServiceCheck: false,
                        title: 'Source Title',
                        level: 0,
                        description: 'Source Description',
                        wikiPage: 'WikiPage Link Here',
                        suite: ['ipvoice', 'wholesale'],
                        param: 'paramval',
                        preSources: [
                            'AnotherSource'
                        ],
                        preSourcesRunOnFail: false,
                        preRules: [
                            'MDR001'
                        ],
                        preRulesRunOnFail: true,
                        runWhenDependenciesResolved: false,
                        preCondition: 's.SourceName',
                        overrideErrorCondition: 'response && response.status == 422',
                        errorMessage: 's.SourceName.errorMessage',
                        createdOn: currDate,
                        createdBy: 'testuser',
                        name: 'SourceName',
                        api: 'APIName'
                    }]);

                    res.body.results.forEach((source) => {
                        chai.expect(source).to.have.all.keys(SOURCE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get list of sources with two sources', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                active: true,
                hideInResult: true,
                includedInitialServiceCheck: false,
                title: 'Source Title',
                level: 0,
                description: 'Source Description',
                wikiPage: 'WikiPage Link Here',
                suite: ['ipvoice', 'wholesale'],
                param: 'paramval',
                preSources: [
                    'AnotherSource'
                ],
                preSourcesRunOnFail: false,
                preRules: [
                    'MDR001'
                ],
                preRulesRunOnFail: true,
                runWhenDependenciesResolved: false,
                preCondition: 's.SourceName',
                overrideErrorCondition: 'response && response.status == 422',
                errorMessage: 's.SourceName.errorMessage',
                createdBy: 'testuser',
                name: 'SourceName',
                api: 'APIName1'
            }).save());

            promises.push(new Source({
                active: true,
                hideInResult: false,
                includedInitialServiceCheck: false,
                title: 'Source Title II',
                level: 5,
                description: 'This is a description of sorts',
                wikiPage: '',
                suite: ['ipvoice', 'wholesale'],
                param: '',
                preSources: [],
                preSourcesRunOnFail: true,
                preRules: [
                    'MDR001',
                    'MTR001',
                    'MTR002'
                ],
                preRulesRunOnFail: false,
                runWhenDependenciesResolved: false,
                preCondition: 's.AnotherSource.isValid',
                overrideErrorCondition: 'response && response.status == 400',
                errorMessage: 's.AnotherSource.errorMessage',
                createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                name: 'AnotherSource',
                api: 'APIName2'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: true,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: 'Source Title II',
                        level: 5,
                        description: 'This is a description of sorts',
                        wikiPage: '',
                        suite: ['ipvoice', 'wholesale'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: true,
                        preRules: [
                            'MDR001',
                            'MTR001',
                            'MTR002'
                        ],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: 's.AnotherSource.isValid',
                        overrideErrorCondition: 'response && response.status == 400',
                        errorMessage: 's.AnotherSource.errorMessage',
                        createdOn: currDate,
                        createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                        name: 'AnotherSource',
                        api: 'APIName2'
                    },
                    {
                        active: true,
                        hideInResult: true,
                        includedInitialServiceCheck: false,
                        title: 'Source Title',
                        level: 0,
                        description: 'Source Description',
                        wikiPage: 'WikiPage Link Here',
                        suite: ['ipvoice', 'wholesale'],
                        param: 'paramval',
                        preSources: [
                            'AnotherSource'
                        ],
                        preSourcesRunOnFail: false,
                        preRules: [
                            'MDR001'
                        ],
                        preRulesRunOnFail: true,
                        runWhenDependenciesResolved: false,
                        preCondition: 's.SourceName',
                        overrideErrorCondition: 'response && response.status == 422',
                        errorMessage: 's.SourceName.errorMessage',
                        createdOn: currDate,
                        createdBy: 'testuser',
                        name: 'SourceName',
                        api: 'APIName1'
                    }]);

                    res.body.results.forEach((source) => {
                        chai.expect(source).to.have.all.keys(SOURCE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get one source with limit, five sources existing', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'SourceName2',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'SourceName3',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'SourceName4',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'SourceName5',
                api: 'APIName'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources?limit=1')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName1',
                        api: 'APIName'
                    }]);

                    res.body.results.forEach((source) => {
                        chai.expect(source).to.have.all.keys(SOURCE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get one source with limit and offset, five sources existing', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'SourceName2',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'SourceName3',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'SourceName4',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'SourceName5',
                api: 'APIName'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources?limit=2&offset=2')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName3',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName4',
                        api: 'APIName'
                    }]);

                    res.body.results.forEach((source) => {
                        chai.expect(source).to.have.all.keys(SOURCE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get source list sorted by name ascending', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'SourceName2',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'SourceName3',
                api: 'APIName'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources?sort=name&order=asc')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName1',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName2',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName3',
                        api: 'APIName'
                    }]);

                    res.body.results.forEach((source) => {
                        chai.expect(source).to.have.all.keys(SOURCE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get source list sorted by name descending', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'SourceName2',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'SourceName3',
                api: 'APIName'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources?sort=name&order=desc')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName3',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName2',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName1',
                        api: 'APIName'
                    }]);

                    res.body.results.forEach((source) => {
                        chai.expect(source).to.have.all.keys(SOURCE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get source list sorted by name ascending case insensitive', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'sourceName1',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'SOURCENAME2',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'SoUrcENaMe3',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'sourcename4',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'sOURCENAMe5',
                api: 'APIName'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources?sort=name&order=asc')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'sourceName1',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SOURCENAME2',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SoUrcENaMe3',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'sourcename4',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'sOURCENAMe5',
                        api: 'APIName'
                    }]);

                    res.body.results.forEach((source) => {
                        chai.expect(source).to.have.all.keys(SOURCE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get source list sorted by name descending case insensitive', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'sourceName1',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'SOURCENAME2',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'SoUrcENaMe3',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'sourcename4',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'sOURCENAMe5',
                api: 'APIName'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources?sort=name&order=desc')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'sOURCENAMe5',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'sourcename4',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SoUrcENaMe3',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SOURCENAME2',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'sourceName1',
                        api: 'APIName'
                    }]);

                    res.body.results.forEach((source) => {
                        chai.expect(source).to.have.all.keys(SOURCE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get source list sorted by title ascending', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName',
                title: 'Alpha'
            }).save());

            promises.push(new Source({
                name: 'SourceName2',
                api: 'APIName',
                title: 'Charlie'
            }).save());

            promises.push(new Source({
                name: 'SourceName3',
                api: 'APIName',
                title: 'Bravo'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources?sort=title&order=asc')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: 'Alpha',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName1',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: 'Bravo',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName3',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: 'Charlie',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName2',
                        api: 'APIName'
                    }]);

                    res.body.results.forEach((source) => {
                        chai.expect(source).to.have.all.keys(SOURCE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get source list sorted by title descending', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName',
                title: 'Alpha'
            }).save());

            promises.push(new Source({
                name: 'SourceName2',
                api: 'APIName',
                title: 'Charlie'
            }).save());

            promises.push(new Source({
                name: 'SourceName3',
                api: 'APIName',
                title: 'Bravo'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources?sort=title&order=desc')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: 'Charlie',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName2',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: 'Bravo',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName3',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: 'Alpha',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName1',
                        api: 'APIName'
                    }]);

                    res.body.results.forEach((source) => {
                        chai.expect(source).to.have.all.keys(SOURCE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get source list sorted by title ascending case insensitive', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName',
                title: 'alpha'
            }).save());

            promises.push(new Source({
                name: 'SourceName2',
                api: 'APIName',
                title: 'cHARLIE'
            }).save());

            promises.push(new Source({
                name: 'SourceName3',
                api: 'APIName',
                title: 'Bravo'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources?sort=title&order=asc')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: 'alpha',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName1',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: 'Bravo',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName3',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: 'cHARLIE',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName2',
                        api: 'APIName'
                    }]);

                    res.body.results.forEach((source) => {
                        chai.expect(source).to.have.all.keys(SOURCE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get source list sorted by title descending case insensitive', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName',
                title: 'alpha'
            }).save());

            promises.push(new Source({
                name: 'SourceName2',
                api: 'APIName',
                title: 'cHARLIE'
            }).save());

            promises.push(new Source({
                name: 'SourceName3',
                api: 'APIName',
                title: 'Bravo'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources?sort=title&order=desc')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: 'cHARLIE',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName2',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: 'Bravo',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName3',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: 'alpha',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName1',
                        api: 'APIName'
                    }]);

                    res.body.results.forEach((source) => {
                        chai.expect(source).to.have.all.keys(SOURCE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get source list sorted by description ascending', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName',
                description: 'Alpha'
            }).save());

            promises.push(new Source({
                name: 'SourceName2',
                api: 'APIName',
                description: 'Charlie'
            }).save());

            promises.push(new Source({
                name: 'SourceName3',
                api: 'APIName',
                description: 'Bravo'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources?sort=description&order=asc')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: 'Alpha',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName1',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: 'Bravo',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName3',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: 'Charlie',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName2',
                        api: 'APIName'
                    }]);

                    res.body.results.forEach((source) => {
                        chai.expect(source).to.have.all.keys(SOURCE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get source list sorted by description descending', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName',
                description: 'Alpha'
            }).save());

            promises.push(new Source({
                name: 'SourceName2',
                api: 'APIName',
                description: 'Charlie'
            }).save());

            promises.push(new Source({
                name: 'SourceName3',
                api: 'APIName',
                description: 'Bravo'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources?sort=description&order=desc')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: 'Charlie',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName2',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: 'Bravo',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName3',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: 'Alpha',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName1',
                        api: 'APIName'
                    }]);

                    res.body.results.forEach((source) => {
                        chai.expect(source).to.have.all.keys(SOURCE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get source list sorted by description ascending case insensitive', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName',
                description: 'Aaaaa'
            }).save());

            promises.push(new Source({
                name: 'SourceName2',
                api: 'APIName',
                description: 'aaaaa'
            }).save());

            promises.push(new Source({
                name: 'SourceName3',
                api: 'APIName',
                description: 'AAAAA'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources?sort=description&order=asc')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: 'aaaaa',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName2',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: 'Aaaaa',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName1',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: 'AAAAA',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName3',
                        api: 'APIName'
                    }]);

                    res.body.results.forEach((source) => {
                        chai.expect(source).to.have.all.keys(SOURCE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get source list sorted by description descending case insensitive', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName',
                description: 'Aaaaa'
            }).save());

            promises.push(new Source({
                name: 'SourceName2',
                api: 'APIName',
                description: 'aaaaa'
            }).save());

            promises.push(new Source({
                name: 'SourceName3',
                api: 'APIName',
                description: 'AAAAA'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources?sort=description&order=desc')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: 'AAAAA',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName3',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: 'Aaaaa',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName1',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: 'aaaaa',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName2',
                        api: 'APIName'
                    }]);

                    res.body.results.forEach((source) => {
                        chai.expect(source).to.have.all.keys(SOURCE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get source list filter by name equality 1', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'SourceName2',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'MergeCheck1',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'MergeCheck2',
                api: 'APIName'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources?name=MergeCheck1')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MergeCheck1',
                        api: 'APIName'
                    }]);

                    res.body.results.forEach((source) => {
                        chai.expect(source).to.have.all.keys(SOURCE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get source list filter by name equality 2', (done) => {
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'SourceName2',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'MergeCheck1',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'MergeCheck2',
                api: 'APIName'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources?name=Source')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([]);

                    done();
                });
            });
        });

        it('Get source list filter by name equality 3', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'SourceName2',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'MergeCheck1',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'MergeCheck2',
                api: 'APIName'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources?name[equal]=SourceName2')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName2',
                        api: 'APIName'
                    }]);

                    res.body.results.forEach((source) => {
                        chai.expect(source).to.have.all.keys(SOURCE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get source list filter by name equality 4', (done) => {
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'SourceName2',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'MergeCheck1',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'MergeCheck2',
                api: 'APIName'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources?name[equal]=Check')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([]);

                    done();
                });
            });
        });

        it('Get source list filter by name like 1', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'SourceName2',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'MergeCheck1',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'MergeCheck2',
                api: 'APIName'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources?name[like]=Merge')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MergeCheck1',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MergeCheck2',
                        api: 'APIName'
                    }]);

                    res.body.results.forEach((source) => {
                        chai.expect(source).to.have.all.keys(SOURCE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get source list filter by name like 2', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'SourceName2',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'MergeCheck1',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'MergeCheck2',
                api: 'APIName'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources?name[like]=Name')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName1',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName2',
                        api: 'APIName'
                    }]);

                    res.body.results.forEach((source) => {
                        chai.expect(source).to.have.all.keys(SOURCE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get source list filter by name like 3', (done) => {
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'SourceName2',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'MergeCheck1',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'MergeCheck2',
                api: 'APIName'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources?name[like]=aSource')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([]);

                    done();
                });
            });
        });

        it('Get source list filter by name like 4 (case insensitive)', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'SourceName2',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'MergeCheck1',
                api: 'APIName'
            }).save());

            promises.push(new Source({
                name: 'MergeCheck2',
                api: 'APIName'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources?name[like]=mErGecHeCk')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MergeCheck1',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MergeCheck2',
                        api: 'APIName'
                    }]);

                    done();
                });
            });
        });

        it('Get source list filter by title equality 1', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName',
                title: 'First source title'
            }).save());

            promises.push(new Source({
                name: 'SourceName2',
                api: 'APIName',
                title: 'Second source title'
            }).save());

            promises.push(new Source({
                name: 'MergeCheck1',
                api: 'APIName',
                title: 'Merge API for service checks'
            }).save());

            promises.push(new Source({
                name: 'MergeCheck2',
                api: 'APIName',
                title: 'Merge API for statistics'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources?title=Second%20source%20title')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: 'Second source title',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName2',
                        api: 'APIName'
                    }]);

                    res.body.results.forEach((source) => {
                        chai.expect(source).to.have.all.keys(SOURCE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get source list filter by title equality 1', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName',
                title: 'First source title'
            }).save());

            promises.push(new Source({
                name: 'SourceName2',
                api: 'APIName',
                title: 'Second source title'
            }).save());

            promises.push(new Source({
                name: 'MergeCheck1',
                api: 'APIName',
                title: 'Merge API for service checks'
            }).save());

            promises.push(new Source({
                name: 'MergeCheck2',
                api: 'APIName',
                title: 'Merge API for statistics'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources?title[equal]=Second%20source%20title')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: 'Second source title',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName2',
                        api: 'APIName'
                    }]);

                    res.body.results.forEach((source) => {
                        chai.expect(source).to.have.all.keys(SOURCE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get source list filter by title like 1', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName',
                title: 'First source title'
            }).save());

            promises.push(new Source({
                name: 'SourceName2',
                api: 'APIName',
                title: 'Second source title'
            }).save());

            promises.push(new Source({
                name: 'MergeCheck1',
                api: 'APIName',
                title: 'Merge API for service checks'
            }).save());

            promises.push(new Source({
                name: 'MergeCheck2',
                api: 'APIName',
                title: 'Merge API for statistics'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources?title[like]=API%20for')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: 'Merge API for service checks',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MergeCheck1',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: 'Merge API for statistics',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'MergeCheck2',
                        api: 'APIName'
                    }]);

                    res.body.results.forEach((source) => {
                        chai.expect(source).to.have.all.keys(SOURCE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get source list filter by title like 2 (case insensitive)', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName',
                title: 'First source title'
            }).save());

            promises.push(new Source({
                name: 'SourceName2',
                api: 'APIName',
                title: 'Second source title'
            }).save());

            promises.push(new Source({
                name: 'MergeCheck1',
                api: 'APIName',
                title: 'Merge API for service checks'
            }).save());

            promises.push(new Source({
                name: 'MergeCheck2',
                api: 'APIName',
                title: 'Merge API for statistics'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources?title[like]=sOurCE%20TITLE')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body.results).to.like([{
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: 'First source title',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName1',
                        api: 'APIName'
                    },
                    {
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: 'Second source title',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName2',
                        api: 'APIName'
                    }]);

                    res.body.results.forEach((source) => {
                        chai.expect(source).to.have.all.keys(SOURCE_KEYS);
                    });

                    done();
                });
            });
        });

        it('Get source list invalid limit (non-integer)', (done) => {
            request.get('/sources?limit=notalimit')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get source list invalid limit (integer equals 0)', (done) => {
            request.get('/sources?limit=0')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get source list invalid limit (negative integer)', (done) => {
            request.get('/sources?limit=-1')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get source list invalid offset (non-integer)', (done) => {
            request.get('/sources?offset=notanoffset')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get source list invalid offset (negative integer)', (done) => {
            request.get('/sources?offset=-1')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get source list invalid order parameter', (done) => {
            request.get('/sources?order=ascending')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get source list invalid name filter operator', (done) => {
            request.get('/sources?name[ophere]=test')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get source list empty title filter operator', (done) => {
            request.get('/sources?title[]=no-op')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);
                done();
            });
        });

        it('Get source list with HTTP GET with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            let res = await request.get('/sources');

            res.should.have.status(200);

            chai.expect(res.body).to.eql({
                metadata: {
                    pagination: {
                        limit: 100,
                        offset: 0,
                        total: 0,
                        prev: null,
                        next: null
                    }
                },
                results: []
            });
        });
    });

    describe('Read source', () => {
        it('Unauthenticated request', async() => {
            await new Source({
                name: 'SourceName1',
                api: 'APIName'
            }).save();

            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.get('/sources/SourceName1');

            res.should.have.status(401);
        });

        it('Authorization tests', async() => {
            await new Source({
                name: 'SourceName1',
                api: 'APIName'
            }).save();

            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.get('/sources/SourceName1');

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Get non-existant source', (done) => {
            request.get('/sources/SourceName')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(404);

                done();
            });
        });

        it('Get one existing source (default values)', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName',
                api: 'APIName'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources/SourceName')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body).to.like({
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName',
                        api: 'APIName'
                    });

                    chai.expect(res.body).to.have.all.keys(SOURCE_KEYS);

                    done();
                });
            });
        });

        it('Get one existing source', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                active: true,
                hideInResult: true,
                includedInitialServiceCheck: false,
                title: 'The Title of the Source',
                level: 2,
                description: 'Source Description',
                wikiPage: 'WikiPage Link Here',
                suite: ['standard', 'ipvoice'],
                param: 'paramval',
                preSources: [
                    'TestSourceName'
                ],
                preSourcesRunOnFail: false,
                preRules: [
                    'MTR001'
                ],
                preRulesRunOnFail: true,
                runWhenDependenciesResolved: false,
                preCondition: '',
                overrideErrorCondition: '',
                errorMessage: '',
                createdBy: 'usernamehere',
                name: 'SourceName',
                api: 'APIName'
            }).save());

            Promise.all(promises).then(() => {
                request.get('/sources/SourceName')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body).to.like({
                        active: true,
                        hideInResult: true,
                        includedInitialServiceCheck: false,
                        title: 'The Title of the Source',
                        level: 2,
                        description: 'Source Description',
                        wikiPage: 'WikiPage Link Here',
                        suite: ['standard', 'ipvoice'],
                        param: 'paramval',
                        preSources: [
                            'TestSourceName'
                        ],
                        preSourcesRunOnFail: false,
                        preRules: [
                            'MTR001'
                        ],
                        preRulesRunOnFail: true,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'usernamehere',
                        name: 'SourceName',
                        api: 'APIName'
                    });

                    chai.expect(res.body).to.have.all.keys(SOURCE_KEYS);

                    done();
                });
            });
        });

        it('Get existing source with HTTP GET with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            let currDate = new Date();

            await new Source({
                name: 'TestSource',
                api: 'APIName'
            }).save();

            let res = await request.get('/sources/TestSource');

            res.should.have.status(200);
            res.body.should.like({
                active: false,
                hideInResult: false,
                includedInitialServiceCheck: false,
                title: '',
                level: 0,
                description: '',
                wikiPage: '',
                suite: ['standard'],
                param: '',
                preSources: [],
                preSourcesRunOnFail: false,
                preRules: [],
                preRulesRunOnFail: false,
                runWhenDependenciesResolved: false,
                preCondition: '',
                overrideErrorCondition: '',
                errorMessage: '',
                createdOn: currDate,
                createdBy: 'unknown',
                name: 'TestSource',
                api: 'APIName'
            });
        });
    });

    describe('Create source', () => {
        it('Unauthenticated request', async() => {
            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.post('/sources').send({
                name: 'SourceName1',
                api: 'APIName'
            });

            res.should.have.status(401);
            chai.expect(await Source.find({ name: 'SourceName1' }).countDocuments()).to.eql(0);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.post('/sources').send({
                    name: 'SourceName1',
                    api: 'APIName'
                });

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(201);
                            chai.expect(await Source.find({ name: 'SourceName1' }).countDocuments()).to.eql(1);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                            chai.expect(await Source.find({ name: 'SourceName1' }).countDocuments()).to.eql(0);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }

                await Source.deleteMany({});
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Create source with HTTP POST (default values)', (done) => {
            let currDate = new Date();

            request.post('/sources')
            .send({
                name: 'SourceName',
                api: 'APIName'
            })
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(201);

                chai.expect(res.body).to.like({
                    active: false,
                    hideInResult: false,
                    includedInitialServiceCheck: false,
                    title: '',
                    level: 0,
                    description: '',
                    wikiPage: '',
                    suite: ['standard'],
                    param: '',
                    preSources: [],
                    preSourcesRunOnFail: false,
                    preRules: [],
                    preRulesRunOnFail: false,
                    runWhenDependenciesResolved: false,
                    preCondition: '',
                    overrideErrorCondition: '',
                    errorMessage: '',
                    createdOn: currDate,
                    createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                    name: 'SourceName',
                    api: 'APIName'
                });

                chai.expect(res.body).to.have.all.keys(SOURCE_KEYS);

                request.get('/sources/SourceName')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body).to.like({
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                        name: 'SourceName',
                        api: 'APIName'
                    });

                    chai.expect(res.body).to.have.all.keys(SOURCE_KEYS);

                    done();
                });
            });
        });

        it('Create source with HTTP POST test invalid level field', (done) => {
            request.post('/sources')
            .send({
                name: 'SourceName',
                level: 'not a valid integer'
            })
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(400);

                done();
            });
        });

        it('Create source with HTTP POST', (done) => {
            let currDate = new Date();

            request.post('/sources')
            .send({
                active: true,
                hideInResult: false,
                includedInitialServiceCheck: false,
                title: 'The Title of the Source',
                level: 2,
                description: 'Source Description',
                wikiPage: 'WikiPage Link Here',
                suite: ['standard', 'ipvoice'],
                param: 'paramval',
                preSources: [
                    'TestSourceName'
                ],
                preSourcesRunOnFail: false,
                preRules: [
                    'MTR001'
                ],
                preRulesRunOnFail: true,
                runWhenDependenciesResolved: false,
                preCondition: '',
                overrideErrorCondition: '',
                errorMessage: '',
                name: 'SourceName',
                api: 'APIName'
            })
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(201);

                chai.expect(res.body).to.like({
                    active: true,
                    hideInResult: false,
                    includedInitialServiceCheck: false,
                    title: 'The Title of the Source',
                    level: 2,
                    description: 'Source Description',
                    wikiPage: 'WikiPage Link Here',
                    suite: ['standard', 'ipvoice'],
                    param: 'paramval',
                    preSources: [
                        'TestSourceName'
                    ],
                    preSourcesRunOnFail: false,
                    preRules: [
                        'MTR001'
                    ],
                    preRulesRunOnFail: true,
                    runWhenDependenciesResolved: false,
                    preCondition: '',
                    overrideErrorCondition: '',
                    errorMessage: '',
                    createdOn: currDate,
                    createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                    name: 'SourceName',
                    api: 'APIName'
                });

                chai.expect(res.body).to.have.all.keys(SOURCE_KEYS);

                request.get('/sources/SourceName')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body).to.like({
                        active: true,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: 'The Title of the Source',
                        level: 2,
                        description: 'Source Description',
                        wikiPage: 'WikiPage Link Here',
                        suite: ['standard', 'ipvoice'],
                        param: 'paramval',
                        preSources: [
                            'TestSourceName'
                        ],
                        preSourcesRunOnFail: false,
                        preRules: [
                            'MTR001'
                        ],
                        preRulesRunOnFail: true,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                        name: 'SourceName',
                        api: 'APIName'
                    });

                    chai.expect(res.body).to.have.all.keys(SOURCE_KEYS);

                    done();
                });
            });
        });

        it('Create source with HTTP POST test duplicate suite', async() => {
            let res = await request.post('/sources').send({
                name: 'SourceName1',
                api: 'APIName',
                suite: [
                    'standard',
                    'standard'
                ]
            });

            res.should.have.status(400);

            chai.expect(await Source.find({ name: 'SourceName1' }).countDocuments()).to.eql(0);
        });

        it('Create source with HTTP POST test duplicate preSources', async() => {
            let res = await request.post('/sources').send({
                name: 'SourceName1',
                api: 'APIName',
                preSources: [
                    'SourceName',
                    'SourceName'
                ]
            });

            res.should.have.status(400);

            chai.expect(await Source.find({ name: 'SourceName1' }).countDocuments()).to.eql(0);
        });

        it('Create source with HTTP POST test duplicate preRules', async() => {
            let res = await request.post('/sources').send({
                name: 'SourceName1',
                api: 'APIName',
                preRules: [
                    'MTR001',
                    'MTR001'
                ]
            });

            res.should.have.status(400);

            chai.expect(await Source.find({ name: 'SourceName1' }).countDocuments()).to.eql(0);
        });

        it('Create duplicate source with HTTP POST', (done) => {
            request.post('/sources')
            .send({
                name: 'SourceName',
                api: 'APIName'
            })
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(201);

                request.post('/sources')
                .send({
                    name: 'SourceName',
                    api: 'APIName'
                })
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(409);
                    done();
                });
            });
        });

        it('Create source with HTTP POST test createdBy immutable', (done) => {
            let currDate = new Date();

            request.post('/sources')
            .send({
                name: 'SourceName',
                api: 'APIName',
                createdBy: 'anotheruser'
            })
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(201);

                chai.expect(res.body).to.like({
                    active: false,
                    hideInResult: false,
                    includedInitialServiceCheck: false,
                    title: '',
                    level: 0,
                    description: '',
                    wikiPage: '',
                    suite: ['standard'],
                    param: '',
                    preSources: [],
                    preSourcesRunOnFail: false,
                    preRules: [],
                    preRulesRunOnFail: false,
                    runWhenDependenciesResolved: false,
                    preCondition: '',
                    overrideErrorCondition: '',
                    errorMessage: '',
                    createdOn: currDate,
                    createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                    name: 'SourceName',
                    api: 'APIName'
                });

                chai.expect(res.body).to.have.all.keys(SOURCE_KEYS);

                done();
            });
        });

        it('Create source with HTTP POST test createdOn immutable', (done) => {
            let customDate = new Date('2020-12-15T16:00:00.000Z');
            let currDate = new Date();

            request.post('/sources')
            .send({
                name: 'SourceName',
                api: 'APIName',
                createdOn: customDate
            })
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(201);

                chai.expect(res.body).to.like({
                    active: false,
                    hideInResult: false,
                    includedInitialServiceCheck: false,
                    title: '',
                    level: 0,
                    description: '',
                    wikiPage: '',
                    suite: ['standard'],
                    param: '',
                    preSources: [],
                    preSourcesRunOnFail: false,
                    preRules: [],
                    preRulesRunOnFail: false,
                    runWhenDependenciesResolved: false,
                    preCondition: '',
                    overrideErrorCondition: '',
                    errorMessage: '',
                    createdOn: currDate,
                    createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                    name: 'SourceName',
                    api: 'APIName'
                });

                chai.expect(res.body).to.have.all.keys(SOURCE_KEYS);

                done();
            });
        });

        it('Create source with HTTP POST unrecognised fields', (done) => {
            let currDate = new Date();

            request.post('/sources')
            .send({
                name: 'SourceName',
                api: 'APIName',
                badFieldName: 'should not exist',
                yetAnotherField: 550
            })
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(201);

                chai.expect(res.body).to.like({
                    active: false,
                    hideInResult: false,
                    includedInitialServiceCheck: false,
                    title: '',
                    level: 0,
                    description: '',
                    wikiPage: '',
                    suite: ['standard'],
                    param: '',
                    preSources: [],
                    preSourcesRunOnFail: false,
                    preRules: [],
                    preRulesRunOnFail: false,
                    runWhenDependenciesResolved: false,
                    preCondition: '',
                    overrideErrorCondition: '',
                    errorMessage: '',
                    createdOn: currDate,
                    createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                    name: 'SourceName',
                    api: 'APIName'
                });

                chai.expect(res.body).to.have.all.keys(SOURCE_KEYS);
                chai.expect(res.body).to.not.have.any.keys('badFieldName', 'yetAnotherField');

                request.get('/sources/SourceName')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body).to.like({
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: UNIT_TEST_ALL_ACCESS_USERNAME,
                        name: 'SourceName',
                        api: 'APIName'
                    });

                    chai.expect(res.body).to.have.all.keys(SOURCE_KEYS);
                    chai.expect(res.body).to.not.have.any.keys('badFieldName', 'yetAnotherField');

                    done();
                });
            });
        });

        it('Create source with HTTP POST with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            let postRes = await request.post('/sources').send({
                name: 'TestSource',
                api: 'APIName'
            });

            postRes.should.have.status(405);

            let getRes = await request.get('/sources/TestSource');

            getRes.should.have.status(404);
        });

        it('Create source with HTTP POST with disableRuleSourceEditing enabled and admin user', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'admin');

            let postRes = await request.post('/sources').send({
                name: 'TestSource',
                api: 'APIName'
            });

            postRes.should.have.status(201);

            let getRes = await request.get('/sources/TestSource');

            getRes.should.have.status(200);
        });
    });

    describe('Update source', () => {
        it('Unauthenticated request', async() => {
            await new Source({
                name: 'SourceName1',
                api: 'APIName',
                active: false
            }).save();

            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.put('/sources/SourceName1').send({
                active: true
            });

            res.should.have.status(401);
            chai.expect((await Source.findOne({ name: 'SourceName1' })).active).to.eql(false);
        });

        it('Authorization tests', async() => {
            await new Source({
                name: 'SourceName1',
                api: 'APIName',
                active: false
            }).save();

            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                await helpers.authenticateSession(request, username);
                let res = await request.put('/sources/SourceName1').send({
                    active: true
                });

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                            chai.expect((await Source.findOne({ name: 'SourceName1' })).active).to.eql(true);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                            chai.expect((await Source.findOne({ name: 'SourceName1' })).active).to.eql(false);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
                await Source.updateOne({ name: 'SourceName1' }, { $set: { active: false }});
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Update source with HTTP PUT', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName',
                api: 'APIName'
            }).save());

            Promise.all(promises).then(() => {
                request.put('/sources/SourceName')
                .send({
                    active: false,
                    hideInResult: false,
                    includedInitialServiceCheck: false,
                    title: 'Source for Wholesale Testing',
                    level: 1,
                    description: 'Description here for source',
                    wikiPage: '',
                    suite: ['wholesale'],
                    param: 'param',
                    preSources: [
                        'MAGPIE'
                    ],
                    preSourcesRunOnFail: false,
                    preRules: [],
                    preRulesRunOnFail: false,
                    runWhenDependenciesResolved: false,
                    preCondition: '',
                    overrideErrorCondition: '',
                    errorMessage: 'There was an error with SourceName',
                    api: 'APIName1'
                }).end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body).to.like({
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: 'Source for Wholesale Testing',
                        level: 1,
                        description: 'Description here for source',
                        wikiPage: '',
                        suite: ['wholesale'],
                        param: 'param',
                        preSources: [
                            'MAGPIE'
                        ],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: 'There was an error with SourceName',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName',
                        api: 'APIName1'
                    });

                    chai.expect(res.body).to.have.all.keys(SOURCE_KEYS);

                    done();
                });
            });
        });

        it('Update source with HTTP PUT test non-existant source', (done) => {
            request.put('/sources/SourceName')
            .send({}).end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(404);
                done();
            });
        });

        it('Update source with HTTP PUT test invalid level field', (done) => {
            let promises = [];

            promises.push(new Source({
                name: 'SourceName',
                api: 'APIName'
            }).save());

            Promise.all(promises).then(() => {
                request.put('/sources/SourceName')
                .send({
                    level: 'not a number'
                }).end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(400);

                    done();
                });
            });
        });

        it('Update source with HTTP PUT test duplicate elements in suite', async() => {
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName'
            }).save());

            await Promise.all(promises);

            let res = await request.put('/sources/SourceName1').send({
                suite: [
                    'ipvoice',
                    'ipvoice'
                ]
            });

            res.should.have.status(400);
        });

        it('Update source with HTTP PUT test duplicate elements in preSources', async() => {
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName'
            }).save());

            await Promise.all(promises);

            let res = await request.put('/sources/SourceName1').send({
                preSources: [
                    'SourceName',
                    'SourceName'
                ]
            });

            res.should.have.status(400);
        });

        it('Update source with HTTP PUT test duplicate elements in preRules', async() => {
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName'
            }).save());

            await Promise.all(promises);

            let res = await request.put('/sources/SourceName1').send({
                preRules: [
                    'MTR001',
                    'MTR001'
                ]
            });

            res.should.have.status(400);
        });

        it('Update source with HTTP PUT test name immutable', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName1',
                api: 'APIName'
            }).save());

            Promise.all(promises).then(() => {
                request.put('/sources/SourceName1')
                .send({
                    name: 'SourceName2'
                }).end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    request.get('/sources/SourceName1')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }

                        res.should.have.status(200);

                        chai.expect(res.body).to.like({
                            active: false,
                            hideInResult: false,
                            includedInitialServiceCheck: false,
                            title: '',
                            level: 0,
                            description: '',
                            wikiPage: '',
                            suite: ['standard'],
                            param: '',
                            preSources: [],
                            preSourcesRunOnFail: false,
                            preRules: [],
                            preRulesRunOnFail: false,
                            runWhenDependenciesResolved: false,
                            preCondition: '',
                            overrideErrorCondition: '',
                            errorMessage: '',
                            createdOn: currDate,
                            createdBy: 'unknown',
                            name: 'SourceName1',
                            api: 'APIName'
                        });

                        chai.expect(res.body).to.have.all.keys(SOURCE_KEYS);

                        request.get('/sources/SourceName2')
                        .end((err, res) => {
                            if (err) {
                                chai.assert.fail('expected', 'actual', err);
                            }

                            res.should.have.status(404);

                            done();
                        });
                    });
                });
            });
        });

        it('Update source with HTTP PUT test createdBy immutable', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName',
                api: 'APIName',
                createdBy: 'testuser'
            }).save());

            Promise.all(promises).then(() => {
                request.put('/sources/SourceName')
                .send({
                    createdBy: 'anotheruser'
                }).end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body).to.like({
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'testuser',
                        name: 'SourceName',
                        api: 'APIName'
                    });

                    chai.expect(res.body).to.have.all.keys(SOURCE_KEYS);

                    done();
                });
            });
        });

        it('Update sources with HTTP PUT test createdBy immutable', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName',
                api: 'APIName',
                createdBy: 'testuser'
            }).save());

            Promise.all(promises).then(() => {
                request.put('/sources/SourceName')
                .send({
                    createdOn: '2010-01-15T16:00:00.000Z'
                }).end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body).to.like({
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'testuser',
                        name: 'SourceName',
                        api: 'APIName'
                    });

                    chai.expect(res.body).to.have.all.keys(SOURCE_KEYS);

                    done();
                });
            });
        });

        it('Update source with HTTP PUT unrecognised fields', (done) => {
            let currDate = new Date();
            let promises = [];

            promises.push(new Source({
                name: 'SourceName',
                api: 'APIName',
            }).save());

            Promise.all(promises).then(() => {
                request.put('/sources/SourceName')
                .send({
                    badField: 'badValue'
                }).end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    chai.expect(res.body).to.like({
                        active: false,
                        hideInResult: false,
                        includedInitialServiceCheck: false,
                        title: '',
                        level: 0,
                        description: '',
                        wikiPage: '',
                        suite: ['standard'],
                        param: '',
                        preSources: [],
                        preSourcesRunOnFail: false,
                        preRules: [],
                        preRulesRunOnFail: false,
                        runWhenDependenciesResolved: false,
                        preCondition: '',
                        overrideErrorCondition: '',
                        errorMessage: '',
                        createdOn: currDate,
                        createdBy: 'unknown',
                        name: 'SourceName',
                        api: 'APIName'
                    });

                    chai.expect(res.body).to.have.all.keys(SOURCE_KEYS);
                    chai.expect(res.body).to.not.have.any.keys('badField');

                    done();
                });
            });
        });

        it('Update source with HTTP POST with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            let currDate = new Date();

            await new Source({
                name: 'TestSource',
                api: 'APIName'
            }).save();

            let putRes = await request.put('/sources/TestSource').send({
                active: true
            });

            putRes.should.have.status(405);

            let getRes = await request.get('/sources/TestSource');

            getRes.should.have.status(200);
            getRes.body.should.like({
                active: false,
                hideInResult: false,
                includedInitialServiceCheck: false,
                title: '',
                level: 0,
                description: '',
                wikiPage: '',
                suite: ['standard'],
                param: '',
                preSources: [],
                preSourcesRunOnFail: false,
                preRules: [],
                preRulesRunOnFail: false,
                runWhenDependenciesResolved: false,
                preCondition: '',
                overrideErrorCondition: '',
                errorMessage: '',
                createdOn: currDate,
                createdBy: 'unknown',
                name: 'TestSource',
                api: 'APIName'
            });

            chai.expect(getRes.body).to.have.all.keys(SOURCE_KEYS);
        });

        it('Update source with HTTP POST with disableRuleSourceEditing enabled and admin user', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'admin');

            let currDate = new Date();

            await new Source({
                name: 'TestSource',
                api: 'APIName'
            }).save();

            let putRes = await request.put('/sources/TestSource').send({
                active: true
            });

            putRes.should.have.status(200);

            let getRes = await request.get('/sources/TestSource');

            getRes.should.have.status(200);
            getRes.body.should.like({
                active: true,
                hideInResult: false,
                includedInitialServiceCheck: false,
                title: '',
                level: 0,
                description: '',
                wikiPage: '',
                suite: ['standard'],
                param: '',
                preSources: [],
                preSourcesRunOnFail: false,
                preRules: [],
                preRulesRunOnFail: false,
                runWhenDependenciesResolved: false,
                preCondition: '',
                overrideErrorCondition: '',
                errorMessage: '',
                createdOn: currDate,
                createdBy: 'unknown',
                name: 'TestSource',
                api: 'APIName'
            });

            chai.expect(getRes.body).to.have.all.keys(SOURCE_KEYS);
        });
    });

    describe('Delete source', () => {
        it('Unauthenticated request', async() => {
            await new Source({
                name: 'SourceName1',
                api: 'APIName'
            }).save();

            request.set('Cookie', cookie.serialize('connect.sid', null));
            let res = await request.delete('/sources/SourceName1');

            res.should.have.status(401);
            chai.expect(await Source.find({ name: 'SourceName1' }).countDocuments()).to.eql(1);
        });

        it('Authorization tests', async() => {
            let usersWithInvalidAuthorization = [];

            for (let username of Object.keys(UNIT_TEST_USERS)) {
                if (await Source.find({ name: 'SourceName1' }).countDocuments() === 0) {
                    await new Source({
                        name: 'SourceName1',
                        api: 'APIName'
                    }).save();
                }

                await helpers.authenticateSession(request, username);
                let res = await request.delete('/sources/SourceName1');

                switch(username) {
                    case 'developer':
                    case 'admin':
                    case 'levelLead':
                        try {
                            res.should.have.status(200);
                            chai.expect(await Source.find({ name: 'SourceName1' }).countDocuments()).to.eql(0);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                    default:
                        try {
                            res.should.have.status(403);
                            chai.expect(await Source.find({ name: 'SourceName1' }).countDocuments()).to.eql(1);
                        } catch(error) {
                            usersWithInvalidAuthorization.push(username);
                        }
                        break;
                }
            }

            if (usersWithInvalidAuthorization.length) {
                assert.fail(`Invalid authorization result for users: ${usersWithInvalidAuthorization.join(',')}`);
            }
        });

        it('Delete source with HTTP DELETE', (done) => {
            let promises = [];

            promises.push(new Source({
                name: 'SourceName',
                api: 'APIName'
            }).save());

            Promise.all(promises).then(() => {
                request.delete('/sources/SourceName')
                .end((err, res) => {
                    if (err) {
                        chai.assert.fail('expected', 'actual', err);
                    }

                    res.should.have.status(200);

                    request.get('/sources/SourceName')
                    .end((err, res) => {
                        if (err) {
                            chai.assert.fail('expected', 'actual', err);
                        }

                        res.should.have.status(404);

                        done();
                    });
                });
            });
        });

        it('Delete source with HTTP DELETE test non-existant source', (done) => {
            request.delete('/sources/SourceName')
            .end((err, res) => {
                if (err) {
                    chai.assert.fail('expected', 'actual', err);
                }

                res.should.have.status(404);
                done();
            });
        });

        it('Delete source with HTTP DELETE with disableRuleSourceEditing enabled', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'developer');

            await new Source({
                name: 'TestSource',
                api: 'APIName'
            }).save();

            let delRes = await request.delete('/sources/TestSource');

            delRes.should.have.status(405);

            let getRes = await request.get('/sources/TestSource');

            getRes.should.have.status(200);
        });

        it('Delete source with HTTP DELETE with disableRuleSourceEditing enabled and admin user', async() => {
            global.gConfig.disableRuleSourceEditing = true;
            await helpers.authenticateSession(request, 'admin');

            await new Source({
                name: 'TestSource',
                api: 'APIName'
            }).save();

            let delRes = await request.delete('/sources/TestSource');

            delRes.should.have.status(200);

            let getRes = await request.get('/sources/TestSource');

            getRes.should.have.status(404);
        });
    });
});