'use strict';

const { connect, disconnect } = require('./config');

async function up() {
    await connect();

    const ServiceCheckModel = (await import('../db/model/serviceCheck.js')).default;

    await ServiceCheckModel.updateMany({
        startMethod: {
            $exists: false
        }
    }, {
        $set: {
          startMethod: null
        }
    }, { multi: true, strict: false });

    const GeneratedServiceCheckModel = (await import('../db/model/generatedServiceCheck.js')).default;

    await GeneratedServiceCheckModel.updateMany({
        startMethod: {
            $exists: false
        }
    }, {
        $set: {
          startMethod: null
        }
    }, { multi: true, strict: false });

    await disconnect();
}


async function down() {
    await connect();

    const ServiceCheckModel = (await import('../db/model/serviceCheck.js')).default;

    await ServiceCheckModel.updateMany({}, {
        $unset: {
            startMethod: 1
        }
    }, { multi: true, strict: false });

    const GeneratedServiceCheckModel = (await import('../db/model/generatedServiceCheck.js')).default;

    await GeneratedServiceCheckModel.updateMany({}, {
        $unset: {
            startMethod: 1
        }
    }, { multi: true, strict: false });

    await disconnect();
}


module.exports = { up, down };
