import React, {
    createContext,
    useContext,
    useEffect,
    useRef,
    useState,
} from 'react'
import { io, Socket } from 'socket.io-client'

// Define the shape of the context
interface SocketContextValue {
    socket: Socket | null // Allow null initially
    isConnected: boolean
    reconnectAttempts: number
}

// Create the SocketContext
const SocketContext = createContext<SocketContextValue | null>(null)

// Define the props for the SocketProvider
interface SocketProviderProps {
    children: React.ReactNode;
}

// Export the SocketProvider as a constant
export const SocketProvider = ({ children }: SocketProviderProps) => {
    const socketRef = useRef<Socket | null>(null) // Persist socket instance
    const [isConnected, setIsConnected] = useState(false)
    const [reconnectAttempts, setReconnectAttempts] = useState(0)

    useEffect(() => {
        console.log('[SocketContext] Mounted')

        // Create the socket instance
        const newSocket = io('/', {
            reconnectionAttempts: 5, // Limit reconnection attempts
            autoConnect: true, // Automatically try to reconnect
        })

        socketRef.current = newSocket // Persist the socket instance

        // Socket event listeners
        newSocket.on('connect', () => {
            setIsConnected(true)
            setReconnectAttempts(0) // Reset reconnect attempts on successful connection
            console.log('[SocketContext] Connected:', newSocket.id)
        })

        newSocket.on('disconnect', (reason) => {
            setIsConnected(false)
            console.log('[SocketContext] Disconnected:', reason)
        })

        newSocket.on('connect_error', (err) => {
            console.error('[SocketContext] Connection Error:', err)
        })

        newSocket.on('reconnect_attempt', () => {
            setReconnectAttempts((prev) => {
                const next = prev + 1
                console.log(`[SocketContext] Reconnect attempt #${next}`)
                return next
            })
        })

        // Cleanup on unmount
        return () => {
            console.log('[SocketContext] Unmounted')
            newSocket.disconnect() // Disconnect socket
            socketRef.current = null // Clear the persisted socket instance
        }
    }, [])

    return (
        <SocketContext.Provider
            value={{
                socket: socketRef.current, // Allow null socket initially
                isConnected,
                reconnectAttempts,
            }}
        >
            {children}
        </SocketContext.Provider>
    )
}

// Custom hook to use the SocketContext
export const useSocketContext = () => {
    const context = useContext(SocketContext)
    if (!context) {
        throw new Error('useSocketContext must be used within a SocketProvider')
    }
    return context
}
