import { Surface, TextStyle } from '@able/react'
import classNames from 'classnames' // For conditional class handling
import styles from './DetailsPanel.module.scss'

interface DetailsPanelProps {
    label: string
    panelElements?: React.ReactElement
    className?: string
    children: React.ReactElement
    fitContent?: boolean
}

export const DetailsPanel = ({
    label,
    panelElements,
    children,
    className,
    fitContent = true,
}: DetailsPanelProps) => {
    return (
        <Surface
            variant="SurfaceLow"
            background="materialBaseSecondary"
            className={classNames(styles.detailsPanel, className)}
        >
            <>
                <div className={styles.header}>
                    <TextStyle element="h2" colour="Default" alias="HeadingD">
                        {label}
                    </TextStyle>
                    <div className={styles.panelElements}>{panelElements}</div>
                </div>
                <div
                    className={classNames(styles.content, {
                        [styles.fitContent]: fitContent,
                    })}
                >
                    {children}
                </div>
            </>
        </Surface>
    )
}
