/* Merge Javascript Function for ServiceCheck Page*/
const RuleResult = Object.freeze({
    ok : 'OK',
    failed : 'Failed',
    error : 'Error',
    reject : 'Reject',
    warning: "Warning",
    actionable: "Actionable",
    actioned: "Actioned"
});

const displayNone = "style='display: none;'";
const contentTable = `<table class="table table-hover pad-rem25 ruleTable">
<thead>
    <tr>
        <th></th>
        <th>Category</th>
        <th>Result</th>
        <th>Details</th>
        <th class='wid-5-rem'>Rule ID</th>
        <th class='wid-6-rem'>Extra Info</th>
    </tr>
</thead>`;

const MagpieSchematicsSourceNames = Object.freeze([
    'MagpieSchematics',
    'MagpieMDNSchematics',
    'MagpieSchematicsCarriageFNN'
]);

let templateAllowedSystemsToAppend = {};
let feedbackState = {};


//Call when a new Rule executed to add its Msg
function ruleCheckMsg(SCid, rule, ruleData, isVisible=true, isCompleted=true, originServiceCheckId=null) {
    var tClass = 'text-info';
    var tStyleVisibility = isVisible ? '' : 'display: none;';
    var rIcone = ''; //Icone based on rule result
    var rInfo = ''; // information like value of extract
    var rExtra = ''; // Extra information for larger amount of value
    var rCategory = ''; // Root Cause Category in an info badge
    var rDescription = ''; //Rule description with i icon
    var rowGroup = 'append'; //to group rows in table based on result
    var isActionable = false;
    var isActioned = false;
    var valueMessage = '';

    console.log(`#> Received Rule "${rule.name}" for SCid "${SCid}" Result "${ruleData.result}" Msg "${ruleData.msg}"`);

    var rActionOutputModal = '';

    //Check toShow Msg or not
    if (rule.hideInResult) {
        console.log(`#> Hide Msg of Rule "${rule.name}"`);
        return false;
    }

    //Rule code
    var rCode = '';
    if (rule.ruleCode) {
        var ruleCode = rule.ruleCode.replace(/\"/g, "'");
        rCode = `<span style="color:grey" class="fas fa-code" title="${ruleCode}"></span>`;
    }

    // Get root cause category from rules data first, then from rule itself
    let rootCauseCategory = ruleData.rootCauseCategory ? ruleData.rootCauseCategory : rule.rootCauseCategory;

    //Root Cause Category
    if (rootCauseCategory) {
        rootCauseCategory = rootCauseCategory.replace('"', "'");
        let categoryColour = getCategoryColourFromCategory(rootCauseCategory);
        // input color uses hex natively but we want rgb to calculate text color dynamically
        let rgbColor = hexToRgb(categoryColour);

        // automatically change text color to white or black based on the background
        let categoryColourStyle = `--ccred: ${rgbColor.r}; --ccgreen: ${rgbColor.g}; --ccblue: ${rgbColor.b}; --threshold: 0.5; background-color: rgb(var(--ccred), var(--ccgreen), var(--ccblue)); --r: calc(var(--ccred) * 0.299); --g: calc(var(--ccgreen) * 0.587); --b: calc(var(--ccblue) * 0.114); --sum: calc(var(--r) + var(--g) + var(--b)); --perceived-lightness: calc(var(--sum) / 255); color: hsl(0, 0%, calc((var(--perceived-lightness) - var(--threshold)) * -10000000%)); }`;
        rCategory = `<span class="badge badge-info" style="${categoryColourStyle}"><span>${rootCauseCategory}</span></span>`;
    }
    //Description
    if (typeof rule.description === 'string' && rule.description) {
        rDescription = $("<div>").html($("<span>").attr("style", "color:blue").addClass("fas fa-info-circle").attr("title", rule.description)).html();
    }

    //Extra information
    var rExtraModal = '';
    if (typeof ruleData.extraInfo !== 'undefined' && ruleData.extraInfo != '' && ruleData.extraInfo != null) {
        rExtra = `<span style="color:lightblue" class="fas fa-envelope-open-text" title="Extra Information" onclick="$('#extra-${SCid}-${rule.name}').modal('show')"></span>`;
        //Extra info Modal
        rExtraModal = `
        <div class="modal fade" id="extra-${SCid}-${rule.name}" tabindex="-1" role="dialog" aria-labelledby="Title" aria-hidden="true">
            <div class="modal-dialog modal-xlg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="extra-title-${SCid}-${rule.name}">${rule.name} Extra Info</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body"><pre id="extra-info-text-${SCid}-${rule.name}"></pre></div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-sm btn-secondary" data-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-sm btn-primary" onclick="copyToClipboardModal('extra-info-text-${SCid}-${rule.name}');">Copy to Clipboard</button>
                    </div>
                </div>
            </div>
        </div>
       `;

       $(document.body).append(rExtraModal);
       $(`#extra-info-text-${SCid}-${rule.name}`).text(ruleData.extraInfo);
    }

    //Extra as button information
    if (typeof ruleData.extraInfo !== 'undefined' && ruleData.extraInfo != '' && ruleData.extraInfo != null) {
        //Extra as button info Modal
        var rBSIPMetricsModal = `
        <div class="modal fade" id="metrics-${rule.name.toLowerCase()}" tabindex="-1" role="dialog" aria-labelledby="Title" aria-hidden="true">
            <div class="modal-dialog modal-xlg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="metrics-title-${rule.name.toLowerCase()}">BSIP Portal Metrics</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body"><pre id="metrics-info-text-${rule.name.toLowerCase()}"></pre></div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-sm btn-secondary" data-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-sm btn-primary" onclick="copyToClipboardModal('metrics-info-text-${rule.name.toLowerCase()}');">Copy to Clipboard</button>
                    </div>
                </div>
            </div>
        </div>
        `;

        $(document.body).append(rBSIPMetricsModal);
        $(`#metrics-info-text-${rule.name.toLowerCase()}`).text(ruleData.extraInfo);
    }

    if (typeof ruleData.valueMsg === 'string') {
        // Converts any ISO formatted date string into locale time
        ruleData.valueMsg = ruleData.valueMsg.replaceAll(/\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+Z/g, function(match) {
            return new Date(match).toLocaleString("en-GB");
        });

        valueMessage = validateHtmlWithElement(ruleData.valueMsg);

        // Will log instances where the innerHTML of the temporary element is different to the original HTML given
        if (ruleData.valueMsg != valueMessage) {
            console.warn(`Value message for rule ${rule.name} may contain invalid HTML`);
            console.warn(`original value message:\n${ruleData.valueMsg}`);
            console.warn(`processed value message:\n${valueMessage}`);
        }
    }

    if (ruleData.result == RuleResult.ok) {
        tClass = 'rule-OK';
        rowGroup = 'append';
        rIcone = "<span style='color:green' class='fas fa-check-circle' title='OK'/>";
        if (valueMessage) {
            rInfo = `<span class='small text-muted'>${valueMessage}</span>`;
        }
    } else if (ruleData.result == RuleResult.warning) {
        tClass = 'rule-Warn';
        rowGroup = 'after';
        rIcone = "<span style='color:#FF9E00' class='fas fa-exclamation-triangle' title='Warning'/>";
        if (valueMessage) {
            rInfo = `<span class='small text-muted'>${valueMessage}</span>`;
        }
    } else if (ruleData.result == 'Failed') {
        tClass = 'rule-Failed';
        rowGroup = 'prepend';
        rIcone = "<span style='color:red' class='fas fa-exclamation-circle' title='Failed'/>";
        if (valueMessage) {
            let sourceDataLinkButtons = '';
            let sourceDataLinks = '';

            if (rule.preSources && rule.preSources.length > 0) {
                if (rule.preSources.length === 1) {
                    sourceDataLinkButtons = `<button class='btn btn-secondary btn-sm' \
                                             onclick='viewSource("${encodeURIComponent(SCid)}", "${encodeURIComponent(rule.preSources[0])}")' title="View source data: ${rule.preSources[0]}"><span class='fas fa-external-link-alt'></span></button>`;
                } else {
                    rule.preSources.forEach(sourceName => {
                        sourceDataLinks += `<a class='dropdown-item' onclick='viewSource("${encodeURIComponent(SCid)}", "${encodeURIComponent(sourceName)}")' href='#'>\
                                                ${sourceName}\
                                            </a>`;
                    });

                    sourceDataLinkButtons = `<div class='btn-group' role='group'>\
                                                <button class='btn btn-secondary btn-sm dropdown-toggle' data-toggle='dropdown' aria-haspopup='true' aria-expanded='false' title='View source data'><span class='fas fa-external-link-alt'></span></button>\
                                                <div class='dropdown-menu' aria-labelledby=''>\
                                                    ${sourceDataLinks}\
                                                </div>\
                                             </div>`;
                }
            }

            rInfo = `<div class="float-left"><span class='small text-muted'>${valueMessage}</span></div><div class="float-right">${sourceDataLinkButtons}</div>`;
        }
    } else if (ruleData.result == 'Reject') {
        tClass = 'rule-Rejected';
        rowGroup = 'prepend';
        rIcone = "<span style='color:orange' class='fas fa-times-circle' title='Rejected'/>";
        rInfo = '';
        rCode = `<span style="color:grey" class="fas fa-code" title="Pre-Conditon : \n${rule.preCondition}"></span>`;
    } else if (ruleData.result == 'Error') {
        tClass = 'rule-Error';
        rowGroup = 'prepend';
        rIcone = "<span style='color:red' class='fas fa-times-circle' title='Error'/>";
        rInfo = `<span class='small text-danger'>"${ruleData.status}: ${ruleData.error}"</span>`;
    } else if (ruleData.result == RuleResult.actioned || ruleData.result == RuleResult.actionable) {
        let viewActionOutputButton = '';
        let executeActionButton = '';

        if (ruleData.result == RuleResult.actioned) {
            tClass = 'rule-actioned';
            isActioned = true;
            rIcone = "<span style='color:green' class='fas fa-user-check' title='Action Executed'/>";
        } else if (ruleData.result == RuleResult.actionable) {
            tClass = 'rule-actionable';
            isActionable = true;
            rIcone = `<span style='color:orange' class='fas fa-user-cog' title='Action to be done'/>`;
        }

        // For rules that run within the development sandbox environment, don't show execute action / view output button
        if (SCid !== 'ruleSandbox') {
            executeActionButton = `<button class="btn btn-outline-success btn-sm runRule-${SCid}-${rule.name}" title="Execute Action" onclick="runRuleAction('${SCid}', '${rule.name}')"><span class="fas fa-play"></span></button>`;

            // For rules where the action has run, enable the view output button
            if (ruleData.action) {
                viewActionOutputButton = `<button class="btn btn-outline-success btn-sm action-view-${SCid}-${rule.name}" title="View Action Results" onclick="$('#action-output-${SCid}-${rule.name}').modal('show')"><span class="fas fa-clipboard-check"></span></button>`;
                actionCreatedOn = `Actioned On: ${new Date(ruleData.action.createdOn).toLocaleString('en-GB')}`;
                actionStatus = `Status Code: ${ruleData.action.status}`;
                actionOutput = JSON.stringify(ruleData.action.response, null, 4);
            } else {
                viewActionOutputButton = `<button class="btn btn-outline-success btn-sm action-view-${SCid}-${rule.name}" title="View Action Results" onclick="$('#action-output-${SCid}-${rule.name}').modal('show')" disabled><span class="fas fa-clipboard-check"></span></button>`;
            }
        }

        rActionOutputModal = `
        <div class="modal fade" id="action-output-${SCid}-${rule.name}" tabindex="-1" role="dialog" aria-labelledby="Title" aria-hidden="true">
            <div class="modal-dialog modal-xlg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="action-title-${SCid}-${rule.name}">${rule.name} Action Output</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body" id="action-result-${SCid}-${rule.name}">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-sm btn-secondary" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>`;

        rInfo = `<span class='small text-muted valueMsg-${SCid}-${rule.name}'>${valueMessage}</span><br><div class="action-${SCid}-${rule.name}">${executeActionButton}${viewActionOutputButton}</div>`;

        $(document.body).append(rActionOutputModal);
    }

    let summaryElement = $(`#rule-row-summary-${SCid}-${rule.name}`);
    if (summaryElement.length) {
        $(`#rule-row-summary-${SCid}-${rule.name}`).remove();
    }

    //Set WikiUrl from Rule and if empty to default Rule Name
    let ruleWikiURL = wikiBaseURL + (rule.wikiPage?rule.wikiPage:rule.name);
    let summaryRowHtml = $('<tr>').attr('id', `rule-row-summary-${SCid}-${rule.name}`).attr('class', `${tClass}`)
        .attr('style', `${tStyleVisibility}`)
        .append($('<td>').attr('class', `icon-${SCid}-${rule.name}`).html(rIcone))
        .append($('<td>').html(rCategory))
        .append($('<td>').attr('class', `msg-${SCid}-${rule.name}`).html($("<div>").text(ruleData.msg).html()))
        .append($('<td>').html(rInfo))
        .append($('<td>').html($('<div>').attr('class', `rule-${rule.name}`).html($('<a>').attr('target', rule.name).attr('href', ruleWikiURL).text(rule.name))))
        .append($('<td>').html(`${rDescription} ${rCode} ${rExtra}`));

    let ruleRowHtml = $('<tr>').attr('id', `rule-row-${SCid}-${rule.name}`).attr('class', `rule-row-${SCid} rule ${tClass}`)
        .attr('style', `${tStyleVisibility}`)
        .append($('<td>').attr('class', `icon-${SCid}-${rule.name}`).html(rIcone))
        .append($('<td>').html(rCategory))
        .append($('<td>').attr('class', `msg-${SCid}-${rule.name}`).html($("<div>").text(ruleData.msg).html()))
        .append($('<td>').html(rInfo))
        .append($('<td>').html($('<a>').attr('target', rule.name).attr('href', ruleWikiURL).text(rule.name)))
        .append($('<td>').html(`${rDescription} ${rCode} ${rExtra}`));

    //Print rule result
    if (!rule.hideInResult) {
        //Hide reject otherwise selected to be shown rejected
        if (ruleData.result !== RuleResult.reject || rule.showReject) {
            //Hide reject otherwise selected to be shown rejected
            //Failed and Warning show in Summary anyway
            let displayInSummary = false;

            if (['Failed','Warning'].indexOf(ruleData.result) > -1) {
                displayInSummary = rule.failedInSummary;
            } else {
                displayInSummary = rule.displayInSummary;
            }

            // Add rule to summary block if display in summary is set or failed in summary is set and the rule has failed
            if (displayInSummary) {
                addToSCBlock('Summary', SCid, summaryRowHtml, rowGroup, isCompleted, isActionable, isActioned);

                // Adds the rule to the summary block of the original service check ID as well
                // Developer note: This functionality CAN generate multiple html elements with the same ID
                // such as for "msg-${SCid}-${rule.name}" and "info-${SCid}-${rule.name}"
                // Since these IDs are only used in action rules, there is currently no workaround implemented for it
                // but it may be an issue in the future
                if (originServiceCheckId && rule.depedenciesFromRelatedServiceChecks) {
                    let summaryRowOriginCheckHtml = $('<tr>').attr('id', `rule-row-summary-${SCid}-${rule.name}-${originServiceCheckId}`).attr('class', `${tClass}`)
                        .attr('style', `${tStyleVisibility}`)
                        .append($('<td>').attr('class', `icon-${SCid}-${rule.name}`).html(rIcone))
                        .append($('<td>').html(rCategory))
                        .append($('<td>').attr('class', `msg-${SCid}-${rule.name}`).html($("<div>").text(ruleData.msg).html()))
                        .append($('<td>').html(rInfo))
                        .append($('<td>').html($('<div>').attr('class', `rule-${rule.name}`).html($('<a>').attr('target', rule.name).attr('href', ruleWikiURL).text(rule.name))))
                        .append($('<td>').html(`${rDescription} ${rCode} ${rExtra}`));

                    addToSCBlock('Summary', originServiceCheckId, summaryRowOriginCheckHtml, rowGroup, isCompleted, isActionable, isActioned);
                }
            }

            addToSCBlock(rootCauseCategory, SCid, ruleRowHtml, rowGroup, null, isActionable, isActioned);

            mergeShowLinkPopoverImages(`rule-${rule.name}`);

            if (ruleData.action) {
                let actionStatusCode = ruleData.action.status;
                let actionCreatedOn = ruleData.action.createdOn;
                let actionError = ruleData.action.error;
                let actionResponse = ruleData.action.response;
                let actionResultElement = $(`#action-result-${SCid}-${rule.name}`);

                actionResultElement.empty();
                actionResultElement.append(actionCreatedOn ? $('<div>').html($("<p>").text(`Created on: ${new Date(actionCreatedOn).toLocaleString('en-GB')}`)) : '')
                actionResultElement.append(actionStatusCode ? $('<div>').addClass('text-left text-monospace').html(`HTTP status code: <span class="badge badge-light">${actionStatusCode}</span><br>`) : '')
                actionResultElement.append(actionError ? $('<div>').addClass('alert alert-danger').text(actionError) : '');

                appendObjectTable(actionResultElement, actionResponse);
                appendObjectTextbox(actionResultElement, null, actionResponse);
            }
        }
    }
}


function viewSource(id, sourceName) {
    id = decodeURIComponent(id);
    sourceName = decodeURIComponent(sourceName);

    $(`#pills-sources-t-${id}`).on('shown.bs.tab', function(e) {
        $(`#pills-sources-t-${id}`).off('shown.bs.tab');

        $(`.source-expand-${id}`).filter(function() {
            return $(this).attr('data-source') == sourceName;
        }).each(function() {
            let dataTarget = $(this).attr('data-target');

            if (dataTarget && !$(dataTarget).hasClass('show')) {
                $(this).trigger('click');
            }

            let offsetTop = Math.max($(this).offset().top - 60, 0);

            $('html,body').animate({
                scrollTop: offsetTop
            });
        });
    });

    $(`#pills-sources-t-${id}`).tab("show");
}


function viewRule(id, ruleName, category) {
    let categoryID = getRuleIdFromCat(category);
    let ruleRow = $(`#rule-row-${id}-${ruleName}`);

    if (ruleRow.length) {
        ruleRow.addClass('table-success');

        // Highlights the rule and attempts to scroll to it
        $(`#pills-${categoryID}-t-${id}`).on('shown.bs.tab', function(e) {
            $(`#pills-${categoryID}-t-${id}`).off('shown.bs.tab');

            let offsetTop = Math.max(ruleRow.offset().top - 60, 0);
            $('html,body').animate({
                scrollTop: offsetTop
            });
        });

        // Opens tab containing the rule
        $(`#pills-${categoryID}-t-${id}`).tab("show");
    }
}


function runRuleAction(serviceCheckId, ruleName) {
    // Timeout is 60s as action itself may have a shorter timeout
    if (serviceCheckId) {
        $(`.runRule-${serviceCheckId}-${ruleName}`).prop('disabled', true);
        $(`.runRule-${serviceCheckId}-${ruleName}`).find('span').eq(0).removeClass('fas fa-play').addClass('fas fa-sync fa-spin');
        $.ajax({
            type: "POST",
            url: `/serviceCheck/${serviceCheckId}/action`,
            contentType: "application/json",
            timeout: 120000,
            data: JSON.stringify({
                rule: ruleName
            }),
            success: function (response) {
                updateActionRuleRow(serviceCheckId, ruleName, response);
            },
            error: function(err) {
                switch (err.status) {
                    case 422:
                        alert(err.responseJSON.message);
                        break;
                    default:
                        if (err.responseJSON && err.responseJSON.updatedRule && typeof err.responseJSON.updatedRule.error === 'string') {
                            // Updates rule row with rule code error details
                            updateActionRuleRow(serviceCheckId, ruleName, err.responseJSON);
                            alert(err.responseJSON.updatedRule.error);
                        } else {
                            alert("Error actioning rule: " + err.statusText);
                            $(`.icon-${serviceCheckId}-${ruleName}`).html($("<span>").attr("style", "color:red").attr("title", "Error").addClass("fas fa-times-circle"));
                            $(`.valueMsg-${serviceCheckId}-${ruleName}`).removeClass("text-muted").addClass("text-danger").text(`Unexpected error when actioning rule`);
                        }
                        break;
                }
            },
            complete: function() {
                $(`.runRule-${serviceCheckId}-${ruleName}`).find('span').eq(0).removeClass('fas fa-sync fa-spin').addClass('fas fa-play');
            }
        });
    }
}


function updateActionRuleRow(serviceCheckId, ruleName, response) {
    if (!(response && typeof response === "object")) {
        return;
    }

    if (response.updatedRule && typeof response.updatedRule.msg === "string") {
        $(`.msg-${serviceCheckId}-${ruleName}`).text(response.updatedRule.msg);
    }

    let valueMessage = '';
    if (typeof response.updatedRule.valueMsg === 'string') {
        // Converts any ISO formatted date string into locale time
        response.updatedRule.valueMsg = response.updatedRule.valueMsg.replaceAll(/\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+Z/g, function(match) {
            return new Date(match).toLocaleString("en-GB");
        });

        valueMessage = validateHtmlWithElement(response.updatedRule.valueMsg);

        // Will log instances where the innerHTML of the temporary element is different to the original HTML given
        if (response.updatedRule.valueMsg != valueMessage) {
            console.warn(`Value message for rule ${ruleName} may contain invalid HTML`);
            console.warn(`original value message:\n${response.updatedRule.valueMsg}`);
            console.warn(`processed value message:\n${valueMessage}`);
        }
    }

    switch (response.updatedRule && response.updatedRule.result) {
        case RuleResult.actionable:
            $(`.icon-${serviceCheckId}-${ruleName}`).html($("<span>").attr("style", "color:orange").attr("title", "Action to be done").addClass("fas fa-user-cog"));
            $(`.runRule-${serviceCheckId}-${ruleName}`).prop('disabled', false);
            $(`.valueMsg-${serviceCheckId}-${ruleName}`).html(valueMessage);
            break;
        case RuleResult.actioned:
            $(`.icon-${serviceCheckId}-${ruleName}`).html($("<span>").attr("style", "color:green").attr("title", "Action Executed").addClass("fas fa-user-check"));
            $(`.runRule-${serviceCheckId}-${ruleName}`).prop('disabled', true);
            $(`.valueMsg-${serviceCheckId}-${ruleName}`).html(valueMessage);
            break;
        case RuleResult.error:
            $(`.icon-${serviceCheckId}-${ruleName}`).html($("<span>").attr("style", "color:red").attr("title", "Error").addClass("fas fa-times-circle"));
            $(`.runRule-${serviceCheckId}-${ruleName}`).prop('disabled', true);
            $(`.valueMsg-${serviceCheckId}-${ruleName}`).removeClass("text-muted").addClass("text-danger").text(`${response.updatedRule.status}: ${response.updatedRule.error}`);
            break;
    }

    let actionResponse = response.response;
    if (actionResponse) {
        let actionCreatedOn = actionResponse.createdOn;
        let actionStatusCode = actionResponse.status;
        let actionError = actionResponse.error;
        let actionResponseData = actionResponse.response;
        let actionResultElement = $(`#action-result-${serviceCheckId}-${ruleName}`);

        actionResultElement.empty();
        actionResultElement.append(actionCreatedOn ? $('<div>').html($("<p>").text(`Created on: ${new Date(actionCreatedOn).toLocaleString('en-GB')}`)) : '');
        actionResultElement.append(actionStatusCode ? $('<div>').addClass('text-left text-monospace').html(`HTTP status code: <span class="badge badge-light">${actionStatusCode}</span><br>`) : '');
        actionResultElement.append(actionError ? $('<div>').addClass('alert alert-danger').text(actionError) : '');
        appendObjectTable(actionResultElement, actionResponseData);
        appendObjectTextbox(actionResultElement, null, actionResponseData);

        $(`.action-view-${serviceCheckId}-${ruleName}`).prop('disabled', false);
        $(`.action-view-${serviceCheckId}-${ruleName}`).click();
    }
}


//Update a Source Collect block with returned Data
function updateCollectedData(cd, hideSource, hidePreConditionFailedSource) {
    /* cd.category, cd.SCid, cd.duration, cd.data */
    categoryID = categoryToID(cd.category);
    console.log('#> updateCollectedData add Source "' + cd.name + '" for SCid "' + cd.SCid + '"');

    //Find Result Box or create it
    let existResultBox = $(`#result-${cd.SCid}-${categoryID}`);
    if (!existResultBox.length) {
        let preConditionFalse = cd.metadata && cd.metadata.status === 'Pre-Condition False';
        addCollectedData(cd.category, cd.name, cd.SCid, cd.hideInResult, preConditionFalse, hideSource, hidePreConditionFailedSource);
        existResultBox = $(`#result-${cd.SCid}-${categoryID}`);
    }

    existResultBox.empty();
    existResultBox.addClass("p-2");

    let status = cd.metadata && cd.metadata.status ? cd.metadata.status : 'Unknown';
    let collectionDuration = cd.metadata && cd.metadata.collectionDuration ? ` (in ${cd.metadata.collectionDuration}s)` : '';
    $(`#info-${cd.SCid}-${categoryID}`).text(`${status}${collectionDuration}`);


    // Shows parameters for the source if it is present
    if (cd.metadata && cd.metadata.parameters && typeof cd.metadata.parameters === 'object')  {
        let parametersTable = $("<table>").addClass("table table-sm").append(
            $("<thead>").html(
                $("<tr>").append($("<th>").attr("scope", "col").text("Parameter"))
                    .append($("<th>").attr("scope", "col").text("Value"))
                    .append($("<th>").attr("scope", "col").text("Type"))
            )
        );

        let parametersTableBody = $("<tbody>");

        for (let parameter in cd.metadata.parameters) {
            let parameterValue = cd.metadata.parameters[parameter];
            if (typeof parameterValue === 'object') {
                parametersTableBody.append($("<tr>").append($("<td>").text(parameter))
                    .append($("<td>").html($("<pre>").text(JSON.stringify(parameterValue, null, 4))))
                    .append($("<td>").text(typeof parameterValue))
                );
            } else {
                parametersTableBody.append($("<tr>").append($("<td>").text(parameter))
                    .append($("<td>").text(parameterValue))
                    .append($("<td>").text(typeof parameterValue))
                );
            }
        }

        parametersTable.append(parametersTableBody);

        let parametersExpandID = `parameters-${cd.SCid}-${categoryID}`;
        let parametersExpand = $("<div>")
            .append($("<a>").addClass("expand badge badge-info")
                .attr("title", "View source parameters")
                .attr("data-toggle", "collapse")
                .attr("data-target", `#${parametersExpandID}`).html("Source parameters <span style='color:blue' class='fas fa-plus-square'/>"))
            .append($("<div>").attr("id", parametersExpandID).addClass("collapse").append(parametersTable));

        existResultBox.append(parametersExpand);
        existResultBox.append("<hr>");
    }

    if (cd.metadata) {
        if (cd.metadata.statusCode) {
            existResultBox.append($('<div>').addClass('text-left text-monospace').html(`HTTP status code: <span class="badge badge-light">${cd.metadata.statusCode}</span><br>`));
        }

        // Sets sources tab colour if a source has an error
        if (cd.metadata.error) {
            $(`#pills-sources-t-${cd.SCid}`).css({
                'background-color': '#c9302c',
                color: '#fff',
                'border-color': '#c9302c'
            });

            $(`#pills-sources-t-${cd.SCid}`).find('span').eq(0).removeClass().addClass('fas fa-times-circle');
            existResultBox.append($('<div>').addClass('alert alert-danger').text(cd.metadata.error));
        }
    }

    if (cd.data) {
        existResultBox.append("<hr>");
        appendObjectTable(existResultBox, cd.data);
        appendObjectTextbox(existResultBox, `source-data-${cd.SCid}-${categoryID}`, cd.data);
    }
}

//convert string to acceptable HTML ID
function categoryToID(category) {
    if (!category) {
        console.log('Error: Source is empty!');
        return '';
    }
    return category.replace(/\s/g, '-');
}

//Init SC
//Submit of one Service check
function initNewServiceCheck(id, inFNN, rulesData=null, originID=null, originFNN=null) {
    let startTime = new Date();

    if (!originID) {
        $("#Results").prepend($("<div>").prop("id", `service-check-group-${id}`).addClass("p-2 mt-4 mb-4"));
        $(`#service-check-group-${id}`).append(SCResultsTemplate(id, inFNN));
    } else {
        $(`#service-check-group-${originID}`).addClass("border border-secondary rounded");
        $(`#service-check-group-${originID}`).append(generatedServiceCheckCard(id, originFNN));

        // border border-secondary rounded

        // Removes copy links, templates, restart service check icons from generated service checks
        $(`#pills-dicon-${id}`).addClass("d-none");
    }

    $(`#pills-details-${id}`).append(`<span id='startTime-${id}' class=''>${startTime.toLocaleTimeString('en-GB')}</span>  <span id='status-${id}' class=''>(Running...)</span>&nbsp;`)
    addSCBlock('Summary', id, null);
    $(`#nav-${id}`).find('.tab-nav').addClass('alterPad');

    $(`#expandAllSources-${id}`).click(function() {
        $(`.source-expand-${id}`).each(function() {
            let dataTarget = $(this).attr('data-target');

            if (dataTarget && !$(dataTarget).hasClass('show')) {
                $(this).click();
            }
        });
    });

    $(`#collapseAllSources-${id}`).click(function() {
        $(`.source-expand-${id}`).each(function() {
            let dataTarget = $(this).attr('data-target');

            if (dataTarget && $(dataTarget).hasClass('show')) {
                $(this).click();
            }
        });
    });

    $(`#search-rules-text-${id}`).keyup(function(e) {
        // Enter key
        if (e.keyCode === 13) {
            filterRulesByText(id, rulesData, $(`#search-rules-text-${id}`).val());
        }
    });

    $(`#search-rules-clear-${id}`).click(function() {
        $(`#search-rules-text-${id}`).val("");
        filterRulesByText(id, rulesData, $(`#search-rules-text-${id}`).val());
    });

    $(`#search-rules-${id}`).click(function() {
        filterRulesByText(id, rulesData, $(`#search-rules-text-${id}`).val());
    });
}


function filterRulesByText(id, rulesData, textFilter) {
    $(`.rule-row-${id}`).removeClass('table-success');
    $(`#rule-search-table-${id}`).empty();

    if (!textFilter || typeof textFilter !== "string") {
        $(`#pills-rule-search-t-link-${id}`).hide();
        let searchTabElementActive = $(`#pills-rule-search-${id}.active`);
        if (searchTabElementActive.length) {
            $(`#pills-summary-t-${id}`).trigger('click');
        }
        return;
    }

    $(`#pills-rule-search-t-link-${id}`).show();

    if (rulesData) {
        let filteredRules = {};

        for (let ruleName in rulesData) {
            let ruleData = rulesData[ruleName];
            let stringsFieldsFound = [];

            if (ruleData) {
                // Currently matches any of the below fields in rule data,
                // if it is a string, to the text filter (case sensitive)

                // Note: Result key refers to the value in the "Result" column, not the result field in rule data
                let ruleStringFields = {
                    "Rule Name": ruleName,
                    "Status": ruleData.result,
                    "Category": ruleData.rootCauseCategory,
                    "Result": ruleData.msg,
                    "Details": ruleData.valueMsg,
                    "Extra Info": ruleData.extraInfo
                };

                for (let fieldName in ruleStringFields) {
                    let ruleStringToSearch = ruleStringFields[fieldName];
                    // Case insensitive match
                    if (typeof ruleStringToSearch === "string" && ruleStringToSearch.toLowerCase().includes(textFilter.toLowerCase())) {
                        stringsFieldsFound.push(fieldName);
                    }
                }

                if (stringsFieldsFound.length) {
                    filteredRules[ruleName] = stringsFieldsFound;
                }
            }
        }

        for (let ruleName in filteredRules) {
            let stringsFieldsFound = filteredRules[ruleName];
            let searchDetails = $("<td>").text(`Found search string in fields: ${stringsFieldsFound.join(",")}`);

            let ruleLinkButton = $("<button>").addClass("btn btn-secondary btn-sm").attr("title", `View rule: ${ruleName}`).html($("<span>").addClass("fas fa-external-link-alt"))
            .click(function() {
                $(`.rule-row-${id}`).removeClass('table-success');
                viewRule(id, ruleName, rulesData[ruleName].rootCauseCategory);
            });
            let ruleLinks = $("<td>").html(ruleLinkButton);

            let ruleRow = $(`#rule-row-${id}-${ruleName}`);

            if (!ruleRow.length) {
                // Show message with option to view rule data
                let rulesDataId = `rule-data-search-${ruleName}`;

                searchDetails.append("<br>");
                searchDetails.append($("<span>").addClass('text-danger').html('Warning: Rule is not present in the tabs as the conditions to display it may not be met (hideInResult is true, or precondition is false and showReject is false) '));
                searchDetails.append($("<a>").attr("href", "#").attr("data-toggle", "collapse").attr("data-target", `#${rulesDataId}`).text("view"));
                searchDetails.append($("<div>").addClass("collapse").attr("id", rulesDataId).html($("<pre>").text(JSON.stringify(rulesData[ruleName], null, 4))));

                // Disables the select button
                ruleLinkButton.attr('title', 'Rule is not present in tabs and cannot be viewed there');
                ruleLinkButton.prop('disabled', true);
            }

            let searchResultRow = $("<tr>").append(
                $("<td>").text(ruleName)
            ).append(
                searchDetails
            ).append(
                ruleLinks
            );

            $(`#rule-search-table-${id}`).append(searchResultRow);
        }
    }

    $(`#pills-rule-search-t-${id}`).trigger('click');
}


function generatedServiceCheckCard(serviceCheckId, originFNN) {
    let cardHeader = $("<div>").addClass("card-header").append($("<span>").addClass("fa fa-info-circle mr-2").attr("style", "color:blue")).append(`This service check was automatically started by the service check for FNN: `).append($("<b>").text(originFNN));
    let cardBody = $("<div>").addClass("card-body").attr("id", `serviceCheckRecordBlock-${serviceCheckId}`).html(SCResultsTemplate(serviceCheckId, ""));

    return $("<div>").addClass("card mt-4 mb-4").append(cardHeader).append(cardBody);
}

//Add a Service Check Block
function addSCBlock(category, SCid, position='append') { //position can be prepend OR append
    if (!category) {
        return;
    }

    let categoryID = categoryToID(category);
    console.log(`#> Add Tab "${categoryID}" for "${SCid}" at position "${position}"`);

    if (category == 'Summary') {
        let summaryTableHTML = `<div id='result-${SCid}-${categoryID}'>
            <table class="table table-hover pad-rem25 ruleTable">
                <thead>
                    <tr>
                        <th></th>
                        <th>Category</th>
                        <th>Result</th>
                        <th>Details</th>
                        <th class='wid-5-rem'>Rule ID</th>
                        <th class='wid-6-rem'>Extra Info</th>
                    </tr>
                </thead>
                <tbody id="tableBody-${SCid}-summary"></tbody>
            </table>
        </div>`;
        $(`#pills-summary-${SCid}`).append(summaryTableHTML);
    }
}


//Add test to end of a SC block
function addToSCBlock(category, SCid, element, position=null, isCompleted=true, isActionable=false, isActioned=false) {
    let categoryID = getRuleIdFromCat(category);

    // if displayInSummary is set, ignore category
    if (categoryID == "summary") {
        let existResultBox = $(`#result-${SCid}-${categoryID}`);
        if (!existResultBox.length) {
            addSCBlock(categoryID, SCid);
            existResultBox = $(`#result-${SCid}-${categoryID}`);
        }
    }

    if (element.attr('id')) {
        if ($(`#${element.attr('id')}`).length > 0) {
            $(`#${element.attr('id')}`).remove();
            console.warn('removed dupe ' + element.attr('id'));
        }
    }

    //display tab only when rule exists
    //
    $(`#pills-${categoryID}-t-${SCid}`).parent().css({'display':'list-item'});

    // grouping error messages and positioning rows by error, warn and ok
    if (position == 'prepend') {
        $(`#tableBody-${SCid}-${categoryID}`).prepend(element);
    } else if (position == 'after' && $(`#tableBody-${SCid}-${categoryID} tr.rule-OK`).length > 0) {
        $(`#tableBody-${SCid}-${categoryID} tr.rule-OK:first`).before(element);
    } else {
        $(`#tableBody-${SCid}-${categoryID}`).append(element);
    }

    setBackgroundColourSCBlock(categoryID, SCid, isCompleted, isActionable, isActioned);
}


function setBackgroundColourSCBlock(categoryID, SCid, isCompleted, isActionable=null, isActioned=null) {
    let bgColor = null;

    if (categoryID == "summary" && isCompleted == false) {
        bgColor = ['#606060', 'fas fa-info-circle'];
    } else {
        if ($(`#tableBody-${SCid}-${categoryID} tr.rule-Error`).length > 0) {
            bgColor = ['#c9302c', 'fas fa-times-circle'];
        } else if ($(`#tableBody-${SCid}-${categoryID} tr.rule-Failed`).length > 0) {
            bgColor = ['#c9302c', 'fas fa-exclamation-circle'];
        } else if ($(`#tableBody-${SCid}-${categoryID} tr.rule-Rejected`).length > 0) {
            bgColor = ['#ffa500', 'fas fa-times-circle'];
        } else if ($(`#tableBody-${SCid}-${categoryID} tr.rule-Warn`).length > 0) {
            bgColor = ['#ffa500', 'fas fa-exclamation-triangle'];
        } else if ($(`#tableBody-${SCid}-${categoryID} tr.rule-OK`).length > 0) {
            bgColor = ['#28a745', 'fas fa-check-circle'];
        } else {
            bgColor = ['#28a745', 'fas fa-check-circle'];
        }
    }

    if (bgColor) {
        $(`#pills-${categoryID}-t-${SCid}`).css({
            'background-color': bgColor[0],
            color: '#fff',
            'border-color': bgColor[0]
        });
        $(`#pills-${categoryID}-t-${SCid}`).find('span').eq(0).removeClass().addClass(bgColor[1]);
    }

    if (isActionable) {
        if (!$(`#pills-${categoryID}-t-${SCid}`).find('span[class*="icon-actionable"]').length) {
            $(`#pills-${categoryID}-t-${SCid}`).append(`<span class="icon-actionable"><span style="color:orange" class="fas fa-user-cog circle-bg"></span></span>`);
        }
    }

    if (isActioned) {
        if (!$(`#pills-${categoryID}-t-${SCid}`).find('span[class*="icon-actioned"]').length) {
            $(`#pills-${categoryID}-t-${SCid}`).append(`<span class="icon-actioned"><span style="color:green" class="fas fa-user-cog circle-bg"></span></span>`);
        }
    }
}


function addCollectedData(category, name, SCid, hideInResult, isPreConditionFailed, hideSource, hidePreConditionFailedSource) {
    let categoryID = categoryToID(category);

    var DCResults = $(`#DCResults-${SCid}`);
    //Create FNN block if does not exist, for new page
    if (!DCResults.length) {
        console.log('!> Did not find SC, create it.');
        initNewServiceCheck(SCid, null);
    }

    // Adds class "sourceHideInResult" to the source div if should be hidden by default
    // Also hides source div by default if hideInResult is set for the source and the page's
    // setting has "Show Hidden Sources" unchecked
    let hideInResultClass = hideInResult ? "sourceHideInResult" : "";
    let preConditionFailedClass = isPreConditionFailed ? "sourcePreConditionFailed" : "";
    let sourceDisplay = (hideInResult && hideSource) || (isPreConditionFailed && hidePreConditionFailedSource) ? "none" : "block";

    console.log('#> addCollectedData Start of Source "' + name + '" for SCid "' + SCid + '"');
    DCResults.append($("<div>").addClass(`border border-secondary ${hideInResultClass} ${preConditionFailedClass}`).css("display", sourceDisplay)
        .append($("<span>").attr("id", `category-${SCid}-${categoryID}`).addClass("h4").text(category))
        .append(" ")
        .append($("<span>").attr("id", `info-${SCid}-${categoryID}`).addClass("").text("Running..."))
        .append($("<a>").addClass(`source-expand-${SCid} expand btn`).attr("data-source", name).attr("data-toggle", "collapse").attr("data-target", `#result-${SCid}-${categoryID}`)
            .html($("<span>").css("color", "blue").addClass("fas fa-plus-square").attr("title", "Expand/Collapse")))
        .append($("<div>").attr("id", `result-${SCid}-${categoryID}`).addClass("border rounded collapse"))
    );
}

// Clear Longitude and Latitude coordinates if input in AdborId
function clearCoordinates() {
    $('#latitude').val('');
    $('#longitude').val('');
}

// Clear AdborId if input in Longitude and Latitude coordinates
function clearAdborId() {
    $('#adborId').val('');
}

// Predict the Carriage Type and Suite of the entered FNN input
// based on reqular expression match
function predictCTypeAndSuite() {
    var myTextBox = document.getElementById('fnn');
    var myValue = myTextBox.value;
    if (myValue.length < 9)
    {

    }
    else {
      if (myValue.length == 9)
      {
        if (myValue.match(/^N[0-9]{7}R/i))
        {
          console.log('#> Predicted Suite N.R(9) -> Standard');
        }
        else if (myValue.match(/^N[0-9]{7}L/i))
        {
          console.log('#> Predicted Suite N.L(9) -> Outage');
        }
        else if (myValue.match(/^N[0-9]{7}Q/i))
        {
          console.log('#> Predicted Suite N.Q(9) -> Outage');
        }
      }
      else if (myValue.length == 10) {
        if (myValue.match(/^[0][0-9]{9}/i))
        {
            console.log('#> Predicted Suite STD(10) -> IPVoice');
        }
      }
      else if (myValue.length == 11) {
        if (myValue.match(/^61[0-9]{9}/i))
        {
            console.log('#> Predicted Suite ISD(11) -> Standard');
        }
        else if (myValue.match(/^N[0-9]{9}N/i))
        {
            console.log('#> Predicted Suite N.N(11) -> Standard');
        }
      }
      else if (myValue.length == 13) {
        if (myValue.match(/^Y[0-9]{11}N/i))
        {
            console.log('#> Predicted Suite Y.N(13) -> Standard or Outage');
        }
        if (myValue.match(/^Y0[0-9]{10}N/i))
        {
            console.log('#> Predicted CarriageType Y0.N(13) -> BDSL');
        }
      }
      else if (myValue.length == 15) {
        if (myValue.match(/^5[0-9]{14}/i))
        {
            console.log('#> Predicted Suite 5.(15) -> Standard or Outage');
        }
      }
      else if (myValue.length == 16) {
        if (myValue.match(/^A[0-9]{15}/i))
        {
            console.log('#> Predicted Suite A.(16) -> ANDig (backend only)');
        }
      }
    }
}

//Update a value in header if value is not null
function updateHeaderInfo(SCrid, place, valName, valValue, styleClasses) {
    //Default Class
    if (!styleClasses) {
        styleClasses = 'font-weight-bold';
    }
    if (!valValue)
        return false;
    valID = valName.replace(/[^A-Za-z0-9]/ig, '_');
    var headerDivSubVal = $(`#${place}-${SCrid}-${valID}`);

    if (headerDivSubVal.length) {
        headerDivSubVal.text(valValue);
    } else {
        headerDiv = $(`#pills-details-${SCrid}`);
        var elm = `<span class=''>${valName}: <span id='${place}-${SCrid}-${valID}' class='${styleClasses}'>${valValue}</span></span>&nbsp;`
        headerDiv.append(`&nbsp;&nbsp;${elm}`);
    }
}

function addNumberRanges(SCrid, numberRanges) {
    var scRibbon = $(`#SC-ribbon-${SCrid}`);

    if (scRibbon.length === 0){
        console.error(`Element with ID SC-ribbon-${SCrid} not found.`);
        return;
    }

    if ($(`#number-ranges-row-${SCrid}`).length) {
        return;
    }

    let numberRangesStr;
    let anchor;

    if (numberRanges.length > 10) {
        numberRangesStr = numberRanges.slice(0, 10).join(' '.repeat(6));

        anchor = $("<a>")
                .css("color", "blue")
                .css("padding-left", "15px");

        var buttonSpan = $("<span>")
                .addClass("fas fa-plus-square")
                .css("cursor", "pointer")
                .click(() => {
                    $("#numberRangesModalContent").empty();

                    var numberRangesList = $("<ul>");

                    $.each(numberRanges, (_, value) => {
                        var listItem = $("<li>").text(value);
                        numberRangesList.append(listItem);
                    });

                    $("#numberRangesModalContent").append(numberRangesList);
                    $('#numberRangesModal').modal('show');
                });

        anchor.append(buttonSpan);
    } else {
        numberRangesStr = numberRanges.join(' '.repeat(6));
    }

    var numberRangesRibbonRow = $("<div>")
            .addClass("row")
            .addClass('align-items-center')
            .attr("id", `number-ranges-row-${SCrid}`);

    var numberRangesContainer = $("<div>").addClass("col-8");

    var numberRangesHeader = $("<span>").text("Number Ranges: ");

    var numberRangesSpan = $("<span>")
            .addClass("font-weight-bold")
            .text(numberRangesStr)
            .css("white-space", "pre-wrap");

    numberRangesHeader.append(numberRangesSpan);
    numberRangesContainer.append(numberRangesHeader);
    numberRangesRibbonRow.append(numberRangesContainer);
    scRibbon.append(numberRangesRibbonRow);

    if (anchor) {
        numberRangesHeader.append(anchor);
    }
}

function runAppendTemplateToTicket(serviceCheckId, templateName, ticketID, ticketSystem, promise) {
    // Timeout is 2 minutes as action itself may have a shorter timeout
    $.ajax({
        type: "POST",
        url: `/serviceCheck/${serviceCheckId}/appendTemplateToTicket`,
        contentType: "application/json",
        timeout: 120000,
        data: JSON.stringify({
            templateName: templateName,
            ticketID: ticketID,
            ticketSystem: ticketSystem
        }),
        success: function (response) {
            promise.resolve(response);
        },
        error: function(jqXHR, textStatus, errorThrown) {
            promise.reject(jqXHR);
        }
    });
}


//Check if template allows system append
function appendAllowed(templateName, systemName) {
    let canAppend = false;

    if (Array.isArray(templateAllowedSystemsToAppend[templateName])) {
        canAppend = templateAllowedSystemsToAppend[templateName].indexOf(systemName) !== -1;
    }

    return canAppend;
}


// Populate Drop-Down-List of Service Central Incidents
function textTemplateServiceCentralAppend(incidents) {
    if ($('#textTemplateModal-sidropdown option').length === 0) {
        $('#textTemplateModal-sidropdown').hide();
        $('#textTemplateModal-sibutton').hide();
        $('#textTemplateModal-sidropdown').html($("<option>").attr("value", "").attr("selected", "").attr("disabled", "").attr("hidden", "").text("Select Incident"));

        if (incidents && Array.isArray(incidents.ServiceCentral)) {
            for (let i = 0; i < incidents.ServiceCentral.length; i++) {
                $("#textTemplateModal-sidropdown").append($("<option>").attr("value", incidents.ServiceCentral[i]).text(incidents.ServiceCentral[i]));
            }
        }
    } else {
        $("#textTemplateModal-sidropdown").prop("selectedIndex", 0);
    }
}


// Populate Drop-Down-List of SIIAM Incidents
function textTemplateSIIAMAppend(tickets) {
    if ($('#textTemplateModal-siiamdropdown option').length === 0) {
        $('#textTemplateModal-siiamdropdown').hide();
        $('#textTemplateModal-siiambutton').hide();
        $('#textTemplateModal-siiamdropdown').html($("<option>").attr("value", "").attr("selected", "").attr("disabled", "").attr("hidden", "").text("Select Ticket"));

        if (tickets && Array.isArray(tickets.SIIAM)) {
            for (let i = 0; i < tickets.SIIAM.length; i++) {
                $("#textTemplateModal-siiamdropdown").append($("<option>").attr("value", tickets.SIIAM[i]).text(tickets.SIIAM[i]));
            }
        }
    } else {
        $("#textTemplateModal-siiamdropdown").prop("selectedIndex", 0);
    }
}


// Open Template Text Modal
function textTemplate(fnn, id, selectedTemplateName=null) {
    $('#textTemplateModal-sidropdown').hide();
    $('#textTemplateModal-sibutton').hide();
    $('#textTemplateModal-siiamdropdown').hide();
    $('#textTemplateModal-siiambutton').hide();
    $('#textTemplateModalAppendTicket-alert').hide();

    // Finds if text template modal is already open and if it is,
    // do not load the template list
    if ($("#textTemplateModal").hasClass('show')) {
        return;
    }

    $('#inputTemplateName').attr('data-service-check-id', id);
    let templateDisableSelect = $(`#textTemplate-${id}`).attr("data-disable-select");

    $('#textTemplateModal-Title').text(fnn);
    $('#textTemplateModal-ID').val(id);
    $('#textTemplateModal-controls').hide();
    $('#textTemplateModal-text').hide();
    $('#textTemplateModal-alert').hide();
    $('#textTemplateModal-emptyWarning-alert').hide();

    $('#inputTemplateName').attr('disabled', true);
    $('#inputTemplateName').html($("<option>").attr("value", "").attr("selected", "").attr("disabled", "").attr("hidden", "").text("Select Template"));
    $('#inputTemplateName').prop("selectedIndex", 0);
    $('#showRulesData').attr('disabled', true);

    // Removes last listener for modal show
    $('#textTemplateModal').off('shown.bs.modal');

    $('#textTemplateModal').on('shown.bs.modal', function () {
        if (typeof templateDisableSelect !== "undefined" && templateDisableSelect !== false) {
            $("#textTemplateModal-alertText").text('Templates cannot be viewed as service check is not complete');
            $("#textTemplateModal-alert").show();
        } else {
            // Retrives list of templates for this service check
            let templates = $.Deferred();
            $("#templateListLoading").show();
            // Retrives list of incidents for templates for this service check
            let incidents = $.Deferred();

            $.ajax({
                type: "GET",
                url: `/serviceCheck/${encodeURIComponent(id)}/listTemplates`,
                timeout: 60000,
                success: function (response) {
                    templates.resolve(response);
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    templates.reject(errorThrown);
                }
            });

            templates.promise().then((templateList) => {
                templateAllowedSystemsToAppend = {};

                if (Array.isArray(templateList)) {
                    if (templateList.length > 0) {
                        $('#inputTemplateName').removeAttr('disabled');
                        $('#showRulesData').removeAttr('disabled');

                        let selectedTemplateNameExists = false;

                        let defaultTemplateName = null;
                        let defaultTemplateMaxPriority = 0;

                        for (let template of templateList) {
                            if (template.name === selectedTemplateName) {
                                selectedTemplateNameExists = true;
                            }

                            if (template.defaultPriority > 0 && template.defaultPriority > defaultTemplateMaxPriority) {
                                defaultTemplateName = template.name;
                                defaultTemplateMaxPriority = template.defaultPriority;
                            }

                            $("#inputTemplateName").append($("<option>").attr("value", template.name).text(template.title));

                            // Build object with array values to save allowedSystemsToAppend for every Template
                            templateAllowedSystemsToAppend[template.name] = template.allowedSystemsToAppend;
                        }

                        if (selectedTemplateName) {
                            if (selectedTemplateNameExists) {
                                $("#inputTemplateName").val(selectedTemplateName).trigger("change");
                            } else {
                                $("#textTemplateModal-alertText").text(`${selectedTemplateName} is not a valid option for a template name.`);
                                $("#textTemplateModal-alert").show();
                            }
                        }

                        if (!selectedTemplateName && defaultTemplateName) {
                            $("#inputTemplateName").val(defaultTemplateName).trigger("change");

                            let defaultOptionText = $("#inputTemplateName").find("option:selected").text();
                            $("#inputTemplateName").find("option:selected").text(`${defaultOptionText} [DEFAULT]`);
                        }
                    } else {
                        $('#textTemplateModal-emptyWarning-alert').show();
                    }
                } else {
                    $("#textTemplateModal-alertText").text('Error loading list of templates (incorrect format)');
                    $("#textTemplateModal-alert").show();
                }
            }).fail((error) => {
                $("#textTemplateModal-alertText").text('Error loading list of templates for this service check');
                $("#textTemplateModal-alert").show();
            }).always(() => {
                $("#templateListLoading").hide();
            });

            $('#textTemplateModal-sidropdown').html('');
            $('#textTemplateModal-siiamdropdown').html('');

            $.ajax({
                type: "GET",
                url: `/serviceCheck/${encodeURIComponent(id)}/listIncidentsForTemplates`,
                timeout: 60000,
                success: function (response) {
                    incidents.resolve(response);
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    incidents.reject(jqXHR);
                }
            });
            incidents.promise().then((incidentsResponse) => {
                // Populate SI dropdown
                textTemplateServiceCentralAppend(incidentsResponse);

                // Populate SIIAM dropdown
                textTemplateSIIAMAppend(incidentsResponse);
            }).fail((error) => {
                if (error.status !== 403) {
                    $("#textTemplateModal-alertText").text('Error loading service incidents and SIIAM tickets for this service check');
                    $("#textTemplateModal-alert").show();
                }
            });
        }
    });

    $('#textTemplateModal').modal('show');
}


function feedbackClickHandler(id, isPositive) {

    let feedbackMessageInput = "";

    if (!(id in feedbackState)) {
        feedbackState[id] = null;
    }
    if (feedbackState[id] === null) {
        $("#feedbackRemove").hide();
    } else {
        if (feedbackState[id].message) {
            feedbackMessageInput = feedbackState[id].message;
        }
        $("#feedbackRemove").show();
    }

    $("#feedbackSend").off("click").click(function() {
        $("#feedbackSend").prop("disabled", true);
        let feedbackMessage = $("#feedbackMessageInput").val() ? $("#feedbackMessageInput").val() : undefined;

        $.ajax({
            type: "PUT",
            url: `/serviceCheck/${id}/feedback`,
            timeout: 30000,
            data: {
                isPositive: isPositive,
                message: feedbackMessage
            },
            success: function (response) {
                feedbackStateChange(id, {
                    isPositive: isPositive,
                    message: feedbackMessage
                });
            },
            error: function(err) {
                console.error(err);
            },
            complete: function() {
                $("#feedbackSend").prop("disabled", false);
                $("#feedbackModal").modal("hide");
            }
        });
    });

    $("#feedbackRemove").off("click").click(function() {
        $("#feedbackRemove").prop("disabled", true);

        $.ajax({
            type: "DELETE",
            url: `/serviceCheck/${id}/feedback`,
            timeout: 30000,
            success: function (response) {
                feedbackStateChange(id, null);
            },
            error: function(err) {
                console.error(err);
            },
            complete: function() {
                $("#feedbackRemove").prop("disabled", false);
                $("#feedbackModal").modal("hide");
            }
        });
    });

    $("#feedbackMessageInput").val(feedbackMessageInput);
    $("#feedbackMessageInput").trigger("propertychange");
    $("#feedbackModal").modal("show");
}


function feedbackStateChange(id, state) {
    feedbackState[id] = state;
    if (state === null) {
        $(`#feedback-positive-${id}`).removeClass("btn-success");
        $(`#feedback-positive-${id}`).addClass("btn-outline-success");
        $(`#feedback-positive-${id}`).attr("title", "I like this");

        $(`#feedback-negative-${id}`).removeClass("btn-danger");
        $(`#feedback-negative-${id}`).addClass("btn-outline-danger");
        $(`#feedback-negative-${id}`).attr("title", "I don't like this");
    } else if (state.isPositive === true) {
        $(`#feedback-positive-${id}`).removeClass("btn-outline-success");
        $(`#feedback-positive-${id}`).addClass("btn-success");
        $(`#feedback-positive-${id}`).attr("title", "Undo like");

        $(`#feedback-negative-${id}`).removeClass("btn-danger");
        $(`#feedback-negative-${id}`).addClass("btn-outline-danger");
        $(`#feedback-negative-${id}`).attr("title", "I don't like this");
    } else if (state.isPositive === false) {
        $(`#feedback-positive-${id}`).removeClass("btn-success");
        $(`#feedback-positive-${id}`).addClass("btn-outline-success");
        $(`#feedback-positive-${id}`).attr("title", "I like this");

        $(`#feedback-negative-${id}`).removeClass("btn-outline-danger");
        $(`#feedback-negative-${id}`).addClass("btn-danger");
        $(`#feedback-negative-${id}`).attr("title", "View feedback / Undo dislike");
    }
}


function feedbackDisable(id) {
    $(`#feedback-positive-${id}`).attr("disabled", true);
    $(`#feedback-negative-${id}`).attr("disabled", true);
}


function hexToRgb(hex) {
  var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

function getRuleIdFromCat(ruleCat) {
    let categoryID;

    switch (ruleCat) {
        case "Summary":
            categoryID = "summary";
            break;
        case "Administration":
            categoryID = "admin";
            break;
        case "CommPilot":
            categoryID = "commpilot";
            break;
        case "Configuration":
            categoryID = "config";
            break;
        case "Connection":
            categoryID = "connection";
            break;
        case "Data Collection":
            categoryID = "collection";
            break;
        case "Planned Outage":
            categoryID = "planned";
            break;
        case "Sable":
            categoryID = "sable";
            break;
        case "Service Status":
            categoryID = "service";
            break;
        case "Telstra Network":
            categoryID = "telnet";
            break;
        case "Test Team":
            categoryID = "testteam";
            break;
        case "3rd Party Network":
            categoryID = "3net";
            break;
        case "Ticket":
            categoryID = "ticket";
            break;
        case "Unplanned Outage":
            categoryID = "unplanned";
            break;
        default:
            categoryID = "other";
            break;
    }
    return categoryID;
}


function getCategoryColourFromCategory(ruleCat) {
    let categoryColour;

    switch (ruleCat) {
        case "Administration":
            categoryColour = "#97c1a9";
            break;
        case "CommPilot":
            categoryColour = "#cce2cb";
            break;
        case "Configuration":
            categoryColour = "#cbaacb";
            break;
        case "Connection":
            categoryColour = "#ffccb6";
            break;
        case "Data Collection":
            categoryColour = "#ffffb5";
            break;
        case "Planned Outage":
            categoryColour = "#f6eac2";
            break;
        case "Sable":
            categoryColour = "#55cbcd";
            break;
        case "Service Status":
            categoryColour = "#ecd5e3";
            break;
        case "Telstra Network":
            categoryColour = "#abdee6";
            break;
        case "Test Team":
            categoryColour = "#ffc5bf";
            break;
        case "3rd Party Network":
            categoryColour = "#ff968a";
            break;
        case "Ticket":
            categoryColour = "#c6dbda";
            break;
        case "Unplanned Outage":
            categoryColour = "#d4f0f0";
            break;
        default:
            // Default colour in case a new category was added without a configured colour above
            categoryColour = "#eceae4";
            break;
    }

    return categoryColour;
}


//Function to convert rgb color to hex format
function rgb2hex(rgb) {
    rgb = rgb.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
    return rgb && rgb.length > 0 ? "#" + hex(rgb[1]) + hex(rgb[2]) + hex(rgb[3]) : "#0000ff";
}


function hex(x) {
    var hexDigits = new Array ("0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f");
    return isNaN(x) ? "00" : hexDigits[(x - x % 16) / 16] + hexDigits[x % 16];
}


function SCResultsTemplate(id, fnn, suite) {
    // TODO: onclick parameters are not sanitised and it may be possible that
    // an FNN with invalid characters could cause issues with HTML elements
    return `\
    <div id="detail-${id}">
        <div id="SC-ribbon-${id}" class="p-2 p-pl-1">
            <div class="row align-items-center">
                <div class="col-8">
                    <span class="details" id="pills-details-${id}"></span>
                </div>
                <div class="col-4">
                    <span class="icons float-right">
                        <span id="pills-dicon-${id}">
                            <button id="feedback-positive-${id}" class="btn btn-outline-success btn-sm" title="I like this" onclick="feedbackClickHandler('${id}', true)"><span class="fas fa-thumbs-up"/></button>
                            <a class="feedback-popover" id="feedback-popover-${id}"></a>
                            <button id="feedback-negative-${id}" class="btn btn-outline-danger btn-sm" title="I don't like this" onclick="feedbackClickHandler('${id}', false)"><span class="fas fa-thumbs-down"/></button>
                            <button id="textTemplate-${id}" data-disable-select class="btn btn-info btn-sm" type="submit" title="Summary Template" value="Summary Template" onclick="textTemplate('${fnn}', '${id}')">
                            <span class="fas fa-file-alt"></span> Summary Template
                            </button>
                            <form class="d-inline-block service-check-restart" action="/serviceCheck" method="post">
                            <input type="hidden" name="id" value="${id}"/>
                            <button class="btn btn-info btn-sm" type="submit" title="Runs this service check again with the same input" value="Restart Service Check">
                                <span class="fas fa-sync-alt"></span> Restart Service Check
                            </button>
                            </form>
                            <button class="btn btn-info btn-sm copyToClipboardLink" data-toggle="tooltip" data-placement="top" title="Click to copy link of this service check with ID ${id}" href="${window.location.origin}/serviceCheck/view/html/${id}">
                                <span style='color:blue' class='fas fa-link'></span>
                            </button>
                        </span>
                        <a class='expand btn-sm' data-toggle="collapse" data-target="#pills-tabContent-${id}" style="display: inline;line-height: 2.6rem;">
                            <span class="fas fa-minus-square" title="Expand/Collapse"></span>
                        </a>
                    </span>
                </div>
            </div>
        </div>
        <div id="error-${id}" style="display:none;" class="alert alert-danger"></div>
        <div class="collapse show" id="pills-tabContent-${id}">
            <ul id="nav-${id}" class="nav nav-tabs" id="pills-tab" role="tablist">
                <li class="nav-item" role="presentation">
                <a class="nav-link active tab-nav" id="pills-summary-t-${id}" data-toggle="pill" href="#pills-summary-${id}" role="tab" aria-controls="pills-home-${id}" aria-selected="true"><span class="fa fa-info-circle"></span> Summary</a>
                </li>
                <li class="nav-item" ${displayNone} role="presentation">
                <a class="nav-link tab-nav" id="pills-admin-t-${id}" data-toggle="pill" href="#pills-admin-${id}" role="tab" aria-controls="pills-admin-${id}" aria-selected="false"><span class="fa fa-info-circle"></span> Administration</a>
                </li>
                <li class="nav-item" ${displayNone} role="presentation">
                <a class="nav-link tab-nav" id="pills-commpilot-t-${id}" data-toggle="pill" href="#pills-commpilot-${id}" role="tab" aria-controls="pills-commpilot-${id}" aria-selected="false"><span class="fa fa-info-circle"></span> CommPilot</a>
                </li>
                <li class="nav-item" ${displayNone} role="presentation">
                <a class="nav-link tab-nav" id="pills-config-t-${id}" data-toggle="pill" href="#pills-config-${id}" role="tab" aria-controls="pills-config-${id}" aria-selected="false"><span class="fa fa-info-circle"></span> Configuration</a>
                </li>
                <li class="nav-item" ${displayNone} role="presentation">
                <a class="nav-link tab-nav" id="pills-connection-t-${id}" data-toggle="pill" href="#pills-connection-${id}" role="tab" aria-controls="pills-contact-${id}" aria-selected="false"><span class="fa fa-info-circle"></span> Connection</a>
                </li>
                <li class="nav-item" ${displayNone} role="presentation">
                <a class="nav-link tab-nav" id="pills-collection-t-${id}" data-toggle="pill" href="#pills-collection-${id}" role="tab" aria-controls="pills-collection-${id}" aria-selected="false"><span class="fa fa-info-circle"></span> Data Collection</a>
                </li>
                <li class="nav-item" ${displayNone} role="presentation">
                <a class="nav-link tab-nav" id="pills-planned-t-${id}" data-toggle="pill" href="#pills-planned-${id}" role="tab" aria-controls="pills-planned-${id}" aria-selected="false"><span class="fa fa-info-circle"></span> Planned Outage</span></a>
                </li>
                <li class="nav-item" ${displayNone} role="presentation">
                <a class="nav-link tab-nav" id="pills-sable-t-${id}" data-toggle="pill" href="#pills-sable-${id}" role="tab" aria-controls="pills-sable-${id}" aria-selected="false"><span class="fa fa-info-circle"></span> Sable</a>
                </li>
                <li class="nav-item" ${displayNone} role="presentation">
                <a class="nav-link tab-nav" id="pills-service-t-${id}" data-toggle="pill" href="#pills-service-${id}" role="tab" aria-controls="pills-service-${id}" aria-selected="false"><span class="fa fa-info-circle"></span> Service Status</a>
                </li>
                <li class="nav-item" ${displayNone} role="presentation">
                <a class="nav-link tab-nav" id="pills-telnet-t-${id}" data-toggle="pill" href="#pills-telnet-${id}" role="tab" aria-controls="pills-telnet-${id}" aria-selected="false"><span class="fa fa-info-circle"></span> Telstra Network</a>
                </li>
                <li class="nav-item" ${displayNone} role="presentation">
                <a class="nav-link tab-nav" id="pills-testteam-t-${id}" data-toggle="pill" href="#pills-testteam-${id}" role="tab" aria-controls="pills-testteam-${id}" aria-selected="false"><span class="fa fa-info-circle"></span> Test Team</a>
                </li>
                <li class="nav-item" ${displayNone} role="presentation">
                <a class="nav-link tab-nav" id="pills-3net-t-${id}" data-toggle="pill" href="#pills-3net-${id}" role="tab" aria-controls="pills-3net-${id}" aria-selected="false"><span class="fa fa-info-circle"></span> 3rd Party</a>
                </li>
                <li class="nav-item" ${displayNone} role="presentation">
                <a class="nav-link tab-nav" id="pills-ticket-t-${id}" data-toggle="pill" href="#pills-ticket-${id}" role="tab" aria-controls="pills-ticket-${id}" aria-selected="false"><span class="fa fa-info-circle"></span> Ticket</span></a>
                </li>
                <li class="nav-item" ${displayNone} role="presentation">
                <a class="nav-link tab-nav" id="pills-unplanned-t-${id}" data-toggle="pill" href="#pills-unplanned-${id}" role="tab" aria-controls="pills-unplanned-${id}" aria-selected="false"><span class="fa fa-info-circle"></span> Unplanned Outage</span></a>
                </li>
                <li class="nav-item" ${displayNone} role="presentation">
                <a class="nav-link tab-nav" id="pills-other-t-${id}" data-toggle="pill" href="#pills-other-${id}" role="tab" aria-controls="pills-other-${id}" aria-selected="false"><span class="fa fa-info-circle"></span> Other</a>
                </li>
                <li class="nav-item" role="presentation">
                <a class="nav-link tab-nav" id="pills-sources-t-${id}" data-toggle="pill" href="#pills-sources-${id}" role="tab" aria-controls="pills-sources-${id}" aria-selected="false" style="background-color: #28a745; color:#fff; border-color: #28a745"><span class="fa fas fa-check-circle"></span> Sources</a>
                </li>
                <li id="pills-rule-search-t-link-${id}" ${displayNone} class="nav-item" role="presentation">
                <a class="nav-link tab-nav" id="pills-rule-search-t-${id}" data-toggle="pill" href="#pills-rule-search-${id}" role="tab" aria-controls="pills-rule-search-${id}" aria-selected="false" style="background-color: #606060; color:#fff; border-color: #606060"><span class="fa fas fa-search"></span> Rule Search</a>
                </li>
            </ul>
            <div class="p-2 border border-rounded">
                Search Rules <a class="d-inline expand btn-sm" style="color:blue;" data-toggle="collapse" data-target="#search-rules-block-${id}"><span class="fas fa-plus-square" title="Expand/Collapse"></span></a>
                <div class="collapse" id="search-rules-block-${id}">
                    <div class="input-group">
                        <input type="text" class="form-control" id="search-rules-text-${id}"></input>
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary" type="button" title="Clear search" id="search-rules-clear-${id}">&times;</button>
                            <button class="btn btn-primary" type="button" title="Search" id="search-rules-${id}"><span class="fas fa-search"></span></button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tab-content ptb-3">
                <div class="tab-pane fade  show active" id="pills-summary-${id}" role="tabpanel" aria-labelledby="pills-summary-t-${id}"></div>
                <div class="tab-pane fade" id="pills-admin-${id}" role="tabpanel" aria-labelledby="pills-admin-t-${id}">${contentTable}<tbody id="tableBody-${id}-admin"></tbody></table></div>
                <div class="tab-pane fade" id="pills-commpilot-${id}" role="tabpanel" aria-labelledby="pills-commpilot-t-${id}">${contentTable}<tbody id="tableBody-${id}-commpilot"></tbody></table></div>
                <div class="tab-pane fade" id="pills-config-${id}" role="tabpanel" aria-labelledby="pills-config-t-${id}">${contentTable}<tbody id="tableBody-${id}-config"></tbody></table></div>
                <div class="tab-pane fade" id="pills-connection-${id}" role="tabpanel" aria-labelledby="pills-connection-t-${id}">${contentTable}<tbody id="tableBody-${id}-connection"></tbody></table></div>
                <div class="tab-pane fade" id="pills-collection-${id}" role="tabpanel" aria-labelledby="pills-collection-t-${id}">${contentTable}<tbody  id="tableBody-${id}-collection"></tbody></table></div>
                <div class="tab-pane fade" id="pills-planned-${id}" role="tabpanel" aria-labelledby="pills-planned-t-${id}">${contentTable}<tbody id="tableBody-${id}-planned"></tbody></table></div>
                <div class="tab-pane fade" id="pills-sable-${id}" role="tabpanel" aria-labelledby="pills-sable-t-${id}">${contentTable}<tbody id="tableBody-${id}-sable"></tbody></table></div>
                <div class="tab-pane fade" id="pills-service-${id}" role="tabpanel" aria-labelledby="pills-service-t-${id}">${contentTable}<tbody id="tableBody-${id}-service"></tbody></table></div>
                <div class="tab-pane fade" id="pills-telnet-${id}" role="tabpanel" aria-labelledby="pills-telnet-t-${id}">${contentTable}<tbody id="tableBody-${id}-telnet"></tbody></table></div>
                <div class="tab-pane fade" id="pills-testteam-${id}" role="tabpanel" aria-labelledby="pills-testteam-t-${id}">${contentTable}<tbody id="tableBody-${id}-testteam"></tbody></table></div>
                <div class="tab-pane fade" id="pills-3net-${id}" role="tabpanel" aria-labelledby="pills-3net-t-${id}">${contentTable}<tbody id="tableBody-${id}-3net"></tbody></table></div>
                <div class="tab-pane fade" id="pills-ticket-${id}" role="tabpanel" aria-labelledby="pills-ticket-t-${id}">${contentTable}<tbody id="tableBody-${id}-ticket"></tbody></table></div>
                <div class="tab-pane fade" id="pills-unplanned-${id}" role="tabpanel" aria-labelledby="pills-unplanned-t-${id}">${contentTable}<tbody id="tableBody-${id}-unplanned"></tbody></table></div>
                <div class="tab-pane fade" id="pills-other-${id}" role="tabpanel" aria-labelledby="pills-other-t-${id}">${contentTable}<tbody id="tableBody-${id}-other"></tbody></table></div>
                <div class="tab-pane fade" id="pills-sources-${id}" role="tabpanel" aria-labelledby="pills-sources-t-${id}"><div id="DCResults-${id}"><div><button id="expandAllSources-${id}" class="btn btn-secondary btn-sm m-2">Expand all sources</button><button id="collapseAllSources-${id}" class="btn btn-secondary btn-sm m-2">Collapse all sources</button></div></div></div>
                <div class="tab-pane fade" id="pills-rule-search-${id}" role="tabpanel" aria-labelledby="pills-rule-search-t-${id}"><table class="table table-hover pad-rem25"><thead><tr><th class="wid-5-rem">Rule ID</th><th>Search Details</th><th class="wid-5-rem">Links</th></tr></thead><tbody id="rule-search-table-${id}"></tbody></table></div>
            </div>
        </div>
        <div id="SCResults-${id}"></div>
    </div>`;
}


function validateHtmlWithElement(html) {
    // Creates a temporary element and sets the innerHTML to the value message
    // This should convert any invalid / incomplete HTML strings to valid
    let tempElement = document.createElement('div');
    tempElement.innerHTML = html;

    // Reads the element's innerHTML which
    let validatedHtml = tempElement.innerHTML;
    return validatedHtml;
}