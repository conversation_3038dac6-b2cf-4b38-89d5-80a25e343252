{"title": "Rule", "type": "object", "id": "Rule", "headerTemplate": "{{ self.name }}", "options": {"disable_collapse": true, "disable_edit_json": false, "disable_properties": true, "disable_array_add": true, "disable_array_delete": true, "disable_array_reorder": true}, "properties": {"name": {"title": "Name/Code", "type": "string", "description": "Rule Code should start with MTR, MDR, MSR or MAR and end with 3 digits", "readonly": true}, "active": {"title": "Active", "type": "boolean", "format": "checkbox", "default": false}, "hideInResult": {"title": "Hides the rule row in the Rules tabs", "type": "boolean", "format": "checkbox", "default": false}, "showReject": {"title": "Show Msg if Rejected with Precondition (Not Checked for Action Rules)", "type": "boolean", "format": "checkbox", "default": false}, "requiresReview": {"title": "Rule is important and requires review from developers before deploying", "type": "boolean", "format": "checkbox", "default": false}, "includedInitialServiceCheck": {"title": "Run rule as part of initial service check", "type": "boolean", "format": "checkbox", "default": false}, "title": {"title": "Title", "type": "string", "description": "", "minLength": 10, "options": {"input_width": "500px"}}, "rootCauseCategory": {"type": "string", "title": "Root Cause Category", "enum": ["Administration", "CommPilot", "Configuration", "Connection", "Data Collection", "Planned Outage", "Sable", "Service Status", "Telstra Network", "Test Team", "3rd Party Network", "Ticket", "Unplanned Outage"], "default": "Administration"}, "level": {"type": "number", "title": "Level", "enum": [0, 1, 2, 3, 4, 5, 6], "default": 0, "description": "This rule will be run if user select Level equal or more than this level", "options": {"input_width": "100px"}}, "description": {"title": "Description", "type": "string", "format": "textarea", "description": "", "minLength": 0, "options": {"input_height": "75px"}}, "wikiPage": {"title": "Wiki Page", "type": "string", "description": "Name of Page in Merge Wiki"}, "ruleType": {"type": "string", "title": "Rule Type", "enum": ["Logical", "Extract", "Action", "Start Service Check"]}, "isWarning": {"title": "Warning Rule (Not a failed test)", "type": "boolean", "format": "checkbox", "default": false, "description": "Should be selected for MDR and MAR rules"}, "displayInSummary": {"title": "Display rule in summary block", "type": "boolean", "format": "checkbox", "default": false}, "failedInSummary": {"title": "Display rule in summary block if failed", "type": "boolean", "format": "checkbox", "default": true}, "preSources": {"type": "array", "format": "table", "title": "Pre-Sources", "description": "Source will be collected after all these sources are collected", "uniqueItems": true, "items": {"type": "string", "readonly": false}, "options": {"disable_array_delete": false, "disable_array_reorder": false}}, "preSourcesRunOnFail": {"title": "Run if Pre-Sources failed", "type": "boolean", "format": "checkbox", "default": false}, "preRules": {"type": "array", "format": "table", "title": "Pre-Rules", "description": "Source will be collected after all these rules are executed", "uniqueItems": true, "items": {"type": "string", "readonly": false}, "options": {"disable_array_delete": false, "disable_array_reorder": false}}, "preRulesRunOnFail": {"title": "Run if Pre-Rules failed", "type": "boolean", "format": "checkbox", "default": false}, "runWhenDependenciesResolved": {"title": "Always runs the rule, once all of its dependencies (rules and sources) and further chained dependencies have been completed", "type": "boolean", "format": "checkbox", "default": false}, "depedenciesFromRelatedServiceChecks": {"title": "Run the rule when dependencies from related service checks are completed as well", "type": "boolean", "format": "checkbox", "default": false}, "runForEachPreSource": {"title": "Run rule after each pre-source (in order, stops evaluation if rule is successful)", "type": "boolean", "format": "checkbox", "default": false}, "preCondition": {"title": "Precondition Code", "type": "string", "format": "javascript", "description": "", "minLength": 0, "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 20}}}, "preConditionMsg": {"title": "Precondition Message", "type": "string", "description": "", "minLength": 0}, "trueMsg": {"title": "True Message", "type": "string", "description": "", "minLength": 0}, "falseMsg": {"title": "False Message", "type": "string", "description": "", "minLength": 0}, "errorMsg": {"title": "Error Message", "type": "string", "description": "", "minLength": 0}, "ruleCode": {"title": "Main Rule Code", "type": "string", "format": "javascript", "description": "For access to rules data use 'r.', for source data 's.', for all data use 'data.' and for merge function mLib.XXXX()", "readonly": false, "options": {"input_height": "300px"}}, "valueMsgStm": {"title": "Value Message Code", "type": "string", "format": "javascript", "description": "", "minLength": 0, "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 20}}}, "extraInfo": {"title": "Extra Information Code", "type": "string", "format": "javascript", "description": "", "minLength": 0, "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 20}}}, "ignoreCompare": {"title": "Ignore compare", "type": "boolean", "format": "checkbox", "description": "Ignore rule when comparing service check records", "default": false}, "overrideCompare": {"title": "Override Compare Code", "type": "string", "format": "javascript", "description": "", "minLength": 0, "readonly": false, "options": {"ace": {"minLines": 5, "maxLines": 20}}}, "unitTests": {"type": "array", "format": "table", "title": "Testing (Unit Tests)", "items": {"type": "object", "readonly": false, "properties": {"name": {"type": "string", "readonly": false, "options": {"input_width": "30em"}}, "inputRecord": {"type": "string", "title": "Input Record", "format": "json", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 10}}}, "outputRecord": {"type": "string", "title": "Output Record", "format": "json", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 10}}}}}, "options": {"disable_array_delete": false, "disable_array_reorder": false}}, "action": {"type": ["object", "null"], "title": "Action", "default": null, "properties": {"api": {"type": "string", "title": "API", "enum": []}, "parameterCode": {"type": "string", "title": "Parameter Code", "format": "javascript", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 5}}}, "codeCondition": {"type": "string", "title": "Code Condition", "format": "javascript", "readonly": false, "options": {"ace": {"minLines": 1, "maxLines": 5}}}, "autoExecution": {"type": "boolean", "title": "Auto Execution", "format": "checkbox"}}, "options": {"disable_edit_json": true, "disable_properties": true}}}}