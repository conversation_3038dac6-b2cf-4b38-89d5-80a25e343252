import axios from "axios"
import { API_BASE_URL_LEGACY } from "./apiUrl"

const legacyAxiosInstance = axios.create({
    baseURL: API_BASE_URL_LEGACY,
    timeout: 600000, // Set timeout for requests
    headers: {
        'Content-Type': 'application/json',
    },
})

// Interceptors for request/response handling
legacyAxiosInstance.interceptors.request.use(
    (config) => config,
    (error) =>
        Promise.reject(
            error instanceof Error ? error : new Error(String(error))
        )
)

legacyAxiosInstance.interceptors.response.use(
    (response) => response,
    (error) =>
        Promise.reject(
            error instanceof Error ? error : new Error(String(error))
        )
)

export default legacyAxiosInstance
