import mongoose from 'mongoose';

import { MongoMemoryServer } from 'mongodb-memory-server';

let mongoDaemon = await MongoMemoryServer.create({
    instance: {
        dbName: 'mergeTest',
        port: 27018,
        args: [
            '--noscripting'
        ]
    },
    binary: {
        version: '8.0.4'
    },
    auth: {
        enable: true,
        extraUsers: [{
            createUser: 'testuser',
            pwd: 'testpassword',
            roles: [
                'readWrite'
            ],
            database: 'mergeTest'
        }]
    }
});

export async function start() {
    const uri = mongoDaemon.getUri('mergeTest');
    await mongoose.connect(uri, {
        user: 'testuser',
        pass: 'testpassword'
    });
}

export async function stop() {
    await mongoose.connection.close();
    await mongoDaemon.stop();
}

export default {
    start,
    stop
};
