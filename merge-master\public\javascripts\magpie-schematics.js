// Modified version of ShowTooltip function from Magpie API schematics

function overrideShowTooltip(evt, hasDescription, toBeHighlighted) {
    if (initialized == null) {
        Init(evt);
    }
    GetTrueCoords(evt);
    var tipScale = 1/SVGRoot.currentScale;
    var targetElement = evt.currentTarget;
    if (lastElement != targetElement) {
        targetId = targetElement.getAttributeNS(null, 'id');
        if(toBeHighlighted){
            HighlightNode();
        }
        if(hasDescription){
            var tipId = 'tooltip.' + targetId;
            tipGroup = SVGDocument.getElementById(tipId);
            setPanelAndPopupHeight('show');

            // Attempts to bring tooltip position for device more in line with device element
            var xPos = TrueCoords.x + (50 * tipScale);
            xPos = Math.max(xPos - 220, 0);

            var yPos = TrueCoords.y + (50 * tipScale);
            yPos = Math.max(yPos - 120, 0);

            tipGroup.setAttributeNS(null, 'transform', 'translate(' + xPos + ',' + yPos + ') scale(' + tipScale + ')');
            tipGroup.setAttributeNS(null, 'visibility', 'visible');
            magpieSVG.insertBefore(tipGroup, magpieSVG.lastChild);
        }
    }
};