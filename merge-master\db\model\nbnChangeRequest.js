
import mongoose from 'mongoose';

const nbnChangeRequestSchema = new mongoose.Schema({
    changeRequestId: { type: String, default: null, required: true, index: true, immutable: true },
    nbnIds: {
        type: [{
            type: String
        }],
        default: []
    },
    data: {
        type: String,
        get: function(data) {
            try {
                return JSON.parse(data);
            } catch(error) {
                return data;
            }
        },
        set: function(data) {
            return JSON.stringify(data);
        }
    }
},
{
    timestamps: true
});


export default mongoose.model('nbnchangerequest', nbnChangeRequestSchema);
