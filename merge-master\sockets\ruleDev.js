'use strict';

import _ from 'lodash';
import ivm from 'isolated-vm';

import mergeRule from '../modules/mergeRule.js';
import { InputTypes, ProductTypes, RuleResult } from '../modules/enumerations.js';
import compareRuleData from '../modules/compareRuleData.js';
import Rule from '../db/model/rule.js';
import ServiceCheckModel from '../db/model/serviceCheck.js';


export function socketListen(socket) {
    socket.on('ruleDev:preCondition', async function(data) {
        if (!_.isPlainObject(data)) {
            socket.emit('ruleDev:preConditionResult', {
                error: new TypeError("Input data from socket was not a plain object").toString(),
                message: 'Error with input data'
            });
            return;
        }

        let rule = data.rule;
        let variablesCode = data.variables;
        let SCr = new ServiceCheckModel();

        try {
            await new Rule(rule).validate();
        } catch(error) {
            socket.emit('ruleDev:preConditionResult', {
                error: error.toString(),
                message: 'Error encountered while validating input rule'
            });
            return;
        }

        const isolate = new ivm.Isolate({
            memoryLimit: 32
        });
        let ruleVariableContext = await isolate.createContext();
        await ruleVariableContext.global.set('InputTypes', new ivm.ExternalCopy(InputTypes).copyInto());
        await ruleVariableContext.global.set('ProductTypes', new ivm.ExternalCopy(ProductTypes).copyInto());

        try {
            try {
                await ruleVariableContext.eval(variablesCode, { timeout: 2000 });

                let data = await ruleVariableContext.global.get('data', { copy: true });
                let r = await ruleVariableContext.global.get('r', { copy: true });
                let s = await ruleVariableContext.global.get('s', { copy: true });
                let relatedServiceChecksContext = await ruleVariableContext.global.get('relatedServiceChecks', { copy: true });

                if (data === undefined) {
                    throw ReferenceError("data is undefined");
                }
                if (r === undefined) {
                    throw ReferenceError("r is undefined");
                }
                if (s === undefined) {
                    throw ReferenceError("s is undefined");
                }

                if (!_.isPlainObject(data)) {
                    throw ReferenceError("data is not an object");
                }
                if (!_.isPlainObject(r)) {
                    throw ReferenceError("r is not an object");
                }
                if (!_.isPlainObject(s)) {
                    throw ReferenceError("s is not an object");
                }

                if (!_.isPlainObject(data.rulesData)) {
                    data.rulesData = {};
                }
                if (!_.isPlainObject(data.sourcesData)) {
                    data.sourcesData = {};
                }
                if (!_.isPlainObject(data.sourcesMetadata)) {
                    data.sourcesMetadata = {};
                }

                Object.assign(SCr, data);
                // Set rules data in variables sandbox only if the specified r, s values are not
                // empty objects or null / undefined
                if (_.isEmpty(r)) {
                    r = SCr.rulesData ? SCr.rulesData : {};
                } else {
                    SCr.rulesData = r;
                }
                if (_.isEmpty(s)) {
                    s = SCr.sourcesData ? SCr.sourcesData : {};
                } else {
                    SCr.sourcesData = s;
                }

                if (!_.isEmpty(relatedServiceChecksContext)) {
                    SCr.relatedServiceChecks = relatedServiceChecksContext;
                }

                // Initiates rulesData key for current rule name
                if (!r[rule.name]) {
                    r[rule.name] = {};
                }

                // Copies processed rulesData / sourcesData / service check record data
                // back into isolate context
                await ruleVariableContext.global.set('data', new ivm.ExternalCopy(SCr.toJSON()).copyInto());
                await ruleVariableContext.global.set('r', ruleVariableContext.global.getSync('data').getSync('rulesData').derefInto());
                await ruleVariableContext.global.set('s', ruleVariableContext.global.getSync('data').getSync('sourcesData').derefInto());

                await ruleVariableContext.global.delete('relatedServiceChecks');
            } catch(error) {
                socket.emit('ruleDev:preConditionResult', {
                    error: error.toString(),
                    message: 'Error encountered while attempting to evaluate variables'
                });
                return;
            }

            try {
                let preCondResult;

                if (rule.preCondition) {
                    preCondResult = await ruleVariableContext.eval(rule.preCondition, { copy: true, timeout: 2000 });
                } else {
                    preCondResult = true;
                }

                let data = await ruleVariableContext.global.get('data', { copy: true });

                socket.emit('ruleDev:preConditionResult', {
                    error: null,
                    message: 'Precondition run successful',
                    preConditionResult: preCondResult,
                    preConditionIsUndefined: preCondResult === undefined,
                    variables: {
                        data: data
                    }
                });
            } catch(error) {
                socket.emit('ruleDev:preConditionResult', {
                    error: error.toString(),
                    message: 'Error encountered while running preCondition code'
                });
            }
        } finally {
            ruleVariableContext.release();
            if (!isolate.isDisposed) {
                isolate.dispose();
            }
        }
    });

    socket.on('ruleDev:run', async function(data) {
        if (!_.isPlainObject(data)) {
            socket.emit('ruleDev:runResult', {
                error: new TypeError("Input data from socket was not a plain object").toString(),
                message: 'Error with input data'
            });
            return;
        }

        let rule = data.rule;
        let variablesCode = data.variables;
        let SCr = new ServiceCheckModel();
        let relatedServiceChecks = null;

        let ruleModelInstance;
        try {
            ruleModelInstance = new Rule(rule);
            await ruleModelInstance.validate();
        } catch(error) {
            socket.emit('ruleDev:runResult', {
                error: error.toString(),
                message: 'Error encountered while validating input rule'
            });
            return;
        }

        const isolate = new ivm.Isolate({
            memoryLimit: 32
        });
        let ruleVariableContext = await isolate.createContext();

        try {
            try {
                await ruleVariableContext.eval(variablesCode, { timeout: 2000 });

                let data = await ruleVariableContext.global.get('data', { copy: true });
                let r = await ruleVariableContext.global.get('r', { copy: true });
                let s = await ruleVariableContext.global.get('s', { copy: true });
                let relatedServiceChecksContext = await ruleVariableContext.global.get('relatedServiceChecks', { copy: true });

                if (data === undefined) {
                    throw ReferenceError("data is undefined");
                }
                if (r === undefined) {
                    throw ReferenceError("r is undefined");
                }
                if (s === undefined) {
                    throw ReferenceError("s is undefined");
                }

                if (!_.isPlainObject(data)) {
                    throw ReferenceError("data is not an object");
                }
                if (!_.isPlainObject(r)) {
                    throw ReferenceError("r is not an object");
                }
                if (!_.isPlainObject(s)) {
                    throw ReferenceError("s is not an object");
                }

                if (!_.isPlainObject(data.rulesData)) {
                    data.rulesData = {};
                }
                if (!_.isPlainObject(data.sourcesData)) {
                    data.sourcesData = {};
                }
                if (!_.isPlainObject(data.sourcesMetadata)) {
                    data.sourcesMetadata = {};
                }

                Object.assign(SCr, data);
                // Set rules data in variables sandbox only if the specified r, s values are not
                // empty objects or null / undefined
                if (_.isEmpty(r)) {
                    r = SCr.rulesData ? SCr.rulesData : {};
                } else {
                    SCr.rulesData = r;
                }
                if (_.isEmpty(s)) {
                    s = SCr.sourcesData ? SCr.sourcesData : {};
                } else {
                    SCr.sourcesData = s;
                }

                if (!_.isEmpty(relatedServiceChecksContext)) {
                    if (Array.isArray(relatedServiceChecksContext)) {
                        relatedServiceChecks = relatedServiceChecksContext.map((serviceCheckRecord) => new ServiceCheckModel(serviceCheckRecord));
                    }
                }
            } catch(error) {
                socket.emit('ruleDev:runResult', {
                    error: error.toString(),
                    message: 'Error encountered while attempting to evaluate variables'
                });
                return;
            }

            try {
                let [ruleResult, ruleCodeReturnValue, valueMsg, extraInfo] = await mergeRule.runRule(ruleModelInstance, SCr, relatedServiceChecks);

                SCr.rulesData[rule.name].type = rule.ruleType;

                SCr.rulesData[rule.name].valueMsg = valueMsg;
                SCr.rulesData[rule.name].extraInfo = extraInfo;
                SCr.rulesData[rule.name].updatedOn = new Date().toISOString();
                SCr.rulesData[rule.name].rootCauseCategory = rule.rootCauseCategory;
                SCr.rulesData[rule.name].result = ruleResult;

                switch (ruleResult) {
                    case RuleResult.reject:
                        SCr.rulesData[rule.name].msg = rule.preConditionMsg;
                        SCr.rulesData[rule.name].error = null;
                        break;
                    case RuleResult.ok:
                    case RuleResult.actionable:
                        SCr.rulesData[rule.name].msg = rule.trueMsg;
                        break;
                    case RuleResult.actioned:
                        SCr.rulesData[rule.name].msg = valueMsg ? valueMsg : rule.trueMsg;
                        break;
                    case RuleResult.warning:
                    case RuleResult.failed:
                    default:
                        SCr.rulesData[rule.name].msg = rule.falseMsg;
                        break;
                }

                socket.emit('ruleDev:runResult', {
                    error: null,
                    message: 'Rule run successful',
                    variables: {
                        data: SCr.toJSON()
                    }
                });
            } catch(error) {
                SCr.rulesData[rule.name].result = RuleResult.error;

                SCr.rulesData[rule.name].msg = rule.errorMsg;
                SCr.rulesData[rule.name].error = error.toString();
                SCr.rulesData[rule.name].valueMsg = null;
                SCr.rulesData[rule.name].extraInfo = null;
                SCr.rulesData[rule.name].updatedOn = new Date().toISOString();

                socket.emit('ruleDev:runResult', {
                    error: error.toString(),
                    message: 'Error encountered while running rule code',
                    variables: {
                        data: SCr.toJSON()
                    }
                });
            }
        } finally {
            ruleVariableContext.release();
            if (!isolate.isDisposed) {
                isolate.dispose();
            }
        }
    });

    socket.on('ruleDev:compare', async function(data) {
        if (!_.isPlainObject(data)) {
            socket.emit('ruleDev:compareResult', {
                error: new TypeError("Input data from socket was not a plain object").toString(),
                message: 'Error with input data'
            });
            return;
        }

        try {
            let ruleData1 = JSON.parse(data.ruleData1);
            let ruleData2 = JSON.parse(data.ruleData2);

            let compareResult = await compareRuleData.isEqual(ruleData1, ruleData2, data.overrideCompare);
            socket.emit('ruleDev:compareResult', {
                compareResult: compareResult,
                compareResultIsUndefined: compareResult === undefined,
                message: `Rule compare successful, result: ${compareResult}`
            });
        } catch(error) {
            socket.emit('ruleDev:compareResult', {
                error: error.toString(),
                message: 'Error when running rule compare'
            });
        }
    });
}