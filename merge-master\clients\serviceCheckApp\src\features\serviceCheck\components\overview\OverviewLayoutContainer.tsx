import classNames from 'classnames'
import React from 'react'
import styles from './OverviewLayoutContainer.module.scss'

interface OverviewLayoutContainerProps {
    networkDiagramsPanel: React.ReactNode
    productDetailsPanel: React.ReactNode
    deviceDetailsPanel: React.ReactNode
    accountStatusPanel: React.ReactNode
    outagesPanel: React.ReactNode
    activeIncidentsPanel: React.ReactNode
}

export const OverviewLayoutContainer = ({
    networkDiagramsPanel,
    productDetailsPanel,
    deviceDetailsPanel,
    accountStatusPanel,
    outagesPanel,
    activeIncidentsPanel,
}: OverviewLayoutContainerProps) => {
    return (
        <div className={styles.overviewTab}>
            <div className={classNames(styles.networkDiagramsPanel)}>
                {networkDiagramsPanel}
            </div>
            <div className={classNames(styles.productDetailsPanel)}>
                {productDetailsPanel}
            </div>
            <div className={classNames(styles.deviceDetailsPanel)}>
                {deviceDetailsPanel}
            </div>
            <div className={classNames(styles.accountStatusPanel)}>
                {accountStatusPanel}
            </div>
            <div className={classNames(styles.outagesPanel)}>
                {outagesPanel}
            </div>
            <div className={classNames(styles.activeIncidentsPanel)}>
                {activeIncidentsPanel}
            </div>
        </div>
    )
}
