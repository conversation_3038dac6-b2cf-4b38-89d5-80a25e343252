server {
	listen                          80;
	server_name                     merge-dev.in.telstra.com.au *.merge-dev.in.telstra.com.au;
	return 301                      https://$server_name$request_uri;
}

#hangar15
server {
        listen 	443 ssl;
        listen 	[::]:443 ssl;

        server_name 				merge-dev.in.telstra.com.au s1.merge-dev.in.telstra.com.au;
		ssl_certificate             /etc/ssl/certs/merge_signed_v2.crt;
		ssl_certificate_key         /etc/ssl/certs/merge_signed_v2.key;

		client_max_body_size 10M;

		location /public {
			alias /usr/app/merge_stage/public;
			access_log off;
			expires -1;
		}

		location / {

			#auth_basic                            "Username and Password Required (Specific and Limited to Merge-Stage)";
			#auth_basic_user_file                  /usr/app/merge/config/users.htpasswd;

			proxy_pass http://hangar15.networks.in.telstra.com.au:3005;
			proxy_http_version 1.1;

			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		    proxy_set_header X-NginX-Proxy true;

			proxy_set_header Upgrade $http_upgrade;
			proxy_set_header Connection 'upgrade';
			proxy_set_header Host $host;
			proxy_cache_bypass $http_upgrade;
		}
}

#hangar16
server {
        listen 	443 ssl;
        listen 	[::]:443 ssl;

        server_name 				s2.merge-dev.in.telstra.com.au;
		ssl_certificate             /etc/ssl/certs/merge_signed_v2.crt;
		ssl_certificate_key         /etc/ssl/certs/merge_signed_v2.key;

		client_max_body_size 10M;

		location /public {
			alias /usr/app/merge_stage/public;
			access_log off;
			expires -1;
		}

		location / {

			#auth_basic                            "Username and Password Required (Specific and Limited to Merge-Stage)";
			#auth_basic_user_file                  /usr/app/merge/config/users.htpasswd;

			proxy_pass http://hangar16.networks.in.telstra.com.au:3005;
			proxy_http_version 1.1;

			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		    proxy_set_header X-NginX-Proxy true;
			proxy_set_header X-Forwarded-Proto $scheme;

			proxy_set_header Upgrade $http_upgrade;
			proxy_set_header Connection 'upgrade';
			proxy_set_header Host $host;
			proxy_cache_bypass $http_upgrade;
		}
}
