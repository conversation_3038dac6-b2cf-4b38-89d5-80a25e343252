import { DetailsPanel } from '../../../components/detailsPanel/DetailsPanel'
import { GreenCheckIcon } from '../../../components/statusIcon/GreenCheckIcon'
import { OrangeWarningIcon } from '../../../components/statusIcon/OrangeWarningIcon'
import { useAppState } from '../../../hooks/useAppState'
import { PromiseTasksField } from './fields/PromiseTasksField'
import { ServiceCentralIncidentsField } from './fields/ServiceCentralIncidentsField'
import { SIIAMActiveCasesField } from './fields/SIIAMActiveCasesField'
import { SIIAMHistoryCasesField } from './fields/SIIAMHistoryCasesField'

export const ActiveIncidentsPanel = () => {
    const { appState } = useAppState()
    const activeIncidents = appState.serviceDetails?.activeIncidents || {}

    // Ensure hasIncidents logic is clear and readable
    const hasIncidents =
        (activeIncidents.siiamActiveCases?.length ?? 0) > 0 ||
        ((activeIncidents.serviceCentralIncidents?.length ?? 0) > 0 &&
            (activeIncidents.serviceCentralIncidents?.filter(
                (s) => s.state !== 'Resolved' && s.state !== 'Closed'
            )?.length ?? 0) > 0)

    return (
        <DetailsPanel
            label="Active Incidents"
            panelElements={
                hasIncidents ? <OrangeWarningIcon /> : <GreenCheckIcon />
            }
        >
            <>
                <ServiceCentralIncidentsField
                    incidents={activeIncidents.serviceCentralIncidents || []}
                />
                <SIIAMActiveCasesField
                    value={activeIncidents.siiamActiveCases || []}
                />
                <SIIAMHistoryCasesField
                    value={activeIncidents.siiamHistoryCases || []}
                />

                <PromiseTasksField tasks={activeIncidents.promiseTasks || []} />
            </>
        </DetailsPanel>
    )
}
