{"title": "Message Bucket", "type": "object", "id": "Message Bucket", "headerTemplate": "{{ self.name }}", "options": {"disable_collapse": true, "disable_edit_json": false, "disable_properties": true, "disable_array_add": true, "disable_array_delete": true, "disable_array_reorder": true}, "properties": {"name": {"title": "Name", "type": "string", "minLength": 1, "readonly": false}, "description": {"title": "Description", "type": "string", "description": "", "minLength": 0}, "messageFrontOfHouse": {"title": "Message (Front of House)", "type": "string", "format": "textarea", "description": "", "minLength": 0}, "messageCustomer": {"title": "Message (Customer)", "type": "string", "format": "textarea", "description": "", "minLength": 0}}}