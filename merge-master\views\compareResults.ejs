<!-- v1 -->
<%- include('header', {}); %>
<%- include('menu' , {currentTab: 'command' }); %>
<link rel="stylesheet" href="/public/stylesheets/flatpickr.min.css">
<script src="/public/javascripts/flatpickr.min.js" crossorigin="anonymous"></script>
<link rel="stylesheet" type="text/css" href="/public/stylesheets/jsgrid.min.css">
<link rel="stylesheet" type="text/css" href="/public/stylesheets/jsgrid-theme.min.css">
<link rel="stylesheet" type="text/css" href="/public/stylesheets/select2.min.css">
<script type="text/javascript" src="/public/javascripts/jsgrid.min.js"></script>
<script src="/public/javascripts/select2.min.js"></script>
<div class="d-flex flex-grow-1 flex-column" style="padding:1rem;">
    <div class="d-inline">
        <br>
        <div class="jumbotron py-1 px-4 text-white rounded bg-secondary">
            <h3 class="display-6 font-weight-normal"><%= title %></h3>
        </div>

        <div class="row">
            <div class="col-6">
                <p id="displayServiceCheckTotal">0 records total</p>
            </div>
            <div class="col-6">
                <div class="float-right">
                    <button id="resetDates" class="btn btn-sm btn-secondary" title="Default time range (1 month)"><span class="fas fa-undo"></span></button>&nbsp;
                    <label for="startDate" class="col-form-label">Start:</label>&nbsp;
                    <input type="text" id="startDate">&nbsp;
                    <label for="endDate" class="col-form-label">End:</label>&nbsp;
                    <input type="text" id="endDate">&nbsp;
                    <span id="endDateClear" class="fas fa-times" title="Clear end date"></span>&nbsp;
                    <button id="historyRefresh" class="btn btn-primary" title="Refresh"><span class="fas fa-sync"></span></button>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-4">
                <label for="numRecords">Records per page:</label>
                <select id="numRecords" style="width: 6em">
                    <option value=10>10</option>
                    <option value=25>25</option>
                    <option value=50 selected>50</option>
                    <option value=100>100</option>
                    <option value=500>500</option>
                    <option value=1000>1000</option>
                </select>
            </div>
            <div class="col-4"><sup><b>*</b></sup> Select 2 or 3 records to compare</div>
            <div class="col-4">
                <div class="float-right">
                    <label class="col-form-label" id="displaySelectedCount">0 records selected</label>
                    <button id="compare" class="btn btn-success">Compare</button>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <label class="col-form-label">Configure columns</label>
                <a class='expand btn-sm' data-toggle="collapse" data-target="#configureColumns">
                    <span style="color:blue" class="fas fa-plus-square" title="Expand/collapse service check columns to show"></span>
                </a>
                <div id="configureColumns" class="collapse card card-body bg-transparent">
                    <div class="form-row">
                        <label class="col-form-label" for="columnSelectReset">Columns for the service check history table. FNN and Time columns will always be displayed.</label><button id="columnSelectReset" class="btn btn-secondary" title="Reset columns to default"><span class="fas fa-undo"></span></button>
                    </div>
                    <hr>
                    <div class="form-row">
                        <select id="columnSelect" multiple="multiple" style="width:100%;min-height:500px;">
                            <option value="status" selected>Status</option>
                            <option value="suite">Suite</option>
                            <option value="startMethod">Start Method</option>
                            <option value="carriageType" selected>Carriage Type</option>
                            <option value="carriageFNN" selected>Carriage FNN</option>
                            <option value="deviceName" selected>Device Name</option>
                            <option value="billingFNN">Billing FNN</option>
                            <option value="MDNFNN">MDN FNN</option>
                            <option value="CIDN">CIDN</option>
                            <option value="nbnAccessType">NBN Access Type</option>
                            <option value="nbnId">NBN ID</option>
                            <option value="createdBy" selected>User</option>
                            <option value="serviceType" selected>Service Type</option>
                            <option value="siiamCases" selected>SIIAM Cases</option>
                            <option value="serviceCentralIncidents" selected>Service Central Incidents</option>
                            <option value="feedback">Feedback</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <br>
    </div>
    <div id="serviceCheckGrid" class="flex-grow-1"></div>
</div>
<script>
    const currentUser = "<%- user.username %>";
    let selectedRecords = new Set();

    let startDatePicker;
    let endDatePicker;

    // Note: filter values for the "status" and "suite" fields are repeated here and are independent of
    // any definitions in the server codebase
    const serviceCheckColumnFields = Object.freeze({
        fnn: { name: "fnn", title: "FNN", type: "text", itemTemplate: function(value, item) {
            // To update: New UI link
            if (item?.startMethod === 'UI') {
                return $("<a>").attr("href", `/serviceCheck/new?id=${item.id}`).attr("target", "blank").text(value);
            } else {
                return $("<a>").attr("href", `/serviceCheck/view/html/${item.id}`).attr("target", "blank").text(value);
            }
        }},
        status: { name: "status", title: "Status", width: "8em", type: "select", textField: "name", valueField: "value",
            items: [
                {
                    name: "",
                    value: null
                },
                {
                    name: "abortedInitial",
                    value: "abortedInitial"
                },
                {
                    name: "completedWithError",
                    value: "completedWithError"
                },
                {
                    name: "completedWithErrorPartial",
                    value: "completedWithErrorPartial"
                },
                {
                    name: "done",
                    value: "done"
                },
                {
                    name: "donePartial",
                    value: "donePartial"
                },
                {
                    name: "error",
                    value: "error"
                },
                {
                    name: "running",
                    value: "running"
                }
            ]
        },
        startMethod: { name: "startMethod", title: "Start Method", type: "select", textField: "name", valueField: "value", width: "6em",
            items: [
                {
                    name: "",
                    value: null
                },
                {
                    name: "API",
                    value: "API"
                },
                {
                    name: "UI",
                    value: "UI"
                },
                {
                    name: "UI Classic",
                    value: "UI Classic"
                }
            ]
        },
        suite: { name: "suite", title: "Suite", type: "select", textField: "name", valueField: "value", width: "6em",
            items: [
                {
                    name: "",
                    value: null
                },
                {
                    name: "standard",
                    value: "standard"
                },
                {
                    name: "andig",
                    value: "andig"
                },
                {
                    name: "ipvoice",
                    value: "ipvoice"
                },
                {
                    name: "outageInfo",
                    value: "outageInfo"
                },
                {
                    name: "wholesale",
                    value: "wholesale"
                }
            ]
        },
        carriageType: { name: "carriageType", title: "Carriage Type", type: "text" },
        carriageFNN: { name: "carriageFNN", title: "Carriage FNN", type: "text" },
        deviceName: { name: "deviceName", title: "Device Name", type: "text", width: "12em" },
        billingFNN: { name: "billingFNN", title: "Billing FNN", type: "text" },
        MDNFNN: { name: "MDNFNN", title: "MDN FNN", type: "text" },
        CIDN: { name: "CIDN", title: "CIDN", type: "text" },
        nbnAccessType: { name: "nbnAccessType", title: "NBN Access Type", type: "text" },
        nbnId: { name: "nbnId", title: "NBN ID", type: "text" },
        createdBy: { name: "createdBy", title: "User", type: "text", filtering: false },
        serviceType: { name: "serviceType", title: "Service Type", type: "text" },
        siiamCases: { name: "siiamCases", title: "SIIAM Cases", type: "checkbox", sorting: false, width: "6em", itemTemplate: function(value, item) {
            if (value.length == 0) {
                return "None";
            } else {
                let caseList = value.join("<br>");
                let elementId =  "siiam_cases" + item.id;
                let cases = (value.length == 1) ? "case" : "cases";
                let expandElement = `<a>${value.length} open ${cases} </a><a href="#" data-toggle="collapse" data-target="#${elementId}">view</a>\
                                        <div class="collapse" id="${elementId}"><p>${caseList}</p></div>`;
                return expandElement;
            }
        }},
        serviceCentralIncidents: { name: "serviceCentralIncidents", title: "Service Central Incidents", type: "checkbox", sorting: false, width: "8em", itemTemplate: function(value, item) {
            if (value.length == 0) {
                return "None";
            } else {
                let caseList = value.join("<br>");
                let elementId =  "service_central_incidents" + item.id;
                let incidents = (value.length == 1) ? "incident" : "incidents";
                let expandElement = `<a>${value.length} active ${incidents} </a><a href="#" data-toggle="collapse" data-target="#${elementId}">view</a>\
                                        <div class="collapse" id="${elementId}"><p>${caseList}</p></div>`;
                return expandElement;
            }
        }},
        feedback: { name: "feedback", title: "Feedback", width: "4em", type: "select", sorting: false, textField: "name", valueField: "value",
            items: [
                {
                    name: "",
                    value: null
                },
                {
                    name: "Missing feedback",
                    value: "feedbackNotExists"
                },
                {
                    name: "Has feedback",
                    value: "feedbackExists"
                },
                {
                    name: "Has feedback with message",
                    value: "feedbackWithMessage"
                },
                {
                    name: "Has feedback with read message",
                    value: "feedbackWithMessageRead"
                },
                {
                    name: "Has feedback with unread message",
                    value: "feedbackWithMessageUnread"
                },
                {
                    name: "Positive feedback",
                    value: "feedbackIsPositive"
                },
                {
                    name: "Positive feedback and message",
                    value: "feedbackIsPositiveWithMessage"
                },
                {
                    name: "Positive feedback and read message",
                    value: "feedbackIsPositiveWithMessageRead"
                },
                {
                    name: "Positive feedback and unread message",
                    value: "feedbackIsPositiveWithMessageUnread"
                },
                {
                    name: "Negative feedback",
                    value: "feedbackIsNegative"
                },
                {
                    name: "Negative feedback and message",
                    value: "feedbackIsNegativeWithMessage"
                },
                {
                    name: "Negative feedback and read message",
                    value: "feedbackIsNegativeWithMessageRead"
                },
                {
                    name: "Negative feedback and unread message",
                    value: "feedbackIsNegativeWithMessageUnread"
                }
            ],
            itemTemplate: function(value, item) {
                if (value) {
                    let feedbackHTML = $("<div>");
                    if (value.isPositive) {
                        feedbackHTML.append($("<span>").addClass("text-success fa fa-thumbs-up"));
                    } else {
                        feedbackHTML.append($("<span>").addClass("text-danger fa fa-thumbs-down"));
                    }

                    if (value.message) {
                        feedbackHTML.append("&nbsp;");
                        // Stores message in the html element, not ideal, but allows one handler to be written for viewing all feedback messages
                        let feedbackMessageHTML = $("<span>").attr("data-id", item.id).attr("data-message", value.message).attr("data-created-on", new Date(value.createdOn).toLocaleString()).addClass("feedback-message");

                        if (value.messageRead) {
                            feedbackMessageHTML.addClass("fa fa-envelope-open feedback-message-read");
                        } else {
                            feedbackMessageHTML.addClass("fa fa-envelope feedback-message-unread");
                        }

                        feedbackHTML.append(feedbackMessageHTML);
                    }


                    return feedbackHTML.html();
                } else {
                    return "";
                }
            }
        },
        createdOn: { name: "createdOn", title: "Time", type: "text", width: "8em", filtering: false, itemTemplate: function(value) {
            return new Date(value).toLocaleString('en-GB');
        }},
        compare: { title: "Compare", type: "text", align: "center", filtering: false, sorting: false, itemTemplate: function(value, item) {
            let isSelected = selectedRecords.has(item.id);
            return $("<input>").attr("id", `compare-checkbox-${item.id}`)
                    .attr("onclick", `clickCompareCheckbox("${item.id}")`).attr("type", "checkbox").prop("checked", isSelected);
        }}
    });

    const serviceCheckColumnsDefault = Object.freeze([
        "status",
        "carriageType",
        "carriageFNN",
        "deviceName",
        "serviceType",
        "createdBy",
        "siiamCases",
        "serviceCentralIncidents"
    ]);

    $(document).ready(function () {
        let currentFilter = null;

        $("#historyRefresh").click(function() {
            setButtonSpinner($("#historyRefresh"), $("<span>").addClass("fas fa-sync"), true);
            $("#serviceCheckGrid").jsGrid("loadData");
        });

        $("#compare").on('click', function() {
            if (selectedRecords.size < 2 || selectedRecords.size > 3) {
                alert("Only 2 or 3 results can be compared, please select only 2 or 3 records.")
            } else if (selectedRecords.size == 2) {
                $(location).attr("href", "/compareResults/view?" + $.param({
                    id1: Array.from(selectedRecords)[0],
                    id2: Array.from(selectedRecords)[1]
                }));
            } else if (selectedRecords.size == 3) {
                $(location).attr("href", "/compareResults/view?" + $.param({
                    id1: Array.from(selectedRecords)[0],
                    id2: Array.from(selectedRecords)[1],
                    id3: Array.from(selectedRecords)[2]
                }));
            }
        });

        $("#numRecords").select2({
            minimumResultsForSearch: -1,
            width: "resolve"
        });

        $("#numRecords").on('select2:select', function (e) {
            $("#serviceCheckGrid").jsGrid("option", "pageSize", parseInt($("#numRecords").val()));
        });

        let startDateServiceCheck = localStorage.getItem("startDateServiceCheck");
        let endDateServiceCheck = localStorage.getItem("endDateServiceCheck");
        let startDate = null;
        let endDate = null;

        if (isNaN(Date.parse(startDateServiceCheck))) {
            startDate = new Date();
            startDate.setMonth(startDate.getMonth() - 1);
        } else {
            startDate = new Date(startDateServiceCheck);
        }

        if (!isNaN(Date.parse(endDateServiceCheck))) {
            endDate = new Date(endDateServiceCheck);
        }

        startDatePicker = flatpickr("#startDate", {
            defaultDate: startDate,
            enableTime: true,
            enableSeconds: true,
            dateFormat: "d/m/Y h:i:S K",
            position: "below",
            onChange: function(selectedDates, dateStr, instance) {
                if (Array.isArray(selectedDates) && selectedDates[0] instanceof Date) {
                    localStorage.setItem("startDateServiceCheck", selectedDates[0].toISOString());
                }
            }
        });

        endDatePicker = flatpickr("#endDate", {
            defaultDate: endDate,
            enableTime: true,
            enableSeconds: true,
            dateFormat: "d/m/Y h:i:S K",
            position: "below",
            onChange: function(selectedDates, dateStr, instance) {
                if (Array.isArray(selectedDates) && selectedDates[0] instanceof Date) {
                    localStorage.setItem("endDateServiceCheck", selectedDates[0].toISOString());
                }
            }
        });

        $("#endDateClear").click(function() {
            endDatePicker.clear();
            localStorage.removeItem("endDateServiceCheck");
        });

        $("#resetDates").click(function() {
            let defaultDate = new Date();
            defaultDate.setMonth(defaultDate.getMonth() - 1);

            startDatePicker.setDate(defaultDate);
            endDatePicker.clear();

            localStorage.removeItem("startDateServiceCheck");
            localStorage.removeItem("endDateServiceCheck");
        });

        let columnSelectServiceCheck = localStorage.getItem("columnSelectServiceCheck");
        let initialColumns = serviceCheckColumnsDefault;
        let initialFields = [];

        if (columnSelectServiceCheck) {
            try {
                let columns = JSON.parse(columnSelectServiceCheck);
                if (Array.isArray(columns)) {
                    initialColumns = columns;
                }
            } catch(error) {
                // Failed to parse column list from local storage, will continue
            }
        }

        initialFields.push(serviceCheckColumnFields.fnn);
        for (let column of initialColumns) {
            if (serviceCheckColumnFields[column]) {
                initialFields.push(serviceCheckColumnFields[column]);
            }
        }
        initialFields.push(serviceCheckColumnFields.createdOn);
        initialFields.push(serviceCheckColumnFields.compare);

        $("#serviceCheckGrid").jsGrid({
            width: "100%",
            height: "100%",
            filtering: true,
            inserting: false,
            editing: false,
            sorting: true,
            autoload: true,
            paging: true,
            pageLoading: true,
            pageSize: parseInt($("#numRecords").val()),
            pageIndex: 1,
            editButton: false,
            deleteButton: false,
            controller: {
                loadData: function(filter) {
                    const startDateVal = $("#startDate").val();
                    const endDateVal = $("#endDate").val();

                    // Clears selected records as currently selected records may no longer by in the list
                    if (currentFilter === null) {
                        currentFilter = {};
                        for (let key in filter) {
                            if (!['pageSize', 'pageIndex'].includes(key)) {
                                currentFilter[key] = filter[key];
                            }
                        }

                        currentFilter.startDate = startDateVal;
                        currentFilter.endDate = endDateVal;
                    }

                    let currentFilterDiff = false;

                    for (let key in currentFilter) {
                        if (!['startDate', 'endDate'].includes(key) && currentFilter[key] !== filter[key]) {
                            currentFilterDiff = true;
                        }
                    }
                    if (currentFilter.startDate !== startDateVal) {
                        currentFilterDiff = true;
                    }
                    if (currentFilter.endDate !== endDateVal) {
                        currentFilterDiff = true;
                    }

                    if (currentFilterDiff) {
                        for (let key in filter) {
                            if (!['pageSize', 'pageIndex'].includes(key)) {
                                currentFilter[key] = filter[key];
                            }
                        }

                        currentFilter.startDate = startDateVal;
                        currentFilter.endDate = endDateVal;

                        selectedRecords.clear();
                        $("#displaySelectedCount").text(`${selectedRecords.size} records selected`);
                    }

                    let data = $.Deferred();

                    let queryData = setupQueryDataFromFilter(filter);
                    $.ajax({
                        cache: false,
                        type: "GET",
                        url: "/serviceCheck/history",
                        data: queryData,
                        success: function (response) {
                            let total = response.metadata && response.metadata.pagination ? response.metadata.pagination.total : 0;
                            data.resolve({
                                data: response.results,
                                itemsCount: total
                            });

                            let startDate = "";
                            let endDate = "";

                            if (response.metadata) {
                                if (response.metadata.startDate) {
                                    startDate = new Date(response.metadata.startDate).toLocaleString('en-GB');
                                }
                                if (response.metadata.endDate) {
                                    endDate = ` To: ${new Date(response.metadata.endDate).toLocaleString('en-GB')}`;
                                }
                            }

                            $("#displayServiceCheckTotal").text(`${total} records total (From: ${startDate}${endDate})`);
                        },
                        error: function (xhr, status, e) {
                            // Not the best solution, puts nothing in the table
                            data.resolve({
                                data: [],
                                itemsCount: 0
                            });
                        },
                        complete: function(_) {
                            setButtonSpinner($("#historyRefresh"), $("<span>").addClass("fas fa-sync"), false);
                        }
                    });

                    return data.promise();
                }
            },
            fields: initialFields
        });

        $("#columnSelect").select2();
        $("#columnSelect").val(initialColumns);
        $("#columnSelect").trigger("change");

        $("#columnSelect").on("change", function() {
            let currColumns = $("#columnSelect").val();
            localStorage.setItem("columnSelectServiceCheck", JSON.stringify(currColumns));

            let fields = [];
            fields.push(serviceCheckColumnFields.fnn);

            for (let column of currColumns) {
                if (serviceCheckColumnFields[column]) {
                    fields.push(serviceCheckColumnFields[column]);
                }
            }

            fields.push(serviceCheckColumnFields.createdOn);
            fields.push(serviceCheckColumnFields.compare);
            $("#serviceCheckGrid").jsGrid("option", "fields", fields);
        });


        $("#columnSelectReset").click(function() {
            $("#columnSelect").val(serviceCheckColumnsDefault);
            $("#columnSelect").trigger("change");
        });
    });

    function setupQueryDataFromFilter(filter) {
        let limit = filter.pageSize;
        let offset = (filter.pageIndex - 1)*filter.pageSize;
        let queryData = {};

        queryData.limit = limit;
        queryData.offset = offset;

        if (filter.sortField) {
            queryData.sort = filter.sortField;
            queryData.order = filter.sortOrder;
        }

        filter.createdBy = currentUser;

        if (startDatePicker && Array.isArray(startDatePicker.selectedDates) && startDatePicker.selectedDates[0] instanceof Date) {
            queryData.startDate = startDatePicker.selectedDates[0].toISOString();
        }

        if (endDatePicker && Array.isArray(endDatePicker.selectedDates) && endDatePicker.selectedDates[0] instanceof Date) {
            queryData.endDate = endDatePicker.selectedDates[0].toISOString();
        }

        let filterEqualFields = [
            "fnn",
            "status",
            "suite",
            "startMethod",
            "carriageType",
            "carriageFNN",
            "billingFNN",
            "MDNFNN",
            "CIDN",
            "nbnAccessType",
            "nbnId",
            "createdBy",
            "serviceType",
            "siiamCases",
            "serviceCentralIncidents",
            "feedback"
        ];

        let filterLikeFields = [
            "deviceName"
        ];

        filterEqualFields.forEach(field => {
            if (filter[field] !== undefined && filter[field] !== '') {
                queryData[field] = filter[field];
            }
        });

        filterLikeFields.forEach(field => {
            if (filter[field] !== undefined && filter[field] !== '') {
                queryData[field] = {
                    like: filter[field]
                };
            }
        });

        return queryData;
    }

    function clickCompareCheckbox(id) {
        if ($(`#compare-checkbox-${id}`).prop("checked")) {
            selectedRecords.add(id);
        } else {
            selectedRecords.delete(id);
        }

        $("#displaySelectedCount").text(`${selectedRecords.size} records selected`);
    }
</script>

<%- include('footer', {}); %>
