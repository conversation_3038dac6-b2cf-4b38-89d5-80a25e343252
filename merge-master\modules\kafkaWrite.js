'use strict';

import Kafka from 'node-rdkafka';

import '../config/config.js';
import logger from '../modules/logger.js';


//Our producer with its Kafka brokers
//This call returns a new writable stream to our topic 'topic-name'

function createKafkaProducer() {
  const brokerList = global.gConfig.kafkaBrokerList;
  if (!brokerList) {
      logger.info(`Kafka client: broker list null or empty`);
      return null;
  }

  try {
    return Kafka.Producer({
      'metadata.broker.list': global.gConfig.kafkaBrokerList,
      'security.protocol': 'SASL_SSL',
      'ssl.ca.location': global.gConfig.kafkaCaLocation,
      'sasl.mechanisms': 'GSSAPI',
      'sasl.kerberos.service.name': 'kafka',
      'sasl.kerberos.keytab': global.gConfig.kafkaKeyTab,
      'enable.ssl.certificate.verification': false,
      'sasl.kerberos.principal': global.gConfig.kafkaPrincipal,
      'debug': 'all',
      'dr_cb': true
    });
  } catch (error) {
    logger.error(`Kafka client: error creating client, ${error.toString()}`);
    return null;
  }
}

export function sendServiceCheckMessage(SCData, key) {
  if (producer) {
    try {
      // Connect to the broker manually
      if (!producer._isConnected) {
        producer.connect();
        logger.info('Kafka client: resent connection request')
        // Wait for the ready event before proceeding producer.produce(topic, partition (Opt), msg (must be buffer), key (Opt), timestamp (default 0), opaque (opt))
        producer.on('ready', () => {
          logger.info('Kafka client: connected');
          produceMessage(SCData, key)
        });
      } else {
        produceMessage(SCData, key);
      }
    } catch(error) {
      logger.error(`Kafka client: error while connecting to kafka brokers, ${error.toString()}`);
    }
  }
}

function produceMessage(SCData, key) {
  if (producer) {
    try {
      producer.produce('cmn-str-obs-merge', null, Buffer.from(SCData), key, Date.now());
      producer.flush(500, function() {
        logger.info('Kafka client: event flushed');
      });
    } catch (error) {
      logger.error(`Kafka client: error while sending message, ${error.toString()}`);
    }
  }
}
//send message

const producer = createKafkaProducer();

if (producer) {
  logger.info('Kafka client: initiate connection request');
  producer.connect();

  // Any errors we encounter, including connection errors
  producer.on('event.error', function(error) {
    logger.error(`Kafka client: event error, ${error}`);
  });

  producer.setPollInterval(100);

  producer.on('delivery-report', function(error, report) {
    logger.info(`Kafka client: delivery report, ${report}`);
  });

  sendServiceCheckMessage('Merge proccess restarted', global.gConfig.env);
}

export default {
  sendServiceCheckMessage
};
