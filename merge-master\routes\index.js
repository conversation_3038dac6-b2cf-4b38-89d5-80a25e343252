import express from 'express';

import auth from '../modules/auth.js';
import { AuthorizationRoles } from '../modules/enumerations.js';

const router = express.Router();

/* GET home page. */
router.get('/', function(req, res) {
    res.render('home', { title: 'Merge Home Page', user: req.user });
});


/* GET history page. */
router.get('/my/info', auth.authorizeForRoles([
    AuthorizationRoles.readOnly,
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.apiAccess,
    AuthorizationRoles.levelLead,
    AuthorizationRoles.offshoreAccess
], true), function(req, res) {
    res.render('myInfo', { title: 'My User Info', user: req.user });
});

export default router;
