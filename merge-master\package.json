{"name": "merge", "version": "7.6.2", "private": true, "type": "module", "scripts": {"commit": "cz", "start": "DEBUG=merge:* node ./bin/merge.js", "test": "LOG_LEVEL=silent ./node_modules/mocha/bin/_mocha --require tests/global_hooks.js --exit", "migrate": "./node_modules/migrate/bin/migrate --matches \"*-*.js\" --store=\"./migrations/migrationstore\"", "release": "standard-version"}, "dependencies": {"aes-js": "^3.1.2", "ajv": "^8.12.0", "argparse": "^2.0.1", "async": "^3.2.5", "axios": "^1.6.7", "axios-cookiejar-support": "^5.0.0", "body-parser": "^1.20.2", "compressing": "^1.10.0", "connect-mongo": "^5.1.0", "cookie-parser": "^1.4.6", "cron": "^3.1.6", "cz-conventional-changelog": "^3.3.0", "debug": "^4.3.4", "dedent": "^1.5.1", "ejs": "^3.1.9", "escape-string-regexp": "^5.0.0", "express": "^4.18.2", "express-ntlm": "^2.7.0", "express-prom-bundle": "^7.0.0", "express-session": "^1.18.0", "express-validator": "^7.0.1", "flatpickr": "^4.6.13", "hal": "^1.2.0", "http-cookie-agent": "^6.0.1", "http-errors": "^2.0.0", "isolated-vm": "^4.7.2", "json2csv": "^5.0.7", "jsonwebtoken": "^9.0.2", "ldapauth-fork": "^5.0.5", "ldapjs": "^3.0.7", "libphonenumber-js": "^1.10.57", "lodash": "^4.17.21", "marked": "^12.0.0", "migrate": "^2.1.0", "minimist": "^1.2.8", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "mongoose": "^8.2.0", "mysql2": "^3.9.7", "node-json2html": "^3.0.0", "node-rdkafka": "^2.18.0", "nodemailer": "^6.9.9", "nodemon": "^3.1.0", "on-finished": "^2.4.1", "on-headers": "^1.0.2", "passport": "^0.7.0", "passport-http": "^0.3.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pino": "^8.19.0", "pino-pretty": "^10.3.1", "prom-client": "^15.1.0", "promise": "^8.3.0", "query-string": "^9.0.0", "serve-favicon": "^2.5.0", "socket.io": "^4.7.4", "socket.io-client": "^4.8.1", "split-file": "^2.3.0", "string-sanitizer": "^2.0.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "tough-cookie": "^4.1.3", "tunnel": "^0.0.6", "unixcrypt": "^1.2.0", "url-join": "^5.0.0", "uuid": "^9.0.1"}, "devDependencies": {"@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.2", "chai": "^4.4.1", "chai-as-promised": "^7.1.1", "chai-datetime": "^1.8.0", "chai-http": "^4.4.0", "chai-like": "^1.1.1", "cookie": "^0.6.0", "deep-equal-in-any-order": "^2.0.6", "husky": "^9.0.11", "mocha": "^10.3.0", "mongodb-memory-server": "^10.1.3", "sinon": "^17.0.1", "standard-version": "^9.5.0", "superagent-defaults": "^0.1.14", "supertest": "^6.3.4"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog", "disableSubjectLowerCase": true}}}