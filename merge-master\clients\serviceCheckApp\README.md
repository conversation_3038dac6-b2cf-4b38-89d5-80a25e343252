# ServiceCheck App

<!-- To update: New UI link -->
This is a React application built using Vite with TypeScript for static type-checking. The app is served through an **Express.js backend** and is accessible at the `/serviceCheck/new` route.

---

## Table of Contents

- [Features](#features)
- [Getting Started](#getting-started)
- [Development Workflow](#development-workflow)
- [Production Workflow](#production-workflow)
- [Additional Notes](#additional-notes)

---

## Features

- React with TypeScript for building modern and maintainable UI components.
- SCSS support for styling.
- Express.js backend serving the app at `/serviceCheck/new`.
- Vite for fast builds and hot module replacement (HMR).

---

## Getting Started

### Prerequisites

- Node.js
- npm or yarn installed globally

### Installation

1. Install dependencies:

    ```bash
    npm install
    ```

2. Build the frontend:
    ```bash
    npm run build
    ```

---

## Development Workflow

This application does **not run on the default Vite dev server**. Instead, the app is served through the Express.js backend at `/serviceCheck/new`. The Vite dev server is used separately for development tasks like hot module replacement (HMR) and rebuilds.

### Steps for Development:

1. Start the Vite development server in one terminal:

    ```bash
    npm run dev
    ```

    - This will enable hot module reloading (HMR) and other development features provided by Vite.
    - **Note**: The Vite dev server runs on a separate port (default is `5173`) and does not serve the app directly.

2. Ensure the Express.js server is running on port `3004` in another terminal:
    - The Express server serves the application at `/serviceCheck/new`.

### Why Two Servers?

- The Express.js server serves the app to ensure the correct API and route structure for production-like behavior.
- The Vite dev server is used in parallel to handle live reload, faster builds, and efficient development features.
- This separation ensures that development tools like HMR can work independently while the app remains accessible at `/serviceCheck/new`.

---

## Production Workflow

In production, the app is served entirely through the Express.js backend. The production build outputs static files to the `dist` folder, which the server serves as follows:

1. When you run the production backend, the Express.js server will:

    - Serve static assets like JavaScript, CSS, and images from the `dist` folder.
    - Handle routes such as `/serviceCheck/new` to load the React app.

2. To build the app for production:
    ```bash
    npm run build
    ```
    - This command generates optimized static assets in the `dist` folder.

---

## Additional Notes

- The app is accessible at `http://localhost:3004/serviceCheck/new`.
- The SCSS files are automatically compiled to CSS during the build process.

