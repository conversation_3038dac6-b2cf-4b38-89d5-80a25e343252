import mongoose from 'mongoose';
import regex from '../../modules/helpers/regex.js';


const authKeyDbSchema = new mongoose.Schema({
    authKeyHeader: { type: String, minLength: 1, required: true },
    authKeyName: { type: String, minLength: 1, required: true }
}, { _id: false });

const resultAPISchema = new mongoose.Schema({
    name: { type: String, default: null, required: true },
    timeout: { type: Number, min: 1, default: 60000 },
    uri: { type: String, default: null, required: true },
    queryParams: { type: String, default: '' },
    header: { type: String, default: '' },
    authKeyDb: {
        type: [{
            type: authKeyDbSchema
        }],
        default: []
    },
    parseResponse: { type: String, default: '' },
    transform: { type: mongoose.Schema.Types.Mixed, default: null }
}, { _id: false });

const asyncPollSchema = new mongoose.Schema({
    name: { type: String, default: null, minLength: 1, required: true },
    interval: { type: Number, default: null },
    uri: { type: String, default: '' },
    queryParams: { type: String, default: '' },
    header: { type: String, default: '' },
    authKeyDb: {
        type: [{
            type: authKeyDbSchema
        }],
        default: []
    },
    timeout: { type: Number, min: 1, default: 60000 },
    parseResponse: { type: String, default: '' },
    doneCondition: { type: String, default: null, required: true },
    errorCondition: { type: String, default: '' },
    transform: { type: mongoose.Schema.Types.Mixed, default: null },
    resultAPI: { type: resultAPISchema, default: null }
}, { _id: false });

const asyncCallbackSchema = new mongoose.Schema({
    timeout: { type: Number, min: 1, default: 60000 },
    enabledEnvs: {
        type: [{
            type: String,
            enum: [
                'localDev',
                'dev',
                'testing',
                'stage',
                'prd'
            ]
        }],
        default: [],
        validate: {
            validator: function(env) {
                return env.length === new Set(env).size;
            },
            message: 'array should only contain unique string values'
        }
    },
    idField: { type: String, default: '' },
    parseResponse: { type: String, default: '' },
    doneCondition: { type: String, default: null, required: true },
    errorCondition: { type: String, default: '' }
}, { _id: false });

const apiSchema = new mongoose.Schema({
    name: { type: String, default: null, validate: regex.apiName, unique: true, required: true, immutable: true },
    active: { type: Boolean, default: false },
    description: { type: String, default: '' },
    apiType: { type: String, enum: [ 'rest', 'function' ], default: 'rest' },
    method: { type: String, enum: [ null, 'post', 'get' ], default: 'get' },
    parameters: {
        type: [{
            type: String
        }],
        default: [],
        validate: {
            validator: function(parameters) {
                return parameters.length === new Set(parameters).size;
            },
            message: 'array should only contain unique string values'
        }
    },
    baseUrl: { type: String, default: '' },
    uri: { type: String, default: '' },
    queryParams: { type: String, default: '' },
    body: { type: String, default: '' },
    header: { type: String, default: '' },
    authKeyDb: {
        type: [{
            type: authKeyDbSchema
        }],
        default: []
    },
    timeout: { type: Number, min: 1, default: 300000 },
    parseResponse: { type: String, default: null },
    pollCondition: { type: String, default: null },
    proxyRequired: { type: Boolean, default: false },
    useCookies: { type: Boolean, default: false },
    tlsMinVersion: { type: String, enum: [ null, 'TLSv1.3', 'TLSv1.2', 'TLSv1.1', 'TLSv1' ], default: null },
    errorCondition: { type: String, default: 'response.data && typeof response.data.error !== \'undefined\'' },
    asyncPoll: { type: asyncPollSchema, default: null },
    asyncCallback: { type: asyncCallbackSchema, default: null },
    masslCertificateName: { type: String, default: null },
    wikiPage: { type: String, default: '' },
    createdBy: { type: String, required: true, immutable: true, default: 'unknown' },
    createdOn: { type: Date, required: true, immutable: true, default: Date.now }
});

apiSchema.methods.toJSON = function() {
    let obj = this.toObject();
    delete obj._id;
    delete obj.__v;
    return obj;
}


export default mongoose.model('api', apiSchema);
