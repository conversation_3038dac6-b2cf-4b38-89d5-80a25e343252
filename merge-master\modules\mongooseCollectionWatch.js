/**
 * This module sets up watchers on certain collections in the database
 * If an error occurs with the change stream, a new change stream will be created as the original change stream
 * will not receive events on collection change after errors such as a replicaset disconnect
 *
 * Only applies to environments that use connect to MongoDB instances configured as a replica set
 */

import callbackEmitter from './callbackEmitter.js';
import logger from './logger.js';
import BannerMessage from '../db/model/bannerMessage.js';
import CallbackResponse from '../db/model/callbackResponse.js';


export default function(app) {
    let callbackResponseChangeStream = null;
    function setupCallbackResponseWatcher() {
        // Attempts to listen on the callback response collection for changes
        // $changeStream only works on connections to a Mongodb replicaset
        try {
            // Removes any listeners from the current change stream instance if it exists
            if (callbackResponseChangeStream) {
                callbackResponseChangeStream.removeAllListeners();
            }

            callbackResponseChangeStream = CallbackResponse.watch().on('change', async function(change) {
                try {
                    let callbackResponse = await CallbackResponse.findOne(change.documentKey);
                    if (callbackResponse) {
                        callbackEmitter.emit('response', callbackResponse.data);
                    }
                } catch(error) {
                    logger.error(`Collection watch (callback response): handling change error, ${error.toString()}`);
                }
            }).on('error', (error) => {
                logger.error(`Collection watch (callback response): change stream error, ${error.toString()}`);

                setupCallbackResponseWatcher();
            });
            logger.info(`Collection watch (callback response): change stream created`);
        } catch(error) {
            logger.error(`Collection watch (callback response): setup error, ${error.toString()}`);
        }
    }

    let bannerMessageChangeStream = null;
    function setupBannerMessageWatcher() {
        // Attempts to listen on the banner message collection for changes
        // $changeStream only works on connections to a Mongodb replicaset
        try {
            if (bannerMessageChangeStream) {
                bannerMessageChangeStream.removeAllListeners();
            }

            bannerMessageChangeStream = BannerMessage.watch().on('change', async function(change) {
                try {
                    let bannerMessage = await BannerMessage.findOne({});
                    if (bannerMessage && bannerMessage.message) {
                        app.locals.bannerMessage = bannerMessage.message;
                    } else {
                        app.locals.bannerMessage = null;
                    }
                } catch(error) {
                    logger.error(`Collection watch (banner message): handling change error, ${error.toString()}`);
                }
            }).on('error', (error) => {
                logger.error(`Collection watch (banner message): change stream error, ${error.toString()}`);
                setupBannerMessageWatcher();
            });

            logger.info(`Collection watch (banner message): change stream created`);
        } catch(error) {
            logger.error(`Collection watch (banner message): setup error, ${error.toString()}`);
        }
    }

    setupCallbackResponseWatcher();
    setupBannerMessageWatcher();
}
