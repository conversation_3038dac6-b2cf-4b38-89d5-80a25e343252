
import { <PERSON>ronJob } from 'cron';
import nodemailer from 'nodemailer';

import logger from './logger.js';
import { getRecords } from './serviceCheck.js';
import { ServiceCheckStreamRead } from './helpers/csv.js';


let nodeMailerTransporter = null;
const reportEmailBody = 'If there are any issues with this report, please contact the <a href="mailto:<EMAIL>">Merge team</a>';


export async function sendServiceCheckRecordReportEmail(sendDate) {
    const reportConfig = global.gConfig.serviceCheckRecordReportEmail;
    logger.info(`Service check record report: sending to ${reportConfig.emailTo}`);

    try {
        let endDate = new Date(sendDate);
        endDate.setHours(0, 0, 0, 0);
        let startDate = new Date(endDate);
        startDate.setDate(startDate.getDate() - 1);

        let serviceCheckQueryPromise = getRecords({
            limit: 50000,
            offset: 0,
            startDate,
            endDate,
            sort: 'createdOn',
            order: 'desc'
        }, false);

        let cursor = serviceCheckQueryPromise.cursor();
        let serviceCheckStream = new ServiceCheckStreamRead(cursor);

        let serviceCheckReadPromise = new Promise((resolve, reject) => {
            const chunks = [];
            serviceCheckStream.on('data', (data) => {
                chunks.push(data);
            });

            serviceCheckStream.on('error', (error) => {
                logger.error(`Service check record report: error obtaining record history in stream, ${error.toString()}`);
                reject(error);
            });

            serviceCheckStream.on('end', () => {
                resolve(Buffer.concat(chunks).toString('utf-8'));
            });
        });

        const serviceCheckRecordCsv = await serviceCheckReadPromise;
        const messageInfo = await nodeMailerTransporter.sendMail({
            from: reportConfig.emailFrom,
            to: reportConfig.emailTo,
            subject: `Merge Service Check Records (${startDate.toLocaleDateString('en-AU')})`,
            attachments: [{
                filename: `Service_check_history_${startDate.toLocaleDateString('en-AU').replaceAll('/', '_')}.csv`,
                content: serviceCheckRecordCsv
            }],
            html: reportEmailBody
        });

        logger.info(`Service check record report: sent e-mail with ID ${messageInfo.messageId}`);
    } catch(error) {
        logger.error(`Service check record report: error sending e-mail, ${error.toString()}`);
    }
}

async function sendReportJob() {
    await sendServiceCheckRecordReportEmail(new Date());
}


if (global.gConfig.serviceCheckRecordReportEmail) {
    const reportConfig = global.gConfig.serviceCheckRecordReportEmail;

    if (reportConfig.sendHost === global.gHostname) {
        nodeMailerTransporter = nodemailer.createTransport({
            host: reportConfig.smtpHost,
            port: reportConfig.smtpPort,
            secure: false,
            requireTLS: true,
            tls: {
                rejectUnauthorized: false
            },
            auth: {
                user: global.gConfig.robotAccount01Username,
                pass: global.gConfig.robotAccount01Password
            }
        });

        logger.info('Service check record report: started cron job');

        CronJob.from({
            cronTime: global.gConfig.serviceCheckRecordReportEmail.cron,
            onTick: sendReportJob,
            start: true
        });
    }
}

