
import mongoose from 'mongoose';

const callbackResponseSchema = new mongoose.Schema({
    data: {
        type: String,
        get: function(data) {
            try {
                return JSON.parse(data);
            } catch(error) {
                return data;
            }
        },
        set: function(data) {
            return JSON.stringify(data);
        }
    }
},
{
    timestamps: true
});

// Callback responses do not really need to be stored, after the document is saved
// it should trigger a change event on the change stream which will be read and handled

// Disabled expiry for testing environment
//callbackResponseSchema.index({ createdAt: 1 }, { expireAfterSeconds: 60 });

export default mongoose.model('callbackresponse', callbackResponseSchema);
