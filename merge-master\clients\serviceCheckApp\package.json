{"name": "clients", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@babel/runtime": "^7.26.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@tanstack/react-query": "^5.61.5", "axios": "^1.7.8", "dompurify": "^3.2.3", "react": "19.0.0", "react-dom": "19.0.0", "react-error-boundary": "^5.0.0", "react-router": "^7.0.1", "react-svg-pan-zoom": "^3.13.1", "sass": "^1.81.0", "socket.io-client": "^4.8.1", "svgo": "^3.3.2", "vite-plugin-checker": "^0.8.0"}, "devDependencies": {"@able/react": "1.16.0", "@eslint/js": "^9.15.0", "@types/axios": "^0.14.4", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "@types/react-svg-pan-zoom": "^3.3.9", "@vitejs/plugin-react": "^4.3.4", "axios-mock-adapter": "^2.1.0", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "typescript": "~5.7.2", "typescript-eslint": "^8.16.0", "vite": "^6.0.1"}}