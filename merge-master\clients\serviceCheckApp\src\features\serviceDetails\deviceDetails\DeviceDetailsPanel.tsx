import classNames from 'classnames'
import { DetailsPanel } from '../../../components/detailsPanel/DetailsPanel'

interface DeviceDetailsPanelProps {
    className?: string
    children: React.ReactElement
}

export const DeviceDetailsPanel = ({
    children,
    className,
}: DeviceDetailsPanelProps) => (
    <DetailsPanel label="Device Details" className={classNames(className)}>
        {children}
    </DetailsPanel>
)
