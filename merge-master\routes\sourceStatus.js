
import express from 'express';

import auth from '../modules/auth.js';
import { AuthorizationRoles } from '../modules/enumerations.js';

const router = express.Router();

router.get('/', auth.authorizeForRoles([
    AuthorizationRoles.readOnly,
    AuthorizationRoles.level0,
    AuthorizationRoles.level1,
    AuthorizationRoles.level2,
    AuthorizationRoles.level3,
    AuthorizationRoles.commandAccess,
    AuthorizationRoles.isDeveloper,
    AuthorizationRoles.isAdmin,
    AuthorizationRoles.levelLead
], true), function (req, res) {
    res.render('sourceStatus', { title: 'Source Status', user: req.user });
});


export default router;
