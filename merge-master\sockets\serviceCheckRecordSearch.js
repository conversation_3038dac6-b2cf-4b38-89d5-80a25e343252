
import ivm from 'isolated-vm';
import _ from 'lodash';

import { InputTypes, ProductTypes } from '../modules/enumerations.js';
import { ServiceCheckSearchError } from '../modules/error.js';
import { findAndPopulate, getRecords } from '../modules/serviceCheck.js';

export function socketListen(socket) {
    let controller;

    async function serviceCheckRecordSearch(searchExpression, filters, maxRecords, abortControllerSignal) {
        const isolate = new ivm.Isolate({
            memoryLimit: 64
        });

        let cursor = null;
        let recordCount = 0;
        let recordsFound = 0;
        let searchCompleteMessage = null;

        try {
            const [serviceCheckQuery, serviceCheckCountQuery] = getRecords(filters, true);

            const serviceCheckCount = _.get(await serviceCheckCountQuery, [0, 'total'], 0);
            socket.emit('serviceCheckRecordSearch:recordCountTotal', serviceCheckCount);

            cursor = serviceCheckQuery.cursor();

            for await (let serviceCheck of cursor) {
                abortControllerSignal.throwIfAborted();

                const record = await findAndPopulate(serviceCheck.id);
                if (record) {
                    const serviceCheckRecordContext = await isolate.createContext();

                    await serviceCheckRecordContext.global.set('data', new ivm.ExternalCopy(record.toJSON()).copyInto());
                    let rulesDataReference = serviceCheckRecordContext.global.getSync('data').getSync('rulesData');
                    let sourcesDataReference = serviceCheckRecordContext.global.getSync('data').getSync('sourcesData');
                    await serviceCheckRecordContext.global.set('r', rulesDataReference instanceof ivm.Reference ? rulesDataReference.derefInto() : new ivm.ExternalCopy({}).copyInto());
                    await serviceCheckRecordContext.global.set('s', sourcesDataReference instanceof ivm.Reference ? sourcesDataReference.derefInto() : new ivm.ExternalCopy({}).copyInto());
                    await serviceCheckRecordContext.global.set('InputTypes', new ivm.ExternalCopy(InputTypes).copyInto());
                    await serviceCheckRecordContext.global.set('ProductTypes', new ivm.ExternalCopy(ProductTypes).copyInto());

                    // Currently only sets specific functions from mergeLib
                    // TODO: implement dynamic loading of all functions from merge lib into the isolate context
                    await serviceCheckRecordContext.global.set('checkIsValidPhoneNumber', function(...args) {
                        return mergeLib.checkIsValidPhoneNumber(...args);
                    });
                    await serviceCheckRecordContext.global.set('checkIsMobilePhoneNumber', function(...args) {
                        return mergeLib.checkIsMobilePhoneNumber(...args);
                    });
                    await serviceCheckRecordContext.global.set('formatE164PhoneNumber', function (...args) {
                        return mergeLib.formatE164PhoneNumber(...args);
                    });
                    await serviceCheckRecordContext.global.set('formatNationalPhoneNumber', function (...args) {
                        return mergeLib.formatNationalPhoneNumber(...args);
                    });

                    try {
                        const searchExpressionResult = await serviceCheckRecordContext.eval(searchExpression, { copy: true, timeout: 2000 });
                        if (searchExpressionResult && !abortControllerSignal.aborted) {
                            recordsFound++;
                            socket.emit('serviceCheckRecordSearch:match', {
                                ...serviceCheck,
                                searchExpressionResult: searchExpressionResult
                            });
                        }
                    } catch(error) {
                        if (error.name === 'AbortError') {
                            throw error;
                        } else {
                            throw new ServiceCheckSearchError(`Error evaluating search expression for service check with ID: ${serviceCheck.id}`, { cause: error });
                        }
                    } finally {
                        serviceCheckRecordContext.release();
                    }
                }
                recordCount++;
                socket.emit('serviceCheckRecordSearch:recordCountIteration', recordCount);

                if (recordsFound >= maxRecords) {
                    searchCompleteMessage = 'Search complete, max record limit reached';
                    break;
                }
            }

            if (serviceCheckCount === 0) {
                searchCompleteMessage = 'No records found, check filters and time range';
            } else if (recordCount === serviceCheckCount) {
                searchCompleteMessage = 'Search complete, finished iterating through all service check records';
            }
        } catch(error) {
            if (error.name === 'AbortError') {
                searchCompleteMessage = null;
            } else {
                socket.emit('serviceCheckRecordSearch:error', error.toString());
            }
        } finally {
            if (typeof cursor?.close === 'function') {
                await cursor.close();
            }

            if (!isolate.isDisposed) {
                isolate.dispose();
            }

            controller = null;
            socket.emit('serviceCheckRecordSearch:complete', searchCompleteMessage);
        }
    }


    socket.on('serviceCheckRecordSearch:start', async function(data) {
        // Currently the mechanism to ensure the service check record search
        // can't be run more than once per socket.io connection is a variable
        // This can be improved in the future
        if (controller) {
            return;
        }

        const searchExpression = data?.searchExpression;
        const maxRecords = data?.maxRecords;
        const filters = data?.filters;

        if (typeof searchExpression !== 'string' || !searchExpression) {
            socket.emit('serviceCheckRecordSearch:inputError', 'Search expression must not be empty');
            return;
        }

        if (typeof maxRecords !== 'number' || maxRecords < 0 || maxRecords > 1000) {
            socket.emit('serviceCheckRecordSearch:inputError', 'Invalid max results number');
            return;
        }

        if (!_.isPlainObject(filters)) {
            socket.emit('serviceCheckRecordSearch:inputError', 'Filter format is incorrect');
            return;
        }

        let limit = null;
        let offset = 0;
        let sort = 'createdOn';
        let order = 'desc';

        let fnn = filters.fnn;
        let status = filters.status;
        let suite = filters.suite;
        let carriageType = filters.carriageType;
        let carriageFNN = filters.carriageFNN;
        let createdBy = filters.createdBy;
        let billingFNN = filters.billingFNN;
        let MDNFNN = filters.MDNFNN;
        let CIDN = filters.CIDN;
        let nbnAccessType = filters.nbnAccessType;
        let nbnId = filters.nbnId;
        let serviceType = filters.serviceType;
        let startDate = filters.startDate ? new Date(filters.startDate) : null;
        let endDate = filters.endDate ? new Date(filters.endDate) : null;

        if (startDate === null || (startDate instanceof Date && isNaN(startDate.getTime()))) {
            socket.emit('serviceCheckRecordSearch:inputError', 'Valid start date must be provided');
            return;
        }

        if (endDate instanceof Date && isNaN(endDate.getTime())) {
            socket.emit('serviceCheckRecordSearch:inputError', 'Valid end date must be provided');
            return;
        }

        socket.emit('serviceCheckRecordSearch:started');
        controller = new AbortController();

        await serviceCheckRecordSearch(searchExpression, {
            limit,
            offset,
            sort,
            order,
            fnn,
            status,
            suite,
            carriageType,
            carriageFNN,
            createdBy,
            billingFNN,
            MDNFNN,
            CIDN,
            nbnAccessType,
            nbnId,
            serviceType,
            startDate,
            endDate
        }, maxRecords, controller.signal);
    });

    socket.on('serviceCheckRecordSearch:cancel', async function() {
        if (controller instanceof AbortController) {
            controller.abort();
        }
    });

    socket.on('disconnect', () => {
        if (controller instanceof AbortController) {
            controller.abort();
        }
    });
}
