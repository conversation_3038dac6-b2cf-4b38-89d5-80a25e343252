@use '@able/web/src/index' as able;

.accordionPanel {
    width: 100%;

   

    .accordionHeader {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        background-color: able.color(materialBaseTertiary);
        padding: able.spacing(spacing2x);
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        cursor: pointer;

        .headerContent {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            gap: 1rem;
        }

        .headerActions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
    }

    .accordionContent {
        flex: 1;
        padding: able.spacing(spacing2x);
        display: flex; 
        flex-direction: column;
        gap: able.spacing(spacing1x); 
        min-height: 250px;
        max-height: 500px;
        overflow: auto;
        overscroll-behavior: contain;
    
        &.closed {
            display: none;
        }

        /* Webkit Browsers (Chrome, Edge, Safari) */
        &::-webkit-scrollbar {
            width: 10px; 
            height: 10px; 
        }
      
        &::-webkit-scrollbar-thumb {
          background-color: able.color(materialBaseBrandQuaternary); /* Scrollbar thumb color */
          border-radius: 6px; 
        }
    
        &::-webkit-scrollbar-track {
          background-color: able.color(materialBaseSecondary); /* Track color */
        }
      
        /* Firefox */
        scrollbar-width: thin; 
        scrollbar-color: able.color(materialBaseBrandTertiary) able.color(materialBaseSecondary); /* Thumb and track colors */
    }
}
