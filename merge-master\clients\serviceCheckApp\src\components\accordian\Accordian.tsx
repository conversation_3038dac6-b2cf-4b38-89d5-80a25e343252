import { Surface, TextStyle } from '@able/react'
import classNames from 'classnames'
import React, { useCallback, useState } from 'react'
import { ChevronIcon } from '../chevronIcon/ChevronIcon'
import styles from './Accordian.module.scss'

interface AccordionProps {
    label: string
    children: React.ReactElement
    defaultOpen?: boolean
    overflowHidden?: boolean
    headerContent?: React.ReactElement
}

export const Accordion = ({
    label,
    children,
    defaultOpen,
    overflowHidden,
    headerContent,
}: AccordionProps) => {
    const [isOpen, setIsOpen] = useState(defaultOpen ?? true)

    const togglePanel = useCallback(() => {
        setIsOpen((prev) => !prev)
    }, [])

    return (
        <Surface
            variant="SurfaceLow"
            background="materialBaseSecondary"
            className={styles.accordionPanel}
        >
            <>
                <div className={styles.accordionHeader} onClick={togglePanel}>
                    <div className={styles.headerContent}>
                        <TextStyle
                            element="h2"
                            colour="Default"
                            alias="HeadingD"
                        >
                            {label}
                        </TextStyle>
                        <div className={styles.headerActions}>
                            <div
                                className={styles.headerContent}
                                onClick={(
                                    e: React.MouseEvent<HTMLDivElement>
                                ) => e.stopPropagation()}
                            >
                                {headerContent}
                            </div>

                            <ChevronIcon isOpen={isOpen} />
                        </div>
                    </div>
                </div>

                <div
                    className={classNames(styles.accordionContent, {
                        [styles.closed]: !isOpen,
                        [styles.overflowHidden]: overflowHidden,
                    })}
                >
                    {children}
                </div>
            </>
        </Surface>
    )
}
