import { useCallback, useEffect, useRef, useState } from 'react'
import { useSocketContext } from '../../context/SocketContext'
import { useUser } from '../../context/UserContext'
import { allDatesValid } from '../../features/serviceTest/components/testRunForm/validation'
import { useAppState } from '../../hooks/useAppState'
import {
    TestPackageRequest,
    TestPackageResult,
    TestPackageResultData,
    TestResultsModel,
} from '../../infrastructure/models'

export const useTestPackageRunner = () => {
    const { socket, isConnected } = useSocketContext()
    const { appState, setAppState } = useAppState()
    const testPackages = appState.testPackages
    const { canModify } = useUser()

    // State to track if tests are currently loading
    const isLoading = appState.testResults.some(
        (testResult) => testResult.status === 'loading'
    )

    // State to track if the user attempted to click without valid conditions
    const [hasAttemptedRun, setHasAttemptedRun] = useState(false)

    // Ref to store timeout IDs for each test package
    const timeoutIdsRef = useRef<{ [key: string]: NodeJS.Timeout }>({})

    // Dynamically calculate whether tests can run
    const canRunTests =
        isConnected &&
        socket !== null &&
        appState.id?.trim() !== '' &&
        Array.isArray(appState.selectedTestPackages) &&
        appState.selectedTestPackages.length > 0 &&
        !isLoading &&
        appState.serviceDetails.status !== 'running' &&
        canModify(appState.createdBy) &&
        allDatesValid(appState.selectedTestPackages)

    // Emit the `serviceCheck:runTestPackage` event
    const runTestPackages = useCallback(() => {
        if (!canRunTests) {
            setHasAttemptedRun(true) // User tried to run tests without valid conditions
            return
        }

        setHasAttemptedRun(false) // Reset attempted run state

        // Set the state to 'running' to block additional changes to the serviceCheckRecord
        setAppState({
            ...appState,
            serviceDetails: {
                ...appState.serviceDetails,
                status: 'running',
            },
        })

        const { id, selectedTestPackages } = appState

        // Initialize updatedTestResults
        const updatedTestResults: TestResultsModel[] = testPackages.map(
            (testPackage) => {
                const isSelected = selectedTestPackages.filter(
                    (selectedPackage) =>
                        selectedPackage.name === testPackage.name
                )
                const existingTestResult = appState.testResults.find(
                    (result) => result.packageName === testPackage.name
                )

                return {
                    packageName: testPackage.name,
                    packageTitle: testPackage.title,
                    status: isSelected
                        ? 'loading'
                        : existingTestResult?.status ?? 'not-run',
                    results: isSelected
                        ? testPackage.sectionTitles.map((sectionTitle) => ({
                              name: '',
                              title: sectionTitle,
                              ruleName: '',
                              data: { valueMsg: '' } as TestPackageResultData,
                          }))
                        : existingTestResult?.results || [],
                }
            }
        )

        // Update `appState` with modified `testResults`
        setAppState((prevState) => ({
            ...prevState,
            testResults: updatedTestResults,
        }))

        const payload = {
            id,
            testPackages: selectedTestPackages
                .filter((testPackage) => testPackage.isSelected)
                .map(
                    ({ name, parameters }) =>
                        ({
                            name,
                            parameters,
                        } as TestPackageRequest)
                ),
        }

        socket.emit('serviceCheck:runTestPackage', payload)
        console.log('[TestPackageRunner] Test Package Run Emitted:', payload)
    }, [socket, appState, canRunTests, testPackages, setAppState])

    // Reset `hasAttemptedRun` when user interacts with test package selection
    useEffect(() => {
        if (hasAttemptedRun) {
            setHasAttemptedRun(false)
        }
    }, [appState.selectedTestPackages, hasAttemptedRun])

    // Listen for `serviceCheck:testPackageResult` responses
    useEffect(() => {
        if (!socket) return

        const handleTestPackageResult = (data: TestPackageResult) => {
            console.log('[TestPackageRunner] Test Package Result:', data)

            const { name, results } = data

            setAppState((prevState) => ({
                ...prevState,
                testResults: prevState.testResults.map((testResult) =>
                    testResult.packageName === name
                        ? {
                              ...testResult,
                              status: 'completed',
                              results: results,
                          }
                        : testResult
                ),
            }))
        }

        socket.on('serviceCheck:testPackageResult', handleTestPackageResult)

        // Cleanup function to remove event listener on unmount
        return () => {
            socket.off(
                'serviceCheck:testPackageResult',
                handleTestPackageResult
            )
        }
    }, [socket, setAppState])

    // Cleanup timeouts on component unmount
    useEffect(() => {
        return () => {
            Object.values(timeoutIdsRef.current).forEach((timeoutId) => {
                clearTimeout(timeoutId)
            })
            timeoutIdsRef.current = {}
        }
    }, [])

    return {
        canRunTests,
        isLoading,
        hasAttemptedRun,
        runTestPackages,
    }
}
